<!--
  ~ Copyright (C) 2021 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!-- This layout is the first frame of the interpolation animation. -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    leaving state 1 and 2 for possibility of animating splash screen-->

    <ImageView
        android:id="@+id/icon_background"
        android:layout_width="240dp"
        android:layout_height="240dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/home_credit_logo_288x288_shadow" />
    <ImageView
        android:id="@+id/imageView"
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_marginBottom="6dp"
        android:src="@drawable/home_credit_logo_288x288"
        app:layout_constraintBottom_toBottomOf="@+id/icon_background"
        app:layout_constraintEnd_toEndOf="@+id/icon_background"
        app:layout_constraintStart_toStartOf="@+id/icon_background"
        app:layout_constraintTop_toTopOf="@+id/icon_background"
        app:srcCompat="@drawable/home_credit_logo_288x288" />
</androidx.constraintlayout.widget.ConstraintLayout>