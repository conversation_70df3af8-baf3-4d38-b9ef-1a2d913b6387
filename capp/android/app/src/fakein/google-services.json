{"project_info": {"project_number": "395607921371", "firebase_url": "https://superkupoin.firebaseio.com", "project_id": "superkupoin", "storage_bucket": "superkupoin.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:395607921371:android:f104714c69ec070dc572fe", "android_client_info": {"package_name": "net.homecredit.koyal"}}, "oauth_client": [{"client_id": "395607921371-00okm83t9cdlvlck0l5qv8vtan52ag17.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "net.homecredit.koyal", "certificate_hash": "aec006b45c3b6b71d69ad3ca1cca651810439343"}}, {"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD4qTVnaFsaAqL_WDqj6gGRRfz4U9lywus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}, {"client_id": "395607921371-49lc7g1ob9cqeepvslnbihgbi4ct9692.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "demo.homecredit.koyal.selfcare.dev.mocklocal"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:395607921371:android:a1a4122a19f4324cc572fe", "android_client_info": {"package_name": "net.homecredit.koyal.dev"}}, "oauth_client": [{"client_id": "395607921371-leefs24ubnuscoluq3nuku6offo2ejjn.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "net.homecredit.koyal.dev", "certificate_hash": "e458f639d0e08f206ed05d989a45a53b396d17f0"}}, {"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD4qTVnaFsaAqL_WDqj6gGRRfz4U9lywus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}, {"client_id": "395607921371-49lc7g1ob9cqeepvslnbihgbi4ct9692.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "demo.homecredit.koyal.selfcare.dev.mocklocal"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:395607921371:android:2f4a38a1b09bce4ac572fe", "android_client_info": {"package_name": "net.homecredit.selfcare"}}, "oauth_client": [{"client_id": "395607921371-b41ej1ua8ookspfq2go6b9be3eifdser.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "net.homecredit.selfcare", "certificate_hash": "e545b9ec81429731c5b83bb2f5a589cc3376ffb9"}}, {"client_id": "395607921371-s8s23da071lf9prarr9b4o2kpd5da6rc.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "net.homecredit.selfcare", "certificate_hash": "34f788e197b1168a1dd1d4e3df2bdea58d0c0a07"}}, {"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD4qTVnaFsaAqL_WDqj6gGRRfz4U9lywus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}, {"client_id": "395607921371-49lc7g1ob9cqeepvslnbihgbi4ct9692.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "demo.homecredit.koyal.selfcare.dev.mocklocal"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:395607921371:android:396134bec938ae71c572fe", "android_client_info": {"package_name": "net.homecredit.selfcare.fake"}}, "oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD4qTVnaFsaAqL_WDqj6gGRRfz4U9lywus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}, {"client_id": "395607921371-49lc7g1ob9cqeepvslnbihgbi4ct9692.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "demo.homecredit.koyal.selfcare.dev.mocklocal"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:395607921371:android:311230e0acdf83cac572fe", "android_client_info": {"package_name": "net.homecredit.selfcare.mocklocal"}}, "oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD4qTVnaFsaAqL_WDqj6gGRRfz4U9lywus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}, {"client_id": "395607921371-49lc7g1ob9cqeepvslnbihgbi4ct9692.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "demo.homecredit.koyal.selfcare.dev.mocklocal"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:395607921371:android:1ba477537a00421fc572fe", "android_client_info": {"package_name": "net.homecredit.selfcare.mockremote"}}, "oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyD4qTVnaFsaAqL_WDqj6gGRRfz4U9lywus"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "395607921371-fd8ko0fq74pnqugpk3ipqs837ahf8b2v.apps.googleusercontent.com", "client_type": 3}, {"client_id": "395607921371-49lc7g1ob9cqeepvslnbihgbi4ct9692.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "demo.homecredit.koyal.selfcare.dev.mocklocal"}}]}}}], "configuration_version": "1"}