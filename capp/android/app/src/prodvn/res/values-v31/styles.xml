<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.App.Starting" parent="Theme.SplashScreen">
        <!-- Set the splash screen background, animated icon, and animation
        duration. -->
        <item name="windowSplashScreenBackground">#F2F2F2</item>

        <!-- Use windowSplashScreenAnimatedIcon to add a drawable or an animated
             drawable. One of these is required. -->
        <item name="windowSplashScreenAnimatedIcon">@drawable/home_credit_logo_288x288</item>
        <!-- Required for animated icons. -->
        <item name="windowSplashScreenAnimationDuration">200</item>

        <!-- Set the theme of the Activity that directly follows your splash
        screen. This is required. -->
        <item name="postSplashScreenTheme">@style/Theme.AppCompat.Light.NoActionBar</item>
    </style>

    <style name="LaunchTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">#F2F2F2</item>
    </style>
</resources>