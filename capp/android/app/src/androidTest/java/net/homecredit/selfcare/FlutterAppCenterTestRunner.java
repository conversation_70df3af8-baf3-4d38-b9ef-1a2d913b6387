package net.homecredit.selfcare;


import android.util.Log;
import androidx.test.rule.ActivityTestRule;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.junit.Rule;
import org.junit.rules.TestRule;
import org.junit.runner.Description;
import org.junit.runner.Runner;
import org.junit.runner.notification.Failure;
import org.junit.runner.notification.RunNotifier;

import dev.flutter.plugins.integration_test.IntegrationTestPlugin;
import com.microsoft.appcenter.espresso.Factory;
import com.microsoft.appcenter.espresso.ReportHelper;
public class FlutterAppCenterTestRunner extends Runner {

    private static final String TAG = "FlutterAppCenterTestRunner";

    final Class testClass;
    TestRule rule = null;

    public FlutterAppCenterTestRunner(Class<?> testClass) {
        super();
        this.testClass = testClass;

        // Look for an `ActivityTestRule` annotated `@Rule` and invoke `launchActivity()`
        Field[] fields = testClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Rule.class)) {
                try {
                    Object instance = testClass.newInstance();
                    if (field.get(instance) instanceof ActivityTestRule) {
                        rule = (TestRule) field.get(instance);
                        break;
                    }
                } catch (InstantiationException | IllegalAccessException e) {
                    // This might occur if the developer did not make the rule public.
                    // We could call field.setAccessible(true) but it seems better to throw.
                    throw new RuntimeException("Unable to access activity rule", e);
                }
            }
        }
    }

    @Override
    public Description getDescription() {
        return Description.createTestDescription(testClass, "Flutter Tests");
    }

    @Rule
    EventReportHelper reportHelper = ReportFactory.getReportHelper();

    @Override
    public void run(RunNotifier notifier) {
        if (rule == null) {
            throw new RuntimeException("Unable to run tests due to missing activity rule");
        }
        try {
            if (rule instanceof ActivityTestRule) {
                ((ActivityTestRule) rule).launchActivity(null);
            }
        } catch (RuntimeException e) {
            Log.v(TAG, "launchActivity failed, possibly because the activity was already running. " + e);
            Log.v(
                    TAG,
                    "Try disabling auto-launch of the activity, e.g. ActivityTestRule<>(MainActivity.class, true, false);");
        }
        Map<String, String> results = null;
        try {
            results = IntegrationTestPlugin.testResults.get();
        } catch (ExecutionException | InterruptedException e) {
            throw new IllegalThreadStateException("Unable to get test results");
        }

        for (String name : results.keySet()) {
            Description d = Description.createTestDescription(testClass, name);

            if (name.contains(":")){
                String[] splited = name.split(":");
                Log.i(TAG, "splited: 1: "+ splited[0] + "2: " + splited[1] );
                d.addChild(Description.createTestDescription(testClass, splited[0]));
                d.addChild(Description.createTestDescription(testClass, splited[1]));

            }

            reportHelper.starting(d);
            notifier.fireTestStarted(d);

            String outcome = results.get(name);
            if (!outcome.equals("success")) {
                Exception dummyException = new Exception(outcome);
                reportHelper.failed(dummyException, d);
                notifier.fireTestFailure(new Failure(d, dummyException));

            } else {
                reportHelper.succeeded(d);
            }
            reportHelper.label("test label");
            reportHelper.finished(d);
            notifier.fireTestFinished(d);

        }
    }
}
