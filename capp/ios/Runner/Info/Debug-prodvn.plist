<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>NSSupportsLiveActivities</key>
		<true/>
		<key>TRUECALLER_APP_KEY</key>
		<string>${TRUECALLER_APP_KEY}</string>
		<key>TRUECALLER_APP_LINK</key>
		<string>${TRUECALLER_APP_LINK}</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>$(APP_DISPLAY_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIcons</key>
		<dict/>
		<key>CFBundleIcons~ipad</key>
		<dict/>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>app</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>truecallersdk-$(TRUECALLER_APP_KEY)</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>line3rdp.$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Deep link dev</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>net.homecredit.koyal.dev.development</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Deep link</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>demo.homecredit.koyal.selfcare</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Deep link repayment</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>hcvn</string>
					<string>hcvnvnpay</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Deep link Homezui</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>homezuiapp</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Deep link GrabMoca</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>gc-02f725e4cc744e829ffa7166fbff9651-6bnlikmsk-ios</string>
					<string>gc-8b5a0ef01be74e5ebb3291f9eb71ff60-y1ixdfndn-ios</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>itms-beta</string>
        	<string>itms</string>
			<string>truesdk</string>
			<string>lineauth2</string>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbapi20160328</string>
			<string>fbauth</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
			<string>tel</string>
			<string>momo</string>
			<string>zalo</string>
			<string>zalopay</string>
			<string>zalopay.api.v2</string>
			<string>grabconnect2</string>
			<string>grab</string>
			<string>grabdriver</string>
			<string>homezuiapp</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>NSUserTrackingUsageDescription</key>
		<string>This allows us to provide relevant products and personalize experiences just for you </string>
		<key>NSCalendarsUsageDescription</key>
		<string>Cho phép ứng dụng truy cập Lịch để bảo vệ bạn khỏi rủi ro gian lận</string>
		<key>NSCalendarsFullAccessUsageDescription</key>
		<string>Cho phép ứng dụng truy cập Lịch để bảo vệ bạn khỏi rủi ro gian lận</string>
		<key>NSCameraUsageDescription</key>
		<string>Application asks you to use your camera to take picture for verifying</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Application needs to access your current location to help you choose the nearest store to pick up a credit card or to meet our staffs for further verification if required.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Application needs to access your current location to help you choose the nearest store to pick up a credit card or to meet our staffs for further verification if required.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Application ask you to grant access to photo and video for Identity Verification in Underwriting process during Loan Application</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Application needs to save images to Photos Library</string>
		<key>NSFaceIDUsageDescription</key>
		<string>Application uses Face Id as a mean of local unlock</string>
		<key>NSAdvertisingAttributionReportEndpoint</key>
		<string>https://appsflyer-skadnetwork.com/</string>		
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIUserInterfaceStyle</key>
		<string>Light</string>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
		<key>io.flutter.embedded_views_preview</key>
		<true/>
		<key>NSMicrophoneUsageDescription</key>
		<string>Your microphone will be used to record your speech when you use the Voice feature.</string>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>FirebaseAutomaticScreenReportingEnabled</key>
		<false/>
		<key>NFCReaderUsageDescription</key>
		<string>Application uses NFC to scan ID card</string>
		<key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
		<array>
			<string>A0000002471001</string>
			<string>A0000002472001</string>
			<string>00000000000000</string>
		</array>
	</dict>
</plist>