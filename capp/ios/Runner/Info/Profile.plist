<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>TRUECALLER_APP_KEY</key>
		<string>${TRUECALLER_APP_KEY}</string>
		<key>TRUECALLER_APP_LINK</key>
		<string>${TRUECALLER_APP_LINK}</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>$(APP_DISPLAY_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIcons</key>
		<dict/>
		<key>CFBundleIcons~ipad</key>
		<dict/>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>app</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>truecallersdk-$(TRUECALLER_APP_KEY)</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>line3rdp.$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Deep link dev</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>net.homecredit.koyal.dev.development</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Deep link</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>demo.homecredit.koyal.selfcare</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>itms-beta</string>
        	<string>itms</string>
			<string>truesdk</string>
			<string>lineauth2</string>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbapi20160328</string>
			<string>fbauth</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
			<string>tel</string>
			<string>momo</string>
			<string>zalo</string>
			<string>zalopay</string>
			<string>zalopay.api.v2</string>
			<string>grabconnect2</string>
			<string>grab</string>
			<string>grabdriver</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>NSCalendarsUsageDescription</key>
		<string>Allow app to access calendar installed on your mobile phone for creating reminder events</string>
		<key>NSCalendarsFullAccessUsageDescription</key>
		<string>Allow app to access calendar installed on your mobile phone for creating reminder events</string>
		<key>NSCameraUsageDescription</key>
		<string>Application asks you to use your camera to take picture for verifying</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Application needs to access your current location to help you choose the nearest store to pick up a credit card or to meet our staffs for further verification if required.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Application needs to access your current location to help you choose the nearest store to pick up a credit card or to meet our staffs for further verification if required.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Application asks you to grant access to photo and video for verifying</string>
		<key>NSFaceIDUsageDescription</key>
		<string>Application uses Face Id as a mean of local unlock</string>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIUserInterfaceStyle</key>
		<string>Light</string>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
		<key>io.flutter.embedded_views_preview</key>
		<true/>
		<key>NSMicrophoneUsageDescription</key>
		<string>Your microphone will be used to record your speech when you use the Voice feature.</string>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>FirebaseAutomaticScreenReportingEnabled</key>
		<false/>
	</dict>
</plist>