// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0E212C7528BC8D2A00731A2A /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 0E212C7728BC8D2A00731A2A /* Localizable.strings */; };
		116B7DAA2C817BC700C3391A /* Utsname.swift in Sources */ = {isa = PBXBuildFile; fileRef = 116B7DA92C817BC700C3391A /* Utsname.swift */; };
		118172EF2CC5FDAE00B225F6 /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 118172EE2CC5FDAE00B225F6 /* Localizable.xcstrings */; };
		118172F22CC5FE3D00B225F6 /* LoanState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 118172F12CC5FE3900B225F6 /* LoanState.swift */; };
		119AFECD2CB3CDBB0047F49E /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 119AFECC2CB3CDBB0047F49E /* WidgetKit.framework */; };
		119AFECF2CB3CDBB0047F49E /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 119AFECE2CB3CDBB0047F49E /* SwiftUI.framework */; };
		119AFEDC2CB3CDBC0047F49E /* LiveActivitiesExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 119AFECB2CB3CDBB0047F49E /* LiveActivitiesExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		119AFEF82CB3CDD90047F49E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 119AFEF22CB3CDD90047F49E /* Assets.xcassets */; };
		119AFEFB2CB3CDD90047F49E /* WidgetBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 119AFEF52CB3CDD90047F49E /* WidgetBundle.swift */; };
		119AFEFC2CB3CDD90047F49E /* LoanJourneyWidgetLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 119AFEF62CB3CDD90047F49E /* LoanJourneyWidgetLiveActivity.swift */; };
		11A8A7312C9A8B8900B58ABD /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		11A8A7322C9A8B8900B58ABD /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		11A8A7362C9A8B8900B58ABD /* PUUIMainStoryBoard.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBA256EAFCA00F24BBC /* PUUIMainStoryBoard.storyboard */; };
		11A8A7372C9A8B8900B58ABD /* PUUIEMITopView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBB256EAFCA00F24BBC /* PUUIEMITopView.xib */; };
		11A8A7382C9A8B8900B58ABD /* PUUIStoredCardView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBC256EAFCA00F24BBC /* PUUIStoredCardView.xib */; };
		11A8A7392C9A8B8900B58ABD /* PUUITabBarTopView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBD256EAFCA00F24BBC /* PUUITabBarTopView.xib */; };
		11A8A73A2C9A8B8900B58ABD /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 0E212C7728BC8D2A00731A2A /* Localizable.strings */; };
		11A8A73B2C9A8B8900B58ABD /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		11A8A73C2C9A8B8900B58ABD /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		11A8A73D2C9A8B8900B58ABD /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		11A8A73E2C9A8B8900B58ABD /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		11A8A7412C9A8B8900B58ABD /* NotificationServiceExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 904DCBE62B1FE180001A87F7 /* NotificationServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		11AABB512CB4F98F004E1B6E /* LiveActivitiesPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11FA5B342CB4F37900348BD5 /* LiveActivitiesPlugin.swift */; };
		11FA5B352CB4F37900348BD5 /* LiveActivitiesPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11FA5B342CB4F37900348BD5 /* LiveActivitiesPlugin.swift */; };
		11FA5B362CB4F37900348BD5 /* LAAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11FA5B332CB4F37900348BD5 /* LAAttributes.swift */; };
		11FA5B392CB4F39800348BD5 /* LAAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11FA5B332CB4F37900348BD5 /* LAAttributes.swift */; };
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		2B0E198BE27DA64031ACB8C0 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1BE3BD0851BE41272FB36B22 /* Pods_Runner.framework */; };
		2EA653CB4623D1CAB8E06F01 /* Pods_NotificationServiceExtension.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8A2EDF922AFE5B71990B4A31 /* Pods_NotificationServiceExtension.framework */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		49F4F7412BCD432400470D31 /* KeyChainHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49F4F7402BCD432400470D31 /* KeyChainHelper.swift */; };
		49F4F7432BCD437C00470D31 /* ApiClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = 49F4F7422BCD437C00470D31 /* ApiClient.swift */; };
		592CECDF0EA406CF883C7915 /* BuildFile in Frameworks */ = {isa = PBXBuildFile; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		79030FBE256EAFCA00F24BBC /* PUUIMainStoryBoard.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBA256EAFCA00F24BBC /* PUUIMainStoryBoard.storyboard */; };
		79030FBF256EAFCA00F24BBC /* PUUIEMITopView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBB256EAFCA00F24BBC /* PUUIEMITopView.xib */; };
		79030FC0256EAFCA00F24BBC /* PUUIStoredCardView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBC256EAFCA00F24BBC /* PUUIStoredCardView.xib */; };
		79030FC1256EAFCA00F24BBC /* PUUITabBarTopView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 79030FBD256EAFCA00F24BBC /* PUUITabBarTopView.xib */; };
		904DCBE92B1FE182001A87F7 /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 904DCBE82B1FE182001A87F7 /* NotificationService.swift */; };
		904DCBED2B1FE182001A87F7 /* NotificationServiceExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 904DCBE62B1FE180001A87F7 /* NotificationServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		911C47992D126A9700D4C24E /* AppGroupUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 911C47982D126A9700D4C24E /* AppGroupUtils.swift */; };
		911C479A2D126A9700D4C24E /* AppGroupUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 911C47982D126A9700D4C24E /* AppGroupUtils.swift */; };
		911C479C2D1270CF00D4C24E /* AppGroupUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 911C47982D126A9700D4C24E /* AppGroupUtils.swift */; };
		911C479D2D12741500D4C24E /* LoanState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 118172F12CC5FE3900B225F6 /* LoanState.swift */; };
		911C479E2D12741500D4C24E /* LoanState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 118172F12CC5FE3900B225F6 /* LoanState.swift */; };
		912E45682CEADCF700265640 /* Bod1EnterApplicationFormViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 912E45672CEADCD300265640 /* Bod1EnterApplicationFormViewBuilder.swift */; };
		912E456A2CEAE1EA00265640 /* ThreeStepProgressBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 912E45692CEAE1E100265640 /* ThreeStepProgressBarView.swift */; };
		912E456C2CEAF23D00265640 /* Bod2SubmittedApplicationFormViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 912E456B2CEAF23D00265640 /* Bod2SubmittedApplicationFormViewBuilder.swift */; };
		912E456E2CEAF3B000265640 /* Bod2TimeExceededViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 912E456D2CEAF3B000265640 /* Bod2TimeExceededViewBuilder.swift */; };
		912E45702CEAF44100265640 /* Bod2ApplicationApprovedViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 912E456F2CEAF44100265640 /* Bod2ApplicationApprovedViewBuilder.swift */; };
		912E45722CEAF48100265640 /* Bod2AOsViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 912E45712CEAF48100265640 /* Bod2AOsViewBuilder.swift */; };
		912E45742CEAF49800265640 /* Bod2RejectedCancelledViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 912E45732CEAF49800265640 /* Bod2RejectedCancelledViewBuilder.swift */; };
		913CA9F52CD8CFC2008E8468 /* LAAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11FA5B332CB4F37900348BD5 /* LAAttributes.swift */; };
		914CEBD62CD4734F004242D4 /* InitialScoringViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEBD52CD4734F004242D4 /* InitialScoringViewBuilder.swift */; };
		914CEBD92CD473D3004242D4 /* String+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEBD82CD473D3004242D4 /* String+Extension.swift */; };
		914CEC122CD86FEE004242D4 /* AppLogoMinimalView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC072CD86FEE004242D4 /* AppLogoMinimalView.swift */; };
		914CEC132CD86FEE004242D4 /* BannerImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC082CD86FEE004242D4 /* BannerImageView.swift */; };
		914CEC142CD86FEE004242D4 /* DIExpandLeadingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC092CD86FEE004242D4 /* DIExpandLeadingView.swift */; };
		914CEC152CD86FEE004242D4 /* LockScreenBottomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC0A2CD86FEE004242D4 /* LockScreenBottomView.swift */; };
		914CEC162CD86FEE004242D4 /* LockScreenTopView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC0B2CD86FEE004242D4 /* LockScreenTopView.swift */; };
		914CEC172CD86FEE004242D4 /* PrimaryButtonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC0C2CD86FEE004242D4 /* PrimaryButtonView.swift */; };
		914CEC182CD86FEE004242D4 /* ScoringWithMoreOffersViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC0E2CD86FEE004242D4 /* ScoringWithMoreOffersViewBuilder.swift */; };
		914CEC192CD86FEE004242D4 /* ScoringWithNoOffersViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC0F2CD86FEE004242D4 /* ScoringWithNoOffersViewBuilder.swift */; };
		914CEC1A2CD86FEE004242D4 /* ScoringWithOneOfferViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC102CD86FEE004242D4 /* ScoringWithOneOfferViewBuilder.swift */; };
		914CEC1B2CD86FEE004242D4 /* ScoringTimeExceededViewBuilder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC112CD86FEE004242D4 /* ScoringTimeExceededViewBuilder.swift */; };
		914CEC1D2CD8711A004242D4 /* AppGroup+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 914CEC1C2CD8711A004242D4 /* AppGroup+Extension.swift */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		A5027E6197ED2E33D9F86147 /* Pods_HomeCreditVietnam.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4C6EB312ECF2A2A710052430 /* Pods_HomeCreditVietnam.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		119AFEDA2CB3CDBC0047F49E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 119AFECA2CB3CDBB0047F49E;
			remoteInfo = LoanJourneyWidgetExtension;
		};
		11A8A72B2C9A8B8900B58ABD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 904DCBE52B1FE180001A87F7;
			remoteInfo = NotificationServiceExtension;
		};
		904DCBEB2B1FE182001A87F7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 904DCBE52B1FE180001A87F7;
			remoteInfo = NotificationServiceExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		11A8A73F2C9A8B8900B58ABD /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				119AFEDC2CB3CDBC0047F49E /* LiveActivitiesExtension.appex in Embed Foundation Extensions */,
				11A8A7412C9A8B8900B58ABD /* NotificationServiceExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		11A8A7432C9A8B8900B58ABD /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		904DCBEE2B1FE186001A87F7 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				904DCBED2B1FE182001A87F7 /* NotificationServiceExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0693C73EC52ABF14C0A5EA89 /* Pods-HomeCreditVietnam.debug-fakevn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.debug-fakevn.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.debug-fakevn.xcconfig"; sourceTree = "<group>"; };
		0E212C7628BC8D2A00731A2A /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		0E212C7E28BCAD1700731A2A /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/Localizable.strings; sourceTree = "<group>"; };
		116B7DA92C817BC700C3391A /* Utsname.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Utsname.swift; sourceTree = "<group>"; };
		118172EE2CC5FDAE00B225F6 /* Localizable.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = Localizable.xcstrings; sourceTree = "<group>"; };
		118172F12CC5FE3900B225F6 /* LoanState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoanState.swift; sourceTree = "<group>"; };
		118172F32CC6341300B225F6 /* LiveActivitiesExtensionRelease-prodvn.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "LiveActivitiesExtensionRelease-prodvn.entitlements"; sourceTree = "<group>"; };
		119AFECB2CB3CDBB0047F49E /* LiveActivitiesExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = LiveActivitiesExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		119AFECC2CB3CDBB0047F49E /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		119AFECE2CB3CDBB0047F49E /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		119AFEF22CB3CDD90047F49E /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		119AFEF32CB3CDD90047F49E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		119AFEF52CB3CDD90047F49E /* WidgetBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetBundle.swift; sourceTree = "<group>"; };
		119AFEF62CB3CDD90047F49E /* LoanJourneyWidgetLiveActivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoanJourneyWidgetLiveActivity.swift; sourceTree = "<group>"; };
		11A8A75E2C9A8B8900B58ABD /* HomeCreditVietnam.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = HomeCreditVietnam.app; sourceTree = BUILT_PRODUCTS_DIR; };
		11CAA9F12CB63618000EED32 /* LiveActivitiesExtensionDebug-prodvn.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "LiveActivitiesExtensionDebug-prodvn.entitlements"; sourceTree = "<group>"; };
		11FA5B332CB4F37900348BD5 /* LAAttributes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = LAAttributes.swift; path = ../../../../plugins/live_activities/ios/LAAttributes.swift; sourceTree = "<group>"; };
		11FA5B342CB4F37900348BD5 /* LiveActivitiesPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = LiveActivitiesPlugin.swift; path = ../../../../plugins/live_activities/ios/LiveActivitiesPlugin.swift; sourceTree = "<group>"; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		18C1ADB39883FF1F86217A4E /* Pods-NotificationServiceExtension.release-fakevn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.release-fakevn.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.release-fakevn.xcconfig"; sourceTree = "<group>"; };
		1BE3BD0851BE41272FB36B22 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1E60ADD93D188129041E8FEE /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		24721AC429C9AACF28E99F7E /* Pods-Runner.release-prodin.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-prodin.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-prodin.xcconfig"; sourceTree = "<group>"; };
		271D8FF6634CEE97558B33B4 /* Pods-HomeCreditVietnam.debug-fakein.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.debug-fakein.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.debug-fakein.xcconfig"; sourceTree = "<group>"; };
		30CB5736E4679A9806B0073C /* Pods-HomeCreditVietnam.release-fakein.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.release-fakein.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.release-fakein.xcconfig"; sourceTree = "<group>"; };
		34182D09C6BC40A82BCE8B39 /* Pods-NotificationServiceExtension.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.profile.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.profile.xcconfig"; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		4134240268FDFC76D29DCF78 /* Pods-HomeCreditVietnam.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.release.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.release.xcconfig"; sourceTree = "<group>"; };
		4276B7507FFCFAB2E99FFACD /* Pods-NotificationServiceExtension.release-prodin.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.release-prodin.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.release-prodin.xcconfig"; sourceTree = "<group>"; };
		44CC5A912551501F00B814E8 /* Runner.entitlements */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		49F4F7402BCD432400470D31 /* KeyChainHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyChainHelper.swift; sourceTree = "<group>"; };
		49F4F7422BCD437C00470D31 /* ApiClient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ApiClient.swift; sourceTree = "<group>"; };
		49F4F7442BCD43B200470D31 /* NotificationServiceExtensionDebug-prodvn.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "NotificationServiceExtensionDebug-prodvn.entitlements"; sourceTree = "<group>"; };
		49F4F7452BCD43CE00470D31 /* NotificationServiceExtensionRelease-prodvn.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "NotificationServiceExtensionRelease-prodvn.entitlements"; sourceTree = "<group>"; };
		49F4F7462BCD619400470D31 /* RunnerDebug-prodvn.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "RunnerDebug-prodvn.entitlements"; sourceTree = "<group>"; };
		49F4F7472BCD61A700470D31 /* RunnerRelease-prodvn.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "RunnerRelease-prodvn.entitlements"; sourceTree = "<group>"; };
		4B425A8AF2461D18459FBBA8 /* Pods-Runner.release-fakein.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-fakein.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-fakein.xcconfig"; sourceTree = "<group>"; };
		4C6EB312ECF2A2A710052430 /* Pods_HomeCreditVietnam.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HomeCreditVietnam.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		534CAF8D59FE01675E4EFA82 /* Pods-NotificationServiceExtension.debug-fakevn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.debug-fakevn.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.debug-fakevn.xcconfig"; sourceTree = "<group>"; };
		53C7B91C98D3B9866C700515 /* Pods-NotificationServiceExtension.release-prodvn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.release-prodvn.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.release-prodvn.xcconfig"; sourceTree = "<group>"; };
		575598C316AE2A10B557B679 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		58C765CEC0AF029ED6EC9BEC /* Pods-NotificationServiceExtension.release-fakein.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.release-fakein.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.release-fakein.xcconfig"; sourceTree = "<group>"; };
		622AEBA419BA1288DBAEEDE2 /* Pods-HomeCreditVietnam.debug-prodvn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.debug-prodvn.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.debug-prodvn.xcconfig"; sourceTree = "<group>"; };
		649A56ACA05EB15B87595FE7 /* Pods-NotificationServiceExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.debug.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.debug.xcconfig"; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		775F8D2C321A9E9235200BA5 /* Pods-NotificationServiceExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.release.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.release.xcconfig"; sourceTree = "<group>"; };
		79030FBA256EAFCA00F24BBC /* PUUIMainStoryBoard.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = PUUIMainStoryBoard.storyboard; sourceTree = "<group>"; };
		79030FBB256EAFCA00F24BBC /* PUUIEMITopView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = PUUIEMITopView.xib; sourceTree = "<group>"; };
		79030FBC256EAFCA00F24BBC /* PUUIStoredCardView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = PUUIStoredCardView.xib; sourceTree = "<group>"; };
		79030FBD256EAFCA00F24BBC /* PUUITabBarTopView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = PUUITabBarTopView.xib; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		87E4CFF906D56A9ADD21BD1E /* Pods-NotificationServiceExtension.debug-prodin.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.debug-prodin.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.debug-prodin.xcconfig"; sourceTree = "<group>"; };
		8A2EDF922AFE5B71990B4A31 /* Pods_NotificationServiceExtension.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_NotificationServiceExtension.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8C86D236D48236C411D705E1 /* Pods-Runner.debug-prodin.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-prodin.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-prodin.xcconfig"; sourceTree = "<group>"; };
		90079D7D2B9B9BE800CD2ECF /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		90079D7F2B9B9BE800CD2ECF /* UserNotificationsUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotificationsUI.framework; path = System/Library/Frameworks/UserNotificationsUI.framework; sourceTree = SDKROOT; };
		904DCBE62B1FE180001A87F7 /* NotificationServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationServiceExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		904DCBE82B1FE182001A87F7 /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		904DCBEA2B1FE182001A87F7 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		911C47982D126A9700D4C24E /* AppGroupUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppGroupUtils.swift; path = ../../../../plugins/live_activities/ios/AppGroupUtils.swift; sourceTree = "<group>"; };
		912E45672CEADCD300265640 /* Bod1EnterApplicationFormViewBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Bod1EnterApplicationFormViewBuilder.swift; sourceTree = "<group>"; };
		912E45692CEAE1E100265640 /* ThreeStepProgressBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThreeStepProgressBarView.swift; sourceTree = "<group>"; };
		912E456B2CEAF23D00265640 /* Bod2SubmittedApplicationFormViewBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Bod2SubmittedApplicationFormViewBuilder.swift; sourceTree = "<group>"; };
		912E456D2CEAF3B000265640 /* Bod2TimeExceededViewBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Bod2TimeExceededViewBuilder.swift; sourceTree = "<group>"; };
		912E456F2CEAF44100265640 /* Bod2ApplicationApprovedViewBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Bod2ApplicationApprovedViewBuilder.swift; sourceTree = "<group>"; };
		912E45712CEAF48100265640 /* Bod2AOsViewBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Bod2AOsViewBuilder.swift; sourceTree = "<group>"; };
		912E45732CEAF49800265640 /* Bod2RejectedCancelledViewBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Bod2RejectedCancelledViewBuilder.swift; sourceTree = "<group>"; };
		914CEBD52CD4734F004242D4 /* InitialScoringViewBuilder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InitialScoringViewBuilder.swift; sourceTree = "<group>"; };
		914CEBD82CD473D3004242D4 /* String+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "String+Extension.swift"; sourceTree = "<group>"; };
		914CEC072CD86FEE004242D4 /* AppLogoMinimalView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppLogoMinimalView.swift; sourceTree = "<group>"; };
		914CEC082CD86FEE004242D4 /* BannerImageView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BannerImageView.swift; sourceTree = "<group>"; };
		914CEC092CD86FEE004242D4 /* DIExpandLeadingView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DIExpandLeadingView.swift; sourceTree = "<group>"; };
		914CEC0A2CD86FEE004242D4 /* LockScreenBottomView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LockScreenBottomView.swift; sourceTree = "<group>"; };
		914CEC0B2CD86FEE004242D4 /* LockScreenTopView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LockScreenTopView.swift; sourceTree = "<group>"; };
		914CEC0C2CD86FEE004242D4 /* PrimaryButtonView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PrimaryButtonView.swift; sourceTree = "<group>"; };
		914CEC0E2CD86FEE004242D4 /* ScoringWithMoreOffersViewBuilder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ScoringWithMoreOffersViewBuilder.swift; sourceTree = "<group>"; };
		914CEC0F2CD86FEE004242D4 /* ScoringWithNoOffersViewBuilder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ScoringWithNoOffersViewBuilder.swift; sourceTree = "<group>"; };
		914CEC102CD86FEE004242D4 /* ScoringWithOneOfferViewBuilder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ScoringWithOneOfferViewBuilder.swift; sourceTree = "<group>"; };
		914CEC112CD86FEE004242D4 /* ScoringTimeExceededViewBuilder.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ScoringTimeExceededViewBuilder.swift; sourceTree = "<group>"; };
		914CEC1C2CD8711A004242D4 /* AppGroup+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppGroup+Extension.swift"; sourceTree = "<group>"; };
		91A90FA613BFF86B953D48DF /* Pods-NotificationServiceExtension.debug-fakein.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.debug-fakein.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.debug-fakein.xcconfig"; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A84A75A02BA8598300E4CD3C /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		B65D2E30691C7AA70E1C981D /* Pods-Runner.debug-prodvn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-prodvn.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-prodvn.xcconfig"; sourceTree = "<group>"; };
		BA1093957B234B298C541EAC /* Pods-NotificationServiceExtension.debug-prodvn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.debug-prodvn.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.debug-prodvn.xcconfig"; sourceTree = "<group>"; };
		BD4061CE6671867C6DAEFE16 /* Pods-Runner.debug-fakein.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-fakein.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-fakein.xcconfig"; sourceTree = "<group>"; };
		BF36AB569BE4424F1BED8686 /* Pods-HomeCreditVietnam.release-fakevn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.release-fakevn.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.release-fakevn.xcconfig"; sourceTree = "<group>"; };
		CB4FDF7D15468E68D4B13688 /* Pods-Runner.release-prodvn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-prodvn.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-prodvn.xcconfig"; sourceTree = "<group>"; };
		D43D9EF560368865B111B86B /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		DFFB62C7AFB051AFF3F856D7 /* Pods-Runner.debug-fakevn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-fakevn.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-fakevn.xcconfig"; sourceTree = "<group>"; };
		E2CCAC9DD08EC64AC47B9498 /* Pods-HomeCreditVietnam.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.profile.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.profile.xcconfig"; sourceTree = "<group>"; };
		E921830CDEF9063CB2E2BA1D /* Pods-HomeCreditVietnam.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.debug.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.debug.xcconfig"; sourceTree = "<group>"; };
		F2830E55397B5C2B4B163D41 /* Pods-HomeCreditVietnam.release-prodin.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.release-prodin.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.release-prodin.xcconfig"; sourceTree = "<group>"; };
		F4B94CE116F93A6A3703A4B1 /* Pods-Runner.release-fakevn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-fakevn.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-fakevn.xcconfig"; sourceTree = "<group>"; };
		F9D90E131966C98C57F97063 /* Pods-HomeCreditVietnam.debug-prodin.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.debug-prodin.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.debug-prodin.xcconfig"; sourceTree = "<group>"; };
		FACC93C8E2949DE3744AD0B0 /* Pods-HomeCreditVietnam.release-prodvn.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HomeCreditVietnam.release-prodvn.xcconfig"; path = "Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam.release-prodvn.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		119AFEC82CB3CDBB0047F49E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				119AFECF2CB3CDBB0047F49E /* SwiftUI.framework in Frameworks */,
				119AFECD2CB3CDBB0047F49E /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		11A8A7332C9A8B8900B58ABD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A5027E6197ED2E33D9F86147 /* Pods_HomeCreditVietnam.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		904DCBE32B1FE180001A87F7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2EA653CB4623D1CAB8E06F01 /* Pods_NotificationServiceExtension.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				592CECDF0EA406CF883C7915 /* BuildFile in Frameworks */,
				2B0E198BE27DA64031ACB8C0 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		118172F02CC5FE3000B225F6 /* Models */ = {
			isa = PBXGroup;
			children = (
				118172F12CC5FE3900B225F6 /* LoanState.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		119AFEF72CB3CDD90047F49E /* LiveActivitiesExtension */ = {
			isa = PBXGroup;
			children = (
				11CAA9F12CB63618000EED32 /* LiveActivitiesExtensionDebug-prodvn.entitlements */,
				118172F32CC6341300B225F6 /* LiveActivitiesExtensionRelease-prodvn.entitlements */,
				118172F02CC5FE3000B225F6 /* Models */,
				914CEBD42CD47335004242D4 /* UI */,
				914CEBD72CD4739C004242D4 /* Extension */,
				119AFEF32CB3CDD90047F49E /* Info.plist */,
				119AFEF22CB3CDD90047F49E /* Assets.xcassets */,
				119AFEF52CB3CDD90047F49E /* WidgetBundle.swift */,
				119AFEF62CB3CDD90047F49E /* LoanJourneyWidgetLiveActivity.swift */,
				118172EE2CC5FDAE00B225F6 /* Localizable.xcstrings */,
			);
			path = LiveActivitiesExtension;
			sourceTree = "<group>";
		};
		33FFBFED37A737F7BA03F518 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				90079D7D2B9B9BE800CD2ECF /* UserNotifications.framework */,
				90079D7F2B9B9BE800CD2ECF /* UserNotificationsUI.framework */,
				8A2EDF922AFE5B71990B4A31 /* Pods_NotificationServiceExtension.framework */,
				1BE3BD0851BE41272FB36B22 /* Pods_Runner.framework */,
				4C6EB312ECF2A2A710052430 /* Pods_HomeCreditVietnam.framework */,
				119AFECC2CB3CDBB0047F49E /* WidgetKit.framework */,
				119AFECE2CB3CDBB0047F49E /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		911C479B2D126AF200D4C24E /* LiveActivitiesPlugin */ = {
			isa = PBXGroup;
			children = (
				11FA5B342CB4F37900348BD5 /* LiveActivitiesPlugin.swift */,
				911C47982D126A9700D4C24E /* AppGroupUtils.swift */,
				11FA5B332CB4F37900348BD5 /* LAAttributes.swift */,
			);
			path = LiveActivitiesPlugin;
			sourceTree = "<group>";
		};		
		904DCBE72B1FE182001A87F7 /* NotificationServiceExtension */ = {
			isa = PBXGroup;
			children = (
				116B7DA92C817BC700C3391A /* Utsname.swift */,
				49F4F7452BCD43CE00470D31 /* NotificationServiceExtensionRelease-prodvn.entitlements */,
				49F4F7442BCD43B200470D31 /* NotificationServiceExtensionDebug-prodvn.entitlements */,
				904DCBE82B1FE182001A87F7 /* NotificationService.swift */,
				904DCBEA2B1FE182001A87F7 /* Info.plist */,
				49F4F7402BCD432400470D31 /* KeyChainHelper.swift */,
				49F4F7422BCD437C00470D31 /* ApiClient.swift */,
			);
			path = NotificationServiceExtension;
			sourceTree = "<group>";
		};
		914CEBD42CD47335004242D4 /* UI */ = {
			isa = PBXGroup;
			children = (
				914CEC0D2CD86FEE004242D4 /* Common */,
				914CEBD52CD4734F004242D4 /* InitialScoringViewBuilder.swift */,
				914CEC112CD86FEE004242D4 /* ScoringTimeExceededViewBuilder.swift */,
				914CEC102CD86FEE004242D4 /* ScoringWithOneOfferViewBuilder.swift */,
				914CEC0E2CD86FEE004242D4 /* ScoringWithMoreOffersViewBuilder.swift */,
				914CEC0F2CD86FEE004242D4 /* ScoringWithNoOffersViewBuilder.swift */,
				912E45672CEADCD300265640 /* Bod1EnterApplicationFormViewBuilder.swift */,
				912E456B2CEAF23D00265640 /* Bod2SubmittedApplicationFormViewBuilder.swift */,
				912E456D2CEAF3B000265640 /* Bod2TimeExceededViewBuilder.swift */,
				912E456F2CEAF44100265640 /* Bod2ApplicationApprovedViewBuilder.swift */,
				912E45712CEAF48100265640 /* Bod2AOsViewBuilder.swift */,
				912E45732CEAF49800265640 /* Bod2RejectedCancelledViewBuilder.swift */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		914CEBD72CD4739C004242D4 /* Extension */ = {
			isa = PBXGroup;
			children = (
				914CEBD82CD473D3004242D4 /* String+Extension.swift */,
				914CEC1C2CD8711A004242D4 /* AppGroup+Extension.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		914CEC0D2CD86FEE004242D4 /* Common */ = {
			isa = PBXGroup;
			children = (
				912E45692CEAE1E100265640 /* ThreeStepProgressBarView.swift */,
				914CEC072CD86FEE004242D4 /* AppLogoMinimalView.swift */,
				914CEC082CD86FEE004242D4 /* BannerImageView.swift */,
				914CEC092CD86FEE004242D4 /* DIExpandLeadingView.swift */,
				914CEC0A2CD86FEE004242D4 /* LockScreenBottomView.swift */,
				914CEC0B2CD86FEE004242D4 /* LockScreenTopView.swift */,
				914CEC0C2CD86FEE004242D4 /* PrimaryButtonView.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};	
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				904DCBE72B1FE182001A87F7 /* NotificationServiceExtension */,
				119AFEF72CB3CDD90047F49E /* LiveActivitiesExtension */,
				97C146EF1CF9000F007C117D /* Products */,
				9C988847BDCF8EC0A6528468 /* Pods */,
				33FFBFED37A737F7BA03F518 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				904DCBE62B1FE180001A87F7 /* NotificationServiceExtension.appex */,
				11A8A75E2C9A8B8900B58ABD /* HomeCreditVietnam.app */,
				119AFECB2CB3CDBB0047F49E /* LiveActivitiesExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				49F4F7472BCD61A700470D31 /* RunnerRelease-prodvn.entitlements */,
				49F4F7462BCD619400470D31 /* RunnerDebug-prodvn.entitlements */,
				79030FBB256EAFCA00F24BBC /* PUUIEMITopView.xib */,
				79030FBA256EAFCA00F24BBC /* PUUIMainStoryBoard.storyboard */,
				79030FBC256EAFCA00F24BBC /* PUUIStoredCardView.xib */,
				79030FBD256EAFCA00F24BBC /* PUUITabBarTopView.xib */,
				44CC5A912551501F00B814E8 /* Runner.entitlements */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
				911C479B2D126AF200D4C24E /* LiveActivitiesPlugin */,
				0E212C7728BC8D2A00731A2A /* Localizable.strings */,
				A84A75A02BA8598300E4CD3C /* PrivacyInfo.xcprivacy */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		9C988847BDCF8EC0A6528468 /* Pods */ = {
			isa = PBXGroup;
			children = (
				649A56ACA05EB15B87595FE7 /* Pods-NotificationServiceExtension.debug.xcconfig */,
				91A90FA613BFF86B953D48DF /* Pods-NotificationServiceExtension.debug-fakein.xcconfig */,
				534CAF8D59FE01675E4EFA82 /* Pods-NotificationServiceExtension.debug-fakevn.xcconfig */,
				87E4CFF906D56A9ADD21BD1E /* Pods-NotificationServiceExtension.debug-prodin.xcconfig */,
				BA1093957B234B298C541EAC /* Pods-NotificationServiceExtension.debug-prodvn.xcconfig */,
				775F8D2C321A9E9235200BA5 /* Pods-NotificationServiceExtension.release.xcconfig */,
				58C765CEC0AF029ED6EC9BEC /* Pods-NotificationServiceExtension.release-fakein.xcconfig */,
				18C1ADB39883FF1F86217A4E /* Pods-NotificationServiceExtension.release-fakevn.xcconfig */,
				4276B7507FFCFAB2E99FFACD /* Pods-NotificationServiceExtension.release-prodin.xcconfig */,
				53C7B91C98D3B9866C700515 /* Pods-NotificationServiceExtension.release-prodvn.xcconfig */,
				34182D09C6BC40A82BCE8B39 /* Pods-NotificationServiceExtension.profile.xcconfig */,
				1E60ADD93D188129041E8FEE /* Pods-Runner.debug.xcconfig */,
				BD4061CE6671867C6DAEFE16 /* Pods-Runner.debug-fakein.xcconfig */,
				DFFB62C7AFB051AFF3F856D7 /* Pods-Runner.debug-fakevn.xcconfig */,
				8C86D236D48236C411D705E1 /* Pods-Runner.debug-prodin.xcconfig */,
				B65D2E30691C7AA70E1C981D /* Pods-Runner.debug-prodvn.xcconfig */,
				575598C316AE2A10B557B679 /* Pods-Runner.release.xcconfig */,
				4B425A8AF2461D18459FBBA8 /* Pods-Runner.release-fakein.xcconfig */,
				F4B94CE116F93A6A3703A4B1 /* Pods-Runner.release-fakevn.xcconfig */,
				24721AC429C9AACF28E99F7E /* Pods-Runner.release-prodin.xcconfig */,
				CB4FDF7D15468E68D4B13688 /* Pods-Runner.release-prodvn.xcconfig */,
				D43D9EF560368865B111B86B /* Pods-Runner.profile.xcconfig */,
				E921830CDEF9063CB2E2BA1D /* Pods-HomeCreditVietnam.debug.xcconfig */,
				271D8FF6634CEE97558B33B4 /* Pods-HomeCreditVietnam.debug-fakein.xcconfig */,
				0693C73EC52ABF14C0A5EA89 /* Pods-HomeCreditVietnam.debug-fakevn.xcconfig */,
				F9D90E131966C98C57F97063 /* Pods-HomeCreditVietnam.debug-prodin.xcconfig */,
				622AEBA419BA1288DBAEEDE2 /* Pods-HomeCreditVietnam.debug-prodvn.xcconfig */,
				4134240268FDFC76D29DCF78 /* Pods-HomeCreditVietnam.release.xcconfig */,
				30CB5736E4679A9806B0073C /* Pods-HomeCreditVietnam.release-fakein.xcconfig */,
				BF36AB569BE4424F1BED8686 /* Pods-HomeCreditVietnam.release-fakevn.xcconfig */,
				F2830E55397B5C2B4B163D41 /* Pods-HomeCreditVietnam.release-prodin.xcconfig */,
				FACC93C8E2949DE3744AD0B0 /* Pods-HomeCreditVietnam.release-prodvn.xcconfig */,
				E2CCAC9DD08EC64AC47B9498 /* Pods-HomeCreditVietnam.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		119AFECA2CB3CDBB0047F49E /* LiveActivitiesExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 119AFEF12CB3CDBD0047F49E /* Build configuration list for PBXNativeTarget "LiveActivitiesExtension" */;
			buildPhases = (
				119AFEC72CB3CDBB0047F49E /* Sources */,
				119AFEC82CB3CDBB0047F49E /* Frameworks */,
				119AFEC92CB3CDBB0047F49E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = LiveActivitiesExtension;
			productName = LoanJourneyWidgetExtension;
			productReference = 119AFECB2CB3CDBB0047F49E /* LiveActivitiesExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		11A8A7292C9A8B8900B58ABD /* HomeCreditVietnam */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 11A8A74A2C9A8B8900B58ABD /* Build configuration list for PBXNativeTarget "HomeCreditVietnam" */;
			buildPhases = (
				11A8A72E2C9A8B8900B58ABD /* [CP] Check Pods Manifest.lock */,
				11A8A72F2C9A8B8900B58ABD /* Run Script */,
				11A8A7302C9A8B8900B58ABD /* Sources */,
				11A8A7332C9A8B8900B58ABD /* Frameworks */,
				11A8A7352C9A8B8900B58ABD /* Resources */,
				11A8A73F2C9A8B8900B58ABD /* Embed Foundation Extensions */,
				11A8A7422C9A8B8900B58ABD /* Run Script */,
				11A8A7432C9A8B8900B58ABD /* Embed Frameworks */,
				11A8A7442C9A8B8900B58ABD /* Thin Binary */,
				11A8A7452C9A8B8900B58ABD /* Google plist */,
				11A8A7462C9A8B8900B58ABD /* ShellScript */,
				11A8A7472C9A8B8900B58ABD /* PrivacyInfo */,
				11A8A7482C9A8B8900B58ABD /* [CP] Embed Pods Frameworks */,
				11A8A7492C9A8B8900B58ABD /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				11A8A72A2C9A8B8900B58ABD /* PBXTargetDependency */,
				119AFEDB2CB3CDBC0047F49E /* PBXTargetDependency */,
			);
			name = HomeCreditVietnam;
			productName = Runner;
			productReference = 11A8A75E2C9A8B8900B58ABD /* HomeCreditVietnam.app */;
			productType = "com.apple.product-type.application";
		};
		904DCBE52B1FE180001A87F7 /* NotificationServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 904DCC022B1FE186001A87F7 /* Build configuration list for PBXNativeTarget "NotificationServiceExtension" */;
			buildPhases = (
				B48BEBEE6152655AD6D97CEB /* [CP] Check Pods Manifest.lock */,
				904DCBE22B1FE180001A87F7 /* Sources */,
				904DCBE32B1FE180001A87F7 /* Frameworks */,
				904DCBE42B1FE180001A87F7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NotificationServiceExtension;
			productName = NotificationServiceExtension;
			productReference = 904DCBE62B1FE180001A87F7 /* NotificationServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				C79E012EB7B67C2241348245 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				904DCBEE2B1FE186001A87F7 /* Embed Foundation Extensions */,
				777F08AD2A261627007D2D5D /* Run Script */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				C2C380EE2540D72000D6FD55 /* Google plist */,
				C2C380ED2540D69D00D6FD55 /* ShellScript */,
				A81D70B62BC3E81B0013A098 /* PrivacyInfo */,
				B749BF3CADBA78582E05CE0D /* [CP] Embed Pods Frameworks */,
				7A85E7F08F31E92FB12E8C42 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				904DCBEC2B1FE182001A87F7 /* PBXTargetDependency */,
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					119AFECA2CB3CDBB0047F49E = {
						CreatedOnToolsVersion = 16.0;
					};
					904DCBE52B1FE180001A87F7 = {
						CreatedOnToolsVersion = 15.0.1;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				vi,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				904DCBE52B1FE180001A87F7 /* NotificationServiceExtension */,
				11A8A7292C9A8B8900B58ABD /* HomeCreditVietnam */,
				119AFECA2CB3CDBB0047F49E /* LiveActivitiesExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		119AFEC92CB3CDBB0047F49E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				118172EF2CC5FDAE00B225F6 /* Localizable.xcstrings in Resources */,
				119AFEF82CB3CDD90047F49E /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		11A8A7352C9A8B8900B58ABD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				11A8A7362C9A8B8900B58ABD /* PUUIMainStoryBoard.storyboard in Resources */,
				11A8A7372C9A8B8900B58ABD /* PUUIEMITopView.xib in Resources */,
				11A8A7382C9A8B8900B58ABD /* PUUIStoredCardView.xib in Resources */,
				11A8A7392C9A8B8900B58ABD /* PUUITabBarTopView.xib in Resources */,
				11A8A73A2C9A8B8900B58ABD /* Localizable.strings in Resources */,
				11A8A73B2C9A8B8900B58ABD /* LaunchScreen.storyboard in Resources */,
				11A8A73C2C9A8B8900B58ABD /* AppFrameworkInfo.plist in Resources */,
				11A8A73D2C9A8B8900B58ABD /* Assets.xcassets in Resources */,
				11A8A73E2C9A8B8900B58ABD /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		904DCBE42B1FE180001A87F7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				79030FBE256EAFCA00F24BBC /* PUUIMainStoryBoard.storyboard in Resources */,
				79030FBF256EAFCA00F24BBC /* PUUIEMITopView.xib in Resources */,
				79030FC0256EAFCA00F24BBC /* PUUIStoredCardView.xib in Resources */,
				79030FC1256EAFCA00F24BBC /* PUUITabBarTopView.xib in Resources */,
				0E212C7528BC8D2A00731A2A /* Localizable.strings in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		11A8A72E2C9A8B8900B58ABD /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HomeCreditVietnam-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		11A8A72F2C9A8B8900B58ABD /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		11A8A7422C9A8B8900B58ABD /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ \"$CONFIGURATION\" != *vn ]]\nthen\n    rm -r \"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/vi.lproj\"\nfi\n";
		};
		11A8A7442C9A8B8900B58ABD /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin\n";
		};
		11A8A7452C9A8B8900B58ABD /* Google plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Google plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "PATH_TO_GOOGLE_PLISTS=\"${PROJECT_DIR}/Runner/GoogleService-Info\"\ncp -r \"$PATH_TO_GOOGLE_PLISTS/${CONFIGURATION}.plist\" \"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/GoogleService-Info.plist\"\n";
		};
		11A8A7462C9A8B8900B58ABD /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "${PODS_ROOT}/FirebaseCrashlytics/run\n";
		};
		11A8A7472C9A8B8900B58ABD /* PrivacyInfo */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = PrivacyInfo;
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "PATH_TO_PRIVACY_INFOS=\"${PROJECT_DIR}/Runner/PrivacyInfo\"\ncp -r \"$PATH_TO_PRIVACY_INFOS/${CONFIGURATION}.xcprivacy\" \"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/PrivacyInfo.xcprivacy\"\n";
		};
		11A8A7482C9A8B8900B58ABD /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		11A8A7492C9A8B8900B58ABD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HomeCreditVietnam/Pods-HomeCreditVietnam-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin\n";
		};
		777F08AD2A261627007D2D5D /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ \"$CONFIGURATION\" != *vn ]]\nthen\n    rm -r \"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/vi.lproj\"\nfi\n";
		};
		7A85E7F08F31E92FB12E8C42 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		A81D70B62BC3E81B0013A098 /* PrivacyInfo */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = PrivacyInfo;
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "PATH_TO_PRIVACY_INFOS=\"${PROJECT_DIR}/Runner/PrivacyInfo\"\ncp -r \"$PATH_TO_PRIVACY_INFOS/${CONFIGURATION}.xcprivacy\" \"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/PrivacyInfo.xcprivacy\"\n";
		};
		B48BEBEE6152655AD6D97CEB /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NotificationServiceExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B749BF3CADBA78582E05CE0D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C2C380ED2540D69D00D6FD55 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "${PODS_ROOT}/FirebaseCrashlytics/run\n";
		};
		C2C380EE2540D72000D6FD55 /* Google plist */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Google plist";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "PATH_TO_GOOGLE_PLISTS=\"${PROJECT_DIR}/Runner/GoogleService-Info\"\ncp -r \"$PATH_TO_GOOGLE_PLISTS/${CONFIGURATION}.plist\" \"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app/GoogleService-Info.plist\"\n";
		};
		C79E012EB7B67C2241348245 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		119AFEC72CB3CDBB0047F49E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				914CEC152CD86FEE004242D4 /* LockScreenBottomView.swift in Sources */,
				119AFEFB2CB3CDD90047F49E /* WidgetBundle.swift in Sources */,
				912E456A2CEAE1EA00265640 /* ThreeStepProgressBarView.swift in Sources */,
				914CEBD92CD473D3004242D4 /* String+Extension.swift in Sources */,
				912E456E2CEAF3B000265640 /* Bod2TimeExceededViewBuilder.swift in Sources */,
				912E45722CEAF48100265640 /* Bod2AOsViewBuilder.swift in Sources */,
				914CEC1B2CD86FEE004242D4 /* ScoringTimeExceededViewBuilder.swift in Sources */,
				912E45682CEADCF700265640 /* Bod1EnterApplicationFormViewBuilder.swift in Sources */,
				914CEC142CD86FEE004242D4 /* DIExpandLeadingView.swift in Sources */,
				914CEC192CD86FEE004242D4 /* ScoringWithNoOffersViewBuilder.swift in Sources */,
				911C479C2D1270CF00D4C24E /* AppGroupUtils.swift in Sources */,
				912E456C2CEAF23D00265640 /* Bod2SubmittedApplicationFormViewBuilder.swift in Sources */,
				914CEC162CD86FEE004242D4 /* LockScreenTopView.swift in Sources */,
				118172F22CC5FE3D00B225F6 /* LoanState.swift in Sources */,
				914CEC1A2CD86FEE004242D4 /* ScoringWithOneOfferViewBuilder.swift in Sources */,
				912E45702CEAF44100265640 /* Bod2ApplicationApprovedViewBuilder.swift in Sources */,
				119AFEFC2CB3CDD90047F49E /* LoanJourneyWidgetLiveActivity.swift in Sources */,
				914CEC182CD86FEE004242D4 /* ScoringWithMoreOffersViewBuilder.swift in Sources */,
				912E45742CEAF49800265640 /* Bod2RejectedCancelledViewBuilder.swift in Sources */,
				11FA5B392CB4F39800348BD5 /* LAAttributes.swift in Sources */,
				914CEC172CD86FEE004242D4 /* PrimaryButtonView.swift in Sources */,
				914CEC132CD86FEE004242D4 /* BannerImageView.swift in Sources */,
				914CEBD62CD4734F004242D4 /* InitialScoringViewBuilder.swift in Sources */,
				914CEC122CD86FEE004242D4 /* AppLogoMinimalView.swift in Sources */,
				914CEC1D2CD8711A004242D4 /* AppGroup+Extension.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		11A8A7302C9A8B8900B58ABD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				11FA5B352CB4F37900348BD5 /* LiveActivitiesPlugin.swift in Sources */,
				911C479E2D12741500D4C24E /* LoanState.swift in Sources */,
				11FA5B362CB4F37900348BD5 /* LAAttributes.swift in Sources */,
				11A8A7312C9A8B8900B58ABD /* AppDelegate.swift in Sources */,
				11A8A7322C9A8B8900B58ABD /* GeneratedPluginRegistrant.m in Sources */,
				911C479A2D126A9700D4C24E /* AppGroupUtils.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		904DCBE22B1FE180001A87F7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				904DCBE92B1FE182001A87F7 /* NotificationService.swift in Sources */,
				116B7DAA2C817BC700C3391A /* Utsname.swift in Sources */,
				49F4F7412BCD432400470D31 /* KeyChainHelper.swift in Sources */,
				49F4F7432BCD437C00470D31 /* ApiClient.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				913CA9F52CD8CFC2008E8468 /* LAAttributes.swift in Sources */,
				911C479D2D12741500D4C24E /* LoanState.swift in Sources */,
				11AABB512CB4F98F004E1B6E /* LiveActivitiesPlugin.swift in Sources */,
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				911C47992D126A9700D4C24E /* AppGroupUtils.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		119AFEDB2CB3CDBC0047F49E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 119AFECA2CB3CDBB0047F49E /* LiveActivitiesExtension */;
			targetProxy = 119AFEDA2CB3CDBC0047F49E /* PBXContainerItemProxy */;
		};
		11A8A72A2C9A8B8900B58ABD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 904DCBE52B1FE180001A87F7 /* NotificationServiceExtension */;
			targetProxy = 11A8A72B2C9A8B8900B58ABD /* PBXContainerItemProxy */;
		};
		904DCBEC2B1FE182001A87F7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 904DCBE52B1FE180001A87F7 /* NotificationServiceExtension */;
			targetProxy = 904DCBEB2B1FE182001A87F7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		0E212C7728BC8D2A00731A2A /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				0E212C7628BC8D2A00731A2A /* en */,
				0E212C7E28BCAD1700731A2A /* vi */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		119AFEDD2CB3CDBD0047F49E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		119AFEDE2CB3CDBD0047F49E /* Debug-fakein */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-fakein";
		};
		119AFEE12CB3CDBD0047F49E /* Debug-fakevn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-fakevn";
		};
		119AFEE22CB3CDBD0047F49E /* Debug-prodin */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-prodin";
		};
		119AFEE52CB3CDBD0047F49E /* Debug-prodvn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "LiveActivitiesExtension/LiveActivitiesExtensionDebug-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.LiveActivitiesExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-prodvn";
		};
		119AFEE62CB3CDBD0047F49E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		119AFEE72CB3CDBD0047F49E /* Release-fakein */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Release-fakein";
		};
		119AFEEA2CB3CDBD0047F49E /* Release-fakevn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Release-fakevn";
		};
		119AFEEB2CB3CDBD0047F49E /* Release-prodin */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Release-prodin";
		};
		119AFEEE2CB3CDBD0047F49E /* Release-prodvn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "LiveActivitiesExtension/LiveActivitiesExtensionRelease-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.LiveActivitiesExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Release-prodvn";
		};
		119AFEEF2CB3CDBD0047F49E /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = LiveActivitiesExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = LiveActivitiesExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.LoanJourneyWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Profile;
		};
		11A8A74B2C9A8B8900B58ABD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info/Debug.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		11A8A74C2C9A8B8900B58ABD /* Debug-fakein */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-fakein";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.fake;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "Koyal Selfcare DEV fake";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-fakein";
		};
		11A8A74F2C9A8B8900B58ABD /* Debug-fakevn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD -DVN -DLIVE_ACTIVITY";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.fake;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-fakevn";
		};
		11A8A7502C9A8B8900B58ABD /* Debug-prodin */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodin";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-prodin";
		};
		11A8A7532C9A8B8900B58ABD /* Debug-prodvn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "AppIcon-prodvn-summer";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodvn";
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/RunnerDebug-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Debug-prodvn.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"c++\"",
					"-l\"sqlite3\"",
					"-l\"swiftCoreGraphics\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AppsFlyerLib\"",
					"-framework",
					"\"CallAppSDK\"",
					"-framework",
					"\"Contacts\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CoreVideo\"",
					"-framework",
					"\"CredoAppCalendarEvents\"",
					"-framework",
					"\"CredoAppCore\"",
					"-framework",
					"\"CredoAppIovation\"",
					"-framework",
					"\"CredoAppMedia\"",
					"-framework",
					"\"DTTJailbreakDetection\"",
					"-framework",
					"\"DotCamera\"",
					"-framework",
					"\"DotCapture\"",
					"-framework",
					"\"DotCore\"",
					"-framework",
					"\"DotDocument\"",
					"-framework",
					"\"DotDocumentCommons\"",
					"-framework",
					"\"DotFaceBackgroundUniformity\"",
					"-framework",
					"\"DotFaceCommons\"",
					"-framework",
					"\"DotFaceCore\"",
					"-framework",
					"\"DotFaceDetectionBalanced\"",
					"-framework",
					"\"DotFaceExpressionNeutral\"",
					"-framework",
					"\"DotFaceEyeGazeLiveness\"",
					"-framework",
					"\"DotFacePassiveLiveness\"",
					"-framework",
					"\"DotProtocolBuffers\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"FirebaseABTesting\"",
					"-framework",
					"\"FirebaseAnalytics\"",
					"-framework",
					"\"FirebaseAppCheckInterop\"",
					"-framework",
					"\"FirebaseCore\"",
					"-framework",
					"\"FirebaseCoreExtension\"",
					"-framework",
					"\"FirebaseCoreInternal\"",
					"-framework",
					"\"FirebaseCrashlytics\"",
					"-framework",
					"\"FirebaseDynamicLinks\"",
					"-framework",
					"\"FirebaseFirestore\"",
					"-framework",
					"\"FirebaseFirestoreInternal\"",
					"-framework",
					"\"FirebaseInstallations\"",
					"-framework",
					"\"FirebaseMessaging\"",
					"-framework",
					"\"FirebasePerformance\"",
					"-framework",
					"\"FirebaseRemoteConfig\"",
					"-framework",
					"\"FirebaseRemoteConfigInterop\"",
					"-framework",
					"\"FirebaseSessions\"",
					"-framework",
					"\"FirebaseSharedSwift\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"FraudForce\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GTMSessionFetcher\"",
					"-framework",
					"\"GoogleAppMeasurement\"",
					"-framework",
					"\"GoogleAppMeasurementIdentitySupport\"",
					"-framework",
					"\"GoogleDataTransport\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GoogleMapsUtils\"",
					"-framework",
					"\"GoogleToolboxForMac\"",
					"-framework",
					"\"GoogleUtilities\"",
					"-framework",
					"\"GoogleUtilitiesComponents\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"LocalAuthentication\"",
					"-framework",
					"\"MLImage\"",
					"-framework",
					"\"MLKitBarcodeScanning\"",
					"-framework",
					"\"MLKitCommon\"",
					"-framework",
					"\"MLKitVision\"",
					"-framework",
					"\"Mantle\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"NFCPassportReader\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"OpenSSL\"",
					"-framework",
					"\"OrderedSet\"",
					"-framework",
					"\"PhoneNumberKit\"",
					"-framework",
					"\"Promises\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SDWebImageWebPCoder\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketRocket\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"Toast\"",
					"-framework",
					"\"TrueIDVideoSDK\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebRTC\"",
					"-framework",
					"\"absl\"",
					"-framework",
					"\"account_manager\"",
					"-framework",
					"\"advertising_id\"",
					"-framework",
					"\"app_links\"",
					"-framework",
					"\"app_settings\"",
					"-framework",
					"\"app_tracking_transparency\"",
					"-framework",
					"\"appsflyer_sdk\"",
					"-framework",
					"\"biometric_signature\"",
					"-framework",
					"\"camera_avfoundation\"",
					"-framework",
					"\"capp_plugins\"",
					"-framework",
					"\"cloud_firestore\"",
					"-framework",
					"\"connectivity_plus\"",
					"-framework",
					"\"credolab\"",
					"-framework",
					"\"device_calendar\"",
					"-framework",
					"\"device_info_plus\"",
					"-framework",
					"\"file_saver\"",
					"-framework",
					"\"firebase_analytics\"",
					"-framework",
					"\"firebase_core\"",
					"-framework",
					"\"firebase_crashlytics\"",
					"-framework",
					"\"firebase_dynamic_links\"",
					"-framework",
					"\"firebase_messaging\"",
					"-framework",
					"\"firebase_performance\"",
					"-framework",
					"\"firebase_remote_config\"",
					"-framework",
					"\"flutter_app_badger\"",
					"-framework",
					"\"flutter_custom_tabs_ios\"",
					"-framework",
					"\"flutter_document_picker\"",
					"-framework",
					"\"flutter_html_to_pdf\"",
					"-framework",
					"\"flutter_image_compress_common\"",
					"-framework",
					"\"flutter_inappwebview_ios\"",
					"-framework",
					"\"flutter_innovatrics_vn\"",
					"-framework",
					"\"flutter_ios_calendar_events\"",
					"-framework",
					"\"flutter_ios_core_proxy\"",
					"-framework",
					"\"flutter_ios_iovation\"",
					"-framework",
					"\"flutter_ios_media\"",
					"-framework",
					"\"flutter_libphonenumber\"",
					"-framework",
					"\"flutter_local_notifications\"",
					"-framework",
					"\"flutter_native_contact_picker\"",
					"-framework",
					"\"flutter_pdfview\"",
					"-framework",
					"\"flutter_pin_encryption\"",
					"-framework",
					"\"flutter_secure_storage\"",
					"-framework",
					"\"flutter_timezone\"",
					"-framework",
					"\"flutter_true_call\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"geolocator_apple\"",
					"-framework",
					"\"gma_vault\"",
					"-framework",
					"\"google_maps_flutter_ios\"",
					"-framework",
					"\"google_mlkit_barcode_scanning\"",
					"-framework",
					"\"google_mlkit_commons\"",
					"-framework",
					"\"grpc\"",
					"-framework",
					"\"grpcpp\"",
					"-framework",
					"\"iface\"",
					"-framework",
					"\"image_gallery_saver\"",
					"-framework",
					"\"image_picker_ios\"",
					"-framework",
					"\"in_app_review\"",
					"-framework",
					"\"innoonnxruntime\"",
					"-framework",
					"\"installer_checker\"",
					"-framework",
					"\"integration_test\"",
					"-framework",
					"\"ios_teamid\"",
					"-framework",
					"\"launch_app_store\"",
					"-framework",
					"\"leveldb\"",
					"-framework",
					"\"libwebp\"",
					"-framework",
					"\"local_auth_darwin\"",
					"-framework",
					"\"maps_launcher\"",
					"-framework",
					"\"memory_info\"",
					"-framework",
					"\"nanopb\"",
					"-framework",
					"\"onepay_custom_deeplink\"",
					"-framework",
					"\"open_filex\"",
					"-framework",
					"\"openssl_grpc\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"path_provider_foundation\"",
					"-framework",
					"\"pdf_combiner\"",
					"-framework",
					"\"permission_handler_apple\"",
					"-framework",
					"\"restart_app\"",
					"-framework",
					"\"rive_common\"",
					"-framework",
					"\"safe_device\"",
					"-framework",
					"\"secure_application\"",
					"-framework",
					"\"sensors_plus\"",
					"-framework",
					"\"share_plus\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"sms_autofill\"",
					"-framework",
					"\"sqflite_darwin\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"vibration\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"vn_idcard_reader\"",
					"-framework",
					"\"vnpay\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
					"-framework",
					"\"workmanager\"",
					"-framework",
					"\"zalopay\"",
					"-framework",
					"\"zpdk\"",
					"-weak_framework",
					"\"CoreNFC\"",
					"-weak_framework",
					"\"CryptoKit\"",
					"-weak_framework",
					"\"CryptoTokenKit\"",
					"-weak_framework",
					"\"FirebaseFirestoreInternal\"",
					"-weak_framework",
					"\"LinkPresentation\"",
					"-weak_framework",
					"\"UserNotifications\"",
					"-weak_framework",
					"\"WebKit\"",
					"-lswiftCompatibility50",
					"-lswiftCompatibility51",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD -DVN -DLIVE_ACTIVITY";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-prodvn";
		};
		11A8A7542C9A8B8900B58ABD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		11A8A7552C9A8B8900B58ABD /* Release-fakein */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-fakein";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-fakein";
		};
		11A8A7582C9A8B8900B58ABD /* Release-fakevn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.fake;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-fakevn";
		};
		11A8A7592C9A8B8900B58ABD /* Release-prodin */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodin";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-prodin";
		};
		11A8A75C2C9A8B8900B58ABD /* Release-prodvn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_ALTERNATE_APPICON_NAMES = "AppIcon-prodvn-summer";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodvn";
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/RunnerRelease-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Release-prodvn.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"c++\"",
					"-l\"sqlite3\"",
					"-l\"swiftCoreGraphics\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"Accelerate\"",
					"-framework",
					"\"AppsFlyerLib\"",
					"-framework",
					"\"CallAppSDK\"",
					"-framework",
					"\"Contacts\"",
					"-framework",
					"\"CoreData\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreImage\"",
					"-framework",
					"\"CoreLocation\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreTelephony\"",
					"-framework",
					"\"CoreText\"",
					"-framework",
					"\"CoreVideo\"",
					"-framework",
					"\"CredoAppCalendarEvents\"",
					"-framework",
					"\"CredoAppCore\"",
					"-framework",
					"\"CredoAppIovation\"",
					"-framework",
					"\"CredoAppMedia\"",
					"-framework",
					"\"DTTJailbreakDetection\"",
					"-framework",
					"\"DotCamera\"",
					"-framework",
					"\"DotCapture\"",
					"-framework",
					"\"DotCore\"",
					"-framework",
					"\"DotDocument\"",
					"-framework",
					"\"DotDocumentCommons\"",
					"-framework",
					"\"DotFaceBackgroundUniformity\"",
					"-framework",
					"\"DotFaceCommons\"",
					"-framework",
					"\"DotFaceCore\"",
					"-framework",
					"\"DotFaceDetectionBalanced\"",
					"-framework",
					"\"DotFaceExpressionNeutral\"",
					"-framework",
					"\"DotFaceEyeGazeLiveness\"",
					"-framework",
					"\"DotFacePassiveLiveness\"",
					"-framework",
					"\"DotProtocolBuffers\"",
					"-framework",
					"\"FBLPromises\"",
					"-framework",
					"\"FirebaseABTesting\"",
					"-framework",
					"\"FirebaseAnalytics\"",
					"-framework",
					"\"FirebaseAppCheckInterop\"",
					"-framework",
					"\"FirebaseCore\"",
					"-framework",
					"\"FirebaseCoreExtension\"",
					"-framework",
					"\"FirebaseCoreInternal\"",
					"-framework",
					"\"FirebaseCrashlytics\"",
					"-framework",
					"\"FirebaseDynamicLinks\"",
					"-framework",
					"\"FirebaseFirestore\"",
					"-framework",
					"\"FirebaseFirestoreInternal\"",
					"-framework",
					"\"FirebaseInstallations\"",
					"-framework",
					"\"FirebaseMessaging\"",
					"-framework",
					"\"FirebasePerformance\"",
					"-framework",
					"\"FirebaseRemoteConfig\"",
					"-framework",
					"\"FirebaseRemoteConfigInterop\"",
					"-framework",
					"\"FirebaseSessions\"",
					"-framework",
					"\"FirebaseSharedSwift\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"FraudForce\"",
					"-framework",
					"\"GLKit\"",
					"-framework",
					"\"GTMSessionFetcher\"",
					"-framework",
					"\"GoogleAppMeasurement\"",
					"-framework",
					"\"GoogleAppMeasurementIdentitySupport\"",
					"-framework",
					"\"GoogleDataTransport\"",
					"-framework",
					"\"GoogleMaps\"",
					"-framework",
					"\"GoogleMapsBase\"",
					"-framework",
					"\"GoogleMapsCore\"",
					"-framework",
					"\"GoogleMapsUtils\"",
					"-framework",
					"\"GoogleToolboxForMac\"",
					"-framework",
					"\"GoogleUtilities\"",
					"-framework",
					"\"GoogleUtilitiesComponents\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"LocalAuthentication\"",
					"-framework",
					"\"MLImage\"",
					"-framework",
					"\"MLKitBarcodeScanning\"",
					"-framework",
					"\"MLKitCommon\"",
					"-framework",
					"\"MLKitVision\"",
					"-framework",
					"\"Mantle\"",
					"-framework",
					"\"Metal\"",
					"-framework",
					"\"NFCPassportReader\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"OpenSSL\"",
					"-framework",
					"\"OrderedSet\"",
					"-framework",
					"\"PhoneNumberKit\"",
					"-framework",
					"\"Promises\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SDWebImageWebPCoder\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"SocketRocket\"",
					"-framework",
					"\"StoreKit\"",
					"-framework",
					"\"SystemConfiguration\"",
					"-framework",
					"\"Toast\"",
					"-framework",
					"\"TrueIDVideoSDK\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebRTC\"",
					"-framework",
					"\"absl\"",
					"-framework",
					"\"account_manager\"",
					"-framework",
					"\"advertising_id\"",
					"-framework",
					"\"app_links\"",
					"-framework",
					"\"app_settings\"",
					"-framework",
					"\"app_tracking_transparency\"",
					"-framework",
					"\"appsflyer_sdk\"",
					"-framework",
					"\"biometric_signature\"",
					"-framework",
					"\"camera_avfoundation\"",
					"-framework",
					"\"capp_plugins\"",
					"-framework",
					"\"cloud_firestore\"",
					"-framework",
					"\"connectivity_plus\"",
					"-framework",
					"\"credolab\"",
					"-framework",
					"\"device_calendar\"",
					"-framework",
					"\"device_info_plus\"",
					"-framework",
					"\"file_saver\"",
					"-framework",
					"\"firebase_analytics\"",
					"-framework",
					"\"firebase_core\"",
					"-framework",
					"\"firebase_crashlytics\"",
					"-framework",
					"\"firebase_dynamic_links\"",
					"-framework",
					"\"firebase_messaging\"",
					"-framework",
					"\"firebase_performance\"",
					"-framework",
					"\"firebase_remote_config\"",
					"-framework",
					"\"flutter_app_badger\"",
					"-framework",
					"\"flutter_custom_tabs_ios\"",
					"-framework",
					"\"flutter_document_picker\"",
					"-framework",
					"\"flutter_html_to_pdf\"",
					"-framework",
					"\"flutter_image_compress_common\"",
					"-framework",
					"\"flutter_inappwebview_ios\"",
					"-framework",
					"\"flutter_innovatrics_vn\"",
					"-framework",
					"\"flutter_ios_calendar_events\"",
					"-framework",
					"\"flutter_ios_core_proxy\"",
					"-framework",
					"\"flutter_ios_iovation\"",
					"-framework",
					"\"flutter_ios_media\"",
					"-framework",
					"\"flutter_libphonenumber\"",
					"-framework",
					"\"flutter_local_notifications\"",
					"-framework",
					"\"flutter_native_contact_picker\"",
					"-framework",
					"\"flutter_pdfview\"",
					"-framework",
					"\"flutter_pin_encryption\"",
					"-framework",
					"\"flutter_secure_storage\"",
					"-framework",
					"\"flutter_timezone\"",
					"-framework",
					"\"flutter_true_call\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"geolocator_apple\"",
					"-framework",
					"\"gma_vault\"",
					"-framework",
					"\"google_maps_flutter_ios\"",
					"-framework",
					"\"google_mlkit_barcode_scanning\"",
					"-framework",
					"\"google_mlkit_commons\"",
					"-framework",
					"\"grpc\"",
					"-framework",
					"\"grpcpp\"",
					"-framework",
					"\"iface\"",
					"-framework",
					"\"image_gallery_saver\"",
					"-framework",
					"\"image_picker_ios\"",
					"-framework",
					"\"in_app_review\"",
					"-framework",
					"\"innoonnxruntime\"",
					"-framework",
					"\"installer_checker\"",
					"-framework",
					"\"integration_test\"",
					"-framework",
					"\"ios_teamid\"",
					"-framework",
					"\"launch_app_store\"",
					"-framework",
					"\"leveldb\"",
					"-framework",
					"\"libwebp\"",
					"-framework",
					"\"local_auth_darwin\"",
					"-framework",
					"\"maps_launcher\"",
					"-framework",
					"\"memory_info\"",
					"-framework",
					"\"nanopb\"",
					"-framework",
					"\"onepay_custom_deeplink\"",
					"-framework",
					"\"open_filex\"",
					"-framework",
					"\"openssl_grpc\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"path_provider_foundation\"",
					"-framework",
					"\"pdf_combiner\"",
					"-framework",
					"\"permission_handler_apple\"",
					"-framework",
					"\"restart_app\"",
					"-framework",
					"\"rive_common\"",
					"-framework",
					"\"safe_device\"",
					"-framework",
					"\"secure_application\"",
					"-framework",
					"\"sensors_plus\"",
					"-framework",
					"\"share_plus\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"sms_autofill\"",
					"-framework",
					"\"sqflite_darwin\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"vibration\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"vn_idcard_reader\"",
					"-framework",
					"\"vnpay\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
					"-framework",
					"\"workmanager\"",
					"-framework",
					"\"zalopay\"",
					"-framework",
					"\"zpdk\"",
					"-weak_framework",
					"\"CoreNFC\"",
					"-weak_framework",
					"\"CryptoKit\"",
					"-weak_framework",
					"\"CryptoTokenKit\"",
					"-weak_framework",
					"\"FirebaseFirestoreInternal\"",
					"-weak_framework",
					"\"LinkPresentation\"",
					"-weak_framework",
					"\"UserNotifications\"",
					"-weak_framework",
					"\"WebKit\"",
					"-lswiftCompatibility50",
					"-lswiftCompatibility51",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DVN -DLIVE_ACTIVITY";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-prodvn";
		};
		11A8A75D2C9A8B8900B58ABD /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info/Profile.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		79E58AB425D3DABE00E9DB67 /* Debug-fakevn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-fakevn";
		};
		79E58AB525D3DABE00E9DB67 /* Debug-fakevn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Debug-fakevn.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD -DLIVE_ACTIVITY -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.fake;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-fakevn";
		};
		79E58AB625D3DADD00E9DB67 /* Debug-prodvn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-prodvn";
		};
		79E58AB725D3DADD00E9DB67 /* Debug-prodvn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodvn";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/RunnerDebug-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Debug-prodvn.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD -DLIVE_ACTIVITY -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-prodvn";
		};
		79E58AB825D3DB0600E9DB67 /* Release-fakevn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-fakevn";
		};
		79E58AB925D3DB0600E9DB67 /* Release-fakevn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Release-fakevn.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.fake;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-fakevn";
		};
		79E58ABA25D3DB1200E9DB67 /* Release-prodvn */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-prodvn";
		};
		79E58ABB25D3DB1200E9DB67 /* Release-prodvn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodvn";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Runner/RunnerRelease-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Release-prodvn.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DLIVE_ACTIVITY -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-prodvn";
		};
		904DCBEF2B1FE186001A87F7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 649A56ACA05EB15B87595FE7 /* Pods-NotificationServiceExtension.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		904DCBF02B1FE186001A87F7 /* Debug-fakein */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 91A90FA613BFF86B953D48DF /* Pods-NotificationServiceExtension.debug-fakein.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-fakein";
		};
		904DCBF32B1FE186001A87F7 /* Debug-fakevn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 534CAF8D59FE01675E4EFA82 /* Pods-NotificationServiceExtension.debug-fakevn.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.fake.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-fakevn";
		};
		904DCBF42B1FE186001A87F7 /* Debug-prodin */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 87E4CFF906D56A9ADD21BD1E /* Pods-NotificationServiceExtension.debug-prodin.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-prodin";
		};
		904DCBF72B1FE186001A87F7 /* Debug-prodvn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BA1093957B234B298C541EAC /* Pods-NotificationServiceExtension.debug-prodvn.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "NotificationServiceExtension/NotificationServiceExtensionDebug-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Debug-prodvn";
		};
		904DCBF82B1FE186001A87F7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 775F8D2C321A9E9235200BA5 /* Pods-NotificationServiceExtension.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		904DCBF92B1FE186001A87F7 /* Release-fakein */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 58C765CEC0AF029ED6EC9BEC /* Pods-NotificationServiceExtension.release-fakein.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Release-fakein";
		};
		904DCBFC2B1FE186001A87F7 /* Release-fakevn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 18C1ADB39883FF1F86217A4E /* Pods-NotificationServiceExtension.release-fakevn.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.fake.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Release-fakevn";
		};
		904DCBFD2B1FE186001A87F7 /* Release-prodin */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4276B7507FFCFAB2E99FFACD /* Pods-NotificationServiceExtension.release-prodin.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "iPhone Distribution";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Release-prodin";
		};
		904DCC002B1FE186001A87F7 /* Release-prodvn */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 53C7B91C98D3B9866C700515 /* Pods-NotificationServiceExtension.release-prodvn.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "NotificationServiceExtension/NotificationServiceExtensionRelease-prodvn.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AV58HC7RTF;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DSAS -DEXPORT_FCM -DVN";
				PRODUCT_BUNDLE_IDENTIFIER = vn.homecredit.capp.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = "Release-prodvn";
		};
		904DCC012B1FE186001A87F7 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 34182D09C6BC40A82BCE8B39 /* Pods-NotificationServiceExtension.profile.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = $EXTENSION_PROVISIONING_PROFILE;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info/Debug.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = Runner/Info/Release.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		C2358DE3254200400058EA9F /* Debug-fakein */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-fakein";
		};
		C2358DE4254200400058EA9F /* Debug-fakein */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-fakein";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Debug-fakein.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD -DIN";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.fake;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "Koyal Selfcare DEV fake";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-fakein";
		};
		C2358DE9254203C40058EA9F /* Release-fakein */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-fakein";
		};
		C2358DEA254203C40058EA9F /* Release-fakein */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit Fake";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-fakein";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = A52465GU4U;
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_fake.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Release-fakein.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DIN";
				PRODUCT_BUNDLE_IDENTIFIER = net.homecredit.capp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-fakein";
		};
		C2C380E9254079D100D6FD55 /* Release-prodin */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = 1;
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-prodin";
		};
		C2C380EA254079D100D6FD55 /* Release-prodin */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodin";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Release-prodin.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DIN";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-prodin";
		};
		C2C380EB25407A6800D6FD55 /* Debug-prodin */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=*]" = armv7;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = "Debug-prodin";
		};
		C2C380EC25407A6800D6FD55 /* Debug-prodin */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				APP_DISPLAY_NAME = "Home Credit";
				ASSETCATALOG_COMPILER_APPICON_NAME = "AppIcon-prodin";
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				FLUTTER_TARGET = lib/main_prod.dart;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				INFOPLIST_FILE = "Runner/Info/Debug-prodin.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Flutter",
				);
				NEW_SETTING = "";
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEBUG_BUILD -DIN";
				PRODUCT_BUNDLE_IDENTIFIER = demo.homecredit.koyal.selfcare.dev.production;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-prodin";
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		119AFEF12CB3CDBD0047F49E /* Build configuration list for PBXNativeTarget "LiveActivitiesExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				119AFEDD2CB3CDBD0047F49E /* Debug */,
				119AFEDE2CB3CDBD0047F49E /* Debug-fakein */,
				119AFEE12CB3CDBD0047F49E /* Debug-fakevn */,
				119AFEE22CB3CDBD0047F49E /* Debug-prodin */,
				119AFEE52CB3CDBD0047F49E /* Debug-prodvn */,
				119AFEE62CB3CDBD0047F49E /* Release */,
				119AFEE72CB3CDBD0047F49E /* Release-fakein */,
				119AFEEA2CB3CDBD0047F49E /* Release-fakevn */,
				119AFEEB2CB3CDBD0047F49E /* Release-prodin */,
				119AFEEE2CB3CDBD0047F49E /* Release-prodvn */,
				119AFEEF2CB3CDBD0047F49E /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		11A8A74A2C9A8B8900B58ABD /* Build configuration list for PBXNativeTarget "HomeCreditVietnam" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11A8A74B2C9A8B8900B58ABD /* Debug */,
				11A8A74C2C9A8B8900B58ABD /* Debug-fakein */,
				11A8A74F2C9A8B8900B58ABD /* Debug-fakevn */,
				11A8A7502C9A8B8900B58ABD /* Debug-prodin */,
				11A8A7532C9A8B8900B58ABD /* Debug-prodvn */,
				11A8A7542C9A8B8900B58ABD /* Release */,
				11A8A7552C9A8B8900B58ABD /* Release-fakein */,
				11A8A7582C9A8B8900B58ABD /* Release-fakevn */,
				11A8A7592C9A8B8900B58ABD /* Release-prodin */,
				11A8A75C2C9A8B8900B58ABD /* Release-prodvn */,
				11A8A75D2C9A8B8900B58ABD /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		904DCC022B1FE186001A87F7 /* Build configuration list for PBXNativeTarget "NotificationServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				904DCBEF2B1FE186001A87F7 /* Debug */,
				904DCBF02B1FE186001A87F7 /* Debug-fakein */,
				904DCBF32B1FE186001A87F7 /* Debug-fakevn */,
				904DCBF42B1FE186001A87F7 /* Debug-prodin */,
				904DCBF72B1FE186001A87F7 /* Debug-prodvn */,
				904DCBF82B1FE186001A87F7 /* Release */,
				904DCBF92B1FE186001A87F7 /* Release-fakein */,
				904DCBFC2B1FE186001A87F7 /* Release-fakevn */,
				904DCBFD2B1FE186001A87F7 /* Release-prodin */,
				904DCC002B1FE186001A87F7 /* Release-prodvn */,
				904DCC012B1FE186001A87F7 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				C2358DE3254200400058EA9F /* Debug-fakein */,
				79E58AB425D3DABE00E9DB67 /* Debug-fakevn */,
				C2C380EB25407A6800D6FD55 /* Debug-prodin */,
				79E58AB625D3DADD00E9DB67 /* Debug-prodvn */,
				97C147041CF9000F007C117D /* Release */,
				C2358DE9254203C40058EA9F /* Release-fakein */,
				79E58AB825D3DB0600E9DB67 /* Release-fakevn */,
				C2C380E9254079D100D6FD55 /* Release-prodin */,
				79E58ABA25D3DB1200E9DB67 /* Release-prodvn */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				C2358DE4254200400058EA9F /* Debug-fakein */,
				79E58AB525D3DABE00E9DB67 /* Debug-fakevn */,
				C2C380EC25407A6800D6FD55 /* Debug-prodin */,
				79E58AB725D3DADD00E9DB67 /* Debug-prodvn */,
				97C147071CF9000F007C117D /* Release */,
				C2358DEA254203C40058EA9F /* Release-fakein */,
				79E58AB925D3DB0600E9DB67 /* Release-fakevn */,
				C2C380EA254079D100D6FD55 /* Release-prodin */,
				79E58ABB25D3DB1200E9DB67 /* Release-prodvn */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
