PODS:
  - abseil/algorithm (1.********.2):
    - abseil/algorithm/algorithm (= 1.********.2)
    - abseil/algorithm/container (= 1.********.2)
  - abseil/algorithm/algorithm (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/algorithm/container (1.********.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base (1.********.2):
    - abseil/base/atomic_hook (= 1.********.2)
    - abseil/base/base (= 1.********.2)
    - abseil/base/base_internal (= 1.********.2)
    - abseil/base/config (= 1.********.2)
    - abseil/base/core_headers (= 1.********.2)
    - abseil/base/cycleclock_internal (= 1.********.2)
    - abseil/base/dynamic_annotations (= 1.********.2)
    - abseil/base/endian (= 1.********.2)
    - abseil/base/errno_saver (= 1.********.2)
    - abseil/base/fast_type_id (= 1.********.2)
    - abseil/base/log_severity (= 1.********.2)
    - abseil/base/malloc_internal (= 1.********.2)
    - abseil/base/no_destructor (= 1.********.2)
    - abseil/base/nullability (= 1.********.2)
    - abseil/base/prefetch (= 1.********.2)
    - abseil/base/pretty_function (= 1.********.2)
    - abseil/base/raw_logging_internal (= 1.********.2)
    - abseil/base/spinlock_wait (= 1.********.2)
    - abseil/base/strerror (= 1.********.2)
    - abseil/base/throw_delegate (= 1.********.2)
  - abseil/base/atomic_hook (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/base (1.********.2):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/base_internal (1.********.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/config (1.********.2):
    - abseil/xcprivacy
  - abseil/base/core_headers (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/cycleclock_internal (1.********.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/dynamic_annotations (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/endian (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/xcprivacy
  - abseil/base/errno_saver (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/fast_type_id (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/log_severity (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/malloc_internal (1.********.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/base/no_destructor (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/base/nullability (1.********.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/base/prefetch (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/base/pretty_function (1.********.2):
    - abseil/xcprivacy
  - abseil/base/raw_logging_internal (1.********.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
    - abseil/xcprivacy
  - abseil/base/spinlock_wait (1.********.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/strerror (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/xcprivacy
  - abseil/base/throw_delegate (1.********.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
    - abseil/xcprivacy
  - abseil/cleanup/cleanup_internal (1.********.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/common (1.********.2):
    - abseil/meta/type_traits
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/container/common_policy_traits (1.********.2):
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/compressed_tuple (1.********.2):
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/container_memory (1.********.2):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/fixed_array (1.********.2):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_map (1.********.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/flat_hash_set (1.********.2):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
    - abseil/xcprivacy
  - abseil/container/hash_function_defaults (1.********.2):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/container/hash_policy_traits (1.********.2):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/hashtable_debug_hooks (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/container/hashtablez_sampler (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/inlined_vector (1.********.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/container/inlined_vector_internal (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/container/layout (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/container/raw_hash_map (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
    - abseil/xcprivacy
  - abseil/container/raw_hash_set (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/crc/cpu_detect (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/crc32c (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_cord_state (1.********.2):
    - abseil/base/config
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/crc/crc_internal (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/crc/non_temporal_arm_intrinsics (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/crc/non_temporal_memcpy (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
    - abseil/xcprivacy
  - abseil/debugging/debugging_internal (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/debugging/demangle_internal (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/debugging/stacktrace (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/xcprivacy
  - abseil/debugging/symbolize (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/commandlineflag (1.********.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/commandlineflag_internal (1.********.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/xcprivacy
  - abseil/flags/config (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/flag (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/flag_internal (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/flags/marshalling (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/flags/path_util (1.********.2):
    - abseil/base/config
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/private_handle_accessor (1.********.2):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/flags/program_name (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/flags/reflection (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/functional/any_invocable (1.********.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/bind_front (1.********.2):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/functional/function_ref (1.********.2):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/hash/city (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/xcprivacy
  - abseil/hash/hash (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/hash/low_level_hash (1.********.2):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/memory (1.********.2):
    - abseil/memory/memory (= 1.********.2)
  - abseil/memory/memory (1.********.2):
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/meta (1.********.2):
    - abseil/meta/type_traits (= 1.********.2)
  - abseil/meta/type_traits (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/bits (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/numeric/int128 (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/numeric/representation (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/profiling/exponential_biased (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/xcprivacy
  - abseil/profiling/sample_recorder (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/random/bit_gen_ref (1.********.2):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
    - abseil/xcprivacy
  - abseil/random/distributions (1.********.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
    - abseil/xcprivacy
  - abseil/random/internal/distribution_caller (1.********.2):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/random/internal/fast_uniform_bits (1.********.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/fastmath (1.********.2):
    - abseil/numeric/bits
    - abseil/xcprivacy
  - abseil/random/internal/generate_real (1.********.2):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/iostream_state_saver (1.********.2):
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/nonsecure_base (1.********.2):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/pcg_engine (1.********.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
    - abseil/xcprivacy
  - abseil/random/internal/platform (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/internal/pool_urbg (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/randen (1.********.2):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
    - abseil/xcprivacy
  - abseil/random/internal/randen_engine (1.********.2):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes (1.********.2):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
    - abseil/xcprivacy
  - abseil/random/internal/randen_hwaes_impl (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/randen_slow (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
    - abseil/xcprivacy
  - abseil/random/internal/salted_seed_seq (1.********.2):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/seed_material (1.********.2):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/random/internal/traits (1.********.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/xcprivacy
  - abseil/random/internal/uniform_helper (1.********.2):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/internal/wide_multiply (1.********.2):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
    - abseil/xcprivacy
  - abseil/random/random (1.********.2):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
    - abseil/xcprivacy
  - abseil/random/seed_gen_exception (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/random/seed_sequences (1.********.2):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/status (1.********.2):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/status/statusor (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/charset (1.********.2):
    - abseil/base/core_headers
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/strings/cord (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cord_internal (1.********.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_functions (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
    - abseil/xcprivacy
  - abseil/strings/cordz_handle (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
    - abseil/xcprivacy
  - abseil/strings/cordz_info (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/cordz_statistics (1.********.2):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_scope (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
    - abseil/xcprivacy
  - abseil/strings/cordz_update_tracker (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/has_ostream_operator (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/strings/internal (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/strings/str_format (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
    - abseil/xcprivacy
  - abseil/strings/str_format_internal (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/strings/string_view (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/xcprivacy
  - abseil/strings/strings (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
    - abseil/xcprivacy
  - abseil/synchronization/graphcycles_internal (1.********.2):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/synchronization/kernel_timeout_internal (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/synchronization/synchronization (1.********.2):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
    - abseil/xcprivacy
  - abseil/time (1.********.2):
    - abseil/time/internal (= 1.********.2)
    - abseil/time/time (= 1.********.2)
  - abseil/time/internal (1.********.2):
    - abseil/time/internal/cctz (= 1.********.2)
  - abseil/time/internal/cctz (1.********.2):
    - abseil/time/internal/cctz/civil_time (= 1.********.2)
    - abseil/time/internal/cctz/time_zone (= 1.********.2)
  - abseil/time/internal/cctz/civil_time (1.********.2):
    - abseil/base/config
    - abseil/xcprivacy
  - abseil/time/internal/cctz/time_zone (1.********.2):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
    - abseil/xcprivacy
  - abseil/time/time (1.********.2):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
    - abseil/xcprivacy
  - abseil/types (1.********.2):
    - abseil/types/any (= 1.********.2)
    - abseil/types/bad_any_cast (= 1.********.2)
    - abseil/types/bad_any_cast_impl (= 1.********.2)
    - abseil/types/bad_optional_access (= 1.********.2)
    - abseil/types/bad_variant_access (= 1.********.2)
    - abseil/types/compare (= 1.********.2)
    - abseil/types/optional (= 1.********.2)
    - abseil/types/span (= 1.********.2)
    - abseil/types/variant (= 1.********.2)
  - abseil/types/any (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/bad_any_cast (1.********.2):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
    - abseil/xcprivacy
  - abseil/types/bad_any_cast_impl (1.********.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_optional_access (1.********.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/bad_variant_access (1.********.2):
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/xcprivacy
  - abseil/types/compare (1.********.2):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/optional (1.********.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/types/span (1.********.2):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/types/variant (1.********.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
    - abseil/xcprivacy
  - abseil/utility/utility (1.********.2):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/xcprivacy
  - abseil/xcprivacy (1.********.2)
  - account_manager (0.0.1):
    - Flutter
  - advertising_id (0.0.1):
    - Flutter
  - app_links (0.0.2):
    - Flutter
  - app_settings (5.1.1):
    - Flutter
  - app_tracking_transparency (0.0.1):
    - Flutter
  - appsflyer_sdk (6.15.3):
    - AppsFlyerFramework (= 6.15.3)
    - Flutter
  - AppsFlyerFramework (6.15.3):
    - AppsFlyerFramework/Main (= 6.15.3)
  - AppsFlyerFramework/Main (6.15.3)
  - biometric_signature (5.1.2):
    - Flutter
  - BoringSSL-GRPC (0.0.32):
    - BoringSSL-GRPC/Implementation (= 0.0.32)
    - BoringSSL-GRPC/Interface (= 0.0.32)
  - BoringSSL-GRPC/Implementation (0.0.32):
    - BoringSSL-GRPC/Interface (= 0.0.32)
  - BoringSSL-GRPC/Interface (0.0.32)
  - camera_avfoundation (0.0.1):
    - Flutter
  - capp_plugins (0.0.1):
    - Flutter
  - cloud_firestore (5.2.1):
    - Firebase/Firestore (= 10.29.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - CredoAppCalendarEvents (3.2.0)
  - CredoAppCore (3.2.0)
  - CredoAppIovation (3.3.0)
  - CredoAppMedia (3.2.0)
  - credolab (0.0.2):
    - Flutter
  - device_calendar (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - dot-camera (8.7.0)
  - dot-capture (8.7.0)
  - dot-core (8.7.0)
  - dot-document (8.7.0):
    - dot-camera (= 8.7.0)
    - dot-capture (= 8.7.0)
    - dot-core (= 8.7.0)
    - dot-document-commons (= 8.7.0)
    - dot-protobuf (= 1.12.0)
  - dot-document-commons (8.7.0)
  - dot-face-background-uniformity (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-commons (8.7.0)
  - dot-face-core (8.7.0):
    - dot-camera (= 8.7.0)
    - dot-capture (= 8.7.0)
    - dot-core (= 8.7.0)
    - dot-face-commons (= 8.7.0)
    - dot-protobuf (= 1.12.0)
    - iface (= 5.1.2)
  - dot-face-detection-balanced (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-expression-neutral (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-eye-gaze-liveness (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-face-passive-liveness (8.7.0):
    - dot-face-core (= 8.7.0)
  - dot-protobuf (1.12.0)
  - DTTJailbreakDetection (0.4.0)
  - file_saver (0.0.1):
    - Flutter
  - Firebase/Analytics (10.29.0):
    - Firebase/Core
  - Firebase/Core (10.29.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.29.0)
  - Firebase/CoreOnly (10.29.0):
    - FirebaseCore (= 10.29.0)
  - Firebase/Crashlytics (10.29.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.29.0)
  - Firebase/DynamicLinks (10.29.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 10.29.0)
  - Firebase/Firestore (10.29.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.29.0)
  - Firebase/Messaging (10.29.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.29.0)
  - Firebase/Performance (10.29.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 10.29.0)
  - Firebase/RemoteConfig (10.29.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.29.0)
  - firebase_analytics (11.2.1):
    - Firebase/Analytics (= 10.29.0)
    - firebase_core
    - Flutter
  - firebase_core (3.3.0):
    - Firebase/CoreOnly (= 10.29.0)
    - Flutter
  - firebase_crashlytics (4.0.4):
    - Firebase/Crashlytics (= 10.29.0)
    - firebase_core
    - Flutter
  - firebase_dynamic_links (6.0.4):
    - Firebase/DynamicLinks (= 10.29.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.0.4):
    - Firebase/Messaging (= 10.29.0)
    - firebase_core
    - Flutter
  - firebase_performance (0.10.0-4):
    - Firebase/Performance (= 10.29.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.0.4):
    - Firebase/RemoteConfig (= 10.29.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.29.0):
    - FirebaseAnalytics/AdIdSupport (= 10.29.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseCore (10.29.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseDynamicLinks (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseFirestore (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseFirestoreInternal (= 10.29.0)
    - FirebaseSharedSwift (~> 10.0)
  - FirebaseFirestoreInternal (10.29.0):
    - abseil/algorithm (~> 1.********.1)
    - abseil/base (~> 1.********.1)
    - abseil/container/flat_hash_map (~> 1.********.1)
    - abseil/memory (~> 1.********.1)
    - abseil/meta (~> 1.********.1)
    - abseil/strings/strings (~> 1.********.1)
    - abseil/time (~> 1.********.1)
    - abseil/types (~> 1.********.1)
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.62.0)"
    - gRPC-Core (~> 1.62.0)
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.29.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebasePerformance (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfig (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/ISASwizzler (~> 7.13)
    - GoogleUtilities/MethodSwizzler (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.29.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.29.0)
  - Flutter (1.0.0)
  - flutter_app_badger (1.3.0):
    - Flutter
  - flutter_custom_tabs_ios (2.0.0):
    - Flutter
  - flutter_document_picker (0.0.1):
    - Flutter
  - flutter_html_to_pdf (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0)
  - flutter_innovatrics_vn (0.0.1):
    - dot-document (= 8.7.0)
    - dot-face-background-uniformity (= 8.7.0)
    - dot-face-core (= 8.7.0)
    - dot-face-detection-balanced (= 8.7.0)
    - dot-face-expression-neutral (= 8.7.0)
    - dot-face-eye-gaze-liveness (= 8.7.0)
    - dot-face-passive-liveness (= 8.7.0)
    - Flutter
  - flutter_ios_calendar_events (4.2.0):
    - CredoAppCalendarEvents (= 3.2.0)
    - CredoAppCore
    - Flutter
  - flutter_ios_core_proxy (6.3.0):
    - CredoAppCore (= 3.2.0)
    - Flutter
  - flutter_ios_iovation (6.2.0):
    - CredoAppCore
    - CredoAppIovation (= 3.3.0)
    - Flutter
    - FraudForce (= 5.6.0)
  - flutter_ios_media (4.2.0):
    - CredoAppCore
    - CredoAppMedia (= 3.2.0)
    - Flutter
  - flutter_libphonenumber (0.0.1):
    - Flutter
    - PhoneNumberKit/PhoneNumberKitCore (~> 3.3.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_contact_picker (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_pin_encryption (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - flutter_true_call (1.9.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FraudForce (5.6.0)
  - geolocator_apple (1.2.0):
    - Flutter
  - gma_vault (0.0.1):
    - Flutter
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_mlkit_barcode_scanning (0.12.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - google_mlkit_commons (0.7.1):
    - Flutter
    - MLKitVision
  - GoogleAppMeasurement (10.29.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.29.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.29.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleMLKit/BarcodeScanning (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - "gRPC-C++ (1.62.5)":
    - "gRPC-C++/Implementation (= 1.62.5)"
    - "gRPC-C++/Interface (= 1.62.5)"
  - "gRPC-C++/Implementation (1.62.5)":
    - abseil/algorithm/container (~> 1.********.2)
    - abseil/base/base (~> 1.********.2)
    - abseil/base/config (~> 1.********.2)
    - abseil/base/core_headers (~> 1.********.2)
    - abseil/cleanup/cleanup (~> 1.********.2)
    - abseil/container/flat_hash_map (~> 1.********.2)
    - abseil/container/flat_hash_set (~> 1.********.2)
    - abseil/container/inlined_vector (~> 1.********.2)
    - abseil/flags/flag (~> 1.********.2)
    - abseil/flags/marshalling (~> 1.********.2)
    - abseil/functional/any_invocable (~> 1.********.2)
    - abseil/functional/bind_front (~> 1.********.2)
    - abseil/functional/function_ref (~> 1.********.2)
    - abseil/hash/hash (~> 1.********.2)
    - abseil/memory/memory (~> 1.********.2)
    - abseil/meta/type_traits (~> 1.********.2)
    - abseil/random/bit_gen_ref (~> 1.********.2)
    - abseil/random/distributions (~> 1.********.2)
    - abseil/random/random (~> 1.********.2)
    - abseil/status/status (~> 1.********.2)
    - abseil/status/statusor (~> 1.********.2)
    - abseil/strings/cord (~> 1.********.2)
    - abseil/strings/str_format (~> 1.********.2)
    - abseil/strings/strings (~> 1.********.2)
    - abseil/synchronization/synchronization (~> 1.********.2)
    - abseil/time/time (~> 1.********.2)
    - abseil/types/optional (~> 1.********.2)
    - abseil/types/span (~> 1.********.2)
    - abseil/types/variant (~> 1.********.2)
    - abseil/utility/utility (~> 1.********.2)
    - "gRPC-C++/Interface (= 1.62.5)"
    - "gRPC-C++/Privacy (= 1.62.5)"
    - gRPC-Core (= 1.62.5)
  - "gRPC-C++/Interface (1.62.5)"
  - "gRPC-C++/Privacy (1.62.5)"
  - gRPC-Core (1.62.5):
    - gRPC-Core/Implementation (= 1.62.5)
    - gRPC-Core/Interface (= 1.62.5)
  - gRPC-Core/Implementation (1.62.5):
    - abseil/algorithm/container (~> 1.********.2)
    - abseil/base/base (~> 1.********.2)
    - abseil/base/config (~> 1.********.2)
    - abseil/base/core_headers (~> 1.********.2)
    - abseil/cleanup/cleanup (~> 1.********.2)
    - abseil/container/flat_hash_map (~> 1.********.2)
    - abseil/container/flat_hash_set (~> 1.********.2)
    - abseil/container/inlined_vector (~> 1.********.2)
    - abseil/flags/flag (~> 1.********.2)
    - abseil/flags/marshalling (~> 1.********.2)
    - abseil/functional/any_invocable (~> 1.********.2)
    - abseil/functional/bind_front (~> 1.********.2)
    - abseil/functional/function_ref (~> 1.********.2)
    - abseil/hash/hash (~> 1.********.2)
    - abseil/memory/memory (~> 1.********.2)
    - abseil/meta/type_traits (~> 1.********.2)
    - abseil/random/bit_gen_ref (~> 1.********.2)
    - abseil/random/distributions (~> 1.********.2)
    - abseil/random/random (~> 1.********.2)
    - abseil/status/status (~> 1.********.2)
    - abseil/status/statusor (~> 1.********.2)
    - abseil/strings/cord (~> 1.********.2)
    - abseil/strings/str_format (~> 1.********.2)
    - abseil/strings/strings (~> 1.********.2)
    - abseil/synchronization/synchronization (~> 1.********.2)
    - abseil/time/time (~> 1.********.2)
    - abseil/types/optional (~> 1.********.2)
    - abseil/types/span (~> 1.********.2)
    - abseil/types/variant (~> 1.********.2)
    - abseil/utility/utility (~> 1.********.2)
    - BoringSSL-GRPC (= 0.0.32)
    - gRPC-Core/Interface (= 1.62.5)
    - gRPC-Core/Privacy (= 1.62.5)
  - gRPC-Core/Interface (1.62.5)
  - gRPC-Core/Privacy (1.62.5)
  - GTMSessionFetcher/Core (3.5.0)
  - iface (5.1.2):
    - onnx (= 2.1.2)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (0.2.0):
    - Flutter
  - installer_checker (0.0.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - ios_teamid (0.0.1):
    - Flutter
  - launch_app_store (0.0.3):
    - Flutter
  - leveldb-library (1.22.6)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - maps_launcher (0.0.1):
    - Flutter
  - memory_info (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta5)
  - MLKitBarcodeScanning (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - NFCPassportReader (1.1.6):
    - OpenSSL-Universal (= 1.1.180)
  - onepay_custom_deeplink (0.0.1):
    - Flutter
  - onnx (2.1.2)
  - open_filex (0.0.2):
    - Flutter
  - OpenSSL-Universal (1.1.180)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pdf_combiner (0.0.1):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - PhoneNumberKit/PhoneNumberKitCore (3.3.4)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - restart_app (0.0.1):
    - Flutter
  - rive_common (0.0.1):
    - Flutter
  - safe_device (1.0.0):
    - DTTJailbreakDetection
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - secure_application (0.0.1):
    - Flutter
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sms_autofill (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (3.0.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - vn_idcard_reader (0.0.1):
    - Flutter
    - NFCPassportReader (= 1.1.6)
  - vnpay (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - workmanager (0.0.1):
    - Flutter
  - zalopay (0.0.1):
    - Flutter

DEPENDENCIES:
  - account_manager (from `.symlinks/plugins/account_manager/ios`)
  - advertising_id (from `.symlinks/plugins/advertising_id/ios`)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - biometric_signature (from `.symlinks/plugins/biometric_signature/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - capp_plugins (from `.symlinks/plugins/capp_plugins/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - credolab (from `.symlinks/plugins/credolab/ios`)
  - device_calendar (from `.symlinks/plugins/device_calendar/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_saver (from `.symlinks/plugins/file_saver/ios`)
  - Firebase/Messaging
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_performance (from `.symlinks/plugins/firebase_performance/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_badger (from `.symlinks/plugins/flutter_app_badger/ios`)
  - flutter_custom_tabs_ios (from `.symlinks/plugins/flutter_custom_tabs_ios/ios`)
  - flutter_document_picker (from `.symlinks/plugins/flutter_document_picker/ios`)
  - flutter_html_to_pdf (from `.symlinks/plugins/flutter_html_to_pdf/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_innovatrics_vn (from `.symlinks/plugins/flutter_innovatrics_vn/ios`)
  - flutter_ios_calendar_events (from `.symlinks/plugins/flutter_ios_calendar_events/ios`)
  - flutter_ios_core_proxy (from `.symlinks/plugins/flutter_ios_core_proxy/ios`)
  - flutter_ios_iovation (from `.symlinks/plugins/flutter_ios_iovation/ios`)
  - flutter_ios_media (from `.symlinks/plugins/flutter_ios_media/ios`)
  - flutter_libphonenumber (from `.symlinks/plugins/flutter_libphonenumber/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_contact_picker (from `.symlinks/plugins/flutter_native_contact_picker/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_pin_encryption (from `.symlinks/plugins/flutter_pin_encryption/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - flutter_true_call (from `.symlinks/plugins/flutter_true_call/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - gma_vault (from `.symlinks/plugins/gma_vault/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_mlkit_barcode_scanning (from `.symlinks/plugins/google_mlkit_barcode_scanning/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - GoogleUtilities
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - installer_checker (from `.symlinks/plugins/installer_checker/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - ios_teamid (from `.symlinks/plugins/ios_teamid/ios`)
  - launch_app_store (from `.symlinks/plugins/launch_app_store/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - maps_launcher (from `.symlinks/plugins/maps_launcher/ios`)
  - memory_info (from `.symlinks/plugins/memory_info/ios`)
  - onepay_custom_deeplink (from `.symlinks/plugins/onepay_custom_deeplink/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pdf_combiner (from `.symlinks/plugins/pdf_combiner/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - rive_common (from `.symlinks/plugins/rive_common/ios`)
  - safe_device (from `.symlinks/plugins/safe_device/ios`)
  - secure_application (from `.symlinks/plugins/secure_application/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sms_autofill (from `.symlinks/plugins/sms_autofill/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - vn_idcard_reader (from `.symlinks/plugins/vn_idcard_reader/ios`)
  - vnpay (from `.symlinks/plugins/vnpay/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - workmanager (from `.symlinks/plugins/workmanager/ios`)
  - zalopay (from `.symlinks/plugins/zalopay/ios`)

SPEC REPOS:
  https://dl.cloudsmith.io/4tzVdnVcQodJri9l/credolab/proxy-sdk/cocoapods/index.git:
    - CredoAppCalendarEvents
    - CredoAppCore
    - CredoAppIovation
    - CredoAppMedia
    - FraudForce
  https://github.com/innovatrics/innovatrics-podspecs:
    - dot-camera
    - dot-capture
    - dot-core
    - dot-document
    - dot-document-commons
    - dot-face-background-uniformity
    - dot-face-commons
    - dot-face-core
    - dot-face-detection-balanced
    - dot-face-expression-neutral
    - dot-face-eye-gaze-liveness
    - dot-face-passive-liveness
    - dot-protobuf
    - iface
    - onnx
  trunk:
    - abseil
    - AppsFlyerFramework
    - BoringSSL-GRPC
    - DTTJailbreakDetection
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDynamicLinks
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - leveldb-library
    - libwebp
    - Mantle
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - NFCPassportReader
    - OpenSSL-Universal
    - OrderedSet
    - PhoneNumberKit
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - Toast

EXTERNAL SOURCES:
  account_manager:
    :path: ".symlinks/plugins/account_manager/ios"
  advertising_id:
    :path: ".symlinks/plugins/advertising_id/ios"
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  biometric_signature:
    :path: ".symlinks/plugins/biometric_signature/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  capp_plugins:
    :path: ".symlinks/plugins/capp_plugins/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  credolab:
    :path: ".symlinks/plugins/credolab/ios"
  device_calendar:
    :path: ".symlinks/plugins/device_calendar/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_saver:
    :path: ".symlinks/plugins/file_saver/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_performance:
    :path: ".symlinks/plugins/firebase_performance/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_app_badger:
    :path: ".symlinks/plugins/flutter_app_badger/ios"
  flutter_custom_tabs_ios:
    :path: ".symlinks/plugins/flutter_custom_tabs_ios/ios"
  flutter_document_picker:
    :path: ".symlinks/plugins/flutter_document_picker/ios"
  flutter_html_to_pdf:
    :path: ".symlinks/plugins/flutter_html_to_pdf/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_innovatrics_vn:
    :path: ".symlinks/plugins/flutter_innovatrics_vn/ios"
  flutter_ios_calendar_events:
    :path: ".symlinks/plugins/flutter_ios_calendar_events/ios"
  flutter_ios_core_proxy:
    :path: ".symlinks/plugins/flutter_ios_core_proxy/ios"
  flutter_ios_iovation:
    :path: ".symlinks/plugins/flutter_ios_iovation/ios"
  flutter_ios_media:
    :path: ".symlinks/plugins/flutter_ios_media/ios"
  flutter_libphonenumber:
    :path: ".symlinks/plugins/flutter_libphonenumber/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_contact_picker:
    :path: ".symlinks/plugins/flutter_native_contact_picker/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_pin_encryption:
    :path: ".symlinks/plugins/flutter_pin_encryption/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  flutter_true_call:
    :path: ".symlinks/plugins/flutter_true_call/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  gma_vault:
    :path: ".symlinks/plugins/gma_vault/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_mlkit_barcode_scanning:
    :path: ".symlinks/plugins/google_mlkit_barcode_scanning/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  installer_checker:
    :path: ".symlinks/plugins/installer_checker/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  ios_teamid:
    :path: ".symlinks/plugins/ios_teamid/ios"
  launch_app_store:
    :path: ".symlinks/plugins/launch_app_store/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  maps_launcher:
    :path: ".symlinks/plugins/maps_launcher/ios"
  memory_info:
    :path: ".symlinks/plugins/memory_info/ios"
  onepay_custom_deeplink:
    :path: ".symlinks/plugins/onepay_custom_deeplink/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pdf_combiner:
    :path: ".symlinks/plugins/pdf_combiner/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  rive_common:
    :path: ".symlinks/plugins/rive_common/ios"
  safe_device:
    :path: ".symlinks/plugins/safe_device/ios"
  secure_application:
    :path: ".symlinks/plugins/secure_application/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sms_autofill:
    :path: ".symlinks/plugins/sms_autofill/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  vn_idcard_reader:
    :path: ".symlinks/plugins/vn_idcard_reader/ios"
  vnpay:
    :path: ".symlinks/plugins/vnpay/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"
  workmanager:
    :path: ".symlinks/plugins/workmanager/ios"
  zalopay:
    :path: ".symlinks/plugins/zalopay/ios"

SPEC CHECKSUMS:
  abseil: d121da9ef7e2ff4cab7666e76c5a3e0915ae08c3
  account_manager: 20c07e67f61d51e07976ec1a9c36ed51cc2589ab
  advertising_id: ea8a9b898196a0673bad1b0fe6c50541d9295d02
  app_links: e7a6750a915a9e161c58d91bc610e8cd1d4d0ad0
  app_settings: 017320c6a680cdc94c799949d95b84cb69389ebc
  app_tracking_transparency: e169b653478da7bb15a6c61209015378ca73e375
  appsflyer_sdk: 0132c7cd33db42ce3daf878f02809a9f88756f9f
  AppsFlyerFramework: ad7ff0d22aa36c7f8cc4f71a5424e19b89ccb8ae
  biometric_signature: b2b4fcff432624eec1615b06c4636105193e143e
  BoringSSL-GRPC: 1e2348957acdbcad360b80a264a90799984b2ba6
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  capp_plugins: 4eb95332ee5004bb1258515bf3707ac438828c75
  cloud_firestore: 8a2ff2bb050f87d0ca996866625cefbe2395a83b
  connectivity_plus: 4c41c08fc6d7c91f63bc7aec70ffe3730b04f563
  CredoAppCalendarEvents: fd1d5a689fa724b106c3a3813ea81695d3000ec3
  CredoAppCore: 32a500698736ac4441befb38e8a899d42a0a5b40
  CredoAppIovation: fd12419b51f09cb23d64c6c985dbad4bf9c202f1
  CredoAppMedia: f52b12ef4d08f8f4850716f6d826feb8aa184266
  credolab: ffdc1aa70c0bd38dc3ff1733b882b0afd15a9c2b
  device_calendar: 9cb33f88a02e19652ec7b8b122ca778f751b1f7b
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  dot-camera: 0f642adc3830ef9e972e6b1a17b0cc3d2657d396
  dot-capture: 76accabe96bfb704a752b93e26003a5ddfd13040
  dot-core: 6f5eacf2512010edbd58c6121d6406fc41eaa6b3
  dot-document: dc40ee7b7431646c19c98521a999d7c3c03ed33e
  dot-document-commons: fd6a31907c3923d38c4e9fb7fc76226e27770347
  dot-face-background-uniformity: 25ea7db29b04b42869c1f57384a22d4cf1d4cfb4
  dot-face-commons: 8c06dd50e04502dda7ba3c9ad0d91d4dbd9750db
  dot-face-core: 680c68cd51c411d1c0a2e9989fb5d87bece8e24f
  dot-face-detection-balanced: 09f42b276c80b5f55c5cc5e22a0fbbd658fe3fcd
  dot-face-expression-neutral: f955b930aadad4902369cc2afe9cab7506aa56c0
  dot-face-eye-gaze-liveness: c5adc76430d4156ba288c041f2a4957a435c1eb6
  dot-face-passive-liveness: ba884b4898924b8e74b9528a31ad96458820f093
  dot-protobuf: c801e41197bcb48b2ee0dd5ef2a16e4fff9cc28e
  DTTJailbreakDetection: 5e356c5badc17995f65a83ed9483f787a0057b71
  file_saver: 503e386464dbe118f630e17b4c2e1190fa0cf808
  Firebase: cec914dab6fd7b1bd8ab56ea07ce4e03dd251c2d
  firebase_analytics: 04491d1ee74c8e7c2330c96afc54188a969b06ee
  firebase_core: 57aeb91680e5d5e6df6b888064be7c785f146efb
  firebase_crashlytics: e3d3e0c99bad5aaab5908385133dea8ec344693f
  firebase_dynamic_links: 550e8cefbdee7b6e74adfebb3cc4d340fa72f6c8
  firebase_messaging: c862b3d2b973ecc769194dc8de09bd22c77ae757
  firebase_performance: 8643e815a354ee94da1192cd69335a48a7b625a4
  firebase_remote_config: 622c7e72a9349b7db48658ea902d9f2f1f2d7aaa
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAnalytics: 23717de130b779aa506e757edb9713d24b6ffeda
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseCore: 30e9c1cbe3d38f5f5e75f48bfcea87d7c358ec16
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 34647b41e18de773717fdd348a22206f2f9bc774
  FirebaseDynamicLinks: 83c278fcae48ac2cf8c3fb10f64f3d469dadcf9b
  FirebaseFirestore: f258936f52d712337233182b90042a76ff48dce0
  FirebaseFirestoreInternal: f43d25cc04835ec3aa1885f4fc946a1a4f9e1c56
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 7b5d8033e183ab59eb5b852a53201559e976d366
  FirebasePerformance: d0ac4aa90f8c1aedeb8d0329a56e2d77d8d9e004
  FirebaseRemoteConfig: 48ef3f243742a8d72422ccfc9f986e19d7de53fd
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  FirebaseSharedSwift: 20530f495084b8d840f78a100d8c5ee613375f6e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_badger: 824f956e8d58c7d9153ba0bf519423be4204e0ae
  flutter_custom_tabs_ios: a651b18786388923b62de8c0537607de87c2eccf
  flutter_document_picker: 109ba1c4622e6e62bfced17c22ff544a9a3541f4
  flutter_html_to_pdf: 0fd9f8d04f335d68b06a7c4ab5fc85d22e1ef91b
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_inappwebview_ios: 11d5efb5b27e88df178a49689dd97bef2c03cfae
  flutter_innovatrics_vn: ebc6005f1721932e2aa0053ba8da848abc683948
  flutter_ios_calendar_events: cd624f02457fe61b76c1eee52b9a7be035c09937
  flutter_ios_core_proxy: 7fdf216119586e87ecfed136110b0d2b66798f29
  flutter_ios_iovation: ecb04baddccd8dfab052d096a157f57f6d5ad010
  flutter_ios_media: f6eb3537f480c3568ff20f5251d7b83f767bf6c4
  flutter_libphonenumber: 6a23afdf063b34d1e53e6dbd6c6bc905d18e8e46
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_native_contact_picker: bd430ba0fbf82768bb50c2c52a69a65759a8f907
  flutter_pdfview: 25f53dd6097661e6395b17de506e6060585946bd
  flutter_pin_encryption: d626e31cf84027b5affd9df61fafdd0477bcea1e
  flutter_secure_storage: 23fc622d89d073675f2eaa109381aefbcf5a49be
  flutter_timezone: ac3da59ac941ff1c98a2e1f0293420e020120282
  flutter_true_call: d3985dfa237db950b3bc73a42f80142fb238915b
  fluttertoast: 9f2f8e81bb5ce18facb9748d7855bf5a756fe3db
  FraudForce: 12b9b24ad2782332654a70da67f25c08c1720433
  geolocator_apple: 6cbaf322953988e009e5ecb481f07efece75c450
  gma_vault: c5ae800f4dde770580a2e41f5a73e6a59e0ea572
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  google_mlkit_barcode_scanning: 383d0c2dd62d92b36c0b10c7e69cf98d3650dc40
  google_mlkit_commons: 96aaca445520311b84a2da013dedf3427fe4cc69
  GoogleAppMeasurement: f9de05ee17401e3355f68e8fc8b5064d429f5918
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  "gRPC-C++": e725ef63c4475d7cdb7e2cf16eb0fde84bd9ee51
  gRPC-Core: eee4be35df218649fe66d721a05a7f27a28f069b
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  iface: 4767a90a11587115add991240183e96223848750
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_review: 318597b3a06c22bb46dc454d56828c85f444f99d
  installer_checker: 17469c1c8e610dc00b28e130e9603bc2d1c1397f
  integration_test: 252f60fa39af5e17c3aa9899d35d908a0721b573
  ios_teamid: 0c24bbb68cdab3596f8ac6f7dda01f27678dc5f2
  launch_app_store: 9ccd81c07a1f6092e8282f0de34330a66c56b1c9
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  local_auth_darwin: 66e40372f1c29f383a314c738c7446e2f7fdadc3
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  maps_launcher: 2e5b6a2d664ec6c27f82ffa81b74228d770ab203
  memory_info: 18a37c81d0c57151f6661fd007b05f8894427a0a
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitBarcodeScanning: 10ca0845a6d15f2f6e911f682a1998b68b973e8b
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  NFCPassportReader: 98329180c6f7199ca52f15a81bd3344588e2b1bc
  onepay_custom_deeplink: 3e44219ae9980a6f1dd6743d975c3eac6b7f3e44
  onnx: ff81ed3e7860e32c3e6addda60834c3ea4d955cb
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  pdf_combiner: f67a578be856036889f3818914569d220994410d
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PhoneNumberKit: 441e8b26ec88d598e3591de9061eff18f5dd12e8
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  restart_app: 806659942bf932f6ce51c5372f91ce5e81c8c14a
  rive_common: cbbac3192af00d7341f19dae2f26298e9e37d99e
  safe_device: 4539eb6bdbeb4b61a763a51c4e73e6b37dea4e3d
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  secure_application: 27d424e8c2e770f63e38e280b5a51f921aa9b0c8
  sensors_plus: 7229095999f30740798f0eeef5cd120357a8f4f2
  share_plus: 8875f4f2500512ea181eef553c3e27dba5135aad
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sms_autofill: c461043483362c3f1709ee76eaae6eb570b31686
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  vibration: 3797858f8cbf53d841e189ef8bd533d96e4ca93c
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  vn_idcard_reader: 7b6ecf40be951dbd78379b38e90b791668d70df2
  vnpay: 2953b0c8169b64e3296c7cfb40dab3aff6cf32d4
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4
  workmanager: 223d80e78c765400c228f2104aa9d4ea68c4b05d
  zalopay: 8322ff43bcec5bcf335b8c3a37473eae3b7fd0a4

PODFILE CHECKSUM: b6a9555042281e8b05668e475576a71689a627bd

COCOAPODS: 1.16.2
