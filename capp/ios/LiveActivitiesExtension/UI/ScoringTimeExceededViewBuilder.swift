//
//  ScoringTimeExceededViewBuilder.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON> (VN) on 1/11/24.
//

import SwiftUI

@available(iOS 16.2, *)
struct ScoringTimeExceededViewBuilder {
    private init() {}
    static let titleKey = "capp_live_activities.scoring_time_exceeded_title"
    static let subtitleKey = "capp_live_activities.scoring_time_exceeded_subtitle"
    static let bannerImageKey = "capp_live_activities.scoring_time_exceeded_banner_image_url"
    static let topHeight: CGFloat = 96.0
    static let bottomHeight: CGFloat = 56.0
    static let placeholderBannerImage: ImageResource = .scoringInitialIcon
    
    static var customBannerImage: UIImage? {
        bannerImageKey.imageDataFromStorage
    }
    
    
    private struct BottomView: View {
        var body: some View {
            LockScreenProgressBottomView(subtitle: subtitleKey.localizedStringFromStorage) {
                GeometryReader { proxy in
                    ProgressView(value: 0.9)
                        .frame(width: proxy.size.width, height: 4)
                        .background(.progressBarBackgroundLightGreen)
                        .tint(.progressBarGreen)
                        .clipShape(RoundedRectangle(cornerRadius: 4))
                }
            }
        }
    }
    
    // MainView is the UI on lock screen
    struct MainView: View {
        var body: some View {
            VStack(spacing: 0) {
                LockScreenTopView(title: titleKey.localizedStringFromStorage, titleFontSize: 18, height: topHeight, bannerSize: 64) {
                    BannerImageView(customBannerImage: customBannerImage, placeholderImage: placeholderBannerImage)
                }
                .frame(height: topHeight)
                
                BottomView()
                    .padding(.bottom, 16)
                    .padding(.top, 8)
                    .padding(.horizontal, 16)
                    .frame(height: bottomHeight)
            }
            .background(.bottomComponent)
        }
    }
    
    struct CompactTrailingView: View {
        var body: some View {
            ProgressView(
                value: 0.9,
                label: {
                    EmptyView()
                }) {
                    EmptyView()
                }
                .progressViewStyle(.circular)
                .tint(.progressBarGreen)
                .frame(height: 24)
        }
    }
    
    struct ExpandLeadingView: View {
        var body: some View {
            Image(.appLogo)
                .resizable()
                .scaledToFit()
                .frame(height: 32)
                .padding(.top, 4)
                .padding(.leading, 4)
                .padding(.bottom, 8)
            GeometryReader { proxy in
                HStack(alignment: .bottom) {
                    Text(titleKey.localizedStringFromStorage)
                        .font(.system(size: 16, weight: .bold, design: .default))
                        .lineLimit(1)
                        .multilineTextAlignment(.leading)
                        .padding(.leading, 4)
                    Spacer()
                }
                .frame(width: proxy.size.width, height: 16)
                .overlay(alignment: .trailing) {
                    BannerImageView(customBannerImage: customBannerImage, placeholderImage: placeholderBannerImage, size: 56)
                        .padding(.bottom, 42)
                }
            }
            .frame(height: 18)
        }
    }
    
    struct ExpandBottomView: View {
        var body: some View {
            BottomView()
                .frame(height: 28)
                .padding(.horizontal, 4)
                .padding(.bottom, 8)
        }
    }
}
