//
//  ScoringWithNoOffersViewBuilder.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON> (VN) on 4/11/24.
//

import SwiftUI

@available(iOS 16.2, *)
struct ScoringWithNoOffersViewBuilder {
    private init() {}
    static let titleKey = "capp_live_activities.scoring_with_no_offers_title"
    static let subtitleKey = "capp_live_activities.scoring_with_no_offers_subtitle"
    static let ctaKey = "capp_live_activities.scoring_with_no_offers_cta"
    static let topHeight: CGFloat = 96.0
    static let bottomHeight: CGFloat = 64.0
    
    private struct BottomView: View {
        var body: some View {
            PrimaryButtonView(buttonText: ctaKey.localizedStringFromStorage, isFullWidth: true)
        }
    }
    
    // MainView is the UI on lock screen
    struct MainView: View {
        var body: some View {
            VStack(spacing: 0) {
                LockScreenTopView(title: titleKey.localizedStringFromStorage, titleFontSize: 18, height: topHeight) {
                    EmptyView()
                }
                .frame(height: topHeight)
                
                BottomView()
                    .padding(.horizontal, 16)
                    .padding(.top, 12)
                    .padding(.bottom, 18)
                    .frame(height: bottomHeight)
            }
            .background(.bottomComponent)
        }
    }
    
    struct CompactTrailingView: View {
        var body: some View {
            Image(.greenCheckmarkCircle)
                .resizable()
                .scaledToFit()
                .frame(height: 24)
        }
    }
    
    struct ExpandLeadingView: View {
        var body: some View {
            DIExpandLeadingView(
                title: titleKey.localizedStringFromStorage,
                customBannerImage: nil,
                placeholderImage: nil
            )
        }
    }
    
    struct ExpandBottomView: View {
        var body: some View {
            BottomView()
                .frame(height: 36)
                .padding(.horizontal, 4)
                .padding(.top, 8)
        }
    }
}

