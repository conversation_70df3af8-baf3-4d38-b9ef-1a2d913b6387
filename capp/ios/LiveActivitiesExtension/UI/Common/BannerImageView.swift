//
//  BannerImageView.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON> (VN) on 1/11/24.
//

import SwiftUI

@available(iOS 16.2, *)
struct BannerImageView: View {
    let customBannerImage: UIImage?
    let placeholderImage: ImageResource?
    let size: CGFloat
    
    init(
        customBannerImage: UIImage?,
        placeholderImage: ImageResource?,
        size: CGFloat = 64
    ) {
        self.customBannerImage = customBannerImage
        self.placeholderImage = placeholderImage
        self.size = size
    }
    
    var body: some View {
            if let customBannerImage = customBannerImage {
                Image(uiImage: customBannerImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: size, height: size)
            } else if let placeholderImage = placeholderImage {
                Image(placeholderImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: size, height: size)
            }
    }
}
