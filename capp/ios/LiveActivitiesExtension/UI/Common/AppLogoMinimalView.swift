//
//  AppLogoMinimalView.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON> (VN) on 1/11/24.
//

import SwiftUI

@available(iOS 16.2, *)
struct AppLogoMinimalView: View {
    var body: some View {
        Image(.appLogo)
            .resizable()
            .scaledToFit()
            .frame(height: 16)
    }
}

@available(iOS 16.2, *)
struct CompletedAppLogoMinimalView: View {
    var body: some View {
        ZStack {
            Image(.appLogo)
                .resizable()
                .scaledToFit()
                .frame(height: 12)
        }
        .frame(height: 26, alignment: .center)
        .overlay(alignment: .bottomTrailing) {
            Image(.greenCheckmark)
                .resizable()
                .scaledToFit()
                .frame(height: 12)
                .padding(.bottom, 2)
        }
    }
}
