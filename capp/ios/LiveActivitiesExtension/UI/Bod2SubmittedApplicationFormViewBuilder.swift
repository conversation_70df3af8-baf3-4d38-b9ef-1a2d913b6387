//
//  EnterBod1ViewBuilder.swift
//  LiveActivitiesExtension
//
//  Created by <PERSON><PERSON> (VN) on 18/11/24.
//

import SwiftUI

@available(iOS 16.2, *)
struct Bod2SubmittedApplicationFormViewBuilder {
    private init() {}
    // configurable properties
    static let titleKey = "capp_live_activities.bod2_submitted_application_form_title"
    static let subtitleKey = "capp_live_activities.bod2_submitted_application_form_subtitle"
    
    static let topHeight: CGFloat = 88.0
    static let bottomHeight: CGFloat = 72.0

    private struct BottomView: View {
        var body: some View {
            LockScreenProgressBottomView(subtitle: subtitleKey.localizedStringFromStorage, subtitleColor: .applicationFormSubtitle) {
                GeometryReader { proxy in
                    HStack(alignment: .center, spacing: 0) {
                        ThreeStepProgressBarView(
                            firstIcon: .applicationFormInitialActive,
                            secondIcon: .applicationFormWaitingActive,
                            thirdIcon: .applicationFormCompletedInactive,
                            firstStepProgress: 1.0,
                            secondStepProgress: 0.15
                        )
                        .frame(width: .infinity, height: 20)
                    }
                    .frame(height: proxy.size.height)
                }
            }
        }
    }
    
    // MainView is the UI on lock screen
    struct MainView: View {
        var body: some View {
            VStack(spacing: 0) {
                LockScreenTopView(
                    topImageBackground: .applicationFormTopBackground,
                    title: titleKey.localizedStringFromStorage,
                    titleFontSize: 18,
                    height: topHeight
                ) {
                    EmptyView()
                }
                .frame(height: topHeight)
                
                BottomView()
                    .padding(.bottom, 16)
                    .padding(.top, 12)
                    .padding(.horizontal, 16)
                    .frame(height: bottomHeight)
            }
            .background(.bottomComponent)
        }
    }
    
    struct CompactTrailingView: View {
        var body: some View {
            Image(.applicationFormWaitingActive)
                .resizable()
                .frame(width: 24, height: 24)
        }
    }
    
    struct ExpandLeadingView: View {
        var body: some View {
            Image(.appLogo)
                    .resizable()
                    .scaledToFit()
                    .frame(height: 32)
                    .padding(.top, 4)
                    .padding(.leading, 4)
                    .padding(.bottom, 8)
            GeometryReader { proxy in
                Text(titleKey.localizedStringFromStorage)
                    .font(.system(size: 18, weight: .bold, design: .default))
                    .lineLimit(1)
                    .multilineTextAlignment(.leading)
                    .padding(.leading, 4)
                    .minimumScaleFactor(0.9)
                    .frame(width: proxy.size.width, height: 18, alignment: .leading)
            }
            .frame(height: 18)
        }
    }
    
    struct ExpandBottomView: View {
        var body: some View {
            BottomView()
                .frame(height: 44)
                .padding(.horizontal, 6)
                .padding(.bottom, 16)
        }
    }
}
