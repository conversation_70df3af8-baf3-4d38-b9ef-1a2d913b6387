//
//  LoanState.swift
//  Runner
//
//  Created by <PERSON><PERSON> on 10/21/24.
//
#if canImport(ActivityKit)
import Foundation
import ActivityKit

enum LoanState: String, Codable {
    case initialScoring = "InitialScoring"
    case scoringTimeExceeded = "ScoringTimeExceeded"
    case scoringWithOneOffer = "ScoringWithOneOffer"
    case scoringWithMoreOffers = "ScoringWithMoreOffers"
    case scoringWithNoOffers = "ScoringWithNoOffers"
    case bod1EnterApplicationForm = "Bod1EnterApplicationForm"
    case bod2SubmittedApplicationForm = "Bod2SubmittedApplicationForm"
    case bod2TimeExceeded = "Bod2TimeExceeded"
    case bod2ApplicationApproved = "Bod2ApplicationApproved"
    case bod2AOs = "Bod2AOs"
    case bod2RejectedCancelled = "Bod2RejectedCancelled"
    case endLA = "EndLA"
    case endApplicationLA = "EndApplicationLA"
    case unknown = "Unknown"
}

@available(iOS 16.2, *)
extension LAAttributes.ContentState {
    init(state: LoanState) {
        self.status = state.rawValue
    }
    
    var loanState: LoanState {
        LoanState(rawValue: status) ?? .unknown
    }
}
#endif
