parameters:
  - name: prodFlavorName
    type: string
  - name: fakeFlavorName
    type: string
  - name: testAppPath
    type: string
    default: ""
  - name: newTestAppPath
    type: string
    default: ""
  - name: newTestAppPathPart2
    type: string
    default: ""
  - name: newTestAppPathPart3
    type: string
    default: ""
  - name: newTestAppPathPart4
    type: string
    default: ""
  - name: newTestAppPathPart5
    type: string
    default: ""
  - name: newTestAppPathPart6
    type: string
    default: ""
  - name: newTestAppPathPart7
    type: string
    default: ""
  - name: newTestAppPathPart8
    type: string
    default: ""
  - name: newTestAppPathPart9
    type: string
    default: ""
  - name: runOldIntegrationTests
    type: boolean
    default: true
  - name: runNewIntegrationTests
    type: boolean
    default: true

variables:
  - group: DevOpsAutomation
  - template: ../ci/variables.yml
  - template: ../ci/variables-prod.yml
  - name: revision
    value: $[counter(format('{0:dd}', pipeline.startTime), 1)]
  - name: targetBranchName
    ${{ if and(eq( variables['Build.Reason'], 'PullRequest' ), eq(variables['System.PullRequest.TargetBranch'],'refs/heads/master')) }}:
      value: "master"
    ${{ if and(eq( variables['Build.Reason'], 'PullRequest' ), eq(variables['System.PullRequest.TargetBranch'],'refs/heads/uat')) }}:
      value: "uat"
    ${{ else }}:
      value: "develop"
  - name: enableUpdateVersionInTask
    ${{ if and(eq( variables['Build.Reason'], 'PullRequest' ), eq(variables['System.PullRequest.TargetBranch'],'refs/heads/develop'), eq(variables['Build.SourceBranch'], 'refs/heads/uat')) }}:
      value: false
    ${{ else }}:
      value: true
stages:
  - stage: preprocessing
    jobs:
      - template: ../ci/preprocess-pubspec.yml
      - template: ../ci/preprocess-flutter.yml
  
  - stage: test
    dependsOn: preprocessing
    jobs:
      - ${{ if eq(parameters.runOldIntegrationTests, true) }}:
          - template: ../ci/integration-tests.yml
            parameters:
              name: flutter_integration_tests
              flavor: ${{ parameters.fakeFlavorName }}
              testAppPath: ${{ parameters.testAppPath }}
              testPath: "test_driver/all_tests_fake_test.dart"
              rootFolder: $(ROOT_FOLDER)
              rootPackage: $(ROOT_PACKAGE)
      - ${{ if eq(parameters.runNewIntegrationTests, true) }}:
          - template: ../ci/integration-tests-new.yml
            parameters:
              displayName: "New Integration tests fake part 1"
              flavor: ${{ parameters.fakeFlavorName }}
              testPath: "test_driver/integration_test.dart"
              testAppPath: ${{ parameters.newTestAppPath }}
              name: flutter_integration_tests_fake
              rootFolder: $(ROOT_FOLDER)
              rootPackage: $(ROOT_PACKAGE)
      - ${{ if and(eq(parameters.runNewIntegrationTests, true), ne(parameters.newTestAppPathPart2, '')) }}:
          - template: ../ci/integration-tests-new.yml
            parameters:
              displayName: "New Integration tests fake part 2"
              flavor: ${{ parameters.fakeFlavorName }}
              testPath: "test_driver/integration_test.dart"
              testAppPath: ${{ parameters.newTestAppPathPart2 }}
              name: flutter_integration_tests_fake_part_2
              rootFolder: $(ROOT_FOLDER)
              rootPackage: $(ROOT_PACKAGE)
      - ${{ if and(eq(parameters.runNewIntegrationTests, true), ne(parameters.newTestAppPathPart3, '')) }}:
          - template: ../ci/integration-tests-new.yml
            parameters:
              displayName: "New Integration tests fake part 3"
              flavor: ${{ parameters.fakeFlavorName }}
              testPath: "test_driver/integration_test.dart"
              testAppPath: ${{ parameters.newTestAppPathPart3 }}
              name: flutter_integration_tests_fake_part_3
              rootFolder: $(ROOT_FOLDER)
              rootPackage: $(ROOT_PACKAGE)
      - ${{ if and(eq(parameters.runNewIntegrationTests, true), ne(parameters.newTestAppPathPart4, '')) }}:
          - template: ../ci/integration-tests-new.yml
            parameters:
              displayName: "New Integration tests fake part 4"
              flavor: ${{ parameters.fakeFlavorName }}
              testPath: "test_driver/integration_test.dart"
              testAppPath: ${{ parameters.newTestAppPathPart4 }}
              name: flutter_integration_tests_fake_part_4
              rootFolder: $(ROOT_FOLDER)
              rootPackage: $(ROOT_PACKAGE)
      - ${{ if and(eq(parameters.runNewIntegrationTests, true), ne(parameters.newTestAppPathPart5, '')) }}:
          - template: ../ci/integration-tests-new.yml
            parameters:
              displayName: "New Integration tests fake part 5"
              flavor: ${{ parameters.fakeFlavorName }}
              testPath: "test_driver/integration_test.dart"
              testAppPath: ${{ parameters.newTestAppPathPart5 }}
              name: flutter_integration_tests_fake_part_5
              rootFolder: $(ROOT_FOLDER)
              rootPackage: $(ROOT_PACKAGE)

  - stage: GenerateVersion
    condition: and(succeeded(), or(eq(variables['Build.Reason'],'PullRequest'), in(variables['Build.SourceBranch'], 'refs/heads/develop', 'refs/heads/uat','refs/heads/master')))
    dependsOn: preprocessing
    variables:
      - template: ../ci/variables.yml
    jobs:
      - template: ../ci/version.yml