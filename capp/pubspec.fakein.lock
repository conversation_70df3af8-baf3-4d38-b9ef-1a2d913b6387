# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: b1595874fbc8f7a50da90f5d8f327bb0bfd6a95dc906c390efe991540c3b54aa
      url: "https://pub.dev"
    source: hosted
    version: "1.3.40"
  aadhaar_masking:
    dependency: transitive
    description:
      path: "../plugins/aadhaar_masking"
      relative: true
    source: path
    version: "0.0.1"
  aadhaar_masking_core:
    dependency: transitive
    description:
      path: "../plugins/aadhaar_masking/core"
      relative: true
    source: path
    version: "0.0.1"
  account_manager:
    dependency: transitive
    description:
      path: "../forks/account_manager"
      relative: true
    source: path
    version: "0.0.1"
  advertising_id:
    dependency: transitive
    description:
      name: advertising_id
      sha256: ab06ee85203ab500be85b7f45de2a75a629d8d9c453dba779276fbc4e97ad8d3
      url: "https://pub.dev"
    source: hosted
    version: "2.7.1"
  alice:
    dependency: transitive
    description:
      path: "../forks/alice"
      relative: true
    source: path
    version: "0.2.5"
  alphabet_list_scroll_view:
    dependency: transitive
    description:
      path: "../forks/alphabet_list_scroll_view"
      relative: true
    source: path
    version: "2.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  analyzer_plugin:
    dependency: transitive
    description:
      name: analyzer_plugin
      sha256: c1d5f167683de03d5ab6c3b53fc9aeefc5d59476e7810ba7bbddff50c6f4392d
      url: "https://pub.dev"
    source: hosted
    version: "0.11.2"
  android_id:
    dependency: transitive
    description:
      name: android_id
      sha256: e60b3c96bf256014d6c1d0a03e2712ef49d717817c13cbdb4c72b939199a4993
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+1"
  android_intent_plus:
    dependency: transitive
    description:
      name: android_intent_plus
      sha256: "38921ec22ebb3b9a7eb678792cf6fab0b6f458b61b9d327688573449c9b47db3"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  android_play_install_referrer:
    dependency: transitive
    description:
      name: android_play_install_referrer
      sha256: cba8387c1aaa35d2441159beebe5e9160901c44632d02d0579c6875756ecb798
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  animations:
    dependency: transitive
    description:
      name: animations
      sha256: fe8a6bdca435f718bb1dc8a11661b2c22504c6da40ef934cee8327ed77934164
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  ansicolor:
    dependency: transitive
    description:
      name: ansicolor
      sha256: "607f8fa9786f392043f169898923e6c59b4518242b68b8862eb8a8b7d9c30b4a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  app_links:
    dependency: transitive
    description:
      name: app_links
      sha256: ad1a6d598e7e39b46a34f746f9a8b011ee147e4c275d407fa457e7a62f84dd99
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  app_links_linux:
    dependency: transitive
    description:
      name: app_links_linux
      sha256: f5f7173a78609f3dfd4c2ff2c95bd559ab43c80a87dc6a095921d96c05688c81
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_links_platform_interface:
    dependency: transitive
    description:
      name: app_links_platform_interface
      sha256: "05f5379577c513b534a29ddea68176a4d4802c46180ee8e2e966257158772a3f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_links_web:
    dependency: transitive
    description:
      name: app_links_web
      sha256: af060ed76183f9e2b87510a9480e56a5352b6c249778d07bd2c95fc35632a555
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  app_settings:
    dependency: transitive
    description:
      name: app_settings
      sha256: "09bc7fe0313a507087bec1a3baf555f0576e816a760cbb31813a88890a09d9e5"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  app_tracking_transparency:
    dependency: transitive
    description:
      name: app_tracking_transparency
      sha256: "64d9745931e565790abdea91b518ac8dc3cebe6d0d0aaf7119343271b983259a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: ab96a1cb3beeccf8145c52e449233fe68364c9641623acd3adad66f8184f1039
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  azlistview:
    dependency: transitive
    description:
      path: "../forks/azlistview"
      relative: true
    source: path
    version: "2.0.0"
  biometric_signature:
    dependency: transitive
    description:
      name: biometric_signature
      sha256: e5d9afaefaac3386056039cef6a64f491ec375cc604b7ac06cdce8541e47cf38
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "106842ad6569f0b60297619e9e0b1885c2fb9bf84812935490e6c5275777804e"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  bloc_concurrency:
    dependency: transitive
    description:
      name: bloc_concurrency
      sha256: "456b7a3616a7c1ceb975c14441b3f198bf57d81cb95b7c6de5cb0c60201afcd8"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.5"
  bloc_test:
    dependency: "direct dev"
    description:
      name: bloc_test
      sha256: "165a6ec950d9252ebe36dc5335f2e6eb13055f33d56db0eeb7642768849b43d2"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.7"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  camera:
    dependency: transitive
    description:
      name: camera
      sha256: dfa8fc5a1adaeb95e7a54d86a5bd56f4bb0e035515354c8ac6d262e35cec2ec8
      url: "https://pub.dev"
    source: hosted
    version: "0.10.6"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      sha256: d8b17e36353cca3fd37e92f3fce28e7249730e078c72e9b43ee7c7ad2e2f7082
      url: "https://pub.dev"
    source: hosted
    version: "0.10.9+16"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: "2e4c568f70e406ccb87376bc06b53d2f5bebaab71e2fbcc1a950e31449381bcf"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.17+5"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: b3ede1f171532e0d83111fe0980b46d17f1aa9788a07a2fbed07366bbdbb9061
      url: "https://pub.dev"
    source: hosted
    version: "2.8.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  capp_aadhaar_masking:
    dependency: transitive
    description:
      path: "../packages/capp_aadhaar_masking/capp_aadhaar_masking"
      relative: true
    source: path
    version: "0.0.1"
  capp_aadhaar_masking_core:
    dependency: transitive
    description:
      path: "../packages/capp_aadhaar_masking/capp_aadhaar_masking_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_address_validation:
    dependency: transitive
    description:
      path: "../packages/capp_address_validation"
      relative: true
    source: path
    version: "0.0.1"
  capp_api:
    dependency: transitive
    description:
      path: "../packages/capp_api"
      relative: true
    source: path
    version: "0.0.1"
  capp_app:
    dependency: "direct main"
    description:
      path: "../packages/capp_app/capp_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_appsflyer_core:
    dependency: transitive
    description:
      path: "../packages/capp_appsflyer/capp_appsflyer_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_auth:
    dependency: transitive
    description:
      path: "../packages/capp_auth/capp_auth_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_auth_core:
    dependency: transitive
    description:
      path: "../packages/capp_auth/capp_auth_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_bank_validation:
    dependency: transitive
    description:
      path: "../packages/capp_bank_validation"
      relative: true
    source: path
    version: "0.0.1"
  capp_cards_core:
    dependency: transitive
    description:
      path: "../packages/capp_cards/capp_cards_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_config_core:
    dependency: transitive
    description:
      path: "../packages/capp_config/capp_config_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_config_in:
    dependency: transitive
    description:
      path: "../packages/capp_config/capp_config_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_content:
    dependency: transitive
    description:
      path: "../packages/capp_content/capp_content"
      relative: true
    source: path
    version: "0.0.1"
  capp_content_core:
    dependency: transitive
    description:
      path: "../packages/capp_content/capp_content_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_credolab_core:
    dependency: transitive
    description:
      path: "../packages/capp_credolab/capp_credolab_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_datascore_core:
    dependency: transitive
    description:
      path: "../packages/capp_datascore/capp_datascore_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_deals_core:
    dependency: transitive
    description:
      path: "../packages/capp_deals/capp_deals_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_demo:
    dependency: transitive
    description:
      path: "../packages/capp_demo/capp_demo_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_demo_core:
    dependency: transitive
    description:
      path: "../packages/capp_demo/capp_demo_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_digio:
    dependency: transitive
    description:
      path: "../packages/capp_digio/capp_digio"
      relative: true
    source: path
    version: "0.0.1"
  capp_digio_core:
    dependency: transitive
    description:
      path: "../packages/capp_digio/capp_digio_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_direct_debit:
    dependency: transitive
    description:
      path: "../packages/capp_direct_debit/capp_direct_debit"
      relative: true
    source: path
    version: "0.0.1"
  capp_direct_debit_core:
    dependency: transitive
    description:
      path: "../packages/capp_direct_debit/capp_direct_debit_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_domain:
    dependency: transitive
    description:
      path: "../packages/capp_domain"
      relative: true
    source: path
    version: "0.0.1"
  capp_emi_calculator:
    dependency: transitive
    description:
      path: "../packages/capp_emi_calculator"
      relative: true
    source: path
    version: "0.0.1"
  capp_evoucher_core:
    dependency: transitive
    description:
      path: "../packages/capp_evoucher/capp_evoucher_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_face_guard:
    dependency: transitive
    description:
      path: "../packages/capp_face_guard"
      relative: true
    source: path
    version: "0.0.1"
  capp_feature_flags:
    dependency: transitive
    description:
      path: "../packages/capp_feature_flags"
      relative: true
    source: path
    version: "0.0.1"
  capp_finbox:
    dependency: transitive
    description:
      path: "../packages/capp_finbox/capp_finbox"
      relative: true
    source: path
    version: "0.0.1"
  capp_finbox_core:
    dependency: transitive
    description:
      path: "../packages/capp_finbox/capp_finbox_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_golden:
    dependency: transitive
    description:
      path: "../packages/capp_golden/capp_golden_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_golden_core:
    dependency: transitive
    description:
      path: "../packages/capp_golden/capp_golden_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_home:
    dependency: transitive
    description:
      path: "../packages/capp_home/capp_home_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_home_core:
    dependency: transitive
    description:
      path: "../packages/capp_home/capp_home_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_hyperverge:
    dependency: transitive
    description:
      path: "../packages/capp_hyperverge/capp_hyperverge"
      relative: true
    source: path
    version: "0.0.1"
  capp_hyperverge_core:
    dependency: transitive
    description:
      path: "../packages/capp_hyperverge/capp_hyperverge_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_innovatrics_core:
    dependency: transitive
    description:
      path: "../packages/capp_innovatrics/capp_innovatrics_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_legal_logs:
    dependency: transitive
    description:
      path: "../packages/capp_legal_logs/capp_legal_logs_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_legal_logs_in:
    dependency: transitive
    description:
      path: "../packages/capp_legal_logs/capp_legal_logs_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_legal_logs_mock:
    dependency: transitive
    description:
      path: "../packages/capp_legal_logs/capp_legal_logs_mock"
      relative: true
    source: path
    version: "0.0.1"
  capp_legal_permission_core:
    dependency: transitive
    description:
      path: "../packages/capp_legal_permission/capp_legal_permission_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_origination:
    dependency: transitive
    description:
      path: "../packages/capp_loan_origination/capp_loan_origination_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_origination_unified:
    dependency: transitive
    description:
      path: "../packages/capp_loan_origination_unified/capp_loan_origination_unified_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_origination_unified_core:
    dependency: transitive
    description:
      path: "../packages/capp_loan_origination_unified/capp_loan_origination_unified_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_shared:
    dependency: transitive
    description:
      path: "../packages/capp_loan_shared/capp_loan_shared_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_shared_core:
    dependency: transitive
    description:
      path: "../packages/capp_loan_shared/capp_loan_shared_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_signature:
    dependency: transitive
    description:
      path: "../packages/capp_loan_signature/capp_loan_signature_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_signature_core:
    dependency: transitive
    description:
      path: "../packages/capp_loan_signature/capp_loan_signature_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_onboarding:
    dependency: transitive
    description:
      path: "../packages/capp_onboarding/capp_onboarding_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_onboarding_core:
    dependency: transitive
    description:
      path: "../packages/capp_onboarding/capp_onboarding_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_payment:
    dependency: transitive
    description:
      path: "../packages/capp_payment/capp_payment_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_payment_core:
    dependency: transitive
    description:
      path: "../packages/capp_payment/capp_payment_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_personal:
    dependency: transitive
    description:
      path: "../packages/capp_personal/capp_personal_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_personal_core:
    dependency: transitive
    description:
      path: "../packages/capp_personal/capp_personal_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_plugins:
    dependency: transitive
    description:
      path: "../packages/capp_plugins/capp_plugins_in"
      relative: true
    source: path
    version: "1.0.0+1"
  capp_plugins_core:
    dependency: transitive
    description:
      path: "../packages/capp_plugins/capp_plugins_core"
      relative: true
    source: path
    version: "1.0.0+1"
  capp_products_core:
    dependency: transitive
    description:
      path: "../packages/capp_products/capp_products_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_repayment:
    dependency: transitive
    description:
      path: "../packages/capp_repayment/capp_repayment_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_repayment_core:
    dependency: transitive
    description:
      path: "../packages/capp_repayment/capp_repayment_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_self_service:
    dependency: transitive
    description:
      path: "../packages/capp_self_service/capp_self_service_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_self_service_core:
    dependency: transitive
    description:
      path: "../packages/capp_self_service/capp_self_service_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_tracking:
    dependency: transitive
    description:
      path: "../packages/capp_tracking"
      relative: true
    source: path
    version: "0.0.1"
  capp_transaction_history:
    dependency: transitive
    description:
      path: "../packages/capp_transaction_history/capp_transaction_history_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_transaction_history_core:
    dependency: transitive
    description:
      path: "../packages/capp_transaction_history/capp_transaction_history_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_transaction_signature:
    dependency: transitive
    description:
      path: "../packages/capp_transaction_signature/capp_transaction_signature_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_transaction_signature_core:
    dependency: transitive
    description:
      path: "../packages/capp_transaction_signature/capp_transaction_signature_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_transactions:
    dependency: transitive
    description:
      path: "../packages/capp_transactions/capp_transactions_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_transactions_core:
    dependency: transitive
    description:
      path: "../packages/capp_transactions/capp_transactions_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_ui:
    dependency: transitive
    description:
      path: "../packages/capp_ui/capp_ui_in"
      relative: true
    source: path
    version: "0.0.1"
  capp_ui_core:
    dependency: transitive
    description:
      path: "../packages/capp_ui/capp_ui_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_vas_core:
    dependency: transitive
    description:
      path: "../packages/capp_vas/capp_vas_core"
      relative: true
    source: path
    version: "0.0.1"
  carousel_slider:
    dependency: transitive
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clipboard:
    dependency: transitive
    description:
      name: clipboard
      sha256: "2ec38f0e59878008ceca0ab122e4bfde98847f88ef0f83331362ba4521f565a9"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: transitive
    description:
      name: cloud_firestore
      sha256: a31eec60eadaa859f0677bf661d9f86ed15961c716512f64884e59edcb341472
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: "3224e6158441c8897325e74f9971140cde2c85ee75a26704407a91b969b50829"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: c1312945cb7dd55921bcc10445f6c9a494bc04104b7d0821c3ed577b970ab088
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  confetti:
    dependency: transitive
    description:
      name: confetti
      sha256: "979aafde2428c53947892c95eb244466c109c129b7eee9011f0a66caaca52267"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "876849631b0c7dc20f8b471a2a03142841b482438e3b707955464f5ffca3e4c3"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: "42657c1715d48b167930d5f34d00222ac100475f73d10162ddf43e714932f204"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "1be13198012c1d5bc042dc40ad1d7f16cbd522350984c0c1abf471d6d7e305c6"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  converter:
    dependency: transitive
    description:
      name: converter
      sha256: "92bd8c7bf5ab3e083f7aad80ee5d587d265f70707807310384bd6c4a7bc57ada"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.6"
  cool_devtool:
    dependency: transitive
    description:
      name: cool_devtool
      sha256: "84911cde32e522c070e99cc934b9725113959ff7984ff2b6736a64b5c8d26e24"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: "4b03e11f6d5b8f6e5bb5e9f7889a56fe6c5cbe942da5378ea4d4d7f73ef9dfe5"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  credolab_core:
    dependency: transitive
    description:
      path: "../plugins/credolab/credolab_core"
      relative: true
    source: path
    version: "0.0.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "0b0036e8cccbfbe0555fd83c1d31a6f30b77a96b598b35a5d36dd41f718695e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+4"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: b36c7f7e24c0bdf1bf9a3da461c837d1de64b9f8beb190c9011d8c72a3dfd745
      url: "https://pub.dev"
    source: hosted
    version: "0.17.2"
  currency_text_input_formatter:
    dependency: transitive
    description:
      name: currency_text_input_formatter
      sha256: e2dda73e5ddbe2b70b898ece09aeb661a10808e084f776bebf81e07e8d57b29c
      url: "https://pub.dev"
    source: hosted
    version: "2.2.6"
  dart_code_metrics:
    dependency: transitive
    description:
      path: "."
      ref: ebfa4c53bc62ff88eaca414043ffe571027ff7ac
      resolved-ref: ebfa4c53bc62ff88eaca414043ffe571027ff7ac
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-code-metrics"
    source: git
    version: "5.7.6"
  dart_code_metrics_presets:
    dependency: transitive
    description:
      name: dart_code_metrics_presets
      sha256: b71eadf02a3787ebd5c887623f83f6fdc204d45c75a081bd636c4104b3fd8b73
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  dart_ipify:
    dependency: transitive
    description:
      name: dart_ipify
      sha256: "3b70d589504126107e81ad0703d91394cc8e2039cb0a11ebd92b9b824a5e9561"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  dartz:
    dependency: transitive
    description:
      name: dartz
      sha256: e6acf34ad2e31b1eb00948692468c30ab48ac8250e0f0df661e29f12dd252168
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  datascore_core:
    dependency: transitive
    description:
      path: "../plugins/datascore/datascore_core"
      relative: true
    source: path
    version: "0.0.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "6f07cba3f7b3448d42d015bfd3d53fe12e5b36da2423f23838efc1d5fb31a263"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.8"
  decimal:
    dependency: transitive
    description:
      name: decimal
      sha256: "24a261d5d5c87e86c7651c417a5dbdf8bcd7080dd592533910e8d0505a279f21"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  device_calendar:
    dependency: transitive
    description:
      name: device_calendar
      sha256: "683fb93ec302b6a65c0ce57df40ff9dcc2404f59c67a2f8b93e59318c8a0a225"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.3"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "282d3cf731045a2feb66abfe61bbc40870ae50a3ed10a4d3d217556c35c8c2ba"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  diacritic:
    dependency: transitive
    description:
      name: diacritic
      sha256: "12981945ec38931748836cd76f2b38773118d0baef3c68404bdfde9566147876"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  diff_match_patch:
    dependency: transitive
    description:
      name: diff_match_patch
      sha256: "2efc9e6e8f449d0abe15be240e2c2a3bcd977c8d126cfd70598aee60af35c0a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  digio:
    dependency: transitive
    description:
      path: "../plugins/digio"
      relative: true
    source: path
    version: "0.0.1"
  digio_core:
    dependency: transitive
    description:
      path: "../plugins/digio/core"
      relative: true
    source: path
    version: "0.0.1"
  digio_esign:
    dependency: transitive
    description:
      path: "../plugins/digio_esign"
      relative: true
    source: path
    version: "0.0.1"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "7d328c4d898a61efc3cd93655a0955858e29a0aa647f0f9e02d59b3bb275e2e8"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  dio_cache_interceptor:
    dependency: transitive
    description:
      path: "../plugins/dio_cache_interceptor"
      relative: true
    source: path
    version: "0.6.0"
  dots_indicator:
    dependency: transitive
    description:
      name: dots_indicator
      sha256: e59dfc90030ee5a4fd4c53144a8ce97cc7a823c2067b8fb9814960cd1ae63f89
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dotted_decoration:
    dependency: transitive
    description:
      name: dotted_decoration
      sha256: a5c5771367690b4f64ebfa7911954ab472b9675f025c373f514e32ac4bb81d5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  dynamic_forms:
    dependency: transitive
    description:
      path: "../plugins/flutter_dynamic_forms/packages/dynamic_forms"
      relative: true
    source: path
    version: "1.0.0"
  either_dart:
    dependency: transitive
    description:
      name: either_dart
      sha256: "156205366f51202ca8ab56911cdf5d38567a986ddcef6cf8646a7b270e042085"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "4fd4e4fdc21b9d7d4141823e1e6515cd94e7b8d84749504c232999fba25d9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  expandable:
    dependency: transitive
    description:
      name: expandable
      sha256: "9604d612d4d1146dafa96c6d8eec9c2ff0994658d6d09fed720ab788c7f5afc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  expression_language:
    dependency: transitive
    description:
      path: "../plugins/flutter_dynamic_forms/packages/expression_language"
      relative: true
    source: path
    version: "1.0.1"
  extended_image:
    dependency: transitive
    description:
      name: extended_image
      sha256: "69d4299043334ecece679996e47d0b0891cd8c29d8da0034868443506f1d9a78"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.1"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: "9a94ec9314aa206cfa35f16145c3cd6e2c924badcc670eaaca8a3a8063a68cd7"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.5"
  extension:
    dependency: transitive
    description:
      name: extension
      sha256: be3a6b7f8adad2f6e2e8c63c895d19811fcf203e23466c6296267941d0ff4f24
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  facebook_app_events:
    dependency: transitive
    description:
      name: facebook_app_events
      sha256: d9456a841863e80c4be1ee71067cd635e8717573a8f552b10b4b6e80bfff8c51
      url: "https://pub.dev"
    source: hosted
    version: "0.19.4"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  faker:
    dependency: transitive
    description:
      name: faker
      sha256: c6f95ed978d91379bed8bfde3da372d969fd6a77cd3c6c458768bd59a7fea408
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "5fc22d7c25582e38ad9a8515372cd9a93834027aacf1801cf01164dac0ffa08c"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  file_saver:
    dependency: transitive
    description:
      path: "../forks/file_saver"
      relative: true
    source: path
    version: "0.2.9"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: b2b91daf8a68ecfa4a01b778a6f52edef9b14ecd506e771488ea0f2e0784198b
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "8f5d2f6590d51ecd9179ba39c64f722edc15226cc93dcc8698466ad36a4a85a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+3"
  finbox_dc_plugin:
    dependency: transitive
    description:
      path: "../plugins/finbox_dc_plugin"
      relative: true
    source: path
    version: "1.0.6"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: "064e5b57b0693305946b7caa6a80ed80a918f46804c247b6cd7ed9cd327df48f"
      url: "https://pub.dev"
    source: hosted
    version: "11.2.1"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: d094547c9022c404b5ca39b7209607fc80e75e39d38875f050508fa4346b3e74
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "06dc023b0144c0df630a56b6262cc9e7d6069fe78148853d97614dbefb6ea923"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.9+1"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "3187f4f8e49968573fd7403011dca67ba95aae419bc0d8131500fae160d94f92"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: e30da58198a6d4b49d5bce4e852f985c32cb10db329ebef9473db2b9f09ce810
      url: "https://pub.dev"
    source: hosted
    version: "5.3.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: f967a7138f5d2ffb1ce15950e2a382924239eaa521150a8f144af34e68b3b3e5
      url: "https://pub.dev"
    source: hosted
    version: "2.18.1"
  firebase_crashlytics:
    dependency: transitive
    description:
      name: firebase_crashlytics
      sha256: "30260e1b8ad1464b41ca4531b44ce63d752daaf2f12c92ca6cdcd82b270abecc"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: a75e1826d92ea4e86e4a753c7b5d64b844a362676fa653185f1581c859186d18
      url: "https://pub.dev"
    source: hosted
    version: "3.6.40"
  firebase_dynamic_links:
    dependency: transitive
    description:
      name: firebase_dynamic_links
      sha256: f094b1f90951981328abdb39c4140c0b0590c8d56fe23a9ad987b654a33000d0
      url: "https://pub.dev"
    source: hosted
    version: "6.0.4"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: a9af616ec8e33c739b12153c420d16d6987532e13c8d764a0f20a64031bb93f1
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6+40"
  firebase_messaging:
    dependency: transitive
    description:
      name: firebase_messaging
      sha256: "1b0a4f9ecbaf9007771bac152afad738ddfacc4b8431a7591c00829480d99553"
      url: "https://pub.dev"
    source: hosted
    version: "15.0.4"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: c5a6443e66ae064fe186901d740ee7ce648ca2a6fd0484b8c5e963849ac0fc28
      url: "https://pub.dev"
    source: hosted
    version: "4.5.42"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "232ef63b986467ae5b5577a09c2502b26e2e2aebab5b85e6c966a5ca9b038b89"
      url: "https://pub.dev"
    source: hosted
    version: "3.8.12"
  firebase_performance:
    dependency: transitive
    description:
      name: firebase_performance
      sha256: "6d17133458b9627f15f278d6f71bebbbce885d393f3462b690e55deeb5c36b90"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0+4"
  firebase_performance_platform_interface:
    dependency: transitive
    description:
      name: firebase_performance_platform_interface
      sha256: "28dc0a70a3459fe51d1c1be5754803a9a0db0e210322ec7526f6ce42bf6ad83e"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+40"
  firebase_performance_web:
    dependency: transitive
    description:
      name: firebase_performance_web
      sha256: db91d86b34280f5253d2913945fdd51d7114486584a298a7bedf1c4b2ab08f79
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6+12"
  firebase_remote_config:
    dependency: transitive
    description:
      name: firebase_remote_config
      sha256: "62e86ed64370c382a2f872fbcabcae591c404776eb84685eb535bab53c0c00d5"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.4"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "80973fa763b7c9a0fc0596afed7063f2378de2cf2d37b017254e613160b43135"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.40"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "14ba362bdcf7abda12fa9060f2ebae7d342153e4d619007071e98cd557ce29a3"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.12"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flagsmith:
    dependency: transitive
    description:
      path: "../forks/flagsmith"
      relative: true
    source: path
    version: "2.0.0-fork.4"
  flare_flutter:
    dependency: transitive
    description:
      name: flare_flutter
      sha256: "99d63c60f00fac81249ce6410ee015d7b125c63d8278a30da81edf3317a1f6a0"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flip_card:
    dependency: transitive
    description:
      name: flip_card
      sha256: "5d4aa58f3983cced0782f4ce45826b7eea36e8e464964d9209dcbc7a87b2292f"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_app_badger:
    dependency: transitive
    description:
      name: flutter_app_badger
      sha256: "64d4a279bab862ed28850431b9b446b9820aaae0bf363322d51077419f930fa8"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  flutter_arc_text:
    dependency: transitive
    description:
      name: flutter_arc_text
      sha256: "38e9cda592ea61af4c7f2446641416580240da4be8a5427a99bf70045533ad16"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  flutter_bloc:
    dependency: transitive
    description:
      name: flutter_bloc
      sha256: b594505eac31a0518bdcb4b5b79573b8d9117b193cc80cc12e17d639b10aa27a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.6"
  flutter_custom_tabs:
    dependency: transitive
    description:
      name: flutter_custom_tabs
      sha256: "34167bd15fa3479855c011f868e0789c9569c12b64358ca7250accc5a24c3312"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_android:
    dependency: transitive
    description:
      name: flutter_custom_tabs_android
      sha256: cf06fde8c002f326dc6cbf69ee3f97c3feead4436229da02d2e2aa39d5a5dbf4
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_ios:
    dependency: transitive
    description:
      name: flutter_custom_tabs_ios
      sha256: ef2de533bc45fb84fefc3854bc8b1e43001671c6bc6bc55faa57942eecd3f70a
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_platform_interface:
    dependency: transitive
    description:
      name: flutter_custom_tabs_platform_interface
      sha256: e18e9b08f92582123bdb84fb6e4c91804b0579700fed6f887d32fd9a1e96a5d5
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_web:
    dependency: transitive
    description:
      name: flutter_custom_tabs_web
      sha256: "08ae322b11e1972a233d057542279873d0f9d1d5f8159c2c741457239d9d562c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_document_picker:
    dependency: transitive
    description:
      name: flutter_document_picker
      sha256: "05fd2cdbcfd679fece8bfd99103e1effd5d3756ef742f263b9bd682288888fce"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  flutter_driver:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_dynamic_forms:
    dependency: transitive
    description:
      path: "../plugins/flutter_dynamic_forms/packages/flutter_dynamic_forms"
      relative: true
    source: path
    version: "1.0.0"
  flutter_dynamic_forms_components:
    dependency: transitive
    description:
      path: "../plugins/flutter_dynamic_forms/packages/flutter_dynamic_forms_components"
      relative: true
    source: path
    version: "1.0.0"
  flutter_facebook_app_links:
    dependency: transitive
    description:
      name: flutter_facebook_app_links
      sha256: "7b8fe835c76188bb326ae88dbba9fde797b918d659b3321579d47b569fcf6d53"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flutter_image_compress:
    dependency: transitive
    description:
      name: flutter_image_compress
      sha256: "45a3071868092a61b11044c70422b04d39d4d9f2ef536f3c5b11fb65a1e7dd90"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  flutter_image_compress_common:
    dependency: transitive
    description:
      name: flutter_image_compress_common
      sha256: "7f79bc6c8a363063620b4e372fa86bc691e1cb28e58048cd38e030692fbd99ee"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_macos:
    dependency: transitive
    description:
      name: flutter_image_compress_macos
      sha256: "26df6385512e92b3789dc76b613b54b55c457a7f1532e59078b04bf189782d47"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  flutter_image_compress_ohos:
    dependency: transitive
    description:
      name: flutter_image_compress_ohos
      sha256: e76b92bbc830ee08f5b05962fc78a532011fcd2041f620b5400a593e96da3f51
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  flutter_image_compress_platform_interface:
    dependency: transitive
    description:
      name: flutter_image_compress_platform_interface
      sha256: "579cb3947fd4309103afe6442a01ca01e1e6f93dc53bb4cbd090e8ce34a41889"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_web:
    dependency: transitive
    description:
      name: flutter_image_compress_web
      sha256: f02fe352b17f82b72f481de45add240db062a2585850bea1667e82cc4cd6c311
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+1"
  flutter_inappwebview:
    dependency: "direct dev"
    description:
      path: "../forks/flutter_inappwebview/flutter_inappwebview"
      relative: true
    source: path
    version: "6.0.0"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      path: "../forks/flutter_inappwebview/flutter_inappwebview_android"
      relative: true
    source: path
    version: "1.0.13"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "5f80fd30e208ddded7dbbcd0d569e7995f9f63d45ea3f548d8dd4c0b473fb4c8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      path: "../forks/flutter_inappwebview/flutter_inappwebview_ios"
      relative: true
    source: path
    version: "1.0.13"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      path: "../forks/flutter_inappwebview/flutter_inappwebview_macos"
      relative: true
    source: path
    version: "1.0.11"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      path: "../forks/flutter_inappwebview/flutter_inappwebview_platform_interface"
      relative: true
    source: path
    version: "1.0.11"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      path: "../forks/flutter_inappwebview/flutter_inappwebview_web"
      relative: true
    source: path
    version: "1.0.8"
  flutter_jailbreak_detection:
    dependency: transitive
    description:
      name: flutter_jailbreak_detection
      sha256: "67ff11ea41965152d24db7104da1f9b343f94ada64c2a9e309ec4d753a12d281"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: "559c600f056e7c704bd843723c21e01b5fba47e8824bd02422165bcc02a5de1d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3"
  flutter_libphonenumber:
    dependency: transitive
    description:
      path: "../forks/flutter_libphonenumber"
      relative: true
    source: path
    version: "1.1.0"
  flutter_local_notifications:
    dependency: transitive
    description:
      path: "../forks/flutter_local_notifications"
      relative: true
    source: path
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_mailer:
    dependency: transitive
    description:
      name: flutter_mailer
      sha256: "4fffaa35e911ff5ec2e5a4ebbca62c372e99a154eb3bb2c0bf79f09adf6ecf4c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  flutter_markdown:
    dependency: transitive
    description:
      name: flutter_markdown
      sha256: "7b25c10de1fea883f3c4f9b8389506b54053cd00807beab69fd65c8653a2711f"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.14"
  flutter_native_contact_picker:
    dependency: transitive
    description:
      path: "../forks/flutter_native_contact_picker"
      relative: true
    source: path
    version: "0.0.4"
  flutter_native_timezone_updated_gradle:
    dependency: transitive
    description:
      name: flutter_native_timezone_updated_gradle
      sha256: ed5f04e348b86be0564db848ad29cd7ad0d53c7949ce1ff56b3dd35fd09df0e5
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  flutter_pin_encryption:
    dependency: transitive
    description:
      path: "../plugins/flutter_pin_encryption"
      relative: true
    source: path
    version: "0.0.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "60fc7b78455b94e6de2333d2f95196d32cf5c22f4b0b0520a628804cb463503b"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  flutter_scroll_shadow:
    dependency: transitive
    description:
      name: flutter_scroll_shadow
      sha256: c0509c642c5077654301fab1fb2260adc94c82a407c60e64162974b4366e7874
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: ffdbb60130e4665d2af814a0267c481bcf522c41ae2e43caf69fa0146876d685
      url: "https://pub.dev"
    source: hosted
    version: "9.0.0"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: "4d91bfc23047422cbcd73ac684bc169859ee766482517c22172c86596bf1464b"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: bd33935b4b628abd0b86c8ca20655c5b36275c3a3f5194769a7b3f37c905369c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: b3773190e385a3c8a382007893d678ae95462b3c2279e987b55d140d3b0cb81b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: "42938e70d4b872e856e678c423cc0e9065d7d294f45bc41fc1981a4eb4beaffe"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: "583a738a15e940c63735ef929f78cde5bf2c2c2e66c54b7c7c1dc9c418923d4a"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  flutter_sticky_header:
    dependency: transitive
    description:
      name: flutter_sticky_header
      sha256: "017f398fbb45a589e01491861ca20eb6570a763fd9f3888165a978e11248c709"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: "578bd8c508144fdaffd4f77b8ef2d8c523602275cd697cc3db284dbd762ef4ce"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.14"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_windowmanager:
    dependency: transitive
    description:
      name: flutter_windowmanager
      sha256: b4d0bc06f6777952b729c0cdb7ce9ad1ecabd8b8b1cb0acb57a36621457dab1b
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  fluttertoast:
    dependency: transitive
    description:
      name: fluttertoast
      sha256: "81b68579e23fcbcada2db3d50302813d2371664afe6165bc78148050ab94bf66"
      url: "https://pub.dev"
    source: hosted
    version: "8.2.5"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: aeac15850ef1b38ee368d4c53ba9a847e900bb2c53a4db3f6881cbb3cb684338
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  fuchsia_remote_debug_protocol:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  ga360_plugin:
    dependency: transitive
    description:
      path: "../plugins/ga360_plugin"
      relative: true
    source: path
    version: "0.0.1"
  geolocator:
    dependency: transitive
    description:
      name: geolocator
      sha256: f4efb8d3c4cdcad2e226af9661eb1a0dd38c71a9494b22526f9da80ab79520e5
      url: "https://pub.dev"
    source: hosted
    version: "10.1.1"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: f15d1536cd01b1399578f1da1eb5d566e7a718db6a3648f2c24d2e2f859f0692
      url: "https://pub.dev"
    source: hosted
    version: "4.5.4"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: bc2aca02423ad429cb0556121f56e60360a2b7d694c8570301d06ea0c00732fd
      url: "https://pub.dev"
    source: hosted
    version: "2.3.7"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "386ce3d9cce47838355000070b1d0b13efb5bc430f8ecda7e9238c8409ace012"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "102e7da05b48ca6bf0a5bda0010f886b171d1a08059f01bfe02addd0175ebece"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "53da08937d07c24b0d9952eb57a3b474e29aae2abf9dd717f7e1230995f13f0e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  get_it:
    dependency: transitive
    description:
      name: get_it
      sha256: d85128a5dae4ea777324730dc65edd9c9f43155c109d5cc0a69cab74139fbac1
      url: "https://pub.dev"
    source: hosted
    version: "7.7.0"
  getwidget:
    dependency: transitive
    description:
      name: getwidget
      sha256: "91df14a8d80e21f3ec02759295b90cc8badb8a872b90d34ad4aeb4085d833b5c"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  gma_lints:
    dependency: "direct dev"
    description:
      path: "../packages/gma_lints"
      relative: true
    source: path
    version: "1.0.1"
  gma_map_base:
    dependency: transitive
    description:
      path: "../packages/gma_map/gma_map_base"
      relative: true
    source: path
    version: "0.0.1"
  gma_map_core:
    dependency: transitive
    description:
      path: "../packages/gma_map/gma_map_core"
      relative: true
    source: path
    version: "0.0.1"
  gma_pdf:
    dependency: transitive
    description:
      path: "../packages/gma_pdf/gma_pdf_small"
      relative: true
    source: path
    version: "0.0.1"
  gma_pdf_core:
    dependency: transitive
    description:
      path: "../packages/gma_pdf/gma_pdf_core"
      relative: true
    source: path
    version: "0.0.1"
  gma_platform:
    dependency: transitive
    description:
      path: "../packages/gma_platform"
      relative: true
    source: path
    version: "0.0.1"
  gma_storage:
    dependency: transitive
    description:
      path: "../packages/gma_storage"
      relative: true
    source: path
    version: "1.0.0"
  gma_vault:
    dependency: transitive
    description:
      path: "../plugins/gma_vault"
      relative: true
    source: path
    version: "0.0.1"
  google_api_availability:
    dependency: transitive
    description:
      name: google_api_availability
      sha256: "3e9548cfd991d983d11425a2436d5bd957d048c279cc9e145ffe3f36fd847385"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  google_api_availability_android:
    dependency: transitive
    description:
      name: google_api_availability_android
      sha256: d95429ae78083585c312de2c6578085e7d53d100a94656d691bce0bb0ce435be
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  google_api_availability_platform_interface:
    dependency: transitive
    description:
      name: google_api_availability_platform_interface
      sha256: "65b7da62fe5b582bb3d508628ad827d36d890710ea274766a992a56fa5420da6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  google_fonts:
    dependency: transitive
    description:
      name: google_fonts
      sha256: b1ac0fe2832c9cc95e5e88b57d627c5e68c223b9657f4b96e1487aa9098c7b82
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "4d6e199c561ca06792c964fa24b2bac7197bf4b401c2e1d23e345e5f9939f531"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.1"
  google_maps_flutter:
    dependency: transitive
    description:
      name: google_maps_flutter
      sha256: "2e302fa3aaf4e2a297f0342d83ebc5e8e9f826e9a716aef473fe7f404ec630a7"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: bccf64ccbb2ea672dc62a61177b315a340af86b0228564484b023657544a3fd5
      url: "https://pub.dev"
    source: hosted
    version: "2.14.11"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: "753ebf6a2bc24c5eba8e714c901345d858abd9694b1f878c43614fd3f06b8060"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.1"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: a951981c22d790848efb9f114f81794945bc5c06bc566238a419a92f110af6cb
      url: "https://pub.dev"
    source: hosted
    version: "2.9.5"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: ff39211bd25d7fad125d19f757eba85bd154460907cd4d135e07e3d0f98a4130
      url: "https://pub.dev"
    source: hosted
    version: "0.5.10"
  google_mlkit_barcode_scanning:
    dependency: transitive
    description:
      name: google_mlkit_barcode_scanning
      sha256: "1df232be24ae9cb93443d2786681efab71b5964c8c476f6072d8b4d61aa03ecc"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.1"
  google_mlkit_commons:
    dependency: transitive
    description:
      name: google_mlkit_commons
      sha256: "9990a65f407a3ef6bae646bf10143faa93fec126683771465bc6c0b43fb0e6e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.1"
  google_mlkit_face_detection:
    dependency: transitive
    description:
      name: google_mlkit_face_detection
      sha256: "0aeab4f39204f7a235ed4cccedfe7e61401b43f4ef139a868c01fa29fdc225ab"
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  grouped_list:
    dependency: transitive
    description:
      name: grouped_list
      sha256: fef106470186081c32636aa055492eee7fc7fe8bf0921a48d31ded24821af19f
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  gtk:
    dependency: transitive
    description:
      name: gtk
      sha256: e8ce9ca4b1df106e4d72dad201d345ea1a036cc12c360f1a7d5a758f78ffa42c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  hashcodes:
    dependency: transitive
    description:
      name: hashcodes
      sha256: "80f9410a5b3c8e110c4b7604546034749259f5d6dcca63e0d3c17c9258f1a651"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: transitive
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: d9793e10dbe0e6c364f4c59bf3e01fb33a9b2a674bc7a1081693dba0614b6269
      url: "https://pub.dev"
    source: hosted
    version: "0.15.1"
  http:
    dependency: transitive
    description:
      name: http
      sha256: b9c29a161230ee03d3ccf545097fccd9b87a5264228c5d348202e0f0c28f9010
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  hypersnapsdk_flutter:
    dependency: transitive
    description:
      name: hypersnapsdk_flutter
      sha256: b7386908f811e9802798e29c9f9169d19351797569220c44d2cfe2ab79667877
      url: "https://pub.dev"
    source: hosted
    version: "1.5.2"
  hyperverge:
    dependency: transitive
    description:
      path: "../plugins/hyperverge"
      relative: true
    source: path
    version: "0.0.1"
  idb_shim:
    dependency: transitive
    description:
      name: idb_shim
      sha256: "11f4ba861df756b60ef2f799dd5540e77f27cf4a44d9e5f6127ad47d67f65602"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1+2"
  identityapi:
    dependency: transitive
    description:
      path: "public/identityapi"
      ref: "8f49c79921836482e2b61841eaeec5488c2ceb52"
      resolved-ref: "8f49c79921836482e2b61841eaeec5488c2ceb52"
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients"
    source: git
    version: "1.0.0"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "8e9d133755c3e84c73288363e6343157c383a0c6c56fc51afcc5d4d7180306d6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  image_downloader:
    dependency: transitive
    description:
      path: "../forks/image_downloader"
      relative: true
    source: path
    version: "0.32.0"
  image_gallery_saver:
    dependency: transitive
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  image_picker:
    dependency: transitive
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8faba09ba361d4b246dc0a17cb4289b3324c2b9f6db7b3d457ee69106a86bd32"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+17"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "4f0568120c6fcc0aaa04511cb9f9f4d29fc3d0139884b1d06be88dcec7641d6b"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+1"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_size_getter:
    dependency: transitive
    description:
      name: image_size_getter
      sha256: "414ebd27e9967fa7adbcf4a10c8409522faa6f98c87e3e54d2c423db8ad75320"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  in_app_review:
    dependency: transitive
    description:
      name: in_app_review
      sha256: "16328b8202d36522322b95804ae5d975577aa9f584d634985849ba1099645850"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: b12ec9aaf6b34d3a72aa95895eb252b381896246bdad4ef378d444affe8410ef
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  installed_app_detector:
    dependency: transitive
    description:
      path: "../plugins/installed_app_detector"
      relative: true
    source: path
    version: "0.0.1"
  installer_checker:
    dependency: transitive
    description:
      path: "../plugins/installer_checker"
      relative: true
    source: path
    version: "0.0.0"
  integration_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  invertible:
    dependency: transitive
    description:
      name: invertible
      sha256: "2e88300b67085adf5e766839a49e3a8da7db85d8826a9068bba24e015b79381a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  ios_teamid:
    dependency: transitive
    description:
      name: ios_teamid
      sha256: af8cfe090c583612b61855eee4c83bf10b77c980bf8682ce0c54efa685037404
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: a5e201311cb08bf3912ebbe9a2be096e182d703f881136ec1e81a2338a9e120d
      url: "https://pub.dev"
    source: hosted
    version: "0.6.4"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  jwt_decoder:
    dependency: transitive
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  koyal_auth:
    dependency: transitive
    description:
      path: "../packages/koyal_auth"
      relative: true
    source: path
    version: "0.0.1"
  koyal_camera:
    dependency: transitive
    description:
      path: "../packages/koyal_camera"
      relative: true
    source: path
    version: "0.0.1"
  koyal_chatbot:
    dependency: transitive
    description:
      path: "../packages/koyal_chatbot/koyal_chatbot_in"
      relative: true
    source: path
    version: "0.0.1"
  koyal_chatbot_core:
    dependency: transitive
    description:
      path: "../packages/koyal_chatbot/koyal_chatbot_core"
      relative: true
    source: path
    version: "0.0.1"
  koyal_core:
    dependency: transitive
    description:
      path: "../packages/koyal_core"
      relative: true
    source: path
    version: "0.0.1"
  koyal_dynamic_forms:
    dependency: transitive
    description:
      path: "../packages/koyal_dynamic_forms"
      relative: true
    source: path
    version: "0.0.1"
  koyal_flavor:
    dependency: "direct dev"
    description:
      path: "../plugins/koyal_flavor"
      relative: true
    source: path
    version: "1.0.0"
  koyal_installer_checker:
    dependency: transitive
    description:
      path: "../packages/koyal_installer_checker"
      relative: true
    source: path
    version: "0.0.1"
  koyal_localizations:
    dependency: transitive
    description:
      path: "../packages/koyal_localizations"
      relative: true
    source: path
    version: "0.0.1"
  koyal_lock:
    dependency: transitive
    description:
      path: "../packages/koyal_lock"
      relative: true
    source: path
    version: "0.0.1"
  koyal_machine_learning:
    dependency: transitive
    description:
      path: "../packages/koyal_machine_learning"
      relative: true
    source: path
    version: "0.0.1"
  koyal_messaging:
    dependency: transitive
    description:
      path: "../packages/koyal_messaging"
      relative: true
    source: path
    version: "0.0.1"
  koyal_navigation_annotation:
    dependency: transitive
    description:
      path: "../plugins/koyal_navigation_annotation"
      relative: true
    source: path
    version: "1.0.0"
  koyal_otp:
    dependency: transitive
    description:
      path: "../packages/koyal_otp"
      relative: true
    source: path
    version: "0.0.1"
  koyal_shared:
    dependency: transitive
    description:
      path: "../packages/koyal_shared/koyal_shared"
      relative: true
    source: path
    version: "0.0.1"
  koyal_shared_core:
    dependency: transitive
    description:
      path: "../packages/koyal_shared/koyal_shared_core"
      relative: true
    source: path
    version: "0.0.1"
  koyal_testing:
    dependency: transitive
    description:
      path: "../packages/koyal_testing"
      relative: true
    source: path
    version: "0.0.1"
  koyal_ui:
    dependency: transitive
    description:
      path: "../packages/koyal_ui"
      relative: true
    source: path
    version: "0.0.0"
  koyal_ui_components:
    dependency: transitive
    description:
      path: "../packages/koyal_ui_components"
      relative: true
    source: path
    version: "0.0.0"
  launch_app_store:
    dependency: transitive
    description:
      name: launch_app_store
      sha256: "0fbe127f37fbababe3d3d0ab4e74097ffc033bdfb799a041ecb93f3f0ee1455d"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "3f87a60e8c63aecc975dda1ceedbc8f24de75f09e4856ea27daf8958f2f0ce05"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.5"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "932549fb305594d82d7183ecd9fa93463e9914e1b67cacc34bc40906594a1806"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  linkify:
    dependency: transitive
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  liquid_pull_to_refresh:
    dependency: transitive
    description:
      name: liquid_pull_to_refresh
      sha256: "11e4cd8c5460085a31b479ec4e1cd063eb8e599f35684d57a44dafa1fd1f67f3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  local_auth:
    dependency: transitive
    description:
      name: local_auth
      sha256: "434d854cf478f17f12ab29a76a02b3067f86a63a6d6c4eb8fbfdcfe4879c1b7b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: e0e5b1ea247c5a0951c13a7ee13dc1beae69750e6a2e1910d1ed6a3cd4d56943
      url: "https://pub.dev"
    source: hosted
    version: "1.0.38"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "6d2950da311d26d492a89aeb247c72b4653ddc93601ea36a84924a396806d49c"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "1b842ff177a7068442eae093b64abe3592f816afd2a533c0ebcdbe40f9d2075a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: bc4e66a29b0fdf751aafbec923b5bed7ad6ed3614875d8151afe2578520b2ab5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: db2ff852ed77090ba9f62d3611e4208a3d11dfa35991a81ae724c113fcb3e3f7
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  logger_flutter:
    dependency: transitive
    description:
      path: "../forks/logger_flutter"
      relative: true
    source: path
    version: "0.9.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c0bbfe94d46aedf9b8b3e695cf3bd48c8e14b35e3b2c639e0aa7755d589ba946
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: b1fec1293bba18c31011e3d53caff16157b80fd5174212f7a15354e3c4fc4f1c
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  maps_launcher:
    dependency: transitive
    description:
      name: maps_launcher
      sha256: "57ba3c31db96e30f58c23fcb22a1fac6accc5683535b2cf344c534bbb9f8f910"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: b3c60dee8c2af50ad0e6e90cceba98e47718a6ee0a7a6772c77846a0cc21f78b
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  mask_text_input_formatter:
    dependency: transitive
    description:
      name: mask_text_input_formatter
      sha256: "19bb7809c3c2559277e95521b3ee421e1409eb2cc85efd2feb191696c92490f4"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  merge_images:
    dependency: transitive
    description:
      name: merge_images
      sha256: d8162a42642d88d447e11f887394261fb213aac0523111ccd48f0d815d10a9b3
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0-nullsafety"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: bdb68674043280c3428e9ec998512fb681678676b3c54e773629ffe74419f8c7
      url: "https://pub.dev"
    source: hosted
    version: "1.15.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  mobilecommonapi:
    dependency: transitive
    description:
      path: "public/mobilecommonapi"
      ref: f98979b2c59590ea38ffd151c8fe8b11bc5624b7
      resolved-ref: f98979b2c59590ea38ffd151c8fe8b11bc5624b7
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients"
    source: git
    version: "1.0.0"
  mocktail:
    dependency: transitive
    description:
      name: mocktail
      sha256: dd85ca5229cf677079fd9ac740aebfc34d9287cdf294e6b2ba9fae25c39e4dc2
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  multiple_localization:
    dependency: transitive
    description:
      name: multiple_localization
      sha256: "8b071f538bdf087a7bf13cd8b78a1a041994960c8e9f0a1aaaaf0cfb39845019"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  navigation_history_observer:
    dependency: transitive
    description:
      name: navigation_history_observer
      sha256: "0e9bf4914eea89baa84f5882581e20541be73e1a14d0cbf1f279a695aff71cc3"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  navigator:
    dependency: transitive
    description:
      path: "../packages/navigator"
      relative: true
    source: path
    version: "0.0.1"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  nock:
    dependency: transitive
    description:
      name: nock
      sha256: e5bb7dae9c94405477988cf3bd8a94ef141abd74577125d6a51d47909ff107f9
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "8ebdbaa3b96d5285d068f80772390d27c21e1fa10fb2df6627b1b9415043608d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  onepay_custom_deeplink:
    dependency: transitive
    description:
      path: "../plugins/onepay_custom_deeplink"
      relative: true
    source: path
    version: "0.0.1"
  open_filex:
    dependency: "direct overridden"
    description:
      name: open_filex
      sha256: "854aefd72dfd74219dc8c8d1767c34ec1eae64b8399a5be317bddb1ec2108915"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.2"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: cb44f49b6e690fa766f023d5b22cac6b9affe741dd792b6ac7ad4fabe0d7b097
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: c464428172cb986b758c6d1724c603097febb8fb855aa265aeecc9280c294d4a
      url: "https://pub.dev"
    source: hosted
    version: "2.2.12"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  paynimo:
    dependency: transitive
    description:
      path: "../plugins/paynimo"
      relative: true
    source: path
    version: "0.0.0"
  payu:
    dependency: transitive
    description:
      path: "../plugins/payu"
      relative: true
    source: path
    version: "0.0.0"
  payu_core:
    dependency: transitive
    description:
      path: "../plugins/payu_core"
      relative: true
    source: path
    version: "0.0.0"
  pdf_merger:
    dependency: transitive
    description:
      path: "../forks/pdf_merger"
      relative: true
    source: path
    version: "0.0.6"
  pdfx:
    dependency: transitive
    description:
      path: "../forks/pdfx"
      relative: true
    source: path
    version: "2.6.0"
  percent_indicator:
    dependency: transitive
    description:
      name: percent_indicator
      sha256: c37099ad833a883c9d71782321cb65c3a848c21b6939b6185f0ff6640d05814c
      url: "https://pub.dev"
    source: hosted
    version: "4.2.3"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: "18bf33f7fefbd812f37e72091a15575e72d5318854877e0e4035a24ac1113ecb"
      url: "https://pub.dev"
    source: hosted
    version: "11.3.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "71bbecfee799e65aff7c744761a57e817e73b738fedf62ab7afd5593da21f9f1"
      url: "https://pub.dev"
    source: hosted
    version: "12.0.13"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: e6f6d73b12438ef13e648c4ae56bd106ec60d17e90a59c4545db6781229082a0
      url: "https://pub.dev"
    source: hosted
    version: "9.4.5"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: af26edbbb1f2674af65a8f4b56e1a6f526156bc273d0e65dd8075fab51c78851
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+2"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: e9c8eadee926c4532d0305dff94b85bf961f16759c3af791486613152af4b4f9
      url: "https://pub.dev"
    source: hosted
    version: "4.2.3"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "8036802a00bae2a78fc197af8a158e3e2f7b500561ed23b4c458107685e645bb"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.0"
  pin_input_text_field:
    dependency: transitive
    description:
      name: pin_input_text_field
      sha256: f45683032283d30b670ec343781660655e3e1953438b281a0bc6e2d358486236
      url: "https://pub.dev"
    source: hosted
    version: "4.5.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "9b71283fc13df574056616011fb138fd3b793ea47cc509c189a6c3fa5f8a1a65"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.5"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: db7306cf0249f838d1a24af52b5a5887c5bf7f31d8bb4e827d071dc0939ad346
      url: "https://pub.dev"
    source: hosted
    version: "3.6.2"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  pretty_dio_logger:
    dependency: transitive
    description:
      name: pretty_dio_logger
      sha256: "948f7eeb36e7aa0760b51c1a8e3331d4b21e36fabd39efca81f585ed93893544"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0-beta-1"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "21e54fd2faf1b5bdd5102afd25012184a6793927648ea81eea80552ac9405b32"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  progress_dialog:
    dependency: transitive
    description:
      path: "../forks/progress_dialog"
      relative: true
    source: path
    version: "1.2.4-nullsafety"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "307de764d305289ff24ad257ad5c5793ce56d04947599ad68b3baa124105fc17"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  pub_updater:
    dependency: transitive
    description:
      name: pub_updater
      sha256: "54e8dc865349059ebe7f163d6acce7c89eb958b8047e6d6e80ce93b13d7c9e60"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  pubspec:
    dependency: transitive
    description:
      name: pubspec
      sha256: f534a50a2b4d48dc3bc0ec147c8bd7c304280fff23b153f3f11803c4d49d927e
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5c4208b4dc0d55c3184d10d83ee0ded6212dc2b5e2ba17c5a0c0aab279128d21"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: transitive
    description:
      name: qr_flutter
      sha256: c5c121c54cb6dd837b9b9d57eb7bc7ec6df4aee741032060c8833a678c80b87e
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  rational:
    dependency: transitive
    description:
      name: rational
      sha256: cb808fb6f1a839e6fc5f7d8cb3b0a10e1db48b3be102de73938c627f0b636336
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  restart_app:
    dependency: transitive
    description:
      name: restart_app
      sha256: "00d5ec3e9de871cedbe552fc41e615b042b5ec654385e090e0983f6d02f655ed"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  retrofit:
    dependency: transitive
    description:
      name: retrofit
      sha256: "9254ec985d5e26a839a9070ae25b98f0781c9c420e4241c5fb8b8965aa1fc7f2"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5d22055fd443806c03ef24a02000637cf51eae49c2a0168d38a43fc166b0209c"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.5"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "12669c4a913688a26555323fb9cec373d8f9fbe091f2d01c40c723b33caa8989"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  sas_collector:
    dependency: transitive
    description:
      path: "../plugins/sas_collector"
      relative: true
    source: path
    version: "0.0.1"
  scrollable_positioned_list:
    dependency: transitive
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  secure_application:
    dependency: transitive
    description:
      path: "../forks/secure_application"
      relative: true
    source: path
    version: "3.8.0"
  selfcareapi:
    dependency: transitive
    description:
      path: "public/selfcareapi"
      ref: 2ccaddb460b6c93ccd28c8dff05f296cbd81b6d3
      resolved-ref: 2ccaddb460b6c93ccd28c8dff05f296cbd81b6d3
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients"
    source: git
    version: "1.0.0"
  sembast:
    dependency: transitive
    description:
      name: sembast
      sha256: "9a9f0c7aca07043fef857b8b365f41592e48832b61462292699b57978e241c11"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.0"
  sembast_web:
    dependency: transitive
    description:
      name: sembast_web
      sha256: "1ba0402b9491b76312358ec3cb3a33b9fc22eb673d4f9a3e99b2425135add72c"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  sensors:
    dependency: transitive
    description:
      name: sensors
      sha256: "69c10fe94a63dd7c6f5e6557366e5329a713bf6c45d885d95a56642dd8722e35"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  share:
    dependency: transitive
    description:
      name: share
      sha256: "97e6403f564ed1051a01534c2fc919cb6e40ea55e60a18ec23cee6e0ce19f4be"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  share_plus:
    dependency: transitive
    description:
      name: share_plus
      sha256: fb5319f3aab4c5dda5ebb92dca978179ba21f8c783ee4380910ef4c1c6824f51
      url: "https://pub.dev"
    source: hosted
    version: "8.0.3"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: c24a96135a2ccd62c64b69315a14adc5c3419df63b4d7c05832a346fdb73682c
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: aef74dc9195746a384843102142ab65b6a4735bb3beea791e63527b88cc83306
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: e792b76b96a36d4a41b819da593aff4bdd413576b3ba6150df5d8d9996d2e74c
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: a988c0e8d8ffbdb8a28aa7ec8e449c260f3deb808781fe1284d22c5bba7156e8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  shimmer:
    dependency: transitive
    description:
      name: shimmer
      sha256: "1f1009b5845a1f88f1c5630212279540486f97409e9fc3f63883e71070d107bf"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sms_autofill:
    dependency: transitive
    description:
      name: sms_autofill
      sha256: c65836abe9c1f62ce411bb78d5546a09ece4297558070b1bd871db1db283aaf9
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: "84cf769ad83aa6bb61e0aa5a18e53aea683395f196a6f39c4c881fb90ed4f7ae"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "52de2200bb098de739794c82d09c41ac27b2e42fd7e23cce7b9c74bf653c7296"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.10"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: "2d7299468485dca85efeeadf5d38986909c5eb0cd71fd3db2c2f000e6c9454bb"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "78f489aab276260cdd26676d2169446c7ecd3484bbd5fead4ca14f3ed4dd9ee3"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "4468b24876d673418a7b7147e5a08a715b4998a7ae69227acafaab762e0e5490"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4+5"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "96a698e2bc82bd770a4d6aab00b42396a7c63d9e33513a56945cbccb594c2474"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  string_unescape:
    dependency: transitive
    description:
      name: string_unescape
      sha256: b9628c8516cbb47ffced9a47440942ac7b480efd9b7dcd5058e27cf6819e99f7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  sync_http:
    dependency: transitive
    description:
      name: sync_http
      sha256: "7f0cd72eca000d2e026bcd6f990b81d0ca06022ef4e32fb257b30d3d1014a961"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "69fe30f3a8b04a0be0c15ae6490fc859a78ef4c43ae2dd5e8a623d45bfcf9225"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0+3"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test:
    dependency: "direct dev"
    description:
      name: test
      sha256: "7ee44229615f8f642b68120165ae4c2a75fe77ae2065b1e55ae4711f6cf0899e"
      url: "https://pub.dev"
    source: hosted
    version: "1.25.7"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5b8a98dafc4d5c4c9c72d8b31ab2b23fc13422348d2997120294d3bac86b4ddb"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  test_core:
    dependency: transitive
    description:
      name: test_core
      sha256: "55ea5a652e38a1dfb32943a7973f3681a60f872f8c3a05a14664ad54ef9c6696"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.4"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  truecaller:
    dependency: transitive
    description:
      path: "../plugins/truecaller"
      relative: true
    source: path
    version: "0.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: "53bdf7e979cfbf3e28987552fd72f637e63f3c8724c9e56d9246942dc2fa36ee"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: d315be0f6641898b280ffa34e2ddb14f3d12b1a37882557869646e0cc363d0cc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  uri:
    dependency: transitive
    description:
      name: uri
      sha256: "889eea21e953187c6099802b7b4cf5219ba8f3518f604a1033064d45b1b8268a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "360a6ed2027f18b73c8d98e159dda67a61b7f2e0f6ec26e86c3ada33b0621775"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: e2b9622b4007f97f504cd64c0128309dfb978ae66adbe944125ed9e1750f06af
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "769549c999acdb42b8bcfa7c43d72bf79a382ca7441ab18a808e101149daf672"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "44cf3aabcedde30f2dba119a9dea3b0f2672fbe6fa96e85536251d678216b3c4"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  value_layout_builder:
    dependency: transitive
    description:
      name: value_layout_builder
      sha256: "98202ec1807e94ac72725b7f0d15027afde513c55c69ff3f41bcfccb950831bc"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "773c9522d66d523e1c7b25dfb95cc91c26a1e17b107039cfe147285e92de7878"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.14"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "2430b973a4ca3c4dbc9999b62b8c719a160100dcbae5c819bae0cacce32c9cdb"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.12"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: ab9ff38fc771e9ee1139320adbe3d18a60327370c218c60752068ebee4b49ab1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.15"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vibration:
    dependency: transitive
    description:
      name: vibration
      sha256: "06588a845a4ebc73ab7ff7da555c2b3dbcd9676164b5856a38bf0b2287f1045d"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  vibration_platform_interface:
    dependency: transitive
    description:
      name: vibration_platform_interface
      sha256: f66b39aab2447038978c16f3d6f77228e49ef5717556e3da02313e044e4a7600
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  video_player:
    dependency: "direct overridden"
    description:
      name: video_player
      sha256: aced48e701e24c02b0b7f881a8819e4937794e46b5a5821005e2bf3b40a324cc
      url: "https://pub.dev"
    source: hosted
    version: "2.8.7"
  video_player_android:
    dependency: "direct overridden"
    description:
      name: video_player_android
      sha256: "4de50df9ee786f5891d3281e1e633d7b142ef1acf47392592eb91cba5d355849"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: cd5ab8a8bc0eab65ab0cea40304097edc46da574c8c1ecdee96f28cd8ef3792f
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "229d7642ccd9f3dc4aba169609dd6b5f3f443bb4cc15b82f7785fcada5af9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.3"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "881b375a934d8ebf868c7fb1423b2bfaa393a0a265fa3f733079a86536064a10"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "5c5f338a667b4c644744b661f309fb8080bb94b18a7e91ef1dbd343bed00ed6d"
      url: "https://pub.dev"
    source: hosted
    version: "14.2.5"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "3d2ad6751b3c16cf07c7fca317a1413b3f26530319181b37e3b9039b84fc01d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  web:
    dependency: "direct overridden"
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_ffi:
    dependency: transitive
    description:
      name: web_ffi
      sha256: "48ef8037f7bc051d11b88d0f2903e02bec21092c51833d37c3361c36e3edc4f7"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: ca49c0bc209c687b887f30527fb6a9d80040b072cc2990f34b9bec3e7663101b
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  webdriver:
    dependency: transitive
    description:
      name: webdriver
      sha256: "003d7da9519e1e5f329422b36c4dcdf18d7d2978d1ba099ea4e45ba490ed845e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "67d3a8b6c79e1987d19d848b0892e582dbb0c66c57cc1fef58a177dd2aa2823d"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: "direct overridden"
    description:
      name: webview_flutter_android
      sha256: dad3313c9ead95517bb1cae5e1c9d20ba83729d5a59e5e83c0a2d66203f27f91
      url: "https://pub.dev"
    source: hosted
    version: "3.16.1"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: f14ee08021772fed913da8daebcfdeb46be457081e521e93e9918fe6cd1ce9e8
      url: "https://pub.dev"
    source: hosted
    version: "3.16.1"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "84ba388638ed7a8cb3445a320c8273136ab2631cd5f2c57888335504ddab1bc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  workmanager:
    dependency: transitive
    description:
      path: "../forks/flutter_workmanager"
      relative: true
    source: path
    version: "0.5.1-fork.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "23812a9b125b48d4007117254bca50abb6c712352927eece9e155207b1db2370"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  ymchat_flutter:
    dependency: transitive
    description:
      name: ymchat_flutter
      sha256: "479bdef883bd85a4889db525b6f06205e3199726f497f00d90a088264c6e88ba"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  youtube_player_flutter:
    dependency: transitive
    description:
      path: "../forks/youtube_player_flutter/packages/youtube_player_flutter"
      relative: true
    source: path
    version: "9.0.1"
sdks:
  dart: ">=3.5.1 <4.0.0"
  flutter: ">=3.24.0"
