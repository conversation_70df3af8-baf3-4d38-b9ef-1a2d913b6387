import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_app_bar.dart';

class SecondIdentifierPage extends TestPageWithAppBar {
  late TestElement confirmButton;
  late TestElement escapeButton;
  late TestElement panInput;
  late TestElement lanInput;
  late TestElement idCardInput;
  late TestElement ktpInput;
  late TestElement invalidCredentialsDialogOkButton;
  late TestElement blockedDialog;
  late TestElement blockedDialogButton;
  late TestElement insiderUserUsedForPublicAccountDialog;
  late TestElement insiderUserUsedForPublicAccountDialogButton;

  SecondIdentifierPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__secondIdentifierScreen__')),
        ) {
    confirmButton = registerButtonElement(finder: find.byKey(const Key('__secondIdentifierConfirmButton__')));
    escapeButton = registerButtonElement(finder: find.byKey(const Key('__secondIdentifierEscapeOptionButton__')));
    panInput = registerButtonElement(finder: find.byKey(const Key('__secondId_pan_input__')));
    lanInput = registerButtonElement(finder: find.byKey(const Key('__secondId_lan_input__')));
    idCardInput = registerButtonElement(finder: find.byKey(const Key('__secondId_idcard_input__')));
    ktpInput = registerButtonElement(finder: find.byKey(const Key('__secondId_ktp_input__')));
    invalidCredentialsDialogOkButton =
        registerButtonElement(finder: find.byKey(const Key('__SecondIdentifierInvalidCredentialsDialogOkButton__')));
    blockedDialog = registerButtonElement(finder: find.byKey(const Key('__SecondIdentifierBlockedDialog_')));
    blockedDialogButton =
        registerButtonElement(finder: find.byKey(const Key('__SecondIdentifierInvalidCredentialsDialogOkButton__')));
    insiderUserUsedForPublicAccountDialog =
        registerButtonElement(finder: find.byKey(const Key('__showInsiderUserUsedForPublicAccountDialog_')));
    insiderUserUsedForPublicAccountDialogButton =
        registerButtonElement(finder: find.byKey(const Key('__showInsiderUserUsedForPublicAccountDialogButton_')));
  }
}
