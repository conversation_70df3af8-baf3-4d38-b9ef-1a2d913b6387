import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../base/test_page_with_app_bar.dart';

class IdCardPreviewInnovatrics extends TestPageWithAppBar {
  IdCardPreviewInnovatrics(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__idCardPreviewScreen__')));

  final Finder _continueButton = find.byKey(const Key('__idCardPreviewConfirmButton__'));

  late TestElement continueButton = registerButtonElement(finder: _continueButton);

  Future<void> tapContinue() async {
    await continueButton.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }
}
