import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_phone_input.dart';
import 'home_page.dart';
import 'pin_page.dart';

class AuthLandingPage extends TestPageWithPhoneInput {
  late TestElement confirmPhoneNumberButton;
  late TestElement continueAsGuestButton;

  AuthLandingPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__authLandingScaffold__')),
        ) {
    confirmPhoneNumberButton = registerButtonElement(finder: find.byKey(const Key('__confirmPhoneButton__')));
    continueAsGuestButton = registerButtonElement(finder: find.byKey(const Key('__continueAsGuestButton__')));
  }

  Future<void> fillPhone({
    required String phoneNumber,
    bool settleAfterPump = true,
  }) async {
    await phoneNumberInputField.enterText(phoneNumber);
    await confirmPhoneNumberButton.tap(settleAfterPump: settleAfterPump);
  }

  Future<void> fillSignIn({
    required String phoneNumber,
    required String password,
  }) async {
    final pinPage = PinPage(tester);
    final homePage = HomeTestPage(tester);

    await phoneNumberInputField.enterText(phoneNumber);
    await confirmPhoneNumberButton.tap();

    pinPage.isReady();
    await pinPage.enterPin(password);

    await tester.pumpAndSettle(const Duration(milliseconds: 200));
    // you should end on home after this
    homePage.isReady();
  }

  Future<void> tapLogo({required Key logoKey}) async {
    final logo = find.byKey(logoKey);
    await tester.tap(logo);
    await tester.pumpAndSettle(const Duration(milliseconds: 200));
  }
}
