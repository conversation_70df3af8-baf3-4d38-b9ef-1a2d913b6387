import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class ProductOfferDetailPage extends TestPage {
  late TestElement decreseButton;
  late TestElement increseButton;
  late TestElement amountText;
  late TestElement progress;
  late TestElement amountSlider;
  late TestElement continueButton;
  late TestElement faqButton;
  late TestElement vasOverlay;
  late TestElement overlayNoButton;
  late TestElement vasDetailScreeen;
  late TestElement vasDetailContinue;

  ProductOfferDetailPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__productOfferDetailPage__'))) {
    decreseButton = registerButtonElement(finder: find.byKey(const Key('__inputCalculatorDecreseButton__')));
    increseButton = registerButtonElement(finder: find.byKey(const Key('__inputCalculatorIncreseButton__')));
    amountText = registerTextElement(finder: find.byKey(const Key('__inputCalculatorValueText__')));
    progress = registerElement(finder: find.byKey(const Key('__progressIndicator__')));
    amountSlider = registerElement(finder: find.byKey(const Key('__inputCalculatorSlider__')));
    continueButton = registerButtonElement(finder: find.byKey(const Key('__continueButton__')));
    faqButton = registerButtonElement(finder: find.byKey(const Key('__faqButton__')));
    vasOverlay = registerElement(finder: find.byKey(const Key('__vasOverlay__')));
    overlayNoButton = registerButtonElement(finder: find.byKey(const Key('__overlayNoButton__')));
    vasDetailScreeen = registerElement(finder: find.byKey(const Key('__vasDetailScreen__')));
    vasDetailContinue = registerButtonElement(finder: find.byKey(const Key('__vasDetailContinueButton__')));
  }

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  String getAmoutText() {
    final text = amountText.finder.evaluate().single.widget as Text;
    return text.data ?? '';
  }

  Future<void> tapInstallment(int number) async {
    final element = registerElement(finder: find.byKey(Key('__installment-${number}__')));
    await element.tap(settleAfterPump: false);
  }

  bool matchInstanllmentBorderColor(int number, Color color) {
    final installmentItemFinder = find.byKey(Key('__installment-${number}__'));
    final container = find
        .descendant(of: installmentItemFinder, matching: find.byType(AnimatedContainer))
        .evaluate()
        .single
        .widget as AnimatedContainer;
    final decoration = container.decoration as BoxDecoration?;
    final border = decoration?.border as Border?;
    return border!.right.color == color;
  }

  bool isContinueEnabled() {
    final button = continueButton.finder.evaluate().single.widget as PrimaryButton;
    return button.onPressed != null;
  }

  Future<void> tapContinue() async {
    await continueButton.tap(settleAfterPump: false);
  }

  Future<void> selectVas(String vasId) async {
    final vasCheckboxFinder =
        find.descendant(of: find.byKey(Key('__vas${vasId}__')), matching: find.byType(KoyalCheckbox));
    await registerButtonElement(finder: vasCheckboxFinder).tap(settleAfterPump: false);
  }

  Future<void> tapFaq() async {
    await tester.ensureVisible(faqButton.finder);
    await tester.pumpAndSettle();
    await faqButton.tap(settleAfterPump: false);
  }

  Future<void> closeFaq() async {
    // tap outside of modal
    await tester.tap(find.byType(KoyalAppBar).first);
  }

  Finder findSectionHeadingComponent() {
    return find.byKey(const Key('__faqSectionHeading__'));
  }

  Future<void> goToVasDetail(String vasId) async {
    final vasButton = find.descendant(of: find.byKey(Key('__vas${vasId}__')), matching: find.byType(TertiaryButton));
    final element = registerButtonElement(finder: vasButton);
    await element.scrollTo();
    await element.tap();
  }

  Future<void> selectProtection() async {
    await selectProtectionType('__protectionItem__');
  }

  Future<void> selectNoProtection() async {
    await selectProtectionType('__noProtectionItem__');
  }

  Future<void> selectProtectionType(String key) async {
    final gestureDet = find.descendant(of: find.byKey(Key(key)), matching: find.byType(GestureDetector));
    final element = registerElement(finder: gestureDet);
    await element.scrollTo();
    await element.tap();
  }

  Future<void> slide(double value, double max) async {
    //6 is half of touchable circle
    const halfOfTouchable = 6.0;

    final totalWidth = tester.getSize(amountSlider.finder).width;
    final zeroPoint =
        tester.getTopLeft(amountSlider.finder) + const Offset(KoyalPadding.paddingNormal, halfOfTouchable);
    final calculatedOffset = value * ((totalWidth - KoyalPadding.paddingNormal) / max);
    await tester.dragFrom(zeroPoint, Offset(calculatedOffset, halfOfTouchable));
    await tester.pump();
  }
}
