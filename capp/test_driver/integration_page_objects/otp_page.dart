import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_num_keyboard.dart';

class OtpPage extends TestPageWithNumKeyboard {
  late TestElement helperLastNotificationBody;
  late TestElement otpTextField;
  late TestElement countdownResend;
  late TestElement noAccessToPhoneNumberButton;
  late TestElement otpSendButton;
  late TestElement temporarilyUnavailableDialog;
  late TestElement temporarilyUnavailableDialogButton;
  late TestElement otpGenerationFailedTitle;
  late TestElement otpGenerationFailedToHomeButton;
  late TestElement otpGenerationFailedToContactUsButton;

  OtpPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__otpScreen__')),
        ) {
    helperLastNotificationBody = registerTextElement(finder: find.byKey(const Key('__lastNotificationBody__')));
    otpTextField = registerTextFieldElement(finder: find.byKey(const Key('__otpTextField__')));
    countdownResend = registerElement(finder: find.byKey(const Key('__countdownResend__')));
    noAccessToPhoneNumberButton =
        registerButtonElement(finder: find.byKey(const Key('__noAccessToPhoneNumberButton__')));
    otpSendButton = registerButtonElement(finder: find.byKey(const Key('__otpSendButton__')));
    temporarilyUnavailableDialog =
        registerButtonElement(finder: find.byKey(const Key('__dialogOtpTemporarilyUnavailable__')));
    temporarilyUnavailableDialogButton =
        registerButtonElement(finder: find.byKey(const Key('__dialogOtpTemporarilyUnavailableButton__')));
    otpGenerationFailedTitle = registerElement(finder: find.byKey(const Key('__otpGenerateFailedScreen__')));
    otpGenerationFailedToHomeButton =
        registerButtonElement(finder: find.byKey(const Key('__otpGenerateFailedToHomeButton__')));
    otpGenerationFailedToContactUsButton =
        registerButtonElement(finder: find.byKey(const Key('__otpGenerateFailedToContactUsButton__')));
  }

  Future<void> enterOtp(
    String otp, {
    bool settleAfterPump = false,
    bool systemKeyboard = true,
  }) {
    return enterNumber(
      otp,
      settleAfterPump: settleAfterPump,
      otpInputField: systemKeyboard ? otpTextField : null,
    );
  }
}
