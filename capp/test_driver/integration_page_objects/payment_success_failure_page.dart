import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'base/test_page_with_app_bar.dart';

class PaymentSuccessFailurePage extends TestPageWithAppBar {
  PaymentSuccessFailurePage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentSuccessFailureScreen__')),
          appBarFinder: find.byKey(const Key('__paymentSuccessFailureScreenAppBar__')),
        );

  @override
  void isReady() {
    return screen.isReady();
  }
}
