import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_app_bar.dart';

class PinPassSetSuccessScreenVn extends TestPageWithAppBar {
  late TestElement continueButton;

  PinPassSetSuccessScreenVn(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__PinPassSetSuccessScreen__'))) {
    continueButton = registerButtonElement(finder: find.byKey(const Key('__PinPassSetSuccessScreenContinueButton__')));
  }
}
