import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class ChangePhoneBosCanceledPage extends TestPage {
  late TestElement reasonOfChangeOption;
  late TestElement tryAgainButton;
  late TestElement laterButton;

  ChangePhoneBosCanceledPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__ChangePhoneBosCancelScreen__')),
        ) {
    tryAgainButton = registerButtonElement(
      finder: find.byKey(const Key('__TryAgainButton__')),
    );
    laterButton = registerButtonElement(
      finder: find.byKey(const Key('__DoItLaterButton__')),
    );
  }
}
