import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class ChangePhoneBosSuccessPage extends TestPage {
  late TestElement reasonOfChangeOption;
  late TestElement continueButton;

  ChangePhoneBosSuccessPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__ChangePhoneBosSuccessScreen__')),
        ) {
    continueButton = registerButtonElement(
      finder: find.byKey(const Key('__ContinueButton__')),
    );
  }
}
