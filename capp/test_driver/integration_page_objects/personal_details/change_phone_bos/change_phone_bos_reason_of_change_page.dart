import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class ChangePhoneBosReasonOfChangePage extends TestPage {
  late TestElement reasonOfChangeOption;
  late TestElement continueButton;

  ChangePhoneBosReasonOfChangePage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__ChangePhoneBosReasonOfChangeScreen__')),
        ) {
    reasonOfChangeOption = registerElement(
      finder: find.byKey(Key('__reasonOfChange${ReasonOfChange.noLongerUsed}Option__')),
    );
    continueButton = registerButtonElement(
      finder: find.byKey(const Key('__ChangePhoneBosReasonOfChangeContinueButton__')),
    );
  }
}
