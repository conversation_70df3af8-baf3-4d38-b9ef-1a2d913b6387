import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class QwartaDocumentsPage extends TestPageWithAppBar {
  late TestElement firstDocument;

  QwartaDocumentsPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__qwartaDocumentsScreen__')),
          appBarFinder: find.byKey(const Key('__qwartaDocumentsScreenAppBar__')),
        );

  Future<void> checkPage() async {
    final documents = find.byKey(const Key('__ContractualDocumentListItem__'));
    firstDocument = registerButtonElement(finder: documents.first);
  }
}
