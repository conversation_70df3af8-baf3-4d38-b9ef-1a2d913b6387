import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class UjjwalDetailPage extends TestPageWithAppBar {
  final _ujjwalDetailScrollableFinder = find.byKey(const Key('__ujwallDetailScrollable__'));
  final _alertMessageFinder = find.byKey(const Key('__ujjwalSignedAlertMessage__'));
  final _viewTransactionHistoryButtonFinder = find.byKey(const Key('__ujjwallDetailViewTransactionsHistoryButton__'));
  final _payNowButtonFinder = find.byKey(const Key('__ujjwalDetailScreenPayNow__'));
  final _expandedRowFinder = find.byKey(const Key('__ujwallDetailExpandedRow__'));
  final _ujwallDetailGraphFinder = find.byKey(const Key('__ujwallDetailGraph__'));
  final __emiDetailViewDetailsAndDocumentsFinder = find.by<PERSON>ey(const Key('__emiDetailViewDetailsAndDocuments__'));
  final __detailsFinder = find.byKey(const Key('__ujjwal_action_details__'));

  late TestElement alertMessage;
  late TestElement viewTransactionHistoryButton;
  late TestElement payNowButton;
  late TestElement expandedRow;
  late TestElement ujwallDetailGraph;
  late TestElement detailsAndDocuments;
  late TestElement monthlyStatementsButton;
  late TestElement transactionDetailButton;
  late TestElement detailsButton;

  UjjwalDetailPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__ujjwalDetailScreen__')),
          appBarFinder: find.byKey(const Key('__ujjwalDetailScreenAppBar__')),
        ) {
    alertMessage = registerElement(finder: _alertMessageFinder);
    viewTransactionHistoryButton = registerButtonElement(finder: _viewTransactionHistoryButtonFinder);
    expandedRow = registerElement(finder: _expandedRowFinder);
    payNowButton = registerButtonElement(finder: _payNowButtonFinder);
    ujwallDetailGraph = registerElement(finder: _ujwallDetailGraphFinder);
    detailsAndDocuments = registerButtonElement(finder: __emiDetailViewDetailsAndDocumentsFinder);
    transactionDetailButton =
        registerButtonElement(finder: find.byKey(const Key('__ujjwal_transaction_view_details__')).first);
    monthlyStatementsButton =
        registerButtonElement(finder: find.byKey(const Key('__ujwallDetailScreenMonthlyStatementsButton__')));
    detailsButton = registerButtonElement(finder: __detailsFinder);
  }

  Future<void> checkPageSignedState() async {
    alertMessage.isReady();
  }

  Future<void> checkPage() async {
    expandedRow.isReady();
    ujwallDetailGraph.isReady();
    detailsAndDocuments.isReady();
    final transactions = find.byKey(const Key('__UjjwalDetailTransactionListItem__'));

    expect(transactions.evaluate().length, 3);
  }

  Future<void> scrollToTransactionHistory({
    bool reverse = false,
  }) async {
    logger.i(
      'Scrolling to transactionsHistory.\n'
      'finder: $_viewTransactionHistoryButtonFinder',
    );
    await tester.dragUntilVisible(
      _viewTransactionHistoryButtonFinder,
      _ujjwalDetailScrollableFinder,
      Offset(0, reverse ? 200.0 : -200.0),
    );
  }
}
