import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class BnplTransactionsHistoryPage extends TestPageWithAppBar {
  late TestElement firstItem;

  BnplTransactionsHistoryPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__bnplTransactionsHistoryScreen__')),
          appBarFinder: find.by<PERSON>ey(const Key('__bnplTransactionsHistoryScreenAppBar__')),
        );

  Future<void> checkPage() async {
    final items = find.byKey(const Key('__bnplTransactionsListItem__'));
    firstItem = registerButtonElement(finder: items.first);
  }
}
