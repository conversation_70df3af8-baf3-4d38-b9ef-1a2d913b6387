import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class FlexiDetailPage extends TestPageWithAppBar {
  final _flexiDetailScrollableFinder = find.byKey(const Key('__flexiOverviewScrollable__'));
  final _buttonPayNowFinder = find.byKey(const Key('__flexiPayNowButton__'));
  final _buttonWithdrawalFinder = find.byKey(const Key('__flexiActionBeltWithdraw_'));
  final _buttonTransactionsHistoryFinder = find.byKey(const Key('__flexiDetailTransactionsHistoryButton__'));

  late TestElement withdrawalButton;
  late TestElement payButton;
  late TestElement pendingMessageAlert;
  late TestElement transactionHistoryButton;
  late TestElement viewMonthlyBillsButton;
  late TestElement monthlyStatementsButton;

  final _viewMonthlyBillsButtonFinder = find.byKey(const Key('__flexiViewMonthlyBills__'));

  FlexiDetailPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__flexiOverviewScreen__')),
          appBarFinder: find.byKey(const Key('__flexiOverviewScreenAppBar__')),
        ) {
    withdrawalButton = registerButtonElement(finder: _buttonWithdrawalFinder);
    payButton = registerButtonElement(finder: _buttonPayNowFinder);
    transactionHistoryButton = registerButtonElement(finder: _buttonTransactionsHistoryFinder);
    viewMonthlyBillsButton = registerButtonElement(finder: _viewMonthlyBillsButtonFinder);
    pendingMessageAlert =
        registerElement(finder: find.byKey(const Key('__flexiDetailHeader__ActivationPendigAlert__')));
    transactionHistoryButton = registerButtonElement(finder: _buttonTransactionsHistoryFinder);
    monthlyStatementsButton = registerButtonElement(finder: find.byKey(const Key('__flexiMonthlyStatements_')));
  }

  Future<void> checkPage() async {
    withdrawalButton.isReady();
    payButton.isReady();
    transactionHistoryButton.isReady();
  }

  Future<void> scrollToTransactionHistory({
    bool reverse = false,
  }) async {
    logger.i(
      'Scrolling to transactionsHistory.\n'
      'finder: $_buttonTransactionsHistoryFinder',
    );
    await tester.dragUntilVisible(
      _buttonTransactionsHistoryFinder,
      _flexiDetailScrollableFinder,
      Offset(0, reverse ? 200.0 : -200.0),
    );
  }
}
