import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class FlexiWithdrawalOtpPage extends TestPageWithAppBar {
  final _confirmButtonFinder = find.byKey(const Key('__flexiWithdrawalConfirmOtp__'));
  final _inputFinder = find.byKey(const Key('__flexiWithdrawalOtpInput__'));

  late TestElement confirmButton;
  late TestElement input;

  FlexiWithdrawalOtpPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__flexiWithdrawalOtpScreen__')),
          appBarFinder: find.byKey(const Key('__flexiWithdrawalOtpScreenAppBar__')),
        ) {
    confirmButton = registerButtonElement(finder: _confirmButtonFinder);
    input = registerTextElement(finder: _inputFinder);
  }

  Future<void> checkPage() async {
    confirmButton.isReady();
    input.isReady();
  }
}
