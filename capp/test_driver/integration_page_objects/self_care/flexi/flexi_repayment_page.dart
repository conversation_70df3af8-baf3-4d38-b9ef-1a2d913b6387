import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class FlexiRepaymentPage extends TestPageWithAppBar {
  final _buttonPayNowFinder = find.byKey(const Key('__flexiPayNowButton__'));

  late TestElement payButton;

  FlexiRepaymentPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__flexiRepayScreen__')),
          appBarFinder: find.byKey(const Key('__flexiRepayAppBar__')),
        ) {
    payButton = registerButtonElement(finder: _buttonPayNowFinder);
  }

  Future<void> checkPage() async {
    payButton.isReady();
  }
}
