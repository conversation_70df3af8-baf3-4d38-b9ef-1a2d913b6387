import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class FlexiTransactionsPage extends TestPageWithAppBar {
  late TestElement firstTransaction;
  FlexiTransactionsPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__flexiTransactionsScreen__')),
          appBarFinder: find.byKey(const Key('__flexiTransactionsScreenAppBar__')),
        );

  Future<void> checkPage() async {
    final transactions = find.byKey(const Key('__FlexiTransactionsListItem__'));
    firstTransaction = registerButtonElement(finder: transactions.first);
  }
}
