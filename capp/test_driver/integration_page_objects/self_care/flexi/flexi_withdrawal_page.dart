import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_app_bar.dart';

class FlexiWithdrawalPage extends TestPageWithAppBar {
  final _continueButtonNowFinder = find.byKey(const Key('__flexiWithdrawalContinue__'));
  final _rbiButtonNowFinder = find.byKey(const Key('__flexiWithdrawalRbi__'));
  final _sliderFinder = find.byKey(const Key('__flexiWithdrawalSlider__'));
  final _inputTextFinder = find.byKey(const Key('__dynamicCalculationSliderCurrencyTextField_flexiWithdrawal__'));

  late TestElement continueButton;
  late TestElement rbiButton;
  late TestElement slider;
  late TestElement input;

  FlexiWithdrawalPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__flexiWithdrawalScreen__')),
          appBarFinder: find.byKey(const Key('__flexiWithdrawalScreenAppBar__')),
        ) {
    continueButton = registerButtonElement(finder: _continueButtonNowFinder);
    rbiButton = registerButtonElement(finder: _rbiButtonNowFinder);
    slider = registerElement(finder: _sliderFinder);
    input = registerTextElement(finder: _inputTextFinder);
  }

  Future<void> checkPage() async {
    continueButton.isReady();
    rbiButton.isReady();
    slider.isReady();
    input.isReady();
  }
}
