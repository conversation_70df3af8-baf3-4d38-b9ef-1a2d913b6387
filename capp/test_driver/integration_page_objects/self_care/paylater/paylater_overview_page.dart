import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class PaylaterOverviewPage extends TestPage {
  late final Finder _actionDocumentsFinder;
  late final Finder _actionBillsFinder;
  late final Finder _actionTransactionsFinder;

  late final TestElement actionDocuments;
  late final TestElement actionBills;
  late final TestElement actionTransactions;

  PaylaterOverviewPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__paylaterOverviewScreen__')),
        ) {
    _actionDocumentsFinder = find.byKey(const Key('__paylater_action_documents__'));
    actionDocuments = registerButtonElement(finder: _actionDocumentsFinder);
    _actionBillsFinder = find.byKey(const Key('__paylater_action_bills__'));
    actionBills = registerButtonElement(finder: _actionBillsFinder);
    _actionTransactionsFinder = find.byKey(const Key('__paylater_action_transactions__'));
    actionTransactions = registerButtonElement(finder: _actionTransactionsFinder);
  }

  Future<void> checkPage() async {
    actionDocuments.isReady();
    actionBills.isReady();
    actionTransactions.isReady();
  }
}
