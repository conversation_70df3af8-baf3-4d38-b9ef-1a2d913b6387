import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class MarialStatusPage extends TestPage {
  late TestElement _maritalStatusButton;
  late TestElement _numberDependantsInput;
  late TestElement _singleButton;
  late TestElement _marriedButton;
  late TestElement _liveInPartnerButton;
  late TestElement _separatedButton;
  late TestElement _divorcedButton;
  late TestElement _widowedButton;
  late TestElement _secondaryPhoneNumberInput;
  late TestElement _continueButton;

  MarialStatusPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__maritalStatusPage__'))) {
    _maritalStatusButton = registerElement(
      finder: find.byKey(const Key('__maritalStatusButtonKey__')),
      type: TestElementType.button,
    );
    _numberDependantsInput = registerElement(
      finder: find.byKey(const Key('__numberDependantsInput__')),
      type: TestElementType.textfield,
    );
    _singleButton = registerButtonElement(
      finder: find.byKey(
        const Key('__item-single__'),
      ),
    );
    _marriedButton = registerButtonElement(
      finder: find.byKey(
        const Key('__item-married__'),
      ),
    );
    _liveInPartnerButton = registerButtonElement(
      finder: find.byKey(
        const Key('__item-liveInPartner__'),
      ),
    );
    _separatedButton = registerButtonElement(
      finder: find.byKey(
        const Key('__item-separated__'),
      ),
    );
    _divorcedButton = registerButtonElement(
      finder: find.byKey(
        const Key('__item-divorced__'),
      ),
    );
    _widowedButton = registerButtonElement(
      finder: find.byKey(
        const Key('__item-widowed__'),
      ),
    );
    _secondaryPhoneNumberInput = registerElement(
      finder: find.byKey(const Key('__secondaryPhoneNumberInput__')),
      type: TestElementType.textfield,
    );
    _continueButton = registerButtonElement(
      finder: find.byKey(
        const Key('__continueButton__'),
      ),
    );
  }

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  void verifyAbsent() {
    expect(screenFinder, findsNothing);
  }

  Future<void> scrollToMarialStatus() async {
    await tester.dragUntilVisible(
      _maritalStatusButton.finder,
      screenFinder.first,
      const Offset(0, -100),
    );
  }

  Future<void> tapOnMarialStatus() async {
    await _maritalStatusButton.tap();
    await tester.pumpAndSettle();
  }

  Future<void> selectSingle() async {
    await _singleButton.tap();
  }

  Future<void> selectMarried() async {
    await _marriedButton.tap();
  }

  Future<void> selectLiveInPartner() async {
    await _liveInPartnerButton.tap();
  }

  Future<void> selectSeparated() async {
    await _separatedButton.tap();
  }

  Future<void> selectDivorced() async {
    await _divorcedButton.tap();
  }

  Future<void> selectWidowed() async {
    await _widowedButton.tap();
  }

  Future<void> enterNumberDependants(String value) async {
    await _numberDependantsInput.enterText(value);
  }

  Future<void> enterSecondaryPhoneNumber(String value) async {
    await _secondaryPhoneNumberInput.enterText(value);
  }

  Future<void> tapContinue() async {
    await _continueButton.tap();
  }
}
