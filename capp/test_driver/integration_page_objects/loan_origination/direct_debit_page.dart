import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class DirectDebitDetailsPage extends TestPage {
  late TestElement continueButton;

  DirectDebitDetailsPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__page_direct_debit_details__'), skipOffstage: false),
        ) {
    continueButton = registerButtonElement(finder: find.byKey(const Key('__continueButton__')));
  }

  void verifyReady() {
    screen.isReady();
  }

  Future<void> tapOnContinue() async {
    await continueButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }
}

class DirectDebitAccountDetailPage extends TestPage {
  late TestElement editDetailsButton;
  late TestElement giveConsentCheckbox;
  late TestElement continueButton;
  late TestElement backButton;

  DirectDebitAccountDetailPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__page_direct_debit_account_details__'), skipOffstage: false),
        ) {
    editDetailsButton = registerButtonElement(finder: find.byKey(const Key('__editDetailsButtonDDM__')));
    giveConsentCheckbox = registerButtonElement(finder: find.byKey(const Key('__consentToDDM__')));
    continueButton = registerButtonElement(finder: find.byKey(const Key('__continueButton__')));
    backButton = registerButtonElement(finder: find.byKey(const Key('__backButton__')));
  }

  void verifyReady() {
    screen.isReady();
  }

  Future<void> tapOnCheckbox() async {
    await giveConsentCheckbox.tap();
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> tapOnContinue() async {
    await continueButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> tapOnEdit() async {
    await editDetailsButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> tapOnBackButton() async {
    await backButton.tap();
    await tester.pump();
  }
}

class DirectDebitAddUpdateDetailsPage extends TestPage {
  late TestElement bankNameTextField;
  late TestElement accountHolderTextField;
  late TestElement accountNumberTextField;
  late TestElement reAccountNumberTextField;
  late TestElement ifscCodeTextField;
  late TestElement findIfscCodeTextButton;
  late TestElement giveConsentCheckbox;
  late TestElement continueButton;
  late TestElement backButton;
  late TestElement bankIfscSearchScreen;
  final Finder _scrollableFinder = find.byKey(const Key('__continueButton__'));

  DirectDebitAddUpdateDetailsPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__page_direct_debit_add_update_details__'), skipOffstage: false),
        ) {
    bankNameTextField =
        registerElement(finder: find.byKey(const Key('__textInput_bankName__')), type: TestElementType.textfield);
    accountHolderTextField = registerElement(
      finder: find.byKey(const Key('__textInput_accountHolderName__')),
      type: TestElementType.textfield,
    );
    accountNumberTextField =
        registerElement(finder: find.byKey(const Key('__textInput_accountNumber__')), type: TestElementType.textfield);
    reAccountNumberTextField = registerElement(
      finder: find.byKey(const Key('__textInput_reAccountNumber__')),
      type: TestElementType.textfield,
    );
    ifscCodeTextField =
        registerElement(finder: find.byKey(const Key('__textInput_IfscCode__')), type: TestElementType.textfield);
    findIfscCodeTextButton =
        registerElement(finder: find.byKey(const Key('__findIfscCode__')), type: TestElementType.text);
    giveConsentCheckbox = registerButtonElement(finder: find.byKey(const Key('__consentToDDM__')));
    continueButton = registerButtonElement(finder: find.byKey(const Key('__continueButton__')));
    backButton = registerButtonElement(finder: find.byKey(const Key('__backButton__')));
    bankIfscSearchScreen = registerElement(finder: find.byKey(const Key('__BankIfscSearchResultScreen__')));
  }

  Future<void> fillBankDetails() async {
    await accountHolderTextField.enterText('Manish Verma');
    await accountNumberTextField.enterText('**********');
    await reAccountNumberTextField.enterText('**********');
    await ifscCodeTextField.enterText('HDFC0009944');
    await tester.pump(const Duration(seconds: 2));
  }

  void verifyReady() {
    screen.isReady();
  }

  Future<void> tapOnCheckbox() async {
    await tester.dragUntilVisible(
      giveConsentCheckbox.finder,
      _scrollableFinder,
      const Offset(0, -200),
      maxIteration: 10,
    );
    await tester.pump(const Duration(seconds: 2));
    await giveConsentCheckbox.tap();
  }

  Future<void> tapOnContinue() async {
    await continueButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> tapOnBackButton() async {
    await backButton.tap();
    await tester.pump();
  }

  Future<void> tapOnFindIfscText() async {
    await findIfscCodeTextButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }
}

class DirectDebitFailurePage extends TestPage {
  late TestElement retryButton;
  late TestElement homeButton;

  DirectDebitFailurePage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__page_direct_debit_failure__'), skipOffstage: false),
        ) {
    retryButton = registerButtonElement(finder: find.byKey(const Key('__retryButton__')));
    homeButton = registerButtonElement(finder: find.byKey(const Key('__homeButton__')));
  }
}

class SearchIfscPage extends TestPage {
  late TestElement stateTextField;
  late TestElement cityTextField;
  late TestElement branchTextField;
  late TestElement backButton;
  late TestElement confirmButton;

  SearchIfscPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__page_search_ifsc_code_screen__'), skipOffstage: false),
        ) {
    stateTextField = registerButtonElement(finder: find.byKey(const Key('__ifsc_state_texfield__')));
    cityTextField = registerButtonElement(finder: find.byKey(const Key('__ifsc_city_texfield__')));
    branchTextField = registerButtonElement(finder: find.byKey(const Key('__ifsc_branch_texfield__')));
    confirmButton = registerButtonElement(finder: find.byKey(const Key('__ifsc_confirm_button__')));
  }

  Future<void> tapOnStateTextField() async {
    await stateTextField.tap();
    await tester.pump();
    await tester.tap(find.text('Haryana'));
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> tapOnCityTextField() async {
    await cityTextField.tap();
    await tester.pump();
    await tester.tap(find.text('Varanasi'));
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> tapOnBranchTextField() async {
    await branchTextField.tap();
    await tester.pump();
    await tester.tap(find.text('FAIZABAD - CIVIL LINES UTTAR PRADESH').last);
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> tapOnConfirmButton() async {
    await confirmButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }
}
