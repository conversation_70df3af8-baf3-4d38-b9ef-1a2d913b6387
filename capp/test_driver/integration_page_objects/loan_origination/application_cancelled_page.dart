import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class ApplicationCancelledPage extends TestPage {
  late TestElement reapplyButton;
  late TestElement goToHomeButton;

  ApplicationCancelledPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__page_offerCancelledId}__'))) {
    //<<<<<<
    reapplyButton = registerButtonElement(finder: find.byKey(const Key('__retryWidgetPrimaryButton__')));
    goToHomeButton = registerButtonElement(finder: find.byKey(const Key('__goToHomePageButton__')));
  }

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  Future<void> tapOnReapply() async {
    await reapplyButton.tap();
    await Future.delayed(const Duration(seconds: 5), tester.pumpAndSettle);
  }

  Future<void> tapOnGoToHome() async {
    await goToHomeButton.tap();
    await Future.delayed(const Duration(seconds: 5), tester.pumpAndSettle);
  }
}
