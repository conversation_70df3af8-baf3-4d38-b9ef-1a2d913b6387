import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class IncomeInformationPage extends TestPage {
  late TestElement monthlyIncomeTextField;
  late TestElement studentIncomeInformation;
  late TestElement houseMakerIncomeInformation;
  late TestElement selfEmpoyedIncomeInformation;
  late TestElement salariedGovernmentIncomeInformation;
  late TestElement salariedOtherIncomeInformation;
  late TestElement unemployedIncomeInformation;
  late TestElement retiredPensionerIncomeInformation;
  late TestElement retiredNoPensionIncomeInformation;
  late TestElement companyNameTextField;
  late TestElement selectIndustry;
  late TestElement jobStartingFromSalaryOther;
  late TestElement jobStartingFromSalaryGovernment;
  late TestElement calendarDialogTextField;
  late TestElement continueButton;
  late TestElement backButton;

  IncomeInformationPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__page_incomeFormId}__'))) {
    //<<<<<<
    monthlyIncomeTextField = registerElement(
      finder: find.byKey(const Key('__dynamicCalculationSliderCurrencyTextField_null__')),
      type: TestElementType.textfield,
    );
    studentIncomeInformation =
        registerElement(finder: find.byKey(const Key('_categoryItemSTUDENT')), type: TestElementType.button);
    houseMakerIncomeInformation =
        registerButtonElement(finder: find.byKey(const Key('_categoryItemPERSON_IN_HOUSEHOLD')));
    selfEmpoyedIncomeInformation = registerButtonElement(finder: find.byKey(const Key('_categoryItemSELFEMPOYED')));
    salariedGovernmentIncomeInformation =
        registerButtonElement(finder: find.byKey(const Key('_categoryItemSALARIED_GOVT')));
    salariedOtherIncomeInformation =
        registerButtonElement(finder: find.byKey(const Key('_categoryItemSALARIED_OTHER')));
    unemployedIncomeInformation = registerButtonElement(finder: find.byKey(const Key('_categoryItemUNEMPLOYED')));
    retiredPensionerIncomeInformation =
        registerButtonElement(finder: find.byKey(const Key('_categoryItemRETIRED_PENSIONER')));
    retiredNoPensionIncomeInformation =
        registerButtonElement(finder: find.byKey(const Key('_categoryItemRETIRED_NOPENSION')));

    companyNameTextField = registerElement(
      finder: find.byKey(const Key('__dynamicTextInput_companyInfoNameId__')),
      type: TestElementType.textfield,
    );

    selectIndustry =
        registerButtonElement(finder: find.byKey(const Key('__selectInputDropdownButton_companyInfoIndustryId__')));

    jobStartingFromSalaryOther =
        registerButtonElement(finder: find.byKey(const Key('calendar_icon_key')));
    jobStartingFromSalaryGovernment =
        registerButtonElement(finder: find.byKey(const Key('calendar_icon_key')));
    calendarDialogTextField =
        registerElement(finder: find.byType(InputDatePickerFormField), type: TestElementType.textfield);

    backButton = registerButtonElement(finder: find.byKey(const Key('__avatarGoBackButton__')));
    continueButton =
        registerElement(finder: find.byKey(const Key('__avatarGoNextButton__')), type: TestElementType.button);
  }

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  void verifyAbsent() {
    expect(screenFinder, findsNothing);
  }

  Future<void> tapOnSelfEmpoyed() async {
    await selfEmpoyedIncomeInformation.tap();
    await tester.pumpAndSettle();
  }

  Future<void> tapOnSalaryGovernment() async {
    await salariedGovernmentIncomeInformation.tap();
    await tester.pumpAndSettle();
  }

  Future<void> tapOnSalaryOther() async {
    await salariedOtherIncomeInformation.tap();
    await tester.pumpAndSettle();
  }

  Future<void> setMonthlyIncome(String value) async {
    await monthlyIncomeTextField.enterText(value);
    await Future.delayed(const Duration(seconds: 1), tester.pumpAndSettle);
  }

  Future<void> fillSalaryOther(String companyName, String date) async {
    await companyNameTextField.scrollTo();
    await companyNameTextField.enterText(companyName);
    await selectIndustry.scrollTo();
    await selectIndustry.tap();
    await tester.tap(find.byType(InkWell).first);
    await jobStartingFromSalaryOther.scrollTo();
    await jobStartingFromSalaryOther.tap();
    await calendarDialogTextField.enterText(date);
    await tester.testTextInput.receiveAction(TextInputAction.done);
    await tester.pumpAndSettle(const Duration(seconds: 2));
    await tester.tap(find.text('Confirm'));

    await tester.pumpAndSettle();
  }

  Future<void> fillSalaryGovernment(String date) async {
    await jobStartingFromSalaryGovernment.scrollTo();
    await jobStartingFromSalaryGovernment.tap();
    await calendarDialogTextField.enterText(date);
    await tester.testTextInput.receiveAction(TextInputAction.done);
    await tester.pumpAndSettle(const Duration(seconds: 2));
    await tester.tap(find.text('Confirm'));

    await tester.pumpAndSettle();
  }

  Future<void> tapContinueButton() async {
    await continueButton.tap();
    await Future.delayed(const Duration(seconds: 5), tester.pumpAndSettle);
  }
}
