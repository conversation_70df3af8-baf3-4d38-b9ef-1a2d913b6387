import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class ProductInformationPage extends TestPage {
  late TestElement backButton;

  ProductInformationPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__introduction_story_screen__'))) {
    backButton = registerElement(
      finder: find.byKey(const Key('__intorduction_story_back_button__')),
      type: TestElementType.button,
    );
  }

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  void verifyAbsent() {
    expect(screenFinder, findsNothing);
  }

  Future<void> goBack() async {
    await backButton.tap();
    await tester.pumpAndSettle();
  }
}
