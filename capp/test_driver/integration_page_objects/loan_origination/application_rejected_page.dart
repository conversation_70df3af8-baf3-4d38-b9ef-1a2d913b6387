import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class ApplicationRejectedPage extends TestPage {
  late TestElement goToHomeButton;

  ApplicationRejectedPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__dynamicErrorScreen__'))) {
    //<<<<<<
    goToHomeButton = registerButtonElement(finder: find.byKey(const Key('__dynamicErrorButton__')));
  }

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  Future<void> tapOnGoToHome() async {
    await goToHomeButton.tap();
    await Future.delayed(const Duration(seconds: 5), tester.pumpAndSettle);
  }
}
