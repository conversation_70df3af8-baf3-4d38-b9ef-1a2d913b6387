import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class ApplicationCompletedPage extends TestPage {
  late TestElement setUpDirectDebitButton;

  ApplicationCompletedPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__page_loanCompletionFormId}__'))) {
    //<<<<<<
    setUpDirectDebitButton =
        registerElement(finder: find.byKey(const Key('__avatarGoNextButton__')), type: TestElementType.button);
  }

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  Future<void> tapOnSetUpDirectDebit() async {
    await setUpDirectDebitButton.tap();
    await Future.delayed(const Duration(seconds: 5), tester.pumpAndSettle);
  }
}
