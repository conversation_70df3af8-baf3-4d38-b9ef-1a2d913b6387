import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../base/test_page_with_app_bar.dart';

class ContactUsPage extends TestPageWithAppBar {
  final Finder _websiteItem = find.byKey(const Key('__contactUsItem__CommunicationChannelType.websiteChannel__'));
  final Finder _chatbotButton = find.byKey(const Key('__chatbotButton__'));
  final Finder _grievanceButton = find.byKey(const Key('__grievanceButton__'));
  final Finder _helpCenterCard = find.byKey(const Key('__helpCenterCard__'));

  ContactUsPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__contactUsScreen__')));

  Future<void> tapOnGrievanceButton() async {
    final button = registerElement(finder: _grievanceButton)..isReady();
    await button.tap();
    await tester.pumpAndSettle();
  }

  void isWebsiteItemReady() => expect(_websiteItem, findsOneWidget);
  void isChatbotButtonReady() => expect(_chatbotButton, findsOneWidget);
  void isChatbotButtonAbsent() => expect(_chatbotButton, findsNothing);
  void isGrievanceButtonReady() => expect(_grievanceButton, findsOneWidget);
  void isGrievanceButtonAbsent() => expect(_grievanceButton, findsNothing);
  void isHelpCenterCardReady() => expect(_helpCenterCard, findsOneWidget);
  void isHelpCenterCardAbsent() => expect(_helpCenterCard, findsNothing);
}
