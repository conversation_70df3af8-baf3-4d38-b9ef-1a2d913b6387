import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_phone_input.dart';

class PinRecoveryPhonePage extends TestPageWithPhoneInput {
  late TestElement continueButton;

  PinRecoveryPhonePage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__pinRecoveryPhoneScreen__')),
          appBarFinder: find.descendant(
            of: find.byKey(const Key('__pinRecoveryPhoneScreen__')),
            matching: find.byType(KoyalAppBar),
          ),
        ) {
    continueButton = registerButtonElement(finder: find.byKey(const Key('__pinRecoveryContinueButton__')));
  }
}
