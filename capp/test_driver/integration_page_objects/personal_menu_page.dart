import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../integration_reuse_steps/common.dart';
import 'base/test_page_with_app_bar.dart';

class PersonalMenuPage extends TestPageWithAppBar {
  final Finder _listView = find.byType(ListView);

  final Finder _inboxRow = find.byKey(const Key('__inboxRow__'));
  final Finder _editProfileRow = find.byKey(const Key('__editProfileRow__'));
  final Finder _editDocumentsRow = find.byKey(const Key('__editDocumentsRow__'));
  final Finder _faqRow = find.byKey(const Key('__faqRow__'));
  final Finder _rateAppRow = find.byKey(const Key('__rateAppRow__'));
  final Finder _changeLanguageRow = find.byKey(const Key('__selectLanguageRow__'));
  final Finder _changePasswordRow = find.byKey(const Key('__changePasswordRow__'));

  final Finder _devMenuRow = find.text('Development');
  final Finder _demoMenuRow = find.text('Demo');
  final Finder _notificationsDemoRow = find.byKey(const Key('__notificationsDemoRow__'));
  final Finder _featureFlagsDemoRow = find.byKey(const Key('__featureFlagsScreen__'));
  final Finder _selfieDriver = find.byKey(const Key('__selfie_voucher__'));

  late TestElement logoutRow;
  late TestElement loginRow;

  PersonalMenuPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__personalMenuScreen__')),
          appBarFinder: find.byKey(const Key('__personalMenuScreenAppBar__')),
        ) {
    logoutRow = registerElement(finder: find.byKey(const Key('__logoutRow__')));
    loginRow = registerElement(finder: find.byKey(const Key('__loginRow__')));
  }

  void verifyReady() {
    screen.isReady();
  }

  void isInboxRowReady() {
    expect(_inboxRow, findsOneWidget);
  }

  Future<void> tapInboxRow() async {
    await tester.tap(_inboxRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapEditProfileRow() async {
    await tester.tap(_editProfileRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapEditDocumentsRow() async {
    await tester.tap(_editDocumentsRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapFaqRow() async {
    await tester.tap(_faqRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapRateAppRow() async {
    await tester.tap(_rateAppRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapChangeLanguageRow() async {
    await tester.tap(_changeLanguageRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapChangePasswordRow() async {
    await tester.tap(_changePasswordRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapDevMenuRow() async {
    await scrollToDevelopmentRow();
    await tester.tap(_devMenuRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapDemoMenuRow() async {
    //_scrollToItem(_demoMenuRow);
    await tester.tap(_demoMenuRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapBackButton() async {
    await tester.tap(_devMenuRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapSelfieRow() async {
    //_scrollToItem(_selfieDriver);
    await scrollToSelfieRow();
    await tester.tap(_selfieDriver);
    await tester.pumpAndSettle();
  }

  Future<void> scrollToNotificationDemoRow({
    bool reverse = false,
  }) async {
    await tester.dragUntilVisible(_notificationsDemoRow, _listView, Offset(0, reverse ? 200.0 : -200.0));
  }

  Future<void> scrollToDevelopmentRow({
    bool reverse = false,
  }) async {
    await tester.dragUntilVisible(_devMenuRow, _listView, Offset(0, reverse ? 200.0 : -200.0));
  }

  Future<void> scrollToSelfieRow({
    bool reverse = false,
  }) async {
    await tester.dragUntilVisible(_selfieDriver, _listView, Offset(0, reverse ? 200.0 : -200.0));
  }

  Future<void> tapNotificationDemoRow() async {
    await tester.tap(_notificationsDemoRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapFeatureFlagsRow() async {
    await tester.tap(_featureFlagsDemoRow);
    await tester.pumpAndSettle();
  }

  Future<void> tapBackToHome() async {
    await tester.tap(appBarBackButton.finder);
    return pumpHomeFrames(tester);
  }
/*
  Future<void> superCupoItemNavigateBack(
      {String appBarName, Duration? timeout}) {
    await tester.tap(personalMenuBackButtonFinder(appBarName));
  }
*/
}
