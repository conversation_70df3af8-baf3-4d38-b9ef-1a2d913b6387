import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_app_bar.dart';

class InstallmentPlanPage extends TestPageWithAppBar {
  final __installmentPlanScreenContainerFinder = find.byKey(const Key('__installmentPlanScreenContainer__'));
  final __installmentPlanProductTitleFinder = find.byKey(const Key('__productSmallCardTitle__'));
  final __installmentPlanProductFlipKartTitleFinder = find.byKey(const Key('__productSmallCardFlipKartTitle__'));
  final __installmentPlanContinueButtonFinder = find.byKey(const Key('__installmentPlanScreenContinueButton__'));
  final __installmentPlanInstallmentInfoCardFinder = find.byKey(const Key('__installmentPlanInstallmentInfoCard__'));
  final __installmentInfoCardMonthlyInstallmentFinder =
      find.byKey(const Key('__installmentInfoCardMonthlyInstallment__'));
  final __installmentInfoCardDownpaymentFinder = find.byKey(const Key('__installmentInfoCardDownpayment__'));
  final __installmentInfoCardLimitFinder = find.byKey(const Key('__installmentInfoCardLimit__'));
  final __installmentInfoCardLoanAmountFinder = find.byKey(const Key('__installmentInfoCardLoanAmount__'));
  final __installmentInfoCardInterestRateFinder = find.byKey(const Key('__installmentInfoCardInterestRate__'));
  final __installmentPlanScreenMonth0 = find.byKey(const Key('__installmentPlanScreenMonth-0__'));

  late TestElement installmentPlanScreenContainer;
  late TestElement installmentPlanInstallmentInfoCard;
  late TestElement installmentPlanProductTitle;
  late TestElement installmentPlanProductFlipKartTitle;
  late TestElement continueButton;
  late TestElement installmentInfoCardMonthlyInstallment;
  late TestElement installmentInfoCardDownpayment;
  late TestElement installmentInfoCardLimit;
  late TestElement installmentInfoCardLoanAmount;
  late TestElement installmentInfoCardInterestRate;
  late TestElement installmentPlanScreenMonth0;

  InstallmentPlanPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__installmentPlanScreen__')),
          appBarFinder: find.byKey(const Key('__installmentPlanScreenAppBar__')),
        ) {
    installmentPlanScreenContainer = registerElement(finder: __installmentPlanScreenContainerFinder);
    installmentPlanProductTitle = registerTextElement(finder: __installmentPlanProductTitleFinder);
    installmentPlanProductFlipKartTitle = registerTextElement(finder: __installmentPlanProductFlipKartTitleFinder);
    installmentPlanInstallmentInfoCard = registerElement(finder: __installmentPlanInstallmentInfoCardFinder);
    installmentInfoCardMonthlyInstallment = registerElement(finder: __installmentInfoCardMonthlyInstallmentFinder);
    installmentInfoCardDownpayment = registerElement(finder: __installmentInfoCardDownpaymentFinder);
    installmentInfoCardLimit = registerElement(finder: __installmentInfoCardLimitFinder);
    installmentInfoCardLoanAmount = registerElement(finder: __installmentInfoCardLoanAmountFinder);
    installmentInfoCardInterestRate = registerElement(finder: __installmentInfoCardInterestRateFinder);
    installmentPlanScreenMonth0 = registerElement(finder: __installmentPlanScreenMonth0);
    continueButton = registerButtonElement(finder: __installmentPlanContinueButtonFinder);
  }

  void checkProduct({bool? buyOnline, String? productName}) {
    if (productName != null) {
      if (buyOnline == true) {
        installmentPlanProductFlipKartTitle.textEquals(productName);
      } else {
        installmentPlanProductTitle.textEquals(productName);
      }
    }
  }

  void checkInstallmentInfoCard({bool? buyOnline}) {
    installmentPlanInstallmentInfoCard.isReady();
    installmentInfoCardMonthlyInstallment.isReady();
    installmentInfoCardLimit.isReady();
    installmentInfoCardLoanAmount.isReady();
    installmentInfoCardInterestRate.isReady();
    if (buyOnline == false) {
      installmentInfoCardDownpayment.isReady();
    } else {
      installmentInfoCardDownpayment.isAbsent();
    }
  }
}
