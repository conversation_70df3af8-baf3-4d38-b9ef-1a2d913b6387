import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../integration_tests_tools/tester_extension.dart';

class AddressValidationPage extends TestPage {
  final _aadhaarXmlOptionsFinder = find.text('Aadhaar XML');
  final _aadhaarXmlDropdownCheckboxFinder = find.byType(KoyalCheckbox);
  final _aadhaarXmlAttachmentInputFinder = find.byKey(const Key('__aadhaarFileAttachment__'));
  final _onlineKycFinder = find.text('Online KYC with Aadhaar');
  final _addressValidationConfirmButtonFinder = find.byKey(const Key('__addressValidationConfirmButton__'));

  late TestElement _aadhaarXmlDropdownCheckbox;
  late TestElement _aadhaarXmlAttachmentInput;
  late TestElement _aadhaarXmlOptions;
  late TestElement _onlineKyc;
  late TestElement _addressValidationConfirmButton;
  late NavigatorState navigator;

  AddressValidationPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__addressValidationScaffold__')),
        ) {
    _aadhaarXmlDropdownCheckbox = registerElement(finder: _aadhaarXmlDropdownCheckboxFinder);
    _aadhaarXmlAttachmentInput = registerElement(finder: _aadhaarXmlAttachmentInputFinder);
    _aadhaarXmlOptions = registerElement(finder: _aadhaarXmlOptionsFinder);
    _onlineKyc = registerElement(finder: _onlineKycFinder);
    _addressValidationConfirmButton = registerElement(finder: _addressValidationConfirmButtonFinder);
    navigator = tester.state(find.byType(Navigator));
  }

  Future<void> launchOnlineKyc() async {
    await selectOnlineKycOption();
    await checkAadhaarXmlCheckbox();
    await tester.delay();
    _addressValidationConfirmButton.isReady();
  }

  Future<void> aadhaarXml() async {
    await selectAadhaarXmlOption();
    await checkAadhaarXmlCheckbox();
    await tapAadhaarXmlAttachmentInput();
  }

  Future<void> selectAadhaarXmlOption() async {
    await _aadhaarXmlOptions.scrollTo();
    await _aadhaarXmlOptions.tap();
  }

  Future<void> selectOnlineKycOption() async {
    await tester.pumpAndSettle(const Duration(seconds: 2));
    _onlineKyc.isReady();
    await _onlineKyc.tap();
  }

  Future<void> checkAadhaarXmlCheckbox() async {
    await screen.swipeUp();
    await _aadhaarXmlDropdownCheckbox.scrollTo();
    await _aadhaarXmlDropdownCheckbox.tap();
    await tester.pumpAndSettle(const Duration(seconds: 2));
  }

  Future<void> tapAadhaarXmlAttachmentInput() async {
    await screen.swipeUp();
    await _aadhaarXmlAttachmentInput.scrollTo();
    await tester.pumpAndSettle(const Duration(seconds: 2));
    await _aadhaarXmlAttachmentInput.tap();
    await tester.delay();
  }
}
