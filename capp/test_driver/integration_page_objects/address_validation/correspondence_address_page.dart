import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class CorrespondenceAddressPage extends TestPage {
  late TestElement _drivingLicenseOption;
  late TestElement _photoAttachmentInput;
  late TestElement _confirmButton;

  CorrespondenceAddressPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__correspondenceAddressScaffold__'))) {
    _drivingLicenseOption = registerElement(finder: find.byKey(const Key('__correspondenceSelectedDocument__')));
    _photoAttachmentInput = registerElement(finder: find.byKey(const Key('__correspondenceAddressUploadPhoto__')));
    _confirmButton = registerElement(finder: find.byKey(const Key('__correspondenceAddressFlowConfirmButton__')));
  }

  void verifyReady() {
    screen.isReady();
  }

  Future<void> tapDrivingLicense() async {
    await _drivingLicenseOption.tap();
    _photoAttachmentInput.isReady();
  }

  Future<void> tapUploadPhoto() async {
    await tester.pumpAndSettle(const Duration(seconds: 4));
    await _photoAttachmentInput.tap();
  }

  Future<void> tapConfirmButton() async {
    await tester.pumpAndSettle(const Duration(seconds: 2));
    await _confirmButton.tap();
  }
}
