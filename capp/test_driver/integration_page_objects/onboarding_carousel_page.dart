import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class OnboardingCarouselPage extends TestPage {
  late TestElement slidePersonalizedOffers;
  late TestElement slidePurchasingPower;
  late TestElement slideAchievements;

  late TestElement createAccountButton;

  late TestElement continueAsGuestButton;

  OnboardingCarouselPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__onboardingCarouselScreen__'))) {
    slidePersonalizedOffers = registerElement(finder: find.byKey(const Key('__onboardingPersonalisedOffers__')));
    slidePurchasingPower = registerElement(finder: find.byKey(const Key('__onboardingPurchasingPower__')));
    slideAchievements = registerElement(finder: find.byKey(const Key('__onboardingAchievemnts__')));
    createAccountButton = registerButtonElement(finder: find.byKey(const Key('__createAccountButton__')));
    continueAsGuestButton = registerButtonElement(finder: find.byKey(const Key('__continueAsGuestButton__')));
  }

  void verifyReady() {
    isReady();
  }

  Future<void> swipeLeftPersonlizedOffers() async {
    final gesture = await tester.startGesture(const Offset(0, 300));
    await gesture.moveBy(const Offset(-600, 300)); //How much to scroll by
    await tester.pumpAndSettle();
    slidePersonalizedOffers.isReady();
  }

  Future<void> swipeLeftPurchasingPower() async {
    final gesture = await tester.startGesture(const Offset(0, 300));
    await gesture.moveBy(const Offset(-600, 300)); //How much to scroll by
    await tester.pumpAndSettle();
    slidePurchasingPower.isReady();
  }

  Future<void> swipeLeftAchievemmnts() async {
    final gesture = await tester.startGesture(const Offset(0, 300));
    await gesture.moveBy(const Offset(-600, 300)); //How much to scroll by
    await tester.pumpAndSettle();
    slideAchievements.isReady();
  }

  Future<void> tapContinueAsGuestButton() async {
    await continueAsGuestButton.tap();
    await tester.pumpAndSettle();
  }
}
