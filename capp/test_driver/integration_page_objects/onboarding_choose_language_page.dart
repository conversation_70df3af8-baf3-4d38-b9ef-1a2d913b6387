import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

class OnboardingChooseLanguagePage {
  final WidgetTester tester;
  OnboardingChooseLanguagePage(this.tester);

  final Finder _onboardingChooseLanguageScreen = find.byKey(const Key('__onboardingChooseLanguageScreen__'));
  final Finder _chooseEnglishButton = find.byKey(const Key('__chooseEnglishButton__'));
  final Finder _chooseHindiButton = find.byKey(const Key('__chooseHindiButton__'));
  final Finder _chooseVietnameseButton = find.byKey(const Key('__chooseVnButton__'));
  final Finder _chooseIndonesianButton = find.byKey(const Key('__chooseIdButton__'));
  final Finder _chooseTagalogButton = find.byKey(const Key('__chooseTlButton__'));
  final Finder _chooseLanguageConfirmButton = find.byKey(const Key('__chooseLanguageConfirmButton__'));

  void verifyReady() {
    expect(_onboardingChooseLanguageScreen, findsOneWidget);
  }

  Future<void> chooseEnglish() async {
    await tester.tap(_chooseEnglishButton);
    await tester.pumpAndSettle();
  }

  Future chooseVietnamese() async {
    await tester.tap(_chooseVietnameseButton);
    await tester.pumpAndSettle();
  }

  Future<void> chooseHindi() async {
    await tester.tap(_chooseHindiButton);
    await tester.pumpAndSettle();
  }

  Future chooseIndonesian() async {
    await tester.tap(_chooseIndonesianButton);
    await tester.pumpAndSettle();
  }

  Future<void> chooseTagalog() async {
    await tester.tap(_chooseTagalogButton);
    await tester.pumpAndSettle();
  }

  Future<void> confirmLanguage() async {
    await tester.tap(_chooseLanguageConfirmButton);
    await tester.pumpAndSettle();
  }
}
