import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_app_bar.dart';

class UnblockAccountUsernamePage extends TestPageWithAppBar {
  late TestElement continueButton;
  late TestElement usernameInputField;

  UnblockAccountUsernamePage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__unblockAccountUsernameScreen__')),
          appBarFinder: find.descendant(
            of: find.byKey(const Key('__unblockAccountUsernameScreen__')),
            matching: find.byType(KoyalAppBar),
          ),
        ) {
    continueButton = registerButtonElement(finder: find.byKey(const Key('__unblockAccountContinueButton__')));
    usernameInputField = registerTextFieldElement(finder: find.by<PERSON>ey(const Key('__unblockAccountUsernameInput__')));
  }
}
