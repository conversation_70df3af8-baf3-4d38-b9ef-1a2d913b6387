import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class PasswordPage extends TestPage {
  late TestElement passwordInput;
  late TestElement loginButton;
  late TestElement countdownLock;
  late TestElement forgotPasswordButton;
  late TestElement simpleAppBarBackButton;

  late TestElement temporarilyBlockedDialog;
  late TestElement temporarilyBlockedDialogButton;

  late TestElement loginToBlockedAccountDialog;
  late TestElement loginToBlockedAccountDialogButton;

  late TestElement permanentlyBlockedDialog;
  late TestElement permanentlyBlockedDialogButton;

  PasswordPage(WidgetTester tester) : super(tester: tester, screenFinder: find.byKey(const Key('__passwordScreen__'))) {
    passwordInput = registerTextFieldElement(finder: find.byKey(const Key('__passwordScreenPasswordInput__')));
    loginButton = registerButtonElement(finder: find.byKey(const Key('__passwordScreenLoginButton__')));
    countdownLock = registerElement(finder: find.byKey(const Key('__countdownLock__')));
    forgotPasswordButton = registerButtonElement(finder: find.byKey(const Key('__forgotPasswordButton__')));
    simpleAppBarBackButton = registerElement(
      finder: find.descendant(
        of: find.descendant(of: find.byKey(const Key('__passwordScreen__')), matching: find.byType(AppBar)),
        matching: find.byType(BackButtonIcon),
      ),
    );

    temporarilyBlockedDialog = registerElement(finder: find.byKey(const Key('__authErrorCausedTempBlockDialog__')));
    temporarilyBlockedDialogButton =
        registerButtonElement(finder: find.byKey(const Key('__authErrorCausedTempBlockDialogOkButton__')));

    loginToBlockedAccountDialog = registerElement(finder: find.byKey(const Key('__authErrorTempLockAttemptDialog__')));
    loginToBlockedAccountDialogButton =
        registerButtonElement(finder: find.byKey(const Key('__authErrorTempLockAttemptDialogOkButton__')));

    permanentlyBlockedDialog = registerElement(finder: find.byKey(const Key('__authErrorCausedPermaBlockDialog__')));
    permanentlyBlockedDialogButton =
        registerButtonElement(finder: find.byKey(const Key('__authErrorCausedPermaBlockDialogUnblockButton__')));
  }
}
