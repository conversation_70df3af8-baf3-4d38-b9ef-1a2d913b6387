import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class OtpNotAcceptedDialog extends TestPage {
  late TestElement okButton;

  OtpNotAcceptedDialog(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__dialogOtpNotAccepted__'))) {
    okButton = registerElement(finder: find.byKey(const Key('__dialogOtpNotAcceptedOkButton__')));
  }
}
