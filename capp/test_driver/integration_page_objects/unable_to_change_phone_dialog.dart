import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class UnableToChangePhoneDialog extends TestPage {
  late TestElement continueAsGuestButton;
  late TestElement contactUsButton;

  UnableToChangePhoneDialog(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__unableToChangePhoneDialog__'))) {
    continueAsGuestButton =
        registerButtonElement(finder: find.byKey(const Key('__unableToChangePhoneDialogContinueAsGuestButton__')));
    contactUsButton =
        registerButtonElement(finder: find.byKey(const Key('__unableToChangePhoneDialogContactUsButton__')));
  }
}
