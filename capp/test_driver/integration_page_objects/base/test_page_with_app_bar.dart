import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

abstract class TestPageWithAppBar extends TestPage {
  Finder? appBarFinder;
  late TestElement appBar;
  late TestElement appBarBackButton;
  late TestElement appBarCloseButton;
  late TestElement appBarTitle;

  TestPageWithAppBar({
    required WidgetTester tester,
    this.appBarFinder,
    required Finder screenFinder,
  }) : super(tester: tester, screenFinder: screenFinder) {
    appBarFinder ??= find.descendant(
      of: screenFinder,
      matching: find.byType(KoyalAppBar),
    );

    appBar = registerElement(finder: appBarFinder!);
    appBarBackButton = registerElement(
      finder: find.byKey(const Key('__KoyalAppBarBackButton__')),
    );
    appBarCloseButton = registerElement(
      finder: find.byKey(const Key('__KoyalAppBarCloseButton__')),
    );

    appBarTitle = registerElement(
      finder: find.descendant(
        of: find.descendant(
          of: appBarFinder!,
          matching: find.byType(KoyalAppBarTitle),
        ),
        matching: find.byType(Text),
      ),
      type: TestElementType.text,
    );
  }
}
