import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class IdentifyErrorPage extends TestPage {
  late TestElement continueButton;
  IdentifyErrorPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__IdentifyErrorScreen__')),
        ) {
    continueButton = registerButtonElement(
      finder: find.byKey(const Key('__IdentifyErrorContinueButton__')),
    );
  }
}
