import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';


class PersonalMenuDocumentsPage {
  final WidgetTester tester;
  PersonalMenuDocumentsPage(this.tester);

  final Finder _documentGeneralTerms = find.byKey(const Key('__document0__'));
  final Finder _documentPolicy = find.byKey(const Key('__document1__'));

  void verifyReady() {
    expect(_documentGeneralTerms, findsOneWidget);
  }

  void isGeneralTermsDocumentReady() {
    expect(_documentGeneralTerms, findsOneWidget);
  }

  void isPolicyDocumentReady() {
    expect(_documentPolicy, findsOneWidget);
  }

  Future<void> tapGeneralTermsDocument() async {
    await tester.tap(_documentGeneralTerms);
    await tester.pumpAndSettle();
  }

  Future<void> tapPolicyDocument() async {
    await tester.tap(_documentPolicy);
    await tester.pumpAndSettle();
  }
}
