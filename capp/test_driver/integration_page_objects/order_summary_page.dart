import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_app_bar.dart';

class OrderSummaryPage extends TestPageWithAppBar {
  final _partnerShopRowFinder = find.byKey(const Key('__orderSummaryDetailsPartnerShopRow__'));
  final _partnerShopAddressRowFinder = find.byKey(const Key('__orderSummaryDetailsPartnerShopAddressRow__'));
  final _deliveryMethodRowFinder = find.byKey(const Key('__orderSummaryDetailsDeliveryMethodRow__'));
  final _paymentMethodRowFinder = find.byKey(const Key('__orderSummaryDetailsPaymentMethodRow__'));

  late TestElement partnerShopRow;
  late TestElement partnerShopLabel;
  late TestElement partnerShopValue;
  late TestElement partnerShopAddressRow;
  late TestElement partnerShopAddressLabel;
  late TestElement partnerShopAddressValue;
  late TestElement deliveryMethodRow;
  late TestElement deliveryMethodLabel;
  late TestElement deliveryMethodValue;
  late TestElement paymentMethodRow;
  late TestElement paymentMethodLabel;
  late TestElement paymentMethodValue;
  late TestElement submitButton;
  late TestElement rbiDisclaimerButton;

  OrderSummaryPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__orderSummaryScreen__')),
          appBarFinder: find.byKey(const Key('__orderSummaryScreenAppBar__')),
        ) {
    partnerShopRow = registerElement(finder: _partnerShopRowFinder);
    partnerShopLabel = registerTextElement(finder: _getRowLabelFinder(_partnerShopRowFinder));
    partnerShopValue = registerTextElement(finder: _getRowValueFinder(_partnerShopRowFinder));
    partnerShopAddressRow = registerElement(finder: _partnerShopAddressRowFinder);
    partnerShopAddressLabel = registerTextElement(finder: _getRowLabelFinder(_partnerShopAddressRowFinder));
    partnerShopAddressValue = registerTextElement(finder: _getRowValueFinder(_partnerShopAddressRowFinder));
    deliveryMethodRow = registerElement(finder: _deliveryMethodRowFinder);
    deliveryMethodLabel = registerTextElement(finder: _getRowLabelFinder(_deliveryMethodRowFinder));
    deliveryMethodValue = registerTextElement(finder: _getRowValueFinder(_deliveryMethodRowFinder));
    paymentMethodRow = registerElement(finder: _paymentMethodRowFinder);
    paymentMethodLabel = registerTextElement(finder: _getRowLabelFinder(_paymentMethodRowFinder));
    paymentMethodValue = registerTextElement(finder: _getRowValueFinder(_paymentMethodRowFinder));
    submitButton = registerButtonElement(finder: find.byKey(const Key('__orderSummaryScreenContinueToPaymentBtn__')));
    rbiDisclaimerButton =
        registerButtonElement(finder: find.byKey(const Key('__orderSummaryScreenRbiDisclaimerBtn__')));
  }

  Finder _getRowLabelFinder(Finder of) {
    return find.descendant(of: of, matching: find.byType(Text)).at(0);
  }

  Finder _getRowValueFinder(Finder of) {
    return find.descendant(of: of, matching: find.byType(Text)).at(1);
  }

  void checkPartnerShopDetails({
    String? partnerShop,
    String? address,
    String? deliveryMethod,
    String? paymentMethod,
  }) {
    if (partnerShop == null) {
      partnerShopRow.isAbsent();
    } else {
      partnerShopLabel.textEquals('Partner Shop');
      partnerShopValue.textEquals(partnerShop);
    }

    if (address == null) {
      partnerShopAddressRow.isAbsent();
    } else {
      partnerShopAddressLabel.textEquals('Address');
      partnerShopAddressValue.textEquals(address);
    }

    if (deliveryMethod == null) {
      deliveryMethodRow.isAbsent();
    } else {
      deliveryMethodLabel.textEquals('Delivery Method');
      deliveryMethodValue.textEquals(deliveryMethod);
    }

    if (paymentMethod == null) {
      paymentMethodRow.isAbsent();
    } else {
      paymentMethodLabel.textEquals('Payment Method');
      paymentMethodValue.textEquals(paymentMethod);
    }
  }
}
