import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';


class AuthErrorIncorrectDialog extends TestPage {
  late TestElement okButton;

  AuthErrorIncorrectDialog(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__authErrorIncorrectDialog__'))) {
    okButton = registerButtonElement(finder: find.byKey(const Key('__authErrorIncorrectDialogOkButton__')));
  }
}
