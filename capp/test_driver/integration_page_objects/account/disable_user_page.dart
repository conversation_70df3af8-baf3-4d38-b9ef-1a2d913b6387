import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class DisableUserPage extends TestPage {
  late TestElement goBackButton;
  late TestElement confirmButton;
  late TestElement disableUser;

  DisableUserPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__disableUserScreen__')),
        ) {
    goBackButton = registerButtonElement(finder: find.byKey(const Key('__goBackButton__')));
    confirmButton = registerButtonElement(finder: find.byKey(const Key('__confirmButton__')));
  }
}
