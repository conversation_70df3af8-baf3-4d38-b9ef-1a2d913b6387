import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class AccountTestPage extends TestPage {
  final Finder _scrollableFinder = find.byKey(const Key('__accountPageListView__'));

  late TestElement personalDetailsMenuItem;
  late TestElement notificationsMenuItem;
  late TestElement loansMenuItem;
  late TestElement settingsMenuItem;
  late TestElement documentsMenuItem;
  late TestElement faqMenuItem;
  late TestElement helpCenterMenuItem;
  late TestElement contactUsMenuItem;
  late TestElement aboutUsMenuItem;
  late TestElement iPriceMenuItem;
  late TestElement loginMenuItem;
  late TestElement logoutMenuItem;
  late TestElement lockAccountMenuItem;
  late TestElement warningZoneItem;
  late TestElement feedbackSuggestionsMenuItem;

  AccountTestPage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__accountScreen__'))) {
    personalDetailsMenuItem = registerElement(finder: find.byKey(const Key('__profileMenuRow__')));
    notificationsMenuItem = registerElement(finder: find.byKey(const Key('__notificationsMenuRow__')));
    loansMenuItem = registerElement(finder: find.byKey(const Key('__loansMenuRow__')));    
    settingsMenuItem = registerElement(finder: find.byKey(const Key('__settingsRow__')));
    documentsMenuItem = registerElement(finder: find.byKey(const Key('__editDocumentsRow__')));
    faqMenuItem = registerElement(finder: find.byKey(const Key('__faqRow__')));
    helpCenterMenuItem = registerElement(finder: find.byKey(const Key('__helpCenterRow__')));
    contactUsMenuItem = registerElement(finder: find.byKey(const Key('__contactUsRow__')));
    aboutUsMenuItem = registerElement(finder: find.byKey(const Key('__aboutUsRow__')));
    iPriceMenuItem = registerElement(finder: find.byKey(const Key('__iPriceMenuRow__')));
    loginMenuItem = registerElement(finder: find.byKey(const Key('__loginRow__')));
    logoutMenuItem = registerElement(finder: find.byKey(const Key('__logoutRow__')));
    lockAccountMenuItem = registerElement(finder: find.byKey(const Key('__lockAccountRow__')));
    warningZoneItem = registerElement(finder: find.byKey(const Key('__warningZoneMenuRow__')));
    feedbackSuggestionsMenuItem = registerElement(finder: find.byKey(const Key('__feedbackRow__')));
  }

  Future<void> scrollToLogoutRow() async {
    await tester.dragUntilVisible(logoutMenuItem.finder, _scrollableFinder, const Offset(0, -200), maxIteration: 10);
    await tester.pumpAndSettle();
  }

  Future<void> scrollToLockAccountRow() async {
    await tester.dragUntilVisible(
      lockAccountMenuItem.finder,
      _scrollableFinder,
      const Offset(0, -200),
      maxIteration: 10,
    );
    await tester.pumpAndSettle();
  }

  Future<void> scrollToLogintRow() async {
    await tester.dragUntilVisible(loginMenuItem.finder, _scrollableFinder, const Offset(0, -200), maxIteration: 10);
    await tester.pumpAndSettle();
  }
}
