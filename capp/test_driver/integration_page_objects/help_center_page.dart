import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_app_bar.dart';

class HelpCenterPage extends TestPageWithAppBar {
  late TestElement searchButton;
  late TestElement faqTopicButton;
  late TestElement topFaqItem1;
  late TestElement chatWithUsButton;
  late TestElement contactUsButton;

  HelpCenterPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__helpCenterScreen__')),
          appBarFinder: find.byKey(const Key('__helpCenterScreenAppBar__')),
        ) {
    searchButton = registerElement(finder: find.byKey(const Key('__searchHelpCenterButton__')));
    topFaqItem1 = registerElement(finder: find.byKey(const Key('__topFaq_1__')));
    chatWithUsButton = registerElement(finder: find.byKey(const Key('__helpCenterChatWithUsBtn__')));
    contactUsButton = registerElement(finder: find.byKey(const Key('__helpCenterContactUsBtn__')));
    faqTopicButton = registerElement(finder: find.byKey(const Key('__faqTopicButton_faqTopic1__')));
  }
}

class FaqTopicPage extends TestPageWithAppBar {
  late TestElement searchButton;
  late TestElement topFaqItem1;
  late TestElement chatWithUsButton;
  late TestElement backButton;

  FaqTopicPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__faqTopicScreen__')),
          appBarFinder: find.byKey(const Key('__faqTopicScreenAppBar__')),
        ) {
    searchButton = registerElement(finder: find.byKey(const Key('__searchHelpCenterButton__')));
    topFaqItem1 = registerElement(finder: find.byKey(const Key('__faq1__')));
    chatWithUsButton = registerElement(finder: find.byKey(const Key('__faqTopicChatbotButton__')));
    backButton = registerElement(finder: find.byKey(const Key('__KoyalAppBarBackButton__')));
  }
}

class FaqItemPage extends TestPageWithAppBar {
  late TestElement yesButton;
  late TestElement noButton;
  late TestElement backButton;

  FaqItemPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__faqItemScreen__')),
          appBarFinder: find.byKey(const Key('__faqItemScreenAppBar__')),
        ) {
    yesButton = registerElement(finder: find.byKey(const Key('__faqItemVoteYes__')));
    noButton = registerElement(finder: find.byKey(const Key('__faqItemVoteNo__')));
    backButton = registerElement(finder: find.byKey(const Key('__KoyalAppBarBackButton__')));
  }
}
