import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import 'base/test_page_with_app_bar.dart';

class PaymentModeSelectionPage extends TestPageWithAppBar {
  final Finder _amountTextViewFinder = find.byKey(const Key('__amountTextKey__'));

  late TestElement showPencilButton;
  late TestElement editAmountPopup;
  late TestElement netbankingButton;
  late TestElement debitCardButton;
  late TestElement upiPaymentButton;
  late TestElement payNowButton;
  late TestElement popupConfirmButton;

  PaymentModeSelectionPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentModeSelectionScreen__')),
          appBarFinder: find.byKey(const Key('__paymentModeSelectionScreenAppBar__')),
        ) {
    popupConfirmButton = registerButtonElement(finder: find.byKey(const Key('__editAmountConfirmButton__')));
    showPencilButton = registerButtonElement(finder: find.byKey(const Key('__showPencilButtonKey__')));
    popupConfirmButton = registerButtonElement(finder: find.byKey(const Key('__editAmountConfirmButton__')));
    netbankingButton = registerButtonElement(finder: find.byKey(const Key('__netBankingFlatButtonKey__')));
    debitCardButton = registerButtonElement(finder: find.byKey(const Key('__debitCardFlatButtonKey__')));
    upiPaymentButton = registerButtonElement(finder: find.byKey(const Key('__upiSelectedFlatButtonKey__')));
    payNowButton = registerButtonElement(finder: find.byKey(const Key('__payNowButtonKey__')));
  }

  void checkTotalAmountTextExist() {
    expect(_amountTextViewFinder, findsOneWidget);
  }

  Future<void> checkTotalAmountText(String amountText) async {
    findText('₱$amountText');
  }

  Future<void> netBanking() async {
    await netbankingButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 1));
  }

  Future<void> editAmount() async {
    await showPencilButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 2));
  }

  Future<void> debitCard() async {
    await debitCardButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 1));
  }

  Future<void> upiPayment() async {
    await upiPaymentButton.tap();
    await tester.pumpAndSettle(const Duration(seconds: 1));
  }
}
