import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../base/test_page_with_num_keyboard.dart';

class CardActivationPage extends TestPageWithNumKeyboard {
  late final Finder _fourDigitsInputTextFieldFinder;
  late final Finder _errorPopupKoyalOverlayFinder;
  late final Finder _gotItButtonFinder;

  late final TestElement fourDigitsInputTextField;
  late final TestElement errorPopupKoyalOverlay;
  late final TestElement gotItButton;

  CardActivationPage({required WidgetTester tester})
      : super(
          tester: tester,
          appBarFinder: find.byKey(const Key('__cardsActivationScreenAppBar__')),
          screenFinder: find.byKey(const Key('__cardsActivationScreenScaffold__')),
        ) {
    _fourDigitsInputTextFieldFinder = find.byKey(const Key('__cardsActivation4LastDigitsInput__'));
    _errorPopupKoyalOverlayFinder = find.byType(KoyalOverlay);
    _gotItButtonFinder = find.descendant(
      of: _errorPopupKoyalOverlayFinder,
      matching: find.byKey(const Key('__cardsActivationGotItButton__')),
    );

    fourDigitsInputTextField = registerTextFieldElement(finder: _fourDigitsInputTextFieldFinder);
    errorPopupKoyalOverlay = registerElement(finder: _errorPopupKoyalOverlayFinder);
    gotItButton = registerButtonElement(finder: _gotItButtonFinder);
  }

  void checkAppBarTitle(String title) {
    appBarTitle.textEquals(title);
  }
}
