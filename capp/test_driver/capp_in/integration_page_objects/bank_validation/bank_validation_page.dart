import 'package:capp_bank_validation/capp_bank_validation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class BankValidationPage extends TestPage {
  late TestElement internetBankingTile;
  late TestElement uploadPdfStatementTile;
  late TestElement _giveConsentCheckbox;
  late TestElement _continueButton;
  late TestElement _bankValidationWebView;

  BankValidationPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__page_bankVerificationId__'), skipOffstage: false),
        ) {
    internetBankingTile = registerButtonElement(finder: find.byKey(const Key('__viaInternetBanking__')));
    uploadPdfStatementTile = registerButtonElement(finder: find.byKey(const Key('__uploadPdfStatement__')));
    _giveConsentCheckbox = registerButtonElement(finder: find.byKey(const Key('__consentToBV__')));
    _continueButton = registerButtonElement(finder: find.byKey(const Key('__continueButton__')));
    _bankValidationWebView = registerElement(finder: find.byType(BankValidationWebViewScreen));
  }

  Future<void> chooseInternetBanking() async {
    await internetBankingTile.tap();
    await _giveConsentCheckbox.tap();
    await _continueButton.tap();
    await tester.pump(const Duration(seconds: 3));
    _bankValidationWebView.isReady();
  }

  Future<void> choosePdfStatement() async {
    await uploadPdfStatementTile.tap();
    await _giveConsentCheckbox.tap();
    await _continueButton.tap();
    await tester.pump(const Duration(seconds: 3));
    _bankValidationWebView.isReady();
  }
}
