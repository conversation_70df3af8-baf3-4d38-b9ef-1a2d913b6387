import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';
import '../../../integration_page_objects/self_care/ujjwal/index.dart';
import '../../integration_tests_tools/tester_helper_in.dart';

const phoneNumber = '7778889990';
const password = '166437';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Ujjwal Tests - facelifted', () {
    integrationTest(
      'Ujjwall overview',
      (tester, context) async {
        final testerHelper = TesterHelperIn(tester: tester, context: context)
          ..setEmiContractState('active')
          ..forceFakeLoginStateAsClient()
          ..resetFaceliftFlags(facelifted: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.ujjwalActionReminder, isEnabled: false);
        final ujjwalDetailPage = UjjwalDetailPage(tester);
        final ujjwalTransactionsHistoryPage = UjjwalTransactionsHistoryPage(tester);
        final ujjwalTransactionDetailPage = UjjwalTransactionDetailPage(tester);
        final ujjwalInstallmentScheduleDetailPage = UjjwalInstalmenScheduleDetailPage(tester);
        final ujjwalInstalmentDocumentsPage = UjjwalInstalmentDocumentsPage(tester);
        final ujjwalMonthlyStatementsPage = UjjwalMonthlyStatementsPage(tester);
        final ujjwalDocumentsPage = UjjwalDocumentsPage(tester);
        final ujjwalDetailsPage = UjjwalDetailsPage(tester);
        await testerHelper.navigateToUjjwalDetail(facelifted: true);
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalDetailPage.isReady();
        await testerHelper.delay(const Duration(milliseconds: 200));
        await ujjwalDetailPage.viewTransactionHistoryButton.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalTransactionsHistoryPage.isReady();
        await ujjwalTransactionsHistoryPage.checkPage();
        await ujjwalTransactionsHistoryPage.firstTransaction.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalTransactionDetailPage.isReady();
        await ujjwalTransactionDetailPage.checkPage();
        await ujjwalTransactionDetailPage.firstInstallment.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalInstallmentScheduleDetailPage.isReady();
        await ujjwalInstallmentScheduleDetailPage.appBarBackButton.tap();
        ujjwalTransactionDetailPage.isReady();
        await ujjwalTransactionDetailPage.documentsButton.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalInstalmentDocumentsPage.isReady();
        await ujjwalInstalmentDocumentsPage.appBarBackButton.tap();
        ujjwalTransactionDetailPage.isReady();
        await ujjwalTransactionDetailPage.appBarBackButton.tap();
        ujjwalTransactionsHistoryPage.isReady();
        await ujjwalTransactionsHistoryPage.appBarBackButton.tap();
        ujjwalDetailPage.isReady();
        await ujjwalDetailPage.monthlyStatementsButton.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalMonthlyStatementsPage.isReady();
        await ujjwalMonthlyStatementsPage.appBarBackButton.tap();
        ujjwalDetailPage.isReady();
        await ujjwalDetailPage.detailsAndDocuments.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalDocumentsPage.isReady();
        await ujjwalDocumentsPage.appBarBackButton.tap();
        ujjwalDetailPage.isReady();
        await ujjwalDetailPage.detailsButton.tap();
        ujjwalDetailsPage.isReady();
        await ujjwalDetailsPage.appBarBackButton.tap();
        ujjwalDetailPage.isReady();
      },
      appRunner: appRunner,
    );
  });

  group('Ujjwal Tests - before facelift', () {
    integrationTest(
      'Ujjwall transaction history',
      (tester, context) async {
        final testerHelper = TesterHelperIn(tester: tester, context: context)
          ..setEmiContractState('active')
          ..forceFakeLoginStateAsClient()
          ..resetFaceliftFlags(facelifted: false);
        final ujjwalDetailPage = UjjwalDetailPage(tester);
        final ujjwalTransactionHistoryPage = UjjwalTransactionHistoryPage(tester);
        final installmentDetailPage = InstallmentDetailPage(tester);

        await testerHelper.navigateToUjjwalDetail();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalDetailPage.isReady();
        await testerHelper.delay(const Duration(milliseconds: 200));
        await ujjwalDetailPage.scrollToTransactionHistory();
        await testerHelper.delay(const Duration(milliseconds: 200));
        await ujjwalDetailPage.viewTransactionHistoryButton.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalTransactionHistoryPage.isReady();
        ujjwalTransactionHistoryPage.viewDetailsButton.isReady();
        await ujjwalTransactionHistoryPage.bookedButton.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalTransactionHistoryPage.bookedItem.isReady();
        await ujjwalTransactionHistoryPage.repayButton.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        installmentDetailPage.isReady();
        await installmentDetailPage.appBarBackButton.tap();
        await tester.pumpAndSettle(const Duration(milliseconds: 500));
        ujjwalTransactionHistoryPage.isReady();
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Ujjwall detail screen',
      (tester, context) async {
        final testerHelper = TesterHelperIn(tester: tester, context: context)
          ..setEmiContractState('active')
          ..forceFakeLoginStateAsClient()
          ..resetFaceliftFlags(facelifted: false);

        final ujjwalDetailPage = UjjwalDetailPage(tester);
        final installmentDetailPage = InstallmentDetailPage(tester);

        await testerHelper.navigateToUjjwalDetail();
        await testerHelper.delay(const Duration(milliseconds: 200));
        ujjwalDetailPage.isReady();
        await ujjwalDetailPage.checkPage();
        await ujjwalDetailPage.payNowButton.tap();
        await testerHelper.delay(const Duration(milliseconds: 200));
        installmentDetailPage.isReady();
        await installmentDetailPage.appBarBackButton.tap();
        await tester.pumpAndSettle(const Duration(milliseconds: 200));
        ujjwalDetailPage.isReady();
      },
      appRunner: appRunner,
    );
  });
}
