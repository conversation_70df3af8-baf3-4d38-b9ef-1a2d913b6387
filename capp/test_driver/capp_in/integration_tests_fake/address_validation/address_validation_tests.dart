import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects/address_validation/address_validation_page.dart';
import '../../../integration_tests_tools/tester_helper.dart';

// import '../../integration_tests_tools/tester_extension.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Address Validation', () {
    integrationTest(
      'Aadhaar XML',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final addressValidationPage = AddressValidationPage(tester);

        await testerHelper.navigateToAddressValidationScreen();
        addressValidationPage.isReady();

        await addressValidationPage.aadhaarXml();
      },
      appRunner: appRunner,
    );
  });

  group('Address Validation', () {
    integrationTest(
      'Online KYC',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final addressValidationPage = AddressValidationPage(tester);

        await testerHelper.navigateToAddressValidationScreen();
        addressValidationPage.isReady();

        await addressValidationPage.launchOnlineKyc();
      },
      appRunner: appRunner,
    );
  });
}
