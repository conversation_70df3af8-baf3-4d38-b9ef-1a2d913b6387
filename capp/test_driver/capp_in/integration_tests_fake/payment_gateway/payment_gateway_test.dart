import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../integration_page_objects/index.dart';
import '../../../integration_tests_tools/tester_helper.dart';
import '../../integration_tests_tools/integration_tests_in.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  group('payment Gateway test:', () {
    integrationTestFakeIn(
      'payment Gateway test',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final paymentModeSelectionPage = PaymentModeSelectionPage(tester);
        final paymentSuccessFailurePage = PaymentSuccessFailurePage(tester);

        await testerHelper.navigateToPaymentGateway();
        await paymentModeSelectionPage.netBanking();
        await paymentModeSelectionPage.editAmount();
        await paymentModeSelectionPage.debitCard();
        await paymentModeSelectionPage.upiPayment();
        await testerHelper.navigateToPaymentSuccessFailureScreen();
        paymentSuccessFailurePage.isReady();
        await tester.pumpAndSettle(const Duration(seconds: 5));
      },
    );
  });
}
