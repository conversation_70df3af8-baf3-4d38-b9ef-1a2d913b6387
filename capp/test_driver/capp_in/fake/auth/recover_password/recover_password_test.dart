import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_tests_fake/auth/recover_password.dart';

const phoneNumber = '7555150000';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Recover Password [IN]:', () {
    integrationTest(
      'Successful PIN recovery (authV3)',
      (tester, context) async {
        await successfullPinRecoveryTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Successful PIN recovery (authV3, 4digit PIN)',
      (tester, context) async {
        await successfullPinRecoveryTest(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'PIN recovery with errors along the way (authV3)',
      (tester, context) async {
        await pinRecoveryWithErrorsTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'PIN recovery with errors along the way (authV3, 4digit PIN)',
      (tester, context) async {
        await pinRecoveryWithErrorsTest(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );
  });
}
