import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_tests_fake/auth/change_phone.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Change Phone [IN]:', () {
    integrationTest(
      'Change Phone Number',
      (tester, context) async {
        await changePhoneNumberNewUserTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Change Phone Number with Selfie enabled',
      (tester, context) async {
        await changePhoneNumberNewUserTest(
          tester,
          context,
          isSelfieFFEnabled: true,
        );
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Change Phone Number (4digit PIN)',
      (tester, context) async {
        await changePhoneNumberNewUserTest(tester, context, isFourDigitPinEnabled: true);
        // Enable, Check and Update during task #35301
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Change Phone Number (disabled FF)',
      (tester, context) async {
        await changePhoneNumberNewUserTest(tester, context, isChangePhoneEnabled: false);
        // Enable, Check and Update during task #35301
      },
      appRunner: appRunner,
    );
  });
}
