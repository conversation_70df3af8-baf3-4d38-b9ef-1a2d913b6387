import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_page_objects/index.dart';
import '../../../../integration_tests_fake/auth/disable_user.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Disable user [IN]:', () {
    integrationTest(
      'Anonymous user',
      (tester, context) async {
        await disableAnonymousUserTest(
          tester,
          () {AuthLandingPage(tester).isReady();},
          context,
        );
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Logged in user',
      (tester, context) async {
        await disableLoggedInUserTest(
          tester,
          () {AuthLandingPage(tester).isReady();},
          context,
        );
      },
      appRunner: appRunner,
    );
  });
}
