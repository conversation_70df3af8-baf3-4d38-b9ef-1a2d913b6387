import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_tests_fake/auth/unblock_account.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Unblock Account Tests [IN]:', () {
    integrationTest(
      'Unblock account from permanent lock',
      (tester, context) async {
        await unblockAccountFromPermanentLockTest(tester, context);
      },
      appRunner: appRunner,
    );
  });
}
