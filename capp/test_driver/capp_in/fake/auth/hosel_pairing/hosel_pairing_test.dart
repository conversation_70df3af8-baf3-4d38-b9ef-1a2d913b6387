import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_page_objects/index.dart';
import '../../../../integration_tests_fake/auth/hosel_pairing.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Hosel Pairing [IN]:', () {
    integrationTest(
      'Hosel Pairing Required, not a Home Credit Customer',
      (tester, context) async {
        await hoselPairingRequiredNotHCCustomerTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Hosel Pairing Required, general screen check, use Date of Birth',
      (tester, context) async {
        await hoselPairingRequiredGeneralScreenCheckUseDateofBirthTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Hosel Pairing Required, Locked user',
      (tester, context) async {
        await hoselPairingRequiredLockedUserTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Hosel Pairing Required, ID Number',
      (tester, context) async {
        await hoselPairingSteps(tester, context, HoselIdentifierType.idNumber);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Hosel Pairing Required, ID Number, Manual Identification Started',
      (tester, context) async {
        await hoselPairingManualDeduplicationSteps(tester, context, HoselIdentifierType.idNumber);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Hosel Pairing Required, provide more identifiers',
      (tester, context) async {
        await hoselPairingRequiredProvideMoreIdentifiersTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Hosel Pairing Required, no more identifiers left',
      (tester, context) async {
        await hoselPairingRequiredNoMoreIdentifiersLeftTest(tester, context);
      },
      appRunner: appRunner,
    );
  });
}
