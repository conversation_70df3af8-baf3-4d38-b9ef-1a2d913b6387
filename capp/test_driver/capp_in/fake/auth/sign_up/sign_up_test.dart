import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_tests_fake/auth/sign_up.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('SignUp Tests [IN]:', () {
    integrationTest(
      'SignUp walkthrough (auth v3)',
      (tester, context) async {
        await signupWalkthroughAuthV3Test(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'SignUp walkthrough (auth v3, fail on existing user)',
      (tester, context) async {
        await signupWalkthroughAuthV3Test(tester, context, failOnExistingUser: true);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'SignUp walkthrough (auth v3, 4digit PIN)',
      (tester, context) async {
        await signupWalkthroughAuthV3Test(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Signup (auth v3) - phone already registered, create new account',
      (tester, context) async {
        await signupAuthV3PhoneRegisteredTest(tester, context);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Signup (auth v3, 4digit PIN) - phone already registered, create new account',
      (tester, context) async {
        await signupAuthV3PhoneRegisteredTest(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Signup (auth v4, 4digit PIN) - prospect registration',
      (tester, context) async {
        await signupWalkthroughAuthV4ProspectTest(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Signup (auth v4, 4digit PIN) - full, straight from verification',
      (tester, context) async {
        await signupWalkthroughAuthV4PanTest(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Signup (auth v4) - full, not a customer - LAN option',
      (tester, context) async {
        await signupWalkthroughExistingCustomerLanTest(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );

    integrationTest(
      'Signup (auth v3)with KTP - phone already registered',
      (tester, context) async {
        await signupAuthV3PhoneRegisteredKtpIputTest(tester, context, isFourDigitPinEnabled: true);
      },
      appRunner: appRunner,
    );
  });
}
