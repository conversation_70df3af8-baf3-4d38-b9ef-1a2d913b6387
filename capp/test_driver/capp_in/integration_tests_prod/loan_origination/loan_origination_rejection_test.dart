import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects/loan_origination/calculator_page.dart';
import '../../../integration_page_objects/loan_origination/get_loan_page.dart';
import '../../../integration_page_objects/loan_origination/residential_address_page.dart';
import '../../../integration_page_objects/loan_origination/selfie_capture_page.dart';
import '../../../integration_tests_tools/tester_helper.dart';
import '../../integration_reuse_steps/loan.dart';
import '../../integration_tests_tools/integration_tests_in.dart';
import 'selfie_tests.dart' as selfie;

String? phoneNumber = '9355557574';
String? password = '415263';
late GetLoanPage getLoanPage;
late String calculatorAmount;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Loan origination rejection', () {
    integrationTestProdIn('Register user', (tester, context) async {
      final identityTestRepository = context.container.get<IdentityTestProdService>();
      final response = await identityTestRepository.createUserForTest();
      response.fold((l) {
        throw Exception('Error creating user');
      }, (r) {
        phoneNumber = r.username;
        password = r.password;
      });
    });

    integrationTestProdIn('Open Loan Origination', (tester, context) async {
      getLoanPage = GetLoanPage(tester);
      final testerHelper = TesterHelper(tester: tester, context: context);

      await loginUser(phoneNumber!, password!, tester, testerHelper);
      await testerHelper.navigateToHome();

      await navigateToLoanJourneyFromHome(tester);
      getLoanPage.isReady();
    });

    integrationTestProdIn('Get a loan', (tester, context) async {
      final selfiePage = LoanSelfieCapturePage(tester);
      final residentialAddressPage = ResidentialAddressPage(tester);

      await navigateToLoanPage(tester, context);
      getLoanPage.isReady();

      await navigateToSelfie(getLoanPage);
      selfiePage.isReady();
      await selfie.capture(selfiePage);
      getLoanPage.isReady();

      await navigateToIdProof(getLoanPage);
      await panCard(tester, 'ACLNULAB');
      getLoanPage.isReady();

      await navigateToResidentialAddressPage(getLoanPage);
      residentialAddressPage.isReady();
      await residentialAddressPage.aadhaarXml();
      getLoanPage.isReady();

      await getLoanPage.goNext();
    });

    integrationTestProdIn('Product Selection & calculator', (tester, context) async {
      final calculatorPage = CalculatorPage(tester);

      await navigateToLoanPage(tester, context);

      await fillCalculator(calculatorPage, tester, context);

      calculatorAmount = calculatorPage.amountOfMoney.getText()!.replaceAll(RegExp('[^0-9]'), '');

      await calculatorPage.tapGetLoanButton();
    });
  });
}

Future<void> navigateToLoanPage(WidgetTester tester, TestContext context) async {
  final testerHelper = TesterHelper(tester: tester, context: context);

  await loginUser(phoneNumber!, password!, tester, testerHelper);

  await testerHelper.navigateLoanOrigination();
}
