import 'dart:async';

import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../integration_page_objects/loan_origination/correspondence_address_page.dart';
import '../../../integration_page_objects/loan_origination/document_capture_page.dart';
import '../../../integration_page_objects/loan_origination/get_loan_page.dart';
import '../../../integration_tests_tools/tester_extension.dart';
import '../../../integration_tests_tools/tester_helper.dart';
import '../../integration_reuse_steps/loan.dart';
import '../../integration_tests_tools/integration_tests_in.dart';

const phoneNumber = '**********';
const password = '415263';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  group('Correspondence address', () {
    integrationTestProdIn(
      'Driving license',
      (tester, context) async {
        final getLoanPage = GetLoanPage(tester);
        final correspondenceAddressPage = CorrespondenceAddressPage(tester);
        final documentCapturePage = DocumentCapturePage(tester);
        final testerHelper = TesterHelper(tester: tester, context: context);

        unawaited(
          navigateToCorrespondenceAddressPage(
            phoneNumber,
            password,
            tester,
            context.configuration,
            testerHelper,
          ),
        );
        await Future.delayed(const Duration(seconds: 5), tester.pumpAndSettle);
        correspondenceAddressPage.isReady();

        await correspondenceAddressPage.selectDrivingLicense();

        await correspondenceAddressPage.uploadPhoto();
        await tester.delay();
        documentCapturePage.isReady();

        await takeDocumentPhoto(tester);
        correspondenceAddressPage.isReady();

        await correspondenceAddressPage.fillAddressDetails(
          pin: '123456',
          houseNumber: '1',
          streetName: 'Test str.',
          locality: 'Test locality',
          landmark: 'Test landmark',
          floorNumber: '1',
        );
        await correspondenceAddressPage.submitForm();
        getLoanPage.isReady();
      },
      timeout: const Timeout(Duration(minutes: 5)),
    );
  });
}
