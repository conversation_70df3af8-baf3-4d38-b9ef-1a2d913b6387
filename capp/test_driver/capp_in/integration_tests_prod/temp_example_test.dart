import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../integration_page_objects/onboarding_choose_language_page.dart';
import '../integration_tests_tools/integration_tests_in.dart';

void run() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group(
    'Con_A:Temp Test',
    () {
      integrationTestProdIn(
        'Con_A:Initial Screen',
        (tester, context) async {
          OnboardingChooseLanguagePage(tester).verifyReady();
        },
        timeout: const Timeout(Duration(seconds: 120)),
      );
    },
  );
}
