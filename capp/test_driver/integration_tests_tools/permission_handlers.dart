import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class TestPermissionHandlerPlatform with MockPlatformInterfaceMixin implements PermissionHandlerPlatform {
  final PermissionStatus status;

  TestPermissionHandlerPlatform({required this.status});
  @override
  Future<PermissionStatus> checkPermissionStatus(Permission permission) => Future.value(status);

  @override
  Future<ServiceStatus> checkServiceStatus(Permission permission) => Future.value(ServiceStatus.enabled);

  @override
  Future<bool> openAppSettings() => Future.value(true);

  @override
  Future<Map<Permission, PermissionStatus>> requestPermissions(List<Permission> permissions) {
    final permissionsMap = <Permission, PermissionStatus>{for (final permission in permissions) permission: status};
    return Future.value(permissionsMap);
  }

  @override
  Future<bool> shouldShowRequestPermissionRationale(Permission? permission) {
    return Future.value(false);
  }
}
