// ignore_for_file: use_setters_to_change_properties, unawaited_futures
import 'package:capp_address_validation/capp_address_validation.dart' as pending;
import 'package:capp_api/capp_api.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_bank_validation/capp_bank_validation.dart' hide BankDetailsModel;
import 'package:capp_deals_core/capp_deals_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_innovatrics_core/capp_innovatrics_core.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart' hide RepaymentSelectAmountRouteArgs;
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:identityapi/identityapi.dart';
import 'package:identityapi/model/sign_on_state.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:koyal_testing/integration_test/index.dart';
import 'package:koyal_testing/koyal_testing.dart';
import 'package:logger/logger.dart';
import 'package:navigator/navigator.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';
import 'package:selfcareapi/api/action_belt_widgets_api.dart';

import '../integration_reuse_steps/common.dart';
import 'permission_handlers.dart';

/// Help functionality that is shared between PROD & FAKE tests
class TesterHelper {
  final WidgetTester tester;
  final TestContext context;
  final Logger logger = Logger(
    printer: SimplePrinter(
      colors: false,
    ),
    filter: ProductionFilter(),
    level: Level.verbose,
  );

  TesterHelper({
    required this.tester,
    required this.context,
  });

  Future<void> delay(Duration duration, {bool settleAfterPump = true}) async {
    logger.i(
      'Test delay.\n'
      'Duration: $duration',
    );
    await Future<void>.delayed(duration);
    if (settleAfterPump) {
      await tester.pumpAndSettle();
    } else {
      await tester.pump();
    }
  }

  Future<void> pumpHome() async {
    await tester.pump(const Duration(seconds: 4));
    await tester.pump(const Duration(seconds: 4));
    await tester.pump(const Duration(seconds: 2));
    await tester.pump();
  }

  /// Navigate to the Home screen
  Future<void> navigateToHome({TabItem? initialTab}) async {
    logger.i('Navigate to the Home screen.');
    context.configuration.navigator?.toMainScreen(
      arguments: MainScreenArguments(initialTab: initialTab ?? TabItem.home),
    );
    await tester.pumpAndSettle();
    return pumpHomeFrames(tester);
  }

  /// Navigate to Sign Up screen
  Future<void> navigateToSignUp() async {
    logger.i('Navigate to the Sign Up screen.');
    context.configuration.navigator
        ?.pushNamedAndRemoveUntilFromPackage('CappAuth', 'AuthLandingScreen', 'CappHome', 'MainScreen');
    await tester.pumpAndSettle();
  }

  /// Navigate to Sign In screen
  Future<void> navigateToSignIn() async {
    logger.i('Navigate to the Sign In screen.');

    context.configuration.navigator
        ?.pushNamedAndRemoveUntilFromPackage('CappAuth', 'AuthLandingScreen', 'CappHome', 'MainScreen');
    await tester.pumpAndSettle();
  }

  /// Navigate to the Community Deals screen
  Future<void> navigateToCommunityDeals() async {
    logger.i('Navigate to the Community Deals screen.');
    context.configuration.navigator?.toMainScreen(arguments: MainScreenArguments(initialTab: TabItem.communityDeals));
    await tester.pumpAndSettle();
  }

  /// Navigate to the Product Detail screen defined by `productId` parameter.
  Future<void> navigateToProductDetail(String productId) async {
    logger.i(
      'Navigate to the Product Detail screen.\n'
      'Product ID: $productId',
    );
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappDeals',
      'ProductDetailScreen',
      'CappHome',
      'MainScreen',
      arguments: ProductDetailArguments(productId: productId),
    );
    await tester.pumpAndSettle(const Duration(seconds: 1));
  }

  /// Navigate to the Flexi detail
  Future<void> navigateToFlexiDetail({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
    bool facelifted = false,
  }) async {
    logger.i(
      'Navigate to the Flexi Detail screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    if (facelifted) {
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappSelfService',
        'FlexiOverviewScreen',
        'CappSelfService',
        'FlexiOverviewScreen',
        arguments: ContractDetailRouteArgs(
          contractNumber: accountNumber.toString(),
        ),
      );
    } else {
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappSelfService',
        'FlexiDetailScreen',
        'CappSelfService',
        'FlexiDetailScreen',
        arguments: ContractDetailRouteArgs(
          contractNumber: accountNumber.toString(),
        ),
      );
    }
    await tester.pumpAndSettle();
  }

  /// Navigate to the Ujjwal detail
  Future<void> navigateToUjjwalDetail({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
    bool facelifted = false,
  }) async {
    logger.i(
      'Navigate to the Ujjwal Detail screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    if (facelifted) {
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappSelfService',
        'UjjwalOverviewScreen',
        'CappSelfService',
        'UjjwalOverviewScreen',
        arguments: ContractDetailRouteArgs(
          contractNumber: accountNumber.toString(),
        ),
      );
    } else {
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappSelfService',
        'UjjwalDetailScreenOld',
        'CappSelfService',
        'UjjwalDetailScreenOld',
        arguments: ContractDetailRouteArgs(
          contractNumber: accountNumber.toString(),
        ),
      );
    }
    await tester.pumpAndSettle();
  }

  // Navigate to the CreditCard overview
  Future<void> navigateToCreditCardOverview({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
  }) async {
    logger.i(
      'Navigate to the CreditCard Overview screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappSelfService',
      'CreditCardOverviewScreen',
      'CappSelfService',
      'CreditCardOverviewScreen',
      arguments: ContractDetailRouteArgs(
        contractNumber: accountNumber.toString(),
      ),
    );
    await tester.pumpAndSettle();
  }

  // Navigate to the Cel overview
  Future<void> navigateToCelOverview({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
    required ContractType contractType,
  }) async {
    logger.i(
      'Navigate to the CashLoan Overview screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappSelfService',
      'CelOverviewScreen',
      'CappSelfService',
      'CelOverviewScreen',
      arguments: CelOverviewRouteArgs(
        contractNumber: accountNumber.toString(),
        contractType: contractType,
      ),
    );
    await tester.pumpAndSettle();
  }

  // Navigate to the Qwarta overview
  Future<void> navigateToQwartaOverview({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
  }) async {
    logger.i(
      'Navigate to the Qwarta Overview screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappSelfService',
      'QwartaOverviewScreen',
      'CappSelfService',
      'QwartaOverviewScreen',
      arguments: ContractDetailRouteArgs(
        contractNumber: accountNumber.toString(),
      ),
    );
    await tester.pumpAndSettle();
  }

  // Navigate to the Paylater overview
  Future<void> navigateToPaylaterOverview({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
  }) async {
    logger.i(
      'Navigate to the Paylater Overview screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappSelfService',
      'PaylaterOverviewScreen',
      'CappSelfService',
      'PaylaterOverviewScreen',
      arguments: ContractDetailRouteArgs(
        contractNumber: accountNumber.toString(),
      ),
    );
    await tester.pumpAndSettle();
  }

  // Navigate to the Bnpl overview
  Future<void> navigateToBnplOverview({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
  }) async {
    logger.i(
      'Navigate to the Bnpl Overview screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappSelfService',
      'BnplOverviewScreen',
      'CappSelfService',
      'BnplOverviewScreen',
      arguments: ContractDetailRouteArgs(
        contractNumber: accountNumber.toString(),
      ),
    );
    await tester.pumpAndSettle();
  }

  // Navigate to the Sappi overview
  Future<void> navigateToSappiOldOverview({
    int accountNumber = **********,
    ContractStatus contractStatus = ContractStatus.active,
  }) async {
    logger.i(
      'Navigate to the Sappi Overview old screen.\n'
      'Account Number: $accountNumber\n'
      'Contract Status: $contractStatus',
    );
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappSelfService',
      'InsuranceDetailScreen',
      'CappSelfService',
      'InsuranceDetailScreen',
      arguments: ContractDetailRouteArgs(
        contractNumber: accountNumber.toString(),
      ),
    );
    await tester.pumpAndSettle();
  }

  Future navigateLoanOrigination() async {
    logger.i('Navigate to the Loan Origination screen.');
    context.configuration.navigator
        ?.pushNamedAndRemoveUntilFromPackage('CappLoanOrigination', 'GetLoanWizard', 'CappHome', 'MainScreen');
    await tester.pumpAndSettle();
  }

  void setFakeRegisteredUserWithLoan() {
    (context.container<TestIdentityStorage>()).isFakeUserWithCuid = true;
  }

  void forceFakeLoginStateAsRegistered() {
    FakeIdentityApiBase.statusIdentity = SignOnState.registered;

    (context.container<TestIdentityStorage>())
      ..fakeUserSignOnState = SignOnStatus.registered
      ..forceFakeUserSignOnState = true
      ..isInsider = false;
  }

  void forceFakeLoginStateAsClient() {
    (context.container<TestIdentityStorage>())
      ..fakeUserSignOnState = SignOnStatus.client
      ..forceFakeUserSignOnState = true
      ..isInsider = false;
  }

  void forceFakeLoginStateAsClientAsInsider() {
    (context.container<TestIdentityStorage>())
      ..fakeUserSignOnState = SignOnStatus.client
      ..forceFakeUserSignOnState = true
      ..isInsider = true;
  }

  void toggleLocationServiceFake({bool isEnabled = true}) {
    (context.container<ILocationService>() as TestFakeLocationService).isFake = isEnabled;
  }

  void resetFaceliftFlags({required bool facelifted}) {
    toggleFeatureFlag(featureName: FeatureFlag.loanCardsNew, isEnabled: facelifted);
    toggleFeatureFlag(featureName: FeatureFlag.loanCardsHideOld, isEnabled: facelifted);
  }

  void toggleFeatureFlag({
    required String featureName,
    required bool isEnabled,
    String? value,
  }) {
    (context.container<IFeatureFlagRepository>() as TestFeatureFlagRepository).overrideValueForTest(
      featureName: featureName,
      enabled: isEnabled,
      value: value,
    );
  }

  /// Toggle the fake user number, whether to use the one defined
  /// in fake_token_api or not
  void toggleFakeUserNumber({
    bool isForTokenApi = false,
    bool isForVietnam = false,
  }) {
    (context.container<TestIdentityStorage>())
      ..isFakeUserForTokenApi = isForTokenApi
      ..isForVietnam = isForVietnam;
  }

  /// Skip directly to LockScreen
  Future<void> navigateToLockScreen() async {
    logger.i('Navigate to the Lock screen.');
    // ignore: deprecated_member_use
    context.configuration.navigator?.pushReplacementFromPackage(package: 'CappPersonal', screen: 'LockScreen');
    await tester.pumpAndSettle();
  }

  void toggleShortInactivityThreshold({bool isEnabled = true}) {
    (context.container<IFeatureFlagRepository>() as TestFeatureFlagRepository).overrideValueForTest(
      featureName: KoyalFeatureFlag.cappInactivityLockThresholdSeconds,
      enabled: true,
      value: '20',
    );
  }

  Future<void> navigateToBankVerification() async {
    AnalysisProvidersList getProviderList() {
      return const AnalysisProvidersList(
        analysisProviders: [
          AnalysisProviders(provider: 'YODLEE', type: 'NETBANKING', priority: 0),
          AnalysisProviders(provider: 'PERFIOS', type: 'NETBANKING', priority: 1),
          AnalysisProviders(provider: 'PERFIOS', type: 'PDFSTATEMENT', priority: 2),
        ],
      );
    }

    logger.i('Navigate to bank verification screen.\n');
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappBankValidation',
      'BankValidationScreen',
      'CappHome',
      'MainScreen',
      arguments: BankValidationScreenArguments(
        onSuccessCb: () {},
        currentStep: 1,
        totalStep: 2,
        applicationId: ***************,
        loanTypeText: 'Emi Loan',
        analysysProvider: AnalysysProvider.perfios,
        analysisProvidersList: getProviderList(),
        validationRetryCount: 3,
        onCancelJourney: () {},
        loanType: 'ujjwal_card',
        productType: '',
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToBankAnalysisScreen() async {
    logger.i('Navigate to bank verification screen.\n');
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappBankValidation',
      'BankValidationManualEntryScreen',
      'CappHome',
      'MainScreen',
      arguments: BankValidationManualEntryScreenArguments(
        bankAnalysisResponse: const BankAnalysisResponseModel(
          itemId: '',
          itemAccountId: '',
          perfiosTransactionId: '',
          bankName: 'ALLAHABAD BANK',
          ifsc: 'ALLA0001572',
          accountNumber: '***********',
          holderName: 'Raghav Kumar',
          analysisStatus: '',
          errorCode: '',
          reason: '',
          bankCode: '',
          micr: '*********',
          siteRefreshStatus: '',
        ),
        analysisProviders: AnalysysProvider.perfios,
        onSuccessCb: () {},
        applicationId: 123456,
        onCancelJourney: () {},
        loanType: 'ujjwal_card',
        productType: '',
        skipVerification: true,
        propertyUserType: '',
      ),
    );
    await tester.pumpAndSettle();
  }

  void toggleUserAccountStatus({bool passwordChangeRequired = false}) {
    (context.container<UserApi>() as FakeIdentityUserApi).testPasswordChangeRequired = passwordChangeRequired;
  }

  Future<void> navigateToPaymentGateway() async {
    logger.i('Navigate to payment gateway screen.\n');
    context.configuration.navigator?.push(
      path: NavigatorPath.cappSelfService.paymentModeSelectionScreen,
      arguments: PaymentGateWayRouteArguments(
        name: 'Test Driver',
        contractType: ContractType.unknown,
        email: '<EMAIL>',
        phoneNumber: '**********',
        showPencilButton: true,
        contractIds: [**********],
        currency: 'INR',
        loanAmount: Decimal.parse('20000'),
        loanType: 'Acl/Xcl loan',
      ),
    );
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  Future<void> navigateToPaymentSuccessFailureScreen() async {
    logger.i('Navigate to payment gateway screen.\n');
    context.configuration.navigator?.push(
      path: NavigatorPath.cappSelfService.paymentSuccessFaliureScreen,
      arguments: PaymentSuccessFailureRouteArguments(
        contractIds: **********,
        contractType: ContractType.flexi,
        loanAmount: Decimal.parse('20000'),
        loanType: 'Acl/Xcl loan',
        paymentDateTime: DateTime.parse('2020-08-31T06:53:26.757Z').toString(),
        transactionId: 'HCGA16644453211881211',
        transactionMode: 'debit card',
        isSuccess: true,
      ),
    );
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }

  void toggleCappForgotUsernameInvalidCredentials({bool returnInvalidCredentials = false}) {
    context.container<TestingRepository>().cappForgotUsernameApiInvalidCredentials = returnInvalidCredentials;
  }

  /// Change Response code - Initial Reset Password Session
  ///
  /// 400 = Invalid username format,
  /// 429 = Recovery session starts limit exceeded
  void changeV2ResetPasswordSessionResponseCode([int responseCode = 200]) {
    context.container<TestingRepository>().cappV2ResetPasswordResponseCode = responseCode;
  }

  /// Change Response code - Reset Password Complete
  ///
  /// 400 = Session not verified,
  /// 404 = Session Not Found,
  /// 409 = Password does not meet security criteria
  void changeV2ResetPasswordCompleteResponseCode([int responseCode = 204]) {
    context.container<TestingRepository>().cappV2ResetPasswordCompleteResponseCode = responseCode;
  }

  /// Change Response Code - Reset Password Second Identifier
  ///
  /// 400 = Bad Request,
  /// 404 = User or Session Not Found,
  /// 429 = OTP attempts count exceeded limit
  void changeV2ResetPasswordSecondIdrResponseCode([int responseCode = 204]) {
    (context.container<TestingRepository>()).cappV2ResetPasswordSecondIdResponseCode = responseCode;
  }

  void toggleUsernameSecondIdentifierInvalidCredentials({bool returnInvalidCredentials = false}) {
    context.container<TestingRepository>().usernameVerificationSecondIdInvalidCredentials = returnInvalidCredentials;
  }

  Future<void> simulateTapOnLogoutNotification() async {
    final messagingRepository = context.container<IKoyalMessagingRepository>() as TestMessagingRepository;
    await messagingRepository.tapFakeLocalNotification(
      NotificationMessage(
        id: '666',
        createdAt: DateTime.now().toUtc(),
        notification: MessageNotificationPart(
          title: 'Logout',
          body: 'Another Device',
        ),
        data: MessageDataPart(
          notificationType: MessageType.authSignedInOnOtherDevice,
          payload: <String, dynamic>{'notificationType': 'auth.SignedInOnOtherDevice'},
        ),
      ),
    );
  }

  Future<void> simulateTapOnPhoneUpdatedLogoutNotification() async {
    final messagingRepository = context.container<IKoyalMessagingRepository>() as TestMessagingRepository;
    await messagingRepository.tapFakeLocalNotification(
      NotificationMessage(
        id: '666',
        createdAt: DateTime.now().toUtc(),
        notification: MessageNotificationPart(
          title: 'Logout',
          body: 'Phone Updated',
        ),
        data: MessageDataPart(
          notificationType: MessageType.authLogoutPhoneUpdated,
          payload: <String, dynamic>{'notificationType': 'auth.LogoutPhoneUpdated'},
        ),
      ),
    );
  }

  void enableFourDigitPin() {
    // use DEV remote config override
    (context.container<IPasswordLengthProvider>() as PasswordLengthProvider)
      ..demoRemoteConfigValue = 4
      ..passwordLengthInLastLogin = 4;
  }

  void enableSixDigitPin() {
    // use DEV remote config override
    (context.container<IPasswordLengthProvider>() as PasswordLengthProvider)
      ..demoRemoteConfigValue = 6
      ..passwordLengthInLastLogin = 6;
  }

  Future<void> navigateToRepaymentContractList() async {
    logger.i('Navigate to the repayment contract list screen.');
    context.configuration.navigator
        ?.pushNamedAndRemoveUntilFromPackage('CappRepayment', 'RepaymentContractListScreen', 'CappHome', 'MainScreen');
    await tester.pumpAndSettle();
  }

  Future<void> navigateToRepaymentMethodSelection() async {
    logger.i('Navigate to the repayment method selection screen.');
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappRepayment',
      'RepaymentMethodSelectionScreen',
      'CappHome',
      'MainScreen',
      arguments: RepaymentMethodSelectionRouteArgs(
        selectedContract: RepaymentContract(
          contractType: RepaymentContractType.cel,
          loanContract: RepaymentLoanContract(
            contractNumber: '**********',
            contractType: 'mobile',
            loanType: 'mobile',
            contractStatus: 'active',
            productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
            productName: 'Cash Loan',
            dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
            dueAmount: Decimal.parse('69000'),
          ),
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToRepaymentMain({required ScreenArguments routeArgs}) async {
    logger.i('Navigate to the repayment main screen.');
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappRepayment',
      'RepaymentMainScreen',
      'CappHome',
      'MainScreen',
      arguments: routeArgs,
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToBankingApp({required ScreenArguments routeArgs}) async {
    logger.i('Navigate to the banking app screen.');
    context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappRepayment',
      'RepaymentBankingAppScreen',
      'CappHome',
      'MainScreen',
      arguments: routeArgs,
    );
    await tester.pumpAndSettle();
  }

  void enableHoselPairingInRegistrationV3() {
    (context.container<IIdentityRepository>() as TestIdentityRepository).allowHoselPairing = true;
  }

  void changeUserLockStatusInHoselPairing({bool locked = true}) {
    context.container<TestingRepository>().cappHoselPairingLockedUser = locked;
  }

  void changeChangePhoneLoggedInFlag({bool loggedIn = true}) {
    context.container<TestingRepository>().cappChangePhoneLoggedIn = loggedIn;
  }

  void setChangePhoneForCuidUser({required int caseNo}) {
    switch (caseNo) {
      case 0:
        context.container<TestingRepository>().cappChangePhoneSessionId = 'cuid_user_success';
        break;
      case 1:
        context.container<TestingRepository>().cappChangePhoneSessionId = 'cuid_user_failure';
        break;
      case 2:
        context.container<TestingRepository>().cappChangePhoneSessionId = 'cuid_user_retry';
        break;
    }
  }

  void setChangeNationalIdSession({
    bool success = false,
    bool rejected = false,
  }) {
    if (success) {
      context.container<TestingRepository>().cappChangeNationalIdSessionId = 'success_session_id';
    } else if (rejected) {
      context.container<TestingRepository>().cappChangeNationalIdSessionId = 'rejected_session_id';
    } else {
      context.container<TestingRepository>().cappChangeNationalIdSessionId = 'failure_session_id';
    }
  }

  Future<void> navigateToLoanSignatureDemoScreen() async {
    logger.i('Navigate to the Loan Signature Demo screen.');
    context.configuration.navigator
        ?.pushReplacementFromPackage(package: 'CappLoanSignature', screen: 'LoanSignatureDemoScreen');
    await tester.pumpAndSettle();
  }

  Future<void> navigateToTransactionProductSummaryScreen() async {
    logger.i('Navigate to the Transaction Product Summary screen.');
    context.configuration.navigator
        ?.pushReplacementFromPackage(package: 'CappTransactionSignature', screen: 'TransactionSignatureDemoScreen');
    await tester.pumpAndSettle();
  }

  Future<void> navigateToCorrespondenceAddressScreen() async {
    logger.i('Navigate to the Correspondence Address screen.');
    context.configuration.navigator?.pushTyped(
      package: pending.CappAddressValidation,
      screen: pending.CorrespondenceAddressScreen,
      arguments: pending.CorrespondenceAddressScreenArguments(
        contractCode: '4200036269',
        loanType: '',
        pendingDocument: pending.PendingDocument(
          documentType: 'DRIVERS_LICENCE',
          documentId: '10649174',
        ),
        onCancel: () {},
        onSuccess: (message) {},
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToAddressValidationScreen() async {
    context.configuration.navigator?.pushTyped(
      package: pending.CappAddressValidation,
      screen: pending.AddressValidationScreen,
      arguments: pending.AddressValidationScreenArguments(
        documentId: '10649174',
        loanType: '',
        contractCode: '4200036269',
        onCancel: () {},
        onSuccess: (message) {},
      ),
    );
    await tester.pumpAndSettle();
  }

  void setMockPasswordSignService() {
    final c = context.container;
    if (c.isRegistered<IPasswordSignService>()) {
      c.unregister<IPasswordSignService>();
    }

    c.registerLazySingleton<IPasswordSignService>(() {
      return PasswordSignTestService(
        logger: c<Logger>(),
      );
    });
  }

  void setMockBigDataService() {
    final c = context.container;
    if (c.isRegistered<IBigDataCollectionService>()) {
      c.unregister<IBigDataCollectionService>();
    }
    if (c.isRegistered<BigDataCollectionBloc>()) {
      c.unregister<BigDataCollectionBloc>();
    }

    c
      ..registerLazySingleton<IBigDataCollectionService>(
        () => BigdataCollectionTestService(
          ipAddressService: c<IIpAddressService>(),
          locationService: c<ILocationService>(),
          platformService: c<IPlatformService>(),
          bigdataRepository: c<IBigdataRepository>(),
          permissionsService: c<IPermissionsService>(),
          logger: c<Logger>(),
        ),
      )
      ..registerFactory<BigDataCollectionBloc>(() {
        return BigDataCollectionBloc(
          logger: c<Logger>(),
          bigDataCollectionService: c<IBigDataCollectionService>(),
        );
      });
  }

  void setMockInnovatricsBloc() {
    final c = context.container;
    if (c.isRegistered<IdCardCoreBloc>()) {
      c.unregister<IdCardCoreBloc>();
    }

    c.registerTrackingFactory<IdCardCoreBloc>(() {
      return DriverTestIdCardCoreBloc(
        biometricsRepository: c<IBiometricsRepository>(),
        localizationRepository: c<ILocalizationRepository>(),
        featureFlagRepository: c<IFeatureFlagRepository>(),
        biometricsStorage: c<IBiometricsStorage>(),
        platformService: c<IPlatformService>(),
        trackingService: c<CappInnovatricsTrackingService>(),
        firebasePerformanceMonitoring: c<IFirebasePerformanceMonitoring>(),
      );
    });
  }

  Future<void> simulateIdCardScreenResult() async {
    context.configuration.navigator?.pop(
      IdCardResult(
        idNumber: 'idNubmer',
        gender: GenderType.male,
        isAddressSame: true,
        uploadedImageId: 'imageId',
      ),
    );
    await tester.pump();
    await tester.pump(const Duration(seconds: 1));
  }

  void changeAuthenticationOtpResponse([AuthenticationOtpResponse? authenticationOtpResponse]) {
    (context.container<IAuthenticationRepository>() as TestAuthenticationRepository).otpResponse =
        authenticationOtpResponse;
  }

  void changeAuthenticationVerify2ndIdResponse([
    AuthenticationSecondIdVerificationResponse? authenticationSecondIdVerificationResponse,
  ]) {
    (context.container<IAuthenticationRepository>() as TestAuthenticationRepository).secondIdVerificationResponse =
        authenticationSecondIdVerificationResponse;
  }

  void changeAuthenticationAuthenticateResultResponse([
    AuthenticationAuthenticateResultResponse? authenticateResultResponse,
  ]) {
    (context.container<IAuthenticationRepository>() as TestAuthenticationRepository).authenticateResultResponse =
        authenticateResultResponse;
  }

  void changeAuthenticationIdentifyResultResponse([
    AuthenticationIdentifyResultResponse? identifyResultResponse,
  ]) {
    (context.container<IAuthenticationRepository>() as TestAuthenticationRepository).identifyResultResponse =
        identifyResultResponse;
  }

  void setActionBeltScenario(TestContext context, ActionBeltTestScenario scenario) {
    (context.container<ActionBeltWidgetsApi>() as FakeActionBeltWidgetsApi).scenario = scenario;
  }

  void setPermission(PermissionStatus status) {
    PermissionHandlerPlatform.instance = TestPermissionHandlerPlatform(status: status);
  }
}
