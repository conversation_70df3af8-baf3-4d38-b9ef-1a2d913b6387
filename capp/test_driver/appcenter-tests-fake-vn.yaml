trigger: none

pool:
  vmImage: ubuntu-22.04
  demands: CanBuild -equals Mobile

variables:
  - template: ../../ci/variables.yml
  - template: ../ci/variables-prod.yml

stages:
  - stage: GenerateVersion
    variables:
      - template: ../../ci/variables.yml
    jobs:
      - template: ../../ci/version.yml
  - stage: test
    dependsOn: GenerateVersion
    variables:
      - template: ../../ci/variables.yml
      - name: version_buildname
        value: $[ stageDependencies.GenerateVersion.get_version.outputs['Outputs.VERSION_SEMVER'] ]
      - name: version_buildnumber
        value: $[ stageDependencies.GenerateVersion.get_version.outputs['Outputs.VERSION_BUILD_NUMBER'] ]
    jobs:
      - template: ../../ci/appcenter-tests-android.yml
        parameters:
          name: appcenter_tests_android
          rootFolder: $(ROOT_FOLDER)
          rootPackage: $(ROOT_PACKAGE)
          entryPoint: "lib/main_fake.dart"
          flavor: $(FAKE_VN_FLAVOR_NAME)
          testFile: "test_driver/integration_tests_fake_vn.dart"
          appcenterApp: "VN-SuperCupo-Android"
          appcenterToken: $(VN_APPCENTER_API_TOKEN)
          keystoreName: $(PROD_VN_KEYSTORE_NAME)
          keystorePassword: $(ANDROID_CAPP_VN_KEYSTORE_PASSWORD)
          keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
          logAnalyticsWorkspaceId: $(vnp_logworkspace_id)
          logAnalyticsPrimaryKey: $(vnp_logworkspace_primary_key)
