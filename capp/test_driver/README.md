# How to run tests

## Prerequisite

1.  When running on Android you need to have `ANDROID_HOME` or `ANDROID_SDK_ROOT` system variable setup correctly pointing to Android SDK. Usualy is path on windows: C:\Users\<USER>\AppData\Local\Android\Sdk

## Run

_Note:_ Following section refers to old flutter_driver integration tests. To see how to write tests using the new `integration_test` package see the section Integration Tests

### Run in the command line

1. Run AVD or attach a device
   - similar to this it is possible to run the emulated device from the terminal `flutter clean && emulator @10.1_WXGA_Tablet_API_28 -wipe-data`. Where `10.1_WXGA_Tablet_API_28` is device's name.
2. In a terminal, run:

   for test without backend (fake):
   `flutter drive --target test_driver/integration_fake_in.dart --flavor fakein --driver test_driver/all_tests_fake_test.dart`

   for test with backend (prod IN):
   `flutter drive --target test_driver/integration_prod_in.dart --flavor prodin --driver test_driver/all_tests_dev_test.dart`

   - or replace `all_tests_fake_test.dart` with single test file like `flutter drive --target test_driver/integration_fake_in.dart --flavor fakein --driver test_driver/tests_fake/wizard_test.dart`.

### Debug in VS Code

1. Launch app using Test Configuration.
2. Debug specific test using IDE debug button. This doesn't work for the file all_tests_fake_test.dart, so each test must be debugged separately.

### Run in Linux Docker image

1. `az login`
2. `az acr login --name intappsconreg`
3. `docker pull <Latest Linux Image>` - For example: `docker pull intappsconreg.azurecr.io/hci-koyal/hcd-flutter-linux:1.0.49`
4.

```
docker run -v <Your project path>:/t <Latest Linux Image> /bin/bash -c "set -ex; Xvfb :0 -screen 0 1824x1824x16 & cd /t; flutter packages get; flutter drive --target test_driver/integration_fake_in.dart --flavor fake --driver test_driver/all_tests_fake_test.dart
```

You need to replace `<Your project path>` and `<Latest Linux Image>`. For example:

```
docker run -v C:\Development\self-care-mobile:/t intappsconreg.azurecr.io/hci-koyal/hcd-flutter-linux:1.0.30 /bin/bash -c "set -ex; Xvfb :0 -screen 0 1824x1824x16 & cd /t; flutter packages get; flutter drive --target test_driver/integration_fake_in.dart --flavor fake --driver test_driver/all_tests_fake_test.dart
```

### Run on Windows:

To run Flutter on desktop, you need to switch to a version from master channel, we use `1.22.0-12.4.pre`. Then you need to call:

```
flutter config --enable-linux-desktop --enable-macos-desktop --enable-windows-desktop
flutter drive --target test_driver/integration_fake_in.dart --flavor fake --driver test_driver/all_tests_fake_test.dart -d windows
```

## Integration Tests

There is a new library called `integration_test` which can produce self contained integration tests.

To run example integration tests run the following command:

`flutter drive --driver=test_driver/integration_test.dart --target=test_driver/integration_tests_fake_in_part1.dart --flavor fakein`.

`flutter drive --driver=test_driver/integration_test.dart --target=test_driver/integration_tests_fake_vn.dart --flavor fakevn`.

For the example tests see the `test_driver/integration_tests_fake/dynamic_avatar_tests.dart`.

Those tests can be run in the pipeline as linux tests and in the cloud. Right now we are running those tests only as an optional check during PR. See the `azure-pipelines-appcenter-tests.yaml`

Check the official announcement of this library https://medium.com/flutter/updates-on-flutter-testing-f54aa9f74c7e

### Migration

The plan is to migrate all the flutter driver tests to new integration tests. We can have both mechanisms side by side.

1. You should create a copy of the existing page object in the `integration_page_objects` folder and rewrite the implementation
2. Write a test similar to the `test_driver/integration_tests_fake/dynamic_avatar_tests.dart`
3. Register the test in `integration_tests_fake_in.dart` or `integration_tests_fake_vn.dart`

For general migration instructions see https://flutter.dev/docs/testing/integration-tests#migrating-from-flutter_driver

### Flavors

Use correct `AppRunner` instance for the `integrationTest`, you can use helper functions for specific falvor tests with predefined AppRunner `integrationTestFakeIn`, `integrationTestFakeVn`, `integrationTestProdIn`
and `integrationTestProdVn`. Currently available AppRunners ( `FakeInAppRunner`, `FakeVnAppRunner`, `ProdInAppRunner`, `ProdVnAppRunner` ).

### Known issues

1. There is also a problem when the tests fails the next won't execute - https://github.com/flutter/flutter/issues/75216
2. There is no way to get a screenshot right now - https://github.com/flutter/flutter/issues/51890
3. AppCenter can't measure duration of each test because we get the results all at once

### Debugging

To debug new integration tests use predefined `Capp Debug Int-Test Fake IN` configuration in launch.json.
It has predefined tests set to `capp/test_driver/integration_tests_fake_in.dart`. If you want to change this entry point to different tests you need to modify it directly in the `launch.json`.
