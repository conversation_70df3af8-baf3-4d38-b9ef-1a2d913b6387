import 'package:flutter_driver/flutter_driver.dart';
import 'package:test/test.dart';

import '../page_objects/page_objects.dart';
import '../page_objects/personal_faq_page.dart';
import '../tools/retriable_test.dart';
import '../tools/screenshot_service.dart';
import '../tools/test_group.dart';

const timeWait = 2;

void main() {
  testGroup(
    'Profile Documents Tests',
    (testContext) {
      late FlutterDriver driver;
      late ScreenshotService screenshotService;

      late OnboardingChooseLanguagePage onboardingPage;
      late OnboardingCarouselPage carouselPage;
      late CustomerProfilePage customerProfilePage;
      late MainTestPage mainTestPage;
      late PersonalMenuPage personalMenuPage;
      late PersonalMenuFaqPage personalFaqPage;

      setUpAll(() async {
        driver = testContext.driver();
        screenshotService = testContext.screenshotService();

        onboardingPage = OnboardingChooseLanguagePage(driver: driver, screenshotService: screenshotService);
        carouselPage = OnboardingCarouselPage(driver: driver, screenshotService: screenshotService);
        customerProfilePage = CustomerProfilePage(driver: driver, screenshotService: screenshotService);
        mainTestPage = MainTestPage(driver: driver, screenshotService: screenshotService);
        personalMenuPage = PersonalMenuPage(driver: driver, screenshotService: screenshotService);
        personalFaqPage = PersonalMenuFaqPage(driver: driver, screenshotService: screenshotService);
      });

      retriableTest(
        'FAQ test - Question 1',
        () async {
          await navigateFAQSteps(
            driver,
            onboardingPage,
            carouselPage,
            customerProfilePage,
            mainTestPage,
            personalMenuPage,
            personalFaqPage,
          );
          await personalFaqPage.tapQuestion0();
          await personalFaqPage.tapSubQuestion1();

          await screenshotService.takeScreenshot('faq-subQuestion-1');
        },
        timeout: const Timeout(
          Duration(minutes: 2),
        ),
      );

      retriableTest(
        'FAQ test - Question 2',
        () async {
          await navigateFAQSteps(
            driver,
            onboardingPage,
            carouselPage,
            customerProfilePage,
            mainTestPage,
            personalMenuPage,
            personalFaqPage,
          );
          await personalFaqPage.tapQuestion1();
          await personalFaqPage.tapSubQuestion2();

          await screenshotService.takeScreenshot('faq-subQuestion-2');
        },
        timeout: const Timeout(
          Duration(minutes: 2),
        ),
      );

      retriableTest(
        'FAQ test - Question 3',
        () async {
          await navigateFAQSteps(
            driver,
            onboardingPage,
            carouselPage,
            customerProfilePage,
            mainTestPage,
            personalMenuPage,
            personalFaqPage,
          );
          await personalFaqPage.tapQuestion2();

          await screenshotService.takeScreenshot('faq-question-3');

          expect(await personalFaqPage.isSubQuestion3bReady(), isTrue);

          await personalFaqPage.tapSubQuestion3a();

          await screenshotService.takeScreenshot('faq-subQuestion-3a');
        },
        timeout: const Timeout(
          Duration(minutes: 2),
        ),
      );

      retriableTest(
        'FAQ test - Question 4',
        () async {
          await navigateFAQSteps(
            driver,
            onboardingPage,
            carouselPage,
            customerProfilePage,
            mainTestPage,
            personalMenuPage,
            personalFaqPage,
          );
          await personalFaqPage.tapQuestion3();
          await personalFaqPage.tapSubQuestion4();

          await screenshotService.takeScreenshot('faq-subQuestion-4');
        },
        timeout: const Timeout(
          Duration(minutes: 2),
        ),
      );
    },
  );
}

Future<void> navigateFAQSteps(
  FlutterDriver driver,
  OnboardingChooseLanguagePage onboardingPage,
  OnboardingCarouselPage carouselPage,
  CustomerProfilePage customerProfilePage,
  MainTestPage mainTestPage,
  PersonalMenuPage personalMenuPage,
  PersonalMenuFaqPage personalFaqPage,
) async {
  await driver.clearTimeline();
  await driver.requestData('disableFeatureFlagHelpCenter');
  expect(await onboardingPage.isReady(), isTrue);
  await onboardingPage.chooseEnglish();
  await onboardingPage.confirmLanguage();
  expect(await carouselPage.isReady(), isTrue);
  await driver.requestData('navigateHome');

  // delay for prevent error
  await Future.delayed(const Duration(seconds: timeWait), () {});

  // press Profile item in menu
  await mainTestPage.tapMainNavigationItemProfile();

  // delay for prevent error
  await Future.delayed(const Duration(seconds: timeWait), () {});

  // tap on faq row
  await driver.runUnsynchronized(() => customerProfilePage.tapOnFaqRow());

  expect(await personalFaqPage.isReady(), isTrue);

  expect(await personalFaqPage.isQuestion0Ready(), isTrue);
}
