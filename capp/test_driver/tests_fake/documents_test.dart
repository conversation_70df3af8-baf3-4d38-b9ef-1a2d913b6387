import 'package:flutter_driver/flutter_driver.dart';
import 'package:test/test.dart';

import '../page_objects/page_objects.dart';
import '../page_objects/personal_menu_documents_page.dart';
import '../tools/retriable_test.dart';
import '../tools/screenshot_service.dart';
import '../tools/test_group.dart';

const timeWait = 2;

void main() {
  testGroup(
    'Profile Documents Tests',
    (testContext) {
      late FlutterDriver driver;
      late ScreenshotService screenshotService;

      late OnboardingChooseLanguagePage onboardingPage;
      late OnboardingCarouselPage carouselPage;
      late CustomerProfilePage customerProfilePage;
      late MainTestPage mainTestPage;
      late PersonalMenuDocumentsPage personalDocumentsPage;

      setUpAll(() async {
        driver = testContext.driver();
        screenshotService = testContext.screenshotService();

        onboardingPage = OnboardingChooseLanguagePage(driver: driver, screenshotService: screenshotService);
        carouselPage = OnboardingCarouselPage(driver: driver, screenshotService: screenshotService);
        customerProfilePage = CustomerProfilePage(driver: driver, screenshotService: screenshotService);
        mainTestPage = MainTestPage(driver: driver, screenshotService: screenshotService);
        personalDocumentsPage = PersonalMenuDocumentsPage(driver: driver, screenshotService: screenshotService);
      });

      retriableTest(
        'Documents test - General Terms and Conditions',
        () async {
          await driver.clearTimeline();
          expect(await onboardingPage.isReady(), isTrue);
          await onboardingPage.chooseEnglish();
          await onboardingPage.confirmLanguage();
          expect(await carouselPage.isReady(), isTrue);
          await driver.requestData('navigateHome');

          // delay for prevent error
          await Future.delayed(const Duration(seconds: timeWait), () {});

          // press Profile item in menu
          await mainTestPage.tapMainNavigationItemProfile();

          // delay for prevent error
          await Future.delayed(const Duration(seconds: timeWait), () {});

          // tap on documents row
          await driver.runUnsynchronized(() => customerProfilePage.tapOnDocumentsRow());

          expect(await personalDocumentsPage.isReady(), isTrue);

          await personalDocumentsPage.tapGeneralTermsDocument();
          await screenshotService.takeScreenshot('document-general-terms');
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
      retriableTest(
        'Documents test - Privacy Policy',
        () async {
          await driver.clearTimeline();
          expect(await onboardingPage.isReady(), isTrue);
          await onboardingPage.chooseEnglish();
          await onboardingPage.confirmLanguage();
          expect(await carouselPage.isReady(), isTrue);
          await driver.requestData('navigateHome');

          // delay for prevent error
          await Future.delayed(const Duration(seconds: timeWait), () {});

          // press Profile item in menu
          await mainTestPage.tapMainNavigationItemProfile();

          // delay for prevent error
          await Future.delayed(const Duration(seconds: timeWait), () {});

          // tap on documents row
          await driver.runUnsynchronized(() => customerProfilePage.tapOnDocumentsRow());

          expect(await personalDocumentsPage.isReady(), isTrue);

          await personalDocumentsPage.tapPolicyDocument();
          await screenshotService.takeScreenshot('document-policy');
        },
        timeout: const Timeout(
          Duration(minutes: 2),
        ),
      );
    },
  );
}
