import 'package:integration_test/integration_test.dart';

import 'capp_in/integration_tests_prod/homepage_tests.dart' as homepage_tests;
import 'capp_in/integration_tests_prod/push_notification_tests.dart' as push_notification;
// import 'capp_in/integration_tests_prod/loan_origination/loan_origination_new_user_tests.dart'
//     as loan_origination_new_user_tests;
// import 'integration_tests_prod/loan_origination/residential_address_tests.dart' as residential_address_tests;
// import 'capp_in/integration_tests_prod/temp_example_test.dart'
//     as temp_example_tests;

// import 'integration_tests_prod/blog_tests.dart' as blog_tests;
// import 'integration_tests_prod/change_language_tests.dart'
//    as change_language_tests;
// import 'integration_tests_prod/documents_tests.dart' as documents_tests;
// import 'integration_tests_prod/feature_flags_tests.dart' as feature_flags_tests;
// import 'integration_tests_prod/logout_tests.dart' as logout_tests;
// import 'integration_tests_prod/recover_password_tests.dart'
//     as recover_password_tests;
// import 'integration_tests_prod/sign_in_tests.dart' as sign_in_tests;
// import 'integration_tests_prod/sign_up_existing_tests.dart'
//     as sign_up_existing_tests;
// import 'integration_tests_prod/sign_up_insider_tests.dart'
//     as sign_up_insider_tests;
// import 'integration_tests_prod/sign_up_wrong_otp_x_tests.dart'
//     as sign_up_wrong_otp_x_tests;
// import 'integration_tests_prod/sign_up_x_tests.dart' as sign_up_x_tests;
// import 'integration_tests_prod/wizard_tests.dart' as wizard_tests;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  // var testSettings = TestSettings(
  //   isInsiderUser: true,
  //   userId: '',
  //   isUserIdOverride: true,
  //   haveTestHeader: false,
  // );
  // temp_example_tests.run(testSettings);
  homepage_tests.main();
  push_notification.main();
  // loan_origination_new_user_tests.main();
  //change_language_tests.main();

  // residential_address_tests.main();

  // @TODO check and enable tests

  // documents_tests.main();
  // feature_flags_tests.main();

  // logout_tests.main();
  // recover_password_tests.main();
  // sign_in_tests.main();
  // sign_up_existing_tests.main();
  // sign_up_insider_tests.main();
  // sign_up_wrong_otp_x_tests.main();
  // sign_up_x_tests.main();
  // wizard_tests.main(appRunner);

  // Check why no blog is present on the home screen
  // blog_tests.main();
}
