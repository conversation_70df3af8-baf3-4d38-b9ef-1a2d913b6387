import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'category_chips.dart';
import 'page_objects.dart';
import 'test_page.dart';

class CommunityDealsPage extends TestPage {
  static const double scrollOffset = 200.0;

  final _communityScreenFinder = find.byValue<PERSON>ey('__communityScreen__');
  final _communityDealsScrollViewFinder = find.byValue<PERSON>ey('__communityDealsScrollView__');
  // AppBar - Back button
  final _appBarBackButtonFinder = find.descendant(
    of: find.descendant(
      of: find.byValue<PERSON>ey('__communityDealsAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
    firstMatchOnly: true,
  );
  final _appBarBackButtonAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValue<PERSON><PERSON>('__communityDealsAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
  );

  // General Product
  final _productItemFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afp1__'),
    firstMatchOnly: true,
  );
  final _productItemAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afp1__'),
  );
  // Product - Buy online only
  final _productBuyOnlineOnlyItemFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afc2__'),
    firstMatchOnly: true,
  );
  final _productBuyOnlineOnlyItemAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afc2__'),
  );
  // Product - Buy nearby only
  final _productBuyNearbyOnlyItemFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afp5__'),
    firstMatchOnly: true,
  );
  final _productBuyNearbyOnlyItemAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afp5__'),
  );
  // Product - No buy options
  final _productNoBuyOptionsItemFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afp6__'),
    firstMatchOnly: true,
  );
  final _productNoBuyOptionsItemAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-3fa85f64-5717-4562-b3fc-2c963f66afp6__'),
  );
  // Product - Discount banner
  final _productDiscountBannerFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealItemDiscountBanner-3fa85f64-5717-4562-b3fc-2c963f66afp1__'),
    firstMatchOnly: true,
  );
  // Product original price
  final _productOriginalPriceFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealItemOriginalPrice-3fa85f64-5717-4562-b3fc-2c963f66afp1__'),
  );
  // Product discount price
  final _productDiscountPriceFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealItemDiscountPrice-3fa85f64-5717-4562-b3fc-2c963f66afp1__'),
  );
  // Product emi original price
  final _productEmiOriginalPriceFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealItemEmiOriginalPrice-3fa85f64-5717-4562-b3fc-2c963f66afp1__'),
  );
  // Product emi discount price
  final _productEmiDiscountPriceFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealItemEmiDiscountPrice-3fa85f64-5717-4562-b3fc-2c963f66afp1__'),
  );
  // General Coupon
  final _couponItemFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-073c56c6-a7f1-4320-bc51-c4754d900817__'),
    firstMatchOnly: true,
  );
  final _couponItemAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__dealNavigatorProductItem-073c56c6-a7f1-4320-bc51-c4754d900817__'),
  );
  // search bar
  final _searchBarFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__communityDealsSearchBar__'),
    firstMatchOnly: true,
  );
  final _searchBarAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__communityDealsSearchBar__'),
  );
  // search bar - input
  final _searchBarTextInputFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarTextInput__'),
    firstMatchOnly: true,
  );
  final _searchBarTextInputAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarTextInput__'),
  );
  // search bar - reset button
  final _searchBarResetFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarReset__'),
    firstMatchOnly: true,
  );
  final _searchBarResetAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarReset__'),
  );
  // suggestions
  final _searchSuggestionsFinder = find.byValueKey('__communityScreenDealsSearchSuggestions__');
  // suggestion - first
  final _searchSuggestionFirstFinder = find.descendant(
    of: find.byValueKey('__communityScreenDealsSearchSuggestions__'),
    matching: find.byValueKey(
      '__communityDealSuggestionTitle-073c56c6-a7f1-4320-bc51-c4754d900817__',
    ),
    firstMatchOnly: true,
  );
  final _searchSuggestionFirstAbsentFinder = find.descendant(
    of: find.byValueKey('__communityScreenDealsSearchSuggestions__'),
    matching: find.byValueKey(
      '__communityDealSuggestionTitle-073c56c6-a7f1-4320-bc51-c4754d900817__',
    ),
  );
  // category filter
  final _categoryFilterFinder = find.byType('CategoryFilter');
  // category filter - list view
  final _categoryFilterChipListViewFinder = find.descendant(
    of: find.byType('CategoryFilter'),
    matching: find.byValueKey('__categoryFilterListView__'),
    firstMatchOnly: true,
  );
  final _categoryFilterChipListViewAbsentFinder = find.descendant(
    of: find.byType('CategoryFilter'),
    matching: find.byValueKey('__categoryFilterListView__'),
  );
  // category filter - chip - Top offers
  final _categoryFilterChipKeyTopOffers = 'topOffers';
  // category filter - chip - Mobile
  final _categoryFilterChipKeyMobile = '02c75466-6288-4317-b646-3ce6eea77658';
  // category filter - chip - Electronics
  final _categoryFilterChipKeyElectronics = '24682114-5277-4320-9e08-e50ea7153a5c';
  // category filter - chip - Led TVs
  final _categoryFilterChipKeyLedTvs = '98eee1ae-8090-4c84-a905-f87f689e130c';
  // category filter - chip - Refrigenerators
  final _categoryFilterChipKeyRefrigenerators = 'ea6f2df4-a0c9-4994-a18b-069db545c35';
  // category filter - chip - Washing machines
  final _categoryFilterChipKeyWashingMachines = '7f9b18a3-9cb7-489f-91f4-d35ff0d6e56b';

  CommunityDealsPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  SerializableFinder _getCategoryFilterChipFinder(CategoryChip type, {bool isAbsentFinder = false}) {
    var chipKey = '';
    switch (type) {
      case CategoryChip.electronics:
        chipKey = _categoryFilterChipKeyElectronics;
        break;
      case CategoryChip.ledTvs:
        chipKey = _categoryFilterChipKeyLedTvs;
        break;
      case CategoryChip.mobile:
        chipKey = _categoryFilterChipKeyMobile;
        break;
      case CategoryChip.refrigenerators:
        chipKey = _categoryFilterChipKeyRefrigenerators;
        break;
      case CategoryChip.topOffers:
        chipKey = _categoryFilterChipKeyTopOffers;
        break;
      case CategoryChip.washingMachines:
        chipKey = _categoryFilterChipKeyWashingMachines;
        break;
      default:
    }
    return find.descendant(
      of: find.byType('CategoryFilter'),
      matching: find.byValueKey('__categoryFilterChip-${chipKey}__'),
      firstMatchOnly: !isAbsentFinder,
    );
  }

  SerializableFinder _getCategoryFilterChipTextFinder(CategoryChip type, {bool isAbsentFinder = false}) {
    return find.descendant(
      of: _getCategoryFilterChipFinder(type, isAbsentFinder: isAbsentFinder),
      matching: find.byType('Text'),
      firstMatchOnly: !isAbsentFinder,
    );
  }

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_communityScreenFinder, timeout: timeout);
  }

  Future<bool> isAbsent({Duration? timeout}) {
    return widgetAbsent(
      _communityScreenFinder,
      timeout: timeout,
    );
  }

  // App bar - back button

  Future<bool> isAppBarBackButtonReady({Duration? timeout}) {
    return widgetExists(_appBarBackButtonFinder, timeout: timeout);
  }

  Future<bool> isAppBarBackButtonAbsent({Duration? timeout}) {
    return widgetAbsent(
      _appBarBackButtonAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> tapAppBarBackButton({Duration? timeout}) {
    return tap(_appBarBackButtonFinder, timeout: timeout);
  }

  // Community Deals - product

  Future<bool> isProductReady({Duration? timeout}) {
    return widgetExists(_productItemFinder, timeout: timeout);
  }

  Future<bool> isProductAbsent({Duration? timeout}) {
    return widgetAbsent(
      _productItemAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> scrollToProduct({
    Duration? timeout,
    bool reverse = false,
  }) {
    return scrollUntilVisible(
      _communityDealsScrollViewFinder,
      _productItemFinder,
      timeout: timeout,
      dyScroll: reverse ? scrollOffset : -scrollOffset,
    );
  }

  Future<void> tapProductItem({Duration? timeout}) {
    return tap(_productItemFinder, timeout: timeout);
  }

  // Product - Buy online only

  Future<bool> isProductBuyOnlineOnlyReady({Duration? timeout}) {
    return widgetExists(_productBuyOnlineOnlyItemFinder, timeout: timeout);
  }

  Future<bool> isProductBuyOnlineOnlyAbsent({Duration? timeout}) {
    return widgetAbsent(
      _productBuyOnlineOnlyItemAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> scrollToProductBuyOnlineOnly({
    Duration? timeout,
    bool reverse = false,
  }) {
    return scrollUntilVisible(
      _communityDealsScrollViewFinder,
      _productBuyOnlineOnlyItemFinder,
      timeout: timeout,
      dyScroll: reverse ? scrollOffset : -scrollOffset,
    );
  }

  Future<void> tapProductBuyOnlineOnly({Duration? timeout}) {
    return tap(_productBuyOnlineOnlyItemFinder, timeout: timeout);
  }

  // Product - Buy nearby only

  Future<bool> isProductBuyNearbyOnlyReady({Duration? timeout}) {
    return widgetExists(_productBuyNearbyOnlyItemFinder, timeout: timeout);
  }

  Future<bool> isProductBuyNearbyOnlyAbsent({Duration? timeout}) {
    return widgetAbsent(
      _productBuyNearbyOnlyItemAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> scrollToProductBuyNearbyOnly({
    Duration? timeout,
    bool reverse = false,
  }) {
    return scrollUntilVisible(
      _communityDealsScrollViewFinder,
      _productBuyNearbyOnlyItemFinder,
      timeout: timeout,
      dyScroll: reverse ? scrollOffset : -scrollOffset,
    );
  }

  Future<void> tapProductBuyNearbyOnly({Duration? timeout}) {
    return tap(_productBuyNearbyOnlyItemFinder, timeout: timeout);
  }

  // Product - No buy options

  Future<bool> isProductNoBuyOptionsReady({Duration? timeout}) {
    return widgetExists(_productNoBuyOptionsItemFinder, timeout: timeout);
  }

  // Product - deal item discount banner
  Future<bool> isProductDiscountBannerReady({Duration? timeout}) {
    return widgetExists(_productDiscountBannerFinder, timeout: timeout);
  }

  // Product - deal item original price
  Future<bool> isProductOriginalPriceReady({Duration? timeout}) {
    return widgetExists(_productOriginalPriceFinder, timeout: timeout);
  }

  // Product - deal item discount price
  Future<bool> isProductDiscountPriceReady({Duration? timeout}) {
    return widgetExists(_productDiscountPriceFinder, timeout: timeout);
  }

  // Product - deal item emi original price
  Future<bool> isProductEmiOriginalPriceReady({Duration? timeout}) {
    return widgetExists(_productEmiOriginalPriceFinder, timeout: timeout);
  }

  // Product - deal item emi original price
  Future<bool> isProductEmiDiscountPriceReady({Duration? timeout}) {
    return widgetExists(_productEmiDiscountPriceFinder, timeout: timeout);
  }

  Future<bool> isProductNoBuyOptionsAbsent({Duration? timeout}) {
    return widgetAbsent(
      _productNoBuyOptionsItemAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> scrollToProductNoBuyOptions({
    Duration? timeout,
    bool reverse = false,
  }) {
    return scrollUntilVisible(
      _communityDealsScrollViewFinder,
      _productNoBuyOptionsItemFinder,
      timeout: timeout,
      dyScroll: reverse ? scrollOffset : -scrollOffset,
    );
  }

  Future<void> tapProductNoBuyOptions({Duration? timeout}) {
    return tap(_productNoBuyOptionsItemFinder, timeout: timeout);
  }

  // Community Deals - coupon

  Future<bool> isCouponReady({Duration? timeout}) {
    return widgetExists(_couponItemFinder, timeout: timeout);
  }

  Future<bool> isCouponAbsent({Duration? timeout}) {
    return widgetAbsent(
      _couponItemAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> scrollToCoupon({Duration? timeout}) async {
    await driver.scroll(_communityDealsScrollViewFinder, 0.0, -600.0, const Duration(milliseconds: 200));
    return scrollIntoView(_couponItemFinder, timeout: timeout);
  }

  Future<void> tapCouponItem({Duration? timeout}) {
    return tap(_couponItemFinder, timeout: timeout);
  }

  // search box

  Future<bool> isSearchBarReady({Duration? timeout}) {
    return widgetExists(_searchBarFinder, timeout: timeout);
  }

  Future<bool> isSearchBarAbsent({Duration? timeout}) {
    return widgetAbsent(_searchBarAbsentFinder, timeout: timeout);
  }

  // search bar - input

  Future<bool> isSearchBarTextInputReady({Duration? timeout}) {
    return widgetExists(_searchBarTextInputFinder, timeout: timeout);
  }

  Future<bool> isSearchBarTextInputAbsent({Duration? timeout}) {
    return widgetAbsent(_searchBarTextInputAbsentFinder, timeout: timeout);
  }

  Future<String> getTextSearchBarTextInput({Duration? timeout}) {
    return getText(_searchBarTextInputFinder, timeout: timeout);
  }

  Future<void> tapSearchBarTextInput({Duration? timeout}) {
    return tap(_searchBarTextInputFinder, timeout: timeout);
  }

  Future<void> enterTextSearchBarTextInput(String text, {Duration? timeout}) async {
    // focus input field
    await tapSearchBarTextInput(timeout: timeout);
    // enter the text
    return enterText(text, timeout: timeout);
  }

  // search bar - reset button

  Future<bool> isSearchBarResetButtonReady({Duration? timeout}) {
    return widgetExists(_searchBarResetFinder, timeout: timeout);
  }

  Future<bool> isSearchBarResetButtonAbsent({Duration? timeout}) {
    return widgetAbsent(_searchBarResetAbsentFinder, timeout: timeout);
  }

  Future<void> tapSearchBarResetButton({Duration? timeout}) {
    return tap(_searchBarResetFinder, timeout: timeout);
  }

  // suggestions

  Future<bool> isSearchSuggestionsReady({Duration? timeout}) {
    return widgetExists(_searchSuggestionsFinder, timeout: timeout);
  }

  Future<bool> isSearchSuggestionsAbsent({Duration? timeout}) {
    return widgetAbsent(_searchSuggestionsFinder, timeout: timeout);
  }

  // suggestions - first entity

  Future<bool> isSearchSuggestionFirstRowReady({Duration? timeout}) {
    return widgetExists(_searchSuggestionFirstFinder, timeout: timeout);
  }

  Future<bool> isSearchSuggestionFirstRowAbsent({Duration? timeout}) {
    return widgetAbsent(_searchSuggestionFirstAbsentFinder, timeout: timeout);
  }

  // category filter

  Future<bool> isCategoryFilterReady({Duration? timeout}) {
    return widgetExists(_categoryFilterFinder, timeout: timeout);
  }

  Future<bool> isCategoryFilterAbsent({Duration? timeout}) {
    return widgetAbsent(_categoryFilterFinder, timeout: timeout);
  }

  // category filter - list view

  Future<bool> isCategoryFilterListViewReady({Duration? timeout}) {
    return widgetExists(_categoryFilterChipListViewFinder, timeout: timeout);
  }

  Future<bool> isCategoryFilterListViewAbsent({Duration? timeout}) {
    return widgetAbsent(
      _categoryFilterChipListViewAbsentFinder,
      timeout: timeout,
    );
  }

  // category filter - chip - Top offers

  Future<bool> isCategoryFilterChipTopOffersReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(CategoryChip.topOffers),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipTopOffersAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        CategoryChip.topOffers,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipTopOffers({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(CategoryChip.topOffers),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipTopOffers({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(CategoryChip.topOffers),
      timeout: timeout,
    );
  }

  // category filter - chip - Mobile

  Future<bool> isCategoryFilterChipMobileReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(CategoryChip.mobile),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipMobileAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        CategoryChip.mobile,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipMobile({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(CategoryChip.mobile),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipMobile({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(CategoryChip.mobile),
      timeout: timeout,
    );
  }

  // category filter - chip - Electronics

  Future<bool> isCategoryFilterChipElectronicsReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(CategoryChip.electronics),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipElectronicsAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        CategoryChip.electronics,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipElectronics({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(CategoryChip.electronics),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipElectronics({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(CategoryChip.electronics),
      timeout: timeout,
    );
  }

  // category filter - chip - Led TVs

  Future<bool> isCategoryFilterChipLedTvsReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(CategoryChip.ledTvs),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipLedTvsAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        CategoryChip.ledTvs,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipLedTvs({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(CategoryChip.ledTvs),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipLedTvs({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(CategoryChip.ledTvs),
      timeout: timeout,
    );
  }

  // category filter - chip - Refrigenerators

  Future<bool> isCategoryFilterChipRefrigeneratorsReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(CategoryChip.refrigenerators),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipRefrigeneratorsAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        CategoryChip.refrigenerators,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipRefrigenerators({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(CategoryChip.refrigenerators),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipRefrigenerators({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(CategoryChip.refrigenerators),
      timeout: timeout,
    );
  }

  // category filter - chip - Washing machines

  Future<bool> isCategoryFilterChipWashingMachinesReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(CategoryChip.washingMachines),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipWashingMachinesAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        CategoryChip.washingMachines,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipWashingMachines({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(CategoryChip.washingMachines),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipWashingMachines({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(CategoryChip.washingMachines),
      timeout: timeout,
    );
  }
}
