import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class PersonalMenuPage extends TestPage {
  final _appBarFinder = find.byValueKey('__personalMenuScreenAppBar__');

  final _listView = find.byType('ListView');

  final _inboxRow = find.byValueKey('__inboxRow__');
  final _editProfileRow = find.byValueKey('__editProfileRow__');
  final _logoutRow = find.byValueKey('__logoutRow__');
  final _loginRow = find.byValueKey('__loginRow__');
  final _editDocumentsRow = find.byValueKey('__editDocumentsRow__');
  final _faqRow = find.byValueKey('__faqRow__');
  final _rateAppRow = find.byValueKey('__rateAppRow__');
  final _changeLanguageRow = find.byValueKey('__selectLanguageRow__');

  final _demoMenuRow = find.text('Demo');
  final _devMenuRow = find.text('Development');
  final _notificationsDemoRow = find.byValueKey('__notificationsDemoRow__');
  final _featureFlagsDemoRow = find.byValueKey('__featureFlagsScreen__');
  final _selfieDriver = find.byValueKey('__selfie_voucher__');

  SerializableFinder personalMenuBackButtonFinder(String appBarName) => find.descendant(
        of: find.byValueKey(appBarName),
        matching: find.byType('KoyalIconButton'),
        firstMatchOnly: true,
      );

  PersonalMenuPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_appBarFinder);
  }

  Future<void> tapInboxRow({Duration? timeout}) {
    return tap(_inboxRow, timeout: timeout);
  }

  Future<bool> isInboxRowReady({Duration? timeout}) {
    return widgetExists(_inboxRow, timeout: timeout);
  }

  Future<void> tapEditProfileRow({Duration? timeout}) {
    return tap(_editProfileRow, timeout: timeout);
  }

  Future<void> tapEditDocumentsRow({Duration? timeout}) {
    return tap(_editDocumentsRow, timeout: timeout);
  }

  Future<void> tapFaqRow({Duration? timeout}) {
    return tap(_faqRow, timeout: timeout);
  }

  Future<void> tapLogoutRow({Duration? timeout}) {
    return tap(_logoutRow, timeout: timeout);
  }

  Future<void> tapLoginRow({Duration? timeout}) {
    return tap(_loginRow, timeout: timeout);
  }

  Future<void> tapRateAppRow({Duration? timeout}) {
    return tap(_rateAppRow, timeout: timeout);
  }

  Future<void> tapChangeLanguageRow({Duration? timeout}) {
    return tap(_changeLanguageRow, timeout: timeout);
  }

  Future<void> tapDevMenuRow({Duration? timeout}) {
    _scrollToItem(_devMenuRow);
    return tap(_devMenuRow, timeout: timeout);
  }

  Future<void> tapBackButton({Duration? timeout}) {
    return tap(_devMenuRow, timeout: timeout);
  }

  Future<void> tapDemoMenuRow({Duration? timeout}) {
    _scrollToItem(_demoMenuRow);
    return tap(_demoMenuRow, timeout: timeout);
  }

  Future<void> tapNotificationDemoRow({Duration? timeout}) {
    return tap(_notificationsDemoRow, timeout: timeout);
  }

  Future<void> tapFeatureFlagsRow({Duration? timeout}) {
    return tap(_featureFlagsDemoRow, timeout: timeout);
  }

  Future<void> tapSelfieRow({Duration? timeout}) {
    _scrollToItem(_selfieDriver);
    return tap(_selfieDriver, timeout: timeout);
  }

  //Scroll section
  Future<void> scrollToNotificationDemoRow({bool reverse = false, Duration? timeout}) {
    return _scrollToItem(_notificationsDemoRow, reverse: reverse);
  }

  Future<void> scrollToLogoutRow({bool reverse = false, Duration? timeout}) {
    return _scrollToItem(_logoutRow, reverse: reverse);
  }

  //Item navigation
  Future<void> superCupoItemNavigateBack({required String appBarName, Duration? timeout}) {
    return tap(personalMenuBackButtonFinder(appBarName), timeout: timeout);
  }

  ///Private function for scrolling to item within Dev/Demo menu with standart parameters
  Future<void> _scrollToItem(
    SerializableFinder itemFinder, {
    Duration? timeout,
    bool reverse = false,
  }) {
    return scrollUntilVisible(
      _listView,
      itemFinder,
      timeout: timeout,
      dyScroll: reverse ? 200.0 : -200.0,
    );
  }
}
