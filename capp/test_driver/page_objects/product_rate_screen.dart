import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class ProductRateScreen extends TestPage {
  final _screenFinder = find.byValueKey('__productRateScreen__');

  // AppBar - Back button
  final _appBarBackButtonFinder = find.descendant(
    of: find.descendant(
      of: find.byValue<PERSON><PERSON>('__productRateScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
    firstMatchOnly: true,
  );
  final _appBarBackButtonAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__productRateScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
  );
  // AppBar - Title
  final _appBarTitleFinder = find.descendant(
    of: find.descendant(
      of: find.byVal<PERSON><PERSON><PERSON>('__productRateScreenAppBar__'),
      matching: find.byType('KoyalAppBarTitle'),
    ),
    matching: find.byType('Text'),
    firstMatchOnly: true,
  );
  final _appBarTitleAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__productRateScreenAppBar__'),
      matching: find.byType('KoyalAppBarTitle'),
    ),
    matching: find.byType('Text'),
    firstMatchOnly: true,
  );

  // Main text
  final _mainTextFinder =
      find.descendant(of: find.byValueKey('__productRateScreenText__'), matching: find.byType('Text'));
  // Button - Submit Rating
  final _submitRatingButtonFinder = find.byValueKey('__productRateScreenSubmitButton__');
  // Button - Submit Rating - Text
  final _submitRatingButtonTextFinder = find.descendant(
    of: find.byValueKey('__productRateScreenSubmitButton__'),
    matching: find.byType('Text'),
    firstMatchOnly: true,
  );
  final _submitRatingButtonTextAbsentFinder = find.descendant(
    of: find.byValueKey('__productRateScreenSubmitButton__'),
    matching: find.byType('Text'),
  );
  // Button - Rate Later
  final _rateLaterButtonFinder = find.byValueKey('__productRateScreenLaterButton__');
  // Button - Rate Later - Text
  final _rateLaterButtonTextFinder = find.descendant(
    of: find.byValueKey('__productRateScreenLaterButton__'),
    matching: find.byType('Text'),
    firstMatchOnly: true,
  );
  final _rateLaterButtonTextAbsentFinder = find.descendant(
    of: find.byValueKey('__productRateScreenLaterButton__'),
    matching: find.byType('Text'),
  );
  // Rating section
  final _ratingSectionFinder = find.byValueKey('__productRateScreenStars__');
  // Rating stars
  final _ratingStarsFinder = find.descendant(
    of: find.byValueKey('__productRateScreenStars__'),
    matching: find.byType('GFRating'),
    firstMatchOnly: true,
  );
  final _ratingStarsAbsentFinder = find.descendant(
    of: find.byValueKey('__productRateScreenStars__'),
    matching: find.byType('GFRating'),
  );
  // Product Rating - Stars - First star
  final _ratingFirstStarContentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__productRateScreenStars__'),
      matching: find.byType('GFRating'),
      firstMatchOnly: true,
    ),
    matching: find.byType('RichText'),
    firstMatchOnly: true,
  );
  final _ratingFirstStarContentAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__productRateScreenStars__'),
      matching: find.byType('GFRating'),
    ),
    matching: find.byType('RichText'),
  );

  ProductRateScreen({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_screenFinder, timeout: timeout);
  }

  Future<bool> isAbsent({Duration? timeout}) {
    return driver.runUnsynchronized(() {
      return widgetAbsent(
        _screenFinder,
        timeout: timeout,
      );
    });
  }

  // App bar - rate screen - back button

  Future<bool> isAppBarBackButtonReady({Duration? timeout}) {
    return widgetExists(_appBarBackButtonFinder, timeout: timeout);
  }

  Future<bool> isAppBarBackButtonAbsent({Duration? timeout}) {
    return widgetAbsent(
      _appBarBackButtonAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> tapAppBarBackButton({Duration? timeout}) {
    return tap(_appBarBackButtonFinder, timeout: timeout);
  }

  // AppBar - Title

  Future<bool> isAppBarTitleReady({Duration? timeout}) {
    return widgetExists(_appBarTitleFinder, timeout: timeout);
  }

  Future<bool> isAppBarTitleAbsent({Duration? timeout}) {
    return widgetAbsent(_appBarTitleAbsentFinder, timeout: timeout);
  }

  Future<String> getTextAppBarTitle({Duration? timeout}) {
    return getText(_appBarTitleFinder, timeout: timeout);
  }

  // Main text

  Future<bool> isMainTextReady({Duration? timeout}) {
    return widgetExists(_mainTextFinder, timeout: timeout);
  }

  Future<bool> isMainTextAbsent({Duration? timeout}) {
    return widgetAbsent(_mainTextFinder, timeout: timeout);
  }

  Future<String> getTextMainText({Duration? timeout}) {
    return getText(_mainTextFinder, timeout: timeout);
  }

  // Button - Submit Rating

  Future<bool> isButtonSubmitRatingReady({Duration? timeout}) {
    return widgetExists(_submitRatingButtonFinder, timeout: timeout);
  }

  Future<bool> isButtonSubmitRatingAbsent({Duration? timeout}) {
    return widgetAbsent(
      _submitRatingButtonFinder,
      timeout: timeout,
    );
  }

  Future<void> tapButtonSubmitRating({Duration? timeout}) {
    return tap(_submitRatingButtonFinder, timeout: timeout);
  }

  // Button - Submit Rating - Text

  Future<bool> isButtonSubmitRatingTextReady({Duration? timeout}) {
    return widgetExists(_submitRatingButtonTextFinder, timeout: timeout);
  }

  Future<bool> isButtonSubmitRatingTextAbsent({Duration? timeout}) {
    return widgetAbsent(_submitRatingButtonTextAbsentFinder, timeout: timeout);
  }

  Future<String> getTextButtonSubmitRatingText({Duration? timeout}) {
    return getText(_submitRatingButtonTextFinder, timeout: timeout);
  }

  // Button - Rate Later

  Future<bool> isButtonRateLaterReady({Duration? timeout}) {
    return widgetExists(_rateLaterButtonFinder, timeout: timeout);
  }

  Future<bool> isButtonRateLaterAbsent({Duration? timeout}) {
    return widgetAbsent(
      _rateLaterButtonFinder,
      timeout: timeout,
    );
  }

  Future<void> tapButtonRateLater({Duration? timeout}) {
    return tap(_rateLaterButtonFinder, timeout: timeout);
  }

  // Button - Rate Later - Text

  Future<bool> isButtonRateLaterTextReady({Duration? timeout}) {
    return widgetExists(_rateLaterButtonTextFinder, timeout: timeout);
  }

  Future<bool> isButtonRateLaterTextAbsent({Duration? timeout}) {
    return widgetAbsent(_rateLaterButtonTextAbsentFinder, timeout: timeout);
  }

  Future<String> getTextButtonRateLaterText({Duration? timeout}) {
    return getText(_rateLaterButtonTextFinder, timeout: timeout);
  }

  // Rating section

  Future<bool> isRatingSectionReady({Duration? timeout}) {
    return widgetExists(_ratingSectionFinder, timeout: timeout);
  }

  Future<bool> isRatingSectionAbsent({Duration? timeout}) {
    return widgetAbsent(
      _ratingSectionFinder,
      timeout: timeout,
    );
  }

  // Rating stars

  Future<bool> isRatingStarsSectionReady({Duration? timeout}) {
    return widgetExists(_ratingStarsFinder, timeout: timeout);
  }

  Future<bool> isRatingStarsSectionAbsent({Duration? timeout}) {
    return widgetAbsent(
      _ratingStarsAbsentFinder,
      timeout: timeout,
    );
  }

  // Rating stars - First star

  Future<bool> isRatingFirstStarContentReady({Duration? timeout}) {
    return widgetExists(_ratingFirstStarContentFinder, timeout: timeout);
  }

  Future<bool> isRatingFirstStarContentAbsent({Duration? timeout}) {
    return widgetAbsent(_ratingFirstStarContentAbsentFinder, timeout: timeout);
  }

  Future<String> getTextRatingFirstStarContent({Duration? timeout}) {
    return getText(_ratingFirstStarContentFinder, timeout: timeout);
  }

  Future<bool> isRatingFirstStarFull({Duration? timeout}) async {
    // STAR       - 0xe5f9 - 58873
    return await getTextRatingFirstStarContent(timeout: timeout) == String.fromCharCode(58873);
  }

  Future<bool> isRatingFirstStarEmpty({Duration? timeout}) async {
    // STARBORDER - 0xe5fa - 58874
    return await getTextRatingFirstStarContent(timeout: timeout) == String.fromCharCode(58874);
  }

  Future<bool> isRatingFirstStarHalfFull({Duration? timeout}) async {
    // STARHALF   - 0xe5fc - 58876
    return await getTextRatingFirstStarContent(timeout: timeout) == String.fromCharCode(58876);
  }
}
