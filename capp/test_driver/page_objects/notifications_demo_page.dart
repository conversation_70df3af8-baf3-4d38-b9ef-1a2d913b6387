import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class NotificationsDemoPage extends TestPage {
  final _notificationsDemoScreen = find.byValueKey('__notificationsDemoScreen__');
  final _titleInput = find.byValue<PERSON>ey('__notificationsTitleInput__');
  final _bodyInput = find.byValue<PERSON>ey('__notificationsBodyInput__');
  final _customValueInput = find.byValue<PERSON>ey('__notificationsCustomValueInput__');
  final _sendButton = find.byValueKey('__sendRealNotificationButton__');
  final _titleText = find.byValueKey('__lastNotificationTitle__');
  final _bodyText = find.byValueKey('__lastNotificationBody__');
  final _customValueText = find.byValue<PERSON>ey('__lastNotificationCustomValue__');
  final _backButton = find.byValue<PERSON>ey('__notificationsBackButton__');

  NotificationsDemoPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(driver: driver, screenshotService: screenshotService);

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_notificationsDemoScreen, timeout: timeout);
  }

  Future<void> enterNotificationTitle(String text) async {
    await tap(_titleInput);
    await enterText(text);
  }

  Future<void> enterNotificationBody(String text) async {
    await tap(_bodyInput);
    await enterText(text);
  }

  Future<void> enterNotificationCustomValue(String text) async {
    await tap(_customValueInput);
    await enterText(text);
  }

  Future<void> tapSendNotificationButton() {
    return tap(_sendButton);
  }

  Future<void> tapBackButton() {
    return tap(_backButton);
  }

  Future<String> getLastNotificationTitle({Duration? timeout}) {
    return getText(_titleText, timeout: timeout);
  }

  Future<String> getLastNotificationBody({Duration? timeout}) {
    return getText(_bodyText, timeout: timeout);
  }

  Future<String> getLastNotificationCustomValue({Duration? timeout}) {
    return getText(_customValueText, timeout: timeout);
  }
}
