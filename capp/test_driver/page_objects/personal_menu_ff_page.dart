// test files cannot indirectly import flutter libraries,
// import of this specific file is required

import 'package:capp_feature_flags/src/domain/feature_flag_names.dart';
import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class PersonalMenuFetureFlagsPage extends TestPage {
  final _ffListView = find.byValueKey('__featureFlagsListView__');
  final _ffFakeValue = find.byValueKey('__ff_${FeatureFlag.fakeDefaultSeed}_value__');

  PersonalMenuFetureFlagsPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return isListViewReady(timeout: timeout);
  }

  Future<bool> isListViewReady({Duration? timeout}) {
    return widgetExists(_ffListView);
  }

  Future<void> tapBack({Duration? timeout}) {
    final backButton = find.pageBack();
    return tap(backButton, timeout: timeout);
  }

  Future<bool> isFakeSeedReady({Duration? timeout}) {
    return widgetExists(_ffFakeValue);
  }

  Future<bool> getFakeSeedValue({Duration? timeout}) async {
    final value = await driver.getText(_ffFakeValue);
    return value == 'true';
  }

  Future<void> scrollToKey(String ffName) {
    final key = find.byValueKey('__ff_${ffName}__');
    return driver.scrollUntilVisible(
      _ffListView,
      key,
      dyScroll: -3000,
    );
  }
}
