import 'dart:async';

import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_element.dart';

abstract class TestPage extends TestElement {
  TestPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  Future<bool> isLoading({Duration? timeout}) async {
    return !(await isReady(timeout: timeout));
  }

  Future<bool> isReady({Duration? timeout});
}
