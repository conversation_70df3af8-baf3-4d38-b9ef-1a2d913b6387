import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class PersonalMenuDocumentsPage extends TestPage {
  final _documentGeneralTerms = find.byValueKey('__document0__');
  final _documentPolicy = find.byValueKey('__document1__');

  PersonalMenuDocumentsPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return isGeneralTermsDocumentReady(timeout: timeout);
  }

  Future<bool> isGeneralTermsDocumentReady({Duration? timeout}) {
    return widgetExists(_documentGeneralTerms);
  }

  Future<bool> isPolicyDocumentReady({Duration? timeout}) {
    return widgetExists(_documentPolicy);
  }

  Future<void> tapGeneralTermsDocument({Duration? timeout}) {
    return tap(_documentGeneralTerms, timeout: timeout);
  }

  Future<void> tapPolicyDocument({Duration? timeout}) {
    return tap(_documentPolicy, timeout: timeout);
  }
}
