import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class InstallmentSummaryScreen extends TestPage {
  final _screenFinder = find.byValueKey('__installmentSummaryScreen__');

  // AppBar - Back button
  final _appBarBackButtonFinder = find.descendant(
    of: find.descendant(
      of: find.byValue<PERSON>ey('__installmentSummaryScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
    firstMatchOnly: true,
  );
  final _appBarBackButtonAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__installmentSummaryScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
  );

  // Screen Title
  final _screenTitleFinder =
      find.descendant(of: find.byValue<PERSON>ey('__installmentSummaryScreenTitle__'), matching: find.byType('Text'));
  // Screen SubTitle
  final _screenSubTitleFinder =
      find.descendant(of: find.byValueKey('__installmentSummaryScreenSubTitle__'), matching: find.byType('Text'));

  // Button - RBI Disclaimer
  final _buttonRbiDisclaimerFinder = find.byValueKey('__installmentSummaryScreenButtonRbiDisclaimer__');
  // Button - Continue
  final _buttonContinueFinder = find.byValueKey('__installmentSummaryScreenButtonContinue__');

  SerializableFinder _signatureSummaryTileTitleFinder(
    SerializableFinder parent, {
    bool absent = false,
  }) {
    return find.descendant(
      of: find.descendant(
        of: find.byValueKey('__installmentSummaryScreenSignatureSummaryInfo__'),
        matching: parent,
      ),
      matching: find.descendant(of: find.byValueKey('__signatureSummaryTileTitle__'), matching: find.byType('Text')),
      firstMatchOnly: !absent,
    );
  }

  SerializableFinder _signatureSummaryTileValueFinder(
    SerializableFinder parent, {
    bool absent = false,
  }) {
    return find.descendant(
      of: find.descendant(
        of: find.byValueKey('__installmentSummaryScreenSignatureSummaryInfo__'),
        matching: parent,
      ),
      matching: find.descendant(of: find.byValueKey('__signatureSummaryTileValue__'), matching: find.byType('Text')),
      firstMatchOnly: !absent,
    );
  }

  // Signature Summary - Loan Number - Title
  SerializableFinder _summaryLoanNumberTitleFinder({bool absent = false}) {
    return _signatureSummaryTileTitleFinder(
      find.byValueKey('__signatureSummaryInfoLoanNumber__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Number - Value
  SerializableFinder _summaryLoanNumberValueFinder({bool absent = false}) {
    return _signatureSummaryTileValueFinder(
      find.byValueKey('__signatureSummaryInfoLoanNumber__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Amount - Title
  SerializableFinder _summaryLoanAmountTitleFinder({bool absent = false}) {
    return _signatureSummaryTileTitleFinder(
      find.byValueKey('__signatureSummaryInfoLoanAmount__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Amount - Value
  SerializableFinder _summaryLoanAmountValueFinder({bool absent = false}) {
    return _signatureSummaryTileValueFinder(
      find.byValueKey('__signatureSummaryInfoLoanAmount__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Full Name - Title
  SerializableFinder _summaryLoanFullNameTitleFinder({bool absent = false}) {
    return _signatureSummaryTileTitleFinder(
      find.byValueKey('__signatureSummaryInfoFullName__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Full Name - Value
  SerializableFinder _summaryLoanFullNameValueFinder({bool absent = false}) {
    return _signatureSummaryTileValueFinder(
      find.byValueKey('__signatureSummaryInfoFullName__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Birthdate - Title
  SerializableFinder _summaryLoanBirthdateTitleFinder({bool absent = false}) {
    return _signatureSummaryTileTitleFinder(
      find.byValueKey('__signatureSummaryInfoBirthdate__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Birthdate - Value
  SerializableFinder _summaryLoanBirthdateValueFinder({bool absent = false}) {
    return _signatureSummaryTileValueFinder(
      find.byValueKey('__signatureSummaryInfoBirthdate__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Address - Title
  SerializableFinder _summaryLoanAddressTitleFinder({bool absent = false}) {
    return _signatureSummaryTileTitleFinder(
      find.byValueKey('__signatureSummaryInfoAddress__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Address - Value
  SerializableFinder _summaryLoanAddressValueFinder({bool absent = false}) {
    return _signatureSummaryTileValueFinder(
      find.byValueKey('__signatureSummaryInfoAddress__'),
      absent: absent,
    );
  }

  // Signature Summary - Loan Additional Fees Title
  SerializableFinder _summaryLoanAdditionalFeesHeaderFinder({bool absent = false}) {
    // this is an header, row should only contain "value"
    return _signatureSummaryTileValueFinder(
      find.byValueKey('__signatureSummaryInfoAdditionalFeesTitle__'),
      absent: absent,
    );
  }

  // Signature Summary - Processing Fee - Title
  SerializableFinder _summaryLoanProcessingFeeTitleFinder({bool absent = false}) {
    return _signatureSummaryTileTitleFinder(
      find.byValueKey('__signatureSummaryInfoProcessingFee__'),
      absent: absent,
    );
  }

  // Signature Summary - Processing Fee - Value
  SerializableFinder _summaryLoanProcessingFeeValueFinder({bool absent = false}) {
    return _signatureSummaryTileValueFinder(
      find.byValueKey('__signatureSummaryInfoProcessingFee__'),
      absent: absent,
    );
  }

  InstallmentSummaryScreen({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_screenFinder, timeout: timeout);
  }

  Future<bool> isAbsent({Duration? timeout}) {
    return widgetAbsent(_screenFinder, timeout: timeout);
  }

  // App bar - back button

  Future<bool> isAppBarBackButtonReady({Duration? timeout}) {
    return widgetExists(_appBarBackButtonFinder, timeout: timeout);
  }

  Future<bool> isAppBarBackButtonAbsent({Duration? timeout}) {
    return widgetAbsent(_appBarBackButtonAbsentFinder, timeout: timeout);
  }

  Future<void> tapAppBarBackButton({Duration? timeout}) {
    return tap(_appBarBackButtonFinder, timeout: timeout);
  }

  // Screen Title
  Future<bool> isScreenTitleReady({Duration? timeout}) {
    return widgetExists(_screenTitleFinder, timeout: timeout);
  }

  Future<bool> isScreenTitleAbsent({Duration? timeout}) {
    return widgetAbsent(_screenTitleFinder, timeout: timeout);
  }

  Future<String> getTextScreenTitle({Duration? timeout}) {
    return getText(_screenTitleFinder, timeout: timeout);
  }

  // Screen SubTitle
  Future<bool> isScreenSubTitleReady({Duration? timeout}) {
    return widgetExists(_screenSubTitleFinder, timeout: timeout);
  }

  Future<bool> isScreenSubTitleAbsent({Duration? timeout}) {
    return widgetAbsent(_screenSubTitleFinder, timeout: timeout);
  }

  Future<String> getTextScreenSubTitle({Duration? timeout}) {
    return getText(_screenSubTitleFinder, timeout: timeout);
  }

  // Button - RBI Disclaimer
  Future<bool> isButtonRbiDisclaimerReady({Duration? timeout}) {
    return widgetExists(_buttonRbiDisclaimerFinder, timeout: timeout);
  }

  Future<bool> isButtonRbiDisclaimerAbsent({Duration? timeout}) {
    return widgetAbsent(_buttonRbiDisclaimerFinder, timeout: timeout);
  }

  Future<void> tapButtonRbiDisclaimer({Duration? timeout}) {
    return tap(_buttonContinueFinder, timeout: timeout);
  }

  // Button - Continue
  Future<bool> isButtonContinueReady({Duration? timeout}) {
    return widgetExists(_buttonContinueFinder, timeout: timeout);
  }

  Future<bool> isButtonContinueAbsent({Duration? timeout}) {
    return widgetAbsent(_buttonContinueFinder, timeout: timeout);
  }

  Future<void> tapButtonContinue({Duration? timeout}) {
    return tap(_buttonContinueFinder, timeout: timeout);
  }

  // Signature Summary - Loan Number - Title
  Future<bool> isSummaryLoanNumberTitleReady({Duration? timeout}) {
    return widgetExists(_summaryLoanNumberTitleFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanNumberTitleAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanNumberTitleFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanNumberTitle({Duration? timeout}) {
    return getText(_summaryLoanNumberTitleFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Number - Value
  Future<bool> isSummaryLoanNumberValueReady({Duration? timeout}) {
    return widgetExists(_summaryLoanNumberValueFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanNumberValueAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanNumberValueFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanNumberValue({Duration? timeout}) {
    return getText(_summaryLoanNumberValueFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Amount - Title
  Future<bool> isSummaryLoanAmountTitleReady({Duration? timeout}) {
    return widgetExists(_summaryLoanAmountTitleFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanAmountTitleAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanAmountTitleFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanAmountTitle({Duration? timeout}) {
    return getText(_summaryLoanAmountTitleFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Amount - Value
  Future<bool> isSummaryLoanAmountValueReady({Duration? timeout}) {
    return widgetExists(_summaryLoanAmountValueFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanAmountValueAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanAmountValueFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanAmountValue({Duration? timeout}) {
    return getText(_summaryLoanAmountValueFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Full Name - Title
  Future<bool> isSummaryLoanFullNameTitleReady({Duration? timeout}) {
    return widgetExists(_summaryLoanFullNameTitleFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanFullNameTitleAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanFullNameTitleFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanFullNameTitle({Duration? timeout}) {
    return getText(_summaryLoanFullNameTitleFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Full Name - Value
  Future<bool> isSummaryLoanFullNameValueReady({Duration? timeout}) {
    return widgetExists(_summaryLoanFullNameValueFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanFullNameValueAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanFullNameValueFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanFullNameValue({Duration? timeout}) {
    return getText(_summaryLoanFullNameValueFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Birthdate - Title
  Future<bool> isSummaryLoanBirthdateTitleReady({Duration? timeout}) {
    return widgetExists(_summaryLoanBirthdateTitleFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanBirthdateTitleAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanBirthdateTitleFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanBirthdateTitle({Duration? timeout}) {
    return getText(_summaryLoanBirthdateTitleFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Birthdate - Value
  Future<bool> isSummaryLoanBirthdateValueReady({Duration? timeout}) {
    return widgetExists(_summaryLoanBirthdateValueFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanBirthdateValueAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanBirthdateValueFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanBirthdateValue({Duration? timeout}) {
    return getText(_summaryLoanBirthdateValueFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Address - Title
  Future<bool> isSummaryLoanAddressTitleReady({Duration? timeout}) {
    return widgetExists(_summaryLoanAddressTitleFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanAddressTitleAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanAddressTitleFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanAddressTitle({Duration? timeout}) {
    return getText(_summaryLoanAddressTitleFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Address - Value
  Future<bool> isSummaryLoanAddressValueReady({Duration? timeout}) {
    return widgetExists(_summaryLoanAddressValueFinder(), timeout: timeout);
  }

  Future<bool> isSummaryLoanAddressValueAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanAddressValueFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanAddressValue({Duration? timeout}) {
    return getText(_summaryLoanAddressValueFinder(), timeout: timeout);
  }

  // Signature Summary - Loan Additional Fees Header
  Future<bool> isSummaryLoanAdditionalFeesHeaderReady({Duration? timeout}) {
    return widgetExists(
      _summaryLoanAdditionalFeesHeaderFinder(),
      timeout: timeout,
    );
  }

  Future<bool> isSummaryLoanAdditionalFeesHeaderAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanAdditionalFeesHeaderFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanAdditionalFeesHeader({Duration? timeout}) {
    return getText(_summaryLoanAdditionalFeesHeaderFinder(), timeout: timeout);
  }

  // Signature Summary - Processing Fee - Title
  Future<bool> isSummaryLoanProcessingFeeTitleReady({Duration? timeout}) {
    return widgetExists(
      _summaryLoanProcessingFeeTitleFinder(),
      timeout: timeout,
    );
  }

  Future<bool> isSummaryLoanProcessingFeeTitleAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanProcessingFeeTitleFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanProcessingFeeTitle({Duration? timeout}) {
    return getText(_summaryLoanProcessingFeeTitleFinder(), timeout: timeout);
  }

  // Signature Summary - Processing Fee - Value
  Future<bool> isSummaryLoanProcessingFeeValueReady({Duration? timeout}) {
    return widgetExists(
      _summaryLoanProcessingFeeValueFinder(),
      timeout: timeout,
    );
  }

  Future<bool> isSummaryLoanProcessingFeeValueAbsent({Duration? timeout}) {
    return widgetAbsent(
      _summaryLoanProcessingFeeValueFinder(absent: true),
      timeout: timeout,
    );
  }

  Future<String> getTextSummaryLoanProcessingFeeValue({Duration? timeout}) {
    return getText(_summaryLoanProcessingFeeValueFinder(), timeout: timeout);
  }
}
