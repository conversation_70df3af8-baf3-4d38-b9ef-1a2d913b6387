import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class PersonalMenuFaqPage extends TestPage {
  final _faqScreen = find.byValueKey('__faqScreen__');
  final _faqQuestion0 = find.byValue<PERSON>ey('__faq1__');
  final _faqQuestion1 = find.byValue<PERSON>ey('__faq2__');
  final _faqQuestion2 = find.byValue<PERSON>ey('__faq3__');
  final _faqQuestion3 = find.byValueKey('__faq4__');

  final _subFaq1 = find.byValueKey('__faq1__');
  final _subFaq2 = find.byValue<PERSON>ey('__faq2__');
  final _subFaq3a = find.byValue<PERSON>ey('__faq31__');
  final _subFaq3b = find.byValue<PERSON>ey('__faq32__');
  final _subFaq4 = find.byValue<PERSON><PERSON>('__faq4__');

  final _faqReadTheBlog = find.byValue<PERSON>ey('faqReadBlog');
  final _faqAskCommunity = find.byValue<PERSON>ey('faqAskCommunity');
  final _faqCheckServiceStatus = find.byValueKey('faqCheckServiceStatus');
  final _faqChatWithUs = find.byValueKey('faqChatWithUs');

  PersonalMenuFaqPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_faqScreen, timeout: timeout);
  }

  Future<bool> isQuestion0Ready({Duration? timeout}) {
    return widgetExists(_faqQuestion0);
  }

  Future<bool> isQuestion1Ready({Duration? timeout}) {
    return widgetExists(_faqQuestion1);
  }

  Future<bool> isQuestion2Ready({Duration? timeout}) {
    return widgetExists(_faqQuestion2);
  }

  Future<bool> isQuestion3Ready({Duration? timeout}) {
    return widgetExists(_faqQuestion3);
  }

  Future<bool> isSubQuestion1Ready({Duration? timeout}) {
    return widgetExists(_subFaq1);
  }

  Future<bool> isSubQuestion2Ready({Duration? timeout}) {
    return widgetExists(_subFaq2);
  }

  Future<bool> isSubQuestion3aReady({Duration? timeout}) {
    return widgetExists(_subFaq3a);
  }

  Future<bool> isSubQuestion3bReady({Duration? timeout}) {
    return widgetExists(_subFaq3b);
  }

  Future<bool> isSubQuestion4Ready({Duration? timeout}) {
    return widgetExists(_subFaq4);
  }

  Future<bool> isFaqBlogReady({Duration? timeout}) {
    return widgetExists(_faqReadTheBlog);
  }

  Future<bool> isFaqAskCommunityReady({Duration? timeout}) {
    return widgetExists(_faqAskCommunity);
  }

  Future<bool> isFaqCheckServiceReady({Duration? timeout}) {
    return widgetExists(_faqCheckServiceStatus);
  }

  Future<bool> isFaqChatReady({Duration? timeout}) {
    return widgetExists(_faqChatWithUs);
  }

  Future<void> tapQuestion0({Duration? timeout}) {
    return tap(_faqQuestion0, timeout: timeout);
  }

  Future<void> tapQuestion1({Duration? timeout}) {
    return tap(_faqQuestion1, timeout: timeout);
  }

  Future<void> tapQuestion2({Duration? timeout}) {
    return tap(_faqQuestion2, timeout: timeout);
  }

  Future<void> tapQuestion3({Duration? timeout}) {
    return tap(_faqQuestion3, timeout: timeout);
  }

  Future<void> tapSubQuestion1({Duration? timeout}) {
    return tap(_subFaq1, timeout: timeout);
  }

  Future<void> tapSubQuestion2({Duration? timeout}) {
    return tap(_subFaq2, timeout: timeout);
  }

  Future<void> tapSubQuestion3a({Duration? timeout}) {
    return tap(_subFaq3a, timeout: timeout);
  }

  Future<void> tapSubQuestion3b({Duration? timeout}) {
    return tap(_subFaq3b, timeout: timeout);
  }

  Future<void> tapSubQuestion4({Duration? timeout}) {
    return tap(_subFaq4, timeout: timeout);
  }

  Future<void> scrollToBlog({Duration? timeout}) {
    return scrollUntilVisible(_faqScreen, _faqReadTheBlog);
  }

  Future<void> scrollToCommunity({Duration? timeout}) {
    return scrollUntilVisible(_faqScreen, _faqAskCommunity);
  }

  Future<void> scrollToServiceStatus({Duration? timeout}) {
    return scrollUntilVisible(_faqScreen, _faqCheckServiceStatus);
  }

  Future<void> scrollToChat({Duration? timeout}) {
    return scrollUntilVisible(_faqScreen, _faqChatWithUs);
  }
}
