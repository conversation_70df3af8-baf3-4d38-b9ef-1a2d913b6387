import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class QrCodePage extends TestPage {
  final _qrCodeScreenFinder = find.byValueKey('__qrCodeScreen__');
  // App bar
  final _qrCodeAppBarBackButtonFinder = find.descendant(
    of: find.byValue<PERSON><PERSON>('__qrCodeScreenAppBar__'),
    matching: find.byType('KoyalIconButton'),
    firstMatchOnly: true,
  );
  final _qrCodeAppBarBackButtonAbsentFinder = find.descendant(
    of: find.byValue<PERSON>ey('__qrCodeScreenAppBar__'),
    matching: find.byType('KoyalIconButton'),
  );
  // QR Code information text
  final _qrGeneratorPreRatingInfoTextFinder =
      find.descendant(of: find.byValueKey('__qrGeneratorPreRatingInfoText__'), matching: find.byType('Text'));
  // QR Code image
  final _qrGeneratorPreRatingQrImageFinder = find.byValue<PERSON><PERSON>('__qrGeneratorPreRatingQrImage__');
  // QR Code image error
  final _qrGeneratorPreRatingQrImageErrorFinder =
      find.descendant(of: find.byValueKey('__qrGeneratorPreRatingQrImageError__'), matching: find.byType('Text'));
  // QR Code validity limitation text
  final _qrGeneratorPreRatingValidityLimitationTextFinder = find.descendant(
    of: find.byValueKey('__qrGeneratorPreRatingValidityLimitationText__'),
    matching: find.byType('Text'),
  );
  // QR Code "Navigate to dashboard" button
  final _qrCodeScreenNavigateToDashboardButtonFinder = find.byValueKey('__qrCodeScreenNavigateToDashboardButton__');
  // QR Code "Navigate to dashboard" button extra information
  final _qrCodeScreenNavigateToDashboardInfoFinder =
      find.descendant(of: find.byValueKey('__qrCodeScreenNavigateToDashboardInfo__'), matching: find.byType('Text'));

  // QR Code Overlay - Reviewing Order
  final _qrCodeOverlayReviewingOrderFinder = find.byValueKey('__qrCodeReviewingOrder__');
  final _qrCodeOverlayReviewingOrderTextFinder =
      find.descendant(of: find.byValueKey('__qrCodeReviewingOrderText__'), matching: find.byType('Text'));

  QrCodePage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_qrCodeScreenFinder, timeout: timeout);
  }

  Future<bool> isAbsent({Duration? timeout}) {
    return driver.runUnsynchronized(() {
      return widgetAbsent(_qrCodeScreenFinder, timeout: timeout);
    });
  }

  // App bar - back button

  Future<bool> isAppBarBackButtonReady({Duration? timeout}) {
    return widgetExists(_qrCodeAppBarBackButtonFinder, timeout: timeout);
  }

  Future<bool> isAppBarBackButtonAbsent({Duration? timeout}) {
    return widgetAbsent(_qrCodeAppBarBackButtonAbsentFinder, timeout: timeout);
  }

  Future<void> tapAppBarBackButton({Duration? timeout}) {
    return tap(_qrCodeAppBarBackButtonFinder, timeout: timeout);
  }

  // QR Code information text

  Future<bool> isQrCodeInformationTextReady({Duration? timeout}) {
    return widgetExists(_qrGeneratorPreRatingInfoTextFinder, timeout: timeout);
  }

  Future<bool> isQrCodeInformationTextAbsent({Duration? timeout}) {
    return widgetAbsent(_qrGeneratorPreRatingInfoTextFinder, timeout: timeout);
  }

  // QR Code image

  Future<bool> isQrCodeImageReady({Duration? timeout}) {
    return widgetExists(_qrGeneratorPreRatingQrImageFinder, timeout: timeout);
  }

  Future<bool> isQrCodeImageAbsent({Duration? timeout}) {
    return widgetAbsent(_qrGeneratorPreRatingQrImageFinder, timeout: timeout);
  }

  // QR Code image error

  Future<bool> isQrCodeImageErrorReady({Duration? timeout}) {
    return widgetExists(
      _qrGeneratorPreRatingQrImageErrorFinder,
      timeout: timeout,
    );
  }

  Future<bool> isQrCodeImageErrorAbsent({Duration? timeout}) {
    return widgetAbsent(
      _qrGeneratorPreRatingQrImageErrorFinder,
      timeout: timeout,
    );
  }

  // QR Code validity limitation text

  Future<bool> isValidityLimitationTextReady({Duration? timeout}) {
    return widgetExists(
      _qrGeneratorPreRatingValidityLimitationTextFinder,
      timeout: timeout,
    );
  }

  Future<bool> isValidityLimitationTextAbsent({Duration? timeout}) {
    return widgetAbsent(
      _qrGeneratorPreRatingValidityLimitationTextFinder,
      timeout: timeout,
    );
  }

  // QR Code "Navigate to dashboard" button

  Future<bool> isNavigateToDashboardButtonReady({Duration? timeout}) {
    return widgetExists(
      _qrCodeScreenNavigateToDashboardButtonFinder,
      timeout: timeout,
    );
  }

  Future<bool> isNavigateToDashboardButtonAbsent({Duration? timeout}) {
    return widgetAbsent(
      _qrCodeScreenNavigateToDashboardButtonFinder,
      timeout: timeout,
    );
  }

  Future<void> tapNavigateToDashboardButton({Duration? timeout}) {
    return tap(_qrCodeScreenNavigateToDashboardButtonFinder, timeout: timeout);
  }

  // QR Code "Navigate to dashboard" button extra information

  Future<bool> isNavigateToDashboardExtraInfoReady({Duration? timeout}) {
    return widgetExists(
      _qrCodeScreenNavigateToDashboardInfoFinder,
      timeout: timeout,
    );
  }

  Future<bool> isNavigateToDashboardExtraInfoAbsent({Duration? timeout}) {
    return widgetAbsent(
      _qrCodeScreenNavigateToDashboardInfoFinder,
      timeout: timeout,
    );
  }

  // QR Code Overlay - Reviewing Order

  Future<bool> isQrCodeOverlayReviewingOrderReady({Duration? timeout}) {
    return widgetExists(_qrCodeOverlayReviewingOrderFinder, timeout: timeout);
  }

  Future<bool> isQrCodeOverlayReviewingOrderAbsent({Duration? timeout}) {
    return widgetAbsent(_qrCodeOverlayReviewingOrderFinder, timeout: timeout);
  }

  // QR Code Overlay - Reviewing Order - Text

  Future<bool> isQrCodeOverlayReviewingOrderTextReady({Duration? timeout}) {
    return widgetExists(
      _qrCodeOverlayReviewingOrderTextFinder,
      timeout: timeout,
    );
  }

  Future<bool> isQrCodeOverlayReviewingOrderTextAbsent({Duration? timeout}) {
    return widgetAbsent(
      _qrCodeOverlayReviewingOrderTextFinder,
      timeout: timeout,
    );
  }

  Future<String> getTextQrCodeOverlayReviewingOrderText({Duration? timeout}) {
    return getText(_qrCodeOverlayReviewingOrderTextFinder, timeout: timeout);
  }
}
