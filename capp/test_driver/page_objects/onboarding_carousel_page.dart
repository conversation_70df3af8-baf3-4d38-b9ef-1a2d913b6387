import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class OnboardingCarouselPage extends TestPage {
  final _carouselScreen = find.byValueKey('__onboardingCarouselScreen__');
  final _onboardingPersonalizedOffers = find.byValue<PERSON><PERSON>('__onboardingPersonalisedOffers__');
  final _onboardingPurchasingPower = find.byValue<PERSON>ey('__onboardingPurchasingPower__');
  final _onboardingAchievemnts = find.byValue<PERSON>ey('__onboardingAchievemnts__');
  final _carouselCreateAccountButton = find.byValueKey('__createAccountButton__');
  final _carouselContinueAsGuestButton = find.byValueKey('__continueAsGuestButton__');

  OnboardingCarouselPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_carouselScreen, timeout: timeout);
  }

  Future<void> swipeLeftPersonlizedOffers() {
    return driver.scroll(_onboardingPersonalizedOffers, -400, 0, const Duration(milliseconds: 500));
  }

  Future<void> swipeLeftPurchasingPower() {
    return driver.scroll(_onboardingPurchasingPower, -400, 0, const Duration(milliseconds: 500));
  }

  Future<void> swipeLeftAchievemmnts() {
    return driver.scroll(_onboardingAchievemnts, -400, 0, const Duration(milliseconds: 500));
  }

  Future<void> tapCreateAccountButton() {
    return tap(_carouselCreateAccountButton);
  }

  Future<void> tapContinueAsGuestButton() {
    return tap(_carouselContinueAsGuestButton);
  }
}
