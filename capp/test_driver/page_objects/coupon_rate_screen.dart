import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class CouponRateScreen extends TestPage {
  final _screenFinder = find.byValueKey('__couponRateScreen__');

  // AppBar - Back button
  final _appBarBackButtonFinder = find.descendant(
    of: find.descendant(
      of: find.byValue<PERSON><PERSON>('__couponRateScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
    firstMatchOnly: true,
  );
  final _appBarBackButtonAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__couponRateScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
  );
  // Button - Submit Rating
  final _submitRatingButtonFinder = find.byValue<PERSON>ey('__couponRateScreenSubmitButton__');
  // Button - Rate Later
  final _rateLaterButtonFinder = find.byValueKey('__couponRateScreenLaterButton__');
  // Rating section
  final _ratingSectionFinder = find.byValueKey('__couponRateScreenStars__');
  // Rating stars
  final _ratingStarsFinder = find.descendant(
    of: find.byValueKey('__couponRateScreenStars__'),
    matching: find.byType('GFRating'),
    firstMatchOnly: true,
  );
  final _ratingStarsAbsentFinder = find.descendant(
    of: find.byValueKey('__couponRateScreenStars__'),
    matching: find.byType('GFRating'),
  );
  // Rating stars - First star
  final _ratingFirstStarContentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__couponRateScreenStars__'),
      matching: find.byType('GFRating'),
      firstMatchOnly: true,
    ),
    matching: find.byType('RichText'),
    firstMatchOnly: true,
  );
  final _ratingFirstStarContentAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__couponRateScreenStars__'),
      matching: find.byType('GFRating'),
    ),
    matching: find.byType('RichText'),
  );

  CouponRateScreen({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_screenFinder, timeout: timeout);
  }

  Future<bool> isAbsent({Duration? timeout}) {
    return widgetAbsent(
      _screenFinder,
      timeout: timeout,
    );
  }

  // App bar - rate screen - back button

  Future<bool> isAppBarBackButtonReady({Duration? timeout}) {
    return widgetExists(_appBarBackButtonFinder, timeout: timeout);
  }

  Future<bool> isAppBarBackButtonAbsent({Duration? timeout}) {
    return widgetAbsent(
      _appBarBackButtonAbsentFinder,
      timeout: timeout,
    );
  }

  Future<void> tapAppBarBackButton({Duration? timeout}) {
    return tap(_appBarBackButtonFinder, timeout: timeout);
  }

  // Button - Submit Rating

  Future<bool> isButtonSubmitRatingReady({Duration? timeout}) {
    return widgetExists(_submitRatingButtonFinder, timeout: timeout);
  }

  Future<bool> isButtonSubmitRatingAbsent({Duration? timeout}) {
    return widgetAbsent(
      _submitRatingButtonFinder,
      timeout: timeout,
    );
  }

  Future<void> tapButtonSubmitRating({Duration? timeout}) {
    return tap(_submitRatingButtonFinder, timeout: timeout);
  }

  // Button - Rate Later

  Future<bool> isButtonRateLaterReady({Duration? timeout}) {
    return widgetExists(_rateLaterButtonFinder, timeout: timeout);
  }

  Future<bool> isButtonRateLaterAbsent({Duration? timeout}) {
    return widgetAbsent(
      _rateLaterButtonFinder,
      timeout: timeout,
    );
  }

  Future<void> tapButtonRateLater({Duration? timeout}) {
    return tap(_rateLaterButtonFinder, timeout: timeout);
  }

  // Rating section

  Future<bool> isRatingSectionReady({Duration? timeout}) {
    return widgetExists(_ratingSectionFinder, timeout: timeout);
  }

  Future<bool> isRatingSectionAbsent({Duration? timeout}) {
    return widgetAbsent(
      _ratingSectionFinder,
      timeout: timeout,
    );
  }

  // Rating stars

  Future<bool> isRatingStarsSectionReady({Duration? timeout}) {
    return widgetExists(_ratingStarsFinder, timeout: timeout);
  }

  Future<bool> isRatingStarsSectionAbsent({Duration? timeout}) {
    return widgetAbsent(
      _ratingStarsAbsentFinder,
      timeout: timeout,
    );
  }

  // Rating stars - First star

  Future<bool> isRatingFirstStarContentReady({Duration? timeout}) {
    return widgetExists(_ratingFirstStarContentFinder, timeout: timeout);
  }

  Future<bool> isRatingFirstStarContentAbsent({Duration? timeout}) {
    return widgetAbsent(_ratingFirstStarContentAbsentFinder, timeout: timeout);
  }

  Future<String> getTextRatingFirstStarContent({Duration? timeout}) {
    return getText(_ratingFirstStarContentFinder, timeout: timeout);
  }

  Future<bool> isRatingFirstStarFull({Duration? timeout}) async {
    // STAR       - 0xe5f9 - 58873
    return await getTextRatingFirstStarContent(timeout: timeout) == String.fromCharCode(58873);
  }

  Future<bool> isRatingFirstStarEmpty({Duration? timeout}) async {
    // STARBORDER - 0xe5fa - 58874
    return await getTextRatingFirstStarContent(timeout: timeout) == String.fromCharCode(58874);
  }

  Future<bool> isRatingFirstStarHalfFull({Duration? timeout}) async {
    // STARHALF   - 0xe5fc - 58876
    return await getTextRatingFirstStarContent(timeout: timeout) == String.fromCharCode(58876);
  }
}
