import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

class CommunityDealsWizardPage extends TestPage {
  final _viewDealsPageFinder = find.byValue<PERSON>ey('__viewDealsStepWizardPage__');
  final _viewDealsButtonFinder = find.byValueKey('__viewDealsButton__');

  CommunityDealsWizardPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_viewDealsPageFinder, timeout: timeout);
  }

  Future<void> tapOnViewDealsButton() {
    return tap(_viewDealsButtonFinder);
  }
}
