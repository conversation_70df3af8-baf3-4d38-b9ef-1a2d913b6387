import 'package:flutter_driver/flutter_driver.dart';

import '../tools/screenshot_service.dart';
import 'test_page.dart';

enum DealsCategoryChip { topoffers, topsellers }

class DealsCategoryTestPage extends TestPage {
  final _screenFinder = find.byValue<PERSON>ey('__dealsCategoryScreen__');

  // AppBar - Back button
  final _appBarBackButtonFinder = find.descendant(
    of: find.descendant(
      of: find.byValue<PERSON><PERSON>('__dealsCategoryScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
    firstMatchOnly: true,
  );
  final _appBarBackButtonAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValue<PERSON>ey('__dealsCategoryScreenAppBar__'),
      matching: find.byType('KoyalAppBarLeading'),
    ),
    matching: find.byType('KoyalIconButton'),
  );
  // AppBar - Title
  final _appBarTitleFinder = find.descendant(
    of: find.descendant(
      of: find.byV<PERSON><PERSON><PERSON><PERSON>('__dealsCategoryScreenAppBar__'),
      matching: find.byType('KoyalAppBarTitle'),
    ),
    matching: find.byType('Text'),
    firstMatchOnly: true,
  );
  final _appBarTitleAbsentFinder = find.descendant(
    of: find.descendant(
      of: find.byValueKey('__dealsCategoryScreenAppBar__'),
      matching: find.byType('KoyalAppBarTitle'),
    ),
    matching: find.byType('Text'),
  );
  // search bar
  final _searchBarFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreen__'),
    matching: find.byValueKey('__communityDealsSearchBar__'),
    firstMatchOnly: true,
  );
  final _searchBarAbsentFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreen__'),
    matching: find.byValueKey('__communityDealsSearchBar__'),
  );
  // search bar - input
  final _searchBarTextInputFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarTextInput__'),
    firstMatchOnly: true,
  );
  final _searchBarTextInputAbsentFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarTextInput__'),
  );
  // search bar - reset button
  final _searchBarResetFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarReset__'),
    firstMatchOnly: true,
  );
  final _searchBarResetAbsentFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreen__'),
    matching: find.byValueKey('__communityDealsSearchBarReset__'),
  );
  // suggestions
  final _searchSuggestionsFinder = find.byValueKey('__dealsCategoryScreenSearchSuggestions__');
  // suggestion - first
  final _searchSuggestionFirstFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreenSearchSuggestions__'),
    matching: find.byValueKey(
      '__communityDealSuggestionTitle-073c56c6-a7f1-4320-bc51-c4754d900817__',
    ),
    firstMatchOnly: true,
  );
  final _searchSuggestionFirstAbsentFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreenSearchSuggestions__'),
    matching: find.byValueKey(
      '__communityDealSuggestionTitle-073c56c6-a7f1-4320-bc51-c4754d900817__',
    ),
  );
  // category filter
  final _categoryFilterFinder = find.byValueKey('__dealsCategoryScreenCategoryFilter__');
  // category filter - list view
  final _categoryFilterChipListViewFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreenCategoryFilter__'),
    matching: find.byValueKey('__dealsFilterListView__'),
    firstMatchOnly: true,
  );
  final _categoryFilterChipListViewAbsentFinder = find.descendant(
    of: find.byValueKey('__dealsCategoryScreenCategoryFilter__'),
    matching: find.byValueKey('__dealsFilterListView__'),
  );
  // category filter - chip - Top sellers
  final _categoryFilterChipKeyTopSellers = 'top-sellers';
  // category filter - chip - TopOffers
  final _categoryFilterChipKeyTopOffers = 'top-offers';

  // __dealsCategoryScreenScrollView__
  // __dealsCategoryScreenProductGridEmpty__

  DealsCategoryTestPage({
    required FlutterDriver driver,
    required ScreenshotService screenshotService,
  }) : super(
          driver: driver,
          screenshotService: screenshotService,
        );

  SerializableFinder _getCategoryFilterChipFinder(DealsCategoryChip type, {bool isAbsentFinder = false}) {
    var chipKey = '';
    switch (type) {
      case DealsCategoryChip.topoffers:
        chipKey = _categoryFilterChipKeyTopOffers;
        break;
      case DealsCategoryChip.topsellers:
        chipKey = _categoryFilterChipKeyTopSellers;
        break;
      default:
    }
    return find.descendant(
      of: find.byValueKey('__dealsCategoryScreenCategoryFilter__'),
      matching: find.byValueKey('__dealsFilterChip-${chipKey}__'),
      firstMatchOnly: !isAbsentFinder,
    );
  }

  SerializableFinder _getCategoryFilterChipTextFinder(DealsCategoryChip type, {bool isAbsentFinder = false}) {
    return find.descendant(
      of: _getCategoryFilterChipFinder(type, isAbsentFinder: isAbsentFinder),
      matching: find.byType('Text'),
      firstMatchOnly: !isAbsentFinder,
    );
  }

  @override
  Future<bool> isReady({Duration? timeout}) {
    return widgetExists(_screenFinder, timeout: timeout);
  }

  Future<bool> isAbsent({Duration? timeout}) {
    return widgetAbsent(_screenFinder, timeout: timeout);
  }

  // App bar - back button

  Future<bool> isAppBarBackButtonReady({Duration? timeout}) {
    return widgetExists(_appBarBackButtonFinder, timeout: timeout);
  }

  Future<bool> isAppBarBackButtonAbsent({Duration? timeout}) {
    return widgetAbsent(_appBarBackButtonAbsentFinder, timeout: timeout);
  }

  Future<void> tapAppBarBackButton({Duration? timeout}) {
    return tap(_appBarBackButtonFinder, timeout: timeout);
  }

  // AppBar - Title

  Future<bool> isAppBarTitleReady({Duration? timeout}) {
    return widgetExists(_appBarTitleFinder, timeout: timeout);
  }

  Future<bool> isAppBarTitleAbsent({Duration? timeout}) {
    return widgetAbsent(_appBarTitleAbsentFinder, timeout: timeout);
  }

  Future<String> getTextAppBarTitle({Duration? timeout}) {
    return getText(_appBarTitleFinder, timeout: timeout);
  }

  // search box

  Future<bool> isSearchBarReady({Duration? timeout}) {
    return widgetExists(_searchBarFinder, timeout: timeout);
  }

  Future<bool> isSearchBarAbsent({Duration? timeout}) {
    return widgetAbsent(_searchBarAbsentFinder, timeout: timeout);
  }

  // search bar - input

  Future<bool> isSearchBarTextInputReady({Duration? timeout}) {
    return widgetExists(_searchBarTextInputFinder, timeout: timeout);
  }

  Future<bool> isSearchBarTextInputAbsent({Duration? timeout}) {
    return widgetAbsent(_searchBarTextInputAbsentFinder, timeout: timeout);
  }

  Future<String> getTextSearchBarTextInput({Duration? timeout}) {
    return getText(_searchBarTextInputFinder, timeout: timeout);
  }

  Future<void> tapSearchBarTextInput({Duration? timeout}) {
    return tap(_searchBarTextInputFinder, timeout: timeout);
  }

  Future<void> enterTextSearchBarTextInput(String text, {Duration? timeout}) async {
    // focus input field
    await tapSearchBarTextInput(timeout: timeout);
    // enter the text
    return enterText(text, timeout: timeout);
  }

  // search bar - reset button

  Future<bool> isSearchBarResetButtonReady({Duration? timeout}) {
    return widgetExists(_searchBarResetFinder, timeout: timeout);
  }

  Future<bool> isSearchBarResetButtonAbsent({Duration? timeout}) {
    return widgetAbsent(_searchBarResetAbsentFinder, timeout: timeout);
  }

  Future<void> tapSearchBarResetButton({Duration? timeout}) {
    return tap(_searchBarResetFinder, timeout: timeout);
  }

  // suggestions

  Future<bool> isSearchSuggestionsReady({Duration? timeout}) {
    return widgetExists(_searchSuggestionsFinder, timeout: timeout);
  }

  Future<bool> isSearchSuggestionsAbsent({Duration? timeout}) {
    return widgetAbsent(_searchSuggestionsFinder, timeout: timeout);
  }

  // suggestions - first entity

  Future<bool> isSearchSuggestionFirstRowReady({Duration? timeout}) {
    return widgetExists(_searchSuggestionFirstFinder, timeout: timeout);
  }

  Future<bool> isSearchSuggestionFirstRowAbsent({Duration? timeout}) {
    return widgetAbsent(_searchSuggestionFirstAbsentFinder, timeout: timeout);
  }

  // category filter

  Future<bool> isCategoryFilterReady({Duration? timeout}) {
    return widgetExists(_categoryFilterFinder, timeout: timeout);
  }

  Future<bool> isCategoryFilterAbsent({Duration? timeout}) {
    return widgetAbsent(_categoryFilterFinder, timeout: timeout);
  }

  // category filter - list view

  Future<bool> isCategoryFilterListViewReady({Duration? timeout}) {
    return widgetExists(_categoryFilterChipListViewFinder, timeout: timeout);
  }

  Future<bool> isCategoryFilterListViewAbsent({Duration? timeout}) {
    return widgetAbsent(
      _categoryFilterChipListViewAbsentFinder,
      timeout: timeout,
    );
  }

  // category filter - chip - Top sellers

  Future<bool> isCategoryFilterChipTopSellersReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(DealsCategoryChip.topsellers),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipTopSellersAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        DealsCategoryChip.topsellers,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipTopSellers({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(DealsCategoryChip.topsellers),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipTopSellers({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(DealsCategoryChip.topsellers),
      timeout: timeout,
    );
  }

  // category filter - chip - TopOffers

  Future<bool> isCategoryFilterChipTopOffersReady({Duration? timeout}) {
    return widgetExists(
      _getCategoryFilterChipFinder(DealsCategoryChip.topoffers),
      timeout: timeout,
    );
  }

  Future<bool> isCategoryFilterChipTopOffersAbsent({Duration? timeout}) {
    return widgetAbsent(
      _getCategoryFilterChipFinder(
        DealsCategoryChip.topoffers,
        isAbsentFinder: true,
      ),
      timeout: timeout,
    );
  }

  Future<String> getTextCategoryFilterChipTopOffers({Duration? timeout}) {
    return getText(
      _getCategoryFilterChipTextFinder(DealsCategoryChip.topoffers),
      timeout: timeout,
    );
  }

  Future<void> tapCategoryFilterChipTopOffers({Duration? timeout}) {
    return tap(
      _getCategoryFilterChipFinder(DealsCategoryChip.topoffers),
      timeout: timeout,
    );
  }
}
