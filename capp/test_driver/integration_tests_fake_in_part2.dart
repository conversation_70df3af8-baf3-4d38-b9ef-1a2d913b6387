import 'package:integration_test/integration_test.dart';

// Consider to move all test classes to 'integration_test' folder https://github.com/Dart-Code/Dart-Code/pull/3133
// Consider to keep all filenames with '_test.dart' postfix https://github.com/Dart-Code/Dart-Code/commit/4983d93c73f07a456b1ac0d24f9a0d3f2160d528
import '../../packages/capp_config/capp_config_core/test/firebase_mock.dart';
import 'capp_in/fake/auth/recover_password/recover_password_test.dart' as recover_password_tests_in;
import 'capp_in/fake/auth/reset_password/reset_pin_test.dart' as reset_pin_tests_in;
import 'capp_in/integration_tests_fake/capp_transaction_signature_test.dart' as transaction_signature;
import 'capp_in/integration_tests_fake/general_feedback_tests.dart' as general_feedback_tests;
import 'capp_in/integration_tests_fake/help_center_tests.dart' as help_center_tests;
import 'capp_in/integration_tests_fake/payment_gateway/payment_gateway_test.dart' as payment_gateway_test;
import 'capp_in/integration_tests_tools/app_runners/fake_in_app_runner.dart';

void main() {
  final appRunner = FakeInAppRunner();
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  setupFirebaseAnalyticsMocks();

  general_feedback_tests.main();
  help_center_tests.main();
  //direct_debit_test.main();

  transaction_signature.main();
  payment_gateway_test.main();
  //bank_validation_test.main();

  recover_password_tests_in.run(appRunner);
  reset_pin_tests_in.run(appRunner);
}
