import 'package:flutter_driver/flutter_driver.dart';
import 'package:test/test.dart';

import '../page_objects/page_objects.dart';

const timeWait = 2;

Future<void> onBoardingEnglishLang(
  FlutterDriver driver,
  OnboardingChooseLanguagePage onboardingPage,
  OnboardingCarouselPage carouselPage,
) async {
  await driver.clearTimeline();
  expect(await onboardingPage.isReady(), isTrue);

  // select english language
  await onboardingPage.chooseEnglish();

  // delay for prevent error
  await Future.delayed(const Duration(seconds: timeWait), () {});

  // confirm language
  await driver.runUnsynchronized(() {
    return onboardingPage.confirmLanguage();
  });

  // delay for prevent error
  await Future.delayed(const Duration(seconds: timeWait), () {});

  // wait for onboarding  carusel page
  expect(await carouselPage.isReady(), isTrue);
}
