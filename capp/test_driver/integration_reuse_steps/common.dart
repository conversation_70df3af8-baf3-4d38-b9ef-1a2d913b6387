import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_dynamic_forms/koyal_dynamic_forms.dart';

import '../integration_page_objects/home_page.dart';
import '../integration_page_objects/main_page.dart';
import '../integration_page_objects/onboarding_carousel_page.dart';
import '../integration_page_objects/onboarding_choose_language_page.dart';
import '../integration_page_objects/personal_menu_page.dart';
import '../integration_page_objects/personal_menu_page_v2.dart';
import '../integration_page_objects/profile_page.dart';

Future<void> onBoardingEnglishLang(
  OnboardingChooseLanguagePage onboardingPage,
  OnboardingCarouselPage carouselPage,
) async {
  onboardingPage.verifyReady();
  await onboardingPage.chooseEnglish();
  await onboardingPage.confirmLanguage();
  carouselPage.verifyReady();
}

Future<void> toHomePage(
  OnboardingChooseLanguagePage onboardingPage,
  OnboardingCarouselPage carouselPage,
  HomeTestPage homePage,
) async {
  await onBoardingEnglishLang(onboardingPage, carouselPage);
  await carouselPage.continueAsGuestButton.tap();
  homePage.verifyReady();
}

Future<void> toProfilePage(
  OnboardingChooseLanguagePage onboardingPage,
  OnboardingCarouselPage carouselPage,
  HomeTestPage homePage,
  MainTestPage mainTestPage,
  ProfilePage customerProfilePage,
  PersonalMenuPage personalMenuPage,
) async {
  await toHomePage(onboardingPage, carouselPage, homePage);

  mainTestPage.verifyReady();

  await mainTestPage.tapMainNavigationItemProfile();

  customerProfilePage.isReady();

  await customerProfilePage.profileMenuButton.tap();

  personalMenuPage.verifyReady();
}

Future<void> toProfilePageV2(
  OnboardingChooseLanguagePage onboardingPage,
  OnboardingCarouselPage carouselPage,
  HomeTestPage homePage,
  MainTestPage mainTestPage,
  ProfilePage customerProfilePage,
  PersonalMenuPageV2 personalMenuPageV2,
) async {
  await toHomePage(onboardingPage, carouselPage, homePage);

  mainTestPage.verifyReady();

  await mainTestPage.tapMainNavigationItemProfile();

  customerProfilePage.isReady();

  await customerProfilePage.profileMenuButton.tap();

  personalMenuPageV2.screen.isReady();
}

Future<dynamic>? navigateHome(ExternalConfigurationData configuration) => configuration.navigator?.toMainScreen();

Future<dynamic>? navigateLoan(ExternalConfigurationData configuration, {Object? arguments}) =>
    configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'CappLoanOrigination',
      'GetLoanWizard',
      'CappHome',
      'MainScreen',
      arguments: arguments,
    );

Future<dynamic>? navigateResetState(ExternalConfigurationData configuration, {bool resetState = false}) =>
    configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
      'KoyalDynamicForms',
      'WorkflowAddressScreen',
      'CappHome',
      'MainScreen',
      arguments: WorkflowAddressArguments(resetState: resetState),
    );

Future<void> pumpHomeFrames(
  WidgetTester tester, {
  int count = 10,
  Duration? delay,
}) async {
  if (delay != null) {
    await Future.delayed(delay, () {});
  }
  for (var i = 0; i < count; i++) {
    await tester.pump();
  }
  return;
}
