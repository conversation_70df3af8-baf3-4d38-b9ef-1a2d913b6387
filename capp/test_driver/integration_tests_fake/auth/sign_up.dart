import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../integration_page_objects/auth_error_dialogs/already_used_dialog.dart';
import '../../integration_page_objects/index.dart';
import '../../integration_tests_tools/tester_helper.dart';
import 'common/auth_common_steps.dart';

Future<void> signupWalkthroughAuthV3Test(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool failOnExistingUser = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdNonHoselSetPage = SecondIdNonHoselSetPage(tester);
  final setPasswordPage = SetPasswordPage(tester);
  final alreadyUsedDialog = AlreadyUsedDialog(tester);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authLandingPage.isReady();
  // enter unregistered phone number
  await authLandingPage.phoneNumberInputField.enterText('9778889992');
  await authLandingPage.confirmPhoneNumberButton.tap();
  // otp screen
  otpPage.isReady();
  // request OTP
  await otpPage.otpSendButton.tap();
  // enter valid OTP
  await otpPage.enterOtp('483755');
  // check for Set 2nd identifier screen
  secondIdNonHoselSetPage.isReady();

  if (failOnExistingUser) {
    // enter already used email and expect error dialog
    await secondIdNonHoselSetPage.emailEntryField.enterText('<EMAIL>');
    await secondIdNonHoselSetPage.confirmButton.tap();

    // otp screen
    otpPage.isReady();

    // enter valid OTP
    await otpPage.enterOtp('4837');

    alreadyUsedDialog.isReady();
    alreadyUsedDialog.loginButton.isReady();
    await alreadyUsedDialog.loginButton.tap();
    authLandingPage.isReady();
    secondIdNonHoselSetPage.isAbsent();
    return;
  }

  // enter the mail
  await secondIdNonHoselSetPage.emailEntryField.enterText('<EMAIL>');

  // continue
  await secondIdNonHoselSetPage.confirmButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('4837');

  setPasswordPage.isReady();

  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387', settleAfterPump: true);
  // enter the pin again, enter incorrect pin
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '7110' : '711022', settleAfterPump: true);

  // Invalid PIN by criteria
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1112' : '111222', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1112' : '111222', settleAfterPump: true);

  // enter the pin correctly
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387');
  await testerHelper.pumpHome();

  // home page
  homePage.isReady();
  authLandingPage.isAbsent();
  secondIdNonHoselSetPage.isAbsent();
}

Future<void> signupAuthV3PhoneRegisteredTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdNonHoselSetPage = SecondIdNonHoselSetPage(tester);
  final secondIdVerificationPage = SecondIdVerificationPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('**********');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('483755');

  secondIdVerificationPage.isReady();

  // tap signup button (no HC customer)
  await secondIdVerificationPage.signUpButton.tap();

  // create an account page
  await secondIdNonHoselSetPage.emailEntryField.enterText('<EMAIL>');

  // continue
  await secondIdNonHoselSetPage.confirmButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('4837');

  setPasswordPage.isReady();

  // enter the pin correctly
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387');
  await testerHelper.pumpHome();

  // home page
  homePage.isReady();
  authLandingPage.isAbsent();
  secondIdNonHoselSetPage.isAbsent();
}

// AUTH V4 - prospects without 2nd ID, new FF, new APIs

/// Auth Landing
/// Enter phone - not yet registered, not in hosel
/// OTP
/// Set PIN
/// Home
Future<void> signupWalkthroughAuthV4ProspectTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.prospectsWithout2ndId, isEnabled: true);

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('9778889994');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('483755');

  setPasswordPage.isReady();

  // enter the pin correctly
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387');
  await testerHelper.pumpHome();

  // home page
  homePage.isReady();
  authLandingPage.isAbsent();
}

/// Auth Landing
/// Enter phone - not yet registered, in Hosel (has PAN)
/// OTP
/// 2nd ID set screen - select PAN, enter
/// Set PIN
/// Home
Future<void> signupWalkthroughAuthV4PanTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdNonHoselSetPage = SecondIdNonHoselSetPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.prospectsWithout2ndId, isEnabled: true);

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('**********');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('483755');

  secondIdNonHoselSetPage.isReady();

  // create an account page - enter PAN
  // [a-zA-Z]{3}[PFRCAHBJL][a-zA-Z][0-9]{4}[a-zA-Z]
  await secondIdNonHoselSetPage.panInput.enterText('AbCPa1234b');

  // continue
  await secondIdNonHoselSetPage.confirmButton.tap();

  setPasswordPage.isReady();

  // enter the pin correctly
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387');
  await testerHelper.pumpHome();

  // home page
  homePage.isReady();
  authLandingPage.isAbsent();
  secondIdNonHoselSetPage.isAbsent();
}

/// Auth Landing
/// Enter phone - already exists
/// OTP
/// 2nd ID verification screen - select "I am not a customer" option
/// 2nd ID set screen - select LAN, fill
/// Set PIN
/// Home
Future<void> signupWalkthroughExistingCustomerLanTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdNonHoselSetPage = SecondIdNonHoselSetPage(tester);
  final secondIdVerificationPage = SecondIdVerificationPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.prospectsWithout2ndId, isEnabled: true);

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('**********');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('483755');

  // check there is existing user screen and that LAN input is present
  secondIdVerificationPage.isReady();
  secondIdNonHoselSetPage.lanInput.isReady();

  // tap signup button (no HC customer)
  await secondIdVerificationPage.signUpButton.tap();

  // create an account page
  secondIdNonHoselSetPage.isReady();
  await secondIdNonHoselSetPage.lanInput.enterText('************0');

  // continue
  await secondIdNonHoselSetPage.confirmButton.tap();

  setPasswordPage.isReady();

  // enter the pin correctly
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '1893' : '189387');
  await testerHelper.pumpHome();

  // home page
  homePage.isReady();
  authLandingPage.isAbsent();
  secondIdNonHoselSetPage.isAbsent();
}

Future<void> signupAuthV3PhoneRegisteredKtpIputTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdNonHoselSetPage = SecondIdNonHoselSetPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('**********');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('483755');

  secondIdNonHoselSetPage.isReady();

  // create an account page
  await secondIdNonHoselSetPage.ktpInput.enterText('************0000');

  // continue
  await secondIdNonHoselSetPage.confirmButton.tap();

  setPasswordPage.isReady();
}

Future<void> signupAuthV3PhoneRegisteredIdIputTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final otpPage = OtpPage(tester);
  final secondIdNonHoselSetPage = SecondIdNonHoselSetPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('**********');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // otp screen
  otpPage.isReady();

  // enter valid OTP
  await otpPage.enterOtp('483755');

  secondIdNonHoselSetPage.isReady();

  // create an account page
  await secondIdNonHoselSetPage.idInput.enterText('************');

  // continue
  await secondIdNonHoselSetPage.confirmButton.tap();

  setPasswordPage.isReady();
}
