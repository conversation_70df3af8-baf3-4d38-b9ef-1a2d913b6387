import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../integration_page_objects/index.dart';
import '../../integration_tests_tools/tester_helper.dart';
import 'common/auth_common_steps.dart';

Future<void> successfullPinRecoveryTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final homePage = HomeTestPage(tester);
  final otpPage = OtpPage(tester);
  final pinPage = PinPage(tester);
  final pinPassRecoverySuccessPage = PinPassRecoverySuccessPage(tester);
  final pinRecoveryPhonePage = PinRecoveryPhonePage(tester);
  final secondIdVerificationPage = SecondIdVerificationPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.passwordRecoveryV2, isEnabled: true);
  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);

  // Welcome to SuperCupo world
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('9778889990');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // PIN
  pinPage.isReady();
  await pinPage.forgotPinButton.tap();

  // Forgot Password screen
  pinRecoveryPhonePage.isReady();

  // check phone number and continue
  final phoneWithoutFormatting =
      pinRecoveryPhonePage.phoneNumberInputField.getText()?.replaceAll(' ', '').replaceAll('-', '');
  expect(phoneWithoutFormatting, equals('9778889990'));

  await testerHelper.delay(const Duration(milliseconds: 500));
  await pinRecoveryPhonePage.continueButton.tap();

  // Phone Verification
  otpPage.isReady();
  await otpPage.enterOtp('775566', settleAfterPump: true);

  // Identity Verification
  secondIdVerificationPage.isReady();
  // try to continue without 2nd ID
  await secondIdVerificationPage.confirmButton.tap();
  secondIdVerificationPage.isReady();

  await secondIdVerificationPage.emailEntryField.enterText('<EMAIL>');
  // continue
  await secondIdVerificationPage.confirmButton.tap();

  // Reset PIN
  setPasswordPage.isReady();
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '8855' : '885522', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '8855' : '885522', settleAfterPump: true);

  // PIN reset
  pinPassRecoverySuccessPage.isReady();
  await pinPassRecoverySuccessPage.continueButton.tap(settleAfterPump: false);
  await testerHelper.pumpHome();

  // Home screen
  homePage.isReady();
}

Future<void> pinRecoveryWithErrorsTest(
  WidgetTester tester,
  TestContext context, {
  bool isFourDigitPinEnabled = false,
  bool hasLangSelection = true,
}) async {
  final testerHelper = TesterHelper(tester: tester, context: context);
  final authLandingPage = AuthLandingPage(tester);
  final dailyPasswordSetLimitReachedDialog = DailyPasswordSetLimitReachedDialog(tester);
  final homePage = HomeTestPage(tester);
  final noOldPasswordDialog = NoOldPasswordDialog(tester);
  final otpNotAcceptedDialog = OtpNotAcceptedDialog(tester);
  final otpPage = OtpPage(tester);
  final pinPage = PinPage(tester);
  final pinRecoveryPhonePage = PinRecoveryPhonePage(tester);
  final secondIdVerificationPage = SecondIdVerificationPage(tester);
  final setPasswordPage = SetPasswordPage(tester);

  testerHelper.toggleFeatureFlag(featureName: FeatureFlag.passwordRecoveryV2, isEnabled: true);
  if (isFourDigitPinEnabled) {
    testerHelper.enableFourDigitPin();
  } else {
    testerHelper.enableSixDigitPin();
  }

  await testStepsFromInitToAuth(tester, hasLangSelection: hasLangSelection);

  // Welcome to SuperCupo world
  authLandingPage.isReady();

  // enter phone number
  await authLandingPage.phoneNumberInputField.enterText('9778889990');
  await authLandingPage.confirmPhoneNumberButton.tap();

  // PIN
  pinPage.isReady();
  // set error response for OTP validation
  testerHelper.changeV2ResetPasswordSessionResponseCode(400);
  // tap "Forgot Pin"
  await pinPage.forgotPinButton.tap();

  // Forgot Password screen
  pinRecoveryPhonePage.isReady();
  // enter phone number and continue
  await pinRecoveryPhonePage.phoneNumberInputField.enterText('9778889990');
  await pinRecoveryPhonePage.continueButton.tap();

  // Phone Verification
  otpPage.isReady();
  await otpPage.enterOtp('775566', settleAfterPump: true);

  // back at "Forgot PIN" with an error
  pinRecoveryPhonePage.isReady();
  otpNotAcceptedDialog.isReady();
  await otpNotAcceptedDialog.okButton.tap();
  otpNotAcceptedDialog.isAbsent();

  // set valid response for OTP validation
  testerHelper
    ..changeV2ResetPasswordSessionResponseCode()
    // set error response for Identity verification
    ..changeV2ResetPasswordSecondIdrResponseCode(400);
  // continue
  await pinRecoveryPhonePage.continueButton.tap();

  // Phone Verification
  otpPage.isReady();
  await otpPage.enterOtp('775566', settleAfterPump: true);

  // Identity Verification
  secondIdVerificationPage.isReady();
  await secondIdVerificationPage.emailEntryField.enterText('<EMAIL>');
  await secondIdVerificationPage.confirmButton.tap();
  secondIdVerificationPage.isReady();
  await secondIdVerificationPage.invalidCredentialsDialogOkButton.tap();

  // try to use blocked user
  testerHelper.changeV2ResetPasswordSecondIdrResponseCode(403);

  await secondIdVerificationPage.emailEntryField.enterText('<EMAIL>');
  await secondIdVerificationPage.confirmButton.tap();
  await secondIdVerificationPage.blockedDialogButton.tap();

  testerHelper.changeV2ResetPasswordSecondIdrResponseCode();
  // enter 2nd ID again and continue
  await secondIdVerificationPage.emailEntryField.enterText('<EMAIL>');
  // continue
  await secondIdVerificationPage.confirmButton.tap();

  // Reset PIN
  setPasswordPage.isReady();
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '8855' : '885522', settleAfterPump: true);
  // enter missmatched PIN
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '8854' : '885544', settleAfterPump: true);
  // check for error
  setPasswordPage.isReady();

  // set general error response for PIN completion
  testerHelper.changeV2ResetPasswordCompleteResponseCode(409);
  // enter matching PINs
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '2244' : '223344', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '2244' : '223344', settleAfterPump: true);
  // check for error
  setPasswordPage.isReady();
  // set valid response for PIN completion
  testerHelper.changeV2ResetPasswordCompleteResponseCode();

  // trigger error for reusing old PIN
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '9911' : '991122', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '9911' : '991122', settleAfterPump: true);
  // check error dialog
  noOldPasswordDialog.isReady();
  await noOldPasswordDialog.okButton.tap();
  noOldPasswordDialog.isAbsent();

  // trigger error for too many reset attempts
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '9922' : '993344', settleAfterPump: true);
  await setPasswordPage.enterPasscode(isFourDigitPinEnabled ? '9922' : '993344', settleAfterPump: true);
  // check error dialog
  dailyPasswordSetLimitReachedDialog.isReady();
  await dailyPasswordSetLimitReachedDialog.goToHomepageButton.tap(settleAfterPump: false);
  await tester.pumpAndSettle();

  dailyPasswordSetLimitReachedDialog.isAbsent();

  setPasswordPage.isReady();
}
