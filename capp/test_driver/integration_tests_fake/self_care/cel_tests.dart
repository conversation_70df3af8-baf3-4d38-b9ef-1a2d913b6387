import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flagsmith/flagsmith.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../integration_page_objects/home_page.dart';
import '../../integration_page_objects/self_care/cel/index.dart';
import '../../integration_reuse_steps/common.dart';
import '../../integration_tests_tools/tester_helper.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group(
    'CEL Tests',
    () {
      integrationTest(
        'CashLoan navigate from HomePage test',
        (tester, context) async {
          final testerHelper = TesterHelper(tester: tester, context: context)..forceFakeLoginStateAsClient();
          final homePage = HomeTestPage(tester);
          final celOverviewPage = CelOverviewPage(tester);
          await tester.pumpAndSettle();
          await testerHelper.navigateToHome();
          await testerHelper.delay(const Duration(milliseconds: 300));
          homePage.verifyReady();

          await pumpHomeFrames(tester, delay: const Duration(milliseconds: 300));
          await tester.pumpAndSettle();
          //await homePage.scrollToInstallmentsOverview();
          //await pumpHomeFrames(tester, delay: const Duration(milliseconds: 8000));
          await tester.pumpAndSettle();

          await homePage.cashLoanOverviewTile.tap();
          await tester.pumpAndSettle(const Duration(microseconds: 200));
          celOverviewPage.isReady();
        },
        appRunner: appRunner,
        featureFlagOverrides: [
          Flag.seed(FeatureFlag.loanCardsNew),
          Flag.seed(FeatureFlag.loanCardsHideOld),
          Flag.seed(FeatureFlag.homeFinancialCashLoan),
          Flag.seed(FeatureFlag.homeFinancialBnpl, enabled: false),
          Flag.seed(FeatureFlag.homeFinancialCreditCard, enabled: false),
          Flag.seed(FeatureFlag.homeFinancialEmi, enabled: false),
          Flag.seed(FeatureFlag.homeFinancialFlexi, enabled: false),
          Flag.seed(FeatureFlag.homeFinancialPayLater, enabled: false),
          Flag.seed(FeatureFlag.homeFinancialPosLoan, enabled: false),
          Flag.seed(FeatureFlag.homeFinancialQwarta, enabled: false),
          Flag.seed(FeatureFlag.homeScreenProductsAndServices, enabled: false),
          Flag.seed(FeatureFlag.pendingActionsFacelift, enabled: false),
          Flag.seed(FeatureFlag.homePendingActionsNew, enabled: false),
        ],
      );

      integrationTest(
        'CashLoan overview',
        (tester, context) async {
          final testerHelper = TesterHelper(tester: tester, context: context)..forceFakeLoginStateAsClient();
          final celOverviewPage = CelOverviewPage(tester);
          final loanContractsDocumentsPage = LoanContractsDocumentsPage(tester);
          final loanContractsDetailPage = LoanContractsDetailPage(tester);
          final celInstallmentsPage = CelInstallmentsPage(tester);
          final celInstallmentsDetailPage = CelInstallmentsDetailPage(tester);
          final celDetailsPage = CelDetailsPage(tester);

          await testerHelper.navigateToCelOverview(contractType: ContractType.cash);
          await testerHelper.delay(const Duration(milliseconds: 200));
          celOverviewPage.isReady();
          await celOverviewPage.checkPage();
          await celOverviewPage.actionDocuments.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          loanContractsDocumentsPage.isReady();
          await loanContractsDocumentsPage.checkPage();
          await loanContractsDocumentsPage.firstDocument.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          loanContractsDetailPage.isReady();
          await loanContractsDetailPage.appBarBackButton.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          loanContractsDocumentsPage.isReady();
          await loanContractsDocumentsPage.appBarBackButton.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          celOverviewPage.isReady();
          await celOverviewPage.actionInstallments.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          celInstallmentsPage.isReady();
          await celInstallmentsPage.checkPage();
          await celInstallmentsPage.firstItem.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          celInstallmentsDetailPage.isReady();
          await celInstallmentsDetailPage.appBarBackButton.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          celInstallmentsPage.isReady();
          await celInstallmentsPage.appBarBackButton.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          celOverviewPage.isReady();
          await celOverviewPage.actionDetails.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          celDetailsPage.isReady();
          await celDetailsPage.appBarBackButton.tap();
          await testerHelper.delay(const Duration(milliseconds: 200));
          celOverviewPage.isReady();
        },
        appRunner: appRunner,
        featureFlagOverrides: [
          Flag.seed(FeatureFlag.loanCardsNew),
          Flag.seed(FeatureFlag.loanCardsHideOld),
          Flag.seed(FeatureFlag.celActionDetails),
          Flag.seed(FeatureFlag.celActionInstallmentPlans),
          Flag.seed(FeatureFlag.celActionDocuments),
          Flag.seed(FeatureFlag.homeFinancialCashLoan),
          Flag.seed(FeatureFlag.celActionPaymentReminder, enabled: false),
        ],
      );
    },
  );
}
