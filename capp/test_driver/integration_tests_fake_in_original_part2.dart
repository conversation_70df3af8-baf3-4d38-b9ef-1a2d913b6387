import 'package:integration_test/integration_test.dart';

// Consider to move all test classes to 'integration_test' folder https://github.com/Dart-Code/Dart-Code/pull/3133
// Consider to keep all filenames with '_test.dart' postfix https://github.com/Dart-Code/Dart-Code/commit/4983d93c73f07a456b1ac0d24f9a0d3f2160d528
import '../../packages/capp_config/capp_config_core/test/firebase_mock.dart';
import 'capp_in/fake/auth/change_phone/change_phone_test.dart' as change_phone_tests_in;
import 'capp_in/fake/auth/hosel_pairing/hosel_pairing_test.dart' as hosel_pairing_tests_in;
import 'capp_in/fake/auth/login/login_otp_test.dart' as otp_test;
import 'capp_in/fake/auth/login/sign_in_test.dart' as sign_in_tests_in;
import 'capp_in/fake/auth/sign_up/sign_up_test.dart' as sign_up_tests_in;
import 'capp_in/fake/auth/unblock_account/unblock_account_test.dart' as unblock_account_tests_in;
import 'capp_in/fake/auth/unlock_screen/unlock_screen_test.dart' as unlock_screen_tests_in;
import 'capp_in/fake/authentication/authentication_test.dart' as authentication_tests_in;
import 'capp_in/integration_tests_tools/app_runners/fake_in_app_runner.dart';
// import 'integration_tests_fake/blog_tests.dart' as blog_tests;
// import 'integration_tests_fake/change_language_tests.dart'
//     as change_language_tests;
// import 'integration_tests_fake/wizard_tests.dart' as wizard_tests;
// import 'integration_tests_fake/documents_tests.dart' as documents_tests;

void main() {
  final appRunner = FakeInAppRunner();
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  setupFirebaseAnalyticsMocks();

  sign_up_tests_in.run(appRunner);
  change_phone_tests_in.run(appRunner);
  sign_in_tests_in.run(appRunner);
  authentication_tests_in.run(appRunner);
  otp_test.run(appRunner);
  hosel_pairing_tests_in.run(appRunner);

  unblock_account_tests_in.run(appRunner);
  unlock_screen_tests_in.run(appRunner);

  // @TODO check and enable tests
  // wizard_tests.main(appRunner);
  // change_language_tests.main();

  // @TODO - check why blog is not displayed on the home screen
  // blog_tests.main();

  // @TODO - missing documents in fake version
  // documents_tests.main();
}
