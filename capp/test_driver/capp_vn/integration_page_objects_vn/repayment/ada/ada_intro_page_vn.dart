import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../integration_page_objects/base/test_page_with_app_bar.dart';

class AdaIntroTestPageVn extends TestPageWithAppBar {
  final Finder _adaIntroContinueButtonFinder = find.by<PERSON>ey(const Key('__adaIntroContinueButton__'));

  AdaIntroTestPageVn(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__adaIntroScreen__')),
          appBarFinder: find.byKey(const Key('__adaIntroAppBar__')),
        );

  // Button
  void checkContinueButtonExist() {
    expect(_adaIntroContinueButtonFinder, findsOneWidget);
  }

  Future<void> tapContinue() async {
    await tester.tap(_adaIntroContinueButtonFinder);
    await tester.pumpAndSettle();
  }
}
