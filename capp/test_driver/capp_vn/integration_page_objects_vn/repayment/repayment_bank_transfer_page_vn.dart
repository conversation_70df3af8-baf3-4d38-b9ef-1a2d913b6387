import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../integration_page_objects/base/test_page_with_app_bar.dart';

class RepaymentBankTransferTestPageVn extends TestPageWithAppBar {
  final Finder _repaymentContractInfoTitleFinder = find.byKey(const Key('__contractInfoTitle__'));
  final Finder _repaymentTotalAmountInfoTitleFinder = find.byKey(const Key('__totalAmountInfo__'));
  final Finder __repaymentDueDateInfoFinder = find.byKey(const Key('__dueDateInfo__'));
  final Finder __repaymentContractNumberInfoFinder = find.byKey(const Key('__contractNumberInfo__'));
  final Finder __repaymentBankTransferInfoTitleFinder = find.byKey(const Key('__bankTransferInfoTitle__'));
  final Finder __repaymentAccountNameInfoFinder = find.byKey(const Key('__accountNameInfo__'));
  final Finder __repaymentBankNameInfoFinder = find.byKey(const Key('__bankNameInfo__'));
  final Finder __repaymentAccountNumberInfoFinder = find.byKey(const Key('__accountNumberInfo__'));
  final Finder __repaymentBankTransferContentFinder = find.byKey(const Key('__bankTransferContent__'));
  final Finder __repaymentBackToHomeButtonFinder = find.byKey(const Key('__backToHomeButton__'));

  RepaymentBankTransferTestPageVn(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__bankTransferScreen__')),
          appBarFinder: find.byKey(const Key('__bankTransferAppBar__')),
        );

  void verifyReady() {
    screen.isReady();
  }

  void isContractInfoTitleExist() {
    expect(_repaymentContractInfoTitleFinder, findsOneWidget);
  }

  void isTotalAmountInfoTitleExist() {
    expect(_repaymentTotalAmountInfoTitleFinder, findsOneWidget);
  }

  void isDueDateInfoExist() {
    expect(__repaymentDueDateInfoFinder, findsOneWidget);
  }

  void isContractNumberInfoExist() {
    expect(__repaymentContractNumberInfoFinder, findsOneWidget);
  }

  void isBankTransferInfoTitleExist() {
    expect(__repaymentBankTransferInfoTitleFinder, findsOneWidget);
  }

  void isAccountNameInfoExist() {
    expect(__repaymentAccountNameInfoFinder, findsOneWidget);
  }

  void isBankNameInfoExist() {
    expect(__repaymentBankNameInfoFinder, findsOneWidget);
  }

  void isAccountNumberInfoExist() {
    expect(__repaymentAccountNumberInfoFinder, findsOneWidget);
  }

  void isBankTransferContentExist() {
    expect(__repaymentBankTransferContentFinder, findsOneWidget);
  }

  void isBackToHomeButtonExist() {
    expect(__repaymentBackToHomeButtonFinder, findsOneWidget);
  }

  Future<void> tapBackToHomeButton() async {
    await tester.tap(__repaymentBackToHomeButtonFinder);
    await tester.pumpAndSettle();
  }
}
