import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../integration_page_objects/base/test_page_with_app_bar.dart';

class RepaymentSelectAmountRelTestPageVn extends TestPageWithAppBar {
  final Finder _selectAmountRelTitleFinder = find.byKey(const Key('__selectAmountRelTitle__'));
  final Finder _selectAmountMinDueAmountRelOptionFinder = find.byKey(const Key('__selectMinimumDueAmountRelOption__'));
  final Finder _selectAmountTotalDueAmountRelOptionFinder =
      find.byKey(const Key('__selectAmountTotalDueAmountRelOption__'));
  final Finder _selectAmountCustomAmountRelOptionFinder =
      find.byKey(const Key('__selectAmountCustomAmountRelOption__'));
  final Finder _selectAmountRelContinueButtonFinder = find.byKey(const Key('__selectAmountRelContinueButton__'));
  final Finder _selectAmountCustomAmountRelInputContainerFinder =
      find.byKey(const Key('__selectAmountCustomAmountRelInputContainer__'));
  final Finder _selectAmountCustomAmountRelInputTextFinder =
      find.byKey(const Key('__selectAmountCustomAmountRelInputText__'));
  final Finder _selectAmountRelPopupLessThanMinAmountFinder =
      find.byKey(const Key('__selectAmountRelPopupLessThanMinAmount__'));
  final Finder _selectAmountRelPopupLessThanMinAmountContinueButtonFinder =
      find.byKey(const Key('__selectAmountRelPopupLessThanMinAmountContinueButton__'));
  final Finder _selectAmountRelPopupLessThanMinThresholdAmountContinueButtonFinder =
      find.byKey(const Key('__selectAmountRelPopupLessThanMinThresholdAmountContinueButton__'));
  final Finder _selectAmountRelPopupLessThanMinThresholdAmountFinder =
      find.byKey(const Key('__selectAmountRelPopupLessThanMinThresholdAmount__'));

  RepaymentSelectAmountRelTestPageVn(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__selectAmountRelScreen__')),
          appBarFinder: find.byKey(const Key('__selectAmountRelAppBar__')),
        );

  void verifyReady() {
    screen.isReady();
  }

  void isTitleExist() {
    expect(_selectAmountRelTitleFinder, findsOneWidget);
  }

  void isMinDueAmountOptionExist() {
    expect(_selectAmountMinDueAmountRelOptionFinder, findsOneWidget);
  }

  void isMinDueAmountOptionNotExist() {
    expect(_selectAmountMinDueAmountRelOptionFinder, findsNothing);
  }

  void isTotalDueAmountOptionExist() {
    expect(_selectAmountTotalDueAmountRelOptionFinder, findsOneWidget);
  }

  void isCustomAmountOptionExist() {
    expect(_selectAmountCustomAmountRelOptionFinder, findsOneWidget);
  }

  void isCustomAmountInputTextExist() {
    expect(_selectAmountCustomAmountRelInputTextFinder, findsOneWidget);
  }

  void isCustomAmountInputContainerExist() {
    expect(_selectAmountCustomAmountRelInputContainerFinder, findsOneWidget);
  }

  void isContinueButtonExist() {
    expect(_selectAmountRelContinueButtonFinder, findsOneWidget);
  }

  void isPopupLessThanMinAmountExist() {
    expect(_selectAmountRelPopupLessThanMinAmountFinder, findsOneWidget);
  }

  Future<void> enterCustomAmount(int amount) async {
    await tester.enterText(_selectAmountCustomAmountRelInputTextFinder, amount.toString());
    await tester.pumpAndSettle();
  }

  Future<void> tapContinueButton() async {
    await tester.tap(_selectAmountRelContinueButtonFinder);
    await tester.pumpAndSettle();
  }

  Future<void> tapPopupLessThanMinAmountContinueButton() async {
    await tester.tap(_selectAmountRelPopupLessThanMinAmountContinueButtonFinder);
    await tester.pumpAndSettle();
  }

  Future<void> tapCustomAmountOption() async {
    await tester.tap(_selectAmountCustomAmountRelOptionFinder);
    await tester.pumpAndSettle();
  }

  Future<void> tapTotalDueAmountOption() async {
    await tester.tap(_selectAmountTotalDueAmountRelOptionFinder);
    await tester.pumpAndSettle();
  }

  Future<void> tapMinDueAmountOption() async {
    await tester.tap(_selectAmountMinDueAmountRelOptionFinder);
    await tester.pumpAndSettle();
  }

  void isMinAmountOptionSelected() {
    expect(tester.widget<SelectableContainer>(_selectAmountMinDueAmountRelOptionFinder).isSelected, true);
  }

  void isMinAmountOptionUnSelected() {
    expect(tester.widget<SelectableContainer>(_selectAmountMinDueAmountRelOptionFinder).isSelected, false);
  }

  void isTotalDueAmountOptionSelected() {
    expect(
      tester.widget<SelectableContainer>(_selectAmountTotalDueAmountRelOptionFinder).isSelected,
      true,
    );
  }

  void isTotalDueAmountOptionUnSelected() {
    expect(tester.widget<SelectableContainer>(_selectAmountTotalDueAmountRelOptionFinder).isSelected, false);
  }

  void isCustomAmountOptionSelected() {
    expect(
      tester.widget<SelectableContainer>(_selectAmountCustomAmountRelOptionFinder).isSelected,
      true,
    );
  }

  void isCustomAmountOptionUnSelected() {
    expect(tester.widget<SelectableContainer>(_selectAmountCustomAmountRelOptionFinder).isSelected, false);
  }

  void isContinueButtonEnable() {
    expect(tester.widget<PrimaryButton>(_selectAmountRelContinueButtonFinder).onPressed != null, true);
  }

  void isContinueButtonDisable() {
    expect(tester.widget<PrimaryButton>(_selectAmountRelContinueButtonFinder).onPressed != null, false);
  }

  void isPopupLessThanMinThresholdAmountExist() {
    expect(_selectAmountRelPopupLessThanMinThresholdAmountFinder, findsOneWidget);
  }

  Future<void> tapPopupLessThanMinThresholdAmountContinueButton() async {
    await tester.tap(_selectAmountRelPopupLessThanMinThresholdAmountContinueButtonFinder);
    await tester.pumpAndSettle();
  }
}
