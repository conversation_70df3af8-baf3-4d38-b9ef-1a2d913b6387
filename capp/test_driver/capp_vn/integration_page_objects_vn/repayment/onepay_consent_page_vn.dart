import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class OnePayConsentTestPageVn extends TestPage {
  final Finder _onepayConsentContinueButtonViewFinder = find.byKey(const Key('__consentPopupContinueButton__'));

  late TestElement continueButton;

  OnePayConsentTestPageVn(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__consentPopup__'))) {
    continueButton = registerButtonElement(finder: _onepayConsentContinueButtonViewFinder);
  }

  Future<void> tapContinue() async {
    await continueButton.tap();
  }
}
