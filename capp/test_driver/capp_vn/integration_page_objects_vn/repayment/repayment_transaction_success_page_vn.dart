import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../integration_page_objects/base/test_page_with_app_bar.dart';

class RepaymentTransactionSuccessTestPageVn extends TestPageWithAppBar {
  final Finder _feedbackForm = find.byKey(const Key('__feedbackForm__'));
  final Finder _exploreAdaButton = find.byKey(const Key('__exploreAdaButton__'));
  final Finder _doneButton = find.byKey(const Key('__doneButton__'));
  final Finder _adaReminderPopup_ = find.byKey(const Key('__adaReminderDialog__'));
  final Finder _adaReminderLaterButton_ = find.byKey(const Key('__adaReminderDialogLater__'));
  final Finder _adaReminderExploreButton_ = find.byKey(const Key('__adaReminderDialogExplore__'));

  RepaymentTransactionSuccessTestPageVn(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__transactionSuccessBaseScreen__')),
          appBarFinder: find.byKey(const Key('__transactionSuccessBaseAppBar__')),
        );

  void verifyReady() {
    screen.isReady();
  }

  void isFeedbackDisplayed() {
    expect(_feedbackForm, findsOneWidget);
  }

  void isExploreAdaButtonDisplayed() {
    expect(_exploreAdaButton, findsOneWidget);
  }

  void isExploreAdaButtonNotDisplayed() {
    expect(_exploreAdaButton, findsNothing);
  }

  void isAdaReminderPopupDisplayed() {
    expect(_adaReminderPopup_, findsOneWidget);
  }

  void isAdaReminderLaterButtonDisplayed() {
    expect(_adaReminderLaterButton_, findsOneWidget);
  }

  void isAdaReminderExploreButtonDisplayed() {
    expect(_adaReminderExploreButton_, findsOneWidget);
  }

  Future<void> tapExploreAdaButton() async {
    await tester.tap(_exploreAdaButton);
    await tester.pumpAndSettle();
  }

  Future<void> tapDoneButton() async {
    await tester.tap(_doneButton);
    await tester.pumpAndSettle();
  }

  Future<void> tapAdaReminderExploreButton() async {
    await tester.tap(_adaReminderExploreButton_);
    await tester.pumpAndSettle();
  }

  Future<void> tapAdaReminderLaterButton() async {
    await tester.tap(_adaReminderLaterButton_);
    await tester.pumpAndSettle();
  }
}
