import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../../../../../packages/capp_cards/capp_cards_vn/lib/capp_cards.dart';
import '../../../../integration_page_objects/base/test_page_with_app_bar.dart';

class TransactionsPage extends TestPageWithAppBar {
  late final Finder _selectableChipFinder;
  late final Finder _listViewFinder;
  late final Finder _emptyViewFinder;

  late final TestElement last10DaysButton;
  late final TestElement last30DaysButton;
  late final TestElement dateRangeButton;
  late final TestElement listView;
  late final TestElement emptyView;

  TransactionsPage({required WidgetTester tester})
      : super(
          tester: tester,
          appBarFinder: find.byKey(const Key('__TransactionsAppBar__')),
          screenFinder: find.byKey(const Key('__TransactionsScaffold__')),
        ) {
    _selectableChipFinder = find.byType(SelectableChip);
    _listViewFinder = find.byKey(const Key('__transactionsListView__'));
    _emptyViewFinder = find.byType(AnnouncementContainer);

    last10DaysButton = registerElement(finder: _selectableChipFinder.at(0));
    last30DaysButton = registerElement(finder: _selectableChipFinder.at(1));
    dateRangeButton = registerElement(finder: _selectableChipFinder.at(2));

    listView = registerElement(finder: _listViewFinder);
    emptyView = registerElement(finder: _emptyViewFinder);
  }

  void checkAppBarTitle(String title) {
    appBarTitle.textEquals(title);
  }

  void checkExpectedElements() {
    expect(_selectableChipFinder, findsNWidgets(3));
    expect(_listViewFinder, findsOneWidget);
  }

  Future<void> checkExpectedTransactionsItem(int itemCount) async {
    if (itemCount == 0) {
      expect(emptyView, findsOneWidget);
      return;
    }
    //Find TransactionTile
    expect(find.descendant(of: _listViewFinder, matching: find.byType(TransactionTile)), findsNWidgets(itemCount));
  }

  Future<void> tapOnTransactionsItem(int position) async {
    //Find TransactionTile
    final finder = find.descendant(of: _listViewFinder, matching: find.byType(TransactionTile));
    return tester.tap(finder.at(position));
  }
}
