import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../../../integration_page_objects/base/test_page_with_app_bar.dart';

class CardsKycIntroPage extends TestPageWithAppBar {
  late final Finder _scrollFinder;
  late final Finder _mainHeading;
  late final Finder _benefitsOverviewHeader;
  late final Finder _continueButton;
  late final Finder _benefitsOverviewFirstItem;
  late final Finder _benefitsOverview2ndItem;

  late final TestElement continueButton;

  CardsKycIntroPage({required WidgetTester tester})
      : super(
          tester: tester,
          appBarFinder: find.byKey(const Key('__kycIntroKoyalAppBar__')),
          screenFinder: find.byKey(const Key('__kycIntroScaffold__')),
        ) {
    _scrollFinder = find.byKey(const Key('__kycIntroScrollView__'));
    _mainHeading = find.byKey(const Key('__kycIntroMainHeading__'));
    _benefitsOverviewHeader = find.byKey(const Key('__benefitsOverviewHeader__'));
    _continueButton = find.byKey(const Key('__kycIntroPrimaryButton__'));
    _benefitsOverviewFirstItem = find.byKey(const Key('__benefitsOverviewFirstItem__'));
    _benefitsOverview2ndItem = find.byKey(const Key('__benefitsOverview2ndItem__'));
    continueButton = registerButtonElement(finder: _continueButton);
  }

  void checkAppBarTitle(String title) {
    appBarTitle.textEquals(title);
  }

  Future<void> findMainHeadingFinder() async {
    return tester.dragUntilVisible(_mainHeading, _scrollFinder, const Offset(0, -100));
  }

  Future<void> findBenefitsOverviewHeaderFinder() async {
    return tester.dragUntilVisible(_benefitsOverviewHeader, _scrollFinder, const Offset(0, -100));
  }

  void primaryButtonExist() {
    expect(_continueButton, findsWidgets);
  }

  void benefitsOverviewHeaderExist() {
    expect(_benefitsOverviewHeader, findsWidgets);
  }

  void benefitsOverviewFirstItemExist() {
    expect(_benefitsOverviewFirstItem, findsWidgets);
  }

  void benefitsOverview2ndItemExist() {
    expect(_benefitsOverview2ndItem, findsWidgets);
  }
}
