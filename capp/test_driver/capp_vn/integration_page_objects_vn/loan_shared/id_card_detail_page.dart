import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../../integration_page_objects/base/test_page_with_app_bar.dart';

class IdCardDetailsLoanSharedPage extends TestPageWithAppBar {
  late TestElement _idNumberField;
  late TestElement _dateBirthField;
  late TestElement _dateIssueField;
  late TestElement _dateExpiryField;
  late TestElement _fullNameField;
  late TestElement _submitButton;
  late TestElement _calendarDialogTextField;
  late TestElement _confirmDateButton;
  late TestElement _regionRaField;
  late TestElement _districtRaField;
  late TestElement _wardRaField;
  late TestElement _streetNameRaField;
  late TestElement _houseNumberRaField;
  late TestElement _regionCaField;
  late TestElement _districtCaField;
  late TestElement _wardCaField;
  late TestElement _streetNameCaField;
  late TestElement _houseNumberCaField;
  late TestElement _radioRowCorrespondent;
  late TestElement _radioCaSecondChoice;
  late TestElement _radioMaleGender;
  late TestElement _dropDownItem;
  late TestElement _addressSearchBar;

  IdCardDetailsLoanSharedPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__idCardDetailScreenLoanShared__')),
          appBarFinder: find.byKey(const Key('__idCardDetailAppBarLoanShared__')),
        ) {
    _idNumberField =
        registerTextFieldElement(finder: find.byKey(const Key('__idCardDetailIdNumberTextFieldLoanShared__')));
    _dateIssueField = registerElement(finder: find.byKey(const Key('__idCardDetailDateIssueInputFieldLoanShared__')));
    _fullNameField =
        registerTextFieldElement(finder: find.byKey(const Key('__idCardDetailFullNameTextFieldLoanShared__')));
    _dateBirthField = registerElement(finder: find.byKey(const Key('__idCardDetailDateBirthInputFieldLoanShared__')));
    _dateExpiryField = registerElement(finder: find.byKey(const Key('__idCardDetailDateExpiryInputFieldLoanShared__')));
    _calendarDialogTextField =
        registerElement(finder: find.byType(InputDatePickerFormField), type: TestElementType.textfield);
    _confirmDateButton = registerButtonElement(finder: find.byKey(const Key('__confirmDateButton__')));
    _regionRaField = registerElement(finder: find.byKey(const Key('__IdCardRaRegionFieldLoanShared__')));
    _districtRaField = registerElement(finder: find.byKey(const Key('__IdCardRaDistrictFieldLoanShared__')));
    _wardRaField = registerElement(finder: find.byKey(const Key('__IdCardRaWardFieldLoanShared__')));
    _streetNameRaField =
        registerTextFieldElement(finder: find.byKey(const Key('__IdCardRaStreetNameTextFieldLoanShared__')));
    _houseNumberRaField =
        registerTextFieldElement(finder: find.byKey(const Key('__IdCardRaHouseNumberTextFieldLoanShared__')));
    _regionCaField = registerElement(finder: find.byKey(const Key('__IdCardCaRegionFieldLoanShared__')));
    _districtCaField = registerElement(finder: find.byKey(const Key('__IdCardCaDistrictFieldLoanShared__')));
    _wardCaField = registerElement(finder: find.byKey(const Key('__IdCardCaWardFieldLoanShared__')));
    _streetNameCaField =
        registerTextFieldElement(finder: find.byKey(const Key('__IdCardCaStreetNameTextFieldLoanShared__')));
    _houseNumberCaField =
        registerTextFieldElement(finder: find.byKey(const Key('__IdCardCaHouseNumberTextFieldLoanShared__')));
    _radioRowCorrespondent =
        registerElement(finder: find.byKey(const Key('__idCardDetailisCorrAddressRadioRowLoanShared__')));
    _radioCaSecondChoice =
        registerButtonElement(finder: find.byKey(const Key('__RadioRowisCorrespondenceSecondChoice__')));
    _radioMaleGender = registerElement(finder: find.byKey(const Key('__RadioRowGenderFirstChoice__')));
    _dropDownItem = registerElement(finder: find.byKey(const Key('__idCardAddressDropDownItemLoanShared__')));
    _addressSearchBar = registerTextFieldElement(finder: find.byKey(const Key('__idCardAddressSearchBarLoanShared__')));
    _submitButton = registerButtonElement(finder: find.byKey(const Key('__idCardDetailSubmitButtonLoanShared__')));
  }

  Future<void> enterIdNumber(String text) async {
    await _idNumberField.enterText(text, settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterFullName(String text) async {
    await _fullNameField.scrollTo(settleAfterPump: false);
    await _fullNameField.enterText(text, settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> tapOnContinue() async {
    await _submitButton.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterBirthDate(String date) async {
    await _dateBirthField.scrollTo(settleAfterPump: false);
    await _dateBirthField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _calendarDialogTextField.enterText(date, settleAfterPump: false, duration: const Duration(seconds: 1));
    await tester.testTextInput.receiveAction(TextInputAction.done);
    await tester.pump(const Duration(seconds: 2));
    await _confirmDateButton.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterExpireDate(String date) async {
    await _dateExpiryField.scrollTo(settleAfterPump: false);
    await _dateExpiryField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _calendarDialogTextField.enterText(date, settleAfterPump: false, duration: const Duration(seconds: 1));
    await tester.testTextInput.receiveAction(TextInputAction.done);
    await tester.pump(const Duration(seconds: 2));
    await _confirmDateButton.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterIssueDate(String date) async {
    await _dateIssueField.scrollTo(settleAfterPump: false);
    await _dateIssueField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _calendarDialogTextField.enterText(date, settleAfterPump: false, duration: const Duration(seconds: 1));
    await tester.testTextInput.receiveAction(TextInputAction.done);
    await tester.pump(const Duration(seconds: 2));
    await _confirmDateButton.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterRaRegion(String region) async {
    await _regionRaField.scrollTo(settleAfterPump: false);
    await _regionRaField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _addressSearchBar.enterText(region, settleAfterPump: false, duration: const Duration(seconds: 1));
    await _dropDownItem.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterRaDistrict(String district) async {
    await _districtRaField.scrollTo(settleAfterPump: false);
    await _districtRaField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _addressSearchBar.enterText(district, settleAfterPump: false, duration: const Duration(seconds: 1));
    await _dropDownItem.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterRaWard(String ward) async {
    await _wardRaField.scrollTo(settleAfterPump: false);
    await _wardRaField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _addressSearchBar.enterText(ward, settleAfterPump: false, duration: const Duration(seconds: 1));
    await _dropDownItem.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterRaStreetName(String text) async {
    await _streetNameRaField.scrollTo(settleAfterPump: false);
    await _streetNameRaField.enterText(text, settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterRaHouseNumber(String text) async {
    await _houseNumberRaField.scrollTo(settleAfterPump: false);
    await _houseNumberRaField.enterText(text, settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterCaRegion(String region) async {
    await _regionCaField.scrollTo(settleAfterPump: false);
    await _regionCaField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _addressSearchBar.enterText(region, settleAfterPump: false, duration: const Duration(seconds: 1));
    await _dropDownItem.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterCaDistrict(String district) async {
    await _districtCaField.scrollTo(settleAfterPump: false);
    await _districtCaField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _addressSearchBar.enterText(district, settleAfterPump: false, duration: const Duration(seconds: 1));
    await _dropDownItem.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterCaWard(String ward) async {
    await _wardCaField.scrollTo(settleAfterPump: false);
    await _wardCaField.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
    await _addressSearchBar.enterText(ward, settleAfterPump: false, duration: const Duration(seconds: 1));
    await _dropDownItem.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterCaStreetName(String text) async {
    await _streetNameCaField.scrollTo(settleAfterPump: false);
    await _streetNameCaField.enterText(text, settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> enterCaHouseNumber(String text) async {
    await _houseNumberCaField.scrollTo(settleAfterPump: false);
    await _houseNumberCaField.enterText(text, settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> selectIsNotCorrespondent() async {
    await _radioRowCorrespondent.scrollTo(settleAfterPump: false);
    await _radioCaSecondChoice.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }

  Future<void> selectMaleGender() async {
    await _radioMaleGender.scrollTo(settleAfterPump: false);
    await _radioMaleGender.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }
}
