import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

import '../../../integration_page_objects/base/test_page_with_app_bar.dart';

class IdCardInstructionsLoanSharedPage extends TestPageWithAppBar {
  late TestElement _continueButton;

  IdCardInstructionsLoanSharedPage(WidgetTester tester)
      : super(
          tester: tester,
          screenFinder: find.byKey(const Key('__idCardInstructionScreenLoanShared__')),
          appBarFinder: find.byKey(const Key('__idCardInstructionAppBarLoanShared__')),
        ) {
    _continueButton =
        registerButtonElement(finder: find.byKey(const Key('__idCardInstructionContinueButtonLoanShared__')));
  }

  Future<void> tapOnContinue(String text) async {
    await _continueButton.tap(settleAfterPump: false, duration: const Duration(seconds: 1));
  }
}
