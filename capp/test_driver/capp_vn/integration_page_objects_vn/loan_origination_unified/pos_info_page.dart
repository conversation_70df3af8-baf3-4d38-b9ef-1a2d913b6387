import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class PosInfoTestPage extends TestPage {
  PosInfoTestPage(WidgetTester tester) : super(tester: tester, screenFinder: find.byKey(const Key('__posInfoPage__'))) {
    navigateToHomeBtn = registerButtonElement(finder: find.byKey(const Key('__posInfoPage_navigateToHomeBtn__')));
    checkAnotherOfferBtn = registerButtonElement(finder: find.byKey(const Key('__posInfoPage_checkAnotherOfferBtn__')));
  }
  late final TestElement navigateToHomeBtn;
  late final TestElement checkAnotherOfferBtn;
  late final TestElement visitStoreBtn;

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  Future<void> navigateToHome() {
    navigateToHomeBtn.isReady();
    return navigateToHomeBtn.tap();
  }
}
