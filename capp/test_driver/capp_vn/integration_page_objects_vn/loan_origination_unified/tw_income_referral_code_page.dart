import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';

class TwIncomeSAReferralCodePage extends TestPage {
  TwIncomeSAReferralCodePage(WidgetTester tester)
      : super(tester: tester, screenFinder: find.byKey(const Key('__twIncomeReferralCodePage__'))) {
    codeInput = registerTextFieldElement(finder: find.byKey(const Key('__twReferralCodeInput__')));
    incomeInput = registerTextFieldElement(finder: find.byKey(const Key('__twIncomeInput__')));
    verifySaCodeBtn = registerButtonElement(finder: find.byKey(const Key('__twReferralCodeVerifyButton__')));
    confirmBtn = registerButtonElement(finder: find.byKey(const Key('_twIncomereferralCodeConfirmButton__')));
    saName = registerButtonElement(finder: find.byKey(const Key('__twReferralCodeSaName__')));
  }
  late final TestElement codeInput;
  late final TestElement verifySaCodeBtn;
  late final TestElement incomeInput;
  late final TestElement confirmBtn;
  late final TestElement saName;

  void verifyReady() {
    expect(screenFinder, findsOneWidget);
  }

  void verifyAbsent() {
    expect(screenFinder, findsNothing);
  }

  Future<void> fillSACode({
    required String code,
  }) async {
    final textFieldFinder = find.descendant(
      of: codeInput.finder,
      matching: find.byType(TextField),
    );
    await tester.enterText(textFieldFinder, code);
    await tester.testTextInput.receiveAction(TextInputAction.done);
    await tester.pumpAndSettle();
  }

  Future<void> fillIncome({
    required String income,
  }) async {
    final textFieldFinder = find.descendant(
      of: incomeInput.finder,
      matching: find.byType(TextField),
    );
    await tester.enterText(textFieldFinder, income);
    await tester.testTextInput.receiveAction(TextInputAction.done);
    await tester.pumpAndSettle();
  }

  void verifyValidSACode() {
    late final l10n = L10nCappLoanOriginationUnified.of(
      tester.element(screenFinder),
    );
    expect(find.textContaining(l10n.referralCodeVerified), findsAny);
    expect(find.textContaining(l10n.invalidReferralCode), findsNothing);
    saName.isReady();
  }

  void verifyInvalidSACode() {
    late final l10n = L10nCappLoanOriginationUnified.of(
      tester.element(screenFinder),
    );
    expect(find.textContaining(l10n.referralCodeVerified), findsNothing);
    expect(find.textContaining(l10n.invalidReferralCode), findsAny);
  }

  void verifyRemindPoup() {
    late final l10n = L10nCappLoanOriginationUnified.of(
      tester.element(screenFinder),
    );
    expect(find.byType(KoyalOverlay), findsAny);
    expect(find.textContaining(l10n.twPopupTitle), findsAny);
    expect(find.textContaining(l10n.twPopupSubTitle), findsAny);
    expect(find.textContaining(l10n.twPopupDontHaveReferralCode), findsAny);
  }
}
