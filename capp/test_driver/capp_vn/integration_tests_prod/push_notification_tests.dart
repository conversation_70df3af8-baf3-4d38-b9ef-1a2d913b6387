import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../integration_reuse_steps/push_notifications.dart';
import '../../integration_tests_tools/tester_helper.dart';
import '../integration_tests_tools/integration_tests_vn.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Acq_A:Push notification Tests', () {
    integrationTestProdVn('Acq_A:Test that push notification arrives to the device', (tester, context) async {
      final testerHelper = TesterHelper(tester: tester, context: context);
      await testPushNotificationDelivery(tester, testerHelper);
    });
  });
}
