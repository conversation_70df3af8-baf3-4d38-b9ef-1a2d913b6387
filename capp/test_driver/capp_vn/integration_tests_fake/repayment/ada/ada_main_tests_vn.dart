import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment/capp_repayment.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects_vn/repayment/ada/ada_contract_list_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/ada/ada_main_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/ada/ada_payment_method_page_vn.dart';
import '../../../integration_tests_tools/tester_helper_vn.dart';
import '../repayment_new/common/repayment_new_common_steps.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Ada Main screen', () {
    integrationTest(
      'Ada Main screen initialization, select contract, select ZaloPay, tap confirm',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final mainPage = AdaMainTestPageVn(tester);
        final paymentMethodPage = AdaPaymentMethodTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentMethodPopup__')),
        );
        final contractListPage = AdaContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );

        // FF setup
        testerHelper
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitOnepay, isEnabled: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitZalopay, isEnabled: true);

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '0233322232', password: '123');
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Navigate to ada main screen to add new
        await testerHelper.navigateToAdaMain(routeArgs: RepaymentAdaMainRouteArgs(isSkipDisplayIntro: true));
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Check contract main exists
        mainPage.checkContractMainExist();

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(seconds: 1));

        // Select first contract
        await contractListPage.tapFirstContract();

        // Tap payment method main
        await mainPage.tapPaymentMethodMain();
        paymentMethodPage.isReady();

        // Check badge of payment methods is valid
        mainPage.checkBadgeMethodItemExist(RepaymentPaymentMethod.onepay);

        // Tap Zalo Pay payment method
        await paymentMethodPage.tapPaymentMethod(
          RepaymentUserPaymentMethod(
            category: RepaymentPaymentCategory.ewallet.name,
            gmaId: RepaymentPaymentMethod.zalo.getId(),
            description: '',
            badge: '',
            iconUrl: '',
            title: 'ZaloPay',
          ),
        );

        // Agree checkbox by default
        mainPage.checkAgreeCheckBoxEnable(isEnable: true);

        // Check continue button enable to register ZaloPay
        paymentMethodPage.isAbsent();
        mainPage
          ..isReady()
          ..checkPaymentMethodMainExist()
          ..checkConfirmButtonEnable();

        // Tap payment method main
        await mainPage.tapPaymentMethodMain();
        paymentMethodPage.isReady();

        // Tap OnePay payment method
        await paymentMethodPage.tapPaymentMethod(
          RepaymentUserPaymentMethod(
            category: RepaymentPaymentCategory.ewallet.name,
            gmaId: RepaymentPaymentMethod.onepay.getId(),
            description: '',
            badge: '',
            iconUrl: '',
            title: 'OnePay',
          ),
        );
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });
}
