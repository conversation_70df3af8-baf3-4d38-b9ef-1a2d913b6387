import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_shared_core/capp_loan_shared_core.dart';
import 'package:capp_repayment/capp_repayment.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects_vn/repayment/ada/ada_intro_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/ada/ada_onepay_page.dart';
import '../../../integration_page_objects_vn/repayment/ada/ada_payment_method_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/ada/loo_ada_main_page.dart';
import '../../../integration_tests_tools/tester_helper_vn.dart';
import '../repayment_new/common/repayment_new_common_steps.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Loo ADA register new', () {

    integrationTest(
      'Ada Main screen initialization, select contract, select OnePay, tap confirm',
          (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final adaIntroPage = AdaIntroTestPageVn(tester);
        final looAdaMain = LooAdaMainTestPage(tester);
        final onePayPage = AdaOnepayTestPageVn(tester);
        final paymentMethodPage = AdaPaymentMethodTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentMethodPopup__')),
        );

        // FF setup
        testerHelper
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitOnepay, isEnabled: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitZalopay, isEnabled: true);

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '0233322232', password: '123');
        await testerHelper.delay(const Duration(microseconds: 1000));
        // Navigate to ada processing screen
        await testerHelper.navigateToAdaProcessingScreen(routeArgs: AdaProcessingArguments(
          elementId: 'sdfasdfasdfasdfdasf',
          flowInstanceId: '1212233-3507-11ee-be56-0242ac120002',
          contractType: 'CEL',
          flow: 'loanJourney',
        ),);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Check contract main exists
        await adaIntroPage.isPresent();
        adaIntroPage.checkContinueButtonExist();
        await testerHelper.delay(const Duration(milliseconds: 500));
        await adaIntroPage.tapContinue();
        await testerHelper.delay(const Duration(seconds: 1));

        looAdaMain.isReady();

        // Tap payment method main
        await looAdaMain.tapPaymentMethodMain();
        paymentMethodPage.isReady();

        // Tap OnePay payment method
        await paymentMethodPage.tapPaymentMethod(
          RepaymentUserPaymentMethod(
            category: RepaymentPaymentCategory.ewallet.name,
            gmaId: RepaymentPaymentMethod.onepay.getId(),
            description: '',
            badge: '',
            iconUrl: '',
            title: 'OnePay',
          ),
        );

        // Agree checkbox by default
        looAdaMain.checkAgreeCheckBoxEnable(isEnable: true);

        // Check continue button enable to register ZaloPay
        paymentMethodPage.isAbsent();
        looAdaMain
          ..isReady()
          ..checkPaymentMethodMainExist()
          ..checkConfirmButtonEnable();

        await testerHelper.delay(const Duration(milliseconds: 500));
        await looAdaMain.tapConfirm();

        await testerHelper.delay(const Duration(milliseconds: 2000));
        onePayPage.isReady();

      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Ada Main screen initialization, select contract, select ZaloPay',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final adaIntroPage = AdaIntroTestPageVn(tester);
        final looAdaMain = LooAdaMainTestPage(tester);
        final paymentMethodPage = AdaPaymentMethodTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentMethodPopup__')),
        );

        // FF setup
        testerHelper
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitOnepay, isEnabled: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitZalopay, isEnabled: true);

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '0233322232', password: '123');
        await testerHelper.delay(const Duration(microseconds: 1000));
        // Navigate to ada processing screen
        await testerHelper.navigateToAdaProcessingScreen(routeArgs: AdaProcessingArguments(
          elementId: 'sdfasdfasdfasdfdasf',
          flowInstanceId: '1212233-3507-11ee-be56-0242ac120002',
          contractType: 'CEL',
          flow: 'loanJourney',
        ),);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Check contract main exists
        await adaIntroPage.isPresent();
        adaIntroPage.checkContinueButtonExist();
        await testerHelper.delay(const Duration(milliseconds: 500));
        await adaIntroPage.tapContinue();
        await testerHelper.delay(const Duration(milliseconds: 1000));

        looAdaMain.isReady();

        // Tap payment method main
        await looAdaMain.tapPaymentMethodMain();
        paymentMethodPage.isReady();

        // // Tap Zalo Pay payment method
        await paymentMethodPage.tapPaymentMethod(
          RepaymentUserPaymentMethod(
            category: RepaymentPaymentCategory.ewallet.name,
            gmaId: RepaymentPaymentMethod.zalo.getId(),
            description: '',
            badge: '',
            iconUrl: '',
            title: 'ZaloPay',
          ),
        );

        await testerHelper.delay(const Duration(milliseconds: 500));
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });

  group('Loo ADA cancel', () {

    integrationTest(
      'Ada Main screen view detail - Cancel Zalo Pay - Success/Failed',
          (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final looAdaMain = LooAdaMainTestPage(tester);

        // FF setup
        testerHelper
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitOnepay, isEnabled: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitZalopay, isEnabled: true);

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '0233322232', password: '123');
        await testerHelper.delay(const Duration(microseconds: 1000));
        // Navigate to loo ada main screen
        await testerHelper.navigateToLooAdaMain(
          routeArgs: LooAdaMainRouteArgs(
            flowInstanceId: '1212233-3507-11ee-be56-0242ac120002',
            contractType: 'CEL',
            ddmCode: '4434343434',
            savedPaymentMethodId: 'zaloPay',
          ),
        );
        await testerHelper.delay(const Duration(microseconds: 1000));

        looAdaMain
          ..isReady()
          ..checkPaymentMethodMainChangeButtonExist(isExist: false)
          ..checkConfirmButtonExist(isExist: false)
          ..checkCancelButtonExist();

        await testerHelper.delay(const Duration(milliseconds: 500));

        // Tap cancel button
        await looAdaMain.tapCancelButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        looAdaMain.checkCancelPopupExist(isExisted: true);

        // Tap back button
        await looAdaMain.tapCancelPopupBackButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        looAdaMain.checkCancelPopupExist(isExisted: false);

        // Tap cancel button
        await looAdaMain.tapCancelButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        looAdaMain.checkCancelPopupExist(isExisted: true);
        // Tap delete button
        await looAdaMain.tapCancelPopupDeleteButton();
        await testerHelper.delay(const Duration(seconds: 2));
        looAdaMain..checkCancelPopupExist(isExisted: false)
        ..isAbsent();

        // Test failed case
        // Navigate to loo ada main screen
        await testerHelper.navigateToLooAdaMain(
          routeArgs: LooAdaMainRouteArgs(
            flowInstanceId: 'cancel-failed-flow-id',
            contractType: 'CEL',
            ddmCode: '4434343434',
            savedPaymentMethodId: 'zaloPay',
          ),
        );
        await testerHelper.delay(const Duration(microseconds: 1000));
        // Tap cancel button
        await looAdaMain.tapCancelButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        // Tap delete button
        await looAdaMain.tapCancelPopupDeleteButton();
        await testerHelper.delay(const Duration(seconds: 2));
        looAdaMain..checkCancelPopupExist(isExisted: false)
          ..checkCancelErrorPopupExist(isExisted: true);
        await looAdaMain.tapCancelErrorPopupTryAgainButton();
        await testerHelper.delay(const Duration(milliseconds: 200));
        looAdaMain.checkCancelErrorPopupExist(isExisted: false);
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Ada Main screen view detail - Cancel One Pay - Success/Failed',
          (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final looAdaMain = LooAdaMainTestPage(tester);

        // FF setup
        testerHelper
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitOnepay, isEnabled: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.autoDebitZalopay, isEnabled: true);

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '0233322232', password: '123');
        await testerHelper.delay(const Duration(microseconds: 1000));
        // Navigate to loo ada main screen
        await testerHelper.navigateToLooAdaMain(
          routeArgs: LooAdaMainRouteArgs(
            flowInstanceId: '1212233-3507-11ee-be56-0242ac120002',
            contractType: 'CEL',
            ddmCode: '4434343434',
            savedPaymentMethodId: 'onePay',
          ),
        );
        await testerHelper.delay(const Duration(microseconds: 1000));

        looAdaMain
          ..isReady()
          ..checkPaymentMethodMainChangeButtonExist(isExist: false)
          ..checkConfirmButtonExist(isExist: false)
          ..checkCancelButtonExist();

        await testerHelper.delay(const Duration(milliseconds: 500));

        // Tap cancel button
        await looAdaMain.tapCancelButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        looAdaMain.checkCancelPopupExist(isExisted: true);

        // Tap back button
        await looAdaMain.tapCancelPopupBackButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        looAdaMain.checkCancelPopupExist(isExisted: false);

        // Tap cancel button
        await looAdaMain.tapCancelButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        looAdaMain.checkCancelPopupExist(isExisted: true);
        // Tap delete button
        await looAdaMain.tapCancelPopupDeleteButton();
        await testerHelper.delay(const Duration(seconds: 2));
        looAdaMain..checkCancelPopupExist(isExisted: false)
          ..isAbsent();

        // Test failed case
        // Navigate to loo ada main screen
        await testerHelper.navigateToLooAdaMain(
          routeArgs: LooAdaMainRouteArgs(
            flowInstanceId: 'cancel-failed-flow-id',
            contractType: 'CEL',
            ddmCode: '4434343434',
            savedPaymentMethodId: 'onePay',
          ),
        );
        await testerHelper.delay(const Duration(microseconds: 1000));
        // Tap cancel button
        await looAdaMain.tapCancelButton();
        await testerHelper.delay(const Duration(milliseconds: 500));
        // Tap delete button
        await looAdaMain.tapCancelPopupDeleteButton();
        await testerHelper.delay(const Duration(seconds: 2));
        looAdaMain..checkCancelPopupExist(isExisted: false)
          ..checkCancelErrorPopupExist(isExisted: true);
        await looAdaMain.tapCancelErrorPopupTryAgainButton();
        await testerHelper.delay(const Duration(milliseconds: 200));
        looAdaMain.checkCancelErrorPopupExist(isExisted: false);
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });
}
