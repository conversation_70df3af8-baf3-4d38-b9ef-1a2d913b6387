import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects_vn/repayment/repayment_online_payment_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/repayment_select_amount_cel_page_vn.dart';
import '../../../integration_tests_tools/tester_helper_vn.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group(
      'Amount selection screen - Contract has due amount and total outstanding debt greater than default threshold amount (50.000)',
      () {
    final repaymentLoanContract = RepaymentLoanContract(
      contractNumber: '0354468901',
      contractType: 'CEL',
      loanType: 'cash',
      contractStatus: 'active',
      productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
      productName: 'Cash Loan',
      dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
      dueAmount: Decimal.parse('69000'),
      totalOutstandingDebt: Decimal.parse('2000000'),
    );

    integrationTest(
      'Auto select due amount when screen opened - Continue with due amount option',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt greater than default threshold amount (50.000).',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(repaymentLoanContract.dueAmount!);
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount greater than due amount - Continue',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt greater than default threshold amount (50.000).',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click custom amount option
        await selectAmountPage.tapCustomAmountOption();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check components existed
        selectAmountPage
          ..isCustomAmountInputContainerExist()
          ..isCustomAmountInputTextExist();

        // Enter custom amount greater than due amount
        await selectAmountPage.enterCustomAmount('500000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(Decimal.parse('500000'));
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount greater than total outstanding debt - Continue',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt greater than default threshold amount (50.000).',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click custom amount option
        await selectAmountPage.tapCustomAmountOption();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check components existed
        selectAmountPage
          ..isCustomAmountInputContainerExist()
          ..isCustomAmountInputTextExist();

        // Enter custom amount greater than total outstanding deb
        await selectAmountPage.enterCustomAmount('3000000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(repaymentLoanContract.totalOutstandingDebt!);
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount less than due amount - Continue',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt greater than default threshold amount (50.000).',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click custom amount option
        await selectAmountPage.tapCustomAmountOption();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check components existed
        selectAmountPage
          ..isCustomAmountInputContainerExist()
          ..isCustomAmountInputTextExist();

        // Enter custom amount less than due amount
        await selectAmountPage.enterCustomAmount('60000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check due amount warning popup existed
        selectAmountPage.isDueAmountWarningPopupExist();

        await selectAmountPage.tapDueAmountWarningPopupContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(Decimal.parse('60000'));
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount less than default threshold amount - Continue disabled',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt greater than default threshold amount (50.000).',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click custom amount option
        await selectAmountPage.tapCustomAmountOption();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check components existed
        selectAmountPage
          ..isCustomAmountInputContainerExist()
          ..isCustomAmountInputTextExist();

        // Enter custom amount less than default threshold amount (50.000)
        await selectAmountPage.enterCustomAmount('40000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check continue button disabled
        selectAmountPage.isContinueButtonDisable();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });

  group('Amount selection screen - Contract has due amount less than default threshold amount (50.000)', () {
    final repaymentLoanContract = RepaymentLoanContract(
      contractNumber: '0354468901',
      contractType: 'CEL',
      loanType: 'cash',
      contractStatus: 'active',
      productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
      productName: 'Cash Loan',
      dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
      dueAmount: Decimal.parse('35000'),
      totalOutstandingDebt: Decimal.parse('2000000'),
    );

    integrationTest(
      'Auto select custom amount option when screen opened',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount less than default threshold amount (50.000)',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check custom amount option selected
        selectAmountPage.isCustomAmountOptionSelected();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount greater than due amount and default threshold amount - Continue',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount less than default threshold amount (50.000)',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check custom amount option selected
        selectAmountPage
          ..isCustomAmountOptionSelected()
          // Check components existed
          ..isCustomAmountInputContainerExist()
          ..isCustomAmountInputTextExist();

        // Enter custom amount greater than due amount
        await selectAmountPage.enterCustomAmount('80000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(Decimal.parse('80000'));
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount greater than total outstanding debt - Continue',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount less than default threshold amount (50.000)',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check custom amount option selected
        selectAmountPage
          ..isCustomAmountOptionSelected()
          // Check components existed
          ..isCustomAmountInputContainerExist()
          ..isCustomAmountInputTextExist();

        // Enter custom amount greater than total outstanding deb
        await selectAmountPage.enterCustomAmount('3000000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(repaymentLoanContract.totalOutstandingDebt!);
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select due amount option',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
            'Navigate to the repayment CEL amount selection screen with contract has due amount less than default threshold amount (50.000)',);
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click due amount option
        await selectAmountPage.tapDueAmountOption();
        await testerHelper.delay(const Duration(microseconds: 5000));

        // Check due amount warning popup existed
        selectAmountPage.isLessThanThresholdAmountWarningPopupExist();

        await selectAmountPage.tapLessThanThresholdAmountWarningPopupContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check custom amount option selected automatically
        selectAmountPage.isCustomAmountOptionSelected();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });

  group(
      'Amount selection screen - Contract has due amount and total outstanding debt less than default threshold amount (50.000)',
      () {
    final repaymentLoanContract = RepaymentLoanContract(
      contractNumber: '0354468901',
      contractType: 'CEL',
      loanType: 'cash',
      contractStatus: 'active',
      productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
      productName: 'Cash Loan',
      dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
      dueAmount: Decimal.parse('45000'),
      totalOutstandingDebt: Decimal.parse('45000'),
    );

    integrationTest(
      'Auto select due amount option when screen opened',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);
        // navigate to CEL amount selection
        testerHelper.logger.i(
          'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt less than default threshold amount (50.000)',
        );
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(repaymentLoanContract.dueAmount!);
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount less than due amount and total outstanding debt - Continue button disabled',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);

        // navigate to CEL amount selection
        testerHelper.logger.i(
          'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt less than default threshold amount (50.000)',
        );
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Select custom amount option
        await selectAmountPage.tapCustomAmountOption();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Enter custom amount less than due amount and total outstanding debt
        await selectAmountPage.enterCustomAmount('10000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check continue button is disabled
        selectAmountPage.isContinueButtonDisable();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount greater than total outstanding debt but less than default threshold amount (50.000)',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);
        // navigate to CEL amount selection
        testerHelper.logger.i(
          'Navigate to the repayment CEL amount selection screen with contract has due amount and total outstanding debt less than default threshold amount (50.000)',
        );
        await testerHelper.navigateToRepaymentCelAmountSelection(
          repaymentLoanContract: repaymentLoanContract,
        );
        await testerHelper.delay(const Duration(microseconds: 500));

        // Check screen is ready
        selectAmountPage
          ..verifyReady()

          // Check components existed
          ..isTitleExist()
          ..isDueAmountOptionExist()
          ..isCustomAmountOptionExist()
          ..isContinueButtonExist();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Select custom amount option
        await selectAmountPage.tapCustomAmountOption();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Enter custom amount greater total outstanding debt but less than default threshold amount (50.000)
        await selectAmountPage.enterCustomAmount('49000');
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click continue button
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountPage.isAbsent();

        await testerHelper.delay(const Duration(microseconds: 500));
        // Check correct payment amount
        onlinePaymentPage.isCorrectPaymentAmount(repaymentLoanContract.totalOutstandingDebt!);
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });
}
