import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment/capp_repayment.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../../integration_tests_tools/tester_helper.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_new/repayment_banking_app_page_vn.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_new/repayment_contract_list_page_vn.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_new/repayment_main_page_vn.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_new/repayment_payment_method_page_vn.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_payment_summary_page_vn.dart';
import '../common/repayment_new_common_steps.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Main screen', () {
    final routeArgsForRepayNowFlow = RepaymentMainRouteArgs(
      viewType: RepaymentViewType.methodSelection,
      selectedContract: RepaymentContract(
        contractType: RepaymentContractType.cel,
        loanContract: RepaymentLoanContract(
          contractNumber: '**********',
          contractType: 'mobile',
          loanType: 'mobile',
          contractStatus: 'active',
          productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
          productName: 'Cash Loan',
          dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
          dueAmount: Decimal.parse('69000'),
          totalOutstandingDebt: Decimal.parse('100000'),
        ),
      ),
    );

    final routeArgsForRepaymentFlow = RepaymentMainRouteArgs(
      viewType: RepaymentViewType.contractList,
      selectedContract: RepaymentContract(
        contractType: RepaymentContractType.rel,
        relContract: RepaymentRelContract(
          accountNumber: '**********',
          accountStatus: 'PENDING_CLOSE',
          contractStatus: 'active',
          contractType: 'REL',
          minimumDueAmount: Decimal.parse('50.00'),
          totalAmountDue: Decimal.zero,
          dueDate: DateTime.parse('2021-01-11T06:53:26.757Z'),
          outstandingBalance: Decimal.zero,
        ),
      ),
    );

    integrationTest(
      'Main screen initialization, select contract from repay now, select main payment method and select bank',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final bankingAppPage = RepaymentBankingAppTestPageVn(tester);
        final paymentMethodPage = RepaymentPaymentMethodTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentMethodPopup__')),
        );
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        testerHelper
          // Turn off direct discount
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentDirectDiscountMainScreen, isEnabled: false)
          // Turn on direct discount for EL contract
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentAllowDirectDiscountEL, isEnabled: true);
        // Navigate to main screen
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayNowFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Check contract main exists
        mainPage
          ..checkContractMainExist()

          // Check payment method main exists
          ..checkPaymentMethodMainExist()

          // Check direct discount section is not existed
          ..checkDirectDiscountSectionIsExist(isExisted: false)
          // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false);

        await mainPage.tapPaymentMethodMain();

        paymentMethodPage.isReady();

        await paymentMethodPage.scrollToPaymentMethod(RepaymentPaymentMethod.mobileBanking);

        // Tap banking mobile
        await paymentMethodPage.tapPaymentMethod(RepaymentPaymentMethod.mobileBanking);

        paymentMethodPage.isAbsent();
        bankingAppPage.isReady();

        // Tap bank
        await bankingAppPage.tapBank(bank: RepaymentBankingAppBloc.getBaoKiemBanks().elementAt(0));

        mainPage.isReady();
        bankingAppPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Main screen initialization and auto open contract list popup from repayment flow',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final paymentMethodPage = RepaymentPaymentMethodTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentMethodPopup__')),
        );
        final paymentSummaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        testerHelper
          // Turn off direct discount
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentDirectDiscountMainScreen, isEnabled: false)
          // Turn on direct discount for EL contract
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentAllowDirectDiscountEL, isEnabled: true);

        // Navigate to main screen
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepaymentFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Check contract list popup is displayed immediately
        contractListPage.isReady();

        await contractListPage.tapFirstContract();

        // Check contract main exists
        mainPage
          ..checkContractMainExist()

          // Check payment method main exists
          ..checkPaymentMethodMainExist()

          // Check direct discount section is not existed
          ..checkDirectDiscountSectionIsExist(isExisted: false)
          // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false);

        await testerHelper.delay(const Duration(seconds: 1));
        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        await mainPage.enterCustomAmountInputTextMain('120000');

        // ==== Start checking payment summary amount when direct discount is disable
        // Select payment method
        await mainPage.tapPaymentMethodMain();
        paymentMethodPage.isReady();
        // Tap Viettel Money method
        await paymentMethodPage.tapPaymentMethod(RepaymentPaymentMethod.viettel);
        // Tap continue button
        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(seconds: 2));
        // Check voucher information UI is existed
        paymentSummaryPage
          // Check components existed
          ..isTotalAmountInfoExist()
          ..isDiscountAmountInfoExist(isExisted: false)
          ..isFinalAmountInfoExist(isExisted: false)

          // Verify displayed amount are the same with selected amount
          ..isCorrectTotalAmount(Decimal.parse('120000'));
        await testerHelper.delay(const Duration(microseconds: 500));
        // ==== End checking payment summary amount when direct discount is disable
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    final routeArgsForNoCuidUser = RepaymentMainRouteArgs(
      viewType: RepaymentViewType.methodSelection,
    );

    integrationTest(
      'Main screen initialization and show no loan screen for no CUID users',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '0937227632', password: '0');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForNoCuidUser);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // No loan screen is displayed
        mainPage.checkNoLoanScreenIsDisplay();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Main screen - Test enable direct discount feature flag',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final paymentMethodPage = RepaymentPaymentMethodTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentMethodPopup__')),
        );
        final paymentSummaryPage = RepaymentPaymentSummaryTestPageVn(tester);

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        testerHelper
          // Turn on direct discount
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentDirectDiscountMainScreen, isEnabled: true)
          // Turn on direct discount for EL contract
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentAllowDirectDiscountEL, isEnabled: true);

        // Navigate to main screen
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepaymentFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Check contract list popup is displayed immediately
        contractListPage.isReady();

        await contractListPage.tapFirstContract();
        await testerHelper.delay(const Duration(microseconds: 4000));
        // Check contract main exists
        mainPage
          ..checkContractMainExist()

          // Check payment method main exists
          ..checkPaymentMethodMainExist()

          // Check direct discount section is existed
          ..checkDirectDiscountSectionIsExist(isExisted: true)
          ..checkVoucherEmptyIsExist(isExisted: true)
          ..checkVoucherItemIsExist(isExisted: false)
          // Check direct discount action button text
          ..checkDirectDiscountActionButtonText('Chọn')
          // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false);

        await mainPage.tapVoucherActionButton();
        await testerHelper.delay(const Duration(microseconds: 4000));

        // Select inapplicable voucher (min amount greater than 500.000)
        await mainPage.tapFirstVoucher();
        await testerHelper.delay(const Duration(microseconds: 500));
        await mainPage.tapApplyVoucher();
        await testerHelper.delay(const Duration(milliseconds: 2000));

        // Check voucher selected is shown
        mainPage
          // Check direct discount section is existed
          ..checkDirectDiscountSectionIsExist(isExisted: true)
          ..checkVoucherEmptyIsExist(isExisted: false)
          ..checkVoucherItemIsExist(isExisted: true)
          // Check direct discount action button text
          ..checkDirectDiscountActionButtonText('Thay đổi')
          // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false);

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        await mainPage.enterCustomAmountInputTextMain('120000');
        await testerHelper.delay(const Duration(milliseconds: 100));
        await mainPage.dragToBottom();
        await testerHelper.delay(const Duration(seconds: 2));

        // Check voucher is become applicable because selected amount greater than voucher min amount
        mainPage
          // Check direct discount section is existed
          ..checkDirectDiscountSectionIsExist(isExisted: true)
          ..checkVoucherEmptyIsExist(isExisted: false)
          ..checkVoucherItemIsExist(isExisted: true)
          // Check direct discount action button text
          ..checkDirectDiscountActionButtonText('Thay đổi')
          // Check after discount text is existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: true);

        await testerHelper.delay(const Duration(seconds: 1));

        // Test changing contract will reset voucher
        await mainPage.dragToContractSection();
        await testerHelper.delay(const Duration(milliseconds: 500));
        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(seconds: 1));
        await contractListPage.scrollToSixthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));
        await contractListPage.tapSixthContract();
        await testerHelper.delay(const Duration(seconds: 1));

        mainPage
          // Check direct discount section is existed
          ..checkVoucherEmptyIsExist(isExisted: true)
          ..checkVoucherItemIsExist(isExisted: false)
          // Check direct discount action button text
          ..checkDirectDiscountActionButtonText('Chọn')
          // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false);

        // Re-select voucher
        // Test changing contract will reset voucher
        await mainPage.dragToContractSection();
        await testerHelper.delay(const Duration(milliseconds: 500));
        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(seconds: 2));
        await contractListPage.tapFirstContract();
        await testerHelper.delay(const Duration(microseconds: 4000));
        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        await mainPage.enterCustomAmountInputTextMain('120000');
        await testerHelper.delay(const Duration(milliseconds: 1000));
        await mainPage.tapVoucherActionButton();
        await testerHelper.delay(const Duration(microseconds: 4000));
        // Select applicable voucher (because selected amount is greater than min required amount)
        await mainPage.tapFirstVoucher();
        await testerHelper.delay(const Duration(microseconds: 500));
        await mainPage.tapApplyVoucher();
        await testerHelper.delay(const Duration(microseconds: 2000));
        await mainPage.dragToBottom();
        await testerHelper.delay(const Duration(seconds: 1));

        // Check voucher is shown
        mainPage
          // Check direct discount section is existed
          ..checkDirectDiscountSectionIsExist(isExisted: true)
          ..checkVoucherEmptyIsExist(isExisted: false)
          ..checkVoucherItemIsExist(isExisted: true)
          // Check direct discount action button text
          ..checkDirectDiscountActionButtonText('Thay đổi')
          // Check after discount text is existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: true);

        await testerHelper.delay(const Duration(seconds: 1));

        // ==== Start checking payment summary amount when selected applicable voucher
        // Select payment method
        await mainPage.tapPaymentMethodMain();
        paymentMethodPage.isReady();
        // Tap Viettel Money method
        await paymentMethodPage.tapPaymentMethod(RepaymentPaymentMethod.viettel);
        // Tap continue button
        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(seconds: 2));
        // Check voucher information UI is existed
        paymentSummaryPage
          ..verifyReady()
          // Check components existed
          ..isTotalAmountInfoExist()
          ..isDiscountAmountInfoExist(isExisted: true)
          ..isFinalAmountInfoExist(isExisted: true)

          // Fake api always return voucher validation data: discount 10k, original 100k, final amount 90k
          // Verify displayed amount are the same with voucher validation response data
          ..isCorrectTotalAmount(Decimal.parse('100000'))
          ..isCorrectDiscountAmount(Decimal.parse('10000'))
          ..isCorrectFinalAmount(Decimal.parse('90000'));

        await paymentSummaryPage.appBarBackButton.tap();
        await testerHelper.delay(const Duration(microseconds: 500));
        // ==== End checking payment summary amount when selected applicable voucher

        // Remove voucher
        await mainPage.tapRemoveVoucher();
        await testerHelper.delay(const Duration(milliseconds: 500));

        mainPage
          // Check direct discount section is existed
          ..checkVoucherEmptyIsExist(isExisted: true)
          ..checkVoucherItemIsExist(isExisted: false)
          // Check direct discount action button text
          ..checkDirectDiscountActionButtonText('Chọn')
          // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false);

        // ==== Start checking payment summary amount when not select voucher
        // Tap continue button
        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(seconds: 2));
        // Check voucher information UI is existed
        paymentSummaryPage
          ..verifyReady()
          // Check components existed
          ..isTotalAmountInfoExist()
          ..isDiscountAmountInfoExist(isExisted: true)
          ..isFinalAmountInfoExist(isExisted: true)

          // Fake api always return voucher validation data: discount 10k, original 100k, final amount 90k
          // Verify displayed amount are the same with selected amount
          ..isCorrectTotalAmount(Decimal.parse('120000'))
          ..isCorrectDiscountAmount(Decimal.parse('0'))
          ..isCorrectFinalAmount(Decimal.parse('120000'));
        // ==== End checking payment summary amount when not select voucher
        await testerHelper.delay(const Duration(seconds: 2));
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 180)),
    );

    integrationTest(
      'Main screen - Test applied payment methods',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final paymentMethodPage = RepaymentPaymentMethodTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__paymentMethodPopup__')),
        );

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Turn on direct discount
        testerHelper.toggleFeatureFlag(featureName: FeatureFlag.repaymentDirectDiscountMainScreen, isEnabled: true);
        // Navigate to main screen
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepaymentFlow);
        await testerHelper.delay(const Duration(seconds: 1));

        await contractListPage.tapSixthContract();
        await testerHelper.delay(const Duration(seconds: 2));

        await mainPage.tapVoucherActionButton();
        await testerHelper.delay(const Duration(seconds: 2));

        // Select voucher does not have applied payment method bank transfer QR
        await mainPage.tapFourthVoucher();
        await testerHelper.delay(const Duration(microseconds: 500));
        await mainPage.tapApplyVoucher();
        await testerHelper.delay(const Duration(milliseconds: 2000));

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));
        await mainPage.enterCustomAmountInputTextMain('200000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkMainVoucherEnable(
          isEnable: true,
        );

        // Select payment method
        await mainPage.dragToBottom();
        await mainPage.tapPaymentMethodMain();
        paymentMethodPage.isReady();
        // Tap Bank transfer QR method
        await paymentMethodPage.tapPaymentMethod(RepaymentPaymentMethod.bankTransferQr);

        // Voucher does not accept bank transfer QR payment method now is disable
        mainPage.checkMainVoucherEnable(
          isEnable: false,
        );
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 180)),
    );

    integrationTest(
      'Main screen show sappi alert - show sappi',
          (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        testerHelper
        // Turn off direct discount
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentDirectDiscountMainScreen, isEnabled: false)
        // Turn on direct discount for EL contract
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentAllowDirectDiscountEL, isEnabled: false)
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentContractSourceGetFromPcs, isEnabled: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentSappiAlertBar, isEnabled: true);
        // Navigate to main screen
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepaymentFlow);

        // Check contract main exists
        mainPage
        // Check direct discount section is not existed
          ..checkDirectDiscountSectionIsExist(isExisted: false)
        // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false)
          ..checkSappiAlertExist();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Main screen show sappi alert - hide sappi when repaymentSappiAlertBar FF is off',
          (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        testerHelper
        // Turn off direct discount
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentDirectDiscountMainScreen, isEnabled: false)
        // Turn on direct discount for EL contract
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentAllowDirectDiscountEL, isEnabled: false)
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentContractSourceGetFromPcs, isEnabled: true)
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentSappiAlertBar, isEnabled: false);
        // Navigate to main screen
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepaymentFlow);

        // Check contract main exists
        mainPage
        // Check direct discount section is not existed
          ..checkDirectDiscountSectionIsExist(isExisted: false)
        // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false)
          ..checkSappiAlertNotExist();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Main screen show sappi alert - hide sappi when repaymentContractSourceGetFromPcs FF is off',
          (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        testerHelper
        // Turn off direct discount
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentDirectDiscountMainScreen, isEnabled: false)
        // Turn on direct discount for EL contract
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentAllowDirectDiscountEL, isEnabled: false)
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentContractSourceGetFromPcs, isEnabled: false)
          ..toggleFeatureFlag(featureName: FeatureFlag.repaymentSappiAlertBar, isEnabled: true);
        // Navigate to main screen
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepaymentFlow);

        // Check contract main exists
        mainPage
        // Check direct discount section is not existed
          ..checkDirectDiscountSectionIsExist(isExisted: false)
        // Check after discount text is not existed
          ..checkAfterDiscountAmountTextIsExist(isExisted: false)
          ..checkSappiAlertNotExist();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

  });
}
