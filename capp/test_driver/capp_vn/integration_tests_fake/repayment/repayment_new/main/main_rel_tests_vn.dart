import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../../integration_tests_tools/tester_helper.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_new/repayment_contract_list_page_vn.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_new/repayment_main_page_vn.dart';
import '../../../../integration_page_objects_vn/repayment/repayment_payment_summary_page_vn.dart';
import '../common/repayment_new_common_steps.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group(
      'Main screen - Rel - No BSNP - Contract has due amount and total outstanding debt greater than default threshold amount (50.000)',
      () {
    final routeArgsForRepayRelFlow = RepaymentMainRouteArgs(
      viewType: RepaymentViewType.methodSelection,
      selectedContract: RepaymentContract(
        contractType: RepaymentContractType.rel,
        relContract: RepaymentRelContract(
          accountNumber: '**********',
          accountStatus: 'PENDING_CLOSE',
          contractStatus: 'active',
          contractType: 'REL',
          currency: 'VND',
          minimumDueAmount: Decimal.parse('55000'),
          totalAmountDue: Decimal.parse('70000'),
          dueDate: DateTime.parse('2022-03-07T11:12:13'),
        ),
      ),
    );

    integrationTest(
      'Auto select min amount option when selected contract',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));
        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToSixthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapSixthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkMinRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist()
          ..checkMinRelAmountOptionsSelected()
          ..checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount options and input amount less than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToSixthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapSixthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkMinRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));
        await mainPage.enterCustomAmountInputTextMain('10000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonDisable();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
    integrationTest(
      'Select custom amount options and input amount greater than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToSixthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapSixthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkMinRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        await mainPage.enterCustomAmountInputTextMain('80000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });

  group(
      'Main screen - Rel - No BSNP - Contract has due amount and total outstanding debt less than default threshold amount (50.000)',
      () {
    final routeArgsForRepayRelFlow = RepaymentMainRouteArgs(
      viewType: RepaymentViewType.methodSelection,
      selectedContract: RepaymentContract(
        contractType: RepaymentContractType.rel,
        relContract: RepaymentRelContract(
          accountNumber: '**********',
          accountStatus: 'PENDING_CLOSE',
          contractStatus: 'active',
          contractType: 'REL',
          currency: 'VND',
          minimumDueAmount: Decimal.parse('20000'),
          totalAmountDue: Decimal.parse('25000'),
          dueDate: DateTime.parse('2022-03-07T11:12:43'),
        ),
      ),
    );

    integrationTest(
      'Auto select total amount option when selected contract',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToSeventhContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapSeventhContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkMinRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
    integrationTest(
      'Auto select min due amount option and show less than total due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToSeventhContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapSeventhContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkMinRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkContinueButtonEnable();

        await mainPage.tapMinRelAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));
        mainPage.checkRelLessThanMinThresholdPopupMainExist();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
    integrationTest(
      'Select custom amount options and input amount less than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToSeventhContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapSeventhContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkMinRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));
        await mainPage.enterCustomAmountInputTextMain('10000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonDisable();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
    integrationTest(
      'Select custom amount options and input amount greater than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToSeventhContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapSeventhContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkMinRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        await mainPage.enterCustomAmountInputTextMain('30000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });

  group(
      'Main screen - Rel - BSNP - Contract has due amount and total outstanding debt greater than default threshold amount (50.000)',
      () {
    final routeArgsForRepayRelFlow = RepaymentMainRouteArgs(
      viewType: RepaymentViewType.methodSelection,
      selectedContract: RepaymentContract(
        contractType: RepaymentContractType.rel,
        relContract: RepaymentRelContract(
          accountNumber: '**********',
          accountStatus: 'PENDING_CLOSE',
          contractStatus: 'active',
          contractType: 'REL',
          currency: 'VND',
          gmaProductType: 'BNPL',
          minimumDueAmount: Decimal.parse('60000'),
          totalAmountDue: Decimal.parse('80000'),
          dueDate: DateTime.parse('2022-03-07T11:12:43'),
        ),
      ),
    );

    integrationTest(
      'Auto select total amount option when selected contract',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToEighthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapEighthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount options and input amount less than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToEighthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapEighthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));
        await mainPage.enterCustomAmountInputTextMain('10000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonDisable();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
    integrationTest(
      'Select custom amount options and input amount greater than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToEighthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapEighthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        await mainPage.enterCustomAmountInputTextMain('90000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });
  group(
      'Main screen - Rel - BSNP - Contract has due amount and total outstanding debt less than default threshold amount (50.000)',
      () {
    final routeArgsForRepayRelFlow = RepaymentMainRouteArgs(
      viewType: RepaymentViewType.methodSelection,
      selectedContract: RepaymentContract(
        contractType: RepaymentContractType.rel,
        relContract: RepaymentRelContract(
          accountNumber: '**********',
          accountStatus: 'PENDING_CLOSE',
          contractStatus: 'active',
          contractType: 'REL',
          currency: 'VND',
          gmaProductType: 'BNPL',
          minimumDueAmount: Decimal.parse('20000'),
          totalAmountDue: Decimal.parse('30000'),
          dueDate: DateTime.parse('2023-06-09T11:11:11'),
        ),
      ),
    );

    integrationTest(
      'Auto select total amount option when selected contract',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToTwelfthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapTwelfthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkCustomAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );

    integrationTest(
      'Select custom amount options and input amount less than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );

        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToTwelfthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapTwelfthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));
        await mainPage.enterCustomAmountInputTextMain('10000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonDisable();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
    integrationTest(
      'Select custom amount options and input amount greater than due amount',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = RepaymentMainTestPageVn(tester);
        final contractListPage = RepaymentContractListTestPageVn(
          tester: tester,
          screenFinder: find.byKey(const Key('__contractListPopup__')),
        );
        final summaryPage = RepaymentPaymentSummaryTestPageVn(tester);
        // Navigate to home screen
        await testStepsToLoginAsClientAndNavigateToHomeScreen(tester: tester, username: '**********', password: '123');

        // Navigate to main scren
        await testerHelper.navigateToRepaymentMain(routeArgs: routeArgsForRepayRelFlow);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Choose a method payment
        await chooseAPaymentMethodMain(tester, mainPage);
        await testerHelper.delay(const Duration(microseconds: 1000));

        // Tap contract main
        await mainPage.tapContractMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.checkContractListExist();

        await contractListPage.scrollToTwelfthContract();

        await testerHelper.delay(const Duration(microseconds: 1000));

        await contractListPage.tapTwelfthContract();
        await testerHelper.delay(const Duration(microseconds: 1000));

        contractListPage.isAbsent();

        mainPage
          ..isReady()
          ..checkTotalRelAmountOptionsMainExist()
          ..checkTotalRelAmountOptionsSelected()
          ..checkCustomAmountOptionsMainExist();

        await mainPage.tapCustomAmountOptionsMain();
        await testerHelper.delay(const Duration(microseconds: 1000));

        await mainPage.enterCustomAmountInputTextMain('40000');
        await testerHelper.delay(const Duration(microseconds: 1000));

        mainPage.checkContinueButtonEnable();

        await mainPage.tapContinue();
        await testerHelper.delay(const Duration(microseconds: 500));

        summaryPage.isReady();
        mainPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });
}
