import 'package:capp_repayment/capp_repayment.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects_vn/repayment/repayment_method_selection_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/repayment_online_payment_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/repayment_payment_summary_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/repayment_select_amount_cel_page_vn.dart';
import '../../../integration_tests_tools/tester_helper_vn.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Method selection screen', () {
    integrationTest(
      'Method selection initialization and select payment method',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final methodSelectionPage = RepaymentMethodSelectionTestPageVn(tester);
        final selectAmountPage = RepaymentSelectAmountCelTestPageVn(tester);
        final selectAmountCelPage = RepaymentSelectAmountCelTestPageVn(tester);
        final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);
        final paymentSummaryPage = RepaymentPaymentSummaryTestPageVn(tester);

        // navigate to method selection screen
        await testerHelper.navigateToRepaymentMethodSelection();

        // Check screen is ready
        methodSelectionPage
          ..isReady()

          // Check list container exist
          ..checkListContainerExist();

        // Click EWallet continue button
        await methodSelectionPage.tapContinueButton(RepaymentMethodType.eWallet);

        selectAmountPage.isReady();
        methodSelectionPage.isAbsent();

        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(milliseconds: 500));

        onlinePaymentPage.isReady();
        selectAmountCelPage.isAbsent();

        await onlinePaymentPage.appBarBackButton.tap();
        await testerHelper.delay(const Duration(microseconds: 500));

        await selectAmountPage.appBarBackButton.tap();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click Internet Banking continue button
        await methodSelectionPage.tapContinueButton(RepaymentMethodType.internetBanking);

        selectAmountPage.isReady();
        methodSelectionPage.isAbsent();
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(milliseconds: 500));

        paymentSummaryPage.isReady();
        selectAmountCelPage.isAbsent();

        await paymentSummaryPage.appBarBackButton.tap();
        await testerHelper.delay(const Duration(microseconds: 500));

        await selectAmountPage.appBarBackButton.tap();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click ATM Card continue button
        await methodSelectionPage.tapContinueButton(RepaymentMethodType.atmCard);

        selectAmountPage.isReady();
        methodSelectionPage.isAbsent();
        await selectAmountPage.tapContinue();
        await testerHelper.delay(const Duration(milliseconds: 500));

        paymentSummaryPage.isReady();
        selectAmountCelPage.isAbsent();

        await paymentSummaryPage.appBarBackButton.tap();
        await testerHelper.delay(const Duration(microseconds: 500));

        await selectAmountPage.appBarBackButton.tap();
        await testerHelper.delay(const Duration(microseconds: 500));

        // Click bank transfer continue button
        await methodSelectionPage.tapContinueButton(RepaymentMethodType.bankTransfer);
        await testerHelper.delay(const Duration(milliseconds: 500));
        selectAmountCelPage.isReady();
        methodSelectionPage.isAbsent();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });
}
