import 'package:capp_repayment/capp_repayment.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects_vn/repayment/promise_to_pay/repayment_ptp_payment_plan_page_vn.dart';
import '../../../integration_page_objects_vn/repayment/promise_to_pay/repayment_ptp_payment_processing_page_vn.dart';
import '../../../integration_tests_tools/tester_helper_vn.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('PTP Payment plan', () {
    final ptpContract = RepaymentPtpContract(
      entityType: 'CONTRACT',
      entityId: '2100167289',
      hasActivePTP: false,
      maximalLength: 4,
      minimumAmount: Decimal.tryParse('52000'),
      minimumRate: 5,
      ptpEligible: true,
      totalAmount: Decimal.tryParse('60000'),
      lastPTP: RepaymentPtpEvaluation(
        entityId: '6659877',
        status: 'active',
        promiseAmount: Decimal.tryParse('50000'),
      ),
    );
    final routeArgs = RepaymentPtpPaymentPlanRouteArgs(ptpContract: ptpContract);
    integrationTest(
      'Payment method initialization and input',
      (tester, context) async {
        final testerHelper = TesterHelperVn(tester: tester, context: context);
        final paymentPlanPage = RepaymentPtpPaymentPlanTestPageVn(tester);
        final processingPage = RepaymentPtpPaymentProcessingTestPageVn(tester);
        // Navigate to PTP payment plan
        await testerHelper.navigateToPtpPaymentPlan(routeArgs: routeArgs);
        await testerHelper.delay(const Duration(microseconds: 1000));

        paymentPlanPage
          ..checkPaymentPlanContentExists()
          ..checkPaymentPlanChoiceChipsExists()
          ..checkNumerOfChoiceChips(ptpContract.maximalLength!)
          ..checkPaymentPlanDueDateExists()
          ..checkPaymentPlanExtendUtilDateExists()
          ..checkNumerOfChoiceChips(ptpContract.maximalLength!);

        // Tap a choice chip
        await paymentPlanPage.tapSecondChoiceChip();
        await testerHelper.delay(const Duration(microseconds: 1000));

        paymentPlanPage
          ..checkExtendUtilDateIsOk()

          // Check button send still not enable
          ..checkSendButtonEnable(isEnable: false);

        // Tap due amount
        await paymentPlanPage.tapDueAmountOption();
        paymentPlanPage.checkSendButtonEnable(isEnable: true);

        // Tap custom amount and enter amount
        await paymentPlanPage.tapCustomAmountOption();
        paymentPlanPage.checkSendButtonEnable(isEnable: false);
        await paymentPlanPage.enterTextForCustomAmount('50000');
        await testerHelper.delay(const Duration(microseconds: 1000));
        paymentPlanPage.checkSendButtonEnable(isEnable: false);

        await paymentPlanPage.enterTextForCustomAmount('53000');
        await testerHelper.delay(const Duration(microseconds: 1000));
        paymentPlanPage.checkSendButtonEnable(isEnable: true);

        // Tap submit
        await paymentPlanPage.tapSubmit();
        paymentPlanPage.checkPaymentPlanExtendPopupExists();
        // await paymentPlanPage.tapExtendPopupAgreeButton();
        // await testerHelper.delay(const Duration(microseconds: 2000));
        // paymentPlanPage.isAbsent();
        // processingPage.isReady();
      },
      appRunner: appRunner,
      timeout: const Timeout(Duration(seconds: 90)),
    );
  });
}
