import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects_vn/repayment/repayment_online_payment_page_vn.dart';
import '../../../integration_tests_tools/tester_helper_vn.dart';

Future<void> testFakeOnlinePaymentInitialization({
  required WidgetTester tester,
  required TestContext context,
}) async {
  final testerHelper = TesterHelperVn(tester: tester, context: context);
  final onlinePaymentPage = RepaymentOnlinePaymentTestPageVn(tester);

  // navigate to online payment option screen
  await testerHelper.navigateToRepaymentOnlinePayment();

  // Check payment content exists
  onlinePaymentPage
    ..checkPaymentContentExists()

    // Check online payment list exists
    ..checkOnlinePaymentListExists();
}
