import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../../integration_page_objects/index.dart';
import '../../../../integration_reuse_steps/ulo.dart';
import '../../../integration_page_objects_vn/loan_shared/id_card_detail_page.dart';
import '../../../integration_tests_tools/integration_tests_vn.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Id card verification', () {
    integrationTestFakeVn('Happy path', (tester, context) async {
      final idCardDetailsPage = IdCardDetailsInnovatrics(tester);
      final loanPage = LoanOriginationUnifiedCorePage(tester);

      await startFakeFlowPage('__idCardAppFormEditButton__', context, tester);
      idCardDetailsPage.isReady();

      await idCardDetailsPage.enterIdNumber('022233300100');
      expect(find.text('022233300100'), findsOneWidget);

      await idCardDetailsPage.enterIssueDate('01/01/2018');
      expect(find.text('01/01/2018'), findsOneWidget);

      await idCardDetailsPage.enterFullName('ilham AMRIT ACLAD test');
      expect(find.text('ilham AMRIT ACLAD test'), findsOneWidget);

      await idCardDetailsPage.enterBirthDate('01/01/2000');
      expect(find.text('01/01/2000'), findsOneWidget);

      await idCardDetailsPage.selectGender(isMale: false);

      await idCardDetailsPage.enterExpiryDate('01/01/2030');
      expect(find.text('01/01/2030'), findsOneWidget);

      await idCardDetailsPage.enterRegion(region: 'FAKE_VALUE1');
      expect(find.text('FAKE_VALUE1'), findsOneWidget);

      await idCardDetailsPage.enterDistrict(district: 'FAKE_VALUE2');
      expect(find.text('FAKE_VALUE2'), findsOneWidget);

      await idCardDetailsPage.enterWard(ward: 'FAKE_VALUE3');
      expect(find.text('FAKE_VALUE3'), findsOneWidget);

      await idCardDetailsPage.enterHouseNumber(text: '112233');
      expect(find.text('112233'), findsOneWidget);

      await idCardDetailsPage.enterStreetName(text: 'Automation');
      expect(find.text('Automation'), findsOneWidget);

      await idCardDetailsPage.selectIsSameAddress(isSame: false);

      await idCardDetailsPage.enterRegion(region: 'FAKE_VALUE3', isFirstAddress: false);
      expect(find.text('FAKE_VALUE3'), findsNWidgets(2));

      await idCardDetailsPage.enterDistrict(district: 'FAKE_VALUE1', isFirstAddress: false);
      expect(find.text('FAKE_VALUE1'), findsNWidgets(2));

      await idCardDetailsPage.enterWard(ward: 'FAKE_VALUE2', isFirstAddress: false);
      expect(find.text('FAKE_VALUE2'), findsNWidgets(2));

      await idCardDetailsPage.enterHouseNumber(text: 'B2', isFirstAddress: false);
      expect(find.text('B2'), findsOneWidget);

      await idCardDetailsPage.enterStreetName(text: 'Kamunanyea', isFirstAddress: false);
      expect(find.text('Kamunanyea'), findsOneWidget);

      await idCardDetailsPage.tapOnContinue();
      await tester.pumpAndSettle();
      idCardDetailsPage.isAbsent();
      loanPage.isReady();

      expect(find.text('Residential Address'), findsWidgets);
      expect(find.text('Correspondence Address'), findsWidgets);
      expect(find.text('112233 Automation, FAKE_VALUE3, FAKE_VALUE2, FAKE_VALUE1'), findsOneWidget);
      expect(find.text('B2 Kamunanyea, FAKE_VALUE2, FAKE_VALUE1, FAKE_VALUE3'), findsOneWidget);
      expect(find.text('*********100'), findsOneWidget);
    });
  });

  group('Id card details negative', () {
    integrationTestFakeVn('Mandatory validation', (tester, context) async {
      final onboardPage = OnboardingChooseLanguagePage(tester);
      final idCardDetailsPage = IdCardDetailsLoanSharedPage(tester);

      await onboardPage.chooseEnglish();
      await startFakeFlowPage('__idCardAppFormEditButton__', context, tester);
      idCardDetailsPage.isReady();

      await idCardDetailsPage.enterIssueDate('01/01/2018');
      expect(find.text('01/01/2018'), findsOneWidget);

      await idCardDetailsPage.enterBirthDate('01/01/2000');
      expect(find.text('01/01/2000'), findsOneWidget);

      await idCardDetailsPage.enterExpireDate('01/01/2030');
      expect(find.text('01/01/2030'), findsOneWidget);

      await idCardDetailsPage.enterRaRegion('FAKE_VALUE1');
      expect(find.text('FAKE_VALUE1'), findsOneWidget);

      await idCardDetailsPage.enterRaDistrict('FAKE_VALUE2');
      expect(find.text('FAKE_VALUE2'), findsOneWidget);

      await idCardDetailsPage.enterRaWard('FAKE_VALUE3');
      expect(find.text('FAKE_VALUE3'), findsOneWidget);

      await idCardDetailsPage.enterRaHouseNumber('');
      await idCardDetailsPage.enterRaStreetName('');
      await idCardDetailsPage.enterFullName('');
      await idCardDetailsPage.enterIdNumber('');
      await idCardDetailsPage.selectIsNotCorrespondent();
      await idCardDetailsPage.tapOnContinue();
      expect(find.text('Required'), findsNWidgets(8));
      expect(find.text('Must contain your full name shown on the ID Card'), findsOneWidget);
    });

    integrationTestFakeVn('Max input validation', (tester, context) async {
      final onboardPage = OnboardingChooseLanguagePage(tester);
      final idCardDetailsPage = IdCardDetailsLoanSharedPage(tester);

      await onboardPage.chooseEnglish();
      await startFakeFlowPage('__idCardAppFormEditButton__', context, tester);
      idCardDetailsPage.isReady();

      await idCardDetailsPage.enterIdNumber('022233300100');
      expect(find.text('022233300100'), findsOneWidget);

      await idCardDetailsPage.enterIssueDate('01/01/2018');
      expect(find.text('01/01/2018'), findsOneWidget);

      await idCardDetailsPage.enterFullName('ilham AMRIT ACLAD test');
      expect(find.text('ilham AMRIT ACLAD test'), findsOneWidget);

      await idCardDetailsPage.enterBirthDate('01/01/2000');
      expect(find.text('01/01/2000'), findsOneWidget);

      await idCardDetailsPage.enterExpireDate('01/01/2030');
      expect(find.text('01/01/2030'), findsOneWidget);

      await idCardDetailsPage.enterRaRegion('FAKE_VALUE1');
      expect(find.text('FAKE_VALUE1'), findsOneWidget);

      await idCardDetailsPage.enterRaDistrict('FAKE_VALUE2');
      expect(find.text('FAKE_VALUE2'), findsOneWidget);

      await idCardDetailsPage.enterRaWard('FAKE_VALUE3');
      expect(find.text('FAKE_VALUE3'), findsOneWidget);

      await idCardDetailsPage.enterRaHouseNumber('123456789012345678901');
      expect(find.text('123456789012345678901'), findsOneWidget);

      await idCardDetailsPage.enterRaStreetName('12345678901234567890123456789012345678901');
      expect(find.text('12345678901234567890123456789012345678901'), findsOneWidget);

      await idCardDetailsPage.selectIsNotCorrespondent();

      await idCardDetailsPage.enterCaRegion('FAKE_VALUE1');
      expect(find.text('FAKE_VALUE1'), findsNWidgets(2));

      await idCardDetailsPage.enterCaDistrict('FAKE_VALUE2');
      expect(find.text('FAKE_VALUE2'), findsNWidgets(2));

      await idCardDetailsPage.enterCaWard('FAKE_VALUE3');
      expect(find.text('FAKE_VALUE3'), findsNWidgets(2));

      await idCardDetailsPage.enterCaHouseNumber('123456789a123456789b1');
      expect(find.text('123456789a123456789b1'), findsOneWidget);

      await idCardDetailsPage.enterCaStreetName('123456789q123456789w123456789e123456789r1');
      expect(find.text('123456789q123456789w123456789e123456789r1'), findsOneWidget);

      await idCardDetailsPage.tapOnContinue();
      expect(find.text('Must not exceed than 20 characters'), findsNWidgets(2));
      expect(find.text('Must not exceed than 40 characters'), findsNWidgets(2));
    });
  });
}
