import 'package:integration_test/integration_test.dart';

import 'card_delivery/card_delivery_tests.dart' as delivery_tests;
import 'card_limitation/card_kyc_intro_tests.dart' as kyc_intro_test;
import 'cards_management/card_activation_tests.dart' as card_activation_tests;
import 'cards_management/credit_card_list_tests.dart' as card_management_test;
import 'cards_management/ecommerce_tests.dart' as ecommerce_tests;
import 'cards_management/vcc_physical_issuance_tests.dart' as vcc_physical_issuance_tests;
import 'loyalty/loyalty_tests.dart' as loyalty_tests;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  card_management_test.main();
  card_activation_tests.main();
  ecommerce_tests.main();
  vcc_physical_issuance_tests.main();
  loyalty_tests.main();
  delivery_tests.main();
  //TimeoutException, back to check later
  // pdf_viewer_tests.main();

  kyc_intro_test.main();
}
