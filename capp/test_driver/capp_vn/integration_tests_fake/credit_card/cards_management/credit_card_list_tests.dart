import 'package:capp_domain/capp_domain.dart' as domain;
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../integration_page_objects_vn/credit_card/cards_management/credit_card_list_page.dart';
import '../../../integration_tests_tools/integration_tests_vn.dart';
import '../credit_card_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Credit card management test', () {
    integrationTestFakeVn('General check exist', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      final cardsManagement = CardsManagementPage(tester: tester);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: '1111111111'),
      );
      cardsManagement
        ..isReady()
        ..checkAppBarTitle('<PERSON>u<PERSON>n lý thẻ');
      await tester.pumpAndSettle(const Duration(seconds: 1));
      cardsManagement
        ..sliderViewExist()
        ..mainScrollViewExist()
        ..viewInfoCellExist()
        ..tempBlockCellExist()
        ..ecommerceCellExist()
        ..issuanceCellExist();
    });

    integrationTestFakeVn('Slider check', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      final cardsManagement = CardsManagementPage(tester: tester);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: '1111111111'),
      );
      cardsManagement.isReady();
      await tester.pumpAndSettle(const Duration(seconds: 1));
      cardsManagement.sliderViewExist();
      await tester.pumpAndSettle(const Duration(seconds: 1));
      await cardsManagement.scrollToNextCard();
      await tester.pumpAndSettle(const Duration(seconds: 1));
      await cardsManagement.scrollBackPreviousCard();
      await tester.pumpAndSettle(const Duration(seconds: 2));
    });
  });
}
