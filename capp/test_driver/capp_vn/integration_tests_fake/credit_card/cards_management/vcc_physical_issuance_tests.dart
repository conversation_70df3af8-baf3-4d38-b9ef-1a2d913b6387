import 'package:capp_domain/capp_domain.dart' as domain;
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../../integration_page_objects/otp_page.dart';
import '../../../integration_page_objects_vn/credit_card/cards_management/credit_card_list_page.dart';
import '../../../integration_page_objects_vn/credit_card/cards_management/physical_issuance_benefit_page.dart';
import '../../../integration_page_objects_vn/credit_card/cards_management/physical_issuance_page.dart';
import '../../../integration_tests_tools/integration_tests_vn.dart';
import '../credit_card_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('VCC Physical Issuance test', () {
    integrationTestFakeVn('VCC Physical Issuance - with enable cardReplacementEligible', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: 'vcc_enable_physical_issuance'),
      );
      final cardsManagement = CardsManagementPage(tester: tester)..isReady();
      await tester.pumpAndSettle(const Duration(seconds: 1));

      cardsManagement.issuanceCellExist();
      await cardsManagement.physicalIssuanceCell.tap();
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      cardsManagement.isAbsent();

      final physicalIssuanceBenefitPage = PhysicalIssuanceBenefitPage(tester: tester)
        ..isReady()
        ..checkAppBarTitle('Cấp thẻ tín dụng vật lý');
      await physicalIssuanceBenefitPage.appBarBackButton.tap();
      physicalIssuanceBenefitPage.isAbsent();
      cardsManagement.isReady();
    });

    integrationTestFakeVn('VCC Physical Issuance - with disable cardReplacementEligible', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: 'vcc_disable_physical_issuance'),
      );
      final cardsManagement = CardsManagementPage(tester: tester)..isReady();
      await tester.pumpAndSettle(const Duration(seconds: 1));

      cardsManagement.issuanceCellExist();
      await cardsManagement.physicalIssuanceCell.tap();
      //No action.
      cardsManagement.isReady();
    });

    integrationTestFakeVn('VCC Physical Issuance - with cancel after activation', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: 'vcc_enable_physical_issuance'),
      );
      final cardsManagement = CardsManagementPage(tester: tester)..isReady();
      await tester.pumpAndSettle(const Duration(seconds: 1));

      cardsManagement.issuanceCellExist();
      await cardsManagement.physicalIssuanceCell.tap();
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      cardsManagement.isAbsent();
      final physicalIssuanceBenefitPage = PhysicalIssuanceBenefitPage(tester: tester)
        ..isReady()
        ..checkAppBarTitle('Cấp thẻ tín dụng vật lý');
      await physicalIssuanceBenefitPage.continueButton.tap();
      physicalIssuanceBenefitPage.isAbsent();
      final physicalIssuancePage = PhysicalIssuancePage(tester: tester)
        ..isReady()
        ..checkAppBarTitle('Cấp thẻ tín dụng vật lý');
      //Wait for Delivery address API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      physicalIssuancePage.checkDeliveryAddress(
        '20, Nguyễn Đăng Giai, Phường Thảo Điền, Thành Phố Thủ Đức, Thành Phố Hồ Chí Minh, Việt Nam',
      );
      await physicalIssuancePage.cancelAfterActivationSelectable.tap();
      await physicalIssuancePage.continueButton.tap();
      physicalIssuancePage.isAbsent();
      final otpPage = OtpPage(tester)..isReady();
      await otpPage.otpSendButton.tap();
      await otpPage.enterOtp('483755');
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      otpPage.isAbsent();
    });

    integrationTestFakeVn('VCC Physical Issuance - with cancel card now', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: 'vcc_enable_physical_issuance'),
      );
      final cardsManagement = CardsManagementPage(tester: tester)..isReady();
      await tester.pumpAndSettle(const Duration(seconds: 1));

      cardsManagement.issuanceCellExist();
      await cardsManagement.physicalIssuanceCell.tap();
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      cardsManagement.isAbsent();
      final physicalIssuanceBenefitPage = PhysicalIssuanceBenefitPage(tester: tester)
        ..isReady()
        ..checkAppBarTitle('Cấp thẻ tín dụng vật lý');
      await physicalIssuanceBenefitPage.continueButton.tap();
      physicalIssuanceBenefitPage.isAbsent();
      final physicalIssuancePage = PhysicalIssuancePage(tester: tester)
        ..isReady()
        ..checkAppBarTitle('Cấp thẻ tín dụng vật lý');

      //Wait for Delivery address API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      physicalIssuancePage.checkDeliveryAddress(
        '20, Nguyễn Đăng Giai, Phường Thảo Điền, Thành Phố Thủ Đức, Thành Phố Hồ Chí Minh, Việt Nam',
      );
      await physicalIssuancePage.cancelNowSelectable.tap();
      await physicalIssuancePage.continueButton.tap();
      physicalIssuancePage.isAbsent();
      final otpPage = OtpPage(tester)..isReady();
      await otpPage.otpSendButton.tap();
      await otpPage.enterOtp('483755');
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      otpPage.isAbsent();
    });
  });
}
