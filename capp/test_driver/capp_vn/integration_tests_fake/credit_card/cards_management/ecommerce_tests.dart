import 'package:capp_domain/capp_domain.dart' as domain;
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../../integration_page_objects/otp_page.dart';
import '../../../integration_page_objects_vn/credit_card/cards_management/credit_card_list_page.dart';
import '../../../integration_page_objects_vn/credit_card/cards_management/ecommerce_bottom_sheet.dart';
import '../../../integration_tests_tools/integration_tests_vn.dart';
import '../credit_card_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Ecommerce test', () {
    integrationTestFakeVn('Ecommerce payment - Turn On', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: 'ecommerce_turn_off'),
      );
      final cardsManagement = CardsManagementPage(tester: tester)..isReady();
      await tester.pumpAndSettle(const Duration(seconds: 1));

      cardsManagement.ecommerceCellExist();
      await cardsManagement.ecommerceCell.tap();
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      final ecommerceBottomSheet = EcommerceBottomSheet(tester: tester)
        ..isReady()
        ..checkConfirmButtonText('Có, kích hoạt thanh toán trực tuyến');

      await ecommerceBottomSheet.cancelButton.tap();
      //Wait for BottomSheet animation running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      ecommerceBottomSheet.isAbsent();

      await cardsManagement.ecommerceCell.tap();
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      await ecommerceBottomSheet.confirmButton.tap();
      //Wait for BottomSheet animation running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      ecommerceBottomSheet.isAbsent();
      cardsManagement.isAbsent();
      final otpPage = OtpPage(tester)..isReady();
      await otpPage.otpSendButton.tap();
      await otpPage.enterOtp('483755');
      //Wait for Otp verify API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      otpPage.isAbsent();
    });

    integrationTestFakeVn('Ecommerce payment - Turn Off', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      await testerHelper.navigateToCardManagementScreen(
        arguments: domain.CardsListManagementRouteArgs(contractNumber: 'ecommerce_turn_on'),
      );
      final cardsManagement = CardsManagementPage(tester: tester)..isReady();
      await tester.pumpAndSettle(const Duration(seconds: 1));

      cardsManagement.ecommerceCellExist();
      await cardsManagement.ecommerceCell.tap();
      //Wait for Card validation API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      final ecommerceBottomSheet = EcommerceBottomSheet(tester: tester)
        ..isReady()
        ..checkConfirmButtonText('Có, tắt thanh toán trực tuyến');

      await ecommerceBottomSheet.cancelButton.tap();
      //Wait for BottomSheet animation running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      ecommerceBottomSheet.isAbsent();

      await cardsManagement.ecommerceCell.tap();
      await ecommerceBottomSheet.confirmButton.tap();
      //Wait for BottomSheet animation running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      ecommerceBottomSheet.isAbsent();
      //Wait for Otp verify API running
      await tester.pumpAndSettle(const Duration(seconds: 1));
      cardsManagement.isAbsent();
    });
  });
}
