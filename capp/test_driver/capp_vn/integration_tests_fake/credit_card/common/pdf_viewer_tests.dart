import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_page_objects/credit_card/common/pdf_viewer_page.dart';
import '../../../integration_tests_tools/integration_tests_vn.dart';
import '../credit_card_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Pdf Viewer test', () {
    integrationTestFakeVn('General', (tester, context) async {
      final testerHelper = CreditCardHelperVn(tester: tester, context: context);
      final pdfViewerPage = PdfViewerPage(tester: tester);

      //Force navigate to Pdf Viewer screen from Home
      await testerHelper.navigateToPdfViewerScreen(
        arguments: PdfViewerArgs(
          title: 'Terms & Conditions',
          documentUrl:
              'https://www.homecredit.vn/upload/Loyalty_Reward_Point_Scheme_T_and_C_Oct24_Mar25_e78a22bec6.pdf',
        ),
      );
      pdfViewerPage
        ..isReady()
        ..checkAppBarTitle('Terms & Conditions');

      //Waiting for Pdf file load
      await tester.pumpAndSettle(const Duration(seconds: 5));
      pdfViewerPage.checkExpectedElements();

      await pdfViewerPage.nextButton.tap();
      pdfViewerPage.documentText.textEquals('Document 2/3');
      pdfViewerPage.checkExpectedPreviousElements();
      await pdfViewerPage.previousButton.tap();
      pdfViewerPage.documentText.textEquals('Document 1/3');
      pdfViewerPage.checkNoExpectedPreviousElements();
    });
  });
}

Future<void> testPdfViewerScreen({
  required WidgetTester tester,
  required TestContext context,
  required PdfViewerPage pdfViewerPage,
}) async {
  pdfViewerPage.isReady();
  //Waiting for Pdf file load
  await tester.pumpAndSettle(const Duration(seconds: 5));
  pdfViewerPage.checkExpectedElements();
}
