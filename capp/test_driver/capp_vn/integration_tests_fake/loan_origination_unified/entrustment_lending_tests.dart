import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../integration_page_objects/home_page.dart';
import '../../../integration_page_objects/loan_origination_unified/call_centre_page.dart';
import '../../../integration_page_objects/loan_origination_unified/product_display_page.dart';
import '../../../integration_tests_tools/tester_helper.dart';
import '../../integration_tests_tools/integration_tests_vn.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Entrustment Lending', () {
    integrationTestFakeVn('Entrustment Lending offer banner leads to call centre page and tap on homepage button',
        (tester, context) async {
      final homePage = HomeTestPage(tester);
      final productDisplayPage = ProductDisplayPage(tester);
      final callCentrePage = CallCentrePage(tester);
      final testerHelper = TesterHelper(tester: tester, context: context);

      await testerHelper.navigateToHome();
      homePage.verifyReady();

      await homePage.scrollToLoanOffersBanner();
      await homePage.scrollToLoanOffersBannerEntrustmentLendingItem();
      await homePage.loanOffersBannerEntrustmentLendingItem.tap();
      homePage.isAbsent();

      await productDisplayPage.continueButton.tap();
      callCentrePage.verifyReady();
      await callCentrePage.homepageButton.tap();
      homePage.verifyReady();
    });

    integrationTestFakeVn('Entrustment Lending offer banner leads to call centre page and tap on call centre button',
        (tester, context) async {
      final homePage = HomeTestPage(tester);
      final productDisplayPage = ProductDisplayPage(tester);
      final callCentrePage = CallCentrePage(tester);
      final testerHelper = TesterHelper(tester: tester, context: context);

      await testerHelper.navigateToHome();
      homePage.verifyReady();

      await homePage.scrollToLoanOffersBanner();
      await homePage.scrollToLoanOffersBannerEntrustmentLendingItem();
      await homePage.loanOffersBannerEntrustmentLendingItem.tap();

      homePage.isAbsent();
      await productDisplayPage.continueButton.tap();
      callCentrePage.verifyReady();
      await callCentrePage.callCentreButton.tap();
    });
  });
}
