import 'dart:async';

import 'package:capp_api/capp_api.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_shared_core/capp_loan_shared_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/elements/index.dart';
import 'package:selfcareapi/model/models.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../../integration_page_objects/index.dart';
import '../../../integration_page_objects/loan_origination_unified/pos_selection_page.dart';
import '../../../integration_reuse_steps/ulo.dart';
import '../../../integration_tests_tools/tester_extension.dart';
import '../../../integration_tests_tools/tester_helper.dart';
import '../../integration_page_objects_vn/loan_origination_unified/account_verification_page.dart';
import '../../integration_page_objects_vn/loan_origination_unified/filter_pos_page.dart';
import '../../integration_page_objects_vn/loan_origination_unified/no_offers_alternative_page.dart';
import '../../integration_page_objects_vn/loan_origination_unified/pos_info_page.dart';
import '../../integration_page_objects_vn/loan_origination_unified/pos_loan_confirmation_page.dart';
import '../../integration_page_objects_vn/loan_origination_unified/tw_income_referral_code_page.dart';
import '../../integration_page_objects_vn/loan_origination_unified/tw_offer_list_page.dart';
import '../../integration_tests_tools/integration_tests_vn.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('TW Journey Test with Action Belt', () {
    /// precondition:  user login in app and not have any offers
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps on 'Continue'
    /// app displays 'No Offers' screen
    integrationTestFakeVn(
      'should show No Offer Alternative when user doesnt have any offers',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.noOffersAlternative);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final noOfferAlternativePage = NoOffersAlternativeTestPage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        await tester.delay();
        noOfferAlternativePage.isReady();
      },
    );

    /// precondition: user login in app
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    integrationTestFakeVn(
      'should show TW product display screen when tap on action belt',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await tester.delay();
        productDisplayPage.isReady();
      },
    );

    /// precondition: user login in app and has a dummy offer
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps 'Continue'
    /// app displays 'Account Verification Screen'
    integrationTestFakeVn(
      'should show Account Verification Page when have dummyOffer & tap TW action belt then tap continue',
      (tester, context) async {
        setDeeplinkScenario(context, DeeplinkTestScenario.newUser);
        setTwTestScenario(context, TwTestScenario.newUser);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final accountVerificationPage = AccountVerificationPage(tester);
        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await tester.delay();
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        accountVerificationPage.isReady();
        await tester.delay(const Duration(seconds: 3)); // to avoid IdCardBloc emit state fail
      },
    );

    /// precondition: user login in app and has a TW offer without SA referral code
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps 'Continue'
    /// app displays 'Pos Loan Confirmation Screen' with Visit Store button
    integrationTestFakeVn(
      'should show Pos Loan Confirmation with Visit Store Btn when tapping TW action belt when having TW offer without referral code & tap Continue',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasTWOffers);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final posLoanConfirmationPage = PosLoanConfirmationPage(tester);
        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await tester.delay();
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        posLoanConfirmationPage.verifyReady();
        posLoanConfirmationPage.visitStoreBtn.isReady();
      },
    );

    /// precondition: user login in app and has a TW offer with SA referral code
    /// and some other offers.
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps 'Continue'
    /// app displays 'Pos Loan Confirmation Screen' with 'Go Home' button
    /// and 'Check Another Offer' button
    integrationTestFakeVn(
      '''
should show Pos Confimration with Home Btn & Check Another Offer Btn when tap TW action belt when have TW offer
with referralCode & tap continue
''',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasTWOffersWithRefCode);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final posLoanConfirmationPage = PosLoanConfirmationPage(tester);
        final offerMenuPage = TWOfferListTestPage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await testerHelper.delay(const Duration(seconds: 1));
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        posLoanConfirmationPage.verifyReady();
        posLoanConfirmationPage.navigateToHomeBtn.isReady();
        posLoanConfirmationPage.checkAnotherOfferBtn.isReady();
        await posLoanConfirmationPage.checkAnotherOfferBtn.tap();
        offerMenuPage.isReady();
      },
    );

    /// precondition: user login in app and has no TW offer but 1 income scoring offer
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps 'Continue'
    /// app displays 'Income Input Screen' with SA Code input
    integrationTestFakeVn(
      'should show Income scoring with SA Ref Code when tap TW action belt and have no TW offer but 1 income scoring & tap continue',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasScoringOffer);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final incomeSaCodeReferralPage = TwIncomeSAReferralCodePage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        incomeSaCodeReferralPage.isReady();
      },
    );

    /// precondition: user login in app and completed the Journey
    /// and not have any other offers
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps 'Continue'
    /// app displays 'POS Info Screen' with the selected POS address
    /// without 'Check Another Offers' button
    integrationTestFakeVn(
      'should show Pos Info without other offers when completed the journey',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.doneTwJourneyWithoutOtherOffers);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final posInfoPage = PosInfoTestPage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        await tester.delay();
        posInfoPage.verifyReady();
        posInfoPage.checkAnotherOfferBtn.isAbsent();
      },
    );

    /// precondition: user login in app and completed the Journey
    /// and has some other offers
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps 'Continue'
    /// app displays 'POS Info Screen' with the selected POS address
    /// with 'Check Another Offers' button
    /// user taps 'Check Another Offers' button
    /// app displays 'Offer Menu Screen'
    integrationTestFakeVn(
      'show Pos Info with other offers when completed the journey',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.doneTwJourneyWithOtherOffers);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final posInfoPage = PosInfoTestPage(tester);
        final offerMenuPage = TWOfferListTestPage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        await tester.delay();
        posInfoPage.verifyReady();
        await posInfoPage.checkAnotherOfferBtn.isPresent();
        await posInfoPage.checkAnotherOfferBtn.tap();
        offerMenuPage.isReady();
      },
    );

    /// precondition: user login in app and has TW offer withou SA Code
    /// user taps on action belt TW
    /// app displays Product Screen for TW
    /// user taps 'Continue'
    /// app displays 'POS Selection Screen'
    integrationTestFakeVn(
      'should show nearby pos when tapping on visit store',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.twJourneyAtPosSelection);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final posSelectionPage = PosSelectionTestPage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        productDisplayPage.isReady();
        productDisplayPage.continueButton.isReady();
        await productDisplayPage.continueButton.tap();
        await tester.delay();
        posSelectionPage.isReady();
      },
    );
  });

  group('TW Journey Test in Offer menu', () {
    /// precondition: user login in app and has TW offer
    /// app displays TW offer in Offer Menu
    /// user taps TW Offer
    /// app displays Product Screen for TW
    integrationTestFakeVn(
      'should show Product Display on tapping TW Offer Menu',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasTWOffers);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final loansPage = LoansTestPage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        await mainPage.bottomBarLoansButton.tap();
        await loansPage.isPresent();
        final offer = (context.container<BannerApi>() as FakeBannerApi).createTWOfferResponse().offerBanners!.first;
        final offerItem = loansPage.from(offer);
        await offerItem.tap();
        productDisplayPage.isReady();
      },
    );
  });

  group('POS Filter Test', () {
    /// precondition: user login in app and has TW offer without SA Code
    /// the app doesn't have location permission
    /// user taps 'Pick a Store'
    /// app display POS selection with Brand Filter
    integrationTestFakeVn(
      'should display brand filter in Pos Filter Page',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasTWOffers);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(featureName: FeatureFlag.homeActionBeltDynamic, isEnabled: true);
        final filterPosPage = FilterPosTestPage(tester);
        await testerHelper.navigateToHome();
        await testerHelper.navigateToFilterPosTW();
        filterPosPage
          ..verifyReady()
          ..verifyBrandFilter();
      },
    );
  });

  group('Product Display Test', () {
    /// precondition: user is in Product Screen for TW
    /// app display hot brands in the bottom of the page
    integrationTestFakeVn(
      'should display hot brands section in product display for TW',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasTWOffers);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final homePage = HomeTestPage(tester);
        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final loansPage = LoansTestPage(tester);

        await testerHelper.navigateToHome();

        homePage.verifyReady();
        await mainPage.bottomBarLoansButton.tap();
        await loansPage.isPresent();

        final offer = (context.container<BannerApi>() as FakeBannerApi).createTWOfferResponse().offerBanners!.first;
        final offerItem = loansPage.from(offer);
        await offerItem.tap();
        productDisplayPage.isReady();
        await tester.pumpAndSettle();
        final scrollFinder = find.byType(ListView).first;
        await tester.drag(scrollFinder, const Offset(0, -10000));
        await tester.pumpAndSettle(const Duration(seconds: 1));
        productDisplayPage.hotBrands.isReady();
      },
    );
  });

  group('Income Input with SA Code', () {
    const validSACode = '0888888888'; // length = 10
    const invalidSACode = '1'; // length < 10

    /// precondition: user is in 'Income Input Screen' with SA Code
    /// app displays income input & SA code input
    integrationTestFakeVn(
      'should display income input and SA code input',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasScoringOffer);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final incomeSaCodeReferralPage = TwIncomeSAReferralCodePage(tester);

        await testerHelper.navigateToHome();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await productDisplayPage.continueButton.tap();
        incomeSaCodeReferralPage.isReady();
        incomeSaCodeReferralPage.codeInput.isReady();
        incomeSaCodeReferralPage.incomeInput.isReady();
      },
    );

    /// precondition: user in 'Income Input Screen' with SA Code
    /// user inputs income, then tap Confirm button
    /// app displays Remind Popup to remind users to input the SA Code
    integrationTestFakeVn(
      'should show popup to remind user when inputted income without inputting SA Code & tapped on confirm btn',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasScoringOffer);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          )
          ..toggleFeatureFlag(
            featureName: FeatureFlag.removePopupSaConfirmationTW,
            isEnabled: false,
          );

        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final incomeSaCodeReferralPage = TwIncomeSAReferralCodePage(tester);

        await testerHelper.navigateToHome();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await productDisplayPage.continueButton.tap();
        incomeSaCodeReferralPage.isReady();
        incomeSaCodeReferralPage.codeInput.isReady();
        await incomeSaCodeReferralPage.fillIncome(income: '5000000');
        await incomeSaCodeReferralPage.confirmBtn.tap();
        incomeSaCodeReferralPage.verifyRemindPoup();
      },
    );

    /// precondition: user in 'Income Input Screen' with SA Code
    /// user inputs income, input a valid code then tap continue
    /// app navigates user to the next screen
    integrationTestFakeVn(
      'should navigate user to next screen when inputted income and valid SA Code then tapped on confirm btn',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasScoringOffer);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final incomeSaCodeReferralPage = TwIncomeSAReferralCodePage(tester);

        await testerHelper.navigateToHome();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await productDisplayPage.continueButton.tap();
        incomeSaCodeReferralPage.isReady();
        incomeSaCodeReferralPage.codeInput.isReady();
        await incomeSaCodeReferralPage.fillIncome(income: '5000000');
        await incomeSaCodeReferralPage.fillSACode(code: validSACode);
        await incomeSaCodeReferralPage.verifySaCodeBtn.tap();
        incomeSaCodeReferralPage.saName.isReady();
        await incomeSaCodeReferralPage.confirmBtn.tap();
        incomeSaCodeReferralPage.isAbsent();
        await tester.delay(const Duration(seconds: 3)); // to avoid IdCardBloc emit state fail
      },
    );

    /// precondition: user in 'Income Input Screen' with SA Code
    /// input a valid code then tap verify
    /// app display SA Name
    integrationTestFakeVn(
      'should display SA name when input a correct SA Code',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasScoringOffer);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final incomeSaCodeReferralPage = TwIncomeSAReferralCodePage(tester);

        await testerHelper.navigateToHome();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await productDisplayPage.continueButton.tap();
        incomeSaCodeReferralPage.isReady();
        incomeSaCodeReferralPage.codeInput.isReady();
        await incomeSaCodeReferralPage.fillSACode(code: validSACode);
        await incomeSaCodeReferralPage.verifySaCodeBtn.tap();
        incomeSaCodeReferralPage.saName.isReady();
      },
    );

    /// precondition: user in 'Income Input Screen' with SA Code
    /// input a valid code then tap verify
    /// app displays valid SA code
    integrationTestFakeVn(
      'should display SA name & successful message when input a correct SA Code',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasScoringOffer);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final incomeSaCodeReferralPage = TwIncomeSAReferralCodePage(tester);

        await testerHelper.navigateToHome();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await productDisplayPage.continueButton.tap();
        incomeSaCodeReferralPage.isReady();
        incomeSaCodeReferralPage.codeInput.isReady();
        await incomeSaCodeReferralPage.fillSACode(code: validSACode);
        await incomeSaCodeReferralPage.verifySaCodeBtn.tap();
        incomeSaCodeReferralPage.verifyValidSACode();
      },
    );

    /// precondition: user in 'Income Input Screen' with SA Code
    /// input an invalid code then tap verify
    /// app displays error message
    integrationTestFakeVn(
      'should display error when input an incorrect SA Code',
      (tester, context) async {
        setTwTestScenario(context, TwTestScenario.hasScoringOffer);
        final testerHelper = TesterHelper(tester: tester, context: context)
          ..forceFakeLoginStateAsRegistered()
          ..setActionBeltScenario(context, ActionBeltTestScenario.twVN)
          ..toggleFeatureFlag(
            featureName: FeatureFlag.homeActionBeltDynamic,
            isEnabled: true,
          );

        final mainPage = MainTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final incomeSaCodeReferralPage = TwIncomeSAReferralCodePage(tester);

        await testerHelper.navigateToHome();
        mainPage.twoWheelerActionButton.isReady();
        await mainPage.twoWheelerActionButton.tap();
        await productDisplayPage.continueButton.tap();
        incomeSaCodeReferralPage.isReady();
        incomeSaCodeReferralPage.codeInput.isReady();
        await incomeSaCodeReferralPage.fillSACode(code: invalidSACode);
        await incomeSaCodeReferralPage.verifySaCodeBtn.tap();
        incomeSaCodeReferralPage.verifyInvalidSACode();
      },
    );
  });
}

extension _OfferTW on LoansTestPage {
  TestElement from(OfferBannerDto offer) {
    return registerButtonElement(finder: find.byKey(Key('__loanOfferListItem_${offer.id}_${offer.offerId}_')));
  }
}

extension _FilterPosNavigation on TesterHelper {
  Future<void> navigateToFilterPosTW() async {
    logger.i('Navigate to the Filter Pos screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappLoanShared',
        'FilterPos',
        'CappHome',
        'MainScreen',
        arguments: FilterPosArguments(
          address: null,
          appointmentType: PosAppointmentType.tw,
        ),
      ),
    );
    await tester.pumpAndSettle();
  }
}
