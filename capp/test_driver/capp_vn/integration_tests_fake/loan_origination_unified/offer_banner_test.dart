import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../integration_page_objects/index.dart';
import '../../../integration_tests_tools/tester_helper.dart';
import '../../integration_tests_tools/integration_tests_vn.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Offer Banners', () {
    integrationTestFakeVn('Income offer banner - should not proceed with no input', (tester, context) async {
      final homePage = HomeTestPage(tester);
      final testerHelper = TesterHelper(tester: tester, context: context);
      await testerHelper.navigateToHome();
      homePage.verifyReady();
      await homePage.scrollToIncomeOfferBanner();
      await homePage.incomeOfferBannerButton.tap();
      homePage.verifyReady();
    });

    integrationTestFakeVn(
      'Income offer banner - proceed with valid input',
      (tester, context) async {
        final homePage = HomeTestPage(tester);
        final testerHelper = TesterHelper(tester: tester, context: context);
        await testerHelper.navigateToHome();
        homePage.verifyReady();

        await homePage.scrollToIncomeOfferBanner();
        homePage.verifyReady();
        await homePage.incomeOfferBannerInput.tap();
        homePage.incomeOfferBannerInput.isReady();
        await homePage.incomeOfferBannerInput
            .enterText('0', settleAfterPump: false, duration: const Duration(seconds: 1));
        await homePage.incomeOfferBannerButton.tap();
        homePage.verifyReady();

        await homePage.incomeOfferBannerInput.tap();
        homePage.incomeOfferBannerInput.isReady();
        await homePage.incomeOfferBannerInput
            .enterText('99999999', settleAfterPump: false, duration: const Duration(seconds: 1));
        await homePage.incomeOfferBannerButton.tap();
        homePage.isAbsent();
      },
      skip: true,
    );

    integrationTestFakeVn('Product offer banner leads to Product Display screen and starts loan journey',
        (tester, context) async {
      final testerHelper = TesterHelper(tester: tester, context: context);

      final homePage = HomeTestPage(tester);
      final productDisplayPage = ProductDisplayPage(tester);
      final loanOriginationUnifiedCore = LoanOriginationUnifiedCorePage(tester);

      await testerHelper.navigateToHome();
      homePage.verifyReady();

      await homePage.scrollToLoanOffersBanner();
      await homePage.scrollToLoanOffersBannerProductItem();
      await homePage.loanOffersBannerProductItem.tap();
      productDisplayPage.isReady();

      await productDisplayPage.continueButton.tap();
      loanOriginationUnifiedCore.isReady();
    });
    integrationTestFakeVn('Product offer banner leads to Product Display screen and can return back to Home screen',
        (tester, context) async {
      final testerHelper = TesterHelper(tester: tester, context: context);

      final homePage = HomeTestPage(tester);
      final productDisplayPage = ProductDisplayPage(tester);

      await testerHelper.navigateToHome();
      homePage.verifyReady();

      await homePage.scrollToLoanOffersBanner();
      await homePage.scrollToLoanOffersBannerProductItem();
      await homePage.loanOffersBannerProductItem.tap();
      productDisplayPage.isReady();

      await productDisplayPage.backButton.tap();
      homePage.isReady();
    });
    integrationTestFakeVn('Loan offers banner - second offer proceed', (tester, context) async {
      final homePage = HomeTestPage(tester);
      final testerHelper = TesterHelper(tester: tester, context: context);
      await testerHelper.navigateToHome();
      homePage.verifyReady();
      await homePage.scrollToIncomeOfferBanner();
      await homePage.scrollToLoanOffersBanner();
      await homePage.loanOffersBanner.swipeLeft();
      await homePage.loanOffersBanner.tap();
      homePage.isAbsent();
    });
    integrationTestFakeVn('Dummy offer banner leads to Sign Up', (tester, context) async {
      final homePage = HomeTestPage(tester);
      final authLandingPage = AuthLandingPageVn(tester);
      final testerHelper = TesterHelper(tester: tester, context: context);
      await testerHelper.navigateToHome();
      homePage.verifyReady();

      await homePage.scrollToLoanOffersBanner();
      await homePage.scrollToLoanOffersBannerDummyItem();
      await homePage.loanOffersBannerDummyItem.tap();
      authLandingPage.isReady();
    });
    integrationTestFakeVn('Prospect offer banner starts loan journey', (tester, context) async {
      final testerHelper = TesterHelper(tester: tester, context: context);

      final homePage = HomeTestPage(tester);
      final loanOriginationUnifiedCore = LoanOriginationUnifiedCorePage(tester);

      await testerHelper.navigateToHome();
      homePage.verifyReady();

      await homePage.scrollToLoanOffersBanner();
      await homePage.scrollToLoanOffersBannerProspectItem();
      await homePage.loanOffersBannerProspectItem.tap();
      loanOriginationUnifiedCore.isReady();
    });
  });
}
