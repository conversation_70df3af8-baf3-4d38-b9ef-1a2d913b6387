import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../integration_page_objects/index.dart';
import '../../../integration_reuse_steps/homepage.dart';
import '../../../integration_tests_tools/tester_helper.dart';
import '../../integration_tests_tools/integration_tests_vn.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Loans screen', () {
    integrationTestFakeVn(
      'Product offer list item leads to Product Display screen, which allows to continue the journey',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);

        final homePage = HomeTestPage(tester);
        final loansPage = LoansTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);
        final loanOriginationUnifiedCore = LoanOriginationUnifiedCorePage(tester);

        await testerHelper.navigateToHome();
        homePage.verifyReady();

        await navigationToLoansTest(tester, testerHelper);

        await loansPage.scrollToLoanOffersBannerProductItem();
        await loansPage.loanOffersBannerFirstItem.tap();
        productDisplayPage.isReady();

        await productDisplayPage.continueButton.tap();
        loanOriginationUnifiedCore.isReady();
      },
    );
    integrationTestFakeVn(
      'Product offer list item leads to Product Display screen, which allows to return back to Loans screen',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);

        final homePage = HomeTestPage(tester);
        final loansPage = LoansTestPage(tester);
        final productDisplayPage = ProductDisplayPage(tester);

        await testerHelper.navigateToHome();
        homePage.verifyReady();

        await navigationToLoansTest(tester, testerHelper);

        await loansPage.scrollToLoanOffersBannerProductItem();
        await loansPage.loanOffersBannerFirstItem.tap();
        productDisplayPage.isReady();

        await productDisplayPage.backButton.tap();
        loansPage.isReady();
      },
    );
  });
}
