import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../integration_page_objects/loan_origination_unified/income_details_page.dart';
import '../../../integration_reuse_steps/ulo.dart';
import '../../integration_tests_tools/integration_tests_vn.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Income details test:', () {
    integrationTestFakeVn('retired case', (tester, context) async {
      final incomeDetailsPage = await openLouFormPage(tester, context);

      await incomeDetailsPage.selectRetired();
      await incomeDetailsPage.enterMonthlyIncome('20000');
      await incomeDetailsPage.tapOnContinue();

      incomeDetailsPage.isAbsent();

      // OPTIONAL: we could check occupation and income (20.000 đ) on application form section
    });

    integrationTestFakeVn('housewife case', (tester, context) async {
      final incomeDetailsPage = await openLouFormPage(tester, context);

      await incomeDetailsPage.selectHousewife();
      await incomeDetailsPage.enterMonthlyIncome('10000');
      await incomeDetailsPage.tapOnContinue();

      incomeDetailsPage.isAbsent();

      // OPTIONAL: we could check occupation and income (10.000 đ) on application form section
    });

    integrationTestFakeVn('self-employed case', (tester, context) async {
      final incomeDetailsPage = await openLouFormPage(tester, context);

      await incomeDetailsPage.selectSelfEmployed();
      await incomeDetailsPage.tapOnProfessionInput();
      await incomeDetailsPage.selectProfessionOther();
      await incomeDetailsPage.enterCompanyName('SelfNamedCompany');
      await incomeDetailsPage.enterJobStartingFrom('7/7/2007');
      await incomeDetailsPage.enterMonthlyIncome('40000');
      await incomeDetailsPage.tapOnContinue();

      incomeDetailsPage.isAbsent();

      // OPTIONAL: we could check occupation and income (40.000 đ) on application form section
    });

    integrationTestFakeVn('student case', (tester, context) async {
      final incomeDetailsPage = await openLouFormPage(tester, context);

      await incomeDetailsPage.selectStudent();
      await incomeDetailsPage.enterUnivesityName('Oxford');
      await incomeDetailsPage.enterMonthlyIncome('5000');
      await incomeDetailsPage.enterStudyStartingFrom('1/9/2020');
      await incomeDetailsPage.tapOnContinue();

      incomeDetailsPage.isAbsent();

      // OPTIONAL: we could check occupation and income (5.000 đ) on application form section
    });

    integrationTestFakeVn('employed case', (tester, context) async {
      final incomeDetailsPage = await openLouFormPage(tester, context);

      await incomeDetailsPage.selectEmployed();
      await incomeDetailsPage.tapOnProfessionInput();
      await incomeDetailsPage.selectProfessionOther();
      await incomeDetailsPage.enterCompanyName('Apple');
      await incomeDetailsPage.enterMonthlyIncome('500000');
      await incomeDetailsPage.enterJobStartingFrom('5/5/2012');
      await incomeDetailsPage.tapOnContinue();

      incomeDetailsPage.isAbsent();

      // OPTIONAL: we could check occupation and income (500.000 đ) on application form section
    });

    integrationTestFakeVn('unemployed case', (tester, context) async {
      final incomeDetailsPage = await openLouFormPage(tester, context);

      await incomeDetailsPage.selectUnemployed();
      await incomeDetailsPage.enterMonthlyIncome('15000');
      await incomeDetailsPage.tapOnContinue();

      incomeDetailsPage.isAbsent();

      // OPTIONAL: we could check occupation and income (15.000 đ) on application form section
    });
  });
}

Future<IncomeDetailsPage> openLouFormPage(WidgetTester tester, TestContext context) async {
  final incomeDetailsPage = IncomeDetailsPage(tester);
  await startFakeFlowPage('__incomeDetailsButtonKey__', context, tester);
  incomeDetailsPage.isReady();
  return incomeDetailsPage;
}
