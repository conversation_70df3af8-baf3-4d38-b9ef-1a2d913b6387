import 'dart:async';

import 'package:capp_auth/capp_auth.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_innovatrics/capp_innovatrics.dart';
import 'package:capp_innovatrics_core/capp_innovatrics_core.dart'
    hide IBiometricsRepository, IBiometricsStorage, CappInnovatricsTrackingService;
import 'package:capp_loan_shared_core/capp_loan_shared_core.dart';
import 'package:capp_repayment/capp_repayment.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart' hide State;
import 'package:decimal/decimal.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:koyal_testing/integration_test/index.dart';
import 'package:logger/logger.dart';
import 'package:navigator/navigator.dart';

import '../../integration_tests_tools/tester_helper.dart';

/// Help functionality that is shared between PROD & FAKE tests
class TesterHelperVn extends TesterHelper {
  TesterHelperVn({
    required WidgetTester tester,
    required TestContext context,
  }) : super(tester: tester, context: context);

  Future<void> navigateToRepaymentOnlinePayment() async {
    logger.i('Navigate to the repayment online payment screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentOnlinePaymentScreen',
        'CappHome',
        'MainScreen',
        arguments: RepaymentOnlinePaymentRouteArgs(
          selectedAmount: Decimal.parse('50000'),
          selectedContract: RepaymentContract(
            contractType: RepaymentContractType.cel,
            loanContract: RepaymentLoanContract(
              contractNumber: '0354468901',
              contractType: 'CEL',
              loanType: 'cash',
              contractStatus: 'active',
              productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
              productName: 'Cash Loan',
              dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
              dueAmount: Decimal.parse('69000'),
            ),
          ),
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToRepaymentCelAmountSelection({
    required RepaymentLoanContract repaymentLoanContract,
    RepaymentMethodType? type,
  }) async {
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentSelectAmountCelScreen',
        'CappHome',
        'MainScreen',
        arguments: RepaymentSelectAmountCelRouteArgs(
          repaymentLoanContract: repaymentLoanContract,
          repaymentMethodType: type ?? RepaymentMethodType.eWallet,
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToRepaymentRelAmountSelectionCase({
    required RepaymentRelContract repaymentRelContract,
    RepaymentMethodType? type,
  }) async {
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentSelectAmountRelScreen',
        'CappHome',
        'MainScreen',
        arguments: RepaymentSelectAmountRelRouteArgs(
          repaymentRelContract: repaymentRelContract,
          repaymentMethodType: type ?? RepaymentMethodType.eWallet,
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  @override
  Future<void> navigateToRepaymentMethodSelection() async {
    logger.i('Navigate to the repayment method selection screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentMethodSelectionScreen',
        'CappHome',
        'MainScreen',
        arguments: RepaymentMethodSelectionRouteArgs(
          selectedContract: RepaymentContract(
            contractType: RepaymentContractType.cel,
            loanContract: RepaymentLoanContract(
              contractNumber: '0354468901',
              contractType: 'CEL',
              loanType: 'cash',
              contractStatus: 'active',
              productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
              productName: 'Cash Loan',
              dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
              dueAmount: Decimal.parse('69000'),
              totalOutstandingDebt: Decimal.parse('2000000'),
            ),
          ),
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToRepaymentPaymentSummary({
    bool? fromRepaymentNew,
    RepaymentVoucherCalculationData? voucherCalculationData,
  }) async {
    logger.i('Navigate to the repayment payment summary screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentPaymentSummaryScreen',
        'CappHome',
        'MainScreen',
        arguments: RepaymentPaymentSummaryRouteArgs(
          contract: RepaymentContract(
            contractType: RepaymentContractType.cel,
            loanContract: RepaymentLoanContract(
              contractNumber: '0354468901',
              contractType: 'CEL',
              loanType: 'cash',
              contractStatus: 'active',
              productImageUrl: 'https://i.imgur.com/Nn4BGG5.png',
              productName: 'Cash Loan',
              dueDate: DateTime.parse('2020-08-31T06:53:26.757Z'),
              dueAmount: Decimal.parse('69000'),
              totalOutstandingDebt: Decimal.parse('2000000'),
            ),
          ),
          selectedAmount: Decimal.parse('50000'),
          fullName: '',
          selectedPaymentMethod: RepaymentUserPaymentMethod(
            category: RepaymentPaymentCategory.ewallet.name,
            gmaId: RepaymentPaymentMethod.viettel.getId(),
            description: '',
            badge: '',
            iconUrl: RepaymentPaymentMethod.momo.iconUrl,
            title: 'Viettel',
          ),
          fromRepaymentNew: fromRepaymentNew ?? false,
          voucherCalculationData: voucherCalculationData,
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> popFromViettelPaymentWithSuccess() async {
    logger.i('Pop from Viettel screen with success');
    context.configuration.navigator?.pop(
      right<String, Map>(<String, dynamic>{
        'transactionId': 'VT012121212121',
        'amount': 59000.0,
        'customerName': 'Trang Nguyen Phuong',
        'contractNumber': '**********',
        'date': DateTime.now().toString(),
        'provider': 'Viettel Money',
      }),
    );
    await tester.pumpAndSettle();
  }

  Future<void> popFromViettelPaymentWithFailed() async {
    logger.i('Pop from Viettel screen with failed');
    context.configuration.navigator?.pop(left<String, Map>('Users cancel payment'));
    await tester.pumpAndSettle();
  }

  Future<void> navigateToBankTransferQr({required RepaymentBankTransferRouteArgs routeArgs}) async {
    logger.i('Navigate to the repayment bank transfer Qr screen.');
    unawaited(
      context.configuration.navigator?.pushNamed(
        NavigatorPath.cappRepayment.repaymentBankTransferQrScreen,
        arguments: routeArgs,
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToPtpPaymentPlan({required RepaymentPtpPaymentPlanRouteArgs routeArgs}) async {
    logger.i('Navigate to the PTP payment plan screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentPtpPaymentPlanScreen',
        'CappHome',
        'MainScreen',
        arguments: routeArgs,
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToAdaMain({RepaymentAdaMainRouteArgs? routeArgs}) async {
    logger.i('Navigate to the ada main screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentAdaMainScreen',
        'CappHome',
        'MainScreen',
        arguments: routeArgs,
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToAdaManagement({String? contractNumber}) async {
    logger.i('Navigate to the ada management screen.');
    unawaited(
      context.configuration.navigator?.pushNamed(
        NavigatorPath.cappRepayment.repaymentAdaManagementScreen,
        arguments: ScreenArguments(
          map: {
            'contractNumber': contractNumber,
          },
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToRepaymentTransactionSuccessScreen(String contractNumber) async {
    logger.i('Navigate to the repayment transaction success screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'RepaymentTransactionSuccessScreen',
        'CappHome',
        'MainScreen',
        arguments: RepaymentTransactionSuccessRouteArgs(
          isEWallet: false,
          contractNo: contractNumber,
          finalAmount: 2000000.0,
          customerName: '',
          phoneNumber: '0983333333',
          paymentOption: 'One Pay',
          paymentOptionId: RepaymentPaymentMethod.onepay.getId(),
          dateProcessed: '2024-07-07T06:53:26.757Z',
          transactionNo: '1010800158',
        ),
      ),
    );
    await tester.pumpAndSettle();
  }

  @override
  void setMockBigDataService() {
    final c = context.container;
    if (c.isRegistered<IBigDataCollectionService>()) {
      c.unregister<IBigDataCollectionService>();
    }
    if (c.isRegistered<BigDataCollectionBloc>()) {
      c.unregister<BigDataCollectionBloc>();
    }
    if (c.isRegistered<ChangeNationalIdVerificationCubit>()) {
      c.unregister<ChangeNationalIdVerificationCubit>();
    }
    if (c.isRegistered<ChangePhoneBosVerificationCubit>()) {
      c.unregister<ChangePhoneBosVerificationCubit>();
    }

    c
      ..registerLazySingleton<IBigDataCollectionService>(
        () => BigdataCollectionTestService(
          ipAddressService: c<IIpAddressService>(),
          locationService: c<ILocationService>(),
          platformService: c<IPlatformService>(),
          bigdataRepository: c<IBigdataRepository>(),
          permissionsService: c<IPermissionsService>(),
          logger: c<Logger>(),
        ),
      )
      ..registerFactory<ChangeNationalIdVerificationCubit>(() {
        return ChangeNationalIdVerificationCubit(
          changeNationalIdRepository: c<IChangeNationalIdRepository>(),
          bigDataCollectionService: c<IBigDataCollectionService>(),
        );
      })
      ..registerFactory<BigDataCollectionBloc>(() {
        return BigDataCollectionBloc(
          logger: c<Logger>(),
          bigDataCollectionService: c<IBigDataCollectionService>(),
        );
      })
      ..registerFactory<ChangePhoneBosVerificationCubit>(() {
        return ChangePhoneBosVerificationCubit(
          changePhoneRepository: c<IChangePhoneRepository>(),
          bigDataCollectionService: c<IBigDataCollectionService>(),
        );
      });
  }

  @override
  void setMockInnovatricsBloc() {
    final c = context.container;
    if (c.isRegistered<InnovatricsSelfieBloc>()) {
      c.unregister<InnovatricsSelfieBloc>();
    }
    if (c.isRegistered<IdCardPreviewBloc>()) {
      c.unregister<IdCardPreviewBloc>();
    }
    if (c.isRegistered<IdCardCoreBloc>()) {
      c.unregister<IdCardCoreBloc>();
    }

    c
      ..registerTrackingFactory<InnovatricsSelfieBloc>(() {
        return DriveTestInnovatricsSelfieBloc(
          biometricsRepository: c<IBiometricsRepository>(),
          innovatricsRepository: c<IInnovatricsRepository>(),
          permissionsService: c<IPermissionsService>(),
          lockStatusBloc: c<LockStatusBloc>(),
          localizationRepository: c<ILocalizationRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
          biometricsStorage: c<IBiometricsStorage>(),
          firebasePerformanceMonitoring: c<IFirebasePerformanceMonitoring>(),
          logger: c<Logger>(),
        );
      })
      ..registerTrackingFactory<IdCardPreviewBloc>(() {
        return DriverTestIdCardPreviewBloc(
          biometricsRepository: c<IBiometricsRepository>(),
          innovatricsRepository: c<IInnovatricsRepository>(),
          permissionsService: c<IPermissionsService>(),
          lockStatusBloc: c<LockStatusBloc>(),
          localizationRepository: c<ILocalizationRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
          biometricsStorage: c<IBiometricsStorage>(),
          firebasePerformanceMonitoring: c<IFirebasePerformanceMonitoring>(),
        );
      })
      ..registerTrackingFactory<IdCardDetailBloc>(() {
        return DriverTestIdCardDetailBloc(
          biometricsRepository: c<IBiometricsRepository>(),
          localizationRepository: c<ILocalizationRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
          biometricsStorage: c<IBiometricsStorage>(),
          platformService: c<IPlatformService>(),
          trackingService: c<CappInnovatricsTrackingService>(),
          firebasePerformanceMonitoring: c<IFirebasePerformanceMonitoring>(),
        );
      });
  }

  Future<void> navigateToAdaProcessingScreen({required AdaProcessingArguments? routeArgs}) async {
    logger.i('Navigate to the ada processing screen.');
    unawaited(
      context.configuration.navigator?.pushFromPackage(
        package: 'CappLoanShared',
        screen: 'AdaProcessingScreen',
        arguments: routeArgs,
      ),
    );
    await tester.pumpAndSettle();
  }

  Future<void> navigateToLooAdaMain({LooAdaMainRouteArgs? routeArgs}) async {
    logger.i('Navigate to the loo ada main screen.');
    unawaited(
      context.configuration.navigator?.pushNamedAndRemoveUntilFromPackage(
        'CappRepayment',
        'LooAdaMainScreen',
        'CappHome',
        'MainScreen',
        arguments: routeArgs,
      ),
    );
    await tester.pumpAndSettle();
  }
}
