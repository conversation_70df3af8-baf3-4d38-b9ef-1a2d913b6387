import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:koyal_testing/integration_test/index.dart';

import '../../../../integration_page_objects/index.dart';
import '../../../../integration_tests_tools/tester_helper.dart';

void run(AppRunner appRunner) {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  group('Lock account Tests VN', () {
  
    integrationTest(
      'Lock account menu item test',
      (tester, context) async {
        final testerHelper = TesterHelper(tester: tester, context: context);
        final mainPage = MainTestPage(tester);
        final accountPage = AccountTestPage(tester);
        final lockPage = LockPageV2(tester);

        testerHelper
          ..setFakeRegisteredUserWithLoan()
          ..setMockBigDataService()
          ..forceFakeLoginStateAsRegistered();

        await testerHelper.navigateToHome();

        // open account tab
        await mainPage.bottomBarProfileButton.tap();

        // tap lock account menu item
        await accountPage.scrollToLockAccountRow();
        await tester.pumpAndSettle(const Duration(milliseconds: 200));
        await accountPage.lockAccountMenuItem.tap();
        await tester.pumpAndSettle(const Duration(milliseconds: 200));

        // arrive at lock page
        lockPage.isReady();
      },
      appRunner: appRunner,
    );
  });
}
