import 'package:flutter_driver/flutter_driver.dart';
import 'package:test/test.dart';

import '../page_objects/page_objects.dart';
import '../reuse_steps/common.dart';
import '../tools/retriable_test.dart';
import '../tools/screenshot_service.dart';
import '../tools/test_group.dart';

const timeWait = 2;

void main() {
  testGroup(
    'Home page Tests',
    (testContext) {
      late FlutterDriver driver;
      late ScreenshotService screenshotService;

      late OnboardingChooseLanguagePage onboardingPage;
      late OnboardingCarouselPage carouselPage;
      late HomeTestPage homePage;

      setUpAll(() async {
        driver = testContext.driver();
        screenshotService = testContext.screenshotService();

        onboardingPage = OnboardingChooseLanguagePage(driver: driver, screenshotService: screenshotService);
        carouselPage = OnboardingCarouselPage(driver: driver, screenshotService: screenshotService);
        homePage = HomeTestPage(driver: driver, screenshotService: screenshotService);
      });

      retriableTest(
        'Onboarding test',
        () async {
          await onBoardingEnglishLang(driver, onboardingPage, carouselPage);

          await carouselPage.tapContinueAsGuestButton();

          // delay for prevent error
          await Future.delayed(const Duration(seconds: 30), () {});

          expect(await homePage.isReady(), isTrue);

          await screenshotService.takeScreenshot('homescreen_onboarding');
        },
        timeout: const Timeout(Duration(minutes: 2)),
      );
    },
  );
}
