trigger:
  tags:
    include:
      - hotfix/in/3.*.*
      - hotfix/vn/3.*.*
      # - hotfix/ph/3.*.*
      # - hotfix/id/3.*.*
      - release/3.*.*
      - test/*

pool:
  vmImage: ubuntu-latest
  demands: CanBuild -equals Mobile
variables:
  - group: access_configuration
  - template: ../ci/variables.yml
  - template: ../ci/variables-prod.yml
  - name: APPBUDLE_GENERATED_PATH
    value: '$(System.DefaultWorkingDirectory)/$(CAPP_FOLDER)/build/app/outputs/bundle'
  - name: System.Debug
    value: true
stages:
  - stage: preprocessing
    jobs:
      - template: ../ci/preprocess-pubspec.yml
      - template: ../ci/preprocess-flutter.yml
      - template: ../ci/version-tag.yml

  - ${{ if or(eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/hotfix/in/'), true),eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/release/'), true)) }}:
      - stage: build_android_in
        dependsOn:
          - preprocessing
        variables:
          - name: version_buildname
            value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_SEMVER']]
          - name: version_buildnumber
            value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
        jobs:
          - template: ../ci/build-android.yml
            parameters:
              name: in_build_prod_android
              displayName: 'IN: Android Build'
              rootFolder: $(CAPP_FOLDER)
              keystoreName: $(PROD_IN_KEYSTORE_NAME)
              keystorePassword: $(ANDROID_DEMO_KEYSTORE_PASSWORD)
              keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
              entryPoint: $(PROD_ENTRY_POINT)
              flavor: $(PROD_IN_FLAVOR_NAME)
              appBundleGeneratedPath: $(APPBUDLE_GENERATED_PATH)
              resultAppBundleName: $(RESULT_PROD_IN_APPBUNDLE_NAME)
              resultApkName: $(RESULT_PROD_IN_APK_NAME)
              appBundleArtifactName: $(PROD_IN_APPBUNDLE_ARTIFACT_NAME)
              apkArtifactName: $(PROD_IN_APK_ARTIFACT_NAME)
              rootPackage: $(CAPP_PAKCAGE_NAME)
              continueOnError: true
              uploadBuild: true
              agentPool: ubuntu-22.04

    # - stage: build_ios_in
    #   dependsOn:
    #     - preprocessing
    #   variables:
    #     - name: version_buildname
    #       value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_MAJOR_MINOR_PATCH']]
    #     - name: version_buildnumber
    #       value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
    #   jobs:
    #     - template: ../ci/build-ios.yml
    #       parameters:
    #         name: in_build_prod_ios
    #         displayName: "IN: iOS Build"
    #         rootPackage: $(CAPP_PAKCAGE_NAME)
    #         rootFolder: $(CAPP_FOLDER)
    #         entryPoint: $(PROD_ENTRY_POINT)
    #         flavor: $(PROD_IN_FLAVOR_NAME)
    #         certificateFile: $(PROD_IN_CERTIFICATE_FILE)
    #         certificatePassword: $(IOS_CERTIFICATE_PASSWORD)
    #         provisioningProfileName: $(PROD_IN_PROVISIONING_PROFILE_NAME)
    #         exportOptionsPath: $(IN_EXPORT_OPTIONS_PATH)
    #         ipaArtifactName: $(PROD_IN_IPA_ARTIFACT_NAME)
    #         continueOnError: true
    #         uploadBuild: true

  - ${{ if or(eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/hotfix/vn/'), true),eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/release/'), true)) }}:
      - stage: build_android_vn
        dependsOn:
          - preprocessing
        variables:
          - name: version_buildname
            value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_SEMVER']]
          - name: version_buildnumber
            value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
        jobs:
          - template: ../ci/build-android.yml
            parameters:
              name: vn_build_prod_android
              displayName: 'VN: Android Build'
              rootFolder: $(CAPP_FOLDER)
              keystoreName: $(PROD_VN_KEYSTORE_NAME)
              keystorePassword: $(ANDROID_CAPP_VN_KEYSTORE_PASSWORD)
              keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
              entryPoint: $(PROD_ENTRY_POINT)
              flavor: $(PROD_VN_FLAVOR_NAME)
              appBundleGeneratedPath: $(APPBUDLE_GENERATED_PATH)
              resultAppBundleName: $(RESULT_PROD_VN_APPBUNDLE_NAME)
              resultApkName: $(RESULT_PROD_VN_APK_NAME)
              appBundleArtifactName: $(PROD_VN_APPBUNDLE_ARTIFACT_NAME)
              apkArtifactName: $(PROD_VN_APK_ARTIFACT_NAME)
              rootPackage: $(CAPP_PAKCAGE_NAME)
              continueOnError: true
              uploadBuild: true

      - stage: build_ios_vn
        dependsOn:
          - preprocessing
        variables:
          - name: version_buildname
            value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_MAJOR_MINOR_PATCH']]
          - name: version_buildnumber
            value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
        jobs:
          - template: ../ci/build-ios.yml
            parameters:
              name: vn_build_prod_ios
              displayName: 'VN: iOS Build'
              rootPackage: $(CAPP_PAKCAGE_NAME)
              rootFolder: $(CAPP_FOLDER)
              entryPoint: $(PROD_ENTRY_POINT)
              flavor: $(PROD_VN_FLAVOR_NAME)
              certificateFile: $(PROD_VN_CERTIFICATE_FILE)
              certificatePassword: $(VN_IOS_CERTIFICATE_PASSWORD)
              provisioningProfileName: $(PROD_VN_PROVISIONING_PROFILE_NAME)
              extensionProvisioningProfileName: $(PROD_VN_EXTENSION_PROVISIONING_PROFILE_NAME)
              moeExtensionProvisioningProfileName: $(PROD_VN_MOE_EXTENSION_PROVISIONING_PROFILE_NAME)
              moeRichExtensionProvisioningProfileName: $(PROD_VN_MOE_RICH_EXTENSION_PROVISIONING_PROFILE_NAME)
              liveActivitiesExtensionProvisioningProfileName: $(PROD_VN_LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE_NAME)
              exportOptionsPath: $(VN_EXPORT_OPTIONS_PATH)
              ipaArtifactName: $(PROD_VN_IPA_ARTIFACT_NAME)
              continueOnError: true
              uploadBuild: true

  # - ${{ if or(eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/hotfix/ph/'), true),eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/release/'), true)) }}:
  #     - stage: build_android_ph
  #       dependsOn:
  #         - preprocessing
  #       variables:
  #         - name: version_buildname
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_SEMVER']]
  #         - name: version_buildnumber
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
  #       jobs:
  #         - template: ../ci/build-android.yml
  #           parameters:
  #             name: ph_build_prod_android
  #             displayName: 'PH: Android Build'
  #             rootFolder: $(CAPP_FOLDER)
  #             keystoreName: $(PROD_PH_KEYSTORE_NAME)
  #             keystorePassword: $(ANDROID_CAPP_PH_KEYSTORE_PASSWORD)
  #             keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
  #             entryPoint: $(PROD_ENTRY_POINT)
  #             flavor: $(PROD_PH_FLAVOR_NAME)
  #             appBundleGeneratedPath: $(APPBUDLE_GENERATED_PATH)
  #             resultAppBundleName: $(RESULT_PROD_PH_APPBUNDLE_NAME)
  #             resultApkName: $(RESULT_PROD_PH_APK_NAME)
  #             appBundleArtifactName: $(PROD_PH_APPBUNDLE_ARTIFACT_NAME)
  #             apkArtifactName: $(PROD_PH_APK_ARTIFACT_NAME)
  #             rootPackage: $(CAPP_PAKCAGE_NAME)
  #             continueOnError: true
  #             uploadBuild: true

  #     - stage: build_huawei_ph
  #       dependsOn:
  #         - preprocessing
  #       variables:
  #         - name: version_buildname
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_SEMVER']]
  #         - name: version_buildnumber
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
  #       jobs:
  #         - template: ../ci/build-huawei.yml
  #           parameters:
  #             name: ph_build_prod_huawei
  #             displayName: 'PH: Huawei Build'
  #             rootFolder: $(CAPP_FOLDER)
  #             keystoreName: $(PROD_PH_KEYSTORE_NAME)
  #             keystorePassword: $(ANDROID_CAPP_PH_KEYSTORE_PASSWORD)
  #             keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
  #             entryPoint: $(PROD_ENTRY_POINT)
  #             flavor: $(PROD_PH_HUAWEI_FLAVOR_NAME)
  #             appBundleGeneratedPath: $(APPBUDLE_GENERATED_PATH)
  #             resultAppBundleName: $(RESULT_PROD_PH_HUAWEI_APPBUNDLE_NAME)
  #             resultApkName: $(RESULT_PROD_PH_HUAWEI_APK_NAME)
  #             appBundleArtifactName: $(PROD_PH_HUAWEI_APPBUNDLE_ARTIFACT_NAME)
  #             apkArtifactName: $(PROD_PH_HUAWEI_APK_ARTIFACT_NAME)
  #             rootPackage: $(CAPP_PAKCAGE_NAME)
  #             continueOnError: true
  #             uploadBuild: true

  #     - stage: build_ios_ph
  #       dependsOn:
  #         - preprocessing
  #       variables:
  #         - name: version_buildname
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_MAJOR_MINOR_PATCH']]
  #         - name: version_buildnumber
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
  #       jobs:
  #         - template: ../ci/build-ios.yml
  #           parameters:
  #             name: ph_build_prod_ios
  #             displayName: 'PH: iOS Build'
  #             rootPackage: $(CAPP_PAKCAGE_NAME)
  #             rootFolder: $(CAPP_FOLDER)
  #             entryPoint: $(PROD_ENTRY_POINT)
  #             flavor: $(PROD_PH_FLAVOR_NAME)
  #             certificateFile: $(PROD_PH_CERTIFICATE_FILE)
  #             certificatePassword: $(PH_IOS_CERTIFICATE_PASSWORD)
  #             provisioningProfileName: $(PROD_PH_PROVISIONING_PROFILE_NAME)
  #             extensionProvisioningProfileName: $(PROD_PH_EXTENSION_PROVISIONING_PROFILE_NAME)
  #             moeExtensionProvisioningProfileName: $(PROD_PH_MOE_EXTENSION_PROVISIONING_PROFILE_NAME)
  #             moeRichExtensionProvisioningProfileName: $(PROD_PH_MOE_RICH_EXTENSION_PROVISIONING_PROFILE_NAME)
  #             exportOptionsPath: $(PH_EXPORT_OPTIONS_PATH)
  #             ipaArtifactName: $(PROD_PH_IPA_ARTIFACT_NAME)
  #             continueOnError: true
  #             uploadBuild: true

  # - ${{ if or(eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/hotfix/id/'), true),eq(startsWith(variables['Build.SourceBranch'], 'refs/tags/release/'), true)) }}:
  #     - stage: build_android_id
  #       dependsOn:
  #         - preprocessing
  #       variables:
  #         - name: version_buildname
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_SEMVER']]
  #         - name: version_buildnumber
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
  #       jobs:
  #         - template: ../ci/build-android.yml
  #           parameters:
  #             name: id_build_prod_android
  #             displayName: 'ID: Android Build'
  #             rootFolder: $(CAPP_FOLDER)
  #             keystoreName: $(PROD_ID_KEYSTORE_NAME)
  #             keystorePassword: $(ANDROID_CAPP_ID_KEYSTORE_PASSWORD)
  #             keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
  #             entryPoint: $(PROD_ENTRY_POINT)
  #             flavor: $(PROD_ID_FLAVOR_NAME)
  #             appBundleGeneratedPath: $(APPBUDLE_GENERATED_PATH)
  #             resultAppBundleName: $(RESULT_PROD_ID_APPBUNDLE_NAME)
  #             resultApkName: $(RESULT_PROD_ID_APK_NAME)
  #             appBundleArtifactName: $(PROD_ID_APPBUNDLE_ARTIFACT_NAME)
  #             apkArtifactName: $(PROD_ID_APK_ARTIFACT_NAME)
  #             rootPackage: $(CAPP_PAKCAGE_NAME)
  #             continueOnError: true
  #             uploadBuild: true

  #     - stage: build_ios_id
  #       dependsOn:
  #         - preprocessing
  #       variables:
  #         - name: version_buildname
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_MAJOR_MINOR_PATCH']]
  #         - name: version_buildnumber
  #           value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
  #       jobs:
  #         - template: ../ci/build-ios.yml
  #           parameters:
  #             name: id_build_prod_ios
  #             displayName: 'ID: iOS Build'
  #             rootPackage: $(CAPP_PAKCAGE_NAME)
  #             rootFolder: $(CAPP_FOLDER)
  #             entryPoint: $(PROD_ENTRY_POINT)
  #             flavor: $(PROD_ID_FLAVOR_NAME)

  #             certificateFile: $(PROD_ID_CERTIFICATE_FILE)
  #             certificatePassword: $(ID_IOS_CERTIFICATE_PASSWORD)
  #             exportOptionsPath: $(ID_EXPORT_OPTIONS_PATH)
  #             provisioningProfileName: $(PROD_ID_PROVISIONING_PROFILE_NAME)
  #             extensionProvisioningProfileName: $(PROD_ID_EXTENSION_PROVISIONING_PROFILE_NAME)
  #             moeExtensionProvisioningProfileName: $(PROD_ID_MOE_EXTENSION_PROVISIONING_PROFILE_NAME)
  #             moeRichExtensionProvisioningProfileName: $(PROD_ID_MOE_RICH_EXTENSION_PROVISIONING_PROFILE_NAME)
  #             ipaArtifactName: $(PROD_ID_IPA_ARTIFACT_NAME)
  #             continueOnError: true
  #             uploadBuild: true

  - stage: scan_apks
    condition: always()
    variables:
      - name: version_buildnumber
        value: $[stageDependencies.preprocessing.get_version.outputs['Outputs.VERSION_BUILD_NUMBER']]
    jobs:
      - template: ../ci/apk-scanner.yml
        parameters:
          name: scan_all_apks
          displayName: Scan all apk permissions
          inApkName: $(PROD_IN_APK_ARTIFACT_NAME)
          # phApkName: $(PROD_PH_APK_ARTIFACT_NAME)
          vnApkName: $(PROD_VN_APK_ARTIFACT_NAME)
          # idApkName: $(PROD_ID_APK_ARTIFACT_NAME)
          resultApkNameVn: $(RESULT_PROD_VN_APK_NAME)
          # resultApkNamePh: $(RESULT_PROD_PH_APK_NAME)
          # resultApkNameId: $(RESULT_PROD_ID_APK_NAME)
          resultApkNameIn: $(RESULT_PROD_IN_APK_NAME)