parameters:
  - name: prodFlavorName
    type: string
  - name: testAppPath
    type: string
    default: ""
  - name: newTestAppPath
    type: string
    default: ""

variables:
  - group: DevOpsAutomation
  - template: ../ci/variables.yml
  - template: ../ci/variables-prod.yml
  - name: revision
    value: $[counter(format('{0:dd}', pipeline.startTime), 1)]
  - name: targetBranchName
    ${{ if and(eq( variables['Build.Reason'], 'PullRequest' ), eq(variables['System.PullRequest.TargetBranch'],'refs/heads/master')) }}:
      value: "master"
    ${{ if and(eq( variables['Build.Reason'], 'PullRequest' ), eq(variables['System.PullRequest.TargetBranch'],'refs/heads/uat')) }}:
      value: "uat"
    ${{ else }}:
      value: "develop"
  - name: enableUpdateVersionInTask
    ${{ if and(eq( variables['Build.Reason'], 'PullRequest' ), eq(variables['System.PullRequest.TargetBranch'],'refs/heads/develop'), eq(variables['Build.SourceBranch'], 'refs/heads/uat')) }}:
      value: false
    ${{ else }}:
      value: true
stages:
  - stage: preprocessing
    jobs:
      - template: ../ci/preprocess-pubspec.yml
      - template: ../ci/preprocess-flutter.yml
  
  - stage: test
    dependsOn: preprocessing
    jobs:
     - template: ../ci/integration-tests-new.yml
       parameters:
         displayName: "New Integration tests prod"
         flavor: ${{ parameters.prodFlavorName }}
         testPath: "test_driver/integration_test.dart"
         testAppPath: ${{ parameters.newTestAppPath }}
         name: flutter_integration_tests_prod
         rootFolder: $(ROOT_FOLDER)
         rootPackage: $(ROOT_PACKAGE)
     
  - stage: publish_code_coverage
    condition: and(succeeded(),eq(variables['Build.Reason'],'PullRequest'))
    jobs:
      - template: ../ci/publish-code-coverage.yml

  - stage: GenerateVersion
    condition: and(succeeded(), or(eq(variables['Build.Reason'],'PullRequest'), in(variables['Build.SourceBranch'], 'refs/heads/develop', 'refs/heads/uat','refs/heads/master')))
    dependsOn: preprocessing
    variables:
      - template: ../ci/variables.yml
    jobs:
      - template: ../ci/version.yml