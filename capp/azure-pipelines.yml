# cSpell:disable
# schedules:
#   - cron: "0 0,12 * * *"
#     displayName: Daily 2AM/2PM (0, 12 UTC) build
#     branches:
#       include:
#         - developschedules: no
pr: none

trigger:
  branches:
    exclude:
      - /*

  paths:
    exclude:
      - capp/README.md
      - capp/CONTRIBUTING.md
      - capp/test_driver/README.md

pool:
  vmImage: ubuntu-latest
  demands: CanBuild -equals Mobile

variables:
  # New variables for GMAT
  ROOT_PACKAGE: 'self_care'

  # use Glob pattern to match all files from root of project
  GLOB_PACKAGES_SEARCH_PATH_KOYAL: 'packages/koyal_**,storybook'
  GLOB_PACKAGES_SEARCH_PATH_CAPP_A_D: 'packages/capp_[a-d]**'
  GLOB_PACKAGES_SEARCH_PATH_CAPP_E_L: 'packages/capp_[e-l]**'
  GLOB_PACKAGES_SEARCH_PATH_CAPP_M_Z: 'packages/capp_[m-z]**'
  G<PERSON><PERSON><PERSON>_PACKAGES_SEARCH_PATH: 'packages/capp_**,packages/koyal_**,packages/gma_**,storybook'
  #
  ROOT_FOLDER: 'capp'

  PACKAGES_SEARCH_PATH_KOYAL: 'koyal_*/ ../storybook/'
  PACKAGES_SEARCH_PATH_CAPP_A_D: 'capp_[a-d]*/'
  PACKAGES_SEARCH_PATH_CAPP_E_L: 'capp_[e-l]*/'
  PACKAGES_SEARCH_PATH_CAPP_M_Z: 'capp_[m-z]*/'
  PACKAGES_SEARCH_PATH: 'capp_*/ koyal_*/ ../storybook/'
  RESULT_PROD_IN_APPBUNDLE_NAME: 'self-care.aab'
  RESULT_PROD_VN_APPBUNDLE_NAME: 'prod-vn.aab'
  RESULT_PROD_PH_HUAWEI_APPBUNDLE_NAME: 'prod-ph-huawei.aab'
  RESULT_PROD_IN_APK_NAME: 'prod-in.apk'
  RESULT_PROD_VN_APK_NAME: 'prod-vn.apk'
  RESULT_PROD_PH_HUAWEI_APK_NAME: 'prod-ph-huawei.apk'
  PROD_IN_APPBUNDLE_ARTIFACT_NAME: 'self-care-android'
  PROD_VN_APPBUNDLE_ARTIFACT_NAME: 'capp-vn-android'
  PROD_IN_APK_ARTIFACT_NAME: 'self-care-android-apk'
  PROD_VN_APK_ARTIFACT_NAME: 'capp-vn-android-apk'
  PROD_IN_PROVISIONING_PROFILE_NAME: 'Capp.mobileprovision'
  PROD_VN_PROVISIONING_PROFILE_NAME: 'VNSuperCupo2024.mobileprovision'
  PROD_ENTRY_POINT: 'lib/main_prod.dart'
  PROD_IN_KEYSTORE_NAME: 'self-care.keystore'
  PROD_VN_KEYSTORE_NAME: 'capp-vn.keystore'
  APPBUDLE_GENERATED_PATH: '$(System.DefaultWorkingDirectory)/$(ROOT_FOLDER)/build/app/outputs/bundle'
  PROD_IN_IPA_ARTIFACT_NAME: 'demo-ipa-file'
  PROD_VN_IPA_ARTIFACT_NAME: 'prod-vn-ipa-file'
  PROD_IN_CERTIFICATE_FILE: 'IndiaAppleDistribution.p12'
  PROD_VN_CERTIFICATE_FILE: 'VNAppleDistribution2024.p12'
  IN_EXPORT_OPTIONS_PATH: 'ios/exportOptionsIN.plist'
  VN_EXPORT_OPTIONS_PATH: 'ios/exportOptionsVN.plist'
  ${{ if eq( variables['Build.Reason'], 'PullRequest' ) }}:
    BUILD_APPS_IN_TEST: true
  ${{ if ne( variables['Build.Reason'], 'PullRequest' ) }}:
    BUILD_APPS_IN_TEST: false

stages:
  - stage: preprocessing
    jobs:
      - template: ../ci/preprocess-pubspec.yml
      - template: ../ci/preprocess-flutter.yml

  - stage: test
    dependsOn: preprocessing
    variables:
      - template: ../ci/variables.yml
    jobs:
      - template: ../ci/test-with-packages.yml
        parameters:
          name: in_flutter_tests_koyal
          displayName: 'IN Flutter KOYAL'

          dependencyChecker: true
          rootFolder: $(ROOT_FOLDER)
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: $(GLOB_PACKAGES_SEARCH_PATH_KOYAL)
          searchPath: $(PACKAGES_SEARCH_PATH_KOYAL)
          coverage_artifact_name: 'coverage_koyal'
          results_artifact_name: 'results_koyal'
      - template: ../ci/test-with-packages.yml
        parameters:
          name: in_flutter_tests_capp_a_d
          rootFolder: $(ROOT_FOLDER)
          displayName: 'IN Flutter Test CAPP A-D'

          dependencyChecker: true
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: $(GLOB_PACKAGES_SEARCH_PATH_CAPP_A_D)
          searchPath: $(PACKAGES_SEARCH_PATH_CAPP_A_D)
          coverage_artifact_name: 'coverage_capp_a_d'
          results_artifact_name: 'results_capp_a_d'
      - template: ../ci/test-with-packages.yml
        parameters:
          name: in_flutter_tests_capp_e_l
          rootFolder: $(ROOT_FOLDER)
          displayName: 'IN Flutter Test CAPP E-L'

          dependencyChecker: true
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: $(GLOB_PACKAGES_SEARCH_PATH_CAPP_E_L)
          searchPath: $(PACKAGES_SEARCH_PATH_CAPP_E_L)
          coverage_artifact_name: 'coverage_capp_e_l'
          results_artifact_name: 'results_capp_e_l'
      - template: ../ci/test-with-packages.yml
        parameters:
          name: in_flutter_tests_capp_m_z
          displayName: 'IN Flutter Test CAPP M-Z'

          dependencyChecker: true
          rootFolder: $(ROOT_FOLDER)
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: $(GLOB_PACKAGES_SEARCH_PATH_CAPP_M_Z)
          searchPath: $(PACKAGES_SEARCH_PATH_CAPP_M_Z)
          coverage_artifact_name: 'coverage_capp_m_z'
          results_artifact_name: 'results_capp_m_z'
      - template: ../ci/analyze-with-packages.yml
        parameters:
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: $(GLOB_PACKAGES_SEARCH_PATH)
          rootFolder: $(ROOT_FOLDER)
          searchPath: $(PACKAGES_SEARCH_PATH)
      - template: ../ci/analyze-code-quality-with-packages.yml
        parameters:
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: 'packages/capp_[a-g]**'
          rootFolder: $(ROOT_FOLDER)
          searchPath: capp_[a-g]*/
          name: capp_a_g_code_quality_analyze_with_packages
      - template: ../ci/analyze-code-quality-with-packages.yml
        parameters:
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: 'packages/capp_[h-z]**'
          rootFolder: $(ROOT_FOLDER)
          searchPath: capp_[h-z]*/
          name: capp_h_z_code_quality_analyze_with_packages
      - template: ../ci/analyze-code-quality-with-packages.yml
        parameters:
          rootPackage: $(ROOT_PACKAGE)
          searchGlob: 'packages/koyal_**'
          rootFolder: $(ROOT_FOLDER)
          searchPath: koyal_*/
          name: koyal_code_quality_analyze_with_packages
      - template: ../ci/integration-tests.yml
        parameters:
          name: flutter_integration_tests
          rootFolder: $(ROOT_FOLDER)
          testAppPath: 'test_driver/integration_fake_in.dart'
          testPath: 'test_driver/all_tests_fake_test.dart'
      - template: ../ci/integration-tests-new.yml
        parameters:
          displayName: 'Integration tests IN fake'
          flavor: 'fakein'
          testPath: 'test_driver/integration_test.dart'
          testAppPath: 'test_driver/integration_tests_fake_in.dart'
          name: flutter_integration_tests_fake_in
          rootFolder: $(ROOT_FOLDER)
      - template: ../ci/integration-tests-new.yml
        parameters:
          displayName: 'Integration tests VN fake'
          flavor: 'fakevn'
          testPath: 'test_driver/integration_test.dart'
          testAppPath: 'test_driver/integration_tests_fake_vn.dart'
          name: flutter_integration_tests_fake_vn
          rootFolder: $(ROOT_FOLDER)

  - stage: publish_code_coverage
    jobs:
      - template: ../ci/publish-code-coverage.yml

  - stage: version
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
    variables:
      - template: ../ci/variables.yml
    jobs:
      - template: ../ci/version.yml

  - stage: translations
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
    variables:
      - template: ../ci/variables.yml
    jobs:
      - template: ../ci/localizations_processor/send-texts-to-bapp.yml
        parameters:
          appName: $(ROOT_FOLDER)
          majorVersion: $[stageDependencies.version.get_version.outputs['Outputs.VERSION_MAJOR']]
          appVersion: $[stageDependencies.version.get_version.outputs['Outputs.VERSION_MAJOR_MINOR_PATCH']]
          repoName: $(Build.Repository.Name)

  - stage: build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
    variables:
      - template: ../ci/variables.yml
    jobs:
      - template: ../ci/build-android.yml
        parameters:
          name: build_prod_in_android
          displayName: 'Android IN Build'
          rootFolder: $(ROOT_FOLDER)
          keystoreName: $(PROD_IN_KEYSTORE_NAME)
          keystorePassword: $(ANDROID_DEMO_KEYSTORE_PASSWORD)
          keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
          entryPoint: $(PROD_ENTRY_POINT)
          flavor: $(PROD_IN_FLAVOR_NAME)
          appBundleGeneratedPath: $(APPBUDLE_GENERATED_PATH)
          resultAppBundleName: $(RESULT_PROD_IN_APPBUNDLE_NAME)
          resultApkName: $(RESULT_PROD_IN_APK_NAME)
          appBundleArtifactName: $(PROD_IN_APPBUNDLE_ARTIFACT_NAME)
          apkArtifactName: $(PROD_IN_APK_ARTIFACT_NAME)
          rootPackage: $(ROOT_PACKAGE)
          agentPool: ubuntu-22.04

      - template: ../ci/build-ios.yml
        parameters:
          name: build_prod_in_ios
          displayName: 'iOS IN Build'
          rootFolder: $(ROOT_FOLDER)
          certificatePassword: $(IOS_CERTIFICATE_PASSWORD)
          provisioningProfileName: $(PROD_IN_PROVISIONING_PROFILE_NAME)
          entryPoint: $(PROD_ENTRY_POINT)
          flavor: $(PROD_IN_FLAVOR_NAME)
          ipaArtifactName: $(PROD_IN_IPA_ARTIFACT_NAME)
          certificateFile: $(PROD_IN_CERTIFICATE_FILE)
          exportOptionsPath: $(IN_EXPORT_OPTIONS_PATH)
          rootPackage: $(ROOT_PACKAGE)

      - template: ../ci/build-android.yml
        parameters:
          name: build_prod_vn_android
          displayName: 'Android VN Build'
          rootFolder: $(ROOT_FOLDER)
          keystoreName: $(PROD_VN_KEYSTORE_NAME)
          keystorePassword: $(ANDROID_CAPP_VN_KEYSTORE_PASSWORD)
          keystoreAlias: $(ANDROID_KEYSTORE_ALIAS)
          entryPoint: $(PROD_ENTRY_POINT)
          flavor: $(PROD_VN_FLAVOR_NAME)
          appBundleGeneratedPath: $(APPBUDLE_GENERATED_PATH)
          resultAppBundleName: $(RESULT_PROD_VN_APPBUNDLE_NAME)
          resultApkName: $(RESULT_PROD_VN_APK_NAME)
          appBundleArtifactName: $(PROD_VN_APPBUNDLE_ARTIFACT_NAME)
          apkArtifactName: $(PROD_VN_APK_ARTIFACT_NAME)
          rootPackage: $(ROOT_PACKAGE)

      - template: ../ci/build-ios.yml
        parameters:
          name: build_prod_vn_ios
          displayName: 'iOS VN Build'
          rootFolder: $(ROOT_FOLDER)
          certificatePassword: $(VN_IOS_CERTIFICATE_PASSWORD)
          provisioningProfileName: $(PROD_VN_PROVISIONING_PROFILE_NAME)
          moeExtensionProvisioningProfileName: $(PROD_VN_MOE_EXTENSION_PROVISIONING_PROFILE_NAME)
          liveActivitiesExtensionProvisioningProfileName: $(PROD_VN_LIVE_ACTIVITIES_EXTENSION_PROVISIONING_PROFILE_NAME)
          entryPoint: $(PROD_ENTRY_POINT)
          flavor: $(PROD_VN_FLAVOR_NAME)
          ipaArtifactName: $(PROD_VN_IPA_ARTIFACT_NAME)
          certificateFile: $(PROD_VN_CERTIFICATE_FILE)
          exportOptionsPath: $(VN_EXPORT_OPTIONS_PATH)
          rootPackage: $(ROOT_PACKAGE)