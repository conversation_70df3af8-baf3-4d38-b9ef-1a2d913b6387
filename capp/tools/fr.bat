@echo off
cmd /c cd ..
cmd /c RMDIR .dart_tools /s
cmd /c cls
color 1b
echo ===================================
echo ==           CLOSE IDE           ==
echo ===================================
echo 1/5 - START CLEARING PROJECT
echo ===================================
color 0A
cmd /c flutter clean

cmd /c cls
echo ===================================
echo ==    2/5 - REPAIR PUB CACHE     ==
echo ===================================
color 0A
cmd /c flutter pub cache repair

cmd /c cls
echo ===================================
echo ==     3/5 - GET DEPENDENCIES    ==
echo ===================================
color 0A
cmd /c flutter pub get

cmd /c cls
echo ===================================
echo ==         4/5 - CLEAN           ==
echo ===================================
color 0A
REM cmd /c fbc.bat

cmd /c cls
echo ===================================
echo ==         5/5 - REBUILD         ==
echo ===================================
color 0A
cmd /c fbb.bat

cmd /c cls
echo ===================================
echo ==      FINISHED, OPEN IDE       ==
echo ==        .. and pray ..         ==
echo ===================================
color 0A