clear
rm -rf .dart_tool

echo ===================================
echo ==           CLOSE IDE           ==
echo ===================================
echo 1/5 - START CLEARING PROJECT
echo ===================================

flutter clean

echo ===================================
echo ==    2/5 - REPAIR PUB CACHE     ==
echo ===================================

flutter pub cache repair

echo ===================================
echo ==     3/5 - GET DEPENDENCIES    ==
echo ===================================

flutter pub get

echo ===================================
echo ==         4/5 - CLEAN           ==
echo ===================================

#flutter pub run build_runner clean

echo ===================================
echo ==         5/5 - REBUILD         ==
echo ===================================

flutter pub run build_runner build --delete-conflicting-outputs

echo ===================================
echo ==      FINISHED, OPEN IDE       ==
echo ==        .. and pray ..         ==
echo ===================================
