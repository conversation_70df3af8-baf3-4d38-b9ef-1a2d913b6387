// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

@Tags(['integration'])
import '../../../../test_driver/capp_vn/integration_tests_tools/integration_tests_vn.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './step/i_am_on_application_form_general_renderer.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_scroll_to_housing_widget.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_click_the_housing_widget.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_see_the_housing_page.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_continue_button_in_housing_page.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_owned.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_see_application_form_general_renderer.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_rented.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_shared.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_rented_shared.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_parental.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_provided.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_paying_guest.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_hostel.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_other.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_mortgage.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_relatives.dart';
import '../../../integration_reuse_steps/loan_origination_unified/step/i_tap_to_dormitory.dart';

void main() {
  group('''Housing tests''', () {
    integrationTestFakeVn('''Open housing page and tap continue button''', (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeTheHousingPage(tester, context);
    });
    integrationTestFakeVn('''Select owned in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToOwned(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select rented in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToRented(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select shared in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToShared(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select rented shared in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToRentedShared(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select parental in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToParental(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select provided in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToProvided(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select paying guest in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToPayingGuest(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select hostel in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToHostel(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select other in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToOther(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select mortgage in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToMortgage(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select relatives in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToRelatives(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
    integrationTestFakeVn('''Select dormitory in housing page and go back to application form general renderer''',
        (tester, context) async {
      await iAmOnApplicationFormGeneralRenderer(tester, context);
      await iScrollToHousingWidget(tester, context);
      await iClickTheHousingWidget(tester, context);
      await iSeeTheHousingPage(tester, context);
      await iTapToDormitory(tester, context);
      await iTapContinueButtonInHousingPage(tester, context);
      await iSeeApplicationFormGeneralRenderer(tester, context);
    });
  });
}
