import '../../../../test_driver/capp_vn/integration_tests_tools/integration_tests_vn.dart';

@integration
@testMethodName: integrationTestFakeVn
@testerName: tester, context
@testerType: WidgetTester, TestContext
Feature: Marial status tests
    Scenario: Select single in marial status page and stay on marital status
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to single status 
        And I tap continue button in marial page 
        Then I see the marital status page

    Scenario: Select married in marial status page and stay on marital status
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to married status 
        And I tap continue button in marial page 
        Then I see the marital status page
        
    Scenario: Select live in partner in marial status page and stay on marital status
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to live in partner status 
        And I tap continue button in marial page 
        Then I see the marital status page

    Scenario: Select separated in marial status page and stay on marital status
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to separated status 
        And I tap continue button in marial page 
        Then I see the marital status page

    Scenario: Select divorced in marial status page and stay on marital status
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to divorced status 
        And I tap continue button in marial page 
        Then I see the marital status page

    Scenario: Select widowed in marial status page and stay on marital status
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to widowed status 
        And I tap continue button in marial page 
        Then I see the marital status page
    
    Scenario: Select widowed and enter 1 number of dependents in marial status page and go back to application form general renderer
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to widowed status
        And I enter number into number dependants input field
        And I tap continue button in marial page 
        Then I see application form general renderer

    Scenario: Select widowed and enter 31 number of dependents in marial status page and go back to application form general renderer
        Given I am on application form general renderer
        When I scroll to marital status widget
        And I click the marital status widget
        And I see the marital status page
        And I tap to widowed status
        And I enter number into number dependants input field
        And I tap continue button in marial page 
        Then I see application form general renderer

        
        

  