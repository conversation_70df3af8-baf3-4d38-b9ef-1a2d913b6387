import 'package:flutter_test/flutter_test.dart';

import 'package:koyal_testing/integration_test/test_context.dart';

import '../../../../test_driver/integration_page_objects/loan_origination/housing_page.dart';

/// Usage: I scroll to rented shared
Future<void> iScrollToRentedShared(WidgetTester tester, TestContext context) async {
  final housingPage = HousingPage(tester);
  await housingPage.scrollToRentedShared();
}
