import 'dart:async';

import 'package:digio_core/digio_core.dart';
import 'package:flutter/services.dart';

import 'index.dart' as digio;

class <PERSON><PERSON> extends IDigio {
  Digio._privateConstructor();

  static final Digio _instance = Digio._privateConstructor();

  static Digio get instance => _instance;

  @override
  Future<DigioResult> initiateKyc({
    required DigioConfig config,
  }) async {
    try {
      final mdigioConfig = digio.DigioConfig();
      mdigioConfig.theme.primaryColor = config.primaryHexColor;
      mdigioConfig.theme.secondaryColor = config.primaryHexColor;
      //mdigioConfig.logo = "https://www.gstatic.com/mobilesdk/160503_mobilesdk/logo/2x/firebase_28dp.png";
      mdigioConfig.environment = config.isProduction ? digio.Environment.PRODUCTION : digio.Environment.SANDBOX;
      final kycWorkflowPlugin = digio.KycWorkflow(mdigioConfig);
      final workflowResult = await kycWorkflowPlugin.start(
        config.kIDDigiLocker ?? '',
        config.identifierDigiLocker ?? '',
        config.tokenIdDigiLocker ?? '',
        'digio_kyc',
        null,
      );

      print('workflowResult : $workflowResult');
      if (workflowResult.message == 'Success') {
        return DigioResult.digiLockerSuccess(workflowResult.toString());
      } else {
        return DigioResult.error(workflowResult.errorCode ?? 1002, workflowResult.message ?? 'KYC Failed');
      }
    } on PlatformException {
      return const DigioResult.error(9999, 'Failed to get platform version.');
    }
  }
}
