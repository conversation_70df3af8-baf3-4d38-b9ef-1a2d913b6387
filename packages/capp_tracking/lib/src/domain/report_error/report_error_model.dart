import 'package:json_annotation/json_annotation.dart';

part 'report_error_model.g.dart';

@JsonSerializable()
class ReportErrorModel {
  final DateTime createdAt;
  final List<ReportErrorItem> apiCalls;
  final String? cuid;
  final String? muid;
  final String appVersion;
  final String? userName;

  ReportErrorModel({
    required this.cuid,
    required this.muid,
    required this.appVersion,
    required this.userName,
    required this.createdAt,
    required this.apiCalls,
  });

  factory ReportErrorModel.fromJson(Map<String, dynamic> json) => _$ReportErrorModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReportErrorModelToJson(this);
}

@JsonSerializable()
class ReportErrorItem {
  final DateTime createdTime;
  final String method;
  final String server;
  final String endpoint;
  final String client;
  final Map<String, dynamic>? requestHeaders;
  final dynamic requestBody;
  final int? responseCode;
  final Map<String, dynamic>? responseHeaders;
  final dynamic responseBody;

  ReportErrorItem({
    required this.createdTime,
    required this.method,
    required this.server,
    required this.endpoint,
    required this.client,
    required this.requestHeaders,
    required this.requestBody,
    required this.responseCode,
    required this.responseHeaders,
    required this.responseBody,
  });

  factory ReportErrorItem.fromJson(Map<String, dynamic> json) => _$ReportErrorItemFromJson(json);

  Map<String, dynamic> toJson() => _$ReportErrorItemToJson(this);
}
