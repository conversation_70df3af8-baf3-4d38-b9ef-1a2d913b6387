import 'package:alice/model/alice_http_call.dart';

import 'report_error_model.dart';

extension AlioceMapperExtension on AliceHttpCall {
  ReportErrorItem toDomain() {
    return ReportErrorItem(
      createdTime: createdTime,
      method: method,
      server: server,
      endpoint: endpoint,
      client: client,
      requestHeaders: request?.headers,
      requestBody: request?.body,
      responseCode: response?.status,
      responseHeaders: response?.headers,
      responseBody: response?.body,
    );
  }
}
