// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_error_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReportErrorModel _$ReportErrorModelFromJson(Map<String, dynamic> json) =>
    ReportErrorModel(
      cuid: json['cuid'] as String?,
      muid: json['muid'] as String?,
      appVersion: json['appVersion'] as String,
      userName: json['userName'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      apiCalls: (json['apiCalls'] as List<dynamic>)
          .map((e) => ReportErrorItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ReportErrorModelToJson(ReportErrorModel instance) =>
    <String, dynamic>{
      'createdAt': instance.createdAt.toIso8601String(),
      'apiCalls': instance.apiCalls,
      'cuid': instance.cuid,
      'muid': instance.muid,
      'appVersion': instance.appVersion,
      'userName': instance.userName,
    };

ReportErrorItem _$ReportErrorItemFromJson(Map<String, dynamic> json) =>
    ReportErrorItem(
      createdTime: DateTime.parse(json['createdTime'] as String),
      method: json['method'] as String,
      server: json['server'] as String,
      endpoint: json['endpoint'] as String,
      client: json['client'] as String,
      requestHeaders: json['requestHeaders'] as Map<String, dynamic>?,
      requestBody: json['requestBody'],
      responseCode: json['responseCode'] as int?,
      responseHeaders: json['responseHeaders'] as Map<String, dynamic>?,
      responseBody: json['responseBody'],
    );

Map<String, dynamic> _$ReportErrorItemToJson(ReportErrorItem instance) =>
    <String, dynamic>{
      'createdTime': instance.createdTime.toIso8601String(),
      'method': instance.method,
      'server': instance.server,
      'endpoint': instance.endpoint,
      'client': instance.client,
      'requestHeaders': instance.requestHeaders,
      'requestBody': instance.requestBody,
      'responseCode': instance.responseCode,
      'responseHeaders': instance.responseHeaders,
      'responseBody': instance.responseBody,
    };
