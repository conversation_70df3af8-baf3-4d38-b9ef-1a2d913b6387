// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';

part 'event_data.g.dart';

@JsonSerializable(
  anyMap: true,
  createToJson: true,
  explicitToJson: true,
)
class BulkEventData {
  BulkEventData({
    required this.events,
  });
  @JsonKey(name: 'events', defaultValue: <EventData>[])
  final List<EventData> events;

  @override
  String toString() => 'BulkEventData(events: $events)';

  Map<String, dynamic> toJson() => _$BulkEventDataToJson(this);

  factory BulkEventData.fromJson(Map<String, dynamic> json) {
    return _$BulkEventDataFromJson(json);
  }
}

@JsonSerializable(
  anyMap: true,
  createToJson: true,
  explicitToJson: true,
)
class EventData {
  final String eventName;
  final DateTime timestamp;
  final String? category;
  final String? label;
  final Map<String, Object>? properties;
  final Map<String, Object>? userProperties;

  EventData({
    required this.eventName,
    required this.timestamp,
    this.category,
    this.label,
    this.properties,
    this.userProperties,
  });

  factory EventData.fromJson(Map<String, dynamic> json) => _$EventDataFromJson(json);

  Map<String, dynamic> toJson() => _$EventDataToJson(this);

  @override
  String toString() {
    return 'EventData(eventName: $eventName, timestamp: $timestamp, category: $category, label: $label, properties: $properties, userProperties: $userProperties)';
  }
}
