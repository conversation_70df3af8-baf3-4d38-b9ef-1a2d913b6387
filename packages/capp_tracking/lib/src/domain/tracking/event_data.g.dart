// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BulkEventData _$BulkEventDataFromJson(Map json) => BulkEventData(
      events: (json['events'] as List<dynamic>?)
              ?.map((e) =>
                  EventData.fromJson(Map<String, dynamic>.from(e as Map)))
              .toList() ??
          [],
    );

Map<String, dynamic> _$BulkEventDataToJson(BulkEventData instance) =>
    <String, dynamic>{
      'events': instance.events.map((e) => e.toJson()).toList(),
    };

EventData _$EventDataFromJson(Map json) => EventData(
      eventName: json['eventName'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      category: json['category'] as String?,
      label: json['label'] as String?,
      properties: (json['properties'] as Map?)?.map(
        (k, e) => MapEntry(k as String, e as Object),
      ),
      userProperties: (json['userProperties'] as Map?)?.map(
        (k, e) => MapEntry(k as String, e as Object),
      ),
    );

Map<String, dynamic> _$EventDataToJson(EventData instance) => <String, dynamic>{
      'eventName': instance.eventName,
      'timestamp': instance.timestamp.toIso8601String(),
      'category': instance.category,
      'label': instance.label,
      'properties': instance.properties,
      'userProperties': instance.userProperties,
    };
