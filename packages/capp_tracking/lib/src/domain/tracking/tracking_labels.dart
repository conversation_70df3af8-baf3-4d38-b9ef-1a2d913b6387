class TrackingLabels {
  static const deleted = 'deleted';
  static const filled = 'filled';
  static const mobileSignUp = 'mobile_sign_up';
  static const inbox = 'inbox';
  static const changePassword = 'change_password';
  static const documents = 'documents';
  static const selectLanguage = 'select_language';
  static const faq = 'faq';
  static const about = 'about';
  static const mobile = 'mobile';
  static const email = 'email';
  static const logout = 'logout';
  static const login = 'login';
  static const register = 'register';
  static const privacyPolicy = 'privacy_policy';
  static const termOfUse = 'terms_of_use';
  static const selectEmailUserAnotherEmail = 'sel_email_use_ano_email';
  static const selectEmailEmail = 'sel_email_email';
  static const selectEmail = 'sel_email';
  static const languageSurityImSure = 'language_surity_imsure';
  static const post90DaysLogin = 'post_90days_login';
  static const mobileOtpError = 'mobile_otp_error';
  static const mobileOtpErrorGotIt = 'mobile_otp_error_got_it';
  static const mobileConfirmOtp = 'mobile_confirm_otp';
  static const mobileResendOtp = 'mobile_resend_otp';
  static const wrongOtp = 'wrong_otp';
  static const wrongOtpResendOtp = 'wrong_otp_resend_otp';
  static const buyWithEasyEmi = 'buy_with_easy_emi';
  static const buyWithoutEmi = 'buy_without_emi';
  static const buyWithMerchant = 'buy_with_merchant';
  static const buyWithFlipkart = 'buy_with_flipkart';
  static const continueLabel = 'continue';
  static const buyWithCash = 'buy_with_cash';
  static const productOrderSummaryFlipkart = 'product_order_summary_flipkart';
  static const continueGetProduct = 'continue_get_product';
  static const productOrderGiftCard = 'product_order_gift_card';
  static const partnerConfirm = 'partner_confirm';
  static const mapStoreSelected = 'map_store_selected';
  static const continueToPayment = 'continue_to_payment';
  static const continueToGetProduct = 'continue_to_get_product';
  static const continueGetGiftCard = 'continue_get_gift_card';
  static const sliderImpression = 'slider_impression';
  static const registerLogin = 'register_login';
  static const guest = 'guest';
  static const rbiDisclaimer = 'rbi_disclaimer';
  static const lowerAvailableLiimit = 'lower_available_liimit';
  static const buyFlikartGiftcardConitinue = 'buy_flikart_giftcard_conitinue';
  static const applyNowPreApproved = 'apply_now_pre_approved';
  static const applyNowNoUjjwal = 'apply_now_no_ujjwal';
  static const applyNow = 'apply_now';
  static const preapprovedLimit = 'preapproved_limit';
  static const noUjjwalCard = 'no_ujjwal_card';
  static const outOfStock = 'out_of_stock';
  static const noThanks = 'no_thanks';
  static const productOrderSummaryGiftcard = 'product_order_summary_giftcard';
  static const viewMap = 'view_map';
  static const sortBy = 'sort_by_';
  static const partnerSelection = 'partner_selection';
  static const partnerMap = 'ptnr_map';
  static const partnerBuyWithEmi = 'partner_buy_with_emi';
  static const partnerBuyWithoutEmi = 'partner_buy_without_emi';
  static const orderSummaryPartner = 'order_summary_partner';
  static const atSearchbar = '_at_searchbar';
  static const belowSearchbar = '_below_searchbar';
  static const searchHistory = 'search_history';
  static const topSearch = 'top_search';
  static const removeSearchHistory = 'remove_search_history';
  static const product = 'product';
  static const category = 'category';
  static const dealSearchScreen = 'deal_search_screen';
  static const search = 'search';
  static const filterBy = 'filter_by';
  static const map = 'map';
  static const merchant = 'merchant';
  static const heartedProduct = 'hearted_product';
  static const productSwipeDirectionSlideNo = 'product_swipe_';
  static const truecallerSliderView = 'view';
  static const useSameNumber = 'use_same_num';
  static const useAnotherNumber = 'use_another_num';
  static const truecallerError = 'truecaller_error_';
  static const safePayNoProtection = 'no_protection';
  static const personalLoanSafePay = 'personal_loan_safepay';
  static const backButton = 'back_button';
  static const crossButton = 'cross_button';
  static const safePayLearnMore = 'safepay_learn_more';
  static const safepayChecked = 'safepay_checked';
  static const safepayUnChecked = 'safepay_unchecked';
  static const loanOriginationBankValidation = 'loan_origination_bank_validation';
  static const loanOriginationNetBanking = 'loan_origination_netbanking';
  static const loanOriginationProductSelection = 'loan_origination_product_selection';
  static const netBanking = 'netbanking';
  static const letsStart = 'lets_start';
  static const panCardLabel = 'pan_card';
  static const loanOriginationWelcomeBack = 'loan_origination_welcome_back';
  static const uidVerificationConsent = 'uid_verif_consent';
  static const loanOriginationResidentialAddress = 'loo_res_addr';
  static const okycUsingAadhar = 'okyc_using_aa';
  static const uploadZipFile = 'upload_zip_file';
  static const aadharXml = 'aa_xml';
  static const aadharXmlCode = 'aa_xml_code';
  static const confirm = 'confirm';
  static const loanOriginationCoresspondanceAddress = 'loan_origination_coresspondance_address';
  static const deeplinkCheckEligibility = 'dp_check_eli';
  static const deeplinkBackButton = 'dp_back_but';
  static const update = 'update';
  static const goToHomePage = 'gotohomepage';
  static const loanOriginationDataProcessingBank = 'loan_origination_data_processing_bank';
  static const loanOriginationDataProcessingBankFailed = 'loan_origination_data_processing_bank_failed';
  static const loanOriginationSelectBank = 'loan_origination_select_bank';
  static const ddmAadharEmandateOverlayImpression = 'aa_em_ov_impression';
  static const ddmAadharEmandateOverlayClickCancel = 'aa_em_ov_click_cancel';
  static const ddmAadharEmandateOverlayClickContinue = 'aa_em_ov_click_continue';
  static const back = 'back';
  static const uidVerficationBottomSlider = 'uid_verfication_bottom_slider';
  static const uidVerificationBottomSliderConfirm = 'uid_verification_bottom_slider_confirm';
  static const offlineKyc = 'offline_kyc';
  static const offlineKycPrefix = 'offline_kyc_';
  static const loanOriginationOfflineKyc = 'loan_origination_offline_kyc';
  static const accountHlName = 'account_hl_name';
  static const accountType = 'account_type';
  static const accountNumber = 'account_number';
  static const looCanOv = 'loo_can_ov';
  static const looCanOvContinue = 'loo_can_ov_continue';
  static const looCanOvCancel = 'loo_can_ov_cancel';
  static const companyName = 'company_name';
  static const employed = 'employed';
  static const employedOther = 'employed_other';
  static const employedGovt = 'employed_govt';
  static const retiredPen = 'retired_pen';
  static const retiredNoPen = 'retired_no_pen';
  static const houseWife = 'house_wife';
  static const jobStart = 'job_start';
  static const netIncome = 'net_income';
  static const retired = 'retired';
  static const selfEmployed = 'self_employed';
  static const student = 'student';
  static const unemployed = 'unemployed';
  static const videoLink = 'video_link';
  static const looIncomeInfoTs = 'loo_income_info_ts';
  static const income = 'income';
  static const rbi = 'rbi';
  static const criteria = 'criteria';
  static const ok = 'ok';
}
