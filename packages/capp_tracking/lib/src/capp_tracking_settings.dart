import 'package:capp_plugins_core/capp_plugins_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

class CappTrackingSettings {
  final Map<KoyalEvent, bool>? commonConfig;
  final Map<KoyalEvent, bool> firebaseEventsConfig;
  final Map<KoyalEvent, bool>? sas360EventsConfig;
  final Map<KoyalEvent, bool> facebookEventsConfig;
  final Map<KoyalEvent, bool> forwarderEventsConfig;
  final Map<KoyalEvent, bool> fastForwarderEventsConfig;
  final Map<KoyalEvent, bool> appsflyerEventsConfig;
  final List<EventTrackingOptions> eventTrackingOptions;
  final String gaTrackingId;
  final SasPluginTenantConfig? sasTenantConfig;
  final SasPluginTenantConfig? sasInsiderTenantConfig;
  final Map<String, bool>? sasSkipTrackViewDefault;
  final String? googlePlayId;
  final String? appStoreId;
  final Map<String, int>? gaPropertyMapIndex;
  final ShouldTrackProperties shouldTrackProperties;

  CappTrackingSettings({
    required this.commonConfig,
    required Map<KoyalEvent, bool> firebaseEventsConfig,
    required Map<KoyalEvent, bool> facebookEventsConfig,
    required Map<KoyalEvent, bool> forwarderEventsConfig,
    required Map<KoyalEvent, bool> fastForwarderEventsConfig,
    required Map<KoyalEvent, bool> appsflyerEventsConfig,
    required this.gaTrackingId,
    required this.eventTrackingOptions,
    required this.shouldTrackProperties,
    Map<KoyalEvent, bool>? sas360EventsConfig,
    this.sasTenantConfig,
    this.sasInsiderTenantConfig,
    this.sasSkipTrackViewDefault,
    required this.googlePlayId,
    required this.appStoreId,
    this.gaPropertyMapIndex,
  })  : firebaseEventsConfig = _updateConfig(data: commonConfig ?? {}, dataToUpdate: firebaseEventsConfig),
        facebookEventsConfig = _updateConfig(data: commonConfig ?? {}, dataToUpdate: facebookEventsConfig),
        forwarderEventsConfig = _updateConfig(data: commonConfig ?? {}, dataToUpdate: forwarderEventsConfig),
        fastForwarderEventsConfig = _updateConfig(data: commonConfig ?? {}, dataToUpdate: fastForwarderEventsConfig),
        appsflyerEventsConfig = _updateConfig(data: commonConfig ?? {}, dataToUpdate: appsflyerEventsConfig),
        sas360EventsConfig = _updateConfig(data: commonConfig ?? {}, dataToUpdate: sas360EventsConfig);

  static Map<KoyalEvent, bool> _updateConfig({
    required Map<KoyalEvent, bool> data,
    required Map<KoyalEvent, bool>? dataToUpdate,
  }) {
    final newData = <KoyalEvent, bool>{}..addAll(data);

    dataToUpdate?.forEach((key, value) {
      newData[key] = value;

      if (newData.containsKey(key)) {
        newData.update(key, (_) => value);
      }
    });

    return newData;
  }
}
