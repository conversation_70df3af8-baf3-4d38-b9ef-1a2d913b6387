import 'package:koyal_shared/koyal_shared.dart';

final class FastForwarderUserPropertyHandler extends UserPropertyHandler {
  FastForwarderUserPropertyHandler({
    required super.shouldTrackProperties,
    required super.globalTrackingProperties,
  });

  @override
  Map<String, dynamic> get globalsIndexMap => {
        KoyalTrackingProperties.propertyUserId: globalTrackingProperties.userId,
        KoyalTrackingProperties.propertyCdTransactionNumber: globalTrackingProperties.transactionNumber,
        KoyalTrackingProperties.propertyCdUserIsInsider: globalTrackingProperties.isInsider,
        KoyalTrackingProperties.propertyCdIsProd: !globalTrackingProperties.isInsider!,
        KoyalTrackingProperties.propertyJourneyType:
            globalTrackingProperties.journeyType.translateToCorrectJourneyType(),
        KoyalTrackingProperties.propertyPropDynamicV1: globalTrackingProperties.journeyType,
        KoyalTrackingProperties.propertyCdOfferId: globalTrackingProperties.offerId.toString(),
        KoyalTrackingProperties.propertyCdContractNumber: globalTrackingProperties.contractId.toString(),
        KoyalTrackingProperties.propertyCdLoanType: globalTrackingProperties.loanType,
        KoyalTrackingProperties.propertyCdProductType: globalTrackingProperties.productType,
        KoyalTrackingProperties.propertyUserType: globalTrackingProperties.userType,
        KoyalTrackingProperties.propertyCdAppVersion: globalTrackingProperties.appVersion,
        KoyalTrackingProperties.propertyCdCuid: globalTrackingProperties.cuid,
        KoyalTrackingProperties.propertyCdSessionID: globalTrackingProperties.sessionID,
        KoyalTrackingProperties.propertyCdEloadCategory: globalTrackingProperties.eLoadCategory,
        KoyalTrackingProperties.propertyCdEbillCategory: globalTrackingProperties.eBillCategory,
        KoyalTrackingProperties.propertyCdTranPaymentMethod: globalTrackingProperties.transactionPaymentMethod,
        KoyalTrackingProperties.propertyCdPrevScreen: globalTrackingProperties.prevScreen,
        KoyalTrackingProperties.propertyCdCurrentcreen: globalTrackingProperties.currScreen,
        KoyalTrackingProperties.propertyCdPhoneNumber: globalTrackingProperties.phoneNumber,
        KoyalTrackingProperties.propertyPhoneReset: globalTrackingProperties.phoneNumberOnReset,
        KoyalTrackingProperties.propertyCdAbTest: globalTrackingProperties.repaymentAbTest,
        KoyalTrackingProperties.propertyCdQrCategory: globalTrackingProperties.qrCategory,
        KoyalTrackingProperties.propertyCdReferralCode: globalTrackingProperties.referralCode,
        KoyalTrackingProperties.propertyTimeSpend: globalTrackingProperties.timeSpend,
        KoyalTrackingProperties.propertyCdFlowStage: globalTrackingProperties.flowStage,
      };
}
