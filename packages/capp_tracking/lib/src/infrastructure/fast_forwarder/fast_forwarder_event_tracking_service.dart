import 'dart:async';

import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_tracking.dart';

/// The Fast Forwarder service was introduced in US 167236 to enable immediate event tracking without buffer delay.
/// Use this only for critical events that require real-time tracking to prevent excessive usage.
/// CAUTION: Events configured for the FastForwader must not be in the standard forwarder, as this would cause duplicate tracking.
class FastForwarderEventTrackingService extends EventTrackingService {
  final IFeatureFlagRepository featureFlagRepository;
  final IForwarderRepository repository;
  final Map<KoyalEvent, bool> eventsConfig;
  final UserPropertyHandler userPropertyHandler;
  final bool enabled;
  final Logger logger;

  String? _deferInstallReferrerUrl;

  FastForwarderEventTrackingService({
    required this.repository,
    required this.logger,
    required this.featureFlagRepository,
    required this.eventsConfig,
    required this.userPropertyHandler,
    required GlobalTrackingProperties gtp,
    this.enabled = true,
  }) : super(globalTrackingProperties: gtp);

  @override
  Future<void> trackEvent({
    required String name,
    KoyalEvent? event,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
  }) async {
    if (shouldTrack(eventsConfig, event) == false) return;
    final flagEnabled = featureFlagRepository.isEnabledCached(FeatureFlag.forwarderLogger);
    if (flagEnabled) {
      final category = properties != null ? properties[KoyalAnalyticsConstants.category] : '';
      final label = properties != null ? properties[KoyalAnalyticsConstants.label1] : '';

      final eventData = EventData(
        eventName: name,
        timestamp: DateTime.now(),
        category: category,
        label: label,
        properties: properties,
        userProperties: userPropertyHandler.getIndexBasedUserProperty(userProperty)
          ..addAll(globalTrackingProperties.utmParameters),
      );
      await repository.log(data: eventData);
    } else {
      logger.i('trackEvent: forwardLogger is disabled by FF');
    }
  }

  @override
  Future<void> trackInstallReferrer({required String referrerUrl}) async {
    // this event is not needed
  }

  @override
  Future<void> trackScreenEvent({required String screenName, Map<String, String>? userProperty}) async {
    // this event is not needed
  }

  @override
  Future<void> setUser({
    required String userId,
    String? cuid,
    required bool isInsider,
    String? phoneNumber,
    required bool isExistingUser,
    required String signonStatus,
  }) async {
    if (_deferInstallReferrerUrl != null) {
      await trackInstallReferrer(referrerUrl: _deferInstallReferrerUrl!);
      _deferInstallReferrerUrl = null;
    }
  }

  @override
  void setCurrentScreen({required String screenName}) {}

  @override
  Future<void> cleanUp() async {}
}
