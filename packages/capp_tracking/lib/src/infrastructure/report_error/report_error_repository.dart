import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:alice/alice.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:path_provider/path_provider.dart';

import '../../domain/report_error/alice_mapper.dart';
import '../../domain/report_error/report_error_model.dart';

class ReportErrorRepository {
  final IPlatformService platformService;
  final IUserRepository userRepository;
  final Alice alice;

  ReportErrorRepository({
    required this.platformService,
    required this.userRepository,
    required this.alice,
  });

  Future<ReportErrorModel> getLogs() async {
    final packageInfo = await platformService.getPackageInfo();
    final user = (await userRepository.currentUser()).fold((l) => null, (r) => r);
    final cuid = await userRepository.userCuid();

    var apiCalls = alice.getDioInterceptor().aliceCore.list;

    if (apiCalls.length > 30) {
      apiCalls = apiCalls.sublist(apiCalls.length - 30, apiCalls.length);
    }
    final logs = <ReportErrorItem>[];
    for (final apiCall in apiCalls) {
      logs.add(apiCall.toDomain());
    }
    return ReportErrorModel(
      createdAt: DateTime.now(),
      apiCalls: logs,
      appVersion: '${packageInfo.version} (${packageInfo.buildNumber})',
      muid: user?.id,
      cuid: cuid,
      userName: user?.username,
    );
  }

  Future<File> getFileLogContent({ReportErrorModel? errorLogs}) async {
    final logs = errorLogs ?? await getLogs();

    final directory = await getTemporaryDirectory();
    final tempDirectoryPath = '${directory.path}/temp';
    final file = File('$tempDirectoryPath/${DateTime.now()}.log');

    if (!file.existsSync()) {
      await Directory(tempDirectoryPath).create(recursive: true);
    }
    await file.writeAsString(
      jsonEncode(logs),
      mode: FileMode.writeOnly,
    );

    return file;
  }
}
