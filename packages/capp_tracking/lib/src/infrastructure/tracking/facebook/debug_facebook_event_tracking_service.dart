import 'package:capp_plugins_core/capp_plugins_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:logger/logger.dart';

import '../../../../capp_tracking.dart';

class DebugFacebookEventTrackingService extends FacebookEventTrackingService {
  final Logger? logger;

  DebugFacebookEventTrackingService({
    this.logger,
    required GlobalTrackingProperties gtp,
    required IFacebookTrackingEventServicePlugin plugin,
  }) : super(
          plugin: plugin,
          eventsConfig: {},
          gtp: gtp,
        );

  @override
  Future<void> trackEvent({
    required String name,
    KoyalEvent? event,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
    List<String?>? customDimensions,
  }) async {
    logger!.v(
      'Track event: $name, properties: ${prepareProperties(properties, userProperty)},  user property: $userProperty',
    );
    return Future.value();
  }

  @override
  Future<void> trackScreenEvent({
    required String screenName,
    Map<String, String>? userProperty,
  }) async {
    logger!.v('Track screen event: $screenName, user property: $userProperty');
    return Future.value();
  }

  @override
  Future<void> setUser({
    required String userId,
    String? cuid,
    required bool isInsider,
    String? phoneNumber,
    required bool isExistingUser,
    required String signonStatus,
  }) =>
      Future.value();
}
