import 'package:capp_plugins_core/capp_plugins_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

class FacebookEventTrackingService extends EventTrackingService {
  final Map<KoyalEvent, bool> eventsConfig;
  final IFacebookTrackingEventServicePlugin plugin;
  FacebookEventTrackingService({
    required this.eventsConfig,
    required this.plugin,
    required GlobalTrackingProperties gtp,
  }) : super(globalTrackingProperties: gtp);

  @override
  Future<bool> init() {
    return Future.value(true);
  }

  @override
  Future<void> trackEvent({
    required String name,
    KoyalEvent? event,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
    List<String?>? customDimensions,
  }) async {
    if (shouldTrack(eventsConfig, event) == false) return;

    //mapping to standard events (custom events cannot be used for create campaing)
    if (name == 'user_action_loan_product') {
      await plugin.logViewContent(
        id: '',
        type: '',
        price: 0,
        currency: '',
        content: prepareProperties(properties, userProperty),
      );
    } else if (name == 'user_action_ujjwal_card_limit') {
      await plugin.logAddToCart(
        id: '',
        type: '',
        currency: '',
        price: 0,
        content: prepareProperties(properties, userProperty),
      );
    } else if (name == 'user_action_flexi_card_limit') {
      await plugin.logPurchase(
        amount: 0,
        currency: 'xzy',
        parameters: prepareProperties(properties, userProperty),
      );
    } else if ([
      'user_action_Flexible Personal Loan',
      'user_action_Ujjwal Card',
      'user_action_Personal Loan',
    ].contains(name)) {
      await plugin.logAddToWishlist(
        id: '',
        type: '',
        currency: '',
        price: 0,
        content: prepareProperties(properties, userProperty),
      );
    }
    return plugin.logEvent(
      name: name,
      parameters: prepareProperties(properties, userProperty),
    );
  }

  @override
  Future<void> trackScreenEvent({
    required String screenName,
    Map<String, String>? userProperty,
  }) async {
    return plugin.logEvent(
      name: screenName,
      parameters: prepareProperties({}, userProperty),
    );
  }

  Map<String, String> prepareProperties(
    Map<String, String>? properties,
    Map<String, String>? userProperty,
  ) {
    final mergedProperties = Map<String, String>.from(properties ?? <String, String>{});
    userProperty?.forEach((key, value) {
      if (mergedProperties.containsKey(key)) {
        mergedProperties[key] = value;
      } else {
        mergedProperties.putIfAbsent(key, () => value);
      }
    });
    if (globalTrackingProperties.userId != null) {
      mergedProperties[KoyalTrackingProperties.propertyUserId] = globalTrackingProperties.userId!;
    }
    return mergedProperties;
  }

  @override
  Future<void> setUser({
    required String userId,
    String? cuid,
    required bool isInsider,
    String? phoneNumber,
    required bool isExistingUser,
    required String signonStatus,
  }) =>
      Future.value();

  @override
  void setCurrentScreen({required String screenName}) {}

  @override
  Future<void> cleanUp() async {}
}
