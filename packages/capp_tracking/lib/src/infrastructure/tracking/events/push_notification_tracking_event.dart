import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_tracking.dart';

/// More info in US: 151608
final class _PushNotificationCustomDim {
  _PushNotificationCustomDim._();

  static const pnId = 'ID_PUSH_NOTIF_MESSAGE';
  static const textSubject = 'TEXT_SUBJECT';
  static const textMessage = 'TEXT_MESSAGE';
  static const textMessageLink = 'TEXT_MESSAGE_LINK';
  static const codeVoucher = 'CODE_VOUCHER';
  static const dTimeProcessed = 'DTIME_PROCCESSED';
  static const textImageUrl = 'TEXT_IMAGE_URL';
  static const textMessageTemplate = 'TEXT_MESSAGE_TEMPLATE'; // only for SAS
  static const dTimeClicked = 'DTIME_CLICKED';
  static const pnSystem = 'PN_SYSTEM';
}

abstract class PushNotificationTrackingEvent extends CappTrackingEvent {
  final Map<String, String> _dimensions = {};
  PushNotificationTrackingEvent({
    required final String pnId,
    required final String textSubject,
    required final String textMessage,
    required final String textMessageLink,
    required final String dTimeProcessed,
    required final String textImageUrl,
    required final String pnSystem,
    final String contractNumber = '',
    final String codeVoucher = '',
    final String textMessageTemplate = '', // only for SAS
    final String cuid = '',
  }) {
    _dimensions[_PushNotificationCustomDim.pnId] = pnId;
    _dimensions[_PushNotificationCustomDim.textSubject] = textSubject;
    _dimensions[_PushNotificationCustomDim.textMessage] = textMessage;
    _dimensions[_PushNotificationCustomDim.textMessageLink] = textMessageLink;
    _dimensions[_PushNotificationCustomDim.codeVoucher] = codeVoucher;
    _dimensions[_PushNotificationCustomDim.textImageUrl] = textImageUrl;
    _dimensions[_PushNotificationCustomDim.dTimeProcessed] = dTimeProcessed;
    _dimensions[_PushNotificationCustomDim.textMessageTemplate] = textMessageTemplate;
    _dimensions[_PushNotificationCustomDim.dTimeClicked] = '${DateTime.now().millisecondsSinceEpoch ~/ 1000}';
    _dimensions[_PushNotificationCustomDim.pnSystem] = pnSystem;
    _dimensions[KoyalTrackingProperties.propertyCdCuid] = cuid;
    _dimensions[KoyalTrackingProperties.propertyCdContractNumber] = contractNumber;
  }

  @override
  String get category => KoyalTrackingCategories.pushNotification;

  @override
  Map<String, String> get customDimensions => _dimensions;
}
