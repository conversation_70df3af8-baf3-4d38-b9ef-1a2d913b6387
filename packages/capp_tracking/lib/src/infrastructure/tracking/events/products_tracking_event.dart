import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_tracking.dart';

// Benefit event with custom dimension 33,73,74
// 33 - SAS
// 71 - designed to provide the card name or item name
// 73 - designed to capture the amount of time (in seconds) a user spend on a specific view/widget.
// 74 - designed to provide the product name (Tracking label e.g. "cash_loan" from BAPP)
abstract class ProductsTrackingEvent extends CappTrackingEvent {
  late final Map<String, String> _customDimensions;

  @override
  String get category => KoyalTrackingCategories.productService;

  @override
  Map<String, String> get customDimensions => _customDimensions;

  ProductsTrackingEvent({
    Duration? duration,
    String? itemName,
    String? itemId,
    String? categoryId,
    required String productName,
  }) {
    _customDimensions = {
      // 74
      TrackingProperties.propertyCdProductName: productName,
      // 33
      TrackingProperties.propertyCdSas: '1',
    };
    // 62
    if (itemId != null) {
      _customDimensions[TrackingProperties.propertyCdItemId] = itemId;
    }
    // 67
    if (categoryId != null) {
      _customDimensions[TrackingProperties.propertyCdCategoryId] = categoryId;
    }
    // 71
    if (itemName != null) {
      _customDimensions[TrackingProperties.propertyCdItemName] = itemName;
    }
    //73
    if (duration != null) {
      _customDimensions[TrackingProperties.propertyCdTimeSpent] = '${duration.inSeconds}';
    }
  }
}
