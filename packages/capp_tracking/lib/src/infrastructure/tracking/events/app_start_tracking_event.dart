import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_tracking.dart';

// non global CD
// 46 - utm_campaign
// 47 - utm_medium
// 48 - utm_source
// 49 - utm_term
// 50 - utm_id
// 51 - utm_content
abstract class AppStartTrackingEvent extends CappTrackingEvent {
  final Map<String, String> _customDimensions = {};

  AppStartTrackingEvent({
    required String url,
  }) {
    final uri = Uri.dataFromString(url);
    for (final utmParam in UTMTrackingProperties.utmParameters) {
      _customDimensions[utmParam] = uri.queryParameters[utmParam] ?? '';
    }
  }

  AppStartTrackingEvent.fromInstallData({
    required final Map<String, dynamic> installData,
  }) {
    for (final utmParam in UTMTrackingProperties.utmParameters) {
      _customDimensions[utmParam] = installData[utmParam] ?? '';
    }
  }

  @override
  Map<String, String> get customDimensions => _customDimensions;

  @override
  String get category => KoyalTrackingCategories.app;

  @override
  String get action => KoyalAnalyticsConstants.view;
}
