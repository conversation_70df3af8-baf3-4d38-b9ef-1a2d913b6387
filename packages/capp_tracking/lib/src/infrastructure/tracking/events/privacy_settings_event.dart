import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_tracking.dart';

// PrivacySettings event with custom dimension 33
// 33 - SAS
abstract class PrivacySettingsTrackingEvent extends CappTrackingEvent {
  final Map<String, String> _customDimensions = {};

  PrivacySettingsTrackingEvent() {
    // 33
    _customDimensions[TrackingProperties.propertyCdSas] = '1';
  }

  @override
  Map<String, String> get customDimensions => _customDimensions;

  @override
  String get category => KoyalTrackingCategories.privacySettings;
}
