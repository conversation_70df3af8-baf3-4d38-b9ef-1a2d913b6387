import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../push_notification_tracking_event.dart';

/// PSH001
final class PushNotificationOpenClickEvent extends PushNotificationTrackingEvent {
  /// This default constructor for standard Notification message object
  factory PushNotificationOpenClickEvent.fromNotificationMessage(NotificationMessage message) {
    return PushNotificationOpenClickEvent._(
      pnId: message.data?.msId ?? '',
      textSubject: message.title ?? '',
      textMessage: message.body ?? '',
      textMessageLink: message.data?.deeplink ?? '',
      dTimeProcessed: 'N/A',
      textImageUrl: message.data?.image ?? '',
      textMessageTemplate: '${message.data?.sasTemplateId ?? 0}',
      pnSystem: message.isFromSas ? 'SAS' : '',
      cuid: '${message.data?.cuid ?? ''}',
      contractNumber: '${message.data?.contractNumber ?? ''}',
    );
  }

  PushNotificationOpenClickEvent._({
    required super.pnId,
    required super.textSubject,
    required super.textMessage,
    required super.textMessageLink,
    required super.dTimeProcessed,
    required super.textImageUrl,
    required super.pnSystem,
    super.textMessageTemplate = '',
    super.cuid = '',
    super.contractNumber = '',
  });

  @override
  String get action => KoyalAnalyticsConstants.click;

  @override
  KoyalEvent get event => KoyalEvent.pushNotificationOpenClick;

  @override
  String get label => KoyalTrackingLabels.open;
}
