/// To help handle performance tracking
mixin CappPerformanceTrackingMixin {
  Stopwatch? _stopwatch;

  /// Common place is to call startTimeMeasurement() in initState method inside widget
  void startTimeMeasurement() {
    _stopwatch = Stopwatch()..start();
  }

  /// Common place is to call finishTimeMeasurement() in deactivate method inside widget
  Duration finishTimeMeasurement() {
    final measuredTime = _stopwatch?.elapsed ?? Duration.zero;
    _stopwatch?.stop();
    _stopwatch = null;

    return measuredTime;
  }
}
