import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_tracking.dart';
import 'events/push_notification_tracking_event.dart';

class CappTrackingService extends KoyalTrackingService {
  final ICurrentUserRepository currentUserRepository;
  final IUserRepository userRepository;

  CappTrackingService({
    required EventTrackingService eventTrackingService,
    required this.currentUserRepository,
    required this.userRepository,
    Map<String, String> defaultUserProperty = const {},
  }) : super(eventTrackingService: eventTrackingService) {
    defaultUserPropertyMap.addAll(_getDefaultDimension());
    setDefaultUserPropertyMap(defaultUserProperty);
  }

  void setDefaultUserPropertyMap(Map<String, String> defaultUserPropertyMap) {
    for (final prop in defaultUserPropertyMap.entries) {
      defaultUserPropertyMap[prop.key] = prop.value;
    }
  }

  void setUtmParametersFromUrl(String url) {
    eventTrackingService.globalTrackingProperties.utmParameters.clear();

    final uri = Uri.dataFromString(url);
    for (final utmParam in UTMTrackingProperties.utmParameters) {
      eventTrackingService.globalTrackingProperties.utmParameters[utmParam] = uri.queryParameters[utmParam] ?? '';
    }
  }

  void setUtmParametersFromData(Map<String, dynamic> data) {
    eventTrackingService.globalTrackingProperties.utmParameters.clear();

    for (final utmParam in UTMTrackingProperties.utmParameters) {
      eventTrackingService.globalTrackingProperties.utmParameters[utmParam] = data[utmParam] ?? '';
    }
  }

  String? get offerId => eventTrackingService.globalTrackingProperties.offerId;

  String? get phoneNumber => eventTrackingService.globalTrackingProperties.phoneNumber;

  set offerId(String? offerId) {
    eventTrackingService.globalTrackingProperties.offerId = offerId;
  }

  Map<String, String> _getDefaultDimension() {
    return {
      TrackingProperties.propertyClientId: '',
      TrackingProperties.propertyUserId: '',
      TrackingProperties.propertyJourneyType: 'CAPP',
      TrackingProperties.propertyPropertyType: 'app_capp',
      TrackingProperties.propertyCdLoanType: '',
      TrackingProperties.propertyCdProductType: '',
      TrackingProperties.propertyPropDynamicV1: '',
      KoyalTrackingProperties.propertyEpochTimestamp: DateTime.now().millisecondsSinceEpoch.toString(),
    };
  }

  Future<String> get isProdDimension async => await currentUserRepository.isInsider() ? 'false' : 'true';
  Future<String> get userSignOnStatus async {
    if (eventTrackingService.globalTrackingProperties.signonStatus != null) {
      return eventTrackingService.globalTrackingProperties.signonStatus!;
    } else {
      final user = await currentUserRepository.getCurrentUser();
      return enumValueToString(user?.signOnState ?? SignOnStatus.anonymous);
    }
  }

  Future<String> get existingUser async =>
      !await userRepository.isExistingUser() ? TrackingProperties.newUser : TrackingProperties.existingUser;

  Future<String> get currentUserId async {
    final user = await currentUserRepository.getCurrentUser();
    return user?.id ?? '';
  }

  Future<void> trackEvent({
    required String eventCategory,
    required String eventAction,
    required String eventLabel,
    Map<String, String> customDimensions = const {},
    KoyalEvent? event,
  }) async {
    final userPropertyMap = <String, String>{};

    if (customDimensions.isNotEmpty) userPropertyMap.addAll(customDimensions);

    userPropertyMap[TrackingProperties.propertyCdSignOnStatus] = await userSignOnStatus;
    userPropertyMap[TrackingProperties.propertyCdIsProd] = await isProdDimension;

    return trackAnalyticsEvent(
      eventCategory: eventCategory,
      eventAction: eventAction,
      eventLabel: eventLabel,
      userPropertyMap: userPropertyMap,
      event: event,
    );
  }

  Future<void> trackInboxEvent({
    required KoyalEvent event,
    required String eventLabel,
    required String eventAction,
    required String? externalMessageId,
    String? eventCategory,
    Map<String, String> customDimensions = const {},
  }) async {
    final userPropertyMap = <String, String>{};
    userPropertyMap[TrackingProperties.propertyCdIsProd] = await isProdDimension;
    userPropertyMap[TrackingProperties.propertyCdSignOnStatus] = await userSignOnStatus;
    if (externalMessageId != null) {
      userPropertyMap[TrackingProperties.propertyCdExternalMessageId] = externalMessageId;
    }
    return trackAnalyticsEvent(
      event: event,
      eventCategory: eventCategory ?? KoyalTrackingCategories.inboxScreen,
      eventAction: eventAction,
      eventLabel: eventLabel,
      userPropertyMap: userPropertyMap..addAll(customDimensions),
    );
  }

  Future<void> trackHomeDashboardEvent({
    required KoyalEvent event,
    required String eventLabel,
    required String eventAction,
    Map<String, String> customDimensions = const {},
  }) async {
    final userPropertyMap = <String, String>{};
    userPropertyMap[TrackingProperties.propertyCdIsProd] = await isProdDimension;
    userPropertyMap[TrackingProperties.propertyCdSignOnStatus] = await userSignOnStatus;
    userPropertyMap.addAll(customDimensions);
    return trackAnalyticsEvent(
      event: event,
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: eventAction,
      eventLabel: eventLabel,
      userPropertyMap: userPropertyMap,
    );
  }

  Future<void> trackFeedbackEvent({
    required KoyalEvent event,
    required String label,
    required String action,
    required String category,
    String? feedbackJourneyId,
    String? negativeFeedbackText,
    String? featureFeedbackText,
    String? feedbackValueId,
    String? feedbackImproveOptionsId,
    String? sas,
  }) async {
    final userPropertyMap = <String, String>{};
    // dim 29
    userPropertyMap[TrackingProperties.propertyCdIsProd] = await isProdDimension;
    // dim 34
    userPropertyMap[TrackingProperties.propertyCdSignOnStatus] = await userSignOnStatus;

    _addProperty(userPropertyMap, TrackingProperties.propertyFeatureFeedbackJourneyId, feedbackJourneyId);
    _addProperty(userPropertyMap, TrackingProperties.propertyCdFeedbackValueId, feedbackValueId);
    _addProperty(userPropertyMap, TrackingProperties.propertyCdImproveOptionId, feedbackImproveOptionsId);
    _addProperty(userPropertyMap, TrackingProperties.propertyCdSas, sas);
    _addProperty(
      userPropertyMap,
      TrackingProperties.propertyNegativeFeedbackText,
      negativeFeedbackText,
      useDefault: true,
    );
    _addProperty(
      userPropertyMap,
      TrackingProperties.propertyFeatureFeedbackText,
      featureFeedbackText,
      useDefault: true,
    );

    return trackAnalyticsEvent(
      event: event,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
      userPropertyMap: userPropertyMap,
    );
  }

  Future<void> trackTruecallerEvent(String category, String action, String label, KoyalEvent? event) async {
    final map = {
      TrackingProperties.propertyCdIsProd: await isProdDimension,
    };
    trackAnalyticsEvent(
      userPropertyMap: map,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
      event: event,
    );
  }

  Future<void> trackLoanProductEvent(
    String category,
    String action,
    String label, {
    String? loanType,
    String? productType,
    KoyalEvent? event,
    String? loanOfferType,
    String? dynamicValue,
  }) async {
    final map = {
      TrackingProperties.propertyUserType: await existingUser,
      TrackingProperties.propertyCdLoanType: loanType ?? '',
      TrackingProperties.propertyCdProductType: productType ?? '',
      TrackingProperties.propertyCdLoanOfferType: loanOfferType ?? '',
    };
    if (dynamicValue != null) {
      map.putIfAbsent(TrackingProperties.propertyPropDynamicV1, () => dynamicValue);
    }

    trackAnalyticsEvent(
      userPropertyMap: map,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
      event: event,
    );
  }

  Future<void> trackVasEvent(
    String category,
    String action,
    String label,
    String? loanType,
    String? productType,
  ) async {
    final map = {
      TrackingProperties.propertyCdIsProd: await isProdDimension,
      TrackingProperties.propertyUserType: await existingUser,
      TrackingProperties.propertyCdLoanType: loanType ?? '',
      TrackingProperties.propertyCdProductType: productType ?? '',
    };

    trackAnalyticsEvent(
      userPropertyMap: map,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
    );
  }

  Future<void> trackIncomeDeclarationEvent(
    String category,
    String action,
    String label,
    String? loanType,
    String? productType,
    String? dynamicValue,
  ) async {
    final map = {
      TrackingProperties.propertyCdIsProd: await isProdDimension,
      TrackingProperties.propertyUserType: await existingUser,
      TrackingProperties.propertyCdLoanType: loanType ?? '',
      TrackingProperties.propertyCdProductType: productType ?? '',
    };
    if (dynamicValue != null) {
      map.putIfAbsent(TrackingProperties.propertyPropDynamicV1, () => dynamicValue);
    }

    trackAnalyticsEvent(
      userPropertyMap: map,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
    );
  }

  Future<void> trackProductIntroEvent(
    String category,
    String action,
    String label,
    String? loanType,
    String? dynamicValue,
  ) async {
    final map = {
      TrackingProperties.propertyCdIsProd: await isProdDimension,
      TrackingProperties.propertyUserType: await existingUser,
      TrackingProperties.propertyCdLoanType: loanType ?? '',
    };
    if (dynamicValue != null) {
      map.putIfAbsent(TrackingProperties.propertyPropDynamicV1, () => dynamicValue);
    }

    trackAnalyticsEvent(
      userPropertyMap: map,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
    );
  }

  Future<void> trackStandaloneEvent(
    String category,
    String action,
    String label,
    String? loanType,
    String? productType, [
    KoyalEvent? event,
  ]) async {
    final map = {
      TrackingProperties.propertyCdIsProd: await isProdDimension,
      TrackingProperties.propertyUserType: await existingUser,
      TrackingProperties.propertyCdLoanType: loanType ?? '',
      TrackingProperties.propertyCdProductType: productType ?? '',
    };

    trackAnalyticsEvent(
      userPropertyMap: map,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
      event: event,
    );
  }

  void trackErrorReloadClick(String errorLabel) {
    trackAnalyticsEvent(
      eventCategory: 'user_action_universal_error',
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: getFormattedAnalyticsText('reload_$errorLabel'),
    );
  }

  void trackGenericErrorView(String errorId) {
    trackAnalyticsEvent(
      eventCategory: TrackingCategories.errorView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: getFormattedAnalyticsText('error_$errorId'),
      event: KoyalEvent.genericError,
    );
  }

  /// P&S - benefits tracking
  /// App Start tracking
  Future<void> trackCappEvent(CappTrackingEvent event) async {
    final userPropertyMap = <String, String>{}..addAll(event.customDimensions);
    eventTrackingService.globalTrackingProperties.timeSpend =
        event.customDimensions[TrackingProperties.propertyCdTimeSpent] ?? '';
    userPropertyMap[TrackingProperties.propertyCdSignOnStatus] = await userSignOnStatus;

    return trackAnalyticsEvent(
      event: event.event,
      eventCategory: event.category,
      eventAction: event.action,
      eventLabel: event.label,
      userPropertyMap: userPropertyMap,
    );
  }

  void _addProperty(
    Map<String, String> userPropertyMap,
    String trackingProperty,
    String? value, {
    final bool useDefault = false,
    final String defaultValue = '',
  }) {
    if (value != null && value.isNotEmpty) {
      userPropertyMap[trackingProperty] = value;
    } else if (useDefault) {
      userPropertyMap[trackingProperty] = defaultValue;
    }
  }

  Future<void> trackGamificationEvent({
    required String category,
    required String action,
    required String label,
    String? screenLabel,
    Map<String, String> customDimensions = const {},
  }) async {
    final userPropertyMap = <String, String>{};
    userPropertyMap[TrackingProperties.propertyClientId] = '';
    userPropertyMap[TrackingProperties.propertyUserId] = await currentUserId;
    userPropertyMap[KoyalTrackingProperties.propertyEpochTimestamp] = DateTime.now().millisecondsSinceEpoch.toString();
    userPropertyMap[KoyalTrackingProperties.propertyCdIsProd] = await isProdDimension;
    userPropertyMap[KoyalTrackingProperties.propertyCdSignOnStatus] = await userSignOnStatus;
    userPropertyMap.addAll(customDimensions);

    trackAnalyticsEvent(
      userPropertyMap: userPropertyMap,
      eventCategory: category,
      eventAction: action,
      eventLabel: label,
      screenLabel: screenLabel,
    );
  }

  Future<void> trackPushNotification(PushNotificationTrackingEvent event) async {
    // check cuid property
    if (event.customDimensions[TrackingProperties.propertyCdCuid]?.isEmpty ?? true) {
      event.customDimensions[TrackingProperties.propertyCdCuid] =
          eventTrackingService.globalTrackingProperties.cuid ?? '';
    }
    // check contract ID
    if (event.customDimensions[TrackingProperties.propertyCdContractNumber]?.isEmpty ?? true) {
      event.customDimensions[TrackingProperties.propertyCdContractNumber] =
          eventTrackingService.globalTrackingProperties.contractId ?? '';
    }

    await trackCappEvent(event);
  }

  Future<void> trackRepaymentReminderEvent({
    required String eventCategory,
    required String eventAction,
    required String eventLabel,
    String? contractNumber,
    String? productType,
    KoyalEvent? event,
    Map<String, String> customDimensions = const {},
  }) async {
    final userPropertyMap = <String, String>{};
    userPropertyMap[TrackingProperties.propertyUserId] = await currentUserId;
    userPropertyMap[TrackingProperties.propertyJourneyType] = 'CAPP';
    userPropertyMap[KoyalTrackingProperties.propertyEpochTimestamp] = DateTime.now().millisecondsSinceEpoch.toString();

    userPropertyMap[TrackingProperties.propertyCdIsProd] = await isProdDimension;
    userPropertyMap[KoyalTrackingProperties.propertyCdCuid] = eventTrackingService.globalTrackingProperties.cuid ?? '';
    userPropertyMap[KoyalTrackingProperties.propertyCdProductType] = productType ?? '';
    userPropertyMap[KoyalTrackingProperties.propertyCdContractNumber] = contractNumber ?? '';
    userPropertyMap.addAll(customDimensions);
    
    trackAnalyticsEvent(
      userPropertyMap: userPropertyMap,
      eventCategory: eventCategory,
      eventAction: eventAction,
      eventLabel: eventLabel,
      event: event,
    );
  }
}
