import 'dart:io';

import 'package:gma_platform/gma_platform.dart';
import 'package:path_provider/path_provider.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../../capp_tracking.dart';

class DiagnosticsRepository extends IDiagnosticsRepository {
  final DiagnosticsApi? diagnosticsApi;
  final IPlatformService? platformService;

  DiagnosticsRepository({this.diagnosticsApi, this.platformService});

  @override
  Future<void> sendLogs({File? file}) async {
    if (GmaPlatform.isWeb) {
      return;
    }
    var logFile = file;
    if (file == null) {
      final tempDirectory = await getTemporaryDirectory();
      logFile = File('${tempDirectory.path}/temp/log.log');
    }
    if (!logFile!.existsSync()) {
      return;
    }
    final deviceId = await platformService!.deviceId();
    return diagnosticsApi!.diagnosticsLogPost(logFile: logFile, deviceId: deviceId);
  }
}
