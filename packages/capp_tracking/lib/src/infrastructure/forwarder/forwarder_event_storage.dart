import 'package:flutter/foundation.dart';
import 'package:gma_storage/gma_storage.dart';

import '../../../capp_tracking.dart';

class ForvarderEventStorage {
  final GmaStorageProvider storage;

  ForvarderEventStorage({required this.storage});

  Future<void> insertEventData({required EventData eventData}) async {
    final previousData = await getEventData();
    BulkEventData currentData;

    if (previousData != null) {
      currentData = BulkEventData(events: [...previousData.events, eventData]);
    } else {
      currentData = BulkEventData(events: [eventData]);
    }

    return storage.insert<Map<String, dynamic>>(_eventsToForward, currentData.toJson());
  }

  Future<BulkEventData?> getEventData() async {
    final json = await storage.get<Map<String, dynamic>>(_eventsToForward);
    return json == null ? null : await compute(_isolatedFromJson, json);
  }

  Future<void> deleteEventData() async => storage.delete(_eventsToForward);
}

Future<BulkEventData> _isolatedFromJson(Map<String, dynamic> json) async {
  return BulkEventData.fromJson(json);
}

const String _eventsToForward = 'eventsToForward';
