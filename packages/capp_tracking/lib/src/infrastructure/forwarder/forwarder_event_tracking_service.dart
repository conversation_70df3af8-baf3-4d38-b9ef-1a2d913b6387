import 'dart:async';

import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:quiver/iterables.dart';
import 'package:rxdart/rxdart.dart';

import '../../../capp_tracking.dart';

class ForwarderEventTrackingService extends EventTrackingService {
  final IFeatureFlagRepository featureFlagRepository;
  final IForwarderRepository repository;
  final ForvarderEventStorage storage;
  final Map<KoyalEvent, bool> eventsConfig;
  final bool enabled;
  final Logger logger;

  String? _deferInstallReferrerUrl;

  final PublishSubject<EventData> _subject = PublishSubject<EventData>();

  ForwarderEventTrackingService({
    required this.repository,
    required this.storage,
    required this.logger,
    required this.featureFlagRepository,
    required this.eventsConfig,
    required GlobalTrackingProperties gtp,
    this.enabled = true,
  }) : super(globalTrackingProperties: gtp) {
    _subject.stream
        .doOnListen(() async => _forwardSavedEvents())
        .doOnData((event) async => storage.insertEventData(eventData: event))
        .bufferTime(const Duration(seconds: 5))
        .listen((events) async => _forwardEvents(events: events));
  }

  @override
  Future<void> trackEvent({
    required String name,
    KoyalEvent? event,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
  }) async {
    if (shouldTrack(eventsConfig, event) == false) return;
    final flagEnabled = featureFlagRepository.isEnabledCached(FeatureFlag.forwarderLogger);
    if (flagEnabled) {
      final category = properties != null ? properties[KoyalAnalyticsConstants.category] : '';
      final label = properties != null ? properties[KoyalAnalyticsConstants.label1] : '';
      final preppedUser = EventData(
        eventName: name,
        timestamp: DateTime.now(),
        category: category,
        label: label,
        properties: properties,
        userProperties: _getIndexBasedUserProperty(userProperty)..addAll(globalTrackingProperties.utmParameters),
      );
      _subject.add(preppedUser);
    } else {
      logger.i('trackEvent: forwardLogger is disabled by FF');
    }
  }

  @override
  Future<void> trackInstallReferrer({required String referrerUrl}) async {
    if (globalTrackingProperties.userId == null) {
      _deferInstallReferrerUrl = referrerUrl;
    } else {
      return trackEvent(
        event: KoyalEvent.appAndroidGooglePlayInstall,
        name: 'android_install',
        properties: {KoyalAnalyticsConstants.category: 'android_install', KoyalAnalyticsConstants.label1: referrerUrl},
        userProperty: {
          KoyalTrackingProperties.propertyUserId: globalTrackingProperties.userId!,
          KoyalTrackingProperties.propertyPropDynamicV1: referrerUrl,
        },
      );
    }
  }

  @override
  Future<void> trackScreenEvent({required String screenName, Map<String, String>? userProperty}) async {
    if (!enabled) return;
    unawaited(
      featureFlagRepository.hasFeatureFlag(FeatureFlag.forwarderLogger).then(
            (value) => value.fold(
              (l) => logger.i('trackEvent: forwardLogger is disabled by FF'),
              (enabled) {
                if (enabled) {
                  _subject.add(
                    EventData(
                      eventName: 'screen_view',
                      timestamp: DateTime.now(),
                      category: 'screen_view',
                      label: screenName,
                      properties: {
                        KoyalAnalyticsConstants.category: 'screen_view',
                        KoyalAnalyticsConstants.action: 'view',
                        KoyalAnalyticsConstants.label1: screenName,
                      },
                      userProperties: _getIndexBasedUserProperty(userProperty)
                        ..addAll(globalTrackingProperties.utmParameters),
                    ),
                  );
                }
              },
            ),
          ),
    );
  }

  @override
  Future<void> setUser({
    required String userId,
    String? cuid,
    required bool isInsider,
    String? phoneNumber,
    required bool isExistingUser,
    required String signonStatus,
  }) async {
    if (_deferInstallReferrerUrl != null) {
      await trackInstallReferrer(referrerUrl: _deferInstallReferrerUrl!);
      _deferInstallReferrerUrl = null;
    }
  }

  Map<String, String> _getIndexBasedUserProperty(Map<String, String>? userProperty) {
    final indexMap = <String, String>{};

    if (globalTrackingProperties.userId != null) {
      indexMap[KoyalTrackingProperties.propertyUserId] = globalTrackingProperties.userId!;
    }

    if (globalTrackingProperties.transactionNumber != null) {
      indexMap[KoyalTrackingProperties.propertyCdTransactionNumber] = globalTrackingProperties.transactionNumber!;
    }

    if (globalTrackingProperties.isInsider != null) {
      indexMap[KoyalTrackingProperties.propertyCdUserIsInsider] = globalTrackingProperties.isInsider.toString();
    }

    if (globalTrackingProperties.isInsider != null) {
      indexMap[KoyalTrackingProperties.propertyCdIsProd] = (!globalTrackingProperties.isInsider!).toString();
    }

    if (globalTrackingProperties.journeyType != null) {
      indexMap[KoyalTrackingProperties.propertyJourneyType] =
          globalTrackingProperties.journeyType.translateToCorrectJourneyType();
      indexMap[KoyalTrackingProperties.propertyPropDynamicV1] = globalTrackingProperties.journeyType!;
    }
    if (globalTrackingProperties.offerId != null) {
      indexMap[KoyalTrackingProperties.propertyCdOfferId] = globalTrackingProperties.offerId.toString();
    }
    if (globalTrackingProperties.contractId != null) {
      indexMap[KoyalTrackingProperties.propertyCdContractNumber] = globalTrackingProperties.contractId.toString();
    }

    if (globalTrackingProperties.loanType != null && globalTrackingProperties.loanType!.isNotEmpty) {
      indexMap[KoyalTrackingProperties.propertyCdLoanType] = globalTrackingProperties.loanType!;
    }

    if (globalTrackingProperties.productType != null && globalTrackingProperties.productType!.isNotEmpty) {
      indexMap[KoyalTrackingProperties.propertyCdProductType] = globalTrackingProperties.productType!;
    }

    if (globalTrackingProperties.userType != null) {
      indexMap[KoyalTrackingProperties.propertyUserType] = globalTrackingProperties.userType!;
    }
    if (globalTrackingProperties.appVersion != null) {
      indexMap[KoyalTrackingProperties.propertyCdAppVersion] = globalTrackingProperties.appVersion!;
    }

    if (globalTrackingProperties.cuid != null) {
      indexMap[KoyalTrackingProperties.propertyCdCuid] = globalTrackingProperties.cuid!;
    }
    if (globalTrackingProperties.sessionID != null) {
      indexMap[KoyalTrackingProperties.propertyCdSessionID] = globalTrackingProperties.sessionID!;
    }

    if (globalTrackingProperties.eLoadCategory != null) {
      indexMap[KoyalTrackingProperties.propertyCdEloadCategory] = globalTrackingProperties.eLoadCategory!;
    }

    if (globalTrackingProperties.eBillCategory != null) {
      indexMap[KoyalTrackingProperties.propertyCdEbillCategory] = globalTrackingProperties.eBillCategory!;
    }

    if (globalTrackingProperties.transactionPaymentMethod != null) {
      indexMap[KoyalTrackingProperties.propertyCdTranPaymentMethod] =
          globalTrackingProperties.transactionPaymentMethod!;
    }

    if (globalTrackingProperties.prevScreen != null) {
      indexMap[KoyalTrackingProperties.propertyCdPrevScreen] = globalTrackingProperties.prevScreen!;
    }
    if (globalTrackingProperties.currScreen != null) {
      indexMap[KoyalTrackingProperties.propertyCdCurrentcreen] = globalTrackingProperties.currScreen!;
    }

    if (globalTrackingProperties.phoneNumber != null) {
      indexMap[KoyalTrackingProperties.propertyCdPhoneNumber] = globalTrackingProperties.phoneNumber!;
    }

    if (globalTrackingProperties.phoneNumberOnReset != null) {
      indexMap[KoyalTrackingProperties.propertyPhoneReset] = globalTrackingProperties.phoneNumberOnReset!;
    }

    if (globalTrackingProperties.repaymentAbTest != null) {
      indexMap[KoyalTrackingProperties.propertyCdAbTest] = globalTrackingProperties.repaymentAbTest!;
    }

    if (globalTrackingProperties.loanJourneyAbTest != null) {
      indexMap[KoyalTrackingProperties.propertyCdAbTest] = globalTrackingProperties.loanJourneyAbTest!;
    }

    if (globalTrackingProperties.qrCategory != null) {
      indexMap[KoyalTrackingProperties.propertyCdQrCategory] = globalTrackingProperties.qrCategory!;
    }

    if (globalTrackingProperties.referralCode != null) {
      indexMap[KoyalTrackingProperties.propertyCdReferralCode] = globalTrackingProperties.referralCode!;
    }
    if (globalTrackingProperties.timeSpend != null) {
      indexMap[KoyalTrackingProperties.propertyTimeSpend] = globalTrackingProperties.timeSpend!;
    }
    if (globalTrackingProperties.flowStage != null) {
      indexMap[KoyalTrackingProperties.propertyCdFlowStage] = globalTrackingProperties.flowStage!;
    }
    userProperty?.forEach((key, value) {
      if (!indexMap.containsKey(key) && value.isNotEmpty) {
        indexMap[key] = value;
      }
    });

    return indexMap;
  }

  Future<void> _forwardSavedEvents() async {
    final bulkEvents = await storage.getEventData();

    if (bulkEvents != null) {
      await _forwardEvents(events: bulkEvents.events);
    }
  }

  Future<void> _forwardEvents({required List<EventData> events}) async {
    if (events.isEmpty) return;
    unawaited(
      storage.deleteEventData(),
    );
    final chunks = partition(events, 99);
    for (final items in chunks) {
      unawaited(
        repository.logBulk(data: items),
      );
    }
  }

  @override
  void setCurrentScreen({required String screenName}) {}

  @override
  Future<void> cleanUp() async {
    await _forwardSavedEvents();
  }
}
