import 'package:advertising_id/advertising_id.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:installed_app_detector/installed_app_detector.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../../capp_tracking.dart';

class ForwarderRepository extends IForwarderRepository {
  final Logger logger;
  final EventForwarderApi api;
  final IPlatformService platformService;
  final MessagingStorage storage;
  final ShouldTrackProperties shouldTrackProperties;
  final InstalledAppDetector? installedAppDetector;
  final String? snappDetectionPackageName;

  ForwarderRepository({
    required this.shouldTrackProperties,
    required this.api,
    required this.platformService,
    required this.storage,
    required this.logger,
    this.installedAppDetector,
    this.snappDetectionPackageName,
  });

  @override
  Future<Either<ForwarderFailure, Unit>> log({required EventData data}) async {
    try {
      final isSnappInstalled = await isAppInstalled();

      await api.eventPost(
        forwardEventRequest: ForwardEventRequest(
          device: DeviceInfoDto(
            deviceId: await platformService.deviceId(),
            deviceModel: await platformService.deviceModel(),
            pushToken: (await storage.getPushToken())?.token,
            appVersion: (await platformService.getPackageInfo()).version,
            advertisingId: shouldTrackProperties.propertyAdvertisingId ? await AdvertisingId.id() : null,
            isSARA: shouldTrackProperties.propertyIsSara ? isSnappInstalled : null,
          ),
          timestamp: data.timestamp,
          data: data.toApi(),
        ),
      );
      return right(unit);
    } on DioError catch (e) {
      if (e.response?.statusCode == 401) {
        return left(const ForwarderFailure.unauthorized());
      }
      logger.wtf('Forwarder api request failed $e');
      return left(const ForwarderFailure.unexpected());
    } on Exception catch (e) {
      logger.wtf('Forwarder post failed $e');
      return left(const ForwarderFailure.unexpected());
    }
  }

  @override
  Future<Either<ForwarderFailure, Unit>> logBulk({required List<EventData> data}) async {
    try {
      final deviceId = await platformService.deviceId();
      final deviceModel = await platformService.deviceModel();
      final pushToken = (await storage.getPushToken())?.token;
      final appVersion = (await platformService.getPackageInfo()).version;
      final advertisingId = shouldTrackProperties.propertyAdvertisingId ? await AdvertisingId.id() : null;
      final isSnappInstalled = await isAppInstalled();

      final request = data
          .map(
            (i) => ForwardEventRequest(
              device: DeviceInfoDto(
                deviceId: deviceId,
                deviceModel: deviceModel,
                pushToken: pushToken,
                appVersion: appVersion,
                advertisingId: advertisingId,
                isSARA: shouldTrackProperties.propertyIsSara ? isSnappInstalled : null,
              ),
              timestamp: i.timestamp,
              data: i.toApi(),
            ),
          )
          .toList();

      await api.eventBulkPost(forwardEventRequest: request);
      return right(unit);
    } on DioError catch (e) {
      if (e.response?.statusCode == 401) {
        return left(const ForwarderFailure.unauthorized());
      }
      logger.wtf('Forwarder api request failed $e');
      return left(const ForwarderFailure.unexpected());
    } on Exception catch (e) {
      logger.wtf('Forwarder post failed $e');
      return left(const ForwarderFailure.unexpected());
    }
  }

  Future<bool> isAppInstalled() async {
    if (snappDetectionPackageName == null) {
      return false;
    }
    final isAppInstalled = await installedAppDetector?.isAppInstalled(snappDetectionPackageName!);
    return isAppInstalled ?? false;
  }
}
