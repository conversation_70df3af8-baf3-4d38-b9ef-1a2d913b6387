import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_tracking.dart';

enum StartupMeasurementOrigin { mainScreen, lockScreen, chooseLanguageScreen, signInScreen }

class StartupMeasurementService {
  final CappTrackingService eventTrackingService;
  final Stopwatch _stopwatch = Stopwatch();
  bool _wasStopped = false;
  StartupMeasurementService({required this.eventTrackingService});

  void startMeasure() {
    _stopwatch.start();
  }

  void stopMeasure(StartupMeasurementOrigin origin) {
    if (!_wasStopped) {
      _wasStopped = true;
      _stopwatch.stop();
      final originToString = _trackingLabel(origin);
      eventTrackingService.trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.startupMeasurement,
        eventLabel: '${_stopwatch.elapsedMilliseconds}',
        eventAction: originToString,
        userPropertyMap: {
          KoyalTrackingProperties.propertyPropDynamicV1: '${_stopwatch.elapsedMilliseconds}',
        },
      );
    }
  }

  String _trackingLabel(StartupMeasurementOrigin origin) {
    switch (origin) {
      case StartupMeasurementOrigin.mainScreen:
        return KoyalAnalyticsConstants.mainScreen;
      case StartupMeasurementOrigin.lockScreen:
        return KoyalAnalyticsConstants.lockScreen;
      case StartupMeasurementOrigin.chooseLanguageScreen:
        return KoyalAnalyticsConstants.chooseLanguageScreen;
      case StartupMeasurementOrigin.signInScreen:
        return KoyalAnalyticsConstants.signInScreen;
    }
  }
}
