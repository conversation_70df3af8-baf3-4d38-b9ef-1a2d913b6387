import 'package:koyal_shared/koyal_shared.dart';

import '../../application/tracking/tracking_event_name_modifier.dart';

/// More info in US 98719
final class AppsflyerEventNameModifier extends TrackingEventNameModifier {
  final List<KoyalEvent> _eventExceptions = const [
    KoyalEvent.sasNewTokenFirebase,
  ];

  @override
  String internalModify(
    String eventName,
    Map<String, String> properties,
  ) {
    return '$eventName${_createCategory(properties)}${_createLabel(properties)}';
  }

  String _createCategory(Map<String, String> properties) {
    final category = properties[KoyalAnalyticsConstants.category];

    return category != null ? '_$category' : '';
  }

  String _createLabel(Map<String, String> properties) {
    final label = properties[KoyalAnalyticsConstants.label1];

    return label != null ? '_$label' : '';
  }

  @override
  List<KoyalEvent> get eventExceptions => _eventExceptions;
}
