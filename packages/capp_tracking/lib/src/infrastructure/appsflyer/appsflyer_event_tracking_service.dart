import 'package:capp_appsflyer_core/capp_appsflyer_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:logger/logger.dart';

import '../../application/tracking/tracking_event_name_modifier.dart';

class AppsflyerEventTrackingService extends EventTrackingService {
  final Map<KoyalEvent, bool> eventsConfig;
  final IAppsflyerService appsflyerService;
  final Logger logger;
  final TrackingEventNameModifier eventNameModifier;

  AppsflyerEventTrackingService({
    required this.eventsConfig,
    required this.appsflyerService,
    required GlobalTrackingProperties gtp,
    required this.logger,
    required this.eventNameModifier,
  }) : super(globalTrackingProperties: gtp);

  @override
  Future trackEvent({
    required String name,
    KoyalEvent? event,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
  }) async {
    if (!shouldTrack(eventsConfig, event)) return;
    // events to appsflyer are modified by requirements in US 98719
    var eventName = eventNameModifier.modify(name, event, properties ?? {});

    // An In-App Event name must be no longer than 45 characters. Events names with more than 45 characters
    // do not appear in the dashboard, but only in the raw Data, Pull and Push APIs.
    if (eventName.length > 45) eventName = eventName.substring(0, 44);
    final empty = <String, String>{};
    return appsflyerService.logEvent(eventName, <String, String>{...properties ?? empty, ...userProperty ?? empty});
  }

  @override
  Future trackScreenEvent({
    required String screenName,
    Map<String, String>? userProperty,
  }) async {
    return appsflyerService.logEvent(screenName, <String, String>{...userProperty ?? <String, String>{}});
  }

  @override
  Future<void> setUser({
    required String userId,
    String? cuid,
    required bool isInsider,
    String? phoneNumber,
    required bool isExistingUser,
    required String signonStatus,
  }) async {
    try {
      appsflyerService.setCustomerUserId(userId);
    } on Exception catch (ex) {
      logger.e('Appsflyer: failed to set customer id ($userId)', ex);
    }
  }

  @override
  void setCurrentScreen({required String screenName}) {}

  @override
  Future<void> cleanUp() async {}
}
