import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

class CappVisibilityDetector extends StatelessWidget {
  final Widget child;
  final VoidCallback onBecameVisible;
  final VoidCallback? onBecameInvisible;

  const CappVisibilityDetector({
    required super.key,
    required this.child,
    required this.onBecameVisible,
    this.onBecameInvisible,
  });

  @override
  Widget build(BuildContext context) => VisibilityDetector(
        key: key!,
        onVisibilityChanged: (visibility) {
          if (visibility.visibleFraction == 1) {
            onBecameVisible();
          } else {
            onBecameInvisible?.call();
          }
        },
        child: child,
      );
}
