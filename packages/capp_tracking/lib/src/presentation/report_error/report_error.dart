import 'package:capp_ui_core/capp_ui.dart' as capp_ui;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../application/report_error/report_error_cubit.dart';

class ReportErrorScreen extends StatelessWidget with RouteWrapper {
  const ReportErrorScreen({Key? key}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (ctx) => context.get<ReportErrorCubit>(),
        child: this,
      );

  @override
  Widget build(BuildContext context) => BlocListener<ReportErrorCubit, ReportErrorState>(
        listener: (context, state) {
          if (state is ReportErrorRerported) {
            context.navigator.pushReplacementTyped(package: KoyalShared, screen: capp_ui.ReportErrorSuccessScreen);
          }
        },
        child: capp_ui.ReportErrorScreen(
          onSubmit: () => context.read<ReportErrorCubit>().reportError(),
        ),
      );
}
