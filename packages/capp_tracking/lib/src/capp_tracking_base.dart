import 'package:alice/alice.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../capp_tracking.dart';
import 'application/report_error/report_error_cubit.dart';
import 'presentation/report_error/report_error.dart';

abstract class CappTrackingBase extends ChildPackage {
  final CappTrackingSettings settings;

  CappTrackingBase({required this.settings});

  @override
  List<KoyalRoute> get routes => [
        KoyalRoute(
          runtimeType,
          ReportErrorScreen,
          'report_error',
          (context, args) => const ReportErrorScreen().wrappedRoute(context),
        ),
      ];

  @override
  void registerDependencies(GetIt container) {
    container
      ..registerLazySingleton<LocalViewEventTracking>(
        () => LocalViewEventTracking(container<GlobalTrackingProperties>()),
      )
      ..registerLazySingleton<CappTrackingService>(
        () => CappTrackingService(
          eventTrackingService: container<EventTrackingService>(),
          currentUserRepository: container<ICurrentUserRepository>(),
          userRepository: container<IUserRepository>(),
        ),
      )
      ..registerLazySingleton<KoyalTrackingService>(
        () => container<CappTrackingService>(),
      )
      ..registerLazySingleton(
        () => StartupMeasurementService(
          eventTrackingService: container.get<CappTrackingService>(),
        ),
      )
      ..registerLazySingleton<ReportErrorRepository>(
        () => ReportErrorRepository(
          platformService: container<IPlatformService>(),
          userRepository: container<IUserRepository>(),
          alice: container<Alice>(),
        ),
      )
      ..registerFactory(
        () => ReportErrorCubit(
          diagnosticsRepository: container<IDiagnosticsRepository>(),
          reportErrorRepository: container<ReportErrorRepository>(),
        ),
      );
    registerTrackingServices(container);
  }

  void registerTrackingServices(GetIt c) {
    // nothing to do
  }
}
