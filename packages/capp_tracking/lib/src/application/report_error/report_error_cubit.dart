import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../capp_tracking.dart';

part 'report_error_state.dart';

class ReportErrorCubit extends Cubit<ReportErrorState> {
  final IDiagnosticsRepository diagnosticsRepository;
  final ReportErrorRepository reportErrorRepository;

  ReportErrorCubit({
    required this.diagnosticsRepository,
    required this.reportErrorRepository,
  }) : super(ReportErrorInitial());

  Future<void> reportError() async {
    //we don't need to wait for result, because we don't want to inform user when it fails
    unawaited(diagnosticsRepository.sendLogs(file: await reportErrorRepository.getFileLogContent()));

    emit(ReportErrorRerported());
  }
}
