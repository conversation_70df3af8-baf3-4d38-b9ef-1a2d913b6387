import 'package:flutter/foundation.dart';
import 'package:koyal_shared/koyal_shared.dart';

/// Simply utility class to modify event name
abstract class TrackingEventNameModifier {
  String modify(
    String name,
    KoyalEvent? event,
    Map<String, String> properties,
  ) {
    // firstly handle preconditions
    if (event == null || eventId[event] == null) {
      return name;
    }

    final eventName = eventId[event] ?? name;

    if (eventExceptions.contains(event)) {
      return eventName;
    }

    return internalModify(eventName, properties);
  }

  /// template method for child`s modifications
  @protected
  String internalModify(
    String eventName,
    Map<String, String> properties,
  );

  // Some event may have exceptions for modification, child can define them.
  @protected
  List<KoyalEvent> get eventExceptions;
}
