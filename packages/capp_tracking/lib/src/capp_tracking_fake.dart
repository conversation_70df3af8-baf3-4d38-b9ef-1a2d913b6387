import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:logger/logger.dart';

import '../capp_tracking.dart';

class CappTrackingFake extends CappTrackingBase {
  CappTrackingFake(CappTrackingSettings settings) : super(settings: settings);

  @override
  void registerTrackingServices(GetIt c) {
    {
      c
        ..registerLazySingleton<FirebaseEventTrackingService>(
          () => DebugFirebaseEventTrackingService(
            firebaseAnalytics: c<FirebaseAnalytics>(),
            eventsConfig: settings.firebaseEventsConfig,
            gtp: c<GlobalTrackingProperties>(),
            shouldTrackProperties: settings.shouldTrackProperties,
          ),
        )
        ..registerLazySingleton<EventTrackingService>(
          () => DebugEventTrackingService(
            logger: c<Logger>(),
            gtp: c<GlobalTrackingProperties>(),
          ),
        )
        ..registerLazySingleton<FirebaseAnalytics>(() => FirebaseAnalytics.instance)
        ..registerLazySingleton<ICrashlyticsInitService>(DebugCrashlyticsInitService.new);
    }
  }
}
