import 'package:selfcareapi/model/event_get_response_v1.dart';
import 'package:selfcareapi/model/event_overview_get_response_v1.dart';
import 'package:selfcareapi/model/mission_get_response_v1.dart';
import 'package:selfcareapi/model/mission_type.dart';

extension EventOverviewGetResponseV1Ext on EventOverviewGetResponseV1 {
  bool get isEventStillValid => endAtLocal.isAfter(DateTime.now());

  bool get isCompleted =>
      pointsClaimed != null &&
      completeScore != null &&
      pointsClaimed! >= completeScore!;

  bool get isRewardClaimed => claimedAt != null;

  DateTime get endAtLocal {
    final result = DateTime.tryParse(endAt ?? '') ?? DateTime.now();
    return result.toLocal();
  }
}

extension MissionGetResponseV1Ext on MissionGetResponseV1 {
  bool get isCompleted => completedAt != null;

  String get iconLocal {
    switch (missionType) {
      case MissionType.biometricEnabled:
        return 'assets/svg/face_id.svg';
      case MissionType.repaymentScreenVisited:
        return 'assets/svg/repayment.svg';
      case MissionType.promotionScreenVisited:
        return 'assets/svg/promotion.svg';
      case MissionType.contractSigned:
        return 'assets/svg/sign_contract.svg';
      case null:
        return '';
    }
  }
}

extension EventGetResponseV1Ext on EventGetResponseV1 {
  bool get isEventStillValid => endAtLocal.isAfter(DateTime.now());

  bool get isCompleted =>
      pointsClaimed != null &&
      completeScore != null &&
      pointsClaimed! >= completeScore!;

  bool get isRewardClaimed => claimedAt != null;

  DateTime get endAtLocal {
    final result = DateTime.tryParse(endAt ?? '') ?? DateTime.now();
    return result.toLocal();
  }
}
