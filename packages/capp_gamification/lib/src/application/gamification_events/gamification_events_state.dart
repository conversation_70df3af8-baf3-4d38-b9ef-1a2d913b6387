import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:selfcareapi/model/event_get_response_v1.dart';

part 'gamification_events_state.freezed.dart';

@freezed
class GamificationEventsState with _$GamificationEventsState {
  const factory GamificationEventsState.loading() = _Loading;
  const factory GamificationEventsState.error(String message) = _Error;
  const factory GamificationEventsState.success(List<EventGetResponseV1> events) = _Success;
}