import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/cupertino.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:selfcareapi/model/event_get_response_v1.dart';
import 'package:selfcareapi/model/mission_get_response_v1.dart';
import 'package:selfcareapi/model/mission_type.dart';

import '../infrastructure/gamification_events/mapper/user_event_mapper.dart';
import 'tracking_constant.dart';

mixin CappGamificationTracking {
  void trackAnalyticsEvent(
    BuildContext context,
    String eventCategory,
    String eventAction,
    String eventLabel, {
    String? screenLabel,
  }) =>
      context.get<CappTrackingService>().trackGamificationEvent(
            category: eventCategory,
            action: eventAction,
            label: eventLabel,
            screenLabel: screenLabel,
          );

  void trackEntryPointWidgetEvent(BuildContext context, String eventAction) =>
      context.get<CappTrackingService>().trackGamificationEvent(
            category: KoyalTrackingCategories.rpyMain,
            action: eventAction,
            label: KoyalTrackingLabels.onbEntrPntWdgt,
          );

  void trackEntryPointBottomSheetScreen(
    BuildContext context,
    String eventCategory,
    String eventAction,
    String screenLabel,
  ) =>
      context.get<CappTrackingService>().trackGamificationEvent(
            category: eventCategory,
            action: eventAction,
            label: '',
            screenLabel: screenLabel,
          );

  void trackEntryPointBottomSheetEvent(
    BuildContext context,
    String eventCategory,
    String eventAction,
    String eventLabel,
  ) =>
      context.get<CappTrackingService>().trackGamificationEvent(
            category: eventCategory,
            action: eventAction,
            label: eventLabel,
          );

  void trackGamificationScreen(
    BuildContext context,
    EventGetResponseV1 userEvent,
    String eventCategory,
    String eventAction,
    String screenLabel,
  ) {
    final missionStatusStr = userEvent.isCompleted ? 'Completed' : 'Not complete';
    context.get<CappTrackingService>().trackGamificationEvent(
      category: eventCategory,
      action: eventAction,
      label: '',
      screenLabel: screenLabel,
      customDimensions: {
        TrackingProperties.propertyCdOnbEvRemainingTime: userEvent.endAtLocal
            .difference(DateTime.now())
            .inMilliseconds
            .toString(),
        TrackingProperties.propertyCdOnbEvMissionStatus: missionStatusStr,
        TrackingProperties.propertyCdOnbEvExpired: userEvent.endAt!,
      },
    );
  }

  void trackGamificationScreenEvent(
    BuildContext context,
    EventGetResponseV1 userEvent,
    String eventCategory,
    String eventAction,
    String eventLabel,
    {String? itemId,}
  ) {
    final endDate = DateTime.tryParse(userEvent.endAt!)?.toLocal() ?? DateTime.now();
    final missionStatusStr = userEvent.isCompleted ? 'Completed' : 'Not complete';
    context.get<CappTrackingService>().trackGamificationEvent(
      category: eventCategory,
      action: eventAction,
      label: eventLabel,
      customDimensions: {
        TrackingProperties.propertyCdOnbEvRemainingTime: endDate
            .difference(DateTime.now())
            .inMilliseconds
            .toString(),
        TrackingProperties.propertyCdOnbEvMissionStatus: missionStatusStr,
        TrackingProperties.propertyCdOnbEvExpired: userEvent.endAt!,
        TrackingProperties.propertyCdItemId: itemId ?? '',
      },
    );
  }

  void trackRewardEvent(
    BuildContext context,
    EventGetResponseV1 userEvent,
    String eventAction,
    String eventLabel,
  ) {
    context.get<CappTrackingService>().trackGamificationEvent(
      category: KoyalTrackingCategories.onbGuidelinesMain,
      action: eventAction,
      label: eventLabel,
      customDimensions: {
        TrackingProperties.propertyCdOnbEvRemainingTime: userEvent.endAtLocal
            .difference(DateTime.now())
            .inMilliseconds
            .toString(),
        TrackingProperties.propertyCdOnbEvMissionStatus: 'Completed',
        TrackingProperties.propertyCdOnbEvExpired:userEvent.endAt!,
      },
    );
  }

  void trackMissionCompletedPopupView(
    BuildContext context,
    MissionType type,
    EventGetResponseV1 userEvent,
  ) {
    var category = '';
    var missionId = '';
    switch (type) {
      case MissionType.biometricEnabled:
        category = KoyalTrackingCategories.userActionDetails;
        missionId = GamificationTrackingConstant.biometricEnabledMissionId;
        break;
      case MissionType.repaymentScreenVisited:
        category = KoyalTrackingCategories.rpyMain;
        missionId = GamificationTrackingConstant.repaymentScreenVisitedMissionId;
        break;
      case MissionType.promotionScreenVisited:
        category = KoyalTrackingCategories.promoScreen;
        missionId = GamificationTrackingConstant.promotionScreenVisitedMissionId;
        break;
      case MissionType.contractSigned:
        category = '';
        missionId = GamificationTrackingConstant.contractSignedMissionId;
        break;
    }

    trackGamificationScreenEvent(
      context,
      userEvent,
      category,
      KoyalTrackingActions.view,
      KoyalTrackingLabels.onbGdlnCpltedMsnPpup,
      itemId: missionId,
    );
  }

  void trackMissionCompletedPopupClick(
    BuildContext context,
    MissionType type,
    EventGetResponseV1 userEvent,
  ) {
    var category = '';
    switch (type) {
      case MissionType.biometricEnabled:
        category = KoyalTrackingCategories.userActionDetails;
        break;
      case MissionType.repaymentScreenVisited:
        category = KoyalTrackingCategories.rpyMain;
        break;
      case MissionType.promotionScreenVisited:
        category = KoyalTrackingCategories.promoScreen;
        break;
      case MissionType.contractSigned:
        break;
    }

    trackGamificationScreenEvent(
      context,
      userEvent,
      category,
      KoyalTrackingActions.click,
      KoyalTrackingLabels.onbGdlnExpOtrMsn,
    );
  }

  void trackMissionClick(
    BuildContext context,
    MissionGetResponseV1 mission,
    EventGetResponseV1 userEvent,
  ) {
    var trackingLabel = '';
    switch (mission.missionType!) {
      case MissionType.biometricEnabled:
        trackingLabel = KoyalTrackingLabels.actBiometricCta;
      case MissionType.repaymentScreenVisited:
        trackingLabel = KoyalTrackingLabels.accRepayCta;
      case MissionType.promotionScreenVisited:
        trackingLabel = KoyalTrackingLabels.visitProhubCta;
      case MissionType.contractSigned:
        trackingLabel = KoyalTrackingLabels.fnshContractCta;
    }

    trackGamificationScreenEvent(
      context,
      userEvent,
      KoyalTrackingCategories.onbGuidelinesMain,
      KoyalTrackingActions.click,
      trackingLabel,
    );
  }
}
