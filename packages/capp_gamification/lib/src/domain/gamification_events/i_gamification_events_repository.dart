import 'package:dartz/dartz.dart';
import 'package:selfcareapi/model/event_get_response_v1.dart';
import 'package:selfcareapi/model/mission_type.dart';
import 'package:selfcareapi/model/user_event_overview_get_response_v1.dart';

import '../failure/user_event_failure.dart';

abstract class IGamificationEventsRepository {
  Future<Either<UserEventFailure, UserEventOverviewGetResponseV1>> loadEventOverview({
    required bool shouldInitialize,
    required List<MissionType> completedMissions,
  });

  Future<Either<UserEventFailure, List<EventGetResponseV1>>> loadListEvents();

  Future<Either<UserEventFailure, void>> completeMission(MissionType mission);
}
