import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_event_failure.freezed.dart';

@freezed
class UserEventFailure with _$UserEventFailure {
  const factory UserEventFailure.badRequest() = _BadRequest; // 400
  const factory UserEventFailure.unauthorized() = _Unauthorized; // 401
  const factory UserEventFailure.forbidden() = _Forbidden; // 403
  const factory UserEventFailure.exception() = _$Exception; // 500
  const factory UserEventFailure.emptyEvents() = _$EmptyEvents; // 500
}