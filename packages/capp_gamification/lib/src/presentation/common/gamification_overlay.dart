import 'package:animations/animations.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class GamificationOverlay extends StatelessWidget {
  final PrimaryButton? primaryButton;
  final SecondaryButton? secondaryButton;
  final TertiaryButton? tertiaryButton;
  final String? title;
  final Widget? body;
  final TextAlign? titleAlign;
  final Widget? image;

  const GamificationOverlay({
    Key? key,
    this.title,
    this.body,
    this.primaryButton,
    this.secondaryButton,
    this.tertiaryButton,
    this.titleAlign,
    this.image,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: KoyalPadding.paddingNormal),
        backgroundColor: Colors.transparent,
        child: dialogContent(context),
      );

  Widget dialogContent(BuildContext context) {
    return KoyalSemantics(
      customIdentifier: 'GamificationOverlay',
      title: title,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (image != null)
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
              child: image,
            ),
          DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: image != null ? const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ) : BorderRadius.circular(8),
              color: ColorTheme.of(context).backgroundColor,
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 24),
                  if (title != null && title!.isNotEmpty)
                    SizedBox(
                      width: double.infinity,
                      child: KoyalText.subtitle1(
                        color: ColorTheme.of(context).defaultTextColor,
                        title!,
                        textAlign: titleAlign ?? TextAlign.center,
                      ),
                    ),
                  const SizedBox(height: 4),
                  body ?? const SizedBox.shrink(),
                  const SizedBox(height: 12),
                  VerticalButtonsLayout(
                    primaryButton: primaryButton,
                    secondaryButton: secondaryButton,
                    tertiaryButton: tertiaryButton,
                  ),
                  const SizedBox(height: 12),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Future<T?> showGamificationOverlay<T>(
  BuildContext context, {
  Key? key,
  Widget? image,
  String? title,
  Widget? body,
  PrimaryButton Function(BuildContext)? primaryButtonBuilder,
  SecondaryButton Function(BuildContext)? secondaryButtonBuilder,
  TertiaryButton Function(BuildContext)? tertiaryButtonBuilder,
  bool dismissible = false,
  TextAlign? titleAlign,
}) async {
  return showModal<T>(
    context: context,
    configuration: FadeScaleTransitionConfiguration(barrierDismissible: dismissible),
    builder: (context) => KoyalWillPopScope(
      child: GamificationOverlay(
        key: key,
        image: image,
        title: title,
        body: body,
        primaryButton: primaryButtonBuilder?.call(context),
        secondaryButton: secondaryButtonBuilder?.call(context),
        tertiaryButton: tertiaryButtonBuilder?.call(context),
        titleAlign: titleAlign,
      ),
      onWillPop: () async => dismissible,
    ),
  );
}
