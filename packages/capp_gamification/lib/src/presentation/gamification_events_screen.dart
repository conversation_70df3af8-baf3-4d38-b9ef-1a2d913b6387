import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart' hide ChipTheme;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:selfcareapi/model/event_get_response_v1.dart';
import 'package:selfcareapi/model/user_event_overview_get_response_v1.dart';

import '../../l10n/i18n.dart';
import '../application/gamification_entry_point/gamification_entry_point_cubit.dart';
import '../application/gamification_events/gamification_events_cubit.dart';
import '../application/gamification_events/gamification_events_state.dart';
import '../core/images.dart';
import '../domain/gamification_events/i_gamification_events_repository.dart';
import '../tracking/capp_gamification_tracking.dart';
import 'common/gamification_overlay.dart';
import 'gamification_events_screen_loading.dart';
import 'widgets/event_header.dart';
import 'widgets/mission_list.dart';

class GamificationEventsScreen extends StatefulWidget with RouteWrapper {
  static const screenName = 'gamification_events_screen';

  const GamificationEventsScreen({super.key});

  @override
  State<GamificationEventsScreen> createState() =>
      _GamificationEventsScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    return MultiBlocProvider(
      key: const Key('_gamification_events_screen_root_'),
      providers: [
        BlocProvider.value(
          value: context.get<GamificationEntryPointCubit>()..initUserEventData(),
        ),
        BlocProvider(
          create: (_) => GamificationEventsCubit(
            eventsRepository: context.get<IGamificationEventsRepository>(),
          ),
        ),
      ],
      child: this,
    );
  }
}

class _GamificationEventsScreenState extends State<GamificationEventsScreen> with CappGamificationTracking {
  StreamSubscription? sup;
  bool shouldDisplayRewardPopup = false;

  @override
  void initState() {
    super.initState();

    sup = context.get<GamificationEntryPointCubit>().stream.listen((state) {
      state.when(
        loading: () {},
        error: (_) {
          showErrorPopup(
            context,
            L10nCappGamification.of(context).notEligibleErrorMessage,
          );
        },
        success: (isCompletedAllMission, data) {
          if (_isValidEvent(data!)) {
            context.read<GamificationEventsCubit>().loadEventsData();
            shouldDisplayRewardPopup = isCompletedAllMission!;
          } else {
            showErrorPopup(
              context,
              L10nCappGamification.of(context).notEligibleErrorMessage,
            );
          }
        },
      );
    });
  }

  @override
  void dispose() {
    sup?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      safeAreaCustomSettings: const SafeAreaCustomSettings(bottom: false),
      safeAreaBarCustomSettings: const SafeAreaCustomSettings(bottom: false),
      appBar: KoyalAppBar(
        title: L10nCappGamification.of(context).event,
        showBottomDivider: false,
      ),
      body: BlocBuilder<GamificationEventsCubit, GamificationEventsState>(
        builder: (context, state) {
          return state.when(
            loading: () {
              return const GamificationEventsScreenLoading();
            },
            error: (message) {
              return GenericError(
                primaryButtonText: L10nCappUi.of(context).tryAgain,
                primaryButtonOnClick: () {
                  context.get<CappTrackingService>().trackErrorReloadClick(GamificationEventsScreen.screenName);
                  context.read<GamificationEventsCubit>().showLoading();
                  context.read<GamificationEventsCubit>().loadEventsData();
                },
                secondaryButtonOnClick: () => context.navigator.toMainScreen(),
                errorId: GamificationEventsScreen.screenName,
              );
            },
            success: (events) {
              final event = events[0];
              return FirstShown(
                onShown: () {
                  trackGamificationScreen(
                    context,
                    event,
                    KoyalTrackingCategories.screenView,
                    KoyalTrackingActions.view,
                    KoyalTrackingCategories.onbGuidelinesMain,
                  );
                },
                child: CappVisibilityDetector(
                  key: const Key('gamification-events-screen'),
                  onBecameVisible: () {
                    if(shouldDisplayRewardPopup) {
                      _showPopupReward(context, event);
                      shouldDisplayRewardPopup = false;
                    }
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        EventHeader(
                          userEvent: event,
                          onEventEnd: () {
                            setState(() {
                              context.read<GamificationEntryPointCubit>().loadEventsOverview();
                            });
                          },
                        ),
                        MissionList(userEvent: event),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Future<void> showErrorPopup(BuildContext context, String message) async {
    return showGamificationOverlay(
      context,
      title: L10nCappGamification.of(context).warning,
      body: KoyalText.body1(
        message,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (ctx) => PrimaryButton(
        onPressed: () => context.navigator.toMainScreen(),
        text: L10nCappGamification.of(context).close,
      ),
    );
  }

  bool _isValidEvent(UserEventOverviewGetResponseV1 eventOverview) {
    return eventOverview.events != null && eventOverview.events!.isNotEmpty;
  }

  Future<void> _showPopupReward(
    BuildContext context,
    EventGetResponseV1 event,
  ) async {
    trackRewardEvent(
      context,
      event,
      KoyalTrackingActions.view,
      KoyalTrackingLabels.rewardPpup,
    );
    return showGamificationOverlay(
      context,
      title: L10nCappGamification.of(context).congratulationRewardTitle,
      dismissible: true,
      image: Image.asset(
        GamificationImage.rewardBanner,
        package: 'capp_gamification',
        fit: BoxFit.cover,
        height: 180,
      ),
      body: KoyalPadding.largeHorizontal(
        child: KoyalText.body2(
          L10nCappGamification.of(context).congratulationRewardMessage,
          textAlign: TextAlign.center,
          color: HciColors.supplementary500,
        ),
      ),
      primaryButtonBuilder: (ctx) => PrimaryButton(
        onPressed: () => _onViewGuideRewardPressed(context, event),
        text: L10nCappGamification.of(context).guideUse,
      ),
      tertiaryButtonBuilder: (ctx) => TertiaryButton(
        text: L10nCappGamification.of(context).backToHome,
        onPressed: () => context.navigator.toMainScreen(),
      ),
    );
  }

  void _onViewGuideRewardPressed(
    BuildContext context,
    EventGetResponseV1 event,
  ) {
    if (event.rewardDeeplink != '') {
      trackRewardEvent(
        context,
        event,
        KoyalTrackingActions.click,
        KoyalTrackingLabels.useNow,
      );
      context.get<GamificationEntryPointCubit>().loadEventsOverview();
      Navigator.pop(context);
      context.get<IDeeplinkService>().deeplinkOrLaunch(event.rewardDeeplink!, context);
    }
  }
}
