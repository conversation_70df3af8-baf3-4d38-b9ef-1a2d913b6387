// DO NOT EDIT. This is code generated via package:gen_lang/generate.dart

import 'dart:async';

import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:multiple_localization/multiple_localization.dart';

import 'messages_all.dart';

class L10nCappGamification {
  static Map<String, Map<String, String>> translations = {};
  static late Locale _locale;
  static bool showKeys = false;

  String? overridenTranslation(
      String key, String languageCode, String? countryCode) {
    var languageTag = getLanguageTag(languageCode, countryCode);
    var languageTranslations = translations[languageTag];
    if (languageTranslations != null &&
        languageTranslations.containsKey(key) &&
        languageTranslations[key]!.isNotEmpty) {
      return languageTranslations[key]!;
    }

    return null;
  }

  String getLanguageTag(String languageCode, String? countryCode) {
    if (countryCode == null) {
      return languageCode;
    }

    return '$languageCode-$countryCode';
  }

  static const GeneratedLocalizationsDelegate delegate = GeneratedLocalizationsDelegate();

  static L10nCappGamification of(BuildContext context) {
    return Localizations.of<L10nCappGamification>(context, L10nCappGamification)!;
  }
  
  static L10nCappGamification load(Locale locale) {
    _locale = locale;
    return L10nCappGamification();
  }
  
  String get backToAnotherMission {
    if(showKeys){
      return 'capp_gamification.back_to_another_mission';
    }
    var ot = overridenTranslation(
        'capp_gamification.back_to_another_mission', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quay lại để xem nhiệm vụ khác nhé", name: 'capp_gamification.back_to_another_mission');
    return result != '' ? result : 'back_to_another_mission';
  }

  String get completed {
    if(showKeys){
      return 'capp_gamification.completed';
    }
    var ot = overridenTranslation(
        'capp_gamification.completed', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã hoàn thành", name: 'capp_gamification.completed');
    return result != '' ? result : 'completed';
  }

  String completedPercent(dynamic completed, dynamic total) {
  if(showKeys){
      return 'capp_gamification.completed_percent';
    }
    var ot = overridenTranslation(
        'capp_gamification.completed_percent', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Đã hoàn thành ${completed}/${total}", name: 'capp_gamification.completed_percent', args: [completed, total]);
  }

  String get congratulationAchievementMessage {
    if(showKeys){
      return 'capp_gamification.congratulation_achievement_message';
    }
    var ot = overridenTranslation(
        'capp_gamification.congratulation_achievement_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục làm nhiệm vụ để có cơ hội nhận ưu đãi 30k khi thanh toán khoản vay", name: 'capp_gamification.congratulation_achievement_message');
    return result != '' ? result : 'congratulation_achievement_message';
  }

  String get congratulationAchievementTitle {
    if(showKeys){
      return 'capp_gamification.congratulation_achievement_title';
    }
    var ot = overridenTranslation(
        'capp_gamification.congratulation_achievement_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chúc mừng! Bạn vừa hoàn thành\n1 nhiệm vụ", name: 'capp_gamification.congratulation_achievement_title');
    return result != '' ? result : 'congratulation_achievement_title';
  }

  String get congratulationRewardMessage {
    if(showKeys){
      return 'capp_gamification.congratulation_reward_message';
    }
    var ot = overridenTranslation(
        'capp_gamification.congratulation_reward_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn đã hoàn thành tất cả nhiệm vụ! Xem ngay cách sử dụng ưu đãi 30k", name: 'capp_gamification.congratulation_reward_message');
    return result != '' ? result : 'congratulation_reward_message';
  }

  String get congratulationRewardTitle {
    if(showKeys){
      return 'capp_gamification.congratulation_reward_title';
    }
    var ot = overridenTranslation(
        'capp_gamification.congratulation_reward_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chúc mừng!", name: 'capp_gamification.congratulation_reward_title');
    return result != '' ? result : 'congratulation_reward_title';
  }

  String get day {
    if(showKeys){
      return 'capp_gamification.day';
    }
    var ot = overridenTranslation(
        'capp_gamification.day', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày", name: 'capp_gamification.day');
    return result != '' ? result : 'day';
  }

  String get doItLater {
    if(showKeys){
      return 'capp_gamification.do_it_later';
    }
    var ot = overridenTranslation(
        'capp_gamification.do_it_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Để sau", name: 'capp_gamification.do_it_later');
    return result != '' ? result : 'do_it_later';
  }

  String get endAfter {
    if(showKeys){
      return 'capp_gamification.end_after';
    }
    var ot = overridenTranslation(
        'capp_gamification.end_after', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kết thúc sau", name: 'capp_gamification.end_after');
    return result != '' ? result : 'end_after';
  }

  String get eventExpired {
    if(showKeys){
      return 'capp_gamification.event_expired';
    }
    var ot = overridenTranslation(
        'capp_gamification.event_expired', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã kết thúc", name: 'capp_gamification.event_expired');
    return result != '' ? result : 'event_expired';
  }

  String get event {
    if(showKeys){
      return 'capp_gamification.event';
    }
    var ot = overridenTranslation(
        'capp_gamification.event', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sự kiện", name: 'capp_gamification.event');
    return result != '' ? result : 'event';
  }

  String get eventHeadTitle {
    if(showKeys){
      return 'capp_gamification.event_head_title';
    }
    var ot = overridenTranslation(
        'capp_gamification.event_head_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giảm 30k thanh toán khoản vay khi hoàn thành các nhiệm vụ sau", name: 'capp_gamification.event_head_title');
    return result != '' ? result : 'event_head_title';
  }

  String get getReward {
    if(showKeys){
      return 'capp_gamification.get_reward';
    }
    var ot = overridenTranslation(
        'capp_gamification.get_reward', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhận thưởng", name: 'capp_gamification.get_reward');
    return result != '' ? result : 'get_reward';
  }

  String get hour {
    if(showKeys){
      return 'capp_gamification.hour';
    }
    var ot = overridenTranslation(
        'capp_gamification.hour', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giờ", name: 'capp_gamification.hour');
    return result != '' ? result : 'hour';
  }

  String get minute {
    if(showKeys){
      return 'capp_gamification.minute';
    }
    var ot = overridenTranslation(
        'capp_gamification.minute', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phút", name: 'capp_gamification.minute');
    return result != '' ? result : 'minute';
  }

  String get mission1 {
    if(showKeys){
      return 'capp_gamification.mission_1';
    }
    var ot = overridenTranslation(
        'capp_gamification.mission_1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kích hoạt đăng nhập bằng sinh trắc học", name: 'capp_gamification.mission_1');
    return result != '' ? result : 'mission_1';
  }

  String get missionCompleted {
    if(showKeys){
      return 'capp_gamification.mission_completed';
    }
    var ot = overridenTranslation(
        'capp_gamification.mission_completed', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoàn thành nhiệm vụ!", name: 'capp_gamification.mission_completed');
    return result != '' ? result : 'mission_completed';
  }

  String get missionList {
    if(showKeys){
      return 'capp_gamification.mission_list';
    }
    var ot = overridenTranslation(
        'capp_gamification.mission_list', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Danh sách nhiệm vụ", name: 'capp_gamification.mission_list');
    return result != '' ? result : 'mission_list';
  }

  String get notStarted {
    if(showKeys){
      return 'capp_gamification.not_started';
    }
    var ot = overridenTranslation(
        'capp_gamification.not_started', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chưa thực hiện", name: 'capp_gamification.not_started');
    return result != '' ? result : 'not_started';
  }

  String get second {
    if(showKeys){
      return 'capp_gamification.second';
    }
    var ot = overridenTranslation(
        'capp_gamification.second', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giây", name: 'capp_gamification.second');
    return result != '' ? result : 'second';
  }

  String get guideUse {
    if(showKeys){
      return 'capp_gamification.guide_use';
    }
    var ot = overridenTranslation(
        'capp_gamification.guide_use', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hướng dẫn sử dụng", name: 'capp_gamification.guide_use');
    return result != '' ? result : 'guide_use';
  }

  String get viewAnotherMission {
    if(showKeys){
      return 'capp_gamification.view_another_mission';
    }
    var ot = overridenTranslation(
        'capp_gamification.view_another_mission', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem nhiệm vụ khác", name: 'capp_gamification.view_another_mission');
    return result != '' ? result : 'view_another_mission';
  }

  String get viewReward {
    if(showKeys){
      return 'capp_gamification.view_reward';
    }
    var ot = overridenTranslation(
        'capp_gamification.view_reward', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem phần thưởng", name: 'capp_gamification.view_reward');
    return result != '' ? result : 'view_reward';
  }

  String get close {
    if(showKeys){
      return 'capp_gamification.close';
    }
    var ot = overridenTranslation(
        'capp_gamification.close', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đóng", name: 'capp_gamification.close');
    return result != '' ? result : 'close';
  }

  String get warning {
    if(showKeys){
      return 'capp_gamification.warning';
    }
    var ot = overridenTranslation(
        'capp_gamification.warning', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông báo", name: 'capp_gamification.warning');
    return result != '' ? result : 'warning';
  }

  String get notEligibleErrorMessage {
    if(showKeys){
      return 'capp_gamification.notEligibleErrorMessage';
    }
    var ot = overridenTranslation(
        'capp_gamification.notEligibleErrorMessage', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn không thoả mãn điều kiện chương trình", name: 'capp_gamification.notEligibleErrorMessage');
    return result != '' ? result : 'notEligibleErrorMessage';
  }

  String get opportunityReceive30K {
    if(showKeys){
      return 'capp_gamification.opportunityReceive30k';
    }
    var ot = overridenTranslation(
        'capp_gamification.opportunityReceive30k', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cơ hội nhận 30k", name: 'capp_gamification.opportunityReceive30k');
    return result != '' ? result : 'opportunityReceive30k';
  }

  String get backToHome {
    if(showKeys){
      return 'capp_gamification.backToHome';
    }
    var ot = overridenTranslation(
        'capp_gamification.backToHome', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quay về Trang chủ", name: 'capp_gamification.backToHome');
    return result != '' ? result : 'backToHome';
  }

  String get missionProgress {
    if(showKeys){
      return 'capp_gamification.missionProgress';
    }
    var ot = overridenTranslation(
        'capp_gamification.missionProgress', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiến độ thực hiện nhiệm vụ", name: 'capp_gamification.missionProgress');
    return result != '' ? result : 'missionProgress';
  }

  String get settingBiometricTitle {
    if(showKeys){
      return 'capp_gamification.settingBiometricTitle';
    }
    var ot = overridenTranslation(
        'capp_gamification.settingBiometricTitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thiết lập sinh trắc học", name: 'capp_gamification.settingBiometricTitle');
    return result != '' ? result : 'settingBiometricTitle';
  }

  String get settingBiometricContent {
    if(showKeys){
      return 'capp_gamification.settingBiometricContent';
    }
    var ot = overridenTranslation(
        'capp_gamification.settingBiometricContent', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng truy cập cài đặt của thiết bị bạn đang sử dụng để kích hoạt mở ứng dụng bằng vân tay/ khuôn mặt", name: 'capp_gamification.settingBiometricContent');
    return result != '' ? result : 'settingBiometricContent';
  }

  String get accessSettingOfDevice {
    if(showKeys){
      return 'capp_gamification.accessSettingOfDevice';
    }
    var ot = overridenTranslation(
        'capp_gamification.accessSettingOfDevice', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Truy cập cài đặt của thiết bị", name: 'capp_gamification.accessSettingOfDevice');
    return result != '' ? result : 'accessSettingOfDevice';
  }

  String get cancel {
    if(showKeys){
      return 'capp_gamification.cancel';
    }
    var ot = overridenTranslation(
        'capp_gamification.cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bỏ qua", name: 'capp_gamification.cancel');
    return result != '' ? result : 'cancel';
  }



  Map<String, String> get _translate => <String, String>{
		'back_to_another_mission' : backToAnotherMission,
		'completed' : completed,
		'congratulation_achievement_message' : congratulationAchievementMessage,
		'congratulation_achievement_title' : congratulationAchievementTitle,
		'congratulation_reward_message' : congratulationRewardMessage,
		'congratulation_reward_title' : congratulationRewardTitle,
		'day' : day,
		'do_it_later' : doItLater,
		'end_after' : endAfter,
		'event_expired' : eventExpired,
		'event' : event,
		'event_head_title' : eventHeadTitle,
		'get_reward' : getReward,
		'hour' : hour,
		'minute' : minute,
		'mission_1' : mission1,
		'mission_completed' : missionCompleted,
		'mission_list' : missionList,
		'not_started' : notStarted,
		'second' : second,
		'guide_use' : guideUse,
		'view_another_mission' : viewAnotherMission,
		'view_reward' : viewReward,
		'close' : close,
		'warning' : warning,
		'notEligibleErrorMessage' : notEligibleErrorMessage,
		'opportunityReceive30k' : opportunityReceive30K,
		'backToHome' : backToHome,
		'missionProgress' : missionProgress,
		'settingBiometricTitle' : settingBiometricTitle,
		'settingBiometricContent' : settingBiometricContent,
		'accessSettingOfDevice' : accessSettingOfDevice,
		'cancel' : cancel,

  };

  @deprecated
  String tr(String key) {
    return _translate[key] ?? key;
  }
}

class GeneratedLocalizationsDelegate extends LocalizationsDelegate<L10nCappGamification> {
  const GeneratedLocalizationsDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
			Locale("vi", "VN"),
			Locale("en", "VN"),

    ];
  }

  LocaleListResolutionCallback listResolution({Locale? fallback}) {
    return (locales, supported) {
      if (locales == null || locales.isEmpty) {
        return fallback ?? supported.first;
      } else {
        return _resolve(locales.first, fallback, supported);
      }
    };
  }

  LocaleResolutionCallback resolution({Locale? fallback}) {
    return (locale, supported) {
      return _resolve(locale, fallback, supported);
    };
  }

  Locale _resolve(Locale? locale, Locale? fallback, Iterable<Locale> supported) {
    if (locale == null || !isSupported(locale)) {
      return fallback ?? supported.first;
    }

    final Locale languageLocale = Locale(locale.languageCode, "");
    if (supported.contains(locale)) {
      return locale;
    } else if (supported.contains(languageLocale)) {
      return languageLocale;
    } else {
      final Locale fallbackLocale = fallback ?? supported.first;
      return fallbackLocale;
    }
  }

  @override
  Future<L10nCappGamification> load(Locale locale) {
    return MultipleLocalizations.load(
        initializeMessages, locale, (l) => L10nCappGamification.load(locale),
        setDefaultLocale: true);
  }

  @override
  bool isSupported(Locale locale) =>
    locale != null && supportedLocales.contains(locale);

  @override
  bool shouldReload(GeneratedLocalizationsDelegate old) => false;
}

// ignore_for_file: unnecessary_brace_in_string_interps
