import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flagsmith/flagsmith.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:mocktail/mocktail.dart';

class TestFeatureFlagRepository extends Mock implements IFeatureFlagRepository {
  @override
  StreamController<void> featureFlagsChanged = StreamController<void>.broadcast();

  @override
  Flag? getFlag(String featureName, {bool appStartFlag = false, bool trackFlagRead = false}) {
    return (appStartFlag ? initialFlags : cachedFlags).firstWhereOrNull((element) => element.key == featureName);
  }
}
