import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_shared/koyal_shared.dart';

void main() {
  test('Filling map with common events', () async {
    Map<KoyalEvent, bool> firebaseData;
    Map<KoyalEvent, bool> commonData;

    commonData = {
      KoyalEvent.undefined: true,
      KoyalEvent.repaymentOwnTransactionSuccessView: true,
    };

    firebaseData = {
      KoyalEvent.repaymentOwnContractSelectionClickCelContract: false,
      KoyalEvent.repaymentOwnContractSelectionClickContinue: false,
      KoyalEvent.repaymentOwnMethodSelectionClickOnline: false,
      KoyalEvent.repaymentOwnMethodSelectionClickPayByCash: false,
      KoyalEvent.repaymentOwnMethodSelectionClickBankTransfer: false,
    };
    expect(firebaseData.containsKey(KoyalEvent.undefined), false);

    Map<KoyalEvent, bool> updateConfig({
      required Map<KoyalEvent, bool> data,
      required Map<KoyalEvent, bool> dataToUpdate,
    }) {
      dataToUpdate.forEach((key, value) {
        data[key] = value;

        if (data.containsKey(key)) {
          data.update(key, (_) => value);
        }
      });

      return data;
    }

    firebaseData = updateConfig(data: commonData, dataToUpdate: firebaseData);
    expect(firebaseData.containsKey(KoyalEvent.undefined), true);
    expect(firebaseData.length == 7, true);
  });
}
