import 'dart:io';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';
import 'package:uuid/uuid.dart';

import '../../capp_config_core.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  SplashScreenState createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  late final SvgPicture _splashLogo;
  double widget1Opacity = 1;
  DateTime timestampStart = DateTime.now();

  @override
  void initState() {
    _splashLogo = SvgPicture.asset(
      'packages/capp_config_core/assets/svg/splash_logo.svg',
    );

    context.read<DeeplinkResolverBloc>().add(const DeeplinkResolverEvent.init());

    // reactivate lock status behavior
    context.read<LockStatusBloc>().add(const LockStatusEvent.setEnabled(enabled: true));

    context.get<ApiLocker>().unblock(didRefresh: false);

    context.read<AuthBloc>().add(
          const AuthEvent.init(),
        );
    super.initState();
    Future.delayed(const Duration(milliseconds: 50), () {
      widget1Opacity = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final content = AnimatedOpacity(
      opacity: widget1Opacity,
      duration: const Duration(milliseconds: 250),
      child: Container(
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _splashLogo,
              KoyalProgressIndicator.mid(
                color: ColorTheme.of(context).primaryColor,
              ),
            ],
          ),
        ),
      ),
    );

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state.status == LoadingState.isCompleted && state.redirect != null) {
          context.get<IFirebasePerformanceMonitoring>().stopTrace(TraceType.appStart);
          _trackSessionStart(context, timestampStart);
          final redirect = state.redirect;
          if (redirect == null) {
            return;
          }
          switch (redirect) {
            case RedirectTo.ongoingMaintenanceWindow:
              context.navigator.pushReplacementTyped(
                package: KoyalShared,
                screen: OngoingMaintenanceScreen,
                arguments: MaintenanceScreenArguments(
                  endDate: state.maintenanceWindow?.maintenanceFinish,
                ),
              );
              break;
            case RedirectTo.mainScreen:
              final authRoute = context.isFlagEnabledRead(FeatureFlag.onboardingV4Bos)
                  ? 'AuthenticationInitialScreen'
                  : 'AuthLandingScreen';
              context.navigator.pushNamedAndRemoveUntilFromPackage(
                'CappAuth',
                authRoute,
                'CappAuth',
                authRoute,
                arguments: AuthLandingScreenArguments(
                  logoutReason: state.logoutReason,
                ),
              );
              break;
            case RedirectTo.onboardingScreen:
              {
                if (context.read<LocalizationBloc>().state.supportedLocales.length > 1 &&
                    context.isFlagEnabledRead(FeatureFlag.onboardingLanguageSelection)) {
                  context.navigator.pushReplacement(
                    path: context.isFlagEnabledRead(FeatureFlag.languageSelectionScreen)
                        ? NavigatorPath.cappOnboardingCore.onboardingLanguageSelectionScreen
                        : NavigatorPath.cappOnboardingCore.onboardingChooseLanguageScreen,
                  );
                } else {
                  context.navigator.pushReplacement(path: NavigatorPath.cappOnboarding.onboardingCarouselScreen);
                }
              }
              break;

            case RedirectTo.signInScreen:
              {
                context.navigator.pushReplacementFromPackage(
                  package: 'CappHome',
                  screen: 'MainScreen',
                  arguments: MainScreenArguments(initialTab: TabItem.home),
                );
                context.navigateToSignInScreen();
                break;
              }
            case RedirectTo.forceUpgrade:
              context.get<IAppVersion>().update(value: true);
              break;
            case RedirectTo.logout:
              // if the user is logged in, but the last login date is too far in past
              // the user needs to be logged out while on splash screen (pushing logout screen, but not calling any BLOCs)
              break;
            case RedirectTo.lockScreen:
              {
                context
                    .read<LockStatusBloc>()
                    .add(const LockStatusEvent.setLock(locked: true, replaceCurrentScreen: true));
                break;
              }
            case RedirectTo.unsupportedServices:
              showKoyalOverlay<void>(
                context,
                title: L10nKoyalShared.of(context).googleServicesNotSupported,
                body: KoyalText.body2(
                  color: ColorTheme.of(context).defaultTextColor,
                  L10nKoyalShared.of(context).googleServicesNotSupportedDescription,
                ),
                dismissible: false,
                primaryButtonBuilder: (c) => PrimaryButton(
                  text: L10nKoyalShared.of(context).okGotIt,
                  onPressed: SystemNavigator.pop,
                ),
              );
              break;
            case RedirectTo.initializationFailed:
              showKoyalOverlay<void>(
                context,
                title: L10nKoyalShared.of(context).error,
                body: FutureBuilder<List<ConnectivityResult>>(
                  future: Connectivity().checkConnectivity(),
                  builder: (context, snapshot) {
                    return Column(
                      children: [
                        if (snapshot.data?.contains(ConnectivityResult.none) == false)
                          KoyalText.body2(
                            color: ColorTheme.of(context).defaultTextColor,
                            L10nKoyalShared.of(context).errorInitialization,
                          )
                        else
                          KoyalText.body3(
                            L10nKoyalShared.of(context).errorCheckInternetConnection,
                            color: ColorTheme.of(context).errorTextColor,
                          ),
                        if (kDebugMode && state.exceptions != null)
                          KoyalPadding.normalVertical(
                            child: KoyalText.body3(color: ColorTheme.of(context).defaultTextColor, state.exceptions!),
                          ),
                      ],
                    );
                  },
                ),
                dismissible: false,
                primaryButtonBuilder: (c) => PrimaryButton(
                  text: L10nKoyalShared.of(context).okGotIt,
                  onPressed: () {
                    // close dialog
                    c.navigator.pop();
                    // close app
                    _closeApp();
                  },
                ),
              );
              break;
            case RedirectTo.jailbreakDetected:
              showKoyalOverlay<void>(
                context,
                title: L10nKoyalShared.of(context).jailbreakDetected,
                body: KoyalText.body2(
                  color: ColorTheme.of(context).defaultTextColor,
                  L10nKoyalShared.of(context).jailbreakDetectedDescription,
                ),
                dismissible: false,
                primaryButtonBuilder: (c) => PrimaryButton(
                  text: L10nKoyalShared.of(context).okGotIt,
                  onPressed: _closeApp,
                ),
              );

              break;
          }
        }
      },
      child: GmaPlatform.isAndroid
          ?
          //ignore: koyal-scaffold
          Scaffold(
              backgroundColor: Colors.white,
              body: content,
            )
          : content,
    );
  }

  void _closeApp() => Platform.isIOS ? exit(0) : SystemNavigator.pop();

  void _trackSessionStart(BuildContext context, DateTime timestampStart) {
    final timestampEnd = DateTime.now();
    final sessionTimestamp = timestampEnd.difference(timestampStart);

    const uuid = Uuid();
    final sessionID = uuid.v4();
    final userMap = {
      'session_timestamp': '${sessionTimestamp.inSeconds}',
      KoyalTrackingLabels.sessionID: sessionID,
    };

    if (context.get<GlobalTrackingProperties>().sessionID == null) {
      context.get<GlobalTrackingProperties>().sessionID = sessionID;

      context.get<CappTrackingService>().trackAnalyticsEvent(
            eventCategory: KoyalTrackingCategories.gaSession,
            eventAction: KoyalTrackingActions.startSession,
            eventLabel: KoyalTrackingLabels.sessionID,
            userPropertyMap: userMap,
          );
      context.get<CappTrackingService>().trackAnalyticsEvent(
            eventCategory: KoyalTrackingCategories.gaSession,
            eventAction: KoyalTrackingActions.startSession,
            eventLabel: KoyalTrackingActions.startSession,
            userPropertyMap: userMap,
          );
    }
  }
}
