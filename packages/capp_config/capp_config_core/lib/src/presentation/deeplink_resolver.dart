// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:capp_direct_debit_core/capp_direct_debit_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_golden_core/capp_golden_core.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_live_activities_core/capp_live_activities_core.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_loan_signature_core/capp_loan_signature_core.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:capp_transaction_signature_core/capp_transaction_signature_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:capp_vas_core/capp_vas_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:navigator/navigator.dart';

import '../../capp_config_core.dart';
import '../application/deeplinks/handling/deeplink_navigation.dart';
import '../infrastructure/deeplinks/handling/deeplink_navigation_index.dart';
import '../infrastructure/deeplinks/handling/deeplink_navigation_utils.dart';

class DeeplinkResolver extends StatelessWidget {
  final Widget? child;
  const DeeplinkResolver({this.child, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocListener<DeeplinkResolverBloc, DeeplinkResolverState>(
      listener: (context, state) async {
        context.get<DeeplinkResolverBloc>().add(const DeeplinkResolverEvent.reset());
        if (state.isError) {
          showToast(context, L10nCappUi.of(context).deeplinkCantResolve);
        }
        await handleContinueAsGuest(context);
        if (state.deeplink == null) {
          return;
        }

        final link = Uri.decodeFull(state.deeplink!.link);
        final linkUri = Uri.tryParse(link);

        context.get<Logger>().d('DeeplinkResolver.build.decodedLink: $link');
        context.get<Logger>().d('DeeplinkResolver.build.uri: $linkUri');

        final looDeepLinkChecker = LooDeepLinkChecker(pathSegments: linkUri?.pathSegments ?? []);
        // Objects for new deeplink handling
        // Main handling of deeplink is placed almost at the end of build function
        // More into at https://dev.azure.com/hci-iap/koyal/_wiki/wikis/GMA/6929/Deeplink-handling
        // Content team
        final contentDeeplinkNavigation = context.get<DeeplinkNavigation>(
          instanceName: DeeplinkNavigation.instanceNameContent,
        ) as ContentDeeplinkNavigation;
        // Auth team
        final authDeeplinkNavigation = context.get<DeeplinkNavigation>(
          instanceName: DeeplinkNavigation.instanceNameAuth,
        ) as AuthDeeplinkNavigation;
        // Credit card team
        final creditCardsDeeplinkNavigation = context.get<DeeplinkNavigation>(
          instanceName: DeeplinkNavigation.instanceNameCreditCards,
        ) as CreditCardsDeeplinkNavigation;
        // Transaction team
        final transactionsDeeplinkNavigation = context.get<DeeplinkNavigation>(
          instanceName: DeeplinkNavigation.instanceNameTransactions,
        ) as TransactionsDeeplinkNavigation;
        // Loans team
        final loansDeeplinkNavigation = context.get<DeeplinkNavigation>(
          instanceName: DeeplinkNavigation.instanceNameLoans,
        ) as LoansDeeplinkNavigation;
        // Repayment team
        final repaymentDeeplinkNavigation = context.get<DeeplinkNavigation>(
          instanceName: DeeplinkNavigation.instanceNameRepayment,
        ) as RepaymentDeeplinkNavigation;

        // Old deeplink handling
        if (link.contains('/${PendingActionType.productVAS.name}')) {
          _navigateForPendingActionVas(context, link);
        } else if (link.contains('/${PendingActionType.productVoC.name}')) {
          _navigateForPendingActionVoc(context, link);
        } else if (link.contains('/contractualdocs')) {
          await navigateContractualDocuments(context, link);
        } else if (link.contains('/selfCare')) {
          await navigateSelfCare(context, link);
        } else if (link.contains('/loan_management')) {
          await navigateSelfCareOverview(context, link);
        } else if (link.contains('/appfeedback')) {
          Future.delayed(const Duration(seconds: 1), () {
            showGeneralFeedbackOverlay(
              KoyalModular.of(context).rootNavigatorKey.currentState!.overlay!.context,
            );
          });
        } else if (link.contains('/getloan') || link.contains('/${PendingActionType.loanApplication.name}')) {
          await context.navigator.pushFromPackage(
            package: 'CappLoanOrigination',
            screen: 'GetLoanWizard',
            arguments: LoanWizardArguments(fromPendingactions: true),
          );
        } else if (link.contains('/${PendingActionType.signContract.name}')) {
          final applicationCode =
              _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkApplicationCode, Uri.tryParse(link));
          final productType = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkProductType, Uri.tryParse(link));
          await context.navigator.pushFromPackage(
            package: 'CappLoanSignature',
            screen: 'LoanSignatureScreen',
            arguments: LoanSignatureScreenArguments(applicationCode: applicationCode, productType: productType),
          );
        } else if (link.contains('/${PendingActionType.signAppendix.name}')) {
          final applicationCode =
              _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkApplicationCode, Uri.tryParse(link));
          final productType = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkProductType, Uri.tryParse(link));
          await context.navigator.pushFromPackage(
            package: 'CappLoanSignature',
            screen: 'AppendixSignatureScreen',
            arguments: AppendixSignatureScreenArguments(applicationCode: applicationCode, productType: productType),
          );
        } else if (link.contains('/${PendingActionType.transactionSignature.name}')) {
          final list = link.split('/');
          final pendingActionId = list.last;
          await context.navigator.pushFromPackage(
            package: 'CappTransactionSignature',
            screen: 'TransactionSignatureScreen',
            arguments: TransactionSignatureScreenArguments(pendingActionId: pendingActionId),
          );
        } else if (link.contains('/${PendingActionType.userMustChangePassword.name}')) {
          if (context.isFlagEnabledRead(FeatureFlag.changePasswordOtp)) {
            await ChangePinHandler.navigateToChangePin(context: context, flow: ChangePasswordFlow.pendingAction);
          } else {
            await context.navigateToChangePassword();
          }
        } else if (link.contains('/${PendingActionType.setupDirectDebit.name}')) {
          String? contractNumber;
          String? productType;
          String? journeyType;
          contractNumber = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkContractNumber, Uri.tryParse(link));
          productType = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkProductTypecamel, Uri.tryParse(link));
          journeyType = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkJourneyType, Uri.tryParse(link));

          if (contractNumber != null && productType != null && journeyType != null) {
            await navigationToDirectDebit(context, contractNumber, productType, journeyType);
          } else {
            await context.navigator.pushFromPackage(package: 'CappPersonal', screen: 'setupDirectDebit');
          }
        } else if (link.contains('/${PendingActionType.limitedOffer.name}')) {
          await context.navigator.pushFromPackage(package: 'CappPersonal', screen: 'limitedOffer');
        } else if (link.contains('/repayment/result') || link.contains('vnpay') || link.startsWith('gc-')) {
          await context.navigator.pushFromPackage(
            package: 'CappRepayment',
            screen: 'RepaymentPaymentResultScreen',
            arguments: ScreenArguments(
              map: <String, dynamic>{
                'deeplink': link,
                'previousRouteName':
                    (context.navigator.canPop() ?? true) ? 'repayment_payment_summary_screen' : 'home_screen',
              },
            ),
          );
        } else if (link.contains('getflexiloan')) {
          await _navigateToLoanJourney(context, link, 'flexi');
        } else if (link.contains('getcashloan')) {
          await _navigateToLoanJourney(context, link, 'cash');
        } else if (link.contains('getemicard')) {
          await _navigateToLoanJourney(context, link, 'emi');
        } else if (link.contains('/campaign')) {
          await _navigateToDynamicIntroScreen(context, state.deeplink!, 'campaign');
        } else if (linkUri != null) {
          if (linkUri.path.contains('golden/foo/view')) {
            await context.navigator.pushFromPackage(
              package: 'CappGolden',
              screen: 'GoldenFooViewScreen',
              arguments: GoldenFooViewRouteArguments(),
            );
          } else if (link.contains('/feedback?')) {
            Future.delayed(const Duration(seconds: 1), () {
              showFeedbackOverlay(
                KoyalModular.of(context).rootNavigatorKey.currentState!.overlay!.context,
                journeyId: linkUri.queryParameters['journeyId']!,
              );
            });
          } else if (linkUri.path.contains('golden/foo/edit')) {
            final linkParams = linkUri.queryParameters;
            String? phoneNumber;
            double? amount;
            if (linkParams.containsKey('phoneNumber')) {
              phoneNumber = linkParams['phoneNumber'];
            }
            if (linkParams.containsKey('amount')) {
              amount = double.tryParse(linkParams['amount']!);
            }
            final arguments = GoldenFooEditRouteArguments(
              initialPhoneNumber: phoneNumber,
              initialAmount: amount,
            );
            await context.navigator.pushFromPackage(
              package: 'CappGolden',
              screen: 'GoldenFooEditScreen',
              arguments: arguments,
            );
          } else if (linkUri.path.contains('repayment/contract_selection')) {
            final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
            if (isAnonymous) {
              final externalNavigator = context.externalNavigator;
              if (externalNavigator is ExternalPkgNavigator) {
                externalNavigator.authCompleteCallback = (ctx) async {
                  externalNavigator.authCompleteCallback = null;

                  final isAno = await ctx.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
                  if (!isAno) {
                    await navigateToRepaymentContractList(ctx);
                  } else {
                    if (ctx.navigator.canPop() ?? false) {
                      await ctx.navigator.toMainScreen();
                    } else {
                      await context.navigator.toMainScreen();
                    }
                  }
                };
              }

              final isUserOnLoginScreen = context.isKeyInWidgetTree(const ValueKey('__continueAsGuestButton__'));
              if (isUserOnLoginScreen) {
                return;
              }

              if (linkUri.toString().endsWith('repayment/contract_selection')) {
                await context.navigateToSignInScreen(popAfterSignedIn: true);
              }
            } else {
              await navigateToRepaymentContractList(context);
            }
          } else if (link.contains('/${PendingActionType.offerScoring.name}')) {
            await _navigateToUlo(context, link);
          } else if (link.contains('/${PendingActionType.loanPending.name}')) {
            await _navigateToUlo(context, link);
          } else if (looDeepLinkChecker.isValidAll([LooLoanDeepLink()])) {
            await DeeplinkNavigationUtils.anonymousChecker(context, (context) async => navigateUlo(context, null));
          } else if (looDeepLinkChecker.isValidAll([LooLoanLiveActivityDeepLink()])) {
            final status = LiveActivityStatus.fromName(linkUri.queryParameters['status'] ?? '');
            if (status != null) {
              LiveActivityTracking.trackingClickLiveActivity(context.get<KoyalTrackingService>(), status);
            }
            await DeeplinkNavigationUtils.anonymousChecker(
              context,
              (context) async => navigateUlo(context, LoanDeepLinkPath.liveActivityPath),
            );
          } else if (looDeepLinkChecker.isValidAll([LooOfferDeepLink()])) {
            final utmSource = state.deeplink?.source;
            final utmMedium = state.deeplink?.medium;
            final utmCampaign = state.deeplink?.campaign;
            final deepLinkOfferType = linkUri.pathSegments.getOrNull(1);
            await DeeplinkNavigationUtils.anonymousChecker(
              context,
              (context) => context.navigator.push(
                path: NavigatorPath.cappLoanOriginationUnified.offerDeeplinkScreen,
                arguments: OfferDeeplinkArguments(
                  utmCampaign: utmCampaign,
                  utmMedium: utmMedium,
                  utmSource: utmSource,
                  deepLinkOfferType: deepLinkOfferType,
                ),
              ),
            );
          } else if (linkUri.path.contains('/gps')) {
            final code = _getQueryParamsFromDeepLink('code', Uri.tryParse(link));
            if (code != null) {
              await context.get<DeeplinkResolverBloc>().sendQRCode(code);
            }
          } else if (linkUri.queryParameters.isNotEmpty && linkUri.queryParameters['af_dp'] != null) {
            // This condition to check user open onelink. We will renew LinkUri, get af_dp value and open new deeplink.
            final newLinkUri = Uri.tryParse(state.deeplink!.link);
            final newLink = newLinkUri?.queryParameters['af_dp'];
            if (newLink != null) {
              context.read<DeeplinkResolverBloc>().add(
                    DeeplinkResolverEvent.openDeeplink(
                      deeplink: DeeplinkModel(link: newLink),
                    ),
                  );
            }
            // Content deeplink handlings
          } else if (contentDeeplinkNavigation.contains(link)) {
            unawaited(
              contentDeeplinkNavigation.handle(
                context,
                link,
                extra: ContentDeeplinkExtra(
                  deeplinkModel: state.deeplink,
                ),
              ),
            );
            // Auth deeplink handling
          } else if (authDeeplinkNavigation.contains(link)) {
            unawaited(
              authDeeplinkNavigation.handle(
                context,
                link,
                extra: AuthDeeplinkExtra(
                  user: state.user,
                  deeplinkModel: state.deeplink,
                ),
              ),
            );
            // Credit Cards deeplink handling
          } else if (creditCardsDeeplinkNavigation.contains(link)) {
            unawaited(
              creditCardsDeeplinkNavigation.handle(
                context,
                link,
                extra: const CreditCardsDeeplinkExtra(),
              ),
            );
            // Transaction deeplink handling
          } else if (transactionsDeeplinkNavigation.contains(link)) {
            unawaited(
              transactionsDeeplinkNavigation.handle(
                context,
                link,
                extra: const TransactionsDeeplinkExtra(),
              ),
            );
            // Loans deeplink handling
          } else if (loansDeeplinkNavigation.contains(link)) {
            unawaited(
              loansDeeplinkNavigation.handle(
                context,
                link,
                extra: const LoansDeeplinkExtra(),
              ),
            );
            // Repayment deeplink handling
          } else if (repaymentDeeplinkNavigation.contains(link)) {
            unawaited(
              repaymentDeeplinkNavigation.handle(
                context,
                link,
                extra: const RepaymentDeeplinkExtra(),
              ),
            );
          } else if (linkUri.pathSegments.isNotEmpty && linkUri.pathSegments.first == 'change-national-id') {
            await _navigateToChangeNationalId(context);
          }
        } else if (link.contains('change-national-id')) {
          await _navigateToChangeNationalId(context);
        } else {
          //* HOMEPAGE ---------------------------------------------
          await context.navigator.toMainScreen();
        }
      },
      child: child,
    );
  }

  Future<void> _navigateToChangeNationalId(BuildContext context) async {
    if (context.isFlagEnabledRead(FeatureFlag.vnChangeNationalId)) {
      unawaited(
        context.navigator.pushNamed(NavigatorPath.cappAuth.changeNationalIdScreen),
      );
    }
  }

  Future<void> _navigateToUlo(BuildContext context, String link) async {
    final productType = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkProductType, Uri.tryParse(link));
    final initParameters = <String, String>{};
    if (productType != null) {
      initParameters[productTypeKey] = productType;
    }
    await context.navigator.push(
      path: NavigatorPath.cappLoanOriginationUnified.getLoanWizard,
      arguments: LoanWizardArguments(
        fromPendingactions: true,
        initParameters: initParameters.isNotEmpty ? initParameters : null,
      ),
    );
  }

  Future<void> _navigateToLoanJourney(BuildContext context, String link, String productType) async {
    await context.navigator.pushFromPackage(
      package: 'CappLoanOrigination',
      screen: 'ProductTypeSelectionDetailScreen',
      arguments: ProductTypeSelectionDetailScreenArguments(
        productTypeName: productType,
        onBackPressed: () {
          context.navigator.toMainScreen();
        },
        onContinuePressed: (_) async {
          context.navigator.pop();
          await context.navigator.pushFromPackage(
            package: 'CappLoanOrigination',
            screen: 'GetLoanWizard',
            arguments: LoanWizardArguments(fromPendingactions: true, productTypeFromExternalSource: productType),
          );
        },
        continueButtonLabel: L10nCappDomain.of(context).productDetailContinue,
      ),
    );
  }

  Future<void> _navigateToDynamicIntroScreen(
    BuildContext context,
    DeeplinkModel deeplinkModel,
    String productType,
  ) async {
    final utmSource = deeplinkModel.source;
    final utmMedium = deeplinkModel.medium;
    final utmCampaign = deeplinkModel.campaign;

    await context.navigator.pushFromPackage(
      package: 'CappLoanOrigination',
      screen: 'ProductIntroScreen',
      arguments: ProductIntroScreenArguments(
        utmSource: utmSource ?? '',
        utmMedium: utmMedium ?? '',
        utmCampaign: utmCampaign ?? '',
        productTypeName: productType,
        onBackPressed: () {
          context.navigator.toMainScreen();
        },
        continueButtonLabel: L10nCappDomain.of(context).productDetailContinue,
      ),
    );
  }

  void _navigateForPendingActionVoc(BuildContext context, String link) {
    final contractCode = _getQueryParamsFromDeepLink('contractCode', Uri.tryParse(link)) ?? '';
    context.navigator.pushFromPackage(
      package: 'CappCards',
      screen: 'CardInsurActivationSuccessScreen',
      arguments: CardInsurActivationSuccessRouteArgs(
        contractNumber: contractCode,
        flow: ActiveVocFlow.pendingAction,
      ),
    );
  }

  void _navigateForPendingActionVas(BuildContext context, String link) {
    final paStage = _getQueryParamsFromDeepLink(pendingActionMetadataKeyPAStage, Uri.tryParse(link));
    final policyId = _getQueryParamsFromDeepLink(pendingActionMetadataKeyPolicyId, Uri.tryParse(link)) ?? '';
    if (paStage == VasCspPendingActionTypeStage.cspPendingApplication.value) {
      context.navigator.pushFromPackage(
        package: 'CappVas',
        screen: 'CspChooseInsurancePlanScreen',
        arguments: CspChooseInsurancePlanRouteArgs(),
      );
    } else if (paStage == VasCspPendingActionTypeStage.cspPendingPayment.value) {
      final preStr = _getQueryParamsFromDeepLink(pendingActionMetadataKeyPremium, Uri.tryParse(link));
      final premium = double.tryParse(preStr ?? '');
      context.navigator.pushFromPackage(
        package: 'CappVas',
        screen: 'CspPaymentSummaryScreen',
        arguments: CspPaymentSummaryRouteArgs(
          policyId: policyId,
          premium: premium,
        ),
      );
    } else if (paStage == VasCspPendingActionTypeStage.cspPendingActivation.value) {
      final deviceId = _getQueryParamsFromDeepLink(pendingActionMetadataKeyDeviceId, Uri.tryParse(link));
      context.navigator.pushFromPackage(
        package: 'CappVas',
        screen: 'CspActivationIntroScreen',
        arguments: CspActivationRouteArgs(
          deviceId: deviceId ?? '',
          policyId: policyId,
        ),
      );
    } else if (paStage == VasCspPendingActionTypeStage.cspPendingReactivate.value) {
      context.navigator.pushFromPackage(
        package: 'CappVas',
        screen: 'CspInsuranceDetailScreen',
        arguments: CspInsuranceDetailRouteArgs(policyId: policyId),
      );
    }
  }

  String? _getQueryParamsFromDeepLink(String key, Uri? link) {
    if (link == null) return null;
    if (link.queryParameters.containsKey(key)) {
      return link.queryParameters[key];
    }
    return null;
  }

  Future<void> navigateContractualDocuments(BuildContext context, String link) async {
    return NavigationSelfService.navigateToSelfService(
      context,
      action: NavigationSelfService.documentAction,
    );
  }

  Future<void> navigateSelfCare(BuildContext context, String link) async {
    String? contractNumber;
    String? contractTypeString;
    String? action;
    contractTypeString = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkContractType, Uri.tryParse(link));
    contractNumber = _getQueryParamsFromDeepLink(DeeplinkConstants.deeplinkContractNumber, Uri.tryParse(link));
    action = _getQueryParamsFromDeepLink(DeeplinkConstants.action, Uri.tryParse(link));
    final documentId = _getQueryParamsFromDeepLink(DeeplinkConstants.documentId, Uri.tryParse(link));
    //Legacy flexi deelplink without contract number
    try {
      final chunks = link.split('/');
      if (chunks.length == 5) {
        final contractRepository = context.get<IContractRepository>();
        final contracts =
            await contractRepository.getContractInfo(const ContractInfoFilter(contractType: ContractType.flexi));

        if (contracts.length == 1) {
          final flexiContract = contracts.first;
          contractNumber = flexiContract.contractNumber;
          contractTypeString = chunks[4];
        }
      }
    } catch (_) {}
    final contractType = enumValueFromString(contractTypeString, ContractType.values);
    return NavigationSelfService.navigateToSelfService(
      context,
      contractType: contractType,
      contractNumber: contractNumber,
      action: action,
      documentId: documentId,
    );
  }

  Future<void> navigateSelfCareOverview(BuildContext context, String link) async {
    final chunks = link.split('/');
    if (chunks.length != 5) {
      return;
    }
    final contractRepository = context.get<IContractRepository>();
    final contractNumber = chunks[4];
    ContractType contractType;
    final numberContract = await contractRepository.getContractInfo(ContractInfoFilter(contractNumber: contractNumber));
    if (numberContract.length != 1) {
      await context.navigator.toMainScreen(arguments: MainScreenArguments(initialTab: TabItem.loans));
      return;
    } else {
      contractType = numberContract.first.contractType;
    }
    return NavigationSelfService.navigateToSelfService(
      context,
      contractType: contractType,
      contractNumber: contractNumber,
    );
  }
}

Future<void> navigateUlo(BuildContext context, String? loanDeepLinkPath) async {
  const signature = PendingActionType.signContract;
  const ulo = PendingActionType.loanPending;
  const uloForScoring = PendingActionType.offerScoring;

  final pendingActions = context.get<INewPendingActionsRepository>();
  await pendingActions.load().then(
        (value) => value.fold((l) {}, (r) {
          final pendingItems =
              r.items.where((pItem) => pItem.type == signature || pItem.type == ulo || pItem.type == uloForScoring);

          final isLoanDeepLinkLA =
              (loanDeepLinkPath ?? '').toLowerCase() == LoanDeepLinkPath.liveActivityPath.toLowerCase();
          final pendingItemsForUlo = r.items.where((pItem) => pItem.type == ulo || pItem.type == uloForScoring);
          final shouldDirectToUlo = pendingItemsForUlo.isNotEmpty && isLoanDeepLinkLA;
          if (pendingItems.length == 1 || shouldDirectToUlo) {
            final initParameters = <String, String>{};

            final productType = pendingItems.first.metadata?['ProductType'];
            if (productType != null) {
              initParameters[productTypeKey] = productType;
            }
            context.navigator.push(
              path: NavigatorPath.cappLoanOriginationUnified.getLoanWizard,
              arguments: LoanWizardArguments(
                fromDeeplink: true,
                initParameters: initParameters.isNotEmpty ? initParameters : null,
              ),
            );
          } else if (pendingItems.length > 1) {
            context.navigator.pushFromPackage(
              package: 'CappConfig',
              screen: 'PendingActionsScreen',
              arguments: PendingActionsScreenArguments(
                pendingActions: pendingItems.toList(),
              ),
            );
          } else {
            context.navigator.toMainScreen();
          }
        }),
      );
}

Future<void> navigateToRepaymentContractList(BuildContext context) async {
  final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
  final topScreenName = navigationHistoryObserver.top?.settings.name;

  Future<void> redirectToRepayment(BuildContext context, String? topScreenName) async {
    final authScreen = context.isFlagEnabledRead(FeatureFlag.onboardingV4Bos)
        ? NavigatorPath.cappAuth.authenticationInitialScreen
        : NavigatorPath.cappAuth.authLandingScreen;
    final repaymentMainScreen = NavigatorPath.cappRepayment.repaymentMainScreen.split('/').last;
    final repaymentNoLoanScreen = NavigatorPath.cappRepayment.repaymentNoLoanScreen.split('/').last;
    context.get<Logger>().d('DeeplinkResolver: $topScreenName');
    if (topScreenName == authScreen.split('/').last) {
      unawaited(context.navigator.toMainScreen());
    }
    if (topScreenName != repaymentMainScreen && topScreenName != repaymentNoLoanScreen) {
      await NavigationUtils.navigateToRepaymentMain(context: context, viewType: RepaymentViewType.contractList);
    }
  }

  if (context.isFlagEnabledRead(FeatureFlag.repaymentNew)) {
    if (topScreenName != NavigatorPath.cappRepayment.repaymentMainScreen.split('/').last) {
      await redirectToRepayment(context, topScreenName);
    }
  } else {
    if (topScreenName != NavigatorPath.cappRepayment.repaymentContractListScreen.split('/').last) {
      await redirectToRepayment(context, topScreenName);
    }
  }
}

Future<void> handleContinueAsGuest(BuildContext context) async {
  final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous().timeout(Durations.short3);

  if (isAnonymous) {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final externalNavigator = context.externalNavigator;
    if (externalNavigator is ExternalPkgNavigator &&
        externalNavigator.handleContinueAsGuestBeforeAuthCompleteCallback == null) {
      externalNavigator.handleContinueAsGuestBeforeAuthCompleteCallback = (ctx) async {
        externalNavigator.handleContinueAsGuestBeforeAuthCompleteCallback = null;
        // Clear repayment deferred deeplink if click continue as guest
        try {
          final deferredDeeplink = ctx.get<DeeplinkResolverBloc>().pendingDeeplink;
          if (deferredDeeplink != null) {
            final linkUri = Uri.tryParse(Uri.decodeFull(deferredDeeplink.link));
            //Repayment
            if (linkUri?.pathSegments.contains('repayment') == true &&
                linkUri?.pathSegments.contains('contract_selection') == true) {
              ctx.get<DeeplinkResolverBloc>().pendingDeeplink = null;
            }
            //Transaction: QR-VN
            if (linkUri != null && linkUri.pathSegments.isNotEmpty && 'qr' == linkUri.pathSegments.first) {
              ctx.get<DeeplinkResolverBloc>().pendingDeeplink = null;
            }
          }

          final navigationHistory = navigationHistoryObserver.history.map((h) => h.settings.name).toList();
          if (context.isFlagEnabledRead(FeatureFlag.onboardingV4Bos)) {
            if ((ctx.navigator.canPop() ?? false) &&
                navigationHistory.contains(NavigatorPath.cappAuth.authenticationInitialScreen.split('/').last)) {
              ctx.navigator.pop();
            }
          } else {
            if ((ctx.navigator.canPop() ?? false) &&
                navigationHistory.contains(NavigatorPath.cappAuth.crossroadsScreen.split('/').last) &&
                navigationHistory.contains(NavigatorPath.cappAuth.authLandingScreen.split('/').last)) {
              ctx.navigator.pop();
            }
          }
        } catch (e) {
          context.get<Logger>().d('DeeplinkResolver: $e');
        }
      };
    }
  }
}

void navigationToBnplInAppWebView(BuildContext context) {
  context.navigator.push(
    path: NavigatorPath.cappLoanOriginationUnified.bnplOfferWebView,
    arguments: BnplOfferArguments(),
  );
}

Future<void> navigationToDirectDebit(
  BuildContext context,
  String contractIds,
  String loanType,
  String journeyType,
) async {
  await context.navigator.pushFromPackage(
    package: 'CappDirectDebit',
    screen: 'DirectDebitDetailScreen',
    arguments: DirectDebitDetailsRouteArguments(
      contractNumber: contractIds,
      journeyType: journeyType,
      loanType: loanType,
      onSuccessFinish: () {},
    ),
  );
}
