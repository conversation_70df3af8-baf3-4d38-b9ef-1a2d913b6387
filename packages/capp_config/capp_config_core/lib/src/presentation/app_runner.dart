import 'dart:async';

import 'package:flare_flutter/flare_cache.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../capp_config_core.dart';

void appRunner(
  List<ChildPackage> packages,
  ISupportedLocales locales, {
  ExternalConfigurationData? externalConfigurationData,
  GetIt? customContainer,
}) {
  zoneGuardedAppRunner(
    (getIt) async => mainWrapper(
      KoyalCoreApp(
        getIt: GetIt.instance,
        module: AppModule(
          packages: packages,
          locales: locales,
        ),
      ),
      externalConfigurationData: externalConfigurationData,
    ),
    customContainer: customContainer,
  );
}

void mainRunner(MainPackage appModule, {ExternalConfigurationData? externalConfigurationData, GetIt? customContainer}) {
  zoneGuardedAppRunner(
    (getIt) async => mainWrapper(
      KoyalCoreApp(
        getIt: GetIt.instance,
        module: appModule,
      ),
      externalConfigurationData: externalConfigurationData,
    ),
    customContainer: customContainer,
  );
}

Future<void> mainWrapper(
  KoyalCoreApp coreApp, {
  ExternalConfigurationData? externalConfigurationData,
}) async {
  await Future.wait([
    FirebaseInitService().init(),
    StorageService().initialize(),
    coreApp.module.preRegisterDependencies(coreApp.getIt),
  ]);

  coreApp.getIt<IFirebasePerformanceMonitoring>().setIsEnabled(
        isEnabled: coreApp.getIt<PerformanceConfig>().isFirebasePerformanceMonitoringEnabled,
      );

  unawaited(
    coreApp.getIt<IFirebasePerformanceMonitoring>().startTrace(TraceType.appStart),
  );

  final injectorChild = externalConfigurationData != null
      ? ExternalConfiguration(
          data: externalConfigurationData,
          child: coreApp,
        )
      : coreApp;
  final injector = Injector(
    key: externalConfigurationData != null ? UniqueKey() : null,
    locator: coreApp.getIt,
    child: injectorChild,
  );

  Bloc.observer = TraceBlocObserver(
    loggerFactory: () => injector.get<Logger>(),
  );

  FlareCache.doesPrune = false;
  runApp(injector);
}
