import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as api_models;
import 'package:selfcareapi/selfcareapi.dart';

import '../../../../capp_config_core.dart';

///  Template class for enumerationsListEnumerationTypeGet request
final class EnumerationGetRequest {
  final api_models.EnumerationType _type;
  final EnumerationsApi _api;
  final Logger _logger;

  EnumerationGetRequest({
    required api_models.EnumerationType type,
    required Logger logger,
    required EnumerationsApi api,
  })  : _type = type,
        _logger = logger,
        _api = api;

  Future<Either<EnumerationsFailure, EnumerationList>> execute() async {
    try {
      final response = await _api.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(_type),
      );
      final list = response.toDomain();
      return right(list);
    } on DioError catch (e) {
      _logger.wtf('Dio error in enumerations repository', e);
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      _logger.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }
}
