import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as api_models;
import 'package:selfcareapi/selfcareapi.dart';

import '../../../../capp_config_core.dart';
import 'enumeration_get_request.dart';

// this repo should probably split between multiple packages
class EnumerationsRepository implements IEnumerationsRepository {
  EnumerationsApi? api;
  Logger? logger;

  EnumerationsRepository({this.api, this.logger});

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getCategories() async {
    try {
      final response = await api!
          .enumerationsListEnumerationTypeGet(api_models.EnumerationTypeWrapper(api_models.EnumerationType.categories));

      return right(response.toDomain());
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getDealsFilters() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.dealsFilters),
      );

      return right(response.toDomain());
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getFaqSubcategories() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.faqSubCategories),
      );

      return right(response.toDomain());
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getBlogPostsCategories() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.blogPostsCategories),
      );
      final list = response.toDomain();
      list.items?.sort((x, y) => (x.order ?? 0).compareTo(y.order ?? 0));
      return right(list);
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getInboxMessageCategories() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.inboxMessageCategories),
      );

      return right(response.toDomain());
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getPromosCategories() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.promoCategories),
      );

      return right(response.toDomain());
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getCommunicationChannels() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.communicationChannels),
      );
      final list = response.toDomain();
      list.items?.sort((x, y) => (x.order ?? 0).compareTo(y.order ?? 0));
      return right(list);
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getFeedbackCategories() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.generalFeedbackCategories),
      );
      final list = response.toDomain();
      list.items?.sort((x, y) => (x.order ?? 0).compareTo(y.order ?? 0));
      return right(list);
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getFeedbackFlows() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.feedbackFlows),
      );
      final list = response.toDomain();
      list.items?.sort((x, y) => (x.order ?? 0).compareTo(y.order ?? 0));
      return right(list);
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getDeeplinkDomainsWhitelist() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.deeplinkDomainWhitelist),
      );
      final list = response.toDomain();
      return right(list);
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getCardsWidgetsIllustrations() async {
    final request = EnumerationGetRequest(
      type: api_models.EnumerationType.cardWidgetIllustrations,
      api: api!,
      logger: logger!,
    );

    return request.execute();
  }

  @override
  Future<Either<EnumerationsFailure, EnumerationList>> getSurveyInsuranceCodes() async {
    try {
      final response = await api!.enumerationsListEnumerationTypeGet(
        api_models.EnumerationTypeWrapper(api_models.EnumerationType.surveyInsuranceCodes),
      );

      return right(response.toDomain());
    } on DioError catch (_) {
      return left(const EnumerationsFailure.unexpected());
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected error in enumerations repository', e, s);
      return left(const EnumerationsFailure.unexpected());
    }
  }
}
