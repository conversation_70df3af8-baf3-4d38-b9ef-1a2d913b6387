import 'package:capp_api/capp_api.dart' as capp_api;
import 'package:capp_domain/capp_domain.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:selfcareapi/model/models.dart' as capp_api;

extension EnumerationItemMapper on capp_api.EnumerationResponse {
  EnumerationItem toDomain() => EnumerationItem(
        id: id.toString(),
        enumerationType: capp_api.TypeEnumerationX.fromApi(enumerationType!)!,
        title: title.toString(),
        description: description.toString(),
        icon: icons!.isNotEmpty ? icons![0].id : '',
        imageUrl: icons!.isNotEmpty ? icons![0].url : '',
        order: order,
        key: key,
        pictogramUrl: icons
            ?.firstWhereOrNull(
              (e) => e.tags?.firstWhereOrNull((t) => capp_api.TagX.fromApi(t) == Tag.imagePictogram) != null,
            )
            ?.url,
      );
}
