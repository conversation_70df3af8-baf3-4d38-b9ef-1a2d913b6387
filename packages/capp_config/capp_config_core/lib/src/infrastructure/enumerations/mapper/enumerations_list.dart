import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:selfcareapi/model/models.dart' as models;

import '../../../../capp_config_core.dart';

extension EnumerationListMapper on models.EnumerationsListResponse {
  EnumerationList toDomain() => EnumerationList(
        page: page ?? 1,
        pageSize: pageSize ?? 0,
        totalCount: totalCount ?? 0,
        items: items?.map((item) => item.toDomain()).toList(),
      );
}
