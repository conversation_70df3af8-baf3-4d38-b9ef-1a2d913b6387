class CappSettingsBase {
  final String appStoreId;
  final String googlePlayId;
  final String? appGalleryId;
  final double defaultPositionLatitude;
  final double defaultPositionLongitude;
  final String imageBaseUrl;
  final String insiderBaseUrl;
  final String insiderStageBaseUrl;
  final String insiderPerformanceBaseUrl;
  final String deeplinkUrl;
  final String dynamicLinkUrl;
  final String? onelinkUrl;

  const CappSettingsBase({
    required this.appStoreId,
    required this.googlePlayId,
    required this.defaultPositionLatitude,
    required this.defaultPositionLongitude,
    required this.imageBaseUrl,
    required this.insiderBaseUrl,
    required this.insiderStageBaseUrl,
    required this.insiderPerformanceBaseUrl,
    required this.deeplinkUrl,
    required this.dynamicLinkUrl,
    this.onelinkUrl,
    this.appGalleryId,
  });
  bool get isHuawei => appGalleryId != null;
}
