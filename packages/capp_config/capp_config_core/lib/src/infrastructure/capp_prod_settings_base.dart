import 'capp_settings_base.dart';

class CappProdSettingsBase extends CappSettingsBase {
  final String baseUrl;
  final String? baseStageUrl;
  final String? basePerformanceUrl;
  final String identityBaseUrl;
  final String? identityBaseStageUrl;
  final String? identityBasePerformanceUrl;
  final String commonBaseUrl;
  final int connectTimeout;
  final int receiveTimeout;
  final int sendTimeout;
  final int extendConnectTimeout;
  final int extendReceiveTimeout;
  final String? snappDetectionPackageName;

  const CappProdSettingsBase({
    required this.baseUrl,
    required this.baseStageUrl,
    required this.basePerformanceUrl,
    required this.commonBaseUrl,
    required this.identityBaseUrl,
    required this.identityBaseStageUrl,
    required this.identityBasePerformanceUrl,
    required String appStoreId,
    required String googlePlayId,
    required String imageBaseUrl,
    required String insiderBaseUrl,
    required String insiderStageBaseUrl,
    required String insiderPerformanceBaseUrl,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    this.snappDetectionPackageName,
    String? onelinkUrl,
    this.connectTimeout = 25 * 1000,
    this.receiveTimeout = 25 * 1000,
    this.sendTimeout = 120 * 1000,
    this.extendConnectTimeout = 30 * 1000,
    this.extendReceiveTimeout = 30 * 1000,
    String? appGalleryId,
  }) : super(
          appStoreId: appStoreId,
          googlePlayId: googlePlayId,
          appGalleryId: appGalleryId,
          imageBaseUrl: imageBaseUrl,
          insiderBaseUrl: insiderBaseUrl,
          insiderStageBaseUrl: insiderStageBaseUrl,
          insiderPerformanceBaseUrl: insiderPerformanceBaseUrl,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          onelinkUrl: onelinkUrl,
        );
}
