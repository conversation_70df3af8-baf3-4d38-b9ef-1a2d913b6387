import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

/// **************************************************************
///
/// This place is NOT for adding another classes
/// please don't do that, this one is "exception"
///
/// **************************************************************
class UserAuthorizationRepository {
  final IIdentityRepository identityRepository;
  final IUserRepository userRepository;
  final IEnvironmentConfigRepository config;
  final ICrashlyticsInitService crashlyticsInitService;
  final EventTrackingService eventTrackingService;
  final ICurrentUserRepository currentUserRepository;
  final IFeatureFlagRepository featureFlagRepository;

  UserAuthorizationRepository({
    required this.identityRepository,
    required this.userRepository,
    required this.config,
    required this.crashlyticsInitService,
    required this.eventTrackingService,
    required this.currentUserRepository,
    required this.featureFlagRepository,
  });

  Future<bool> isCurrentUserAnonymous() async => currentUserRepository.isCurrentUserAnonymous();

  void init() {
    identityRepository.authorizedStream.listen(
      (success) async {
        await _getAndSetUser();
        if (success) {
          await config.getEnvironmentConfig();
        }
        //when logout
        else {
          //clean current traffic for tracking
          await eventTrackingService.cleanUp();
        }
      },
    );
    identityRepository.streamIdentity()?.then((stream) => stream.listen(_setUser));
    featureFlagRepository.featureFlagsChanged.stream.listen((event) async => _getAndSetUser());
  }

  Future _getAndSetUser() async {
    final user = await currentUserRepository.getCurrentUser();
    await _setUser(user);
  }

  Future _setUser(CurrentUser? user) async {
    if (user != null) {
      crashlyticsInitService.updateUserIdentifier(user.id!);
      await eventTrackingService.setUser(
        userId: user.id!,
        isInsider: user.isInsider,
        cuid: await userRepository.userCuid(),
        phoneNumber: user.phoneNumber,
        isExistingUser: await userRepository.isExistingUser(),
        signonStatus: enumValueToString(user.signOnState ?? SignOnStatus.registered),
      );
    } else {
      await eventTrackingService.setUser(
        userId: '',
        isInsider: false,
        isExistingUser: false,
        signonStatus: enumValueToString(user?.signOnState ?? SignOnStatus.anonymous),
      );
    }
  }
}
