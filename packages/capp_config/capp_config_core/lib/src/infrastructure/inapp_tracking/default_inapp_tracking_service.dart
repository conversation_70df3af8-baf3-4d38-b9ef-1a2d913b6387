import '../../domain/i_inapp_tracking.dart';

class DefaultInAppTrackingService implements IInAppTracking {
  @override
  void trackInAppView({required String? msid, required int? templateId}) {
    // no-op
  }

  @override
  void trackInAppCtaClick({required String? msid, required int? templateId}) {
    // no-op
  }

  @override
  void trackInAppCloseClick({required String? msid, required int? templateId}) {
    // no-op
  }
}
