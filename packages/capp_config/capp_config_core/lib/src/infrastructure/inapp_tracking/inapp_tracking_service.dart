import '../../domain/i_inapp_tracking.dart';

class InAppTrackingService {
  final IInAppTracking trackingService;

  InAppTrackingService({required this.trackingService});

  void trackView({required String? msid, required int? templateId}) {
    trackingService.trackInAppView(msid: msid, templateId: templateId);
  }

  void trackCtaClick({required String? msid, required int? templateId}) {
    trackingService.trackInAppCtaClick(msid: msid, templateId: templateId);
  }

  void trackCloseClick({required String? msid, required int? templateId}) {
    trackingService.trackInAppCloseClick(msid: msid, templateId: templateId);
  }
}
