import 'dart:async';
import 'dart:convert';

import 'package:a2a_intent_receiver/core/i_a2a_intent_receiver.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:logger/logger.dart';

import '../../../capp_config_core.dart';
import 'a2a_receiver_constant.dart';

class A2AIntentReceiverService implements IA2AIntentReceiverService {
  final Logger logger;
  final IA2AIntentReceiver intentReceiver;
  final IDeeplinkService deeplinkService;
  StreamSubscription? _intentSub;

  A2AIntentReceiverService({
    required this.logger,
    required this.intentReceiver,
    required this.deeplinkService,
  });

  @override
  Future<void> init() async {
    _intentSub = intentReceiver.getTextStream().listen(
      processIntentData,
      onError: (err) {
        logger.e('Failed to listen intent data: $err');
      },
    );
  }

  @override
  void processIntentData(String rawData) {
    try {
      final json = jsonDecode(rawData) as Map<String, dynamic>;
      final uri = Uri.parse(A2AReceiverConstant.walletPayDeeplink).replace(queryParameters: json);
      deeplinkService.pushDeeplink(DeeplinkModel(link: uri.toString()));
    } catch (e) {
      logger.e('Failed to parse intent data: $e');
    }
  }

  void dispose() {
    _intentSub?.cancel();
  }
}
