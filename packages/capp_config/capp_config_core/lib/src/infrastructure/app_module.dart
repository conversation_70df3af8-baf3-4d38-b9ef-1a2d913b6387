import 'package:app_links/app_links.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../presentation/app_widget.dart';
import 'user_authorization_repository.dart';

class AppModule extends MainPackage {
  final ISupportedLocales locales;
  @override
  final List<ChildPackage> packages;

  AppModule({
    required this.packages,
    required this.locales,
  });

  @override
  Widget get bootstrap => AppWidget(
        supportedLocales: locales.locales,
        insiderLocales: locales.insiderLocales,
      );

  @override
  List<KoyalRoute> get routes => [];

  @override
  void registerDependencies(GetIt container) {
    container
      // library handling deeplinks, please keep as singleton
      ..registerSingleton<AppLinks>(AppLinks())
      ..registerLazySingleton<UserAuthorizationRepository>(
        () => UserAuthorizationRepository(
          crashlyticsInitService: container<ICrashlyticsInitService>(),
          currentUserRepository: container<ICurrentUserRepository>(),
          identityRepository: container<IIdentityRepository>(),
          userRepository: container<IUserRepository>(),
          config: container<IEnvironmentConfigRepository>(),
          eventTrackingService: container<EventTrackingService>(),
          featureFlagRepository: container<IFeatureFlagRepository>(),
        ),
      );
  }
}
