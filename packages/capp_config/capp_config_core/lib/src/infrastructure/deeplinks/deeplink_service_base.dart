//ignore_for_file: use_build_context_synchronously
import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../application/deeplinks/deeplink_resolver_bloc.dart';

abstract class DeeplinkServiceBase implements IDeeplinkService {
  // Values from app configuration
  final String deeplinkUrl;
  final String dynamicLinkUrl;
  final String? onelinkUrl;
  // For remote config
  final IEnvironmentRepository envRepository;

  DeeplinkServiceBase({
    required this.deeplinkUrl,
    required this.dynamicLinkUrl,
    required this.onelinkUrl,
    required this.envRepository,
  });

  @override
  String get baseDeeplinkUrl => _envDeeplinkUrl ?? deeplinkUrl;

  @override
  Future<dynamic> deeplinkOrLaunch(
    String linkUrl,
    BuildContext context, {
    String linkTitle = '',
    bool requireCameraPermissions = false,
    bool openInNativeView = false,
    String? userAgent,
    ShouldOverrideUrlLoadingCallback? shouldOverrideUrlLoadingCallback,
    VoidCallback? onExitBrowserCallback,
  }) async {
    if (linkUrl.isEmpty) {
      // Link can not be empty
      return;
    }

    final link = linkUrl.trim();

    final isDeeplinkUrl = _isDeeplinkUrl(link) || _isDynamicLinkUrl(link) || _isOnelinkUrl(link);

    if (requireCameraPermissions || _cameraPermissionNeeded(link)) {
      if (GmaPlatform.isIOS) {
        // inappwebview plugin version < 6.0 does not support camera permission for iOS >= 15.0
        if (!isDeeplinkUrl) return GmaPlatform.launchUrl(link, mode: LaunchMode.externalApplication);
      } else {
        await _checkCameraPermission(context);
      }
    }
    if (isDeeplinkUrl) {
      context.read<DeeplinkResolverBloc>().add(DeeplinkResolverEvent.proceed(deeplink: DeeplinkModel(link: link)));
    } else {
      if (openInNativeView) {
        return CustomTabsHelper.openUrl(link);
      } else {
        if (link.startsWith('mailto:')) {
          return GmaPlatform.launchUrl(link);
        } else {
          return context.navigator.pushFromPackage(
            package: 'CappContent',
            screen: 'WebViewScreen',
            arguments: WebViewArguments(
              url: link,
              title: linkTitle,
              appendUserAgent: userAgent,
              shouldOverrideUrlLoadingCallback: shouldOverrideUrlLoadingCallback,
              onClose: onExitBrowserCallback,
            ),
          );
        }
      }
    }
  }

  @override
  Uri createDeeplink(String path, {Map<String, dynamic>? queryParameters}) {
    final deeplinkUri = Uri.parse(deeplinkUrl);
    return Uri(scheme: deeplinkUri.scheme, host: deeplinkUri.host, path: path, queryParameters: queryParameters);
  }

  // Getters over env repository
  String? get _envDeeplinkUrl => envRepository.getEnvironmentUrl()?.deeplinkUrl;

  String? get _envDynamicLinkUrl => envRepository.getEnvironmentUrl()?.dynamicLinkUrl;

  String? get _envOnelinkUrl => envRepository.getEnvironmentUrl()?.oneLinkUrl;

  // Function to verify if link can be handled
  bool _isDynamicLinkUrl(String link) =>
      link.startsWith(_envDynamicLinkUrl?.isNotEmpty ?? false ? _envDynamicLinkUrl! : dynamicLinkUrl);

  bool _isDeeplinkUrl(String link) =>
      link.startsWith(_envDeeplinkUrl?.isNotEmpty ?? false ? _envDeeplinkUrl! : deeplinkUrl);

  bool _isOnelinkUrl(String link) =>
      onelinkUrl != null && link.startsWith(_envOnelinkUrl?.isNotEmpty ?? false ? _envOnelinkUrl! : onelinkUrl!);

  // Camera stuff
  bool _cameraPermissionNeeded(String link) {
    return link.contains('homecredit.vn/mp/dang-ky') || link.contains('homepaylater.vn/');
  }

  Future _checkCameraPermission(BuildContext context) async {
    final status = await Permission.camera.request();
    if (!status.isGranted) {
      await showKoyalOverlay<void>(
        context,
        body: KoyalText.body1(
          color: ColorTheme.of(context).defaultTextColor,
          GmaPlatform.isAndroid
              ? L10nCappHome.of(context).allowCameraPermissionsToContinueAndroid
              : L10nCappHome.of(context).allowCameraPermissionsToContinueIos,
          textAlign: TextAlign.center,
        ),
        primaryButtonBuilder: (context) => PrimaryButton(
          onPressed: () => Navigator.pop(context),
          text: L10nCappHome.of(context).okGotIt,
        ),
      );
      if (await Permission.camera.isPermanentlyDenied) {
        await openAppSettings();
      }
    }
  }
}
