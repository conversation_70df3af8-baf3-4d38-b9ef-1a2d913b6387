import 'dart:async';

import 'package:capp_appsflyer_core/capp_appsflyer_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../application/tracking/app_start_tracking_event.dart';
import 'deeplink_service_base.dart';

class DeeplinkService extends DeeplinkServiceBase implements IDynamicLinksService, IAppsflyeDeeplinkHandler {
  final String appStoreId;
  final Logger logger;
  final EventTrackingService trackingService;
  final IKoyalMessagingRepository? koyalMessagingRepository;
  final StreamController<DeeplinkModel> _onDeeplink = StreamController<DeeplinkModel>.broadcast();
  final StreamController<String?> _onError = StreamController<String?>.broadcast();
  final IAppsflyerService? appsflyerService;
  final CappTrackingService cappTrackingService;

  final bool isHuawei;

  @override
  Stream<DeeplinkModel> get onDeeplink => _onDeeplink.stream;

  @override
  Stream<String?> get onError => _onError.stream;

  DeeplinkService({
    required this.logger,
    required this.trackingService,
    required this.koyalMessagingRepository,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required this.appStoreId,
    required String? onelinkUrl,
    required this.cappTrackingService,
    required IEnvironmentRepository envRepository,
    this.appsflyerService,
    this.isHuawei = false,
  }) : super(
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          onelinkUrl: onelinkUrl,
          envRepository: envRepository,
        );

  @override
  Future<void> init() async {
    if (isHuawei) {
      return;
    }
    registerAppsflyer();
    // Deprecated on August 25 2025
    await initDynamicLinks();

    // Added ? instead ! because BUG 170077
    // Seem that issue was regarding PN, when app is only partially initialized so koyalMessagingRepository==nul
    koyalMessagingRepository?.onReceived.listen((notification) {
      if (notification.notificationType == MessageType.deeplink && notification.isDataMessage) {
        final deeplink = notification.data?.deeplink;
        final messageId = notification.data?.msId ?? notification.id;

        pushDeeplink(DeeplinkModel(link: deeplink ?? '', messageId: messageId));
      }
    });
  }

  @override
  void pushDeeplink(DeeplinkModel deeplink) {
    _onDeeplink.add(deeplink);
  }

  @override
  Future<void> initDynamicLinks() async {
    FirebaseDynamicLinks.instance.onLink.listen(onDidReceiveDynamicLink, onError: _onError.add);

    final initialLink = await FirebaseDynamicLinks.instance.getInitialLink();
    if (initialLink != null) await onDidReceiveDynamicLink(initialLink);
  }

  @override
  Future<Uri> createDynamicLink(BuildContext context, String path, {Map<String, dynamic>? queryParameters}) async {
    final info = await context.get<IPlatformService>().getPackageInfo();
    final deeplink = createDeeplink(path, queryParameters: queryParameters);
    final parameters = DynamicLinkParameters(
      uriPrefix: dynamicLinkUrl.endsWith('/') ? dynamicLinkUrl.substring(0, dynamicLinkUrl.length - 1) : dynamicLinkUrl,
      link: deeplink,
      androidParameters: AndroidParameters(packageName: info.packageName, minimumVersion: 1),
      iosParameters: IOSParameters(bundleId: info.packageName, minimumVersion: '1', appStoreId: appStoreId),
    );
    return FirebaseDynamicLinks.instance.buildLink(parameters);
  }

  @override
  Future<void> onDidReceiveDynamicLink(PendingDynamicLinkData? dynamicLinkData) async {
    final link = dynamicLinkData?.link;
    if (link == null) return;
    if (link.hasQuery) {
      unawaited(trackingService.trackCampaignFromUrl(url: link.toString()));
    }
    _onDeeplink.add(DeeplinkModel(link: link.toString()));
  }

  @override
  void registerAppsflyer() {
    appsflyerService?.setDeeplinkHandler(
      (deeplink) => pushDeeplink(DeeplinkModel(link: deeplink)),
      onInstalData: trackAppsflyer,
    );
  }

  // used for deferred deeplinks from Appsflyer
  @override
  void trackAppsflyer(Map<String, dynamic> appsflyerInstallData) {
    logger.d('Received appsflyer install data for tracking');
    // for tracking we need to send only first app launch
    if (appsflyerInstallData[AppsflyerConstants.isFirstLaunch] == true) {
      cappTrackingService.setUtmParametersFromData(appsflyerInstallData);
      final trackEvent = AppFirstOpenEvent.fromInstallData(installData: appsflyerInstallData);
      cappTrackingService.trackCappEvent(trackEvent);
    }
  }
}

class WebDeeplinkService extends DeeplinkServiceBase implements IDeeplinkService {
  final Logger logger;
  final EventTrackingService trackingService;
  final IKoyalMessagingRepository? koyalMessagingRepository;
  final StreamController<DeeplinkModel> _onDeeplink = StreamController<DeeplinkModel>.broadcast();
  final StreamController<String?> _onError = StreamController<String?>.broadcast();

  @override
  Stream<DeeplinkModel> get onDeeplink => _onDeeplink.stream;

  @override
  Stream<String?> get onError => _onError.stream;

  WebDeeplinkService({
    required this.logger,
    required this.trackingService,
    required this.koyalMessagingRepository,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required String? onelinkUrl,
    required IEnvironmentRepository envRepository,
  }) : super(
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          onelinkUrl: onelinkUrl,
          envRepository: envRepository,
        );

  @override
  Future<void> init() => Future.value();

  @override
  void pushDeeplink(DeeplinkModel deeplink) {}

  @override
  String get baseDeeplinkUrl => deeplinkUrl;
}
