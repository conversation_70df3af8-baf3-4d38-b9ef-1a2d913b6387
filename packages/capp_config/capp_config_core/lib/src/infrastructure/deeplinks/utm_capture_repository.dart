import 'package:capp_domain/capp_domain.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/api/user_v2_api.dart';

import 'utm_capture_mapper.dart';

class UTMCaptureRepository extends IUTMCaptureRepository {
  final UserV2Api? api;
  final Logger? logger;

  UTMCaptureRepository({this.api, this.logger});

  @override
  Future<void> utmLinkRequest(UTMLinkRequestModel utmLinkRequestModel) async {
    try {
      await api!.v2UserProfilesUtmLinksPut(utmLinksRequest: UTMLinkRequestMapper.fromDomain(utmLinkRequestModel));
    } on Exception catch (e, s) {
      logger!.wtf('Unexpected platform error in utm capture repository', e, s);
    }
  }
}
