import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import 'deeplink_service_base.dart';

class FakeDeeplinkService extends DeeplinkServiceBase {
  final Logger? logger;

  final StreamController<DeeplinkModel> _onDeeplink = StreamController<DeeplinkModel>.broadcast();

  final StreamController<String> _onError = StreamController<String>.broadcast();

  FakeDeeplinkService({
    required this.logger,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required String? onelinkUrl,
    required IEnvironmentRepository envRepository,
  }) : super(
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          onelinkUrl: onelinkUrl,
          envRepository: envRepository,
        );

  @override
  Stream<DeeplinkModel> get onDeeplink => _onDeeplink.stream;

  @override
  Stream<String> get onError => _onError.stream;

  @override
  Future<void> init() async {}

  @override
  void pushDeeplink(DeeplinkModel model) {
    _onDeeplink.add(model);
  }

  @override
  String get baseDeeplinkUrl => deeplinkUrl;
}
