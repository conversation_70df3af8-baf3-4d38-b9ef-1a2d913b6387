import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';
import '../auth_deeplink_extra.dart';

// example: https://app.gma.homecredit.vn/details
final class DetailsDeeplinkHandler extends DeeplinkDefinitionHandler
    implements DeeplinkNavigationHandlerWithExtra<AuthDeeplinkExtra> {
  const DetailsDeeplinkHandler();

  @override
  List<String> get deeplinkUniqueSegments => [AuthDeeplinkConst.details];

  @override
  Future<void> navigate(BuildContext context, Uri uri, {required AuthDeeplinkExtra extra}) async {
    if (extra.user?.isAnonymous ?? true) {
      await context.navigator.toMainScreen();
    } else {
      await context.navigator.push(path: NavigatorPath.cappPersonal.personalDetailsScreenV2);
    }
  }
}
