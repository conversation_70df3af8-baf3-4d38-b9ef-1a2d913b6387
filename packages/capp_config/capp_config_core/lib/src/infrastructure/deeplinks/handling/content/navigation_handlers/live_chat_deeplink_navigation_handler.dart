import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../../capp_config_core.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

// ignore_for_file: use_build_context_synchronously
final class LiveChatDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const LiveChatDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.liveChat];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
    if (!isAnonymous) {
      await _navigateToChatbot(context);
      return;
    }

    final externalNavigator = context.externalNavigator;
    if (externalNavigator is ExternalPkgNavigator) {
      // If externalNavigator is not null, use it for listening auth process
      externalNavigator.authCompleteCallback = (ctx) async {
        externalNavigator.authCompleteCallback = null;
        final isAno = await ctx.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
        // after the user do a auth process, check if they are anonymous again
        if (!isAno) {
          // If the user is logged in successfully, then navigate to Chatbot screen
          await _navigateToChatbot(context);
        } else {
          // else navigate to anonymous Main screen
          await context.navigator.toMainScreen();
        }
      };
    } else {
      await context.navigateToSignInScreen(popAfterSignedIn: true);
    }
  }

  Future<void> _navigateToChatbot(BuildContext context) async {
    await context.navigator.pushFromPackage(
      package: 'KoyalChatbot',
      screen: 'ChatbotScreen',
    );
  }
}
