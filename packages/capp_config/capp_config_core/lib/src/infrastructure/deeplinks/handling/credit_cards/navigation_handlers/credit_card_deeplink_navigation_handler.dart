import 'dart:convert';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_cards_core/capp_cards_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:navigator/navigator.dart';

import '../../../../../../capp_config_core.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../credit_cards_deeplink_const.dart';

final class _CreditCardConst {
  static const String manageCard = 'manage_card';
  static const String loyaltyDashboard = 'loyalty_dashboard';
  static const String activeVoc = 'vas_voc';
  static const String registerWalletPay = 'register-wallet';
}

// ignore_for_file: use_build_context_synchronously
// TODO(RET-D): please update this one, currently this just copied from resolver and build to handle condition being there
final class CreditCardDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const CreditCardDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [CreditCardsDeeplinkConst.creditCard, CreditCardsDeeplinkConst.card];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final secondSegment = uri.pathSegments[1];
    switch (secondSegment) {
      case _CreditCardConst.manageCard:
      case _CreditCardConst.loyaltyDashboard:
      case _CreditCardConst.activeVoc:
        final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
        final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
        if (isAnonymous) {
          final externalNavigator = context.externalNavigator;
          if (externalNavigator is ExternalPkgNavigator) {
            externalNavigator.authCompleteCallback = (ctx) async {
              externalNavigator.authCompleteCallback = null;
              final isAno = await ctx.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
              if (!isAno) {
                await _navigateToCardWaiting(context, uri.toString());
              } else {
                if (ctx.navigator.canPop() ?? false) {
                  await ctx.navigator.toMainScreen();
                }
              }
            };
          }
          if (navigationHistoryObserver.top?.settings.name != 'auth-landing') {
            await context.navigateToSignInScreen(popAfterSignedIn: true);
          }
        } else {
          await _navigateToCardWaiting(context, uri.toString());
        }
        break;
      case _CreditCardConst.registerWalletPay:
        final data = WalletResponseData.dataFromDeeplink(uri);
        try {
          final jsonString = utf8.decode(base64.decode(data.provisioningData ?? ''));
          final Map<String, dynamic> decodedMap = jsonDecode(jsonString);
          await _goToWalletPay(
            context,
            ScreenArguments(
              map: {
                'data': decodedMap,
                'extraWalletId': data.extraWalletId,
                'rawData': data.provisioningData,
              },
            ),
          );
        } catch (e) {
          await _goToWalletPay(context, ScreenArguments());
        }
        break;
      default:
        await _navigateSelfCareCreditCard(context, uri.toString());
    }
  }

  Future<void> _goToWalletPay(BuildContext context, ScreenArguments? args) {
    return context.navigator.pushFromPackage(
      package: 'CappCards',
      screen: 'WalletPayProvisioningLoadingScreen',
      arguments: args,
    );
  }

  Future<void> _navigateToCardWaiting(BuildContext context, String link) async {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final routes = <CardDeepLinkRouteTo, String>{
      CardDeepLinkRouteTo.manage: 'cards/management',
      CardDeepLinkRouteTo.loyalty: 'loyalty/overview',
      CardDeepLinkRouteTo.activeVoc: 'cards/insur/activation/success',
    };
    final routeTo = DeepLinkRouteMixin.fromLink(link);
    if (routeTo != null) {
      if (routes[routeTo] != navigationHistoryObserver.top?.settings.name) {
        if (navigationHistoryObserver.top?.settings.name == 'auth-landing') {
          await context.navigator.toMainScreen();
        }
        await context.navigator.pushFromPackage(
          package: 'CappCards',
          screen: 'CardDeeplinkWaitingScreen',
          arguments: CardDeeplinkWaitingArgs(link: link),
        );
      }
    }
  }

  Future<void> _navigateSelfCareCreditCard(BuildContext context, String link) async {
    final chunks = link.split('/');
    if (link.contains('credit_card/dashboard')) {
      if (chunks.length == 6) {
        await context.navigator.push(
          path: NavigatorPath.cappSelfService.creditCardDetailScreen,
          arguments: ContractDetailRouteArgs(contractNumber: chunks[5]),
        );
        return;
      }
    }
    if (link.contains('credit_card/statement')) {
      if (chunks.length == 6) {
        await context.navigator.push(
          path: NavigatorPath.cappSelfServiceCore.creditCardStatementsScreen,
          arguments: ContractsRouteArgs(int.parse(chunks[5])),
        );
      }
      return;
    }
    if (link.contains('credit_card/transaction_history')) {
      if (chunks.length == 6) {
        await context.navigator.push(
          path: NavigatorPath.cappSelfService.creditCardTransactionHistoryV3Screen,
          arguments: CreditCardTransactionHistoryRouteArguments(
            accountNumber: int.parse(chunks[5]),
            startOnFlexible: false,
          ),
        );
      }
      return;
    }
  }
}
