import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../../application/deeplinks/handling/ph_deeplink_definition_handler.dart';
import '../../deeplink_navigation_utils.dart';
import '../loans_deeplink_const.dart';

// example: https://app.gma.homecredit.ph/qwartaRenewal
final class QwartaRenewalDeeplinkNavigationHandler extends PhDeeplinkDefinitionHandler
    implements DeeplinkNavigationHandler {
  const QwartaRenewalDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [LoansDeeplinkConst.qwartaRenewal];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final expirationDate = getValueByQueryParams('accountExpirationDate', uri);
    String? formattedExpirationDate;
    try {
      formattedExpirationDate = expirationDate == null
          ? null
          : DateFormat('MMMM d, y').format(
              DateFormat('MM/dd/yyyy hh:mm:ss').parse(expirationDate),
            );
    } catch (e) {
      formattedExpirationDate = null;
    }

    await DeeplinkNavigationUtils.anonymousChecker(
      context,
      (context) async => context.navigator.pushFromPackage(
        package: 'CappSelfService',
        screen: 'QwartaRenewalIntroScreen',
        arguments: QwartaRenewalIntroScreenRouteArguments(
          accountExpirationDate: formattedExpirationDate.toString(),
        ),
      ),
    );
  }
}
