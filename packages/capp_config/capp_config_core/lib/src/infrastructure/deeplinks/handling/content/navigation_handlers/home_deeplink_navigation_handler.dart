import 'package:capp_deals_core/capp_deals_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_ui_core/widgets/index.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class _HomeDeeplinkConstants {
  static const showGetALoan = 'showGetALoan';
}

final class HomeDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const HomeDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [
        ContentDeeplinkConst.home,
        ContentDeeplinkConst.homeAction,
      ];

  @override
  bool verify(Uri uri) {
    // Note: showGetALoan is pointing IN link in webview
    // example: https://app.gma.homecredit.vn/homeaction/showGetALoan
    // example: https://app.gma.homecredit.vn/homeaction/[action_belt_event]
    // example: https://app.gma.homecredit.vn/home
    if (super.verify(uri)) {
      final isHome = uri.pathSegments.first.contains(ContentDeeplinkConst.home);
      if (isHome && uri.pathSegments.length == 1) {
        return true;
      }

      final isHomeAction = uri.pathSegments.first.contains(ContentDeeplinkConst.homeAction);
      if (isHomeAction && uri.pathSegments.length == 2) {
        return true;
      }
    }
    return false;
  }

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    if (uri.pathSegments.length == 1) {
      await context.navigator.toMainScreen();
      return;
    }

    if (uri.pathSegments.contains(_HomeDeeplinkConstants.showGetALoan)) {
      await context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'WebViewScreen',
        arguments: WebViewArguments(url: Constants.loanEmiLink),
      );
    } else {
      await context.navigator.toMainScreen(
        arguments: MainScreenArguments(
          initialTab: TabItem.home,
          arguments: HomeRouteArguments(actionBeltEvent: uri.pathSegments.last),
        ),
      );
    }
  }
}
