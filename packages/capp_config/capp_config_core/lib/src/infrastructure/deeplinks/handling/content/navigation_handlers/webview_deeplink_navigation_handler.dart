import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/deeplink_resolver_bloc.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../../presentation/deeplink_resolver.dart';
import '../content_deeplink_const.dart';

// ignore_for_file: use_build_context_synchronously

final class _WebViewConsts {
  static const title = 'title';
  static const url = 'url';
}

// example: https://app.gma.homecredit.vn/webview?title=test&url=https://www.google.com
final class WebviewDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const WebviewDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.webview];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final title = uri.queryParameters[_WebViewConsts.title];
    final url = uri.queryParameters[_WebViewConsts.url];
    final isInAllowedDomain = await context.read<DeeplinkResolverBloc>().isInAllowedDomain(url);
    if (url?.contains(BnplDomain.pro.getDomain()) ?? false) {
      navigationToBnplInAppWebView(context);
    } else if (isInAllowedDomain) {
      await context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'WebViewScreen',
        arguments: WebViewArguments(title: title, url: url!),
      );
    } else {
      final uri = Uri.tryParse(url ?? '');
      Future.delayed(const Duration(seconds: 1), () {
        showKoyalOverlay<void>(
          KoyalModular.of(context).rootNavigatorKey.currentState!.overlay!.context,
          title: 'Forbidden domain',
          body: KoyalText.body3(color: ColorTheme.of(context).defaultTextColor, uri?.authority ?? 'unknown'),
          primaryButtonBuilder: (context) => PrimaryButton(text: 'Close', onPressed: () => context.navigator.pop()),
        );
      });
    }
  }
}
