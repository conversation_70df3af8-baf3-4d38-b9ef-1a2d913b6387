import 'package:capp_deals_core/capp_deals_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

// example: https://app.gma.homecredit.vn/marketplac
final class MarketplaceDeeplinkNavigationHandler extends DeeplinkDefinitionHandler
    implements DeeplinkNavigationHandler {
  const MarketplaceDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.marketplace];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    await context.navigator.pushFromPackage(
      package: 'CappDeals',
      screen: 'DealsWebViewScreen',
      arguments: DealsWebViewScreenArguments(
        url: uri.toString(),
      ),
    );
  }
}
