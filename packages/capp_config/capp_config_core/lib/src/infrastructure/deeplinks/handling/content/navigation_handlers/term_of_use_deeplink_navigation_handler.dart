import 'dart:async';

import 'package:capp_content_core/capp_content_core.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class TermOfUseDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  //example: https://app.gma.homecredit.ph/how-to-credit-card/term-of-use
  const TermOfUseDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.termOfUse];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    unawaited(
      context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'FixedDocumentScreen',
        arguments: FixedDocumentScreenArguments(type: FixedDocumentType.termsOfUse),
      ),
    );
  }
}
