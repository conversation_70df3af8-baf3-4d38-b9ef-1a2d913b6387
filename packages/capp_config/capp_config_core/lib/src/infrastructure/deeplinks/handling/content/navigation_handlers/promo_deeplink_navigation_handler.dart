import 'package:capp_content/capp_content.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class PromoDetailScreenDeeplinkNavigationHandler extends DeeplinkDefinitionHandler
    implements DeeplinkNavigationHandler {
  const PromoDetailScreenDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.promo];

  @override
  bool verify(Uri uri) {
    // example: https://app.gma.homecredit.ph/promo/dae0c035-ac3a-48e4-f9bb-08dc3801dfb1
    if (super.verify(uri)) {
      if (uri.pathSegments.length == 2) {
        return true;
      }
    }
    return false;
  }

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final promoId = uri.pathSegments.last;

    await context.navigator.pushFromPackage(
      package: 'CappContent',
      screen: 'PromotionDetailScreen',
      arguments: PromotionsDetailScreenArguments(promoId),
    );
  }
}
