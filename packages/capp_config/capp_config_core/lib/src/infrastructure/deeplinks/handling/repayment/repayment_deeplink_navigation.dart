import '../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../application/deeplinks/handling/deeplink_navigation.dart';
import 'navigation_handlers/auto_debit_deeplink_navigation_handler.dart';
import 'repayment_deeplink_extra.dart';

/// Class which defines deeplinks under Repayment (RET_C) team
final class RepaymentDeeplinkNavigation extends DeeplinkNavigation<RepaymentDeeplinkExtra> {
  RepaymentDeeplinkNavigation({required super.logger});

  @override
  List<DeeplinkDefinitionHandler> get deeplinks => [
        const AutoDebitDeeplinkNavigationHandler(),
      ];
}
