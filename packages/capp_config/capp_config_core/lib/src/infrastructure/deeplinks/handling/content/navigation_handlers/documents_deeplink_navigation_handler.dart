import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class DocumentsDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  // example: https://app.gma.homecredit.ph/documents
  const DocumentsDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.documents, PendingActionType.uploadDocument.name];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    await context.navigator.pushFromPackage(
      package: 'CappContent',
      screen: 'DocumentsMenuScreen',
    );
  }
}
