import 'package:capp_content/capp_content.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class BlogDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const BlogDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.blog];

  @override
  bool verify(Uri uri) {
    // example: https://app.gma.homecredit.ph/blog/1e905f42-fed3-4564-3048-08dd142ac01c
    if (super.verify(uri)) {
      if (uri.pathSegments.length == 2) {
        return true;
      }
    }
    return false;
  }

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final blogId = uri.pathSegments.last;

    await context.navigator.pushFromPackage(
      package: 'CappContent',
      screen: 'BlogDetailsScreen',
      arguments: BlogDetailsArguments(blogId: blogId),
    );
  }
}
