import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../credit_cards_deeplink_const.dart';

final class EWalletDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const EWalletDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [CreditCardsDeeplinkConst.eWallet];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    unawaited(
      context.navigator.pushFromPackage(
        package: 'CappEWallet',
        screen: 'EWalletIntroScreen',
      ),
    );
  }
}
