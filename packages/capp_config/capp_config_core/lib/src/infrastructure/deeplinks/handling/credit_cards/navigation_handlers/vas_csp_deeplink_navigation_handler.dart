import 'package:capp_vas_core/capp_vas_core.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../credit_cards_deeplink_const.dart';

final class _VasCspConsts {
  static const String intro = 'intro';
  static const String purchased = 'purchased';
  static List<String> get segments => [
        intro,
        purchased,
      ];
}

final class VasCspDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const VasCspDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [CreditCardsDeeplinkConst.vasCsp];
  // example: https://app.gma.homecredit.vn/vas_csp/intro
  // example: https://app.gma.homecredit.vn/vas_csp/purchased
  @override
  bool verify(Uri uri) {
    if (super.verify(uri)) {
      if (uri.pathSegments.length == 2 && _VasCspConsts.segments.contains(uri.pathSegments[1])) {
        return true;
      }
    }

    return false;
  }

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    switch (uri.pathSegments[1]) {
      case _VasCspConsts.intro:
        await context.navigator.pushFromPackage(
          package: 'CappVas',
          screen: 'CspIntroScreen',
        );
        return;
      case _VasCspConsts.purchased:
        await context.navigator.pushFromPackage(
          package: 'CappVas',
          screen: 'MyInsuranceListScreen',
          arguments: CspInsuranceListRouteArgs(type: InsuranceListType.valid),
        );
        return;
    }
  }
}
