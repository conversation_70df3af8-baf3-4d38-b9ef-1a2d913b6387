import '../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../application/deeplinks/handling/deeplink_navigation.dart';
import 'auth_deeplink_extra.dart';
import 'navigation_handlers/complete_onboarding_deeplink_handler.dart';
import 'navigation_handlers/cuid_pairing_deeplink_handler.dart';
import 'navigation_handlers/details_deeplink_handler.dart';
import 'navigation_handlers/forgot_passcode_deeplink_handler.dart';
import 'navigation_handlers/identification_pending_deeplink_handler.dart';
import 'navigation_handlers/identification_upgrade_to_customer_deeplink_handler.dart';
import 'navigation_handlers/identity_msg_result_deeplink_handler.dart';
import 'navigation_handlers/login_deeplink_handler.dart';
import 'navigation_handlers/login_signup_deeplink_handler.dart';

/// Class which defines deeplinks under Auth (ACQ_C) team
class AuthDeeplinkNavigation extends DeeplinkNavigation<AuthDeeplinkExtra> {
  AuthDeeplinkNavigation({required super.logger});

  @override
  List<DeeplinkDefinitionHandler> get deeplinks => [
        const LoginSignupDeeplinkHandler(),
        const ForgotPasscodeDeeplinkHandler(),
        const DetailsDeeplinkHandler(),
        const LoginDeeplinkHandler(),
        const CuidPairingDeeplinkHandler(),
        const IdentificationUpgradeToCustomerDeeplinkHandler(),
        const IdentificationPendingDeeplinkHandler(),
        const CompleteOnboardingDeeplinkHandler(),
        const IdentityMsgResultDeeplinkHandler(),
      ];
}
