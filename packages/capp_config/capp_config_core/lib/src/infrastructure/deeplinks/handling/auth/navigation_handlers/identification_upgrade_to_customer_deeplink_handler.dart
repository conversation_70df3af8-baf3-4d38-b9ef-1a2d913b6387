import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';

// example: https://app.gma.homecredit.vn/identificationUpgradeToCustomer
final class IdentificationUpgradeToCustomerDeeplinkHandler extends DeeplinkDefinitionHandler
    implements DeeplinkNavigationHandler {
  const IdentificationUpgradeToCustomerDeeplinkHandler();
  @override
  List<String> get deeplinkUniqueSegments => [AuthDeeplinkConst.identificationUpgradeToCustomer];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    unawaited(
      context.navigator.push(
        path: NavigatorPath.cappAuth.retrieveAccount2ndIdentifierScreen,
        arguments: SecondIdentifierScreenArguments(),
      ),
    );
  }
}
