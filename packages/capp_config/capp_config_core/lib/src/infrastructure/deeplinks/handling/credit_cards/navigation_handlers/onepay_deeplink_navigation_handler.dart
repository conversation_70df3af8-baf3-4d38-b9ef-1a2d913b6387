import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../credit_cards_deeplink_const.dart';

final class _OnepayConst {
  static const String csp = 'csp';
}

final class OnepayDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const OnepayDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [CreditCardsDeeplinkConst.onepay];
  // example: https://app.gma.homecredit.vn/onepay/csp
  @override
  bool verify(Uri uri) {
    if (super.verify(uri)) {
      if (uri.pathSegments.length == 2 && uri.pathSegments[1].contains(_OnepayConst.csp)) {
        return true;
      }
    }

    return false;
  }

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final routeName = navigationHistoryObserver.top?.settings.name;
    if (!(routeName ?? '').contains('vas/csp/')) {
      await context.navigator.pushFromPackage(
        package: 'CappVas',
        screen: 'CspIntroScreen',
      );
    }
  }
}
