import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class _HotDealsConsts {
  static const String topSellers = 'topsellers';
}

// example: https://app.gma.homecredit.vn/hot_deals
final class HotDealsDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const HotDealsDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.hotDeals];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    unawaited(
      context.navigator.toMainScreen(
        arguments: MainScreenArguments(
          initialTab: TabItem.communityDeals,
          arguments: CommunityDealsRouteArgument(initialFilter: const FilterId(_HotDealsConsts.topSellers)),
        ),
      ),
    );
  }
}
