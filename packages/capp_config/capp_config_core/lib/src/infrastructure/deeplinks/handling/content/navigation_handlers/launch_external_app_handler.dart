import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../../application/deeplinks/deeplink_resolver_bloc.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

// ignore_for_file: use_build_context_synchronously

// example: https://app.supercupo.co.in/launch?url=https%3A%2F%2Fhomecr.go.link%2F6eepn
final class LaunchExternalAppNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const LaunchExternalAppNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.launchExternalApp];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final url = uri.queryParameters['url'];
    if (url == null || url.isEmpty) return;
    final isInAllowedDomain = await context.read<DeeplinkResolverBloc>().isInAllowedDomain(url);
    if (isInAllowedDomain) {
      await GmaPlatform.launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      final uri = Uri.tryParse(url);
      Future.delayed(const Duration(seconds: 1), () {
        showKoyalOverlay<void>(
          KoyalModular.of(context).rootNavigatorKey.currentState!.overlay!.context,
          title: 'Forbidden domain',
          body: KoyalText.body3(color: ColorTheme.of(context).defaultTextColor, uri?.authority ?? 'unknown'),
          primaryButtonBuilder: (context) => PrimaryButton(text: 'Close', onPressed: () => context.navigator.pop()),
        );
      });
    }
  }
}
