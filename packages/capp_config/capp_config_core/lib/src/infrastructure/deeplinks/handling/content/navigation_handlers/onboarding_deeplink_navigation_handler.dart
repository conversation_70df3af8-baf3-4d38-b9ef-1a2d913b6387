import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:navigator/navigator.dart';

import '../../../../../../capp_config_core.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

// ignore_for_file: use_build_context_synchronously
/// Example https://app.gma.homecredit.vn/onboarding_guideline
final class OnboardingDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const OnboardingDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.onboardingGuideline];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final isFFEnabled = context.get<FeatureFlagBloc>().isFlagEnabledCached(FeatureFlag.vnGamification);
    if (isFFEnabled) {
      final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
      final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
      if (isAnonymous) {
        final externalNavigator = context.externalNavigator;
        if (externalNavigator is ExternalPkgNavigator) {
          externalNavigator.authCompleteCallback = (ctx) async {
            externalNavigator.authCompleteCallback = null;

            final isAno = await ctx.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
            if (!isAno) {
              await checkAndGoToGamificationEventsScreen(context);
            } else {
              if (ctx.navigator.canPop() ?? false) {
                await ctx.navigator.toMainScreen();
              } else {
                await context.navigator.toMainScreen();
              }
            }
          };
        }

        final topScreenName = navigationHistoryObserver.top?.settings.name;
        if (context.isFlagEnabledRead(FeatureFlag.onboardingV4Bos)) {
          if (topScreenName != NavigatorPath.cappAuth.authenticationInitialScreen.split('/').last) {
            if (uri.toString().endsWith('onboarding_guideline')) {
              await context.navigateToSignInScreen(popAfterSignedIn: true);
            }
          }
        } else {
          if (topScreenName != NavigatorPath.cappAuth.authLandingScreen.split('/').last) {
            if (topScreenName == NavigatorPath.cappAuth.crossroadsScreen.split('/').last) {
              await context.navigator.toMainScreen();
            }
            if (uri.toString().endsWith('onboarding_guideline')) {
              await context.navigateToSignInScreen(
                popAfterSignedIn: true,
              );
            }
          }
        }
      } else {
        await checkAndGoToGamificationEventsScreen(context);
      }
    }
  }

  Future<void> checkAndGoToGamificationEventsScreen(BuildContext context) async {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final topScreenName = navigationHistoryObserver.top?.settings.name;
    if(topScreenName != 'gamification_events_screen') {
      await context.navigator.pushFromPackage(
        package: 'CappGamification',
        screen: 'GamificationEventsScreen',
      );
    }
  }
}
