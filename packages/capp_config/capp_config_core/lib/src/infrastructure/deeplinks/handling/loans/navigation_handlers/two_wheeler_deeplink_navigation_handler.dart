import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:navigator/navigator.dart';

import '../../../../../application/deeplinks/deeplink_resolver_bloc.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../loans_deeplink_const.dart';

// example:  https://app.gma.homecredit.vn/two_wheeler

// ignore_for_file: use_build_context_synchronously

final class TwoWheelerDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const TwoWheelerDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [LoansDeeplinkConst.twoWheeler];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    if (isAnonymous) {
      final externalNavigator = context.externalNavigator;
      if (externalNavigator is ExternalPkgNavigator) {
        externalNavigator.authCompleteCallback = (ctx) async {
          externalNavigator.authCompleteCallback = null;
          final isAno = await ctx.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
          if (!isAno) {
            await navigateToTW(context);
          } else {
            if (ctx.navigator.canPop() ?? false) {
              await ctx.navigator.toMainScreen();
            } else {
              await context.navigator.toMainScreen();
            }
          }
        };
      }
      if (navigationHistoryObserver.top?.settings.name != 'auth-landing') {
        await context.navigateToSignInScreen(popAfterSignedIn: true);
      }
    } else {
      await navigateToTW(context);
    }
  }

  Future<void> navigateToTW(BuildContext context) async {
    unawaited(
      context.navigator.push(
        path: NavigatorPath.cappLoanOriginationUnified.tWOfferCheckingScreen,
      ),
    );
  }
}
