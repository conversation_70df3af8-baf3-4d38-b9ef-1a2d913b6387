import '../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../application/deeplinks/handling/deeplink_navigation.dart';
import 'content_deeplink_extra.dart';
import 'navigation_handlers/blog_deeplink_navigation_handler.dart';
import 'navigation_handlers/blogs_deeplink_navigation_handler.dart';
import 'navigation_handlers/community_deals_navigation_handler.dart';
import 'navigation_handlers/deals_deeplink_navigation_handler.dart';
import 'navigation_handlers/discounts_deeplink_navigation_handler.dart';
import 'navigation_handlers/documents_deeplink_navigation_handler.dart';
import 'navigation_handlers/faq_deeplink_navigation_handler.dart';
import 'navigation_handlers/games_deeplink_navigation_handler.dart';
import 'navigation_handlers/home_deeplink_navigation_handler.dart';
import 'navigation_handlers/hot_deals_deeplink_navigation_handler.dart';
import 'navigation_handlers/inbox_deeplink_navigation_handler.dart';
import 'navigation_handlers/launch_external_app_handler.dart';
import 'navigation_handlers/live_chat_deeplink_navigation_handler.dart';
import 'navigation_handlers/loans_deeplink_navigation_handler.dart';
import 'navigation_handlers/map_deeplink_navigation_handler.dart';
import 'navigation_handlers/marketplace_deeplink_navigation_handler.dart';
import 'navigation_handlers/onboarding_deeplink_navigation_handler.dart';
import 'navigation_handlers/pas_deeplink_navigation_handler.dart';
import 'navigation_handlers/promo_deeplink_navigation_handler.dart';
import 'navigation_handlers/promos_deeplink_navigation_handler.dart';
import 'navigation_handlers/survey_deeplink_navigation_handler.dart';
import 'navigation_handlers/term_of_use_deeplink_navigation_handler.dart';
import 'navigation_handlers/webview_deeplink_navigation_handler.dart';

/// Class which defines deeplinks under content team
class ContentDeeplinkNavigation extends DeeplinkNavigation<ContentDeeplinkExtra> {
  ContentDeeplinkNavigation({required super.logger});

  @override
  List<DeeplinkDefinitionHandler> get deeplinks => [
        const SurveyDeeplinkNavigationHandler(),
        const ProductAndServicesDeeplinkNavigationHandler(),
        const TermOfUseDeeplinkNavigationHandler(),
        const BlogsDeeplinkNavigationHandler(),
        const BlogDeeplinkNavigationHandler(),
        const FaqDeeplinkNavigationHandler(),
        const DocumentsDeeplinkNavigationHandler(),
        const InboxDeeplinkNavigationHandler(),
        const HomeDeeplinkNavigationHandler(),
        const GamesDeeplinkNavigationHandler(),
        const LiveChatDeeplinkNavigationHandler(),
        const DealsDeeplinkNavigationHandler(),
        const OnboardingDeeplinkNavigationHandler(),
        const HotDealsDeeplinkNavigationHandler(),
        const MarketplaceDeeplinkNavigationHandler(),
        const DiscountsDeeplinkNavigationHandler(),
        const CommunityDealsNavigationHandler(),
        const LoansDeeplinkNavigationHandler(),
        const WebviewDeeplinkNavigationHandler(),
        const MapDeeplinkNavigationHandler(),
        const PromoDetailScreenDeeplinkNavigationHandler(),
        const PromotionsScreenDeeplinkNavigationHandler(),
        const LaunchExternalAppNavigationHandler(),
      ];
}
