import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../deeplink_navigation_utils.dart';
import '../repayment_deeplink_const.dart';

final class AutoDebitDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  static const String contractNumberKey = 'contractNumber';
  static const String fromFlowKey = 'fromFlow';

  const AutoDebitDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [
        RepaymentDeeplinkConst.autoDebit,
        RepaymentDeeplinkConst.result,
      ];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final link = uri.toString();
    if (link.contains('auto_debit/result/zalopay')) {
      await context.navigator.pushFromPackage(
        package: 'CappRepayment',
        screen: 'RepaymentAdaCheckResultScreen',
        arguments: ScreenArguments(
          map: <String, dynamic>{
            'deeplink': link,
            'previousRouteName': (context.navigator.canPop() ?? true) ? 'repayment_ada_main_screen' : 'home_screen',
          },
        ),
      );
    }
    // AD management deeplink: https://app.gma.homecredit.vn/auto_debit/management
    else if (link.contains('auto_debit/management')) {
      await DeeplinkNavigationUtils.anonymousChecker(
        context,
        (context) async {
          final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
          final navigationHistory = navigationHistoryObserver.history.map((h) => h.settings.name).toList();
          if (navigationHistory.contains('repayment_ada_management_screen')) {
            context.navigator.popUntilFromPackage('CappRepayment', 'RepaymentAdaManagementScreen');
          } else {
            await context.navigator.pushFromPackage(
              package: 'CappRepayment',
              screen: 'RepaymentAdaManagementScreen',
              arguments: ScreenArguments(
                map: {
                  fromFlowKey: 'managementDeepLink',
                },
              ),
            );
          }
        },
      );
    } else if (link.contains('auto_debit/registration')) {
      final linkUri = Uri.tryParse(link);
      final linkParams = linkUri?.queryParameters;
      String? contractNumber;

      if (linkParams != null && linkParams.containsKey(contractNumberKey)) {
        contractNumber = linkParams[contractNumberKey];
      }

      await DeeplinkNavigationUtils.anonymousChecker(
        context,
        (context) async {
          final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
          final navigationHistory = navigationHistoryObserver.history.map((h) => h.settings.name).toList();
          if (navigationHistory.last == 'repayment_ada_management_screen') {
            await context.navigator.pushReplacementFromPackage(
              package: 'CappRepayment',
              screen: 'RepaymentAdaManagementScreen',
              arguments: ScreenArguments(
                map: {
                  fromFlowKey: 'registrationDeepLink',
                  contractNumberKey: contractNumber,
                },
              ),
            );
          } else if (navigationHistory.contains('repayment_ada_management_screen')) {
            context.navigator.popUntilFromPackage('CappRepayment', 'RepaymentAdaManagementScreen');
          } else {
            await context.navigator.pushFromPackage(
              package: 'CappRepayment',
              screen: 'RepaymentAdaManagementScreen',
              arguments: ScreenArguments(
                map: {
                  fromFlowKey: 'registrationDeepLink',
                  contractNumberKey: contractNumber,
                },
              ),
            );
          }
        },
      );
    }
  }
}
