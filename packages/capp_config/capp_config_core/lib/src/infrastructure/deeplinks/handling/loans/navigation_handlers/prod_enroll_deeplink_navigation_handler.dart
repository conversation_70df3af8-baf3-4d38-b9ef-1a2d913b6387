import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../loans_deeplink_const.dart';

// example: https://app.gma.homecredit.vn/prod-enroll
final class ProdEnrollDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const ProdEnrollDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [LoansDeeplinkConst.prodEnroll];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    await context.navigator.push(
      path: NavigatorPath.cappLoanOriginationUnified.productionEnrollScreen,
    );
  }
}
