import 'package:capp_domain/capp_domain.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';
import '../content_deeplink_extra.dart';

final class InboxDeeplinkNavigationHandler extends DeeplinkDefinitionHandler
    implements DeeplinkNavigationHandlerWithExtra<ContentDeeplinkExtra> {
  const InboxDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [
        ContentDeeplinkConst.inbox,
        ContentDeeplinkConst.inboxMessage,
        ContentDeeplinkConst.messaging,
      ];

  @override
  Future<void> navigate(BuildContext context, Uri uri, {required ContentDeeplinkExtra extra}) async {
    switch (uri.pathSegments.first) {
      // This one if for push notifications
      case ContentDeeplinkConst.inboxMessage:
        if (extra.hasMessage) {
          await context.navigator.pushFromPackage(
            package: 'CappHome',
            screen: 'MessageDetailScreen',
            arguments: NotificationMessageScreenArguments(extra.deeplinkModel!.message!),
          );
        }
        return;
      case ContentDeeplinkConst.inbox:
      case ContentDeeplinkConst.messaging:
        await context.navigator.pushFromPackage(
          package: 'CappHomeCore',
          screen: 'InboxScreen',
          arguments: InboxScreenArguments(InboxMessageType.all),
        );
        return;
    }
  }
}
