import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class DealsDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const DealsDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.deals];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    // Get values
    final categoryId = getValueByQueryParams(DeeplinkConstants.deeplinkCategoryId, uri);
    final categoryKey = getValueByQueryParams(DeeplinkConstants.deeplinkCategoryKey, uri);

    // Navigate
    await context.navigator.toMainScreen(
      arguments: MainScreenArguments(
        initialTab: TabItem.communityDeals,
        arguments: CommunityDealsRouteArgument(
          categoryIdFromDynamiclink: categoryId,
          categoryKeyFromDynamiclink: categoryKey,
        ),
      ),
    );
  }
}
