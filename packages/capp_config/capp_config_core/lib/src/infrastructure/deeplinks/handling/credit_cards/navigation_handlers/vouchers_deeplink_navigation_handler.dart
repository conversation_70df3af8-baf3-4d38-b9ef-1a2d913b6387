import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_evoucher_core/capp_evoucher_core.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../../application/deeplinks/deeplink_resolver_bloc.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../credit_cards_deeplink_const.dart';

// ignore_for_file: use_build_context_synchronously
// example: https://app.gma.homecredit.ph/vouchers
final class VouchersDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const VouchersDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [CreditCardsDeeplinkConst.vouchers];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    if (isAnonymous) {
      final externalNavigator = context.externalNavigator;
      if (externalNavigator is ExternalPkgNavigator) {
        externalNavigator.authCompleteCallback = (ctx) async {
          externalNavigator.authCompleteCallback = null;
          final isAno = await ctx.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
          if (!isAno) {
            await context.navigator.pushFromPackage(
              package: 'CappEVoucher',
              screen: 'EVoucherEntryPoint',
              arguments: EVoucherIntroRouteArgs(isBackToHomePage: true),
            );
          } else {
            if (ctx.navigator.canPop() ?? false) {
              await ctx.navigator.toMainScreen();
            }
          }
        };
      }
      if (navigationHistoryObserver.top?.settings.name != 'auth-landing') {
        await context.navigateToSignInScreen(popAfterSignedIn: true);
      }
    } else {
      await context.navigator.pushFromPackage(
        package: 'CappEVoucher',
        screen: 'EVoucherEntryPoint',
        arguments: EVoucherIntroRouteArgs(),
      );
    }
  }
}
