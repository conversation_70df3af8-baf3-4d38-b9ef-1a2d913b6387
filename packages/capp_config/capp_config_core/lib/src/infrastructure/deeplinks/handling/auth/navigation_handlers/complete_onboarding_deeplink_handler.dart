import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';

final class CompleteOnboardingDeeplinkHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const CompleteOnboardingDeeplinkHandler();

  @override
  List<String> get deeplinkUniqueSegments => [AuthDeeplinkConst.completeOnboarding];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final authenticationSessionId = getValueByQueryParams('authenticationSessionId', uri);
    final loginSessionId = getValueByQueryParams('loginSessionId', uri);
    final passwordLength = getValueByQueryParams('passwordLength', uri);

    if (loginSessionId != null) {
      unawaited(
        context.navigator.push(
          path: NavigatorPath.cappAuth.loginPinScreen,
          arguments: PinScreenArguments(
            loginSessionId: loginSessionId,
            passwordLength: int.tryParse(passwordLength ?? '6'),
          ),
        ),
      );
    } else {
      unawaited(
        context.navigator.push(
          path: NavigatorPath.cappAuth.registrationSetPasswordScreen,
          arguments: SetPasswordScreenArguments(
            sessionId: authenticationSessionId,
          ),
        ),
      );
    }
  }
}
