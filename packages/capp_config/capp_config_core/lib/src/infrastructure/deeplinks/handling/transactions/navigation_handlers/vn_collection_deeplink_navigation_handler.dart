import 'package:capp_payment_core/capp_payment_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../../application/deeplinks/handling/vn_deeplink_definition_handler.dart';
import '../../deeplink_navigation_utils.dart';
import '../transactions_deeplink_const.dart';

/// Format for Transaction(Ret_B) deeplink:
///   VN country:
///     eBills:
///       homezuiapp://app.gma.homecredit.vn/bills
///       homezuiapp://app.gma.homecredit.vn/electric
///       homezuiapp://app.gma.homecredit.vn/water
///     eLoads:
///       homezuiapp://app.gma.homecredit.vn/mobiletopup
///     QR HPL:
///        homezuiapp://app.gma.homecredit.vn/qr?transid=xxxx&partnercusid=xxxxx&sourcesystem=xxxx&channel=HPL

final class VnCollectionDeeplinkNavigationHandler extends VnDeeplinkDefinitionHandler
    implements DeeplinkNavigationHandler {
  const VnCollectionDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [
        TransactionsDeeplinkConst.bills,
        TransactionsDeeplinkConst.electric,
        TransactionsDeeplinkConst.water,
        TransactionsDeeplinkConst.mobileTopUp,
        TransactionsDeeplinkConst.qr,
      ];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    switch (uri.pathSegments.first) {
      // eBills
      case TransactionsDeeplinkConst.bills:
      case TransactionsDeeplinkConst.electric:
      case TransactionsDeeplinkConst.water:
        await DeeplinkNavigationUtils.anonymousChecker(
          context,
          (context) async => context.navigator.pushFromPackage(
            package: 'CappTransactions',
            screen: 'EbillTransactionEntrypoint',
          ),
        );
        return;
      // eLoads
      case TransactionsDeeplinkConst.mobileTopUp:
        await DeeplinkNavigationUtils.anonymousChecker(
          context,
          (context) async => context.navigator.pushFromPackage(
            package: 'CappTransactions',
            screen: 'EloadsTransactionEntrypoint',
          ),
        );
        return;
      // QR HPL
      case TransactionsDeeplinkConst.qr:
        await DeeplinkNavigationUtils.anonymousChecker(
          context,
          (context) async => context.navigator.pushFromPackage(
            package: 'CappTransactions',
            screen: 'QrTransactionEntrypoint',
            arguments: uri.queryParameters.isNotEmpty ? DeepLinkEntryPointArgs(uri: uri) : null,
          ),
        );
        return;
    }
  }
}
