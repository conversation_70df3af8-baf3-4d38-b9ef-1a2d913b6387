import 'dart:async';

import 'package:capp_home_core/capp_home_core.dart';
import 'package:flutter/material.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class GamesDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const GamesDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [
        ContentDeeplinkConst.game,
        ContentDeeplinkConst.gameEvent,
        ContentDeeplinkConst.gameMission,
        ContentDeeplinkConst.gameRewardWallet,
      ];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    switch (uri.pathSegments.first) {
      case ContentDeeplinkConst.game:
        unawaited(GamesLauncherService.launchGameHub(context));
        return;
      case ContentDeeplinkConst.gameRewardWallet:
        unawaited(GamesLauncherService.launchGameRewardWallet(context));
        return;
      case ContentDeeplinkConst.gameMission:
        unawaited(GamesLauncherService.launchGameMission(context));
        return;
      case ContentDeeplinkConst.gameEvent:
        unawaited(GamesLauncherService.launchGameEvent(context));
        return;
    }
  }
}
