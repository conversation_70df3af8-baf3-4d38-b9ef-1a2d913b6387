import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

// example: https://app.gma.homecredit.vn/discounts
final class _DiscountsConst {
  static const coupons = 'coupons';
}

final class DiscountsDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const DiscountsDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.discounts];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    unawaited(
      context.navigator.toMainScreen(
        arguments: MainScreenArguments(
          initialTab: TabItem.communityDeals,
          arguments: CommunityDealsRouteArgument(initialFilter: const FilterId(_DiscountsConst.coupons)),
        ),
      ),
    );
  }
}
