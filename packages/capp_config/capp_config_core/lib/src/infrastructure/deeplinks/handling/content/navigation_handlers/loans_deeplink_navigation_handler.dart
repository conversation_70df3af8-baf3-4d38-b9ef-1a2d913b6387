import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

// example: https://app.gma.homecredit.vn/loans
final class LoansDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const LoansDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.loans];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    unawaited(
      context.navigator.toMainScreen(arguments: MainScreenArguments(initialTab: TabItem.loans)),
    );
  }
}
