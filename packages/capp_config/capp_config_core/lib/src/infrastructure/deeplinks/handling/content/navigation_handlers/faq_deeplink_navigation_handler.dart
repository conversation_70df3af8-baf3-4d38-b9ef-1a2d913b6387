import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class FaqDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  // example: https://app.gma.homecredit.ph/faq
  const FaqDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.faq];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    await context.navigator.pushFromPackage(
      package: 'CappContent',
      screen: context.isFlagEnabledRead(KoyalFeatureFlag.helpCenter) ? 'HelpCenterScreen' : 'FaqMenuScreen',
    );
  }
}
