import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';

// example: https://app.gma.homecredit.vn/forgot-passcode
final class ForgotPasscodeDeeplinkHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const ForgotPasscodeDeeplinkHandler();
  @override
  List<String> get deeplinkUniqueSegments => [AuthDeeplinkConst.forgotPasscode];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    await context.navigator.pushFromPackage(package: 'CappAuth', screen: 'PinPassRecoveryEntryScreen');
  }
}
