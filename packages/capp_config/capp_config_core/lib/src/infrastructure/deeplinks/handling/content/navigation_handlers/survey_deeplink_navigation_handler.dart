import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_content/capp_content.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../../../capp_config_core.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class SurveyUriKeys {
  SurveyUriKeys._();

  static const String sessionId = 'surveySessionId';
  static const String welcome = 'welcome';
  static const String surveyId = 'SurveyID';
}

// ignore_for_file: use_build_context_synchronously
final class SurveyDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const SurveyDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.surveyPath];

  @override
  bool verify(Uri uri) {
    if (super.verify(uri)) {
      // example:https://app.gma.homecredit.vn/survey/Ipay88Corporation
      if (uri.pathSegments.length == 2) {
        return true;
      }
      // Used from pending actions
      // example: https://app.gma.homecredit.vn/survey?surveySessionId=Ipay88Corporation
      if (uri.pathSegments.length == 1 && uri.queryParameters.containsKey(SurveyUriKeys.sessionId)) {
        return true;
      }
    }

    return false;
  }

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    // Normal deeplink
    final surveyCategoryId = uri.pathSegments.length == 2 ? uri.pathSegments.last : null;
    // Deeplink from PA
    final surveySessionId = uri.pathSegments.length == 1 ? uri.queryParameters[SurveyUriKeys.sessionId] : null;
    //Deeplink survey welcome kit
    if (surveyCategoryId?.contains(SurveyUriKeys.welcome) ?? false) {
      if (context.isFlagEnabledRead(FeatureFlag.vnWelcomeKitSurvey)) {
        final surveyId = uri.queryParameters[SurveyUriKeys.surveyId];
        final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
        if (!isAnonymous) {
          await _checkAndNavigateToWelcomeKitSurveyScreen(context, surveyId: surveyId);
        } else {
          final externalNavigator = context.externalNavigator;
          if (externalNavigator is ExternalPkgNavigator) {
            externalNavigator.authCompleteCallback = (ctx) async {
              externalNavigator.authCompleteCallback = null;
              final isAno = await ctx.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
              if (!isAno) {
                await _checkAndNavigateToWelcomeKitSurveyScreen(context, surveyId: surveyId);
              } else {
                await context.navigator.toMainScreen();
              }
            };
          } else {
            await context.navigateToSignInScreen(popAfterSignedIn: true);
          }
        }
      }
    } else {
      await context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'SurveyScreen',
        arguments: SurveyScreenArguments(
          surveyCategoryId: surveyCategoryId,
          surveySessionId: surveySessionId,
        ),
      );
    }
  }

  Future<void> _checkAndNavigateToWelcomeKitSurveyScreen(BuildContext context, {String? surveyId}) async {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final topScreenName = navigationHistoryObserver.top?.settings.name;
    final isDisplayingSurveyScreen =
        topScreenName == 'WelcomeKitSurveyScreen' || topScreenName == 'WelcomeKitSurveyListScreen';
    if (!isDisplayingSurveyScreen) {
      // This delay is to resolve the issue conflict navigation with Biometric
      // setting after access to app by deeplink.
      await Future.delayed(const Duration(milliseconds: 500), () async {
        await context.navigator.pushFromPackage(
          package: 'CappWelcomeKit',
          screen: 'WelcomeKitSurveyListScreen',
          arguments: ScreenArguments(
            map: {
              SurveyUriKeys.surveyId: surveyId,
            },
          ),
        );
      });
    }
  }
}
