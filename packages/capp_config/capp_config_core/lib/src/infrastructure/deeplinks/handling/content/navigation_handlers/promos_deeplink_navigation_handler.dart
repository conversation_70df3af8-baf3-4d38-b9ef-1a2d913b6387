import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../content_deeplink_const.dart';

final class PromotionsScreenDeeplinkNavigationHandler extends DeeplinkDefinitionHandler
    implements DeeplinkNavigationHandler {
  // example: https://app.gma.homecredit.ph/promos
  const PromotionsScreenDeeplinkNavigationHandler();

  @override
  List<String> get deeplinkUniqueSegments => [ContentDeeplinkConst.promos];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    await context.navigator.pushFromPackage(
      package: 'CappContent',
      screen: 'PromotionsScreen',
    );
  }
}
