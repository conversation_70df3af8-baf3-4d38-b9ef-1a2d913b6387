// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:logger/logger.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../capp_config_core.dart';

// This function is original copied from deeplink resolver
final class DeeplinkNavigationUtils {
  DeeplinkNavigationUtils._();

  /// Make sure that user must signed in to navigate to destination deeplink.
  static Future<void> anonymousChecker(
    BuildContext context,
    Function(BuildContext context) navigateToDestination,
  ) async {
    final deeplinkResolverBloc = context.read<DeeplinkResolverBloc>();
    // TODO(anyone): Improvement UserState checking and DeeplinkResolverState handling.
    /// Pre-produce steps: open app (without login) -> push SignInScreen (auth-landing) -> click on ContinueAsGuest button -> AuthBloc redirects to mainScreen.
    /// Actual result: It's not popping from SignInScreen to MainScreen (home_screen), it replaces SignInScreen by MainScreen -> DeeplinkResolver will continue handle current state -> if it's required to sign in -> the second SignInScreen will be shown after.
    /// Expected result: If user already chose ContinueAsGuest, then at DeeplinkResolver can know that and bypass.

    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    for (final o in navigationHistoryObserver.history) {
      context.get<Logger>().d(
            'history: routeName=${o.settings.name}, routeSettings=${o.settings.arguments}, route=$o',
          );
    }
    for (final o in navigationHistoryObserver.poppedRoutes) {
      context.get<Logger>().d(
            'poppedRoutes: routeName=${o.settings.name}, routeSettings=${o.settings.arguments}, route=$o',
          );
    }
    final redirect = context.read<AuthBloc>().state.redirect;
    context.get<Logger>().d(
          'redirect: $redirect',
        );
    final isAfterSignInScreenWhenFirstStart = redirect == RedirectTo.mainScreen &&
        navigationHistoryObserver.poppedRoutes.isEmpty &&
        (navigationHistoryObserver.history.length == 1 &&
            navigationHistoryObserver.history.single.settings.name == 'home_screen') &&
        deeplinkResolverBloc.pendingDeeplink != null;
    final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
    if (isAnonymous) {
      if (isAfterSignInScreenWhenFirstStart) {
        //do nothing
        /// A bug on Loads, Bills VN-ActionBelt
        /// Pre-produce steps: trigger deeplink outside -> open app -> SignInScreen -> Continues as Guest -> click on action belt.
        /// Actual result: do nothing.
        /// Expected result: anonymous check then navigate to destination.
        /// Solution: check deeplinkResolverBloc.defferedDeeplink != null.
      } else {
        final externalNavigator = context.externalNavigator;
        if (externalNavigator is ExternalPkgNavigator) {
          externalNavigator.authCompleteCallback = (ctx) async {
            externalNavigator.authCompleteCallback = null;

            final isAno = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
            if (!isAno) {
              // bug Loads, Bills VN-ActionBelt after sign in
              // fix pop after signed in
              ctx.navigator.pop();
              await navigateToDestination(ctx);
            } else if (ctx.navigator.canPop() ?? false) {
              ctx.navigator.pop();
            } else {
              unawaited(ctx.navigator.toMainScreen());
            }
          };
        }
        final isUserOnLoginScreen = context.isKeyInWidgetTree(const ValueKey('__continueAsGuestButton__'));
        if (isUserOnLoginScreen) {
          return;
        }
        await context.navigateToSignInScreen(popAfterSignedIn: true);
      }
    } else {
      await navigateToDestination(context);
    }
  }
}
