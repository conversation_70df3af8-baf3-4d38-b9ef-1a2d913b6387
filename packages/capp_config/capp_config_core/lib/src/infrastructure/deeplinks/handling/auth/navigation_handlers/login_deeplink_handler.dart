import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/widgets.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';

// example: https://app.gma.homecredit.vn/log-in
// example: https://app.gma.homecredit.vn/login
final class LoginDeeplinkHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const LoginDeeplinkHandler();
  @override
  List<String> get deeplinkUniqueSegments => [
        AuthDeeplinkConst.login,
        AuthDeeplinkConst.loginWithDelimiter,
      ];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    unawaited(context.navigateToSignInScreen());
  }
}
