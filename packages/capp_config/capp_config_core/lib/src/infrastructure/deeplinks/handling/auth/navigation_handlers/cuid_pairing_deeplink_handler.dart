import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigator/navigator.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';

// example https://app.gma.homecredit.vn/cuidPairing
final class CuidPairingDeeplinkHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const CuidPairingDeeplinkHandler();

  @override
  List<String> get deeplinkUniqueSegments => [AuthDeeplinkConst.cuidPairing];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    if (context.isFlagEnabledRead(FeatureFlag.retrieveAccountBos)) {
      unawaited(
        context.navigator.push(
          path: NavigatorPath.cappAuth.retrieveAccount2ndIdentifierScreen,
          arguments: SecondIdentifierScreenArguments(),
        ),
      );
    } else {
      unawaited(
        context.navigator.pushFromPackage(
          package: 'CappAuth',
          screen: 'SecondIdHoselPairingScreen',
        ),
      );
    }
  }
}
