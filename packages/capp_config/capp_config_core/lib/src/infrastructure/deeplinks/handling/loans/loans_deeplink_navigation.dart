import '../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../application/deeplinks/handling/deeplink_navigation.dart';
import 'loans_deeplink_extra.dart';
import 'navigation_handlers/cd_deeplink_navigation_handler.dart';
import 'navigation_handlers/prod_enroll_deeplink_navigation_handler.dart';
import 'navigation_handlers/qwarta_renewal_deeplink_navigation_handler.dart';
import 'navigation_handlers/set_trait_deeplink_navigation_handler.dart';
import 'navigation_handlers/two_wheeler_deeplink_navigation_handler.dart';

/// Class which defines deeplinks under Offer and Loans teams (ACQ_A & ACQ_B)
final class LoansDeeplinkNavigation extends DeeplinkNavigation<LoansDeeplinkExtra> {
  LoansDeeplinkNavigation({required super.logger});

  @override
  List<DeeplinkDefinitionHandler> get deeplinks => [
        const SetTraitDeeplinkNavigationHandler(),
        const ProdEnrollDeeplinkNavigationHand<PERSON>(),
        const CdDeeplinkNavigationHandler(),
        const TwoWheelerDeeplinkNavigationHandler(),
        const QwartaRenewalDeeplinkNavigationHandler(),
      ];
}
