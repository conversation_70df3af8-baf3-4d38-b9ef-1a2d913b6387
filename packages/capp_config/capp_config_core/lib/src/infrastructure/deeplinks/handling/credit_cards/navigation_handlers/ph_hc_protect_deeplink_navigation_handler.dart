import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../../application/deeplinks/handling/ph_deeplink_definition_handler.dart';
import '../../deeplink_navigation_utils.dart';
import '../credit_cards_deeplink_const.dart';

// example: https://app.gma.homecredit.ph/hc_protect
final class PhHcProtectDeeplinkNavigationHandler extends PhDeeplinkDefinitionHandler
    implements DeeplinkNavigationHandler {
  const PhHcProtectDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [CreditCardsDeeplinkConst.hcProtect];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    await DeeplinkNavigationUtils.anonymousChecker(
      context,
      (context) async => context.navigator.pushFromPackage(
        package: 'CappProtectionPlan',
        screen: 'HomeCreditProtectDashboard',
      ),
    );
  }
}
