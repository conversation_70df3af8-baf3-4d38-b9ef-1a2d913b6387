import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:navigator/navigator.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';
import '../auth_deeplink_extra.dart';

final class IdentityMsgResultDeeplinkHandler extends DeeplinkDefinitionHandler
    implements DeeplinkNavigationHandlerWithExtra<AuthDeeplinkExtra> {
  const IdentityMsgResultDeeplinkHandler();
  @override
  List<String> get deeplinkUniqueSegments => [AuthDeeplinkConst.identityMessageResult];

  @override
  Future<void> navigate(BuildContext context, Uri uri, {required AuthDeeplinkExtra extra}) async {
    final payload = extra.deeplinkModel?.message?.data?.payload;
    if (payload != null) {
      final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
      final name = navigationHistoryObserver.top?.settings.name;
      // if identify verification is the last route in the stack, we should replace it so the pooling
      // on that screen is no processed anymore as the result has been handled by notification
      final replaceRoute = name?.contains('identify_verification') == true;
      final navigationMethod = replaceRoute ? context.navigator.pushReplacement : context.navigator.push;

      final identifyDataMessageResult = IdentifyDataMessageResult.fromPayload(payload);
      final isStillValid = identifyDataMessageResult.validTo != null
          ? DateTime.tryParse(identifyDataMessageResult.validTo!)?.toUtc().isAfter(DateTime.now().toUtc())
          : true;

      if (isStillValid == false) {
        await navigationMethod(
          path: NavigatorPath.cappAuth.identifyExpiredScreen,
          arguments: IdentifyExpiredScreenArgs(
            identifyFlowType: identifyDataMessageResult.isRetrieveAccount
                ? IdentifyFlowType.retrieveAccount
                : IdentifyFlowType.onboarding,
            phoneNumber: identifyDataMessageResult.phoneNumber,
          ),
        );
        return;
      }

      if (identifyDataMessageResult.identificationResult == IdentifyBosResult.approved) {
        await navigationMethod(
          path: NavigatorPath.cappAuth.identifyApprovedScreen,
          arguments: IdentifyResultApprovedScreenArgs(
            authenticationSessionId: identifyDataMessageResult.sessionId,
            challenge: identifyDataMessageResult.challenge,
            publicKey: identifyDataMessageResult.publicKey,
            identifyFlowType: identifyDataMessageResult.isRetrieveAccount
                ? IdentifyFlowType.retrieveAccount
                : IdentifyFlowType.onboarding,
            loginSessionId: identifyDataMessageResult.loginSessionId,
            passwordLength: identifyDataMessageResult.passwordLength != null
                ? int.tryParse(identifyDataMessageResult.passwordLength!)
                : null,
          ),
        );
      } else if (identifyDataMessageResult.identificationResult == IdentifyBosResult.rejected) {
        await navigationMethod(
          path: NavigatorPath.cappAuth.identifyRejectedScreen,
          arguments: IdentifyResultRejectedScreenArgs(
            identifyFlowType: identifyDataMessageResult.isRetrieveAccount
                ? IdentifyFlowType.retrieveAccount
                : IdentifyFlowType.onboarding,
          ),
        );
      } else if (identifyDataMessageResult.identificationResult == IdentifyBosResult.canceled) {
        if (identifyDataMessageResult.sessionId != null) {
          final path = identifyDataMessageResult.isRetrieveAccount
              ? NavigatorPath.cappAuth.retrieveAccountIdentifyCanceledScreen
              : NavigatorPath.cappAuth.authenticationIdentifyCanceledScreen;

          await navigationMethod(
            path: path,
            arguments: IdentifyCanceledScreenArgs(
              identifyFlowType: identifyDataMessageResult.isRetrieveAccount
                  ? IdentifyFlowType.retrieveAccount
                  : IdentifyFlowType.onboarding,
              bypassSelection: true,
              sessionId: identifyDataMessageResult.sessionId!,
              retakeSelfie: identifyDataMessageResult.selfie,
              retakeIdCard: identifyDataMessageResult.idCard,
            ),
          );
        }
      } else if (identifyDataMessageResult.identificationResult == IdentifyBosResult.failedToFinish) {
        await navigationMethod(
          path: NavigatorPath.cappAuth.identifyFailedToFinishScreen,
          arguments: IdentifyResultFailedToFinishScreenArgs(
            identifyFlowType: identifyDataMessageResult.isRetrieveAccount
                ? IdentifyFlowType.retrieveAccount
                : IdentifyFlowType.onboarding,
          ),
        );
      }
    }
  }
}
