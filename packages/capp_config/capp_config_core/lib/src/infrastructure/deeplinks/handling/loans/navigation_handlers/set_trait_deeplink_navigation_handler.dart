import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../loans_deeplink_const.dart';

final class SetTraitDeeplinkNavigationHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const SetTraitDeeplinkNavigationHandler();
  @override
  List<String> get deeplinkUniqueSegments => [LoansDeeplinkConst.setTrait];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final traits = getValueByQueryParams('traits', uri) ?? '';

    if (traits.isNotEmpty) {
      final keyValuePairs = <Map<String, String>>[];
      final pairs = traits.split(',');
      for (final pair in pairs) {
        final keyValue = pair.split(':');
        if (keyValue.length == 2) {
          keyValuePairs.add({keyValue[0]: keyValue[1]});
        }
      }
      if (keyValuePairs.isNotEmpty) {
        unawaited(
          context.navigator.pushTyped(
            package: KoyalShared,
            screen: SetTraitsScreen,
            arguments: SetTraitsArguments(traits: keyValuePairs),
          ),
        );
      }
    }
  }
}
