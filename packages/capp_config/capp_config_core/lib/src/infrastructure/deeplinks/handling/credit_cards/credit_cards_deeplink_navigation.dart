import '../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../../../../application/deeplinks/handling/deeplink_navigation.dart';
import 'credit_cards_deeplink_extra.dart';
import 'navigation_handlers/credit_card_deeplink_navigation_handler.dart';
import 'navigation_handlers/e_wallet_deeplink_navigation_handler.dart';
import 'navigation_handlers/onepay_deeplink_navigation_handler.dart';
import 'navigation_handlers/ph_hc_protect_deeplink_navigation_handler.dart';
import 'navigation_handlers/vas_csp_deeplink_navigation_handler.dart';
import 'navigation_handlers/vouchers_deeplink_navigation_handler.dart';

/// Class which defines deeplinks under Credit Cards (RET_D) team
class CreditCardsDeeplinkNavigation extends DeeplinkNavigation<CreditCardsDeeplinkExtra> {
  CreditCardsDeeplinkNavigation({required super.logger});

  @override
  List<DeeplinkDefinitionHandler> get deeplinks => [
        const OnepayDeeplinkNavigationHandler(),
        const VasCspDeeplinkNavigationHandler(),
        const EWalletDeeplinkNavigationHandler(),
        const VouchersDeeplinkNavigationHandler(),
        const CreditCardDeeplinkNavigationHandler(),
        const PhHcProtectDeeplinkNavigationHandler(),
      ];
}
