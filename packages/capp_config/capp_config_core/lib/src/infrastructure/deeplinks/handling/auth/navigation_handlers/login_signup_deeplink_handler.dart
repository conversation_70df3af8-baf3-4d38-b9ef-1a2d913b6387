import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../../../capp_config_core.dart';
import '../../../../../application/deeplinks/handling/deeplink_definition.dart';
import '../auth_deeplink_const.dart';

// ignore_for_file: use_build_context_synchronously
final class LoginSignupDeeplinkHandler extends DeeplinkDefinitionHandler implements DeeplinkNavigationHandler {
  const LoginSignupDeeplinkHandler();
  @override
  List<String> get deeplinkUniqueSegments => [AuthDeeplinkConst.loginSignup];

  @override
  Future<void> navigate(BuildContext context, Uri uri) async {
    final isAnonymous = await context.get<DeeplinkResolverBloc>().isCurrentUserAnonymous();
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final routeName = navigationHistoryObserver.top?.settings.name;
    if (isAnonymous && routeName != 'authentication-initial' && routeName != 'auth-landing') {
      await context.navigateToSignInScreen();
    }
  }
}
