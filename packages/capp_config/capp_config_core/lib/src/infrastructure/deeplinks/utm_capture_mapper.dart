import 'package:capp_domain/capp_domain.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

extension UTMLinkRequestMapper on api_models.UtmLinksRequest {
  static api_models.UtmLinksRequest? fromDomain(UTMLinkRequestModel? item) => item == null
      ? null
      : api_models.UtmLinksRequest(
          utmSource: item.utmSource,
          utmCampaign: item.utmCampaign,
          utmTerm: item.utmTerm,
          utmMedium: item.utmMedium,
        );
}
