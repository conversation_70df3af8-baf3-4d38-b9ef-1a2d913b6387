import '../../application/deeplinks/pending_deeplink_handler.dart';

final class FakePendingDeeplinkHandler implements PendingDeeplinkHandler {
  final FakeListeningStrategy _strategy = FakeListeningStrategy();

  FakePendingDeeplinkHandler() {
    _strategy.canContinue = true;
  }

  @override
  void setIsListeningFromAuth({required bool isListening}) {
    _strategy.canContinue = isListening;
  }

  @override
  void setIsListeningFromLock({required bool isListening}) {
    _strategy.canContinue = isListening;
  }

  @override
  ListeningStrategy strategy = FakeListeningStrategy();

  @override
  bool get canContinue => _strategy.canContinue;
}

final class FakeListeningStrategy extends ListeningStrategy {}
