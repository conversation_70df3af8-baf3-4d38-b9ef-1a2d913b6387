import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

class EnvironmentConfigRepository implements IEnvironmentConfigRepository {
  EnvironmentConfigApi api;
  Logger logger;
  ConfigurationRoot environmentConfigSeed;
  ConfigurationRoot? configRoot;

  EnvironmentConfigRepository({
    required this.api,
    required this.environmentConfigSeed,
    required this.logger,
  });

  @override
  ConfigurationRoot currentConfig() => configRoot ?? environmentConfigSeed;

  @override
  Future<Either<EnvironmentConfigFailure, ConfigurationRoot>> getEnvironmentConfig({bool reload = false}) async {
    try {
      if (!reload && configRoot != null) {
        return right(configRoot!);
      }
      final response = await api.envConfigGet();
      configRoot = response.toDomain();
      return right(configRoot!);
    } on DioError catch (_) {
      return left(EnvironmentConfigFailure.unexpected);
    } on Exception catch (e, s) {
      logger.wtf('Unknown error in environment configuration repository', e, s);
      return left(EnvironmentConfigFailure.unexpected);
    }
  }
}
