// ignore_for_file: avoid_unused_constructor_parameters

import 'dart:async';
import 'dart:io';

import 'package:capp_appsflyer_core/capp_appsflyer_core.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:gma_vault/gma_vault.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../capp_config_core.dart';
import '../application/deeplinks/handling/deeplink_navigation.dart';
import '../application/deeplinks/pending_deeplink_handler.dart';
import '../infrastructure/deeplinks/handling/deeplink_navigation_index.dart';
import 'capp_config_base.dart';

class CappConfigProd extends CappConfig {
  final String imageBaseUrl;
  final FirebaseOptions? firebaseOptions;

  CappConfigProd({
    required String appStoreId,
    required this.imageBaseUrl,
    required String googlePlayId,
    required Map<String, dynamic> remoteConfigSeed,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    required ConfigurationRoot environmentConfigSeed,
    required String appTraitValue,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    String? onelinkUrl,
    this.firebaseOptions,
    String? appGalleryId,
  }) : super(
          googlePlayId: googlePlayId,
          appStoreId: appStoreId,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          appTraitValue: appTraitValue,
          environmentConfigSeed: environmentConfigSeed,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          onelinkUrl: onelinkUrl,
          appGalleryId: appGalleryId,
        );

  @override
  Future<void> preRegisterDependencies(GetIt container) async {
    final gmaSecureStorage = await GmaSecureStorage.asyncInit();
    container.registerSingleton<IGmaSecureStorage>(gmaSecureStorage);

    final storages = await Future.wait([
      SecureHiveStorageProvider.asyncInit(
        logger: container<Logger>(),
        secureStorage: container<IGmaSecureStorage>(),
      ),
      PersistantHiveStorageProvider.asyncInit(
        logger: container<Logger>(),
        secureStorage: container<IGmaSecureStorage>(),
      ),
      SessionHiveStorageProvider.asyncInit(
        logger: container<Logger>(),
        secureStorage: container<IGmaSecureStorage>(),
      ),
    ]);

    container
      ..registerSingleton<GmaStorageProvider>(
        storages[0],
        instanceName: GmaStorageType.secure.name,
      )
      ..registerSingleton<GmaStorageProvider>(
        storages[1],
        instanceName: GmaStorageType.persistant.name,
      )
      ..registerSingleton<GmaStorageProvider>(
        storages[2],
      )
      ..registerLazySingleton<AppStateStorage>(
        () => AppStateStorage(
          storage: container.get<GmaStorageProvider>(instanceName: GmaStorageType.persistant.name),
        ),
      )
      ..registerLazySingleton<IAppStateRepository>(
        () => AppStateRepository(
          logger: container<Logger>(),
          storage: container<AppStateStorage>(),
        ),
      )
      ..registerLazySingleton<IFirebasePerformanceMonitoring>(
        () {
          if (kDebugMode == true) {
            return DebugFirebasePerformanceMonitoring(
              enabledTraces: container<PerformanceConfig>().enabledTraces,
            );
          } else {
            return FirebasePerformanceMonitoring(
              performanceConfig: container<PerformanceConfig>(),
              identityStorage: container<IdentityStorage>(),
              enabledTraces: container<PerformanceConfig>().enabledTraces,
            );
          }
        },
      );
  }

  @override
  void registerDependencies(GetIt container) {
    super.registerDependencies(container);

    container
      ..registerLazySingleton<AndroidPlayReferrerService>(
        () => AndroidPlayReferrerService(
          logger: container<Logger>(),
          trackingService: container<EventTrackingService>(),
          appStateRepository: container<IAppStateRepository>(),
        ),
      )
      ..registerLazySingleton<KoyalRouterObserver>(
        () => ScreenNameRouterObserver(
          eventTrackingService: container.get<EventTrackingService>(),
          logger: container.get<Logger>(),
          userDefaultProperty: container.get<CappTrackingService>().defaultUserPropertyMap,
          gtp: container.get<GlobalTrackingProperties>(),
        ),
      )
      ..registerLazySingleton<NavigationHistoryObserver>(
        NavigationHistoryObserver.new,
      )
      ..registerLazySingleton<ImageServiceBase>(
        () => ImageServiceProd(
          imageBaseUrl: imageBaseUrl,
          featureFlagRepository: container<IFeatureFlagRepository>(),
        ),
      )
      ..registerLazySingleton(
        PendingDeeplinkHandler.new,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => ContentDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameContent,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => AuthDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameAuth,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => CreditCardsDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameCreditCards,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => TransactionsDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameTransactions,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => LoansDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameLoans,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => RepaymentDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameRepayment,
      );
  }

  @override
  void registerRemoteConfig(GetIt c) {
    c.registerLazySingleton<IRemoteConfigRepository>(
      () => RemoteConfigRepository(
        logger: c.get<Logger>(),
        currentUserRepository: c.get<ICurrentUserRepository>(),
        isHuawei: isHuawei,
      ),
    );
    c.allReady(timeout: const Duration(seconds: 10)).then(
      (_) {
        if (c.isRegistered<IRemoteConfigRepository>() && c.isRegistered<ICurrentUserRepository>()) {
          c<IRemoteConfigRepository>().handleUrlSwitch(c);
        }
      },
    );
  }

  @override
  void registerPlatformServices(GetIt c) {
    if (GmaPlatform.isWeb) {
      c.registerLazySingleton<IDeeplinkService>(
        () => WebDeeplinkService(
          logger: c<Logger>(),
          trackingService: c<EventTrackingService>(),
          koyalMessagingRepository: c<IKoyalMessagingRepository>(),
          dynamicLinkUrl: dynamicLinkUrl,
          deeplinkUrl: deeplinkUrl,
          onelinkUrl: onelinkUrl,
          envRepository: c<IEnvironmentRepository>(),
        ),
      );
    } else {
      c
        ..registerLazySingleton<DeeplinkService>(
          () => DeeplinkService(
            logger: c<Logger>(),
            trackingService: c<EventTrackingService>(),
            koyalMessagingRepository: c<IKoyalMessagingRepository>(),
            appStoreId: appStoreId,
            dynamicLinkUrl: dynamicLinkUrl,
            deeplinkUrl: deeplinkUrl,
            onelinkUrl: onelinkUrl,
            appsflyerService: c.isRegistered<IAppsflyerService>() ? c<IAppsflyerService>() : null,
            cappTrackingService: c<CappTrackingService>(),
            isHuawei: isHuawei,
            envRepository: c<IEnvironmentRepository>(),
          ),
        )
        ..registerLazySingleton<IDeeplinkService>(
          () => c<DeeplinkService>(),
        )
        ..registerLazySingleton<IDynamicLinksService>(
          () => c<DeeplinkService>(),
        );
    }
    c.registerLazySingleton<IUTMCaptureRepository>(
      () => UTMCaptureRepository(
        logger: c<Logger>(),
        api: c<UserV2Api>(),
      ),
    );
  }

  @override
  void registerVault(GetIt c) {
    c.registerLazySingleton<IGmaVault>(
      Platform.environment.containsKey('FLUTTER_TEST') ? GmaVaultTest.new : GmaVault.new,
    );
  }
}
