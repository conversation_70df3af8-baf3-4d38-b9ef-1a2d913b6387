import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:logger/logger.dart';

import '../presentation/notifications/message_screen_wrapper.dart';

void registerCommonDependencies(GetIt c) {
  c.registerFactory<IMessageScreenWrapperBuilder>(MessageScreenWrapperBuilder.new);
}

void registerStoragesAsPersistent(GetIt c) {
  c
    ..registerLazySingleton<ReactiveStorage>(
      () => ReactiveStorage(
        storageProvider: c<SembastStorageProvider>(),
        logger: c<Logger>(),
      ),
    )
    ..registerLazySingleton<ReactiveStorage>(
      () => ReactiveStorage(
        storageProvider: c<SembastStorageProvider>(instanceName: sembastStorageProviderNoCleanup),
        logger: c<Logger>(),
      ),
      instanceName: reactiveStorageNoCleanup,
    );
}

void registerStoragesAsInMemory(GetIt c) {
  c
    ..registerLazySingleton(
      () => ReactiveStorage(
        storageProvider: InMemoryStorageProvider(logger: c<Logger>()),
        logger: c<Logger>(),
      ),
    )
    ..registerLazySingleton<ReactiveStorage>(
      () => ReactiveStorage(
        storageProvider: c<InMemoryStorageProvider>(),
        logger: c<Logger>(),
      ),
      instanceName: reactiveStorageNoCleanup,
    );
}

void registerCappTestFakeDependencies(GetIt c) {
  registerCommonDependencies(c);
  c
    ..registerLazySingleton<FlavorSettings>(TestFlavorSettings.new)
    ..registerLazySingleton<AssetBundle>(TestAssetBundle.new);
  registerStoragesAsInMemory(c);
  registerFirebase(c);
}

void registerCappTestProdDependencies(GetIt c) {
  registerCommonDependencies(c);

  c
    ..registerLazySingleton<FlavorSettings>(ProdTestFlavorSettings.new)
    ..registerLazySingleton<AssetBundle>(TestAssetBundle.new);
  registerStoragesAsInMemory(c);
  registerFirebase(c);
}

void registerCappFakeDependencies(GetIt c) {
  registerCommonDependencies(c);

  c
    ..registerLazySingleton<FlavorSettings>(FakeFlavorSettings.new)
    ..registerLazySingleton<AssetBundle>(() => rootBundle);
  registerStoragesAsPersistent(c);
  registerFirebase(c);
}

void registerCappProductionDependencies(GetIt c) {
  registerCommonDependencies(c);

  c
    ..registerLazySingleton<FlavorSettings>(ProdFlavorSettings.new)
    ..registerLazySingleton<AssetBundle>(() => rootBundle);
  registerStoragesAsPersistent(c);
  registerFirebase(c);
}

void registerFirebase(GetIt c) {
  c.registerLazySingleton(FirebaseInitService.new);
}
