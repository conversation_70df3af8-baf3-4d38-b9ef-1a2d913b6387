import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:gma_vault/gma_vault.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../capp_config_core.dart';
import '../infrastructure/inapp_tracking/default_inapp_tracking_service.dart';

class CappConfigTestFake extends CappConfigFake {
  CappConfigTestFake({
    required String appStoreId,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required String imageBaseUrl,
    required String googlePlayId,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    required ConfigurationRoot environmentConfigSeed,
    required String appTraitValue,
    required Map<String, dynamic> remoteConfigSeed,
    super.appGalleryId,
  }) : super(
          appStoreId: appStoreId,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          imageBaseUrl: imageBaseUrl,
          remoteConfigSeed: remoteConfigSeed,
          googlePlayId: googlePlayId,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          environmentConfigSeed: environmentConfigSeed,
          appTraitValue: appTraitValue,
        );

  @override
  void registerPersistentStorages(GetIt c) {
    c.registerLazySingleton(() => PermissionsStorage(storage: c.get<ReactiveInMemoryStorageProvider>()));
  }

  @override
  void registerPermissions(GetIt c) {
    c.registerLazySingleton<IPermissionsService>(
      TestPermissionsService.new,
    );
  }

  @override
  void registerInAppTrackingService(GetIt c) {
    c.registerLazySingleton<IInAppTracking>(DefaultInAppTrackingService.new);
  }

  @override
  void registerRepositories(GetIt c) {
    super.registerRepositories(c);

    if (c.isRegistered<IFileRepository>()) {
      c.unregister<IFileRepository>();
    }
    c.registerTrackingLazySingleton<IFileRepository>(FakeFileRepository.new);
  }

  @override
  void registerRemoteConfig(GetIt c) {
    c.registerLazySingleton<IRemoteConfigRepository>(
      () => FakeRemoteConfigRepository(remoteConfigSeed),
    );
  }

  @override
  void registerVault(GetIt c) {
    c.registerLazySingleton<IGmaVault>(GmaVaultTest.new);
  }
}
