import 'dart:io';

import 'package:a2a_intent_receiver/a2a_intent_receiver.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:gma_vault/gma_vault.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../capp_config_core.dart';
import '../application/deeplinks/handling/deeplink_navigation.dart';
import '../application/deeplinks/pending_deeplink_handler.dart';
import '../infrastructure/deeplinks/fake_pending_deeplink_handler.dart';
import '../infrastructure/deeplinks/handling/deeplink_navigation_index.dart';
import '../initialization/capp_core_initialization_service.dart';
import 'capp_config_base.dart';

class CappConfigFake extends CappConfig {
  final String imageBaseUrl;
  final Map<String, dynamic> remoteConfigSeed;

  CappConfigFake({
    required String appStoreId,
    required this.imageBaseUrl,
    required this.remoteConfigSeed,
    required String googlePlayId,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    required ConfigurationRoot environmentConfigSeed,
    required String appTraitValue,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    String? onelinkUrl,
    String? appGalleryId,
  }) : super(
          googlePlayId: googlePlayId,
          appStoreId: appStoreId,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          appTraitValue: appTraitValue,
          environmentConfigSeed: environmentConfigSeed,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          onelinkUrl: onelinkUrl,
          appGalleryId: appGalleryId,
        );

  @override
  void registerRemoteConfig(GetIt c) {
    c.registerLazySingleton<IRemoteConfigRepository>(
      () => FakeRemoteConfigRepository(remoteConfigSeed),
    );
  }

  @override
  Future<void> preRegisterDependencies(GetIt container) async {
    container
      ..registerSingleton<IGmaSecureStorage>(await FakeGmaSecureStorage.asyncInit())
      ..registerSingleton<GmaStorageProvider>(
        SecureInMemoryStorageProvider(logger: container<Logger>()),
        instanceName: GmaStorageType.secure.name,
      )
      ..registerSingleton<GmaStorageProvider>(
        PersistantInMemoryStorageProvider(
          logger: container<Logger>(),
        ),
        instanceName: GmaStorageType.persistant.name,
      )
      ..registerSingleton<GmaStorageProvider>(
        SessionInMemoryStorageProvider(
          logger: container<Logger>(),
        ),
      )
      ..registerLazySingleton<IFirebasePerformanceMonitoring>(
        () => DebugFirebasePerformanceMonitoring(
          enabledTraces: container.get<PerformanceConfig>().enabledTraces,
        ),
      )
      ..registerLazySingleton<PerformanceConfig>(
        () => PerformanceConfig(
          isFirebasePerformanceMonitoringEnabled: true,
        ),
      )
      ..registerLazySingleton<AppStateStorage>(
        () => AppStateStorage(
          storage: ReactiveInMemoryStorageProvider(logger: container<Logger>()),
        ),
      )
      ..registerLazySingleton<IAppStateRepository>(
        () => AppStateRepository(
          logger: container<Logger>(),
          storage: container<AppStateStorage>(),
        ),
      )
      ..registerLazySingleton<IInAppRepository>(
        () => InAppRepository(
          storageProvider: container<GmaStorageProvider>(),
        ),
      )
      ..registerLazySingleton<IInAppService>(
        () => InAppService(
          customInAppRepository: container<ICustomInAppRepository>(),
          customInAppBloc: container<CustomInAppBloc>(),
          featureFlagRepository: container<IFeatureFlagRepository>(),
          inAppRepository: container<IInAppRepository>(),
        ),
      );
  }

  @override
  void registerDependencies(GetIt container) {
    super.registerDependencies(container);

    container
      ..registerLazySingleton<KoyalRouterObserver>(
        () => ScreenNameRouterObserver(
          eventTrackingService: container.get<EventTrackingService>(),
          logger: container.get<Logger>(),
          userDefaultProperty: container.get<CappTrackingService>().defaultUserPropertyMap,
          gtp: container.get<GlobalTrackingProperties>(),
        ),
      )
      ..registerLazySingleton<NavigationHistoryObserver>(
        NavigationHistoryObserver.new,
      )
      ..registerLazySingleton<ImageServiceBase>(() => ImageServiceFake(imageBaseUrl))
      ..registerLazySingleton<PendingDeeplinkHandler>(FakePendingDeeplinkHandler.new)
      ..registerFactory<DeeplinkNavigation>(
        () => ContentDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameContent,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => AuthDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameAuth,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => CreditCardsDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameCreditCards,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => TransactionsDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameTransactions,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => LoansDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameLoans,
      )
      ..registerFactory<DeeplinkNavigation>(
        () => RepaymentDeeplinkNavigation(
          logger: container.get<Logger>(),
        ),
        instanceName: DeeplinkNavigation.instanceNameRepayment,
      );
  }

  @override
  IModuleInitializationService getInitializationService(GetIt container) {
    return CappCoreInitializationService(
      remoteConfigRepository: container<IRemoteConfigRepository>(),
      deeplinkService: container<IDeeplinkService>(),
      apiLocker: container<ApiLocker>(),
      featureFlagInitService: container<IFeatureFlagInitService>(),
      appTrait: appTraitValue,
      retrofitCache: container<RetrofitCache>(instanceName: CappApi.cache),
      androidPlayReferrerService: null,
      a2aIntentReceiverService: container<IA2AIntentReceiverService>(),
    );
  }

  @override
  void registerPlatformServices(GetIt c) {
    c
      ..registerLazySingleton<IDeeplinkService>(
        () => FakeDeeplinkService(
          logger: c<Logger>(),
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          onelinkUrl: onelinkUrl,
          envRepository: c<IEnvironmentRepository>(),
        ),
      )
      ..registerLazySingleton<IUTMCaptureRepository>(
        () => UTMCaptureRepository(logger: c<Logger>(), api: c<UserV2Api>()),
      )
      ..registerLazySingleton<IA2AIntentReceiverService>(
        () => A2AIntentReceiverService(
          logger: c<Logger>(),
          intentReceiver: A2AIntentReceiver.instance,
          deeplinkService: c<IDeeplinkService>(),
        ),
      );
  }

  @override
  void registerVault(GetIt c) {
    c.registerLazySingleton<IGmaVault>(
      Platform.environment.containsKey('FLUTTER_TEST') ? GmaVaultTest.new : GmaVault.new,
    );
  }
}
