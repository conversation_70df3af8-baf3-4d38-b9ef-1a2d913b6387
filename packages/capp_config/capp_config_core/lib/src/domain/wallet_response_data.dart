import 'package:equatable/equatable.dart';

class WalletResponseData extends Equatable {
  final String? provisioningData;
  final String? extraWalletId;

  const WalletResponseData({
    required this.provisioningData,
    required this.extraWalletId,
  });

  @override
  List<Object?> get props => [
        provisioningData,
        extraWalletId,
      ];

  WalletResponseData copyWith({
    String? provisioningData,
    String? extraWalletId,
  }) {
    return WalletResponseData(
      provisioningData: provisioningData ?? this.provisioningData,
      extraWalletId: extraWalletId ?? this.extraWalletId,
    );
  }

  factory WalletResponseData.dataFromDeeplink(Uri url) {
    final params = url.queryParameters;
    return WalletResponseData(
      provisioningData: params['provisioningData'],
      extraWalletId: params['extraWalletId'],
    );
  }
}
