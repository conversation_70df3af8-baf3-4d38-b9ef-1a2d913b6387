abstract class IInAppTracking {
  // Track when the in-app message is viewed.
  void trackInAppView({required String? msid, required int? templateId});

  // Track when the in-app CTA is clicked.
  void trackInAppCtaClick({required String? msid, required int? templateId});

  // Track when the in-app close action is clicked.
  void trackInAppCloseClick({required String? msid, required int? templateId});
}
