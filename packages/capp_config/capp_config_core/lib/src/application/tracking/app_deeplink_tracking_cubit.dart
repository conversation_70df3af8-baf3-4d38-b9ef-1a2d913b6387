import 'package:bloc/bloc.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import 'app_deeplink_tracking_state.dart';
import 'app_start_tracking_event.dart';

// User Story 114489: Create new events (App_open and App_first_open)
class AppDeeplinkTrackingCubit extends Cubit<AppDeeplinkTrackingState> {
  final IAppStateRepository appStateRepository;
  final CappTrackingService trackingService;
  final Logger logger;

  AppDeeplinkTrackingCubit({
    required this.trackingService,
    required this.logger,
    required this.appStateRepository,
  }) : super(AppDeeplinkTrackingState.initial);

  Future<void> trackAppStart(String url) async {
    trackUtmParameters(url);

    final result = await appStateRepository.isFirstStart();
    final isFirstStart = result.fold(
      (l) => false,
      (r) => r,
    );

    if (isFirstStart) {
      await trackingService.trackCappEvent(
        AppFirstOpenEvent(
          url: url,
        ),
      );
      emit(AppDeeplinkTrackingState.appFirstOpen);
    } else {
      await trackingService.trackCappEvent(
        AppOpenEvent(
          url: url,
        ),
      );
      emit(AppDeeplinkTrackingState.appOpen);
    }
  }

  void trackUtmParameters(String url) => trackingService.setUtmParametersFromUrl(url);

  @override
  void onChange(Change<AppDeeplinkTrackingState> change) {
    logger.d('App deeplink start tracking: $change');
    super.onChange(change);
  }
}
