import 'package:capp_tracking/capp_tracking.dart';
import 'package:koyal_shared/koyal_shared.dart';

// APP003 - open
final class AppOpenEvent extends AppStartTrackingEvent {
  AppOpenEvent({required super.url});

  @override
  KoyalEvent get event => KoyalEvent.appOpenView;

  @override
  String get label => KoyalTrackingLabels.open;
}

// APP004 - first open
final class AppFirstOpenEvent extends AppStartTrackingEvent {
  AppFirstOpenEvent({required super.url});

  AppFirstOpenEvent.fromInstallData({
    required Map<String, dynamic> installData,
  }) : super.fromInstallData(installData: installData);

  @override
  KoyalEvent get event => KoyalEvent.appFirstOpenView;

  @override
  String get label => KoyalTrackingLabels.firstOpen;
}
