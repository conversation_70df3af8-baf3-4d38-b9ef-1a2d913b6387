import 'package:flutter/material.dart';

abstract class DeeplinkDefinitionHandler {
  const DeeplinkDefinitionHandler();

  /// Simple getter to provide uniq segments which identify deeplink
  @protected
  List<String> get deeplinkUniqueSegments;

  /// Used for verify if uri segment belong to deeplink or not, it can be override to another style of verification
  bool verify(Uri uri) => uri.pathSegments.isNotEmpty && deeplinkUniqueSegments.contains(uri.pathSegments.first);

  /// Utility function copied and pasted from deeplink resolver
  @protected
  String? getValueByQueryParams(String key, Uri? link) {
    if (link == null) return null;
    if (link.queryParameters.containsKey(key)) {
      return link.queryParameters[key];
    }
    return null;
  }
}

abstract interface class DeeplinkNavigationHandler {
  /// Just for navigation, should not be called without [verify]
  Future<void> navigate(BuildContext context, Uri uri);
}

abstract interface class DeeplinkNavigationHandlerWithExtra<T> {
  /// Just for navigation with generic extra param, should not be called without [verify]
  Future<void> navigate(BuildContext context, Uri uri, {required T extra});
}
