import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import 'deeplink_definition.dart';

abstract class DeeplinkNavigation<T> {
  static const instanceNameContent = 'contentDeeplinkNavigation';
  static const instanceNameAuth = 'authDeeplinkNavigation';
  static const instanceNameCreditCards = 'creditCardsDeeplinkNavigation';
  static const instanceNameTransactions = 'transactionsDeeplinkNavigation';
  static const instanceNameLoans = 'loansDeeplinkNavigation';
  static const instanceNameRepayment = 'repaymentDeeplinkNavigation';

  final Logger logger;

  DeeplinkNavigation({
    required this.logger,
  });

  @protected
  List<DeeplinkDefinitionHandler> get deeplinks;

  bool contains(String link) {
    final uri = Uri.parse(link);
    l('Looking for deeplink in navigation - $link');
    for (final deeplink in deeplinks) {
      if (deeplink.verify(uri)) {
        l('deeplink ${uri.path} found');
        return true;
      }
    }

    return false;
  }

  Future<void> handle(BuildContext context, String url, {required T extra}) async {
    final uri = Uri.parse(url);

    for (final deeplink in deeplinks) {
      if (deeplink.verify(uri)) {
        l('Handling deeplink: ${uri.path}');

        if (deeplink is DeeplinkNavigationHandler) {
          return (deeplink as DeeplinkNavigationHandler).navigate(context, uri);
        } else if (deeplink is DeeplinkNavigationHandlerWithExtra<T>) {
          return (deeplink as DeeplinkNavigationHandlerWithExtra<T>).navigate(context, uri, extra: extra);
        }
      }
    }
  }

  @protected
  void l(String message) {
    logger.d('Deeplink navigation: $message');
  }
}
