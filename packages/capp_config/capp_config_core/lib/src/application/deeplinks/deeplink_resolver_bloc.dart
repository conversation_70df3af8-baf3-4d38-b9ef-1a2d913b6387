import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'pending_deeplink_handler.dart';

part 'deeplink_resolver_bloc.freezed.dart';
part 'deeplink_resolver_event.dart';
part 'deeplink_resolver_state.dart';

class DeeplinkResolverBloc extends Bloc<DeeplinkResolverEvent, DeeplinkResolverState> {
  final INewPendingActionsRepository? newPendingActionsRepository;
  final IDeeplinkService? deeplinkService;
  final IUTMCaptureRepository? utmCaptureRepository;
  final ICurrentUserRepository? currentUserRepository;
  final EventTrackingService? eventTrackingService;
  final String deeplinkUrl;
  final IIdentityRepository identityRepository;
  final IEnumerationsRepository enumerationsRepository;
  final IDeeplinkDecodeExpandRepository qrDecodeExpandRepository;
  final IAuthCompleteRepository authCompleteRepository;
  DeeplinkModel? pendingDeeplink;
  final PendingDeeplinkHandler pendingDeeplinkHandler;

  StreamSubscription<DeeplinkModel>? _onDeeplinkSub;
  StreamSubscription<String?>? _onErrorSub;
  StreamSubscription<CurrentUser?>? _userProfileSubscription;
  StreamSubscription<bool>? _onAuthCompleteStream;

  DeeplinkResolverBloc({
    required this.deeplinkService,
    required this.newPendingActionsRepository,
    this.utmCaptureRepository,
    this.currentUserRepository,
    this.eventTrackingService,
    required this.identityRepository,
    required this.qrDecodeExpandRepository,
    required this.deeplinkUrl,
    required this.enumerationsRepository,
    required this.authCompleteRepository,
    required this.pendingDeeplinkHandler,
  }) : super(DeeplinkResolverState.initialize()) {
    on<_Init>(_onInit);
    on<_UserChanged>(_onUserChanged);
    on<_OpenDeeplink>(_onOpenDeeplink);
    on<_Proceed>(_onProceed);
    on<_Error>(_onError);
    on<_IsListening>(_onIsListening);
    on<_Reset>(_onReset);
    on<_AuthFinished>(_onAuthFinished);
    on<_CheckPendingDeeplink>(_onCheckPendingDeeplink);
  }

  Future<void> _onInit(_Init e, Emitter<DeeplinkResolverState> emit) async {
    newPendingActionsRepository!.opening.listen(
      (item) {
        final newDeeplinkUrl = GetIt.I<IEnvironmentRepository>().getEnvironmentUrl()?.deeplinkUrl;

        return add(
          DeeplinkResolverEvent.openDeeplink(
            deeplink: DeeplinkModel(
              link: item.type.deeplinkUrl(
                newDeeplinkUrl?.isNotEmpty ?? false ? newDeeplinkUrl! : deeplinkUrl,
                metadata: item.metadata,
                id: item.id,
              ),
            ),
          ),
        );
      },
    );
    await _userProfileSubscription?.cancel();
    unawaited(
      identityRepository.streamIdentity()?.then(
            (stream) => _userProfileSubscription = stream.listen(
              (user) => add(DeeplinkResolverEvent.userChanged(user)),
            ),
          ),
    );
    _onDeeplinkSub = deeplinkService!.onDeeplink.listen(
      (link) {
        final hasSuperCupo = Uri.decodeFull(link.link).contains('https://supercupo.page.link/');
        if (hasSuperCupo) {
          return;
        } else {
          // Prevent to open deeplink twice
          if (state.deeplink?.link != link.link) {
            add(DeeplinkResolverEvent.openDeeplink(deeplink: link));
          }
        }
      },
    );
    _onErrorSub = deeplinkService?.onError.listen((err) => add(DeeplinkResolverEvent.error(errorMessage: err)));

    _onAuthCompleteStream = authCompleteRepository.authComplete.listen(
      (authCompleted) {
        if (authCompleted) {
          add(const DeeplinkResolverEvent.authFinished());
        }
      },
    );
  }

  Future<void> _onUserChanged(_UserChanged e, Emitter<DeeplinkResolverState> emit) async {
    emit(
      state.copyWith(user: e.user),
    );
  }

  Future<void> _onOpenDeeplink(_OpenDeeplink e, Emitter<DeeplinkResolverState> emit) async {
    //* delay is neccessary for lock_screen.dart:27 to disable listening on init
    await Future<void>.delayed(const Duration(milliseconds: 400));
    add(DeeplinkResolverEvent.proceed(deeplink: e.deeplink));
  }

  Future<void> _onProceed(_Proceed e, Emitter<DeeplinkResolverState> emit) async {
    final deeplink = e.deeplink;
    if (!pendingDeeplinkHandler.canContinue && deeplink.link.isNotEmpty) {
      pendingDeeplink = deeplink;
      return;
    }
    if (deeplink.hasUtmParams) {
      unawaited(utmCaptureRepository!.utmLinkRequest(deeplink.utmLinkRequest));
    }
    if (state.deeplink == null) {
      emit(state.copyWith(deeplink: deeplink));
    }
  }

  Future<void> _onReset(_Reset e, Emitter<DeeplinkResolverState> emit) async {
    emit(state.copyWith(deeplink: null, isError: false));
  }

  Future<void> _onError(_Error e, Emitter<DeeplinkResolverState> emit) async {
    emit(state.copyWith(isError: true));
  }

  Future<void> _onIsListening(_IsListening e, Emitter<DeeplinkResolverState> emit) async {
    pendingDeeplinkHandler.setIsListeningFromLock(isListening: e.listening);
    add(const DeeplinkResolverEvent.checkPendingDeeplink());
  }

  Future<void> _onAuthFinished(_AuthFinished e, Emitter<DeeplinkResolverState> emit) async {
    pendingDeeplinkHandler.setIsListeningFromAuth(isListening: true);
    add(const DeeplinkResolverEvent.checkPendingDeeplink());
  }

  Future<void> _onCheckPendingDeeplink(_CheckPendingDeeplink e, Emitter<DeeplinkResolverState> emit) async {
    if (pendingDeeplinkHandler.canContinue && pendingDeeplink != null) {
      add(DeeplinkResolverEvent.proceed(deeplink: pendingDeeplink!));
      pendingDeeplink = null;
    }
  }

  Future<void> sendQRCode(String code) async {
    final response = await qrDecodeExpandRepository.deeplinkQrExpandV1(code);

    unawaited(
      response.fold(
        (l) => null,
        (r) {
          if (r != null) {
            const codeParam = '${IDeeplinkDecodeExpandRepository.code}=';
            final codeIndex = r.indexOf(codeParam);

            if (codeIndex != -1) {
              final codeStartIndex = codeIndex + codeParam.length;
              final codeValue = r.substring(codeStartIndex);
              qrDecodeExpandRepository.deeplinkQrDecodeV1(codeValue);
            }
          }
          return null;
        },
      ),
    );
  }

  Future<bool> isInAllowedDomain(String? url) async {
    if (url != null) {
      final uri = Uri.tryParse(url);
      if (uri != null) {
        final response = await enumerationsRepository.getDeeplinkDomainsWhitelist();
        final allowedDomains = response.fold(
          (l) => <String>[],
          (r) => (r.items ?? <EnumerationItem>[]).map((item) => (item.key ?? '').toLowerCase()).toList(),
        );
        final authority = uri.authority.toLowerCase();
        for (final domain in allowedDomains) {
          if (authority == domain || authority.endsWith('.$domain')) {
            return true;
          }
        }
      }
    }
    return false;
  }

  Future<bool> isCurrentUserAnonymous() async {
    return (await currentUserRepository?.isCurrentUserAnonymous()) ?? true;
  }

  @override
  Future<void> close() {
    _onDeeplinkSub?.cancel();
    _onErrorSub?.cancel();
    _userProfileSubscription?.cancel();
    _onAuthCompleteStream?.cancel();
    return super.close();
  }
}
