class PendingDeeplinkHandler {
  ListeningStrategy strategy = FromAuthStrategy();

  bool get canContinue => strategy.canContinue;

  void setIsListeningFromLock({required bool isListening}) {
    // we can change strategy in case when is from onAuth done
    if (strategy is FromAuthStrategy && strategy.canContinue) {
      strategy = FromLockScreenStrategy();
    }
    // in this case, canContinue will be valid on for use case from onAuthComplete
    if (strategy is FromLockScreenStrategy) {
      strategy.canContinue = isListening;
    }
  }

  void setIsListeningFromAuth({required bool isListening}) {
    // just check if this strategy is valid
    if (strategy is FromAuthStrategy) {
      strategy.canContinue = isListening;
    }
  }
}

abstract class ListeningStrategy {
  bool canContinue = false;
}

final class FromLockScreenStrategy extends ListeningStrategy {}

final class FromAuthStrategy extends ListeningStrategy {}

final class SignUpStrategy extends ListeningStrategy {
  @override
  bool get canContinue => true;
}
