part of 'deeplink_resolver_bloc.dart';

@freezed
class DeeplinkResolverEvent with _$DeeplinkResolverEvent {
  const factory DeeplinkResolverEvent.init() = _Init;
  const factory DeeplinkResolverEvent.reset() = _Reset;
  const factory DeeplinkResolverEvent.authFinished() = _AuthFinished;
  const factory DeeplinkResolverEvent.checkPendingDeeplink() = _CheckPendingDeeplink;

// update name
  const factory DeeplinkResolverEvent.isListening({
    required bool listening,
  }) = _IsListening;
  const factory DeeplinkResolverEvent.openDeeplink({
    required DeeplinkModel deeplink,
  }) = _OpenDeeplink;
  const factory DeeplinkResolverEvent.proceed({
    required DeeplinkModel deeplink,
  }) = _Proceed;
  const factory DeeplinkResolverEvent.error({
    String? errorMessage,
  }) = _Error;
  const factory DeeplinkResolverEvent.userChanged(CurrentUser? user) = _UserChanged;
}
