// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deeplink_resolver_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DeeplinkResolverEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeeplinkResolverEventCopyWith<$Res> {
  factory $DeeplinkResolverEventCopyWith(DeeplinkResolverEvent value,
          $Res Function(DeeplinkResolverEvent) then) =
      _$DeeplinkResolverEventCopyWithImpl<$Res, DeeplinkResolverEvent>;
}

/// @nodoc
class _$DeeplinkResolverEventCopyWithImpl<$Res,
        $Val extends DeeplinkResolverEvent>
    implements $DeeplinkResolverEventCopyWith<$Res> {
  _$DeeplinkResolverEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitCopyWith<$Res> {
  factory _$$_InitCopyWith(_$_Init value, $Res Function(_$_Init) then) =
      __$$_InitCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_Init>
    implements _$$_InitCopyWith<$Res> {
  __$$_InitCopyWithImpl(_$_Init _value, $Res Function(_$_Init) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Init implements _Init {
  const _$_Init();

  @override
  String toString() {
    return 'DeeplinkResolverEvent.init()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Init);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return init();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return init?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class _Init implements DeeplinkResolverEvent {
  const factory _Init() = _$_Init;
}

/// @nodoc
abstract class _$$_ResetCopyWith<$Res> {
  factory _$$_ResetCopyWith(_$_Reset value, $Res Function(_$_Reset) then) =
      __$$_ResetCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_Reset>
    implements _$$_ResetCopyWith<$Res> {
  __$$_ResetCopyWithImpl(_$_Reset _value, $Res Function(_$_Reset) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Reset implements _Reset {
  const _$_Reset();

  @override
  String toString() {
    return 'DeeplinkResolverEvent.reset()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Reset);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Reset implements DeeplinkResolverEvent {
  const factory _Reset() = _$_Reset;
}

/// @nodoc
abstract class _$$_AuthFinishedCopyWith<$Res> {
  factory _$$_AuthFinishedCopyWith(
          _$_AuthFinished value, $Res Function(_$_AuthFinished) then) =
      __$$_AuthFinishedCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_AuthFinishedCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_AuthFinished>
    implements _$$_AuthFinishedCopyWith<$Res> {
  __$$_AuthFinishedCopyWithImpl(
      _$_AuthFinished _value, $Res Function(_$_AuthFinished) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_AuthFinished implements _AuthFinished {
  const _$_AuthFinished();

  @override
  String toString() {
    return 'DeeplinkResolverEvent.authFinished()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_AuthFinished);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return authFinished();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return authFinished?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (authFinished != null) {
      return authFinished();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return authFinished(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return authFinished?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (authFinished != null) {
      return authFinished(this);
    }
    return orElse();
  }
}

abstract class _AuthFinished implements DeeplinkResolverEvent {
  const factory _AuthFinished() = _$_AuthFinished;
}

/// @nodoc
abstract class _$$_CheckPendingDeeplinkCopyWith<$Res> {
  factory _$$_CheckPendingDeeplinkCopyWith(_$_CheckPendingDeeplink value,
          $Res Function(_$_CheckPendingDeeplink) then) =
      __$$_CheckPendingDeeplinkCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_CheckPendingDeeplinkCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_CheckPendingDeeplink>
    implements _$$_CheckPendingDeeplinkCopyWith<$Res> {
  __$$_CheckPendingDeeplinkCopyWithImpl(_$_CheckPendingDeeplink _value,
      $Res Function(_$_CheckPendingDeeplink) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_CheckPendingDeeplink implements _CheckPendingDeeplink {
  const _$_CheckPendingDeeplink();

  @override
  String toString() {
    return 'DeeplinkResolverEvent.checkPendingDeeplink()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_CheckPendingDeeplink);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return checkPendingDeeplink();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return checkPendingDeeplink?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (checkPendingDeeplink != null) {
      return checkPendingDeeplink();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return checkPendingDeeplink(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return checkPendingDeeplink?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (checkPendingDeeplink != null) {
      return checkPendingDeeplink(this);
    }
    return orElse();
  }
}

abstract class _CheckPendingDeeplink implements DeeplinkResolverEvent {
  const factory _CheckPendingDeeplink() = _$_CheckPendingDeeplink;
}

/// @nodoc
abstract class _$$_IsListeningCopyWith<$Res> {
  factory _$$_IsListeningCopyWith(
          _$_IsListening value, $Res Function(_$_IsListening) then) =
      __$$_IsListeningCopyWithImpl<$Res>;
  @useResult
  $Res call({bool listening});
}

/// @nodoc
class __$$_IsListeningCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_IsListening>
    implements _$$_IsListeningCopyWith<$Res> {
  __$$_IsListeningCopyWithImpl(
      _$_IsListening _value, $Res Function(_$_IsListening) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? listening = null,
  }) {
    return _then(_$_IsListening(
      listening: null == listening
          ? _value.listening
          : listening // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_IsListening implements _IsListening {
  const _$_IsListening({required this.listening});

  @override
  final bool listening;

  @override
  String toString() {
    return 'DeeplinkResolverEvent.isListening(listening: $listening)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_IsListening &&
            (identical(other.listening, listening) ||
                other.listening == listening));
  }

  @override
  int get hashCode => Object.hash(runtimeType, listening);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_IsListeningCopyWith<_$_IsListening> get copyWith =>
      __$$_IsListeningCopyWithImpl<_$_IsListening>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return isListening(listening);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return isListening?.call(listening);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (isListening != null) {
      return isListening(listening);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return isListening(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return isListening?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (isListening != null) {
      return isListening(this);
    }
    return orElse();
  }
}

abstract class _IsListening implements DeeplinkResolverEvent {
  const factory _IsListening({required final bool listening}) = _$_IsListening;

  bool get listening;
  @JsonKey(ignore: true)
  _$$_IsListeningCopyWith<_$_IsListening> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_OpenDeeplinkCopyWith<$Res> {
  factory _$$_OpenDeeplinkCopyWith(
          _$_OpenDeeplink value, $Res Function(_$_OpenDeeplink) then) =
      __$$_OpenDeeplinkCopyWithImpl<$Res>;
  @useResult
  $Res call({DeeplinkModel deeplink});
}

/// @nodoc
class __$$_OpenDeeplinkCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_OpenDeeplink>
    implements _$$_OpenDeeplinkCopyWith<$Res> {
  __$$_OpenDeeplinkCopyWithImpl(
      _$_OpenDeeplink _value, $Res Function(_$_OpenDeeplink) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deeplink = null,
  }) {
    return _then(_$_OpenDeeplink(
      deeplink: null == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as DeeplinkModel,
    ));
  }
}

/// @nodoc

class _$_OpenDeeplink implements _OpenDeeplink {
  const _$_OpenDeeplink({required this.deeplink});

  @override
  final DeeplinkModel deeplink;

  @override
  String toString() {
    return 'DeeplinkResolverEvent.openDeeplink(deeplink: $deeplink)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_OpenDeeplink &&
            (identical(other.deeplink, deeplink) ||
                other.deeplink == deeplink));
  }

  @override
  int get hashCode => Object.hash(runtimeType, deeplink);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_OpenDeeplinkCopyWith<_$_OpenDeeplink> get copyWith =>
      __$$_OpenDeeplinkCopyWithImpl<_$_OpenDeeplink>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return openDeeplink(deeplink);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return openDeeplink?.call(deeplink);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (openDeeplink != null) {
      return openDeeplink(deeplink);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return openDeeplink(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return openDeeplink?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (openDeeplink != null) {
      return openDeeplink(this);
    }
    return orElse();
  }
}

abstract class _OpenDeeplink implements DeeplinkResolverEvent {
  const factory _OpenDeeplink({required final DeeplinkModel deeplink}) =
      _$_OpenDeeplink;

  DeeplinkModel get deeplink;
  @JsonKey(ignore: true)
  _$$_OpenDeeplinkCopyWith<_$_OpenDeeplink> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ProceedCopyWith<$Res> {
  factory _$$_ProceedCopyWith(
          _$_Proceed value, $Res Function(_$_Proceed) then) =
      __$$_ProceedCopyWithImpl<$Res>;
  @useResult
  $Res call({DeeplinkModel deeplink});
}

/// @nodoc
class __$$_ProceedCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_Proceed>
    implements _$$_ProceedCopyWith<$Res> {
  __$$_ProceedCopyWithImpl(_$_Proceed _value, $Res Function(_$_Proceed) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deeplink = null,
  }) {
    return _then(_$_Proceed(
      deeplink: null == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as DeeplinkModel,
    ));
  }
}

/// @nodoc

class _$_Proceed implements _Proceed {
  const _$_Proceed({required this.deeplink});

  @override
  final DeeplinkModel deeplink;

  @override
  String toString() {
    return 'DeeplinkResolverEvent.proceed(deeplink: $deeplink)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Proceed &&
            (identical(other.deeplink, deeplink) ||
                other.deeplink == deeplink));
  }

  @override
  int get hashCode => Object.hash(runtimeType, deeplink);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProceedCopyWith<_$_Proceed> get copyWith =>
      __$$_ProceedCopyWithImpl<_$_Proceed>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return proceed(deeplink);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return proceed?.call(deeplink);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (proceed != null) {
      return proceed(deeplink);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return proceed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return proceed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (proceed != null) {
      return proceed(this);
    }
    return orElse();
  }
}

abstract class _Proceed implements DeeplinkResolverEvent {
  const factory _Proceed({required final DeeplinkModel deeplink}) = _$_Proceed;

  DeeplinkModel get deeplink;
  @JsonKey(ignore: true)
  _$$_ProceedCopyWith<_$_Proceed> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ErrorCopyWith<$Res> {
  factory _$$_ErrorCopyWith(_$_Error value, $Res Function(_$_Error) then) =
      __$$_ErrorCopyWithImpl<$Res>;
  @useResult
  $Res call({String? errorMessage});
}

/// @nodoc
class __$$_ErrorCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_Error>
    implements _$$_ErrorCopyWith<$Res> {
  __$$_ErrorCopyWithImpl(_$_Error _value, $Res Function(_$_Error) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorMessage = freezed,
  }) {
    return _then(_$_Error(
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_Error implements _Error {
  const _$_Error({this.errorMessage});

  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'DeeplinkResolverEvent.error(errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Error &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, errorMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ErrorCopyWith<_$_Error> get copyWith =>
      __$$_ErrorCopyWithImpl<_$_Error>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return error(errorMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return error?.call(errorMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(errorMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements DeeplinkResolverEvent {
  const factory _Error({final String? errorMessage}) = _$_Error;

  String? get errorMessage;
  @JsonKey(ignore: true)
  _$$_ErrorCopyWith<_$_Error> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_UserChangedCopyWith<$Res> {
  factory _$$_UserChangedCopyWith(
          _$_UserChanged value, $Res Function(_$_UserChanged) then) =
      __$$_UserChangedCopyWithImpl<$Res>;
  @useResult
  $Res call({CurrentUser? user});

  $CurrentUserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$_UserChangedCopyWithImpl<$Res>
    extends _$DeeplinkResolverEventCopyWithImpl<$Res, _$_UserChanged>
    implements _$$_UserChangedCopyWith<$Res> {
  __$$_UserChangedCopyWithImpl(
      _$_UserChanged _value, $Res Function(_$_UserChanged) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
  }) {
    return _then(_$_UserChanged(
      freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as CurrentUser?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $CurrentUserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $CurrentUserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$_UserChanged implements _UserChanged {
  const _$_UserChanged(this.user);

  @override
  final CurrentUser? user;

  @override
  String toString() {
    return 'DeeplinkResolverEvent.userChanged(user: $user)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserChanged &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserChangedCopyWith<_$_UserChanged> get copyWith =>
      __$$_UserChangedCopyWithImpl<_$_UserChanged>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() init,
    required TResult Function() reset,
    required TResult Function() authFinished,
    required TResult Function() checkPendingDeeplink,
    required TResult Function(bool listening) isListening,
    required TResult Function(DeeplinkModel deeplink) openDeeplink,
    required TResult Function(DeeplinkModel deeplink) proceed,
    required TResult Function(String? errorMessage) error,
    required TResult Function(CurrentUser? user) userChanged,
  }) {
    return userChanged(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? init,
    TResult? Function()? reset,
    TResult? Function()? authFinished,
    TResult? Function()? checkPendingDeeplink,
    TResult? Function(bool listening)? isListening,
    TResult? Function(DeeplinkModel deeplink)? openDeeplink,
    TResult? Function(DeeplinkModel deeplink)? proceed,
    TResult? Function(String? errorMessage)? error,
    TResult? Function(CurrentUser? user)? userChanged,
  }) {
    return userChanged?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? init,
    TResult Function()? reset,
    TResult Function()? authFinished,
    TResult Function()? checkPendingDeeplink,
    TResult Function(bool listening)? isListening,
    TResult Function(DeeplinkModel deeplink)? openDeeplink,
    TResult Function(DeeplinkModel deeplink)? proceed,
    TResult Function(String? errorMessage)? error,
    TResult Function(CurrentUser? user)? userChanged,
    required TResult orElse(),
  }) {
    if (userChanged != null) {
      return userChanged(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_Reset value) reset,
    required TResult Function(_AuthFinished value) authFinished,
    required TResult Function(_CheckPendingDeeplink value) checkPendingDeeplink,
    required TResult Function(_IsListening value) isListening,
    required TResult Function(_OpenDeeplink value) openDeeplink,
    required TResult Function(_Proceed value) proceed,
    required TResult Function(_Error value) error,
    required TResult Function(_UserChanged value) userChanged,
  }) {
    return userChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_Reset value)? reset,
    TResult? Function(_AuthFinished value)? authFinished,
    TResult? Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult? Function(_IsListening value)? isListening,
    TResult? Function(_OpenDeeplink value)? openDeeplink,
    TResult? Function(_Proceed value)? proceed,
    TResult? Function(_Error value)? error,
    TResult? Function(_UserChanged value)? userChanged,
  }) {
    return userChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_Reset value)? reset,
    TResult Function(_AuthFinished value)? authFinished,
    TResult Function(_CheckPendingDeeplink value)? checkPendingDeeplink,
    TResult Function(_IsListening value)? isListening,
    TResult Function(_OpenDeeplink value)? openDeeplink,
    TResult Function(_Proceed value)? proceed,
    TResult Function(_Error value)? error,
    TResult Function(_UserChanged value)? userChanged,
    required TResult orElse(),
  }) {
    if (userChanged != null) {
      return userChanged(this);
    }
    return orElse();
  }
}

abstract class _UserChanged implements DeeplinkResolverEvent {
  const factory _UserChanged(final CurrentUser? user) = _$_UserChanged;

  CurrentUser? get user;
  @JsonKey(ignore: true)
  _$$_UserChangedCopyWith<_$_UserChanged> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DeeplinkResolverState {
  DeeplinkModel? get deeplink => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  CurrentUser? get user => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DeeplinkResolverStateCopyWith<DeeplinkResolverState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeeplinkResolverStateCopyWith<$Res> {
  factory $DeeplinkResolverStateCopyWith(DeeplinkResolverState value,
          $Res Function(DeeplinkResolverState) then) =
      _$DeeplinkResolverStateCopyWithImpl<$Res, DeeplinkResolverState>;
  @useResult
  $Res call({DeeplinkModel? deeplink, bool isError, CurrentUser? user});

  $CurrentUserCopyWith<$Res>? get user;
}

/// @nodoc
class _$DeeplinkResolverStateCopyWithImpl<$Res,
        $Val extends DeeplinkResolverState>
    implements $DeeplinkResolverStateCopyWith<$Res> {
  _$DeeplinkResolverStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deeplink = freezed,
    Object? isError = null,
    Object? user = freezed,
  }) {
    return _then(_value.copyWith(
      deeplink: freezed == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as DeeplinkModel?,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as CurrentUser?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CurrentUserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $CurrentUserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_DeeplinkStateCopyWith<$Res>
    implements $DeeplinkResolverStateCopyWith<$Res> {
  factory _$$_DeeplinkStateCopyWith(
          _$_DeeplinkState value, $Res Function(_$_DeeplinkState) then) =
      __$$_DeeplinkStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DeeplinkModel? deeplink, bool isError, CurrentUser? user});

  @override
  $CurrentUserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$_DeeplinkStateCopyWithImpl<$Res>
    extends _$DeeplinkResolverStateCopyWithImpl<$Res, _$_DeeplinkState>
    implements _$$_DeeplinkStateCopyWith<$Res> {
  __$$_DeeplinkStateCopyWithImpl(
      _$_DeeplinkState _value, $Res Function(_$_DeeplinkState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deeplink = freezed,
    Object? isError = null,
    Object? user = freezed,
  }) {
    return _then(_$_DeeplinkState(
      deeplink: freezed == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as DeeplinkModel?,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as CurrentUser?,
    ));
  }
}

/// @nodoc

class _$_DeeplinkState implements _DeeplinkState {
  const _$_DeeplinkState({this.deeplink, required this.isError, this.user});

  @override
  final DeeplinkModel? deeplink;
  @override
  final bool isError;
  @override
  final CurrentUser? user;

  @override
  String toString() {
    return 'DeeplinkResolverState(deeplink: $deeplink, isError: $isError, user: $user)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DeeplinkState &&
            (identical(other.deeplink, deeplink) ||
                other.deeplink == deeplink) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, deeplink, isError, user);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DeeplinkStateCopyWith<_$_DeeplinkState> get copyWith =>
      __$$_DeeplinkStateCopyWithImpl<_$_DeeplinkState>(this, _$identity);
}

abstract class _DeeplinkState implements DeeplinkResolverState {
  const factory _DeeplinkState(
      {final DeeplinkModel? deeplink,
      required final bool isError,
      final CurrentUser? user}) = _$_DeeplinkState;

  @override
  DeeplinkModel? get deeplink;
  @override
  bool get isError;
  @override
  CurrentUser? get user;
  @override
  @JsonKey(ignore: true)
  _$$_DeeplinkStateCopyWith<_$_DeeplinkState> get copyWith =>
      throw _privateConstructorUsedError;
}
