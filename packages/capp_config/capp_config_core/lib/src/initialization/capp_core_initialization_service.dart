import 'package:capp_domain/capp_domain.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../capp_config_core.dart';

class CappCoreInitializationService extends RetryModuleInitializationService {
  final IDeeplinkService? deeplinkService;
  final ApiLocker? apiLocker;
  final IFeatureFlagInitService? featureFlagInitService;
  final String appTrait;
  final RetrofitCache? retrofitCache;
  final AndroidPlayReferrerService? androidPlayReferrerService;
  final IRemoteConfigRepository? remoteConfigRepository;
  final IA2AIntentReceiverService? a2aIntentReceiverService;

  CappCoreInitializationService({
    required this.deeplinkService,
    required this.apiLocker,
    required this.featureFlagInitService,
    required this.appTrait,
    required this.retrofitCache,
    required this.androidPlayReferrerService,
    required this.remoteConfigRepository,
    required this.a2aIntentReceiverService,
  });

  @override
  Future<void> initialize() async {
    await initInputListWithRetry([
      InitializationInput(
        initFunction: () => featureFlagInitService!.init(appTrait),
      ),
      InitializationInput(
        initFunction: () async => retrofitCache?.init(),
      ),
      if (GmaPlatform.isAndroid)
        InitializationInput(
          initFunction: androidPlayReferrerService?.init ?? Future<void>.value,
        ),
      InitializationInput(
        initFunction: remoteConfigRepository!.init,
      ),
      InitializationInput(
        initFunction: () async {
          await deeplinkService?.init();
        },
      ),
      if (GmaPlatform.isAndroid)
        InitializationInput(
          initFunction: a2aIntentReceiverService?.init ?? Future<void>.value,
        ),
    ]);
  }
}
