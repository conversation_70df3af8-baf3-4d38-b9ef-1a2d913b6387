import 'package:capp_plugins_core/capp_plugins_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_shared/koyal_shared.dart';

import 'tracking_settings/capp_tracking_appsflyer_events_config.dart';
import 'tracking_settings/capp_tracking_common_events.dart';
import 'tracking_settings/capp_tracking_facebook_events_config.dart';
import 'tracking_settings/capp_tracking_fast_forwarder_event_config.dart';
import 'tracking_settings/capp_tracking_firebase_forwarder_event_config.dart';
import 'tracking_settings/capp_tracking_sas360_events_config.dart';

class CappTrackingInSettings extends CappTrackingSettings {
  CappTrackingInSettings({
    required String? googlePlayId,
    required String? appStoreId,
    required ShouldTrackProperties shouldTrackProperties,
  }) : super(
          googlePlayId: googlePlayId,
          appStoreId: appStoreId,
          commonConfig: CommonEventsConfig.commonConfig,
          firebaseEventsConfig: FirebaseForwarderEventsConfig.config,
          facebookEventsConfig: FacebookEventsConfig.facebookConfig,
          forwarderEventsConfig: FirebaseForwarderEventsConfig.config,
          fastForwarderEventsConfig: FastForwarderEventsConfig.config,
          appsflyerEventsConfig: AppsFlyerEventsConfig.appsFlyerConfig,
          gaTrackingId: 'UA-103408304-6',
          sas360EventsConfig: Sas360EventsConfig.sas360Config,
          sasTenantConfig: SasPluginTenantConfig(
            appId: 'GMA_PROD',
            tagServer: 'https://execution360.homecredit.co.in/t/mobile',
            tenantId: 'f592f7247700011c35eda333',
          ),
          sasInsiderTenantConfig: SasPluginTenantConfig(
            appId: 'GMA_TEST',
            tagServer: 'https://execution360.homecredit.co.in/t/mobile',
            tenantId: 'fe54d718ec00011f71775f9c',
          ),
          sasSkipTrackViewDefault: Sas360EventsConfig.sas360SkipTrackViewDefault,
          eventTrackingOptions: [
            EventTrackingOptions.firebase,
            EventTrackingOptions.facebook,
            if (!GmaPlatform.isWeb) ...[
              EventTrackingOptions.crashlytics,
              EventTrackingOptions.sas360,
              EventTrackingOptions.forwarder,
            ],
          ],
          shouldTrackProperties: shouldTrackProperties,
        );
}
