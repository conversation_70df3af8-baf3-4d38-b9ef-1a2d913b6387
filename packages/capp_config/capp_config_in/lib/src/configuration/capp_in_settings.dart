import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_config_core/capp_config_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class CappProdSettingsIn extends CappProdSettingsBase {
  const CappProdSettingsIn()
      : super(
          baseUrl: 'https://api.in.hcgma.com/',
          baseStageUrl: null,
          basePerformanceUrl: null,
          commonBaseUrl: 'https://api.in.hcgma.com/',
          identityBaseUrl: 'https://identity.in.hcgma.com/',
          identityBaseStageUrl: null,
          identityBasePerformanceUrl: null,
          imageBaseUrl: 'https://files.in.hcgma.com/image',
          insiderBaseUrl: 'https://insider.in.hcgma.com/',
          insiderStageBaseUrl: 'https://insider.in.hcgma.com/',
          insiderPerformanceBaseUrl: 'https://insider.in.hcgma.com/',
          deeplinkUrl: 'https://app.supercupo.co.in/',
          dynamicLinkUrl: 'https://supercupo.page.link/',
          appStoreId: '1498609662',
          googlePlayId: 'net.homecredit.selfcare',
          defaultPositionLatitude: 28.494367,
          defaultPositionLongitude: 77.088713,
        );
}

const String appTraitValue = 'CAPP';

ConfigurationRoot environmentConfigSeed = const ConfigurationRoot(
  cappIdentityConfiguration: CappIdentityConfiguration(
    passwordLength: 4,
    passwordRegex: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@#\u0024~&*§~±!%^()_+{}:|<>?,.\u0022])\\S{8,}\u0024',
    emailRegex: defaultEmailRegex,
    passwordRegexLength: r'^\S{8,}',
    passwordRegexDigitsLowerCaseUperCaseLetters: r'^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])\S{0,}$',
    passwordRegexSpecialCharacters: r'^(?=.*[±!@#$%^&*()_+{}:"|<>?,.\/;"\\[\]])\S{0,}$',
    threeRepeatingConsecutiveDigits: r'(.*[0-9])\1{2,}',
    threeSucceedingConsecutiveDigits: '(012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210)',
  ),
);

const Map<String, dynamic> remoteConfigDefaultValues = <String, dynamic>{
  'signup_email': 'required',
};
