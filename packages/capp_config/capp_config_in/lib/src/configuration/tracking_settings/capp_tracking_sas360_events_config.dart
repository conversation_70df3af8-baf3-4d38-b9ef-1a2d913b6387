import 'package:koyal_shared/koyal_shared.dart';

class Sas360EventsConfig {
  static Map<KoyalEvent, bool> sas360Config = const {
    KoyalEvent.undefined: true,
    KoyalEvent.sasRecalculateCustomerOffers: true,
    KoyalEvent.sasNewTokenFirebase: true,

    // events US007 - US011 - screen_view events

    // events US019 - US020 - screen_view events
    // events US021 - US024:
    KoyalEvent.authAccountCreationVerifyCustomerOnIdNumberClick: true,
    KoyalEvent.authAccountCreationVerifyCustomerOnContractNumberClick: true,
    KoyalEvent.authAccountCreationVerifyCustomerOnNotCustomerClick: true,
    KoyalEvent.authAccountCreationVerifyCustomerOnContinueClick: true,
    KoyalEvent.authUnlockScreenOnForgotPinClick: true,
    KoyalEvent.authUnlockScreenOnSwitchUserClick: true,
    KoyalEvent.authUnlockScreenOnEnterPinClick: true,
    KoyalEvent.authUnlockScreenOnBiometricsLoginClick: true,
    KoyalEvent.authForgotPinEnterPhoneNumberOnGoBackClick: true,
    KoyalEvent.authForgotPinEnterPhoneNumberOnContinueClick: true,
    KoyalEvent.authForgotPinSecondIdentifierrOnGoBackClick: true,
    KoyalEvent.authForgotPinSecondIdentifierrOnConfirmClick: true,
    KoyalEvent.authForgotPinResetPinOnGoBackClick: true,
    KoyalEvent.authForgotPinResetPinOnCriteriaViolationView: true,
    KoyalEvent.authForgotPinSuccessPinResetOnContinueToHomepageClick: true,
    KoyalEvent.authAccountUnblockedScreenOnForgotPinClick: true,
    KoyalEvent.authChangePhoneNumberEnterPinOnGoBackClick: true,
    KoyalEvent.authChangePhoneNumberEnterPinOnForgotPinClick: true,
    KoyalEvent.authChangePhoneNumberEnterPinOnErrorIncorrectPinView: true,
    KoyalEvent.authChangePhoneNumberEnterPinOnErrorIncorrectPinAccountPermBlockedView: true,

    //SAS init
    KoyalEvent.sasSessionInit: true,

    // home dashboard - US 48159 events
    KoyalEvent.homeDashboardView: true,
    KoyalEvent.homeDashboardLoginRegisterClick: true,
    KoyalEvent.homeDashboardInboxClick: true,
    KoyalEvent.homeDashboardPromotionBannerView: false, // SAS excluded
    KoyalEvent.homeDashboardPromotionBannerSwipe: true,
    KoyalEvent.homeDashboardPromotionBannerClick: true,
    KoyalEvent.loanOriginationProductBannerClick: true,
    KoyalEvent.loanOriginationScoringBannerClick: true,
    KoyalEvent.loanOriginationSignatureScreenBannerClick: true,
    KoyalEvent.homeDashboardActionBeltView: false, // SAS excluded
    KoyalEvent.homeDashboardActionBeltClick: true,
    KoyalEvent.homeDashboardBlogPostView: true,
    KoyalEvent.homeDashboardBlogPostClick: true,
    KoyalEvent.homeDashboardBlogPostViewAllClick: true,
    KoyalEvent.homeDashboardGamesBannerClick: true,
    KoyalEvent.homeDashboardPosLoanBannerClick: true,
    KoyalEvent.homeDashboardPosLoanBannerView: true,
    KoyalEvent.homeDashboardDealsAndCouponsView: true,
    KoyalEvent.homeDashboardDealsAndCouponsViewAllClick: true,
    KoyalEvent.homeDashboardDealsAndCouponsClick: true,
    KoyalEvent.homeDashboardReservationBannerView: true,
    KoyalEvent.homeDashboardReservationBannerClick: true,
    KoyalEvent.homeDashboardInstallmentsOverviewViewAllClick: true,
    KoyalEvent.homeDashboardInstallmentsOverviewBannerView: true,
    KoyalEvent.homeDashboardInstallmentsOverviewBannerClick: true,
    KoyalEvent.homeDashboardLoanOffersViewAllClick: true,
    KoyalEvent.homeDashboardIncomeBannerView: true,
    KoyalEvent.homeDashboardIncomeBannerClick: true,
    KoyalEvent.homeDashboardGamesBannerView: true,
    KoyalEvent.homeDashboardLoadsBillsBannerView: true,
    KoyalEvent.homeDashboardLoadsBillsBannerClick: true,
    KoyalEvent.homeDashboardLudoHeroBannerView: true,
    KoyalEvent.homeDashboardLudoHeroBannerClick: true,
    KoyalEvent.homeDashboardSasBannerView: true,
    KoyalEvent.homeDashboardSasBannerClick: true,
    KoyalEvent.homeDashboardGamesWidgetView: true,
    KoyalEvent.homeDashboardGamesWidgetClick: true,

    // Home Main Navigation
    KoyalEvent.homeMainNavigationItemClick: true,
    KoyalEvent.homeSupercupoAssistantItemClick: true,
    KoyalEvent.homeSupercupoAssistantExitClick: true,
    KoyalEvent.homeAccountNofiticationsClick: true,
    KoyalEvent.homeAccountRewardsClick: true,
    KoyalEvent.homeAccountSettingsClick: true,
    KoyalEvent.homeAccountDocumentsClick: true,
    KoyalEvent.homeAccountFaqClick: true,
    KoyalEvent.homeAccountContactClick: true,
    KoyalEvent.homeAccountAboutUsClick: true,
    KoyalEvent.homeAccountIPriceClick: true,
    KoyalEvent.homeAccountLoginRegisterClick: true,
    KoyalEvent.homeAccountPersonalDetailsClick: true,
    KoyalEvent.homeAccountLoansClick: true,
    KoyalEvent.homeAccountRetrieveClick: true,
    KoyalEvent.homeAccountPromoClick: true,
    KoyalEvent.homeAccountTransationHistoryClick: true,

    // Blog Screen
    KoyalEvent.blogScreenBackButtonClick: true,
    KoyalEvent.blogScreenBlogPostClick: true,
    KoyalEvent.blogScreenCategoryClick: true,
    KoyalEvent.blogScreenBlogPostView: true,

    // Inbox Screen
    KoyalEvent.inboxScreenGoBackClick: true,
    KoyalEvent.inboxScreenMenuItemClick: true,
    KoyalEvent.inboxScreenMessageClick: true,
    KoyalEvent.inboxScreenUnreadSwipe: true,
    KoyalEvent.inboxScreenDeleteSwipe: true,
    KoyalEvent.inboxScreenUndeleteClick: true,
    KoyalEvent.inboxMessageScreenLinkClick: true,
    KoyalEvent.inboxMessageScreenView: true,
    KoyalEvent.inboxMessageScreenDeleteClick: true,

    // Contact Us Screen
    KoyalEvent.contactUsScreenView: true,
    KoyalEvent.contactUsGoBackClick: true,
    KoyalEvent.contactUsWebClick: true,
    KoyalEvent.contactUsEmailClick: true,
    KoyalEvent.contactUsCustomerServiceClick: true,
    KoyalEvent.contactUsChatClick: false,
    KoyalEvent.contactUsSuperCupoClick: true,
    KoyalEvent.contactUsGrievanceOfficerClick: true,

    // Grievance Officer Screen
    KoyalEvent.grievanceOfficerScreenView: true,
    KoyalEvent.grievanceOfficerScreenGoBackClick: true,
    KoyalEvent.grievanceOfficerScreenEmailClick: true,
    KoyalEvent.grievanceOfficerScreenLandlineClick: true,

    //ULO Tracking signature
    KoyalEvent.loanOriginationApplicationSummaryConfirmClick: true,
    KoyalEvent.loanOriginationContractDetailsSignClick: true,
    KoyalEvent.loanOriginationContractOtpEnterClick: true,
    KoyalEvent.loanOriginationApplicationCompletedScreenView: true,
    KoyalEvent.loanOriginationApplicationCompletedViewLoanDetailsClick: true,
    KoyalEvent.loanOriginationApplicationCompletedBannerClick: true,
    KoyalEvent.loanOriginationContractPrepareMaterialsScreenView: true,
    KoyalEvent.loanOriginationContractAccountSetupScreenView: true,
    KoyalEvent.loanOriginationHomeDashboard: true,
    KoyalEvent.loanOriginationAlternativeBannerClick: false,
    KoyalEvent.loanOriginationAlternativeBannerImpression: false,
    KoyalEvent.loanOriginationLoanDashboardAlternativeBannerClick: false,
    KoyalEvent.loanOriginationLoanDashboardAlternativeBannerImpression: false,
    KoyalEvent.loanScoreCalculatorBackClick: false,
    KoyalEvent.loanScoreCalculatorSliderAmount: false,
    KoyalEvent.loanScoreCalculatorTenureClick: false,
    KoyalEvent.loanScoreCalculatorProtectYes: false,
    KoyalEvent.loanScoreCalculatorProtectNo: false,
    KoyalEvent.loanScoreCalculatorProtectMoreClick: false,
    KoyalEvent.loanScoreCalculatorCareYes: false,
    KoyalEvent.loanScoreCalculatorCareNo: false,
    KoyalEvent.loanScoreCalculatorCareMoreClick: false,
    KoyalEvent.loanScoreCalculatorAddInfo: false,
    KoyalEvent.loanScoreCalculatorFeeInfoClick: false,
    KoyalEvent.loanScoreCalculatorTaxInfoClick: false,
    KoyalEvent.loanScoreCalculatorContinueClick: false,
    KoyalEvent.loanOriginationAlternativeScoringFormView: false,
    KoyalEvent.loanOriginationAlternativeScoringFormDataView: false,
    KoyalEvent.loanOriginationAlternativeScoringFormIdDocClick: false,
    KoyalEvent.loanOriginationAlternativeScoringFormSecIdDocClick: false,
    KoyalEvent.loanOriginationAlternativeScoringFormIncomeClick: false,
    KoyalEvent.loanOriginationAlternativeScoringFormMaritalClick: false,
    KoyalEvent.loanOriginationAlternativeScoringFormContinueClick: false,
    KoyalEvent.loanOriginationAlternativeScoringFormBackClick: false,
    KoyalEvent.loanOriginationOfferMenuProductBannerView: false,
    KoyalEvent.loanOriginationOfferMenuProductBannerClick: false,
    KoyalEvent.loanOriginationUpdateProfileScreenView: false,
    KoyalEvent.loanOriginationUpdateProfileScreenDataView: false,
    KoyalEvent.loanOriginationUpdateProfileContinueButtonClick: false,
    KoyalEvent.loanOriginationUpdateProfileIncomeInformationClick: false,
    KoyalEvent.loanOriginationUpdateProfileMaritalStatusClick: false,
    KoyalEvent.loanOriginationUpdateProfileHousingTypeClick: false,

// Acc Creation Existing account
    KoyalEvent.authVerificationErrorScreenContinueClick: true,
    KoyalEvent.authGoToHomepageClick: true,
    KoyalEvent.authLoginButtonClick: true,
    KoyalEvent.authForgotYourPasswordClick: true,
    KoyalEvent.authBackButtonClick: true,
    KoyalEvent.authBiometricAuthenticationView: true,
    KoyalEvent.authBiometricProccedClick: true,
    KoyalEvent.authBiometricLaterClick: true,
  };
  static Map<String, bool> sas360SkipTrackViewDefault = {};
}
