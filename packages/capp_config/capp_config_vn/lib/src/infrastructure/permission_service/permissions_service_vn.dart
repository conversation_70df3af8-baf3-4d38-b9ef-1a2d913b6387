import 'package:koyal_shared/koyal_shared.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionVnService extends PermissionsService {
  PermissionVnService({required PermissionsRepository permissionsRepository})
      : super(permissionsRepository: permissionsRepository);
  @override
  List<Permission> getConsentPermissions() {
    return [
      Permission.location,
      Permission.notification,
    ];
  }

  @override
  List<Permission> getCommunicationsPermissions() {
    return [Permission.notification];
  }
}
