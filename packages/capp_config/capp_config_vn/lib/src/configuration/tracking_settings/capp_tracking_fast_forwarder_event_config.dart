import 'package:koyal_shared/koyal_shared.dart';

/// The Fast Forwarder service was introduced in US 167236 to enable immediate event tracking without buffer delay.
/// Use this only for critical events that require real-time tracking to prevent excessive usage.
/// CAUTION: Events here must not be in the standard forwarder, as this would cause duplicate tracking.
class FastForwarderEventsConfig {
  static Map<KoyalEvent, bool> config = const {
    KoyalEvent.homeDashboardView: true,
  };
}
