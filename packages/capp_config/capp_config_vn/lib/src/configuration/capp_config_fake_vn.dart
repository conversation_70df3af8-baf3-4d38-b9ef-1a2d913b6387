import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_config_core/capp_config_core.dart';
import 'package:capp_ui/capp_ui.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:get_it/get_it.dart';

class CappConfigFakeVn extends CappConfigFake {
  CappConfigFakeVn({
    required String appStoreId,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required String imageBaseUrl,
    required Map<String, dynamic> remoteConfigSeed,
    required String googlePlayId,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    required ConfigurationRoot environmentConfigSeed,
    required String appTraitValue,
    String? appGalleryId,
  }) : super(
          appStoreId: appStoreId,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          imageBaseUrl: imageBaseUrl,
          remoteConfigSeed: remoteConfigSeed,
          googlePlayId: googlePlayId,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          environmentConfigSeed: environmentConfigSeed,
          appTraitValue: appTraitValue,
          appGalleryId: appGalleryId,
        );

  @override
  void registerAnimation(GetIt c) {
    c.registerFactory<ILoadingWidgetFactory>(LoadingFactory.new);
  }
}

class CappConfigTestFakeVn extends CappConfigTestFake {
  CappConfigTestFakeVn({
    required String appStoreId,
    required String deeplinkUrl,
    required String dynamicLinkUrl,
    required String imageBaseUrl,
    required Map<String, dynamic> remoteConfigSeed,
    required String googlePlayId,
    required double defaultPositionLatitude,
    required double defaultPositionLongitude,
    required ConfigurationRoot environmentConfigSeed,
    required String appTraitValue,
    String? appGalleryId,
  }) : super(
          appStoreId: appStoreId,
          deeplinkUrl: deeplinkUrl,
          dynamicLinkUrl: dynamicLinkUrl,
          imageBaseUrl: imageBaseUrl,
          remoteConfigSeed: remoteConfigSeed,
          googlePlayId: googlePlayId,
          defaultPositionLatitude: defaultPositionLatitude,
          defaultPositionLongitude: defaultPositionLongitude,
          environmentConfigSeed: environmentConfigSeed,
          appTraitValue: appTraitValue,
          appGalleryId: appGalleryId,
        );

  @override
  void registerAnimation(GetIt c) {
    c.registerFactory<ILoadingWidgetFactory>(LoadingFactory.new);
  }
}
