import 'package:capp_tracking/capp_tracking.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_shared/koyal_shared.dart';

import 'tracking_settings/capp_tracking_appsflyer_events_config.dart';
import 'tracking_settings/capp_tracking_common_events.dart';
import 'tracking_settings/capp_tracking_facebook_events_config.dart';
import 'tracking_settings/capp_tracking_fast_forwarder_event_config.dart';
import 'tracking_settings/capp_tracking_firebase_forwarder_event_config.dart';

class CappTrackingVnSettings extends CappTrackingSettings {
  CappTrackingVnSettings({
    required String? googlePlayId,
    required String? appStoreId,
    required ShouldTrackProperties shouldTrackProperties,
  }) : super(
          googlePlayId: googlePlayId,
          appStoreId: appStoreId,
          commonConfig: CommonEventsConfig.commonConfig,
          firebaseEventsConfig: FirebaseForwarderEventsConfig.config,
          facebookEventsConfig: FacebookEventsConfig.facebookConfig,
          forwarderEventsConfig: FirebaseForwarderEventsConfig.config,
          fastForwarderEventsConfig: FastForwarderEventsConfig.config,
          gaTrackingId: 'UA-214409412-1',
          appsflyerEventsConfig: AppsFlyerEventsConfig.appsFlyerConfig,
          eventTrackingOptions: [
            EventTrackingOptions.firebase,
            EventTrackingOptions.fastForwarder,
            if (!GmaPlatform.isWeb) ...[
              EventTrackingOptions.crashlytics,
              EventTrackingOptions.forwarder,
              EventTrackingOptions.appsflyer,
            ],
          ],
          gaPropertyMapIndex: TrackingProperties.gaPropertyMapIndexVN,
          shouldTrackProperties: shouldTrackProperties,
        );
}
