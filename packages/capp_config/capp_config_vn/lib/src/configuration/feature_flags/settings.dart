import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:gma_vault/gma_vault.dart';

import 'fake_feature_flag_seed.dart';
import 'prod_feature_flag_seed.dart';

class CappFeatureFlagsProdVnSettings extends CappFeatureFlagsSettings {
  CappFeatureFlagsProdVnSettings()
      : super(
          flagsmithUrl: 'https://api.vn.hcgma.com/flagsmith/api/v1/',
          flagsmithStageUrl: 'https://api.vn.hcgma.com/stage/flagsmith/api/v1/',
          flagsmithPerformanceUrl: null,
          flagsmithApiKey: GmaVault().get(GmaVaultType.flagsmithApiKeyVn) ?? '',
          encryptionPassword: GmaVault().get(GmaVaultType.flagsmithApiKeyVn) ?? '',
          seeds: ProdFeatureFlagSeedVn().defaultValues,
        );
}

class CappFeatureFlagsFakeVnSettings extends CappFeatureFlagsFakeSettings {
  CappFeatureFlagsFakeVnSettings()
      : super(
          flagsmithUrl: '',
          flagsmithStageUrl: '',
          flagsmithPerformanceUrl: '',
          flagsmithApiKey: '',
          encryptionPassword: '',
          seeds: FakeFeatureFlagSeedVn().defaultValues,
          loggedInSeeds: FakeFeatureFlagSeedVn().loggedUser,
        );
}
