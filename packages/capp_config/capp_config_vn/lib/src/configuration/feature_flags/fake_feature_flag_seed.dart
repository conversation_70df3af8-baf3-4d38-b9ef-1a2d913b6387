import 'package:capp_config_core/capp_config_core.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flagsmith/flagsmith.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class FakeFeatureFlagSeedVn implements IFeatureFlagSeed {
  static final List<Flag> _specificSeed = <Flag>[
    Flag.seed(FeatureFlag.contractDocumentEmail, enabled: false),
    Flag.seed(FeatureFlag.newRepayment),
    Flag.seed(FeatureFlag.phoneNumberHintDialogType, value: 'mobileNumberPackage'),
    Flag.seed(FeatureFlag.loanSignature),
    Flag.seed(FeatureFlag.vnEServiceWaf),
    Flag.seed(FeatureFlag.cardReminder, enabled: false),
    Flag.seed(FeatureFlag.profileMenuSelfServiceEloads),
    Flag.seed(FeatureFlag.profileMenuSelfServiceEBills),
    Flag.seed(FeatureFlag.transactionEbillsWaterBill),
    Flag.seed(FeatureFlag.transactionEbillsElectricityBill),
    Flag.seed(FeatureFlag.pairCuidLater),
    Flag.seed(FeatureFlag.offerBanner),
    Flag.seed(FeatureFlag.referralCode),
    Flag.seed(FeatureFlag.passwordValidationUx, enabled: false),
    Flag.seed(FeatureFlag.crossroads, enabled: false),
    Flag.seed(FeatureFlag.calculatorAmountHideButton, enabled: false),
    Flag.seed(FeatureFlag.directFlow, enabled: false),
    Flag.seed(FeatureFlag.relVnAdditionalRepaymentInformation),
    Flag.seed(FeatureFlag.repaymentMethodEWallet),
    Flag.seed(FeatureFlag.repaymentMethodInternetBanking),
    Flag.seed(FeatureFlag.repaymentMethodAtmCard),
    Flag.seed(FeatureFlag.repaymentBappPaymentMethod),
    Flag.seed(FeatureFlag.repaymentPromotionFirstAdoption, enabled: false),
    Flag.seed(FeatureFlag.agentCode, enabled: false),
    Flag.seed(FeatureFlag.onboardingLanguageSelection, enabled: false),
    Flag.seed(FeatureFlag.filterPosWard, enabled: false),
    Flag.seed(FeatureFlag.paymentELoadsBaoKim),
    Flag.seed(KoyalFeatureFlag.eventNameWithAction),
    Flag.seed(FeatureFlag.replaceLogoutWithLock),
    Flag.seed(FeatureFlag.hplVietQr),
    Flag.seed(FeatureFlag.vasRedesign, enabled: false),
    Flag.seed(FeatureFlag.removePopupSaConfirmationTW),
    Flag.seed(FeatureFlag.homeScreenVnNewDesign, enabled: false),
    Flag.seed(FeatureFlag.vnRevampBiggerCards, enabled: false),
    Flag.seed(FeatureFlag.ltuNewUIComponents, enabled: false),
    Flag.seed(FeatureFlag.sasRTDMHomeBanners, enabled: false),
    Flag.seed(FeatureFlag.sasRTDMHomeBannersCache, enabled: false),
    // CCX Intro
    Flag.seed(FeatureFlag.simplifyCCXJourney, enabled: false),
    Flag.seed(FeatureFlag.improveDisplayingWelcomeScreen, enabled: false),
    Flag.seed(FeatureFlag.stopShowingWSDummyScoring, enabled: false),
    Flag.seed(FeatureFlag.documentsExpirationsInAppform, enabled: false),
    Flag.seed(FeatureFlag.documentsExpirationsInAccountVerification, enabled: false),
    Flag.seed(FeatureFlag.backupPartnerForDisbursementMethod, enabled: false),
    Flag.seed(FeatureFlag.vnWelcomeKitSurvey, enabled: false),
    // 3in1 VAS
    Flag.seed(FeatureFlag.ccx3In1Vas, enabled: false),

    Flag.seed(FeatureFlag.minIncomeValidation, enabled: false),

    Flag.seed(FeatureFlag.skipProductIntro, enabled: false),
  ];
  static final List<Flag> _loggedUserSpecificSeed = <Flag>[
    Flag.seed(FeatureFlag.contractDocumentEmail, enabled: false),
    Flag.seed(FeatureFlag.newRepayment),
    Flag.seed(FeatureFlag.phoneNumberHintDialogType, value: 'mobileNumberPackage'),
    Flag.seed(FeatureFlag.loanSignature),
    Flag.seed(FeatureFlag.vnEServiceWaf),
    Flag.seed(FeatureFlag.cardReminder, enabled: false),
    Flag.seed(FeatureFlag.profileMenuSelfServiceEloads),
    Flag.seed(FeatureFlag.profileMenuSelfServiceEBills),
    Flag.seed(FeatureFlag.transactionEbillsWaterBill),
    Flag.seed(FeatureFlag.transactionEbillsElectricityBill),
    Flag.seed(FeatureFlag.offerBanner),
    Flag.seed(FeatureFlag.referralCode),
    Flag.seed(FeatureFlag.crossroads, enabled: false),
    Flag.seed(FeatureFlag.calculatorAmountHideButton, enabled: false),
    Flag.seed(FeatureFlag.directFlow, enabled: false),
    Flag.seed(FeatureFlag.agentCode, enabled: false),
    Flag.seed(FeatureFlag.repaymentPromiseToPayEntryPointMainScreen, enabled: false),
    Flag.seed(FeatureFlag.repaymentDirectDiscountMainScreen),
    Flag.seed(FeatureFlag.repaymentPromotionFirstAdoption, enabled: false),
    Flag.seed(FeatureFlag.repaymentMomoApiV2),
    Flag.seed(FeatureFlag.repaymentMainScreenBanner, enabled: false),
    Flag.seed(FeatureFlag.repaymentAllowDirectDiscountEL, enabled: false),
    Flag.seed(FeatureFlag.repaymentPopupPleaseStay),
    Flag.seed(FeatureFlag.repaymentResultBanner),
    Flag.seed(FeatureFlag.repaymentSappiAlertBar, enabled: false),
    Flag.seed(FeatureFlag.repaymentAdaManagementMenu, enabled: false),
    Flag.seed(FeatureFlag.repaymentSappiPromotionAllowance, enabled: false),
    Flag.seed(FeatureFlag.repaymentNewHomePayLaterName),
    Flag.seed(FeatureFlag.repaymentContractSourceGetFromPcs),
    Flag.seed(FeatureFlag.filterPosWard, enabled: false),
    Flag.seed(FeatureFlag.cashLoanAbTestingDifferentOfferMix),
    Flag.seed(FeatureFlag.loansVNDeepLinkCashCard),
    Flag.seed(FeatureFlag.relVnAdditionalRepaymentInformation),
    Flag.seed(FeatureFlag.referralCodeACL),
    Flag.seed(FeatureFlag.loanVNWaitingScreen),
    Flag.seed(FeatureFlag.loanVNImproveSearchBank, enabled: false),
    Flag.seed(FeatureFlag.loanVNScoringLiveActivities, enabled: false),
    Flag.seed(FeatureFlag.loanVNApplicationLiveActivities, enabled: false),
    Flag.seed(FeatureFlag.loanVNNewAppFormUI, enabled: false),
    Flag.seed(FeatureFlag.loanVNNewAccountVerificationUI, enabled: false),
    Flag.seed(FeatureFlag.loanVNCustomizedCCXScoringFlow, enabled: false),
    Flag.seed(FeatureFlag.displayOfferBannerAtRepaymentSuccessfulScreen),
    Flag.seed(FeatureFlag.paymentELoadsBaoKim),
    Flag.seed(KoyalFeatureFlag.eventNameWithAction),
    Flag.seed(FeatureFlag.paymentQrWelcomeScreen),
    Flag.seed(FeatureFlag.paymentQrListPartnersCta),
    Flag.seed(FeatureFlag.replaceLogoutWithLock),
    Flag.seed(FeatureFlag.hplVietQr),
    Flag.seed(FeatureFlag.posAPIV2),
    Flag.seed(FeatureFlag.newPosSelectionUiV2, enabled: false),
    Flag.seed(FeatureFlag.paymentQrHplOnboardingCta),
    Flag.seed(FeatureFlag.welcomeCashOffer),
    Flag.seed(FeatureFlag.welcomeScoringOffer),
    Flag.seed(FeatureFlag.welcomeIdCard),
    Flag.seed(FeatureFlag.welcomeSigning),
    Flag.seed(FeatureFlag.welcomeCreditCardOffer),
    Flag.seed(FeatureFlag.welcomeCashAndCreditCardOffer),
    Flag.seed(FeatureFlag.welcomeCashAndCreditCardScoringOffer),
    Flag.seed(FeatureFlag.improveDisplayingWelcomeScreen, enabled: false),
    Flag.seed(FeatureFlag.stopShowingWSDummyScoring, enabled: false),
    Flag.seed(FeatureFlag.loanImproveDisplayIR, enabled: false),
    Flag.seed(FeatureFlag.loanImproveDisplayIRNote, enabled: false),
    Flag.seed(FeatureFlag.vasRedesign, enabled: false),
    Flag.seed(FeatureFlag.removePopupSaConfirmationTW),
    Flag.seed(FeatureFlag.homeScreenVnNewDesign, enabled: false),
    Flag.seed(FeatureFlag.vnRevampBiggerCards, enabled: false),
    Flag.seed(FeatureFlag.ltuNewUIComponents, enabled: false),
    Flag.seed(FeatureFlag.transactionPostpaid),
    Flag.seed(FeatureFlag.eloadsUserFeedbackJourney),
    Flag.seed(FeatureFlag.ebillsUserFeedbackJourney),
    Flag.seed(FeatureFlag.vietQrUserFeedbackJourney),
    Flag.seed(FeatureFlag.partnerQrUserFeedbackJourney),
    Flag.seed(FeatureFlag.transactionWaterBill),
    Flag.seed(FeatureFlag.transactionElectricBill),
    Flag.seed(FeatureFlag.hplTenorRevamp),
    Flag.seed(FeatureFlag.hplPromotionQrCommodities),
    Flag.seed(FeatureFlag.hplSignCreditLineContract, enabled: false),
    Flag.seed(FeatureFlag.repaymentCollectData, enabled: false),
    Flag.seed(FeatureFlag.topupPromotionBadge, enabled: false),

    // CCX Intro
    Flag.seed(FeatureFlag.simplifyCCXJourney, enabled: false),
    Flag.seed(FeatureFlag.vnChangeNationalId, enabled: false),
    Flag.seed(FeatureFlag.vnChangeNationalIdLongterm, enabled: false),
    Flag.seed(FeatureFlag.documentsExpirationsInAppform, enabled: false),
    Flag.seed(FeatureFlag.documentsExpirationsInAccountVerification, enabled: false),
    Flag.seed(FeatureFlag.requestNfcChecking, enabled: false),
    Flag.seed(FeatureFlag.updateOcrIdNumberToDms, enabled: false),

    Flag.seed(FeatureFlag.checkNfcSupportBeforeOcr),

    // Video injection
    Flag.seed(FeatureFlag.doEvaluateSelfieVideoInjection),
    Flag.seed(FeatureFlag.doEvaluateDocumentVideoInjection),
    Flag.seed(FeatureFlag.waitForSelfieVideoInjectionEvaluationCompleted),
    Flag.seed(FeatureFlag.waitForDocumentVideoInjectionEvaluationCompleted),

    Flag.seed(FeatureFlag.updateMandatoryNfcInfoFor1BoDCCXAndUpdateIdCardNfc),
    Flag.seed(FeatureFlag.updateMandatoryDmsAndNfcInfoSignatureCcx),
    Flag.seed(FeatureFlag.updateMandatoryDmsInfoSignatureOther),
    Flag.seed(FeatureFlag.eKycNewAddressComponent),

    Flag.seed(FeatureFlag.backupPartnerForDisbursementMethod, enabled: false),
    Flag.seed(FeatureFlag.vnWelcomeKitSurvey, enabled: false),
    // 3in1 VAS
    Flag.seed(FeatureFlag.ccx3In1Vas, enabled: false),

    // Big ticket USP
    Flag.seed(FeatureFlag.bigTicketUSP, enabled: false),

    Flag.seed(FeatureFlag.minIncomeValidation, enabled: false),

    Flag.seed(FeatureFlag.skipProductIntro, enabled: false),
  ];

  @override
  List<Flag> get defaultValues => [
        ...fakeSharedSeed,
        ..._specificSeed,
      ];

  List<Flag> loggedUser = [
    ...fakeLoggedUserSharedSeed,
    ..._loggedUserSpecificSeed,
  ];

  @override
  List<Flag> mergeDefaultWithSpecific(List<Flag> defaultValues, List<Flag> specific) {
    throw UnimplementedError();
  }
}
