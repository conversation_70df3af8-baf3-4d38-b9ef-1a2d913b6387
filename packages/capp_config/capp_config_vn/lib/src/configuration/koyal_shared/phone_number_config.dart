import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:koyal_shared/koyal_shared.dart';

class PhoneNumberConfigVn implements PhoneNumberConfig {
  @override
  final phoneMaxLength = 10;
  @override
  final phoneRegex = r'^0\d{9}$';
  @override
  final String? phonePrefix = null;
  @override
  final useInternationalFormat = true;
  @override
  final country = CountryWithPhoneCode(
    countryCode: 'VN',
    countryName: 'Vietnam',
    exampleNumberFixedLineInternational: '0 7410410123',
    exampleNumberFixedLineNational: '7410410123',
    exampleNumberMobileInternational: '0 7555171589',
    exampleNumberMobileNational: '7555171589',
    phoneCode: '84',
    phoneMaskFixedLineInternational: '0 0000000000',
    phoneMaskFixedLineNational: '0000000000',
    phoneMaskMobileInternational: '0 0000000000',
    phoneMaskMobileNational: '0000000000',
  );
}
