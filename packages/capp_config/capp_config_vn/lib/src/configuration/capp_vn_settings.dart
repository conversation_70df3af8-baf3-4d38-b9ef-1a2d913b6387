import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_config_core/capp_config_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class CappFakeSettingsVn extends CappFakeSettingsBase {
  const CappFakeSettingsVn()
      : super(
          defaultPositionLatitude: 19.124032130955854,
          defaultPositionLongitude: 72.89121279492974,
          imageBaseUrl: 'https://www.homecredit.co.in/sites/default/files/2021-11/money-app-banner.jpeg',
          insiderBaseUrl: 'https://insider.in.hcgma.com/',
          insiderStageBaseUrl: 'https://insider.in.hcgma.com/',
          insiderPerformanceBaseUrl: 'https://insider.in.hcgma.com/',
          deeplinkUrl: 'https://app.gma.homecredit.vn/',
          dynamicLinkUrl: 'https://homezuiapp.page.link/',
          onelinkUrl: 'https://hcv.onelink.me/',
          googlePlayId: '',
          appStoreId: 'app_store_id',
        );
}

class CappProdSettingsVn extends CappProdSettingsBase {
  const CappProdSettingsVn()
      : super(
          baseUrl: 'https://api.vn.hcgma.com/',
          baseStageUrl: 'https://api.vn.hcgma.com/stage/',
          basePerformanceUrl: null,
          commonBaseUrl: 'https://api.vn.hcgma.com/',
          identityBaseUrl: 'https://identity.vn.hcgma.com/',
          identityBaseStageUrl: 'https://identity.vn.hcgma.com/stage/',
          identityBasePerformanceUrl: null,
          imageBaseUrl: 'https://files.vn.hcgma.com/image',
          insiderBaseUrl: 'https://insider.vn.hcgma.com/',
          insiderStageBaseUrl: 'https://insider.vn.hcgma.com/',
          insiderPerformanceBaseUrl: 'https://insider.vn.hcgma.com/',
          deeplinkUrl: 'https://app.gma.homecredit.vn/',
          dynamicLinkUrl: 'https://homezuiapp.page.link/',
          onelinkUrl: 'https://hcv.onelink.me/',
          appStoreId: '1553761073',
          googlePlayId: 'vn.homecredit.capp',
          defaultPositionLatitude: 10.821574379427405,
          defaultPositionLongitude: 106.66491257728812,
        );
}

const String appTraitValue = 'CAPP';

ConfigurationRoot environmentConfigSeed = const ConfigurationRoot(
  cappIdentityConfiguration: CappIdentityConfiguration(
    passwordLength: 6,
    passwordRegex: '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@#\u0024~&*§~±!%^()_+{}:|<>?,.\u0022])\\S{8,}\u0024',
    emailRegex: defaultEmailRegex,
    passwordRegexLength: r'^\S{8,}',
    passwordRegexDigitsLowerCaseUperCaseLetters: r'^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])\S{0,}$',
    passwordRegexSpecialCharacters: r'^(?=.*[±!@#$%^&*()_+{}:"|<>?,.\/;"\\[\]])\S{0,}$',
    threeRepeatingConsecutiveDigits: r'(.*[0-9])\1{2,}',
    threeSucceedingConsecutiveDigits: '(012|123|234|345|456|567|678|789|987|876|765|654|543|432|321|210)',
  ),
);

const Map<String, dynamic> remoteConfigDefaultValues = <String, dynamic>{
  'signup_email': 'required',
};
