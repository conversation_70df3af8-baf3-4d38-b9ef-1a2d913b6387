import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../domain/products_action_belt/i_products_action_belt_repository.dart';

part 'products_action_belt_bloc.freezed.dart';
part 'products_action_belt_event.dart';
part 'products_action_belt_state.dart';

class ProductsActionBeltBloc extends Bloc<ProductsActionBeltEvent, ProductsActionBeltState> {
  final IIdentityRepository identityRepository;
  final ILocalizationRepository localizationRepository;
  final IProductsActionBeltRepository productsActionBeltRepository;
  final IFeatureFlagRepository featureFlagRepository;
  final IUserRepository userRepository;
  final ICurrentUserRepository currentUserRepository;

  static const _defaultLanguage = 'en';

  StreamSubscription<CurrentUser?>? _userSubscription;
  StreamSubscription<SelectedLanguageModel>? selectedLanguageSubscription;

  StreamSubscription<void>? _refreshStreamSubscription;

  ProductsActionBeltBloc({
    required this.identityRepository,
    required this.localizationRepository,
    required this.productsActionBeltRepository,
    required this.featureFlagRepository,
    required this.userRepository,
    required this.currentUserRepository,
  }) : super(const ProductsActionBeltState()) {
    on<_Init>(_init);
    on<_UserChanged>(_userChanged);
    on<_LanguageChanged>(_languageChanged);
    on<_LoadWidgets>(_loadWidgets);
    on<_SubscribeToRefresh>(_subscribeToRefresh);
  }

  Future<void> _init(_Init event, Emitter<ProductsActionBeltState> emit) async {
    {
      final selectedLocale = await localizationRepository.loadSelectedLocale();
      final languageCode = selectedLocale?.languageCode ?? _defaultLanguage;

      // Used for PH shopingmall url
      final userCuid = await userRepository.userCuid();
      final userGuid = await currentUserRepository.userId();
      emit(state.copyWith(language: languageCode, userCuid: userCuid, userGuid: userGuid));
      await _userSubscription?.cancel();
      await identityRepository.streamIdentity()?.then(
            (stream) =>
                _userSubscription = stream.listen((user) => add(ProductsActionBeltEvent.userChanged(user: user))),
          );
      await selectedLanguageSubscription?.cancel();
      selectedLanguageSubscription = localizationRepository.selectedLanguageStream.listen(
        (model) => add(ProductsActionBeltEvent.languageChanged(language: model.localeInfo.languageCode)),
      );
    }
  }

  Future<void> _userChanged(_UserChanged e, Emitter<ProductsActionBeltState> emit) async {
    {
      if (e.user != null && e.user!.id != null && e.user!.id! != state.user?.id) {
        emit(state.copyWith(user: e.user, isLoading: true));
        add(ProductsActionBeltEvent.loadWidgets(userStatus: e.user!.signOnState ?? SignOnStatus.anonymous));
      }
    }
  }

  Future<void> _languageChanged(_LanguageChanged event, Emitter<ProductsActionBeltState> emit) async {
    emit(state.copyWith(language: event.language));
    add(ProductsActionBeltEvent.loadWidgets(userStatus: state.user!.signOnState ?? SignOnStatus.anonymous));
  }

  Future<void> _loadWidgets(_LoadWidgets event, Emitter<ProductsActionBeltState> emit) async {
    final response = await productsActionBeltRepository.getActionBeltItems(
      userStatus: event.userStatus,
      language: state.language ?? _defaultLanguage,
    );
    emit(
      response.fold(
        (l) => state.copyWith(isLoading: false, actionBeltWidgets: []),
        (r) => state.copyWith(isLoading: false, actionBeltWidgets: r, widgets: null),
      ),
    );
  }

  Future<void> _subscribeToRefresh(_SubscribeToRefresh event, Emitter<ProductsActionBeltState> emit) async {
    await _refreshStreamSubscription?.cancel();
    _refreshStreamSubscription = event.refreshStream.listen((event) {
      if (state.user != null && state.user!.signOnState != null) {
        add(ProductsActionBeltEvent.loadWidgets(userStatus: state.user!.signOnState!));
      }
    });
  }

  @override
  Future<void> close() {
    _userSubscription?.cancel();
    selectedLanguageSubscription?.cancel();
    _refreshStreamSubscription?.cancel();
    return super.close();
  }
}
