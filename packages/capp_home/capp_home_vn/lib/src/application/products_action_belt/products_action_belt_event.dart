part of 'products_action_belt_bloc.dart';

@freezed
class ProductsActionBeltEvent with _$ProductsActionBeltEvent {
  const factory ProductsActionBeltEvent.init({required bool useSegmentation}) = _Init;
  const factory ProductsActionBeltEvent.userChanged({required CurrentUser? user}) = _UserChanged;
  const factory ProductsActionBeltEvent.languageChanged({String? language}) = _LanguageChanged;
  const factory ProductsActionBeltEvent.loadWidgets({required SignOnStatus userStatus}) = _LoadWidgets;
  const factory ProductsActionBeltEvent.subscribeToRefresh({required Stream<void> refreshStream}) = _SubscribeToRefresh;
}
