part of 'sas_rtdm_banners_bloc.dart';

sealed class SasRtdmBannersState extends Equatable {
  const SasRtdmBannersState();

  const factory SasRtdmBannersState.initial() = SasRtdmBannersInitial;
  const factory SasRtdmBannersState.loading() = SasRtdmBannersLoading;

  const factory SasRtdmBannersState.success({
    required String correlationId,
    required List<UserSasRtdmBannersItem> rtdmBanners,
  }) = SasRtdmBannersSuccess;

  const factory SasRtdmBannersState.failure() = SasRtdmBannersFailure;

  @override
  List<Object?> get props => [];
}

final class SasRtdmBannersInitial extends SasRtdmBannersState {
  const SasRtdmBannersInitial();
}

final class SasRtdmBannersLoading extends SasRtdmBannersState {
  const SasRtdmBannersLoading();
}

final class SasRtdmBannersSuccess extends SasRtdmBannersState {
  final String correlationId;
  final List<UserSasRtdmBannersItem> rtdmBanners;
  const SasRtdmBannersSuccess({
    required this.correlationId,
    required this.rtdmBanners,
  });

  @override
  List<Object?> get props => [correlationId, rtdmBanners];
}

final class SasRtdmBannersFailure extends SasRtdmBannersState {
  const SasRtdmBannersFailure();
}
