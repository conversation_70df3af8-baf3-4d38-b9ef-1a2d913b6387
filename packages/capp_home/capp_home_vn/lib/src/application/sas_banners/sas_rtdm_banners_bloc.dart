import 'package:bloc/bloc.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:equatable/equatable.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../domain/sas_banners/models/user_sas_rtdm_banners_item.dart';
import '../../domain/sas_banners/repository/i_sas_rtdm_banners_repository.dart';

part 'sas_rtdm_banners_event.dart';
part 'sas_rtdm_banners_state.dart';

class SasRtdmBannersBloc extends Bloc<SasRtdmBannersEvent, SasRtdmBannersState> {
  final ISasRtdmBannersRepository bannerRepository;
  final GlobalTrackingProperties trackingProperties;
  final IFeatureFlagRepository featureFlagRepository;

  String? _sessionId;
  bool _useCache = false;
  final Duration _cacheStale = const Duration(days: 1);

  SasRtdmBannersBloc({
    required this.bannerRepository,
    required this.trackingProperties,
    required this.featureFlagRepository,
  }) : super(const SasRtdmBannersInitial()) {
    on<SasRtdmBannersInit>(_onInit);
    on<SasRtdmBannersRefresh>(_onRefresh);
  }

  Future<void> _onInit(SasRtdmBannersInit event, Emitter<SasRtdmBannersState> emit) async {
    emit(const SasRtdmBannersState.initial());

    // prepare cache
    _useCache = featureFlagRepository.isEnabledCached(FeatureFlag.sasRTDMHomeBannersCache);
    await _checkCacheConditions();

    // get session ID for request
    _sessionId = trackingProperties.sessionID;

    add(const SasRtdmBannersEvent.refresh());
  }

  Future<void> _onRefresh(SasRtdmBannersRefresh event, Emitter<SasRtdmBannersState> emit) async {
    emit(const SasRtdmBannersState.loading());

    final result = _useCache
        ? await bannerRepository.getBannersFromStorageOrRemote(_sessionId ?? '')
        : await bannerRepository.getBannersFromRemote(_sessionId ?? '');

    emit(
      result.fold(
        (l) => const SasRtdmBannersState.failure(),
        (r) => SasRtdmBannersState.success(
          correlationId: r.correlationId,
          rtdmBanners: r.items,
        ),
      ),
    );
  }

  Future<void> _checkCacheConditions() async {
    if (!_useCache) {
      //clear cache if not used
      return bannerRepository.clearCache();
    }

    final lastUpdate = await bannerRepository.getLastCacheUpdate();

    if (lastUpdate != null) {
      // check last date of update
      if (DateTime.now().subtract(_cacheStale).isAfter(lastUpdate)) {
        return bannerRepository.clearCache();
      }
    }
  }
}
