import 'package:capp_home_core/capp_home_core.dart';

import 'presentation/action_belt_items_builder.dart';

class CappHomeSettingsVn extends CappHomeSettings {
  const CappHomeSettingsVn()
      : super(
          accountMenuItems: const [AccountMenuItem.retrieveMyAccount, AccountMenuItem.warningZone],
          assistantIconItems: const [AssistantIconItem.chatbot],
          assistantChipItems: const [
            AssistantChipItem.repayment,
            AssistantChipItem.contact,
            AssistantChipItem.warningZone,
            AssistantChipItem.loanRegistration,
          ],
          actionBeltHasTitle: false,
          hasImprovedLoansScreen: false,
          buildActionBeltItems: buildActionBeltItems,
          blogsFinancialLiteracyCategoryId: '8f5d000f-ea87-4288-a328-80dcfb34950c',
          blogsPromoCategoryId: 'f1da6c1a-c463-46a6-9604-16d8b709da1b',
          blogsWarningZoneCategoryId: 'df68fc14-1f36-4c54-a722-5bdb3749cb15',
          showNearestPartnerShopButton: false,
          usesNewScoringBanner: true,
          animatedPendingActionIcon: 'packages/capp_ui/assets/animation/alarm_clock.flr',
        );
}
