import 'package:capp_api/capp_api.dart' as api;
import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' hide ContractStatus;
import 'package:selfcareapi/selfcareapi.dart';

import '../../domain/products_action_belt/i_products_action_belt_repository.dart';

class ProductsActionBeltRepository implements IProductsActionBeltRepository {
  final ProductActionBeltWidgetsApi productApi;
  final Logger logger;

  ProductsActionBeltRepository({
    required this.productApi,
    required this.logger,
  });

  @override
  Future<Either<ActionBeltFailure, List<UserActionBeltWidget>>> getActionBeltItems({
    required SignOnStatus userStatus,
    required String language,
  }) async {
    try {
      final response = await productApi.productActionBeltWidgetsGet(
        userStatus.toApi,
        language,
      );
      return right(response.toActionBeltWidgets());
    } on DioError catch (e, s) {
      logger.e('Dio Error in products action belt repository', e, s);
      return left(const ActionBeltFailure.unexpected());
    }
  }
}

extension on SignOnStatus {
  ProductActionBeltUserStatusDto get toApi {
    switch (this) {
      case SignOnStatus.anonymous:
        return ProductActionBeltUserStatusDto.guest;
      case SignOnStatus.client:
        return ProductActionBeltUserStatusDto.client;
      case SignOnStatus.registered:
        return ProductActionBeltUserStatusDto.registered;
    }
  }
}
