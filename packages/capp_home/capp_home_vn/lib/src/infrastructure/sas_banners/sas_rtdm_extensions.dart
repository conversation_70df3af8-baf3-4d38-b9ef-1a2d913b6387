import 'package:koyal_shared/koyal_shared.dart';
import 'package:selfcareapi/model/models.dart';

import '../../domain/sas_banners/models/user_sas_rtdm_banners.dart';
import '../../domain/sas_banners/models/user_sas_rtdm_banners_item.dart';

extension SasRtdmBannersExtensions on UserSasBannersResponse {
  UserSasRtdmBanners toDomain(ImageServiceBase imageService) {
    final sasItems = items
        ?.map(
          (item) => item.toDomain(imageService),
        )
        .toList();

    return UserSasRtdmBanners(
      correlationId: correlationId ?? '',
      items: sasItems ?? [],
    );
  }
}

extension SasRtdmBannersItemExtensions on UserSasBanner {
  UserSasRtdmBannersItem toDomain(ImageServiceBase imageService) {
    return UserSasRtdmBannersItem(
      id: id ?? '',
      title: title ?? '',
      bodyText: bodyText ?? '',
      imageUrl: imageService.getUrlFromId(imageId) ?? '',
      url: ctaButtonUrl ?? '',
      buttonText: ctaButtonText ?? '',
      sasId: sasId,
      segmentId: segmentId,
    );
  }
}
