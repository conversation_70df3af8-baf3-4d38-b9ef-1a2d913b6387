import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../domain/sas_banners/models/user_sas_rtdm_banners.dart';
import '../../domain/sas_banners/repository/i_sas_rtdm_banners_repository.dart';
import 'sas_rtdm_extensions.dart';

final class _StorageKeys {
  _StorageKeys._();

  static const String lastUpdateKey = 'sas_rtdm_last_update_key';
  static const String cachedRequestKey = 'sas_rtdm_cached_request';
}

final class SasRtdmBannersRepository implements ISasRtdmBannersRepository {
  final SasBannersApi bannersApi;
  final ImageServiceBase imageService;
  final GmaStorageProvider storage;
  final Logger l;

  SasRtdmBannersRepository({
    required this.bannersApi,
    required this.imageService,
    required this.storage,
    required this.l,
  });

  @override
  Future<Either<Unit, UserSasRtdmBanners>> getBannersFromRemote(String sessionId) async {
    final response = await _requestBanners(sessionId);

    return response != null ? right(response.toDomain(imageService)) : left(unit);
  }

  @override
  Future<void> clearCache() async {
    await storage.delete(_StorageKeys.cachedRequestKey);
    await storage.delete(_StorageKeys.lastUpdateKey);
  }

  @override
  Future<Either<Unit, UserSasRtdmBanners>> getBannersFromStorageOrRemote(String sessionId) async {
    // decide if we have data in cache
    if (await getLastCacheUpdate() == null) {
      final response = await _requestBanners(sessionId);
      // request failed
      if (response == null) return left(unit);
      // store response and it date
      await storage.insert(_StorageKeys.cachedRequestKey, UserSasBannerCacheItem(response: response));
      await storage.insert(_StorageKeys.lastUpdateKey, DateTime.now());

      return right(response.toDomain(imageService));
    }
    // we have something in cache so it can be returned if data are valid
    final result = await storage.get<UserSasBannerCacheItem?>(
      _StorageKeys.cachedRequestKey,
      fromMap: UserSasBannerCacheItem().fromMap,
    );

    return result?.response != null ? right(result!.response!.toDomain(imageService)) : left(unit);
  }

  @override
  Future<DateTime?> getLastCacheUpdate() {
    return storage.get<DateTime?>(_StorageKeys.lastUpdateKey);
  }

  Future<UserSasBannersResponse?> _requestBanners(String sessionId) async {
    try {
      return await bannersApi.sasBannersGet(sessionId, '');
    } on DioError catch (e) {
      l.wtf('Dio error in sas rtdm repository while fetching banners', e);
    } on Exception catch (e, s) {
      l.wtf('Unexpected error in sas rtdm repository while fetching banners', e, s);
    }
    return null;
  }
}

class UserSasBannerCacheItem extends StorageItemBase {
  UserSasBannersResponse? response;

  UserSasBannerCacheItem({this.response});

  @override
  StorageItemBase fromMap(JsonMap map) {
    // items must re-mapped from <dynamic, dynamic> to <String, dynamic>
    map['items'] = (map['items'] as List<dynamic>).map((x) => Map<String, dynamic>.from(x)).toList();

    return UserSasBannerCacheItem(response: UserSasBannersResponse.fromJson(map));
  }

  @override
  JsonMap toMap() {
    return response?.toJson() ?? <String, dynamic>{};
  }
}
