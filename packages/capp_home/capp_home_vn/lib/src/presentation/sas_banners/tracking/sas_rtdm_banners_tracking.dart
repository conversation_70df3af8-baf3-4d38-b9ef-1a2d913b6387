import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

mixin SasRtdmBannersTracking {
  // DAS062
  Future<void> trackBannerView(BuildContext context, String correlationID, String id, int index, int? segmentId) async {
    await context.get<CappTrackingService>().trackHomeDashboardEvent(
      event: KoyalEvent.homeDashboardSasBannerView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: 'sas_hero_banner',
      customDimensions: {
        TrackingProperties.propertyCdItemId: id,
        TrackingProperties.propertyCdSas: correlationID,
        TrackingProperties.propertyCdSpotId: '${index + 1}',
        TrackingProperties.propertyCdClientSegmentId: '${segmentId ?? ''}',
      },
    );
  }

  // DAS063
  Future<void> trackBannerClick(
    BuildContext context,
    String correlationID,
    String id,
    int index,
    int? segmentId,
  ) async {
    await context.get<CappTrackingService>().trackHomeDashboardEvent(
      event: KoyalEvent.homeDashboardSasBannerClick,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'sas_hero_banner',
      customDimensions: {
        TrackingProperties.propertyCdItemId: id,
        TrackingProperties.propertyCdSas: correlationID,
        TrackingProperties.propertyCdSpotId: '${index + 1}',
        TrackingProperties.propertyCdClientSegmentId: '${segmentId ?? ''}',
      },
    );
  }
}
