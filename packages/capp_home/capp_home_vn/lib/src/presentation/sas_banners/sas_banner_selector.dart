import 'package:capp_home_core/capp_home_core.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'sas_rtdm_banners.dart';

class SasBannerSelector extends StatelessWidget implements HomeScreenPadding {
  final bool useNewDesign;
  final bool isRtdmEnabled;
  final String? title;

  const SasBannerSelector({
    super.key,
    required this.useNewDesign,
    required this.isRtdmEnabled,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    if (isRtdmEnabled) {
      return SasRtdmBanners(
        title: useNewDesign ? title : null,
      );
    }
    // Originally here were SAS360 Banners
    context.read<HomePaddingProvider>().isVisible = false;
    return const SizedBox.shrink();
  }

  @override
  bool get disableRight => true;

  @override
  bool get isDisabled => !isRtdmEnabled;
}
