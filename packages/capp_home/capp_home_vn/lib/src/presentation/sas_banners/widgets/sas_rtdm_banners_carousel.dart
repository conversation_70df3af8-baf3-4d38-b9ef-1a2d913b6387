import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'sas_rtdm_banners_item.dart';

class SasRtdmBannersCarousel extends StatelessWidget {
  final List<SasRtdmBannersItem> banners;

  const SasRtdmBannersCarousel({
    super.key,
    required this.banners,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: banners
            .mapWithIndex<Widget>(
              (i, banner) => Padding(
                padding: EdgeInsets.only(
                  right: banners.length - 1 == i ? KoyalPadding.paddingNormal : KoyalPadding.paddingSmall,
                ),
                child: banner,
              ),
            )
            .toList(),
      ),
    );
  }
}
