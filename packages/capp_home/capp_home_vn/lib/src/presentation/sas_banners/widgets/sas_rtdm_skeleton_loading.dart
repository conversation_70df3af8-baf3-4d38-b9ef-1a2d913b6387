import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SasRtdmSkeletonLoading extends StatelessWidget {
  const SasRtdmSkeletonLoading({super.key});

  @override
  Widget build(BuildContext context) {
    context.read<HomePaddingProvider>().isVisible = true;
    return KoyalPadding.normalAll(
      left: false,
      child: KoyalShimmer(
        child: AspectRatio(
          aspectRatio: 328 / 200,
          child: Container(
            width: double.maxFinite,
            height: double.maxFinite,
            decoration: BoxDecoration(
              color: ColorTheme.of(context).backgroundColor,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
}
