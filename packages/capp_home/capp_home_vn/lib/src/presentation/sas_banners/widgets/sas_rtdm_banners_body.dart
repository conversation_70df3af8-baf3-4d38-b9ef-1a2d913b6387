import 'package:capp_domain/capp_domain.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:provider/provider.dart';

import '../../../domain/sas_banners/models/user_sas_rtdm_banners_item.dart';
import '../tracking/sas_rtdm_banners_tracking.dart';
import 'sas_rtdm_banners_carousel.dart';
import 'sas_rtdm_banners_empty.dart';
import 'sas_rtdm_banners_item.dart';

class SasRtdmBannersBody extends StatelessWidget with SasRtdmBannersTracking {
  final String? title;
  final String correlationID;
  final List<UserSasRtdmBannersItem> banners;

  const SasRtdmBannersBody({
    super.key,
    required this.banners,
    required this.title,
    required this.correlationID,
  });

  @override
  Widget build(BuildContext context) {
    if (banners.isEmpty) {
      return const SasRtdmBannersEmpty();
    }
    context.read<HomePaddingProvider>().isVisible = true;
    final mappedBanners = banners
        .map(
          (e) => SasRtdmBannersItem(
            id: e.id,
            sasId: e.sasId,
            width: _getWidth(context),
            title: e.title,
            bodyText: e.bodyText,
            imageUrl: e.imageUrl,
            btnUrl: e.url,
            btnText: e.buttonText,
            onBtnClick: (title, btnUrl, id, sasId) =>
                _onCtaButtonClick(context, title, btnUrl, id, sasId, banners.indexOf(e), e.segmentId),
            onBecomeVisible: (id, sasId) => _onItemBecameVisible(context, id, sasId, banners.indexOf(e), e.segmentId),
          ),
        )
        .toList();

    return KoyalPadding.normalAll(
      bottom: false,
      left: false,
      top: false,
      right: mappedBanners.length == 1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null && (title?.isNotEmpty ?? false)) _BannerTitle(title: title!),
          if (mappedBanners.length == 1) mappedBanners.first else SasRtdmBannersCarousel(banners: mappedBanners),
        ],
      ),
    );
  }

  void _onItemBecameVisible(BuildContext context, String id, int? sasId, int index, int? segmentId) {
    trackBannerView(context, correlationID, sasId?.toString() ?? id, index, segmentId);
  }

  void _onCtaButtonClick(
    BuildContext context,
    String title,
    String btnUrl,
    String id,
    int? sasId,
    int index,
    int? segmentId,
  ) {
    trackBannerClick(context, correlationID, sasId?.toString() ?? id, index, segmentId);
    context.get<IDeeplinkService>().deeplinkOrLaunch(btnUrl, context, linkTitle: title);
  }

  double _getWidth(BuildContext context) => MediaQuery.of(context).size.width * (banners.length > 1 ? 0.8 : 1);
}

class _BannerTitle extends StatelessWidget {
  final String title;

  const _BannerTitle({required this.title});
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 30,
      child: KoyalText.subtitle1(
        title,
        color: ColorTheme.of(context).defaultTextColor,
        textAlign: TextAlign.left,
      ),
    );
  }
}
