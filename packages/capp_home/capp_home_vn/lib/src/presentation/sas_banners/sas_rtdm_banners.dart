import 'package:capp_home_core/capp_home_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../application/sas_banners/sas_rtdm_banners_bloc.dart';
import 'widgets/sas_rtdm_banners_body.dart';
import 'widgets/sas_rtdm_banners_empty.dart';
import 'widgets/sas_rtdm_skeleton_loading.dart';

class SasRtdmBanners extends StatefulWidget {
  final String? title;
  const SasRtdmBanners({super.key, required this.title});

  @override
  State<SasRtdmBanners> createState() => _SasRtdmBannersState();
}

class _SasRtdmBannersState extends State<SasRtdmBanners> {
  @override
  void initState() {
    super.initState();
    context.get<SasRtdmBannersBloc>().add(const SasRtdmBannersEvent.init());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listenWhen: (previous, current) => current.isRefreshing && !previous.isRefreshing,
      listener: (context, state) {
        context.get<SasRtdmBannersBloc>().add(const SasRtdmBannersEvent.refresh());
      },
      child: BlocBuilder<SasRtdmBannersBloc, SasRtdmBannersState>(
        builder: (context, state) {
          return switch (state) {
            SasRtdmBannersFailure() => const SasRtdmBannersEmpty(),
            SasRtdmBannersSuccess() => SasRtdmBannersBody(
                title: widget.title,
                banners: state.rtdmBanners,
                correlationID: state.correlationId,
              ),
            _ => const SasRtdmSkeletonLoading(),
          };
        },
      ),
    );
  }
}
