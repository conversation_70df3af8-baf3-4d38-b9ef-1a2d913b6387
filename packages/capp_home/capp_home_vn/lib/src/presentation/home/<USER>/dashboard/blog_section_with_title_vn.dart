import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../revamp/blog_section/blog_section_grid.dart';

// Created for US 130692
// Copied original BlogPost widget with small changes
class BlogSectionWithTitleVn extends StatelessWidget {
  final int numberOfPosts;
  final String? title;
  const BlogSectionWithTitleVn({Key? key, this.title, required this.numberOfPosts}) : super(key: key);

  @override
  Widget build(BuildContext context) => BlogSectionGridRevamp(
    key: const Key('__homeScreenBlogSection__'),
    title: KoyalText.subtitle1(
      color: ColorTheme.of(context).defaultTextColor,
      title ?? L10nCappHome.of(context).tipsAndStories,
    ),
    isHomeDashboard: true,
    numberOfPosts: numberOfPosts,
  );
}
