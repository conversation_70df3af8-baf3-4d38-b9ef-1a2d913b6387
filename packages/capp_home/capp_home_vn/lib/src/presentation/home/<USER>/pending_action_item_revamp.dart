import 'dart:async';

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flare_flutter/flare_actor.dart';
import 'package:flare_flutter/flare_controls.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../widgets/home_screen_size.dart';

class PendingActionItemRevamp extends StatelessWidget {
  const PendingActionItemRevamp({
    super.key,
    required this.item,
    required this.onTap,
    this.animationIcon,
    required this.backgroundColor,
    required this.hasArrow,
    required this.showLockIcon,
  });

  final PendingActionModel item;

  final VoidCallback onTap;

  final String? animationIcon;

  final Color backgroundColor;

  final bool hasArrow;

  final bool showLockIcon;

  static double cardHeight(BuildContext context) => MediaQuery.sizeOf(context).width > 399 ? 58.0 : 52.0;

  @override
  Widget build(BuildContext context) {
    return HomeScreenSize(
      builder: (context, screenSize) {
        final iconSize = screenSize.isScreenOverLimit ? 34.0 : 30.0;
        return KoyalSemantics(
          customIdentifier: 'PendingAction',
          title: _title(context, item),
          child: InkWell(
            onTap: onTap,
            child: Container(
              height: cardHeight(context),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                color: backgroundColor,
              ),
              child: _PendingActionContent(
                icon: SizedBox(
                  width: iconSize,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 500),
                    child: showLockIcon
                        ? _PendingActionIcon(
                            animationIcon: animationIcon,
                            iconSize: iconSize,
                          )
                        : SvgPicture.asset(
                            item.icon ?? item.defaultIcon(),
                            package: item.iconPackage ?? item.defaultIconPackage,
                            width: iconSize,
                            height: iconSize,
                          ),
                    transitionBuilder: (child, animation) {
                      return RotationTransition(
                        turns: animation,
                        child: FadeTransition(
                          opacity: animation,
                          child: child,
                        ),
                      );
                    },
                  ),
                ),
                title: _title(context, item),
                hasArrow: hasArrow,
                isLargerScreenWidth: screenSize.isScreenOverLimit,
              ),
            ),
          ),
        );
      },
    );
  }

  String _title(BuildContext context, PendingActionModel item) {
    var title = '';
    if (item.type == PendingActionType.cuidPairing ||
        item.type == PendingActionType.identificationUpgradeToCustomer ||
        item.type == PendingActionType.setupDirectDebit) {
      title = item.title(L10nKoyalShared.of(context));
    } else {
      title = item.subtitle(L10nKoyalShared.of(context));
    }
    final infoText = item.infoText(L10nKoyalShared.of(context));
    if (infoText != null) {
      title += '\n$infoText';
    }
    return title;
  }
}

class _PendingActionContent extends StatelessWidget {
  const _PendingActionContent({
    required this.title,
    this.icon,
    required this.hasArrow,
    required this.isLargerScreenWidth,
  });

  final String title;

  final Widget? icon;

  final bool hasArrow;

  final bool isLargerScreenWidth;

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalAll(
      top: false,
      bottom: false,
      child: Row(
        children: [
          if (icon != null) icon!,
          Expanded(
            child: KoyalPadding.smallHorizontal(
              child: isLargerScreenWidth
                  ? KoyalText.body2(
                      title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      color: ColorTheme.of(context).infoIndicatorColor,
                    )
                  : KoyalText.caption2(
                      title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      color: ColorTheme.of(context).infoIndicatorColor,
                    ),
            ),
          ),
          AnimatedOpacity(
            opacity: hasArrow ? 1 : 0,
            duration: const Duration(milliseconds: 300),
            child: Icon(
              KoyalIcons.chevron_forward_outline,
              size: isLargerScreenWidth ? 24.0 : 16.0,
              color: ColorTheme.of(context).infoIndicatorColor,
            ),
          ),
        ],
      ),
    );
  }
}

class _PendingActionIcon extends StatefulWidget {
  const _PendingActionIcon({
    required this.animationIcon,
    required this.iconSize,
  });

  final String? animationIcon;
  final double iconSize;

  @override
  State<_PendingActionIcon> createState() => _PendingActionIconState();
}

class _PendingActionIconState extends State<_PendingActionIcon> {
  final FlareControls controls = FlareControls();
  Timer? _timer;
  static const _animationName = 'Untitled';

  @override
  void initState() {
    _timer = Timer.periodic(const Duration(seconds: 15), (t) {
      controls.play(_animationName);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.iconSize,
      height: widget.iconSize,
      child: FlareActor(
        widget.animationIcon,
        fit: BoxFit.cover,
        animation: 'Untitled',
        controller: controls,
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}
