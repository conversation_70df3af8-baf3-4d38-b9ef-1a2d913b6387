import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

mixin BlogPostTracking {
  void trackView(BuildContext context) {
    context.get<CappTrackingService>().trackEvent(
          event: KoyalEvent.homeDashboardBlogPostView,
          eventCategory: KoyalTrackingCategories.homeDashboard,
          eventAction: KoyalAnalyticsConstants.view,
          eventLabel: 'blogpost',
        );
  }

  void trackClick(BuildContext context) {
    context.get<CappTrackingService>().trackClickEvent(eventCategory: 'blog_click', eventLabel: 'blog');
  }

  void trackViewAllClick(BuildContext context) {
    context.get<CappTrackingService>().trackEvent(
          event: KoyalEvent.homeDashboardBlogPostViewAllClick,
          eventCategory: KoyalTrackingCategories.homeDashboard,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: 'blogpost_view_all',
        );
  }

  void trackBlogPostClick(
    BuildContext context, {
    required String? itemId,
    required int index,
  }) {
    context.get<CappTrackingService>().trackEvent(
      event: KoyalEvent.homeDashboardBlogPostClick,
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'blogpost',
      customDimensions: {
        TrackingProperties.propertyCdItemId: '$itemId',
        TrackingProperties.propertyCdItemIndex: '$index',
      },
    );
  }
}
