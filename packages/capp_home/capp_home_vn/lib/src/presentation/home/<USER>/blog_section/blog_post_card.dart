import 'package:capp_content/capp_content.dart' hide BlogPostImage;
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import 'blog_post_image.dart';
import 'blog_post_tracking.dart';

const _cardBorderRadius = BorderRadius.all(Radius.circular(8));

class BlogPostCardRevamp extends StatelessWidget with BlogPostTracking {
  final BlogSectionPost data;
  final void Function() onTap;

  const BlogPostCardRevamp({Key? key, required this.data, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) => ClipRRect(
        key: Key('__blogPostCard_${data.id}__'),
        borderRadius: _cardBorderRadius,
        child: Material(
          color: ColorTheme.of(context).backgroundColor,
          child: InkWell(
            onTap: () => _navigateToBlogDetails(context, data.id ?? ''),
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: _cardBorderRadius,
              ),
              child: _content(context),
              // child: _content(context),
            ),
          ),
        ),
      );

  Widget _content(BuildContext context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(child: BlogPostImageRevamp(url: data.thumbnailUrl ?? data.imageUrl)),
          Container(
            color: HciColors.supplementary25,
            child: KoyalPadding.small(
              child: KoyalText.subtitle2(
                maxLines: 2,
                data.title ?? '',
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      );

  void _navigateToBlogDetails(BuildContext context, String blogId) {
    context.navigator.pushFromPackage(
      package: 'CappContent',
      screen: 'BlogDetailsScreen',
      arguments: BlogDetailsArguments(blogId: data.id),
    );
    trackClick(context);
    onTap();
  }
}

// Loading Car

class BlogPostCardLoadingRevamp extends StatelessWidget {
  const BlogPostCardLoadingRevamp({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => KoyalShimmer(
        child: Container(
          decoration: BoxDecoration(
            color: ColorTheme.of(context).backgroundColor,
            border: Border.all(color: ColorTheme.of(context).foreground5Color),
            borderRadius: _cardBorderRadius,
          ),
        ),
      );
}
