import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'pending_action_item_revamp.dart';

class PendingActionsHeaderRevamp extends StatefulWidget {
  const PendingActionsHeaderRevamp({super.key});

  @override
  State<PendingActionsHeaderRevamp> createState() => _PendingActionsHeaderRevampState();
}

class _PendingActionsHeaderRevampState extends State<PendingActionsHeaderRevamp> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isAnimating = false;
  static const containerHeight = 86.0;
  static const cardSpacing = 8.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutQuart,
    );
  }

  @override
  Widget build(BuildContext context) {
    final homeSettings = context.get<CappHomeSettings>();
    return Stack(
      alignment: AlignmentDirectional.topCenter,
      children: [
        _buildBlackOverlay(),
        BlocBuilder<PendingActionsBloc, PendingActionsState>(
          builder: (context, state) {
            final items = state.items;
            if (items.isEmpty) return const SizedBox();
            final listWidgetData = _buildAnimationData(items);
            return CappVisibilityDetector(
              key: const ValueKey(
                '__homeScreenPedningActionsVisibilityDetector__',
              ),
              onBecameVisible: () {
                if (state.showPendingActionList) {
                  if (items.length > 1) {
                    _toggleAnimation();
                  }
                  context
                      .read<PendingActionsBloc>()
                      .add(const PendingActionsEvent.showPendingActionsList(isShowPendingList: false));
                }
                _pendingActionImpressionEvent(context, state.items);
              },
              onBecameInvisible: () {
                if (_isAnimating) {
                  _toggleAnimation();
                }
              },
              child: GestureDetector(
                onTap: items.length > 1 ? _toggleAnimation : null,
                child: AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    final maxCardInitialPosition = _getMaxInitialPosition(listWidgetData);
                    final maxHeight = containerHeight +
                        //additional cards height when expand sheet
                        (listWidgetData.length - 1) *
                            (PendingActionItemRevamp.cardHeight(context) + cardSpacing) *
                            _animation.value +
                        //the card over height when sheet collapsed
                        maxCardInitialPosition * (1 - _animation.value);
                    const parentContainerBottomPadding = 28.0;
                    const cardPadding = 16.0;
                    final screenHeight = MediaQuery.of(context).size.height;
                    final maxSheetHeight = screenHeight * 0.75;
                    return Stack(
                      alignment: AlignmentDirectional.bottomCenter,
                      children: [
                        ShadowClipPath(
                          clipper: _ConvexClipper(hasArrow: items.length > 1),
                          shadow: BoxShadow(
                            offset: const Offset(0, 2),
                            color: HciColors.supplementary400.withOpacity(0.25),
                            blurRadius: 8,
                          ),
                          child: Container(
                            height: maxHeight > maxSheetHeight ? maxSheetHeight : maxHeight,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(16),
                                bottomRight: Radius.circular(16),
                              ),
                            ),
                            padding: const EdgeInsets.only(
                              bottom: parentContainerBottomPadding,
                            ),
                            child: SingleChildScrollView(
                              child: Container(
                                height: maxHeight - (parentContainerBottomPadding + cardPadding),
                                margin: const EdgeInsets.only(
                                  left: cardPadding,
                                  right: cardPadding,
                                ),
                                alignment: Alignment.center,
                                child: Stack(
                                  alignment: AlignmentDirectional.center,
                                  clipBehavior: Clip.none,
                                  children: [
                                    ...listWidgetData.map(
                                      (cardData) {
                                        final itemTappable = items.length == 1 || _isAnimating;
                                        final shouldDisplayClockIcon = items.length == 1 || !_isAnimating;
                                        return Positioned(
                                          top: cardData.initialPosition +
                                              _calculateCardPosition(cardData) * _animation.value,
                                          child: Transform.scale(
                                            scale:
                                                cardData.initialScale + (1 - cardData.initialScale) * _animation.value,
                                            alignment: Alignment.topCenter,
                                            child: SizedBox(
                                              width: MediaQuery.of(context).size.width - cardPadding * 2,
                                              child: PendingActionItemRevamp(
                                                onTap: () => itemTappable
                                                    ? _onPendingActionTap(
                                                        context,
                                                        cardData.data,
                                                      )
                                                    : _toggleAnimation(),
                                                item: cardData.data,
                                                animationIcon: homeSettings.animatedPendingActionIcon,
                                                backgroundColor: _colorFromValue(
                                                  _animation.value,
                                                  cardData.originalColor,
                                                ),
                                                hasArrow: itemTappable,
                                                showLockIcon: shouldDisplayClockIcon,
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        if (items.length > 1) _buildArrowIcon(),
                      ],
                    );
                  },
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildBlackOverlay() {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        if (_isAnimating == false && _animation.value == 0) {
          return const SizedBox();
        }
        return GestureDetector(
          onTap: _toggleAnimation,
          child: Container(
            color: Colors.black.withOpacity(_animation.value * 0.5),
          ),
        );
      },
    );
  }

  Widget _buildArrowIcon() {
    return GestureDetector(
      onTap: _toggleAnimation,
      child: Container(
        padding: const EdgeInsets.only(left: 8, right: 8, top: 8),
        color: Colors.transparent,
        child: Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()..scale(1.0, 1.0 + -_animation.value * 2, 1.0),
          child: const Icon(
            Icons.keyboard_arrow_down_rounded,
            color: HciColors.primary500,
            size: 28,
          ),
        ),
      ),
    );
  }

  void _onPendingActionTap(BuildContext context, PendingActionModel item) {
    final productTypeInMetaData = _getProductTypeFromPA(item);
    final productType = productTypeInMetaData.isNotEmpty ? GaUtils.findProductType(productTypeInMetaData) : '';
    final loanType = productTypeInMetaData.isNotEmpty ? GaUtils.findLoanType(productTypeInMetaData) : '';

    //DAS032
    context.get<CappTrackingService>().trackAnalyticsEvent(
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'pending_Action',
      userPropertyMap: {
        TrackingProperties.propertyUserType: 'existing_user',
        TrackingProperties.propertyCdLoanType: loanType,
        TrackingProperties.propertyCdProductType: productType,
      },
    );

    //LOO124
    if (item.type == PendingActionType.transactionSignature || item.type == PendingActionType.loanApplication) {
      context.get<CappTrackingService>().trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.looPenAct,
        eventAction: KoyalAnalyticsConstants.click,
        eventLabel: KoyalTrackingLabels.tileCk,
        userPropertyMap: {
          TrackingProperties.propertyUserType: 'existing_user',
          TrackingProperties.propertyCdLoanType: loanType,
          TrackingProperties.propertyCdProductType: productType,
          TrackingProperties.propertyPropDynamicV1: item.title(L10nKoyalShared.of(context)),
        },
      );
    }

    //LOO150
    if (item.type == PendingActionType.loanApplication) {
      context.get<CappTrackingService>().trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.looWelcomeBack,
        eventAction: KoyalAnalyticsConstants.click,
        eventLabel: KoyalTrackingLabels.letsStart,
        userPropertyMap: {
          TrackingProperties.propertyUserType: 'existing_user',
          TrackingProperties.propertyCdLoanType: loanType,
          TrackingProperties.propertyCdProductType: productType,
        },
      );
    }
    if (item.type == PendingActionType.loanPending) {
      context.get<CappTrackingService>().trackAnalyticsEvent(
            eventCategory: KoyalTrackingLabels.looPendingAction,
            eventAction: KoyalAnalyticsConstants.click,
            eventLabel: item.title(L10nKoyalShared.of(context)),
            event: KoyalEvent.looPendingActionView,
          );
    }
    context.read<PendingActionsBloc>().add(PendingActionsEvent.open(item: item));
  }

  void _pendingActionImpressionEvent(
    BuildContext context,
    List<PendingActionModel> actions,
  ) {
    final productTypeInMetaData = _getProductTypeFromPA(actions.first);
    final productType = productTypeInMetaData.isNotEmpty ? GaUtils.findProductType(productTypeInMetaData) : '';
    final loanType = productTypeInMetaData.isNotEmpty ? GaUtils.findLoanType(productTypeInMetaData) : '';
    //DAS031
    context.get<CappTrackingService>().trackAnalyticsEvent(
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: 'pending_Action',
      userPropertyMap: {TrackingProperties.propertyUserType: 'existing_user'},
    );
    //EventID: LOO122
    if (actions.length == 1 && actions.first.type == PendingActionType.transactionSignature) {
      context.get<CappTrackingService>().trackAnalyticsEvent(
            eventCategory: KoyalTrackingCategories.screenView,
            eventAction: KoyalAnalyticsConstants.view,
            eventLabel: KoyalTrackingLabels.looPendingAction,
            userPropertyMap: {
              TrackingProperties.propertyUserType: 'existing_user',
              TrackingProperties.propertyCdLoanType: loanType,
              TrackingProperties.propertyCdProductType: productType,
            },
            event: KoyalEvent.looPendingActionView,
          );
    }
    //EventID: LOO149
    if (actions.length == 1 && actions.first.type == PendingActionType.loanApplication) {
      context.get<CappTrackingService>().trackAnalyticsEvent(
            eventCategory: KoyalTrackingCategories.screenView,
            eventAction: KoyalAnalyticsConstants.view,
            eventLabel: KoyalTrackingLabels.looWelcomeBack,
            userPropertyMap: {
              TrackingProperties.propertyUserType: 'existing_user',
            },
            event: KoyalEvent.looWelcomeBackView,
          );
    }
    if (actions.length == 1 && actions.first.type == PendingActionType.loanPending) {
      context.get<CappTrackingService>().trackAnalyticsEvent(
            eventCategory: KoyalTrackingCategories.screenView,
            eventAction: KoyalAnalyticsConstants.view,
            eventLabel: '${KoyalTrackingLabels.looPendingAction}_${actions.first.title(L10nKoyalShared.of(context))}',
            event: KoyalEvent.looPendingActionView,
          );
    }
    if (actions.length == 1 &&
        (actions.first.type == PendingActionType.transactionSignature ||
            actions.first.type == PendingActionType.loanApplication)) {
      // LOO123
      context.get<CappTrackingService>().trackAnalyticsEvent(
        eventCategory: KoyalTrackingCategories.looPenAct,
        eventAction: KoyalTrackingActions.view,
        eventLabel: KoyalTrackingLabels.tileVw,
        userPropertyMap: {
          TrackingProperties.propertyUserType: 'existing_user',
          TrackingProperties.propertyCdLoanType: loanType,
          TrackingProperties.propertyCdProductType: productType,
          TrackingProperties.propertyPropDynamicV1: actions.first.title(L10nKoyalShared.of(context)),
        },
      );
    }
  }

  String _getProductTypeFromPA(PendingActionModel? pendingActionModel) {
    if (pendingActionModel != null && pendingActionModel.metadata != null && pendingActionModel.metadata!.isNotEmpty) {
      return pendingActionModel.metadata!['productType'] ?? '';
    } else {
      return '';
    }
  }

  Color _colorFromValue(double value, Color originalColor) {
    return Color.lerp(originalColor, HciColors.secondary50, value) ?? HciColors.secondary50;
  }

  List<_PendingActionAnimationData> _buildAnimationData(
    List<PendingActionModel> items,
  ) {
    final listData = <_PendingActionAnimationData>[];
    var initialPosition = 0.0;
    for (var i = items.length - 1; i >= 0; i--) {
      final item = items[i];
      if (i == 0) {
        listData.add(
          _PendingActionAnimationData(
            initialPosition: initialPosition,
            initialScale: 1.0,
            index: i,
            originalColor: HciColors.secondary50,
            data: item,
          ),
        );
      } else if (i == 1) {
        listData.add(
          _PendingActionAnimationData(
            initialPosition: initialPosition,
            initialScale: 0.9875,
            index: i,
            originalColor: HciColors.secondary100,
            data: item,
          ),
        );
        initialPosition += 4;
      } else {
        listData.add(
          _PendingActionAnimationData(
            initialPosition: initialPosition,
            initialScale: 0.975,
            index: i,
            originalColor: HciColors.secondary200,
            data: item,
          ),
        );
        if (initialPosition == 0) {
          initialPosition += 2;
        }
      }
    }
    return listData;
  }

  void _toggleAnimation() {
    setState(() {
      if (_isAnimating) {
        _controller.reverse();
      } else {
        _controller.forward();
      }
      _isAnimating = !_isAnimating;
    });
  }

  double _getMaxInitialPosition(
    List<_PendingActionAnimationData> listAnimationData,
  ) {
    var max = 0.0;
    for (final item in listAnimationData) {
      if (item.initialPosition > max) {
        max = item.initialPosition;
      }
    }
    return max;
  }

  double _calculateCardPosition(_PendingActionAnimationData value) {
    final distanceToZero = -value.initialPosition;
    final targetDistance = value.index * (PendingActionItemRevamp.cardHeight(context) + cardSpacing);
    return targetDistance + distanceToZero;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

class _PendingActionAnimationData {
  final double initialPosition;
  final double initialScale;
  final int index;
  final Color originalColor;
  final PendingActionModel data;

  _PendingActionAnimationData({
    required this.initialPosition,
    required this.initialScale,
    required this.index,
    required this.originalColor,
    required this.data,
  });
}

class _ConvexClipper extends CustomClipper<Path> {
  final bool hasArrow;

  _ConvexClipper({
    required this.hasArrow,
  });

  @override
  Path getClip(Size size) {
    final arrowWidth = size.width / 5.2, arrowHeight = size.width / 22.5, radius = 0.5;
    final path = Path();
    final rect = Rect.fromLTWH(0, 0, size.width, size.height - arrowHeight);
    if (hasArrow) {
      //draw the rect
      path
        ..addRRect(
          RRect.fromRectAndCorners(
            rect,
            bottomLeft: const Radius.circular(16),
            bottomRight: const Radius.circular(16),
          ),
        )
        //move to the start point to start draw bezel curve
        ..moveTo(
          rect.bottomRight.dx - (size.width - arrowWidth) / 2,
          rect.bottomCenter.dy,
        )
        //start drawing the curve
        ..relativeQuadraticBezierTo(
          ((-arrowWidth / 2) + (arrowWidth / 6)) * (1 - radius),
          0,
          -arrowWidth / 2 * radius,
          arrowHeight * radius,
        )
        ..relativeQuadraticBezierTo(
          -arrowWidth / 6 * radius,
          arrowHeight * (1 - radius),
          -arrowWidth / 2 * (1 - radius),
          arrowHeight * (1 - radius),
        )
        ..relativeQuadraticBezierTo(
          ((-arrowWidth / 2) + (arrowWidth / 6)) * (1 - radius),
          0,
          -arrowWidth / 2 * (1 - radius),
          -arrowHeight * (1 - radius),
        )
        ..relativeQuadraticBezierTo(
          -arrowWidth / 6 * radius,
          -arrowHeight * radius,
          -arrowWidth / 2 * radius,
          -arrowHeight * radius,
        );
    } else {
      path.addRRect(
        RRect.fromRectAndCorners(
          rect,
        ),
      );
    }
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}
