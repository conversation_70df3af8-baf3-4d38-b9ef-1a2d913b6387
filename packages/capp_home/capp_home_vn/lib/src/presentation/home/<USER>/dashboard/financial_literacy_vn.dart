import 'package:capp_content/capp_content.dart';
import 'package:capp_content_core/capp_content_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class FinancialLiteracyVn extends StatefulWidget {
  final String? title;
  final bool navigateToTab;
  final int numberOfPost;

  const FinancialLiteracyVn({
    Key? key,
    this.title,
    required this.navigateToTab,
    required this.numberOfPost,
  }) : super(key: key);

  @override
  State<FinancialLiteracyVn> createState() => _FinancialLiteracyVnState();
}

class _FinancialLiteracyVnState extends State<FinancialLiteracyVn> {
  @override
  Widget build(BuildContext context) => BlocBuilder<BlogSectionBloc, BlogSectionState>(
        builder: (context, state) => state.hasError ? _buildEmpty(context) : _buildBody(context, state),
      );

  Widget _buildBody(BuildContext context, BlogSectionState state) {
    context.read<HomePaddingProvider>().isVisible = true;
    return Padding(
      padding: const EdgeInsets.only(right: KoyalPadding.paddingNormal),
      child: Column(
        children: [
          _buildHeader(state.financialLiteracyCategoryId),
          Column(
            children: state.isLoading
                ? _buildBlogPostsLoading()
                : _buildBlogPosts(
                    data: state.financialLiteracyBlogPosts,
                    categories: state.categories,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmpty(BuildContext context) {
    context.read<HomePaddingProvider>().isVisible = false;
    return const SizedBox.shrink();
  }

  Widget _buildHeader(String financialLiteracyCategoryId) {
    return SizedBox(
      height: 40,
      child: Row(
        children: [
          Expanded(
            child: KoyalText.subtitle1(
              color: ColorTheme.of(context).defaultTextColor,
              widget.title ?? L10nCappHome.of(context).financialLiteracy,
            ),
          ),
          TertiaryButton(
            key: const Key('__blogSectionViewAllButton__'),
            text: L10nCappContent.of(context).viewAll,
            padding: EdgeInsets.zero,
            onPressed: () {
              context.get<CappTrackingService>().trackHomeDashboardEvent(
                    event: KoyalEvent.homeDashboardFinanceTipsViewAllClick,
                    eventAction: KoyalAnalyticsConstants.click,
                    eventLabel: KoyalTrackingLabels.financeTipsViewAll,
                  );
              final args = BlogsArguments(
                initialCategoryId: financialLiteracyCategoryId,
              );
              if (widget.navigateToTab) {
                context.navigator.toMainScreen(
                  arguments: MainScreenArguments(
                    initialTab: TabItem.promo,
                    arguments: args,
                  ),
                );
              } else {
                context.navigator.pushFromPackage(
                  package: 'CappContent',
                  screen: 'BlogsScreen',
                  arguments: args,
                );
              }
            },
          ),
        ],
      ),
    );
  }

  List<Widget> _buildBlogPostsLoading() {
    return Iterable<int>.generate(widget.numberOfPost)
        .map<Widget>((_) => const BlogPostCardLoadingRevamp())
        .intersperse(const SizedBox(height: KoyalPadding.paddingSmall))
        .toList();
  }

  List<Widget> _buildBlogPosts({
    required List<BlogSectionPost> data,
    EnumerationList? categories,
  }) =>
      (data.length > widget.numberOfPost ? data.sublist(0, widget.numberOfPost) : data)
          .mapWithIndex<Widget>(
            (index, item) => CappVisibilityDetector(
              key: Key('FinancialLiteracyVisibilityDetector_${item.id}'),
              onBecameVisible: () {
                context.get<CappTrackingService>().trackHomeDashboardEvent(
                  event: KoyalEvent.homeDashboardFinancialLiteracyItemView,
                  eventAction: KoyalAnalyticsConstants.view,
                  eventLabel: KoyalTrackingLabels.financeTips,
                  customDimensions: {
                    TrackingProperties.propertyCdItemId: item.id.toString(),
                  },
                );
              },
              child: BlogPostCardVerticalRevamp(
                data: item,
                onTap: () {
                  context.get<CappTrackingService>().trackHomeDashboardEvent(
                    event: KoyalEvent.homeDashboardFinancialLiteracyItemClick,
                    eventAction: KoyalAnalyticsConstants.click,
                    eventLabel: KoyalTrackingLabels.financeTips,
                    customDimensions: {
                      TrackingProperties.propertyCdItemId: item.id.toString(),
                    },
                  );
                  context.navigator.pushFromPackage(
                    package: 'CappContent',
                    screen: 'BlogDetailsScreen',
                    arguments: BlogDetailsArguments(blogId: item.id),
                  );
                },
              ),
            ),
          )
          .intersperse(const SizedBox(height: KoyalPadding.paddingSmall))
          .toList();
}
