import 'package:capp_content/capp_content.dart' hide BlogPostCardLoadingRevamp;
import 'package:capp_content_core/capp_content_core.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'blog_post_card.dart';
import 'blog_post_tracking.dart';

class BlogSectionGridRevamp extends StatelessWidget with BlogPostTracking {
  final Widget title;
  final bool showViewAllButton;
  final bool showLoading;
  final bool isHomeDashboard;
  final int numberOfPosts;

  const BlogSectionGridRevamp({
    Key? key,
    required this.title,
    this.showViewAllButton = true,
    this.showLoading = true,
    this.isHomeDashboard = false,
    required this.numberOfPosts,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => BlocBuilder<BlogSectionBloc, BlogSectionState>(
        builder: (context, state) => state.hasError
            ? _buildEmpty(context)
            : _isWidgetVisible(context, state)
                ? CappVisibilityDetector(
                    key: const Key('BlogSectionVisibilityDetector'),
                    onBecameVisible: () {
                      if (isHomeDashboard) {
                        trackView(context);
                      }
                    },
                    child: Column(
                      children: [
                        SizedBox(
                          height: 40,
                          child: Row(
                            children: [
                              Expanded(child: title),
                              if (showViewAllButton)
                                TertiaryButton(
                                  key: const Key('__blogSectionViewAllButton__'),
                                  text: L10nCappContent.of(context).viewAll,
                                  padding: EdgeInsets.zero,
                                  onPressed: () {
                                    if (isHomeDashboard) {
                                      trackViewAllClick(context);
                                    }
                                    context.navigator.pushFromPackage(package: 'CappContent', screen: 'BlogsScreen');
                                  },
                                ),
                            ],
                          ),
                        ),
                        GridView.count(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisSpacing: KoyalPadding.paddingSmall,
                          mainAxisSpacing: KoyalPadding.paddingSmall,
                          crossAxisCount: 2,
                          childAspectRatio: 160 / 144, // this ratio is based on figma design
                          children: state.isLoading ? _blogPostsLoading : _blogPosts(context, data: state.blogPosts),
                        ),
                      ],
                    ),
                  )
                : _buildEmpty(context),
      );

  bool _isWidgetVisible(BuildContext context, BlogSectionState state) {
    final isVisible = showLoading || (!state.isLoading && state.blogPosts.isNotEmpty);
    context.read<HomePaddingProvider>().isVisible = isVisible;
    return isVisible;
  }

  List<Widget> get _blogPostsLoading =>
      Iterable<int>.generate(numberOfPosts).map<Widget>((_) => const BlogPostCardLoadingRevamp()).toList();

  List<Widget> _blogPosts(BuildContext context, {required List<BlogSectionPost> data}) =>
      // max 4 blog post should be visible
      (data.length > numberOfPosts ? data.sublist(0, numberOfPosts) : data)
          .mapWithIndex<Widget>(
            (index, item) => BlogPostCardRevamp(
              data: item,
              onTap: () {
                if (isHomeDashboard) {
                  trackBlogPostClick(context, itemId: item.id, index: index);
                }
              },
            ),
          )
          .toList();

  Widget _buildEmpty(BuildContext context) {
    context.read<HomePaddingProvider>().isVisible = false;
    return const SizedBox.shrink();
  }
}
