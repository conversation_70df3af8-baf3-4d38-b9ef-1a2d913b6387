import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

// Just wrapper, following same split as is in CAPP content
class BlogPostImageRevamp extends StatelessWidget {
  final String? url;

  const BlogPostImageRevamp({
    this.url,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return url.asImage(
      cache: true,
      showShimmer: true,
      useOptimized: true,
    );
  }
}

class BlogPostImageLoadingRevamp extends StatelessWidget {
  const BlogPostImageLoadingRevamp({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => GreyShimmer(
        child: Container(color: ColorTheme.of(context).backgroundColor),
      );
}
