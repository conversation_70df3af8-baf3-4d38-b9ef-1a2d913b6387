import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_personal_core/capp_personal_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

class HomeAppBarRevamp extends KoyalAppBar {
  static const double defaultBarHeight = 56;

  HomeAppBarRevamp({
    Key? key,
    List<Widget>? actions,
    final List<ListMenuItem> moreActionsMenuItems = const [],
    double barHeight = defaultBarHeight,
    double bottomHeight = 0,
    bool showBottomDivider = true,
  }) : super(
          key: key,
          actions: actions,
          moreActionsMenuItems: moreActionsMenuItems,
          barHeight: barHeight,
          bottomHeight: bottomHeight,
          showBottomDivider: showBottomDivider,
        );

  @override
  State<HomeAppBarRevamp> createState() => _HomeAppBarState();
}

class _HomeAppBarState extends State<HomeAppBarRevamp> with TickerProviderStateMixin {
  late Color iconColor;
  late AppBarTheme appBarTheme;
  late TextButtonThemeData textButtonColor;

  static const _iconSize = 24.0;

  final startTextButtonColor = TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: HciColors.supplementary0,
      textStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
    ),
  );

  @override
  void initState() {
    super.initState();
    appBarTheme = AppBarTheme(
      elevation: 0,
      backgroundColor: HciColors.supplementary0,
      foregroundColor: HciColors.supplementary0,
      iconTheme: const IconThemeData(color: HciColors.supplementary0, size: _iconSize),
      actionsIconTheme: const IconThemeData(color: HciColors.supplementary0, size: _iconSize),
      systemOverlayStyle: getDarkUiOverlayStyle(),
    );
    textButtonColor = startTextButtonColor;
    iconColor = HciColors.primary500;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => context.get<PersonalDetailsBloc>()..add(const PersonalDetailsEvent.load()),
      child: BlocBuilder<PendingActionsBloc, PendingActionsState>(
        builder: (context, state) {
          return Theme(
            data: Theme.of(context).copyWith(
              appBarTheme: appBarTheme,
              textButtonTheme: textButtonColor,
            ),
            child: AppBar(
              toolbarHeight: 50,
              leadingWidth: 200,
              elevation: 0,
              surfaceTintColor: Colors.transparent,
              scrolledUnderElevation: state.items.isEmpty ? 2.0 : 0.0,
              shadowColor: HciColors.supplementary400.withOpacity(0.25),
              leading: BlocBuilder<PersonalDetailsBloc, PersonalDetailsState>(
                builder: (context, userDetailState) {
                  var leadingText = L10nCappHome.of(context).welcome;
                  final userInfo = userDetailState.info;
                  if (userInfo.firstName?.isEmpty == false) {
                    leadingText = L10nCappHome.of(context).welcomeUser(userInfo.firstName);
                  }
                  return Container(
                    alignment: Alignment.centerLeft,
                    child: KoyalPadding.normalAll(
                      right: false,
                      bottom: false,
                      top: false,
                      child: KoyalText.button1(
                        leadingText,
                        color: HciColors.primary500,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                  );
                },
              ),
              actions: [
                ConstrainedBox(
                  constraints: const BoxConstraints(minWidth: _iconSize * 2),
                  child: context.watch<HomeBloc>().state.userProfileModel?.isAnonymous ?? true
                      ? const SignInAction(textColor: HciColors.primary500)
                      : AuthorizedAction(
                          iconColor: iconColor,
                        ),
                ),
                if (widget.moreActionsMenuItems.isNotEmpty) ...[
                  IconButton(
                    key: const Key('__KoyalAppBarMoreActionsButton__'),
                    icon: Icon(
                      KoyalIcons.ellipsis_horizontal_solid,
                      size: _iconSize,
                      color: iconColor,
                    ),
                    onPressed: () => showKoyalBottomSheet<String>(
                      context: context,
                      builder: (context) => MenuListView(items: widget.moreActionsMenuItems),
                    ),
                    constraints: const BoxConstraints(maxWidth: _iconSize * 2),
                  ),
                  const SizedBox(width: 4.0),
                ],
              ],
            ),
          );
        },
      ),
    );
  }
}
