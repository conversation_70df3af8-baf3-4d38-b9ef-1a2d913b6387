import 'package:capp_home_core/capp_home_core.dart';
import 'package:flutter/material.dart';

import '../widgets/dashboard/blog_section_with_title_vn.dart';

// Simple wrapping widget to improve readability
class BlogsRevampSelector extends StatelessWidget implements HomeScreenPadding {
  final bool isRevampSelected;
  final String? title;
  final int numberOfPosts;

  const BlogsRevampSelector({
    super.key,
    required this.isRevampSelected,
    required this.numberOfPosts,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return isRevampSelected
        ? BlogSectionWithTitleVn(
            title: title,
            numberOfPosts: numberOfPosts,
          )
        : BlogSectionWithTitle(
            title: title,
            numberOfPosts: numberOfPosts,
          );
  }

  @override
  bool get disableRight => false;

  @override
  bool get isDisabled => false;
}
