import 'package:capp_domain/capp_domain.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

/// Vietnamese new design action belt
/// Only this one has header and can have colored background (other ABs doesnt have background)
/// It can have these layouts: Row (3-4) items, Column (1-2) items, Horizontal ListView with 4.5 items visible (5+)
/// This one does not feature Grid layout
class HSActionBeltVnNewDesign extends HSActionBelt {
  const HSActionBeltVnNewDesign({
    super.key,
    super.title,
    super.itemWidth,
    super.iconWidth,
    super.useLargeText,
    super.rowIconWidth,
  });

  @override
  bool get useNewDesign => true;

  // Main build function is in parent
  @override
  Widget buildBody(BuildContext context) {
    return BlocProvider<ActionBeltBloc>.value(
      value: context.get<ActionBeltBloc>(),
      child: <PERSON><PERSON>uilder<ActionBeltBloc, ActionBeltState>(
        builder: (context, state) {
          if (state.isLoading || state.actionBeltWidgets == null) return buildShimmering();

          final showAction = state.actionBeltWidgets!.length > 4;

          return Column(
            children: [
              if (title != null || showAction) ...[
                buildHeader(
                  context,
                  state.actionBeltWidgets!,
                  state.userGuid,
                  state.userCuid,
                  showAction: showAction,
                ),
                const SizedBox(height: KoyalPadding.paddingSmall),
              ],
              buildCountrySpecificAB(context, state.actionBeltWidgets!, title, state.userCuid, state.userGuid),
            ],
          );
        },
      ),
    );
  }

  Widget buildHeader(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? userGuid,
    String? userCuid, {
    required bool showAction,
  }) {
    return KoyalPadding.normalAll(
      bottom: false,
      top: false,
      left: false,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          KoyalText.subtitle1(
            title ?? '',
            color: ColorTheme.of(context).defaultTextColor,
            textAlign: TextAlign.left,
          ),
          if (showAction)
            TertiaryButton(
              key: const Key('__actionBeltViewAllButtonTitle__'),
              text: '${L10nCappHome.of(context).viewAll} (${items.length})',
              padding: EdgeInsets.zero,
              onPressed: () {
                trackClickEvent(context, null);
                context.navigator.pushFromPackage(
                  package: 'CappHome',
                  screen: 'HSActionBeltAllScreen',
                  arguments: HSActionBeltAllScreenArguments(
                    items: items,
                    userGuid: userGuid,
                    userCuid: userCuid,
                    useNewDesign: true,
                    sizeParams: HSActionBeltSizeParams(
                      itemWidth: itemWidth,
                      iconWidth: iconWidth,
                      useLargeText: useLargeText,
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  @override
  Widget buildCountrySpecificAB(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? title,
    String? userCuid,
    String? userGuid,
  ) {
    final numOfItems = items.length;
    if (numOfItems < 3) {
      return buildColumnActionBelt(context, items, userCuid, userGuid);
    } else if (numOfItems > 2 && numOfItems < 5) {
      return buildRowActionBelt(context, items, userCuid, userGuid);
    } else {
      // numOfItems > 4
      return buildListViewActionBelt(context, items, userCuid, userGuid);
    }
  }
}
