// ignore_for_file: use_build_context_synchronously
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_credolab/capp_credolab.dart';
import 'package:capp_datascore_core/capp_datascore_core.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_legal_permission/capp_legal_permission.dart';
import 'package:capp_legal_permission_core/capp_legal_permission_core.dart';
import 'package:capp_ui_core/widgets/uncategorized/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigator/navigator.dart';

class MainGuardListener extends BlocListener<MainGuardCubit, MainGuardState> {
  final List<Future<void>> additionalChecks;
  final bool? checkForMaintenance;

  MainGuardListener({
    Key? key,
    this.additionalChecks = const [],
    this.checkForMaintenance = false,
  }) : super(
          key: key,
          listener: (context, state) async {
            state as MainGuardStateLoaded;

            final futures = <Future>[];

            if (state.maintenanceWindow?.isScheduled == true && checkForMaintenance == true) {
              futures.add(
                context.navigator.pushReplacementTyped(
                  package: KoyalShared,
                  screen: ScheduledMaintenanceScreen,
                  arguments: MaintenanceScreenArguments(
                    startDate: state.maintenanceWindow?.maintenanceInit,
                    endDate: state.maintenanceWindow?.maintenanceFinish,
                    navigateNext: (context) => context.navigator.toMainScreen(
                      transition: KoyalNavigatorTransition.zoomFadeIn,
                    ),
                  ),
                ),
              );
            }

            if (state.secondIdSettingRequired) {
              futures.add(
                context.navigator.push(
                  path: NavigatorPath.cappAuth.changeSecondIdNonHoselScreen,
                  arguments: SecondIdNonHoselSetScreenArguments(
                    allowGoBack: false,
                    isShowAgreement: false,
                    usernameVerificationSessionId: '',
                  ),
                ),
              );
            }

            if (state.passwordChangeRequired) {
              futures.add(context.navigateToChangePassword(canGoBack: false));
            }

            if (state.showLegal == true) {
              context
                  .read<LegalPermissionBloc>()
                  .add(const LegalPermissionEvent.setShouldCollectBigData(shouldCollect: false));

              futures.add(
                context.navigator.pushFromPackage(
                  package: 'CappLegalPermission',
                  screen: 'LegalConsentScreen',
                  arguments: LegalConsentArguments(
                    eventCode: EventCodeConstant.regular,
                    requestType: DatascoreRequestType.homepage,
                    consentTitle: state.consentTitle,
                    consentData: state.consentData,
                    consentVersion: state.consentVersion,
                    bodyShimmer: const SkeletonLoading(),
                  ),
                ),
              );
            }

            if (state.showPermissions == true) {
              context
                  .read<LegalPermissionBloc>()
                  .add(const LegalPermissionEvent.setShouldCollectBigData(shouldCollect: false));

              futures.add(
                context.navigator.pushFromPackage(
                  package: 'CappLegalPermission',
                  screen: 'PermissionScreen',
                  arguments: LegalConsentArguments(
                    eventCode: EventCodeConstant.regular,
                    requestType: DatascoreRequestType.homepage,
                  ),
                ),
              );
            }

            if (context.mounted && context.isRegistered<LegalPermissionBloc>()) {
              final shouldCollectBigData = (await context.read<LegalPermissionBloc>().shouldCollectBigData()) ?? true;
              if (!context.read<MainGuardCubit>().isAnonymous &&
                  state.showLegal == false &&
                  state.showPermissions == false &&
                  shouldCollectBigData) {
                context.get<CredolabBloc>().add(
                      const CredolabEvent.collectData(skipRequestPermission: true),
                    );
                context
                    .read<LegalPermissionBloc>()
                    .add(const LegalPermissionEvent.setShouldCollectBigData(shouldCollect: false));
              }
            }

            final status = await context.get<IFeedbackJourneyService>().getJourneyToComplete(userId: state.userId);
            if (status != null) {
              futures.add(
                Future.delayed(const Duration(seconds: 1), () {
                  showFeedbackOverlay(
                    KoyalModular.of(context).rootNavigatorKey.currentState!.overlay!.context,
                    journeyId: status.journeyId!,
                  );
                }),
              );
            }

            for (final f in futures) {
              await f;
            }

            for (final f in additionalChecks) {
              await f;
            }

            // finish the guards processing and continue to home
            if (context.mounted) {
              context.read<MainGuardCubit>().finish();
            }
          },
          listenWhen: (previous, current) => current is MainGuardStateLoaded,
        );
}
