// ignore_for_file: use_build_context_synchronously

import 'package:app_settings/app_settings.dart';
import 'package:capp_bio_signature/capp_bio_signature.dart';
import 'package:capp_home_core/capp_home_core.dart' as capp_home_core;
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigator/navigator.dart';

class SettingsMenuScreen extends capp_home_core.SettingsMenuScreen {
  final capp_home_core.SettingsMenuArguments? arguments;

  const SettingsMenuScreen({
    super.key,
    super.requireFaceGuardChangePassword,
    this.arguments,
  });

  @override
  ListMenuItem buildBiometricSettingItem({
    required capp_home_core.SettingsMenuArguments? arguments,
    required BuildContext context,
  }) {
    return ListMenuItem(
      title: capp_home_core.L10nCappHome.of(context).menuSettingsBiometricSetup,
      icon: Icon(
        KoyalIcons.scan_qr_outline,
        color: ColorTheme.of(context).primaryColor,
      ),
      onTap: (context) async {
        try {
          await context.get<IBioSignatureService>().availableBiometric();
          await context.navigator.pushNamed(NavigatorPath.cappAuth.biometricSetupScreen);
        } on BioSignatureAvailableException catch (e) {
          if (e.type == BioSignatureAvailableFailureType.unexpected) {
            _showNoBiometricAvailableDialog(context);
          }
        }
      },
    );
  }

  @override
  List<ListMenuItem> buildMenuItems(
    BuildContext context,
    capp_home_core.AccountMenuLoadSuccess state, {
    required bool showChangePassword,
    required bool showLanguageSelection,
    required bool showPreferredCommunication,
    required bool showDisableUser,
    capp_home_core.SettingsMenuArguments? arguments,
    String? passwordSessionId,
  }) {
    return super.buildMenuItems(
      context,
      state,
      showChangePassword: showChangePassword,
      showLanguageSelection: showLanguageSelection,
      showPreferredCommunication: showPreferredCommunication,
      showDisableUser: showDisableUser,
      arguments: this.arguments,
      passwordSessionId: passwordSessionId,
    );
  }

  void _showNoBiometricAvailableDialog(BuildContext context) {
    context.get<CappTrackingService>().trackViewEvent(
          eventCategory: KoyalTrackingCategories.settingsMenu,
          eventLabel: KoyalTrackingLabels.popupBioDisabled,
          event: KoyalEvent.settingsMenuNoBiometricAvailableDialogView,
        );
    showKoyalOverlay<bool>(
      context,
      key: const Key('__BiometricNoBiometricAvailableDialog__'),
      title: capp_home_core.L10nCappHome.of(context).menuBiometricUnavailableDialogTitle,
      body: KoyalText.body2(
        capp_home_core.L10nCappHome.of(context).menuBiometricUnavailableDialogSubtitle,
        color: ColorTheme.of(context).foreground60Color,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (context) => PrimaryButton(
        text: capp_home_core.L10nCappHome.of(context).menuBiometricUnavailableDialogSettings,
        onPressed: () {
          context.get<CappTrackingService>().trackClickEvent(
                eventCategory: KoyalTrackingCategories.settingsMenu,
                eventLabel: KoyalTrackingLabels.popupBioDisabledSet,
                event: KoyalEvent.settingsMenuNoBiometricAvailableDialogViewButtonClick,
              );
          AppSettings.openAppSettings(type: AppSettingsType.security);
          context.navigator.pop();
        },
      ),
      tertiaryButtonBuilder: (c) => TertiaryButton(
        text: capp_home_core.L10nCappHome.of(context).menuBiometricUnavailableDialogCancel,
        onPressed: () => context.navigator.pop(),
      ),
    );
  }
}
