import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_transactions/capp_transactions.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

List<ActionBeltItemConfig> buildActionBeltItems({
  required BuildContext context,
  List<String> widgetIds = const [],
  CurrentUser? user,
  String? language,
  bool returnAllExistingItems = false,
}) {
  final ids = returnAllExistingItems ? _allWidgetIds : widgetIds;

  return ids
      .map((i) => _getItemById(context: context, id: i, user: user))
      .where((i) => i.label != emptyConfigItemLabel)
      .toList();
}

ActionBeltItemConfig _getItemById({
  required BuildContext context,
  required String id,
  CurrentUser? user,
}) {
  final l10n = L10nCappHome.of(context);
  final isAnonymous = user?.isAnonymous ?? true;

  switch (id) {
    case _loanId:
      return ActionBeltItemConfig(
        label: l10n.loanPayment,
        trackingLabel: 'Loan',
        showIconWithoutFrame: true,
        badge: 'New',
        path: 'assets/svg/action_belt/money.svg',
        package: 'capp_ui',
        onTap: () async {
          if (isAnonymous) {
            await context.navigateToSignInScreen(popAfterSignedIn: true);
          } else {
            await NavigationUtils.navigateToRepaymentMain(context: context, viewType: RepaymentViewType.contractList);
          }
        },
      );
    case _loadsId:
      return context.isFlagEnabledWatch(FeatureFlag.profileMenuSelfServiceEloads)
          ? ActionBeltItemConfig(
              label: l10n.loads,
              trackingLabel: 'Loads',
              showIconWithoutFrame: true,
              path: 'assets/svg/action_belt/smartphone.svg',
              package: 'capp_ui',
              onTap: () async {
                // on VN version, login is not required.
                unawaited(context.get<TransactionsTrackingService>().trackHomeDashboardEloadClick());
                if (isAnonymous) {
                  await context.navigateToSignInScreen(popAfterSignedIn: true);
                } else {
                  await context.navigator.pushEloadEntryPoint();
                }
              },
            )
          : emptyConfigItem;
    default:
      return emptyConfigItem;
  }
}

const _allWidgetIds = [
  _loanId,
  _loadsId,
];

const _loanId = 'e27e0815-f8a7-431b-9f48-f24b7ec32a97';
const _loadsId = 'ea29286f-d3fd-410f-a80c-08c4a6e93b84';
