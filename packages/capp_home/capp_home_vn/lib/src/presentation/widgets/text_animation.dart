import 'dart:async';

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class TextAnimation extends StatefulWidget {
  final List<String> lstTitle;
  final Color titleColor;
  final Color bgColor;

  const TextAnimation({
    super.key,
    required this.lstTitle,
    required this.titleColor,
    required this.bgColor,
  });

  @override
  State<TextAnimation> createState() => _TextAnimationState();
}

class _TextAnimationState extends State<TextAnimation> with SingleTickerProviderStateMixin {
  int _count = 0;
  bool _isStopped = false;

  @override
  void initState() {
    super.initState();

    Timer.periodic(const Duration(seconds: 2), (timer) {
      if (_isStopped) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_count < widget.lstTitle.length - 1) {
          _count++;
        } else {
          _count = 0;
        }
      });
    });
  }

  @override
  void dispose() {
    _isStopped = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lstTitle.length > 1) {
      return _animatedQRText();
    }

    return _qrTextView(widget.lstTitle[0]);
  }

  Widget _qrTextView(String qrText) {
    return Container(
      height: 13,
      margin: const EdgeInsets.symmetric(
        vertical: 5,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        color: widget.bgColor,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 3,
        ),
        child: KoyalText.caption4CustomFontSize(
          color: widget.titleColor,
          overflow: TextOverflow.ellipsis,
          qrText,
          fontSize: 9,
        ),
      ),
    );
  }

  Widget _animatedQRText() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 600),
      transitionBuilder: (child, animation) {
        return FadeTransition(opacity: animation, child: child);
      },
      child: Container(
        height: 13,
        key: ValueKey<int>(_count),
        margin: const EdgeInsets.symmetric(
          vertical: 5,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          color: widget.bgColor,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 3,
          ),
          child: KoyalText.caption4CustomFontSize(
            color: widget.titleColor,
            overflow: TextOverflow.ellipsis,
            widget.lstTitle[_count],
            fontSize: 9,
          ),
        ),
      ),
    );
  }
}
