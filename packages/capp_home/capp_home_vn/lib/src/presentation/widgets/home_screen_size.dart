import 'package:flutter/widgets.dart';

class ScreenSize {
  final Size size;
  final int screenWidthLimit;

  ScreenSize({required this.size, required this.screenWidthLimit});

  bool get isScreenOverLimit => size.width >= screenWidthLimit;
}

typedef ScreenSizeBuilder = Widget Function(
  BuildContext,
  ScreenSize screenSize,
);

class HomeScreenSize extends StatelessWidget {
  static const defaultScreenWidthLimit = 400;

  final ScreenSizeBuilder builder;
  final int screenWidthLimit;

  const HomeScreenSize({
    super.key,
    required this.builder,
    this.screenWidthLimit = HomeScreenSize.defaultScreenWidthLimit,
  });

  @override
  Widget build(
    BuildContext context,
  ) {
    return builder.call(
      context,
      ScreenSize(
        size: MediaQuery.sizeOf(context),
        screenWidthLimit: screenWidthLimit,
      ),
    );
  }
}
