import 'dart:async';

import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_transactions/capp_transactions.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:rive/rive.dart';

import 'text_animation.dart';

class QrAnimationFloatingActionBtn extends StatefulWidget {
  final Object? heroTag;
  final bool isAnonymous;
  final double bottomPadding;

  final String title;
  final int playBackDuration;
  final List<String> qrBadges;
  const QrAnimationFloatingActionBtn({
    super.key,
    required this.title,
    required this.playBackDuration,
    this.heroTag,
    required this.isAnonymous,
    required this.bottomPadding,
    required this.qrBadges,
  });

  @override
  State<QrAnimationFloatingActionBtn> createState() => _QrAnimationFloatingActionBtnState();
}

class _QrAnimationFloatingActionBtnState extends State<QrAnimationFloatingActionBtn>
    with SingleTickerProviderStateMixin, BottomBarTrackingMixin {
  late RiveAnimationController _controller;
  late AnimationController _animationController;
  Artboard? globalArtboard;

  bool _isStopped = false;

  @override
  void initState() {
    super.initState();
    _controller = OneShotAnimation(
      'Timeline 1',
      autoplay: false,
    );

    _animationController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _loadRiveFile();
  }

  void _repeatAnimation() {
    Timer.periodic(Duration(seconds: widget.playBackDuration), (timer) {
      if (_isStopped) {
        timer.cancel();
        return;
      }

      _playAnimation();
    });
  }

  void _playAnimation() {
    _controller.isActive = true;
    _animationController.forward();
  }

  @override
  void dispose() {
    _isStopped = true;
    _animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  // Loads a Rive file
  Future<void> _loadRiveFile() async {
    final bytes = await rootBundle.load('packages/capp_home/assets/animation/qr_code_animation.riv');
    await RiveFile.initialize();
    final rFile = RiveFile.import(bytes);

    setState(() {
      globalArtboard = rFile.mainArtboard;
      globalArtboard?.addController(_controller);
      _playAnimation();
      _repeatAnimation();
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 75 + widget.bottomPadding,
      width: 73,
      child: Column(
        children: [
          Hero(
            tag: widget.heroTag ?? '_default_hero_tag',
            child: GestureDetector(
              onTap: () {
                trackNavigationItemClick(context, 'QR');

                if (widget.isAnonymous) {
                  showKoyalBottomSheet<void>(
                    context: context,
                    builder: (_) => const LoginPromptBottomSheetContent(),
                  );
                } else {
                  context.get<TransactionsTrackingService>().trackHomeDashboardQrClick();
                  context.navigator.pushFromPackage(package: 'CappTransactions', screen: 'QrTransactionEntrypoint');
                }
              },
              child: SizedBox(
                width: 46,
                height: 46,
                child: globalArtboard != null
                    ? Rive(
                        fit: BoxFit.cover,
                        artboard: globalArtboard!,
                      )
                    : const SizedBox(),
              ),
            ),
          ),
          TextAnimation(
            lstTitle: widget.qrBadges,
            titleColor: Colors.white,
            bgColor: ColorTheme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }
}
