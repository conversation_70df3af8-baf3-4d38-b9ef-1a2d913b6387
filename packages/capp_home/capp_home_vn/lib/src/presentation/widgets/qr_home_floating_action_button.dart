import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_transactions/capp_transactions.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

class QrFloationActionButtonVn extends StatelessWidget with BottomBarTrackingMixin {
  final Object? heroTag;
  final bool isAnonymous;
  const QrFloationActionButtonVn({
    super.key,
    this.heroTag,
    this.isAnonymous = true,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      key: key,
      heroTag: heroTag,
      shape: const CircleBorder(),
      elevation: 0,
      backgroundColor: ColorTheme.of(context).primaryColor,
      onPressed: () {
        trackNavigationQrClick(context);

        if (isAnonymous) {
          showKoyalBottomSheet<void>(
            context: context,
            builder: (_) => const LoginPromptBottomSheetContent(),
          );
        } else {
          context.get<TransactionsTrackingService>().trackHomeDashboardQrClick();
          context.navigator.pushFromPackage(package: 'CappTransactions', screen: 'QrTransactionEntrypoint');
        }
      },
      child: Icon(
        KoyalIcons.qr_code_outline,
        size: IconTheme.of(context).size! * 1.1,
        color: ColorTheme.of(context).backgroundColor,
      ),
    );
  }
}
