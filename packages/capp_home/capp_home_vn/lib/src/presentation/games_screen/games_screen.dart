import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

class GamesWrapperView extends StatelessWidget {
  final GamesScreenArguments arguments;
  const GamesWrapperView({
    super.key,
    required this.arguments,
  });

  @override
  Widget build(BuildContext context) {
    unawaited(
      context.get<CappTrackingService>().trackEvent(
            event: getEventId(arguments.viewType),
            eventCategory: KoyalTrackingCategories.screenView,
            eventAction: KoyalAnalyticsConstants.view,
            eventLabel: getEventLabel(arguments.viewType),
          ),
    );

    return KoyalScaffold(
      appBar: KoyalAppBar(),
      body: WebView(
        initialUrl: arguments.viewType.url,
      ),
    );
  }
}

KoyalEvent getEventId(GamesViewType type) {
  switch (type) {
    case GamesViewType.gameHub:
      return KoyalEvent.appGameScreenViewGame;
    case GamesViewType.rewardWallet:
      return KoyalEvent.appGameScreenViewRewardWallet;
    case GamesViewType.mission:
      return KoyalEvent.appGameScreenViewMission;
    case GamesViewType.event:
      return KoyalEvent.appGameScreenViewEvent;
    default:
      return KoyalEvent.appGameScreenViewGame;
  }
}

String getEventLabel(GamesViewType type) {
  switch (type) {
    case GamesViewType.gameHub:
      return 'game_screen_gamehub';
    case GamesViewType.rewardWallet:
      return 'game_screen_reward';
    case GamesViewType.mission:
      return 'game_screen_mission';
    case GamesViewType.event:
      return 'game_screen_event';
    default:
      return 'game_screen_gamehub';
  }
}
