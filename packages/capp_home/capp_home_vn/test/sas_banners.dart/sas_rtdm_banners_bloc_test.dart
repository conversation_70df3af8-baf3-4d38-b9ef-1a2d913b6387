import 'package:bloc_test/bloc_test.dart';
import 'package:capp_api/capp_api.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_home/src/application/sas_banners/sas_rtdm_banners_bloc.dart';
import 'package:capp_home/src/domain/sas_banners/repository/i_sas_rtdm_banners_repository.dart';
import 'package:capp_home/src/infrastructure/sas_banners/sas_rtdm_banners_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../shared_mocks.dart';

void main() {
  final getIt = GetIt.asNewInstance();
  const cacheFf = FeatureFlag.sasRTDMHomeBannersCache;

  setUpAll(() {
    getIt
      // Dependencies
      ..registerLazySingleton<Logger>(Logger.new)
      ..registerLazySingleton<ImageServiceBase>(MockImageServiceBase.new)
      ..registerLazySingleton<SasBannersApi>(FakeSasBannerApi.new)
      ..registerLazySingleton<GmaStorageProvider>(MockGmaStorageProvider.new)
      ..registerLazySingleton<GlobalTrackingProperties>(MockGlobalTrackingProperties.new)
      ..registerLazySingleton<IFeatureFlagRepository>(MockFeatureFlagRepository.new)
      // Repository
      ..registerLazySingleton<ISasRtdmBannersRepository>(
        () => SasRtdmBannersRepository(
          bannersApi: getIt<SasBannersApi>(),
          imageService: getIt<ImageServiceBase>(),
          storage: getIt<GmaStorageProvider>(),
          l: getIt<Logger>(),
        ),
      )
      // Bloc
      ..registerFactory(
        () => SasRtdmBannersBloc(
          bannerRepository: getIt<ISasRtdmBannersRepository>(),
          trackingProperties: getIt<GlobalTrackingProperties>(),
          featureFlagRepository: getIt<IFeatureFlagRepository>(),
        ),
      );
    // Register fallback for generic method stubs
    registerFallbackValue(UserSasBannerCacheItem(response: FakeSasBannersApiConsts.fakeResponse));
    registerFallbackValue(DateTime.now());
    // Common mocking
    when(() => getIt<GmaStorageProvider>().delete(any())).thenAnswer(Future<void>.value);
    when(() => getIt<ImageServiceBase>().getUrlFromId(any<String>())).thenAnswer((id) => 'mocked url for: $id');
    when(() => getIt<GmaStorageProvider>().insert(any<String>(), any<UserSasBannerCacheItem>()))
        .thenAnswer(Future<void>.value);
    when(() => getIt<GmaStorageProvider>().insert<DateTime>(any<String>(), any<DateTime>()))
        .thenAnswer(Future<void>.value);
    when(
      () => getIt<GmaStorageProvider>()
          .get<UserSasBannerCacheItem?>(any<String>(), fromMap: any(named: 'fromMap', that: isNotNull)),
    ).thenAnswer(
      (_) =>
          Future<UserSasBannerCacheItem?>.value(UserSasBannerCacheItem(response: FakeSasBannersApiConsts.fakeResponse)),
    );
  });
  group('SAS RTDM banner init', () {
    blocTest(
      'Cache is disabled by FF',
      setUp: () {
        when(() => getIt<IFeatureFlagRepository>().isEnabledCached(cacheFf)).thenReturn(false);
      },
      build: getIt<SasRtdmBannersBloc>,
      act: (bloc) => bloc.add(const SasRtdmBannersEvent.init()),
      expect: () => [
        isA<SasRtdmBannersInitial>(),
        isA<SasRtdmBannersLoading>(),
        const TypeMatcher<SasRtdmBannersSuccess>()
            .having((s) => s.correlationId, 'Is correlation id filled', matches(FakeSasBannersApiConsts.correlationId))
            .having((s) => s.rtdmBanners, 'Banners loaded', isNotEmpty)
            .having((s) => s.rtdmBanners[0].imageUrl, 'Image id is mapped to url', startsWith('mocked url')),
      ],
      verify: (_) {
        // Check if the cache was cleared
        verify(() => getIt<GmaStorageProvider>().delete(any())).called(2);
      },
    );

    blocTest(
      'Cache is enabled by FF but invalid',
      setUp: () {
        when(() => getIt<IFeatureFlagRepository>().isEnabledCached(cacheFf)).thenReturn(true);
        // Mock stored time behavior
        final timeReturns = <DateTime?>[DateTime.now().subtract(const Duration(days: 2)), null];
        when(() => getIt<GmaStorageProvider>().get<DateTime?>(any()))
            .thenAnswer((_) => Future<DateTime?>.value(timeReturns.removeAt(0)));
      },
      build: getIt<SasRtdmBannersBloc>,
      act: (bloc) => bloc.add(const SasRtdmBannersEvent.init()),
      expect: () => [
        isA<SasRtdmBannersInitial>(),
        isA<SasRtdmBannersLoading>(),
        isA<SasRtdmBannersSuccess>(),
      ],
      verify: (_) {
        // Check if the cache was cleared
        verify(() => getIt<GmaStorageProvider>().delete(any())).called(2);
        // Check if response was stored
        verify(() => getIt<GmaStorageProvider>().insert(any<String>(), any<UserSasBannerCacheItem>())).called(1);
        // Check if time when response was stored is also stored
        verify(() => getIt<GmaStorageProvider>().insert(any<String>(), any<DateTime>())).called(1);
      },
    );

    blocTest(
      'Cache is enabled by FF and valid',
      setUp: () {
        when(() => getIt<IFeatureFlagRepository>().isEnabledCached(cacheFf)).thenReturn(true);
        // Mock cache behavior
        when(() => getIt<GmaStorageProvider>().get<DateTime?>(any()))
            .thenAnswer((_) => Future<DateTime?>.value(DateTime.now().subtract(const Duration(hours: 2))));
      },
      build: getIt<SasRtdmBannersBloc>,
      act: (bloc) => bloc.add(const SasRtdmBannersEvent.init()),
      expect: () => [
        isA<SasRtdmBannersInitial>(),
        isA<SasRtdmBannersLoading>(),
        isA<SasRtdmBannersSuccess>(),
      ],
      verify: (_) {
        // Check if nothing was deleted
        verifyNever(() => getIt<GmaStorageProvider>().delete(any()));
        // // Check if response is restored from storage
        verify(
          () => getIt<GmaStorageProvider>()
              .get<UserSasBannerCacheItem?>(any<String>(), fromMap: any(named: 'fromMap', that: isNotNull)),
        ).called(1);
      },
    );
  });

  group('SAS RTDM banner refresh', () {
    blocTest(
      'Verify that states are in valid order',
      setUp: () {
        when(() => getIt<IFeatureFlagRepository>().isEnabledCached(cacheFf)).thenReturn(false);
      },
      build: getIt<SasRtdmBannersBloc>,
      act: (bloc) => bloc
        ..add(const SasRtdmBannersEvent.init())
        ..add(const SasRtdmBannersEvent.refresh()),
      expect: () => [
        isA<SasRtdmBannersInitial>(),
        isA<SasRtdmBannersLoading>(),
        isA<SasRtdmBannersSuccess>(),
        isA<SasRtdmBannersLoading>(),
        isA<SasRtdmBannersSuccess>(),
      ],
    );
  });
}
