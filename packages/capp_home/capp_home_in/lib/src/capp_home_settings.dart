import 'package:capp_home_core/capp_home_core.dart';

import 'presentation/action_belt_items_builder.dart';

class CappHomeSettingsIn extends CappHomeSettings {
  const CappHomeSettingsIn()
      : super(
          accountMenuItems: const [AccountMenuItem.retrieveMyAccount, AccountMenuItem.rewards],
          assistantIconItems: const [AssistantIconItem.payEMI],
          assistantChipItems: const [AssistantChipItem.contact],
          showAssistantGuestContactButton: true,
          buildActionBeltItems: buildActionBeltItems,
          actionBeltHasTitle: true,
          hasImprovedLoansScreen: true,
          blogsFinancialLiteracyCategoryId: '3404797e-80ba-4a82-9bf2-dac9069dce56',
          showMyCoupons: true,
          animatedPendingActionIcon: 'packages/capp_ui/assets/animation/kupo_pending_action.flr',
        );
}
