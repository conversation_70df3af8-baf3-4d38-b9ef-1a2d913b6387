// GENERATED CODE - DO NOT MODIFY BY HAND

import 'package:dynamic_forms/dynamic_forms.dart';
import '../components.dart';

class FormCouponParser<TFormCoupon extends FormCoupon>
    extends FormElementParser<TFormCoupon> {
  @override
  String get name => 'formCoupon';

  @override
  FormElement getInstance() => FormCoupon();

  @override
  void fillProperties(
    TFormCoupon formCoupon, 
    ParserNode parserNode, 
    Element? parent,
    ElementParserFunction parser,
  ) {
    super.fillProperties(formCoupon, parserNode, parent, parser);
    formCoupon
      ..activationCodeProperty = parserNode.getStringProperty(
        'activationCode',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..activationUrlProperty = parserNode.getStringProperty(
        'activationUrl',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..brandLogoProperty = parserNode.getStringProperty(
        'brandLogo',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..brandNameProperty = parserNode.getStringProperty(
        'brandName',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..childrenProperty = parserNode.getChildrenProperty<FormMedia>(
          parent: formCoupon,
          parser: parser,
          isImmutable: true,
          childrenPropertyName: 'children',
          isContentProperty: true)
      ..codeProperty = parserNode.getStringProperty(
        'code',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..couponIdProperty = parserNode.getStringProperty(
        'couponId',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..descriptionAboutProperty = parserNode.getStringProperty(
        'descriptionAbout',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..descriptionNeedToKnowProperty = parserNode.getStringProperty(
        'descriptionNeedToKnow',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..introductionProperty = parserNode.getStringProperty(
        'introduction',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..titleProperty = parserNode.getStringProperty(
        'title',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..urlProperty = parserNode.getStringProperty(
        'url',
        defaultValue: ParserNode.defaultString,
        isImmutable: true,
      )
      ..validFromProperty = parserNode.getDateTimeProperty(
        'validFrom',
        defaultValue: ParserNode.defaultDateTime,
        isImmutable: true,
        format: null,
      )
      ..validToProperty = parserNode.getDateTimeProperty(
        'validTo',
        defaultValue: ParserNode.defaultDateTime,
        isImmutable: true,
        format: null,
      );
  }
}
