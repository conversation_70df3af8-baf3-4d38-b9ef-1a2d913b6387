part of 'calculator_hs_bloc.dart';

sealed class CalculatorHsState extends Equatable {
  const CalculatorHsState();

  const factory CalculatorHsState.initial() = CalculatorHsInitial;
  const factory CalculatorHsState.loadInProgress() = CalculatorHsLoadInProgress;
  const factory CalculatorHsState.failure() = CalculatorHsFailure;
  const factory CalculatorHsState.success({required HsCalculator calculator}) = CalculatorHsSuccess;

  @override
  List<Object?> get props => [];
}

final class CalculatorHsInitial extends CalculatorHsState {
  const CalculatorHsInitial();
}

final class CalculatorHsLoadInProgress extends CalculatorHsState {
  const CalculatorHsLoadInProgress();
}

final class CalculatorHsFailure extends CalculatorHsState {
  const CalculatorHsFailure();
}

final class CalculatorHsSuccess extends CalculatorHsState {
  final HsCalculator calculator;

  const CalculatorHsSuccess({
    required this.calculator,
  });

  @override
  List<Object?> get props => [calculator];
}
