part of 'banner_visibility_bloc.dart';

sealed class BannerVisibilityState {
  const BannerVisibilityState();

  const factory BannerVisibilityState.hide() = BannersNotVisible;

  const factory BannerVisibilityState.show() = BannersVisible;
}

final class BannersNotVisible extends BannerVisibilityState {
  const BannersNotVisible();
}

final class BannersVisible extends BannerVisibilityState {
  const BannersVisible();
}
