import 'package:capp_api/capp_api.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../domain/calculator/i_emi_calculator_repository.dart';

class EmiCalculatorRepository implements IEmiCalculatorRepository {
  final CalculatorApi calculatorApi;
  final Logger logger;

  EmiCalculatorRepository({
    required this.calculatorApi,
    required this.logger,
  });

  @override
  Future<Either<Unit, HsCalculator>> getEmiCalculator() async {
    try {
      final response = await calculatorApi.inEmiWidgetsCalculatorGet('');
      return right(response.toDomain());
    } on DioError catch (e) {
      logger.d('Unexpected error in emi calculator repository', e);
      return left(unit);
    } on Exception catch (e, s) {
      logger.d('Unexpected error in emi calculator repository', e, s);
      return left(unit);
    }
  }
}
