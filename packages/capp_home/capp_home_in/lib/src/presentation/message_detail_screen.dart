import 'package:capp_home_core/capp_home_core.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';

import '../services/dynamic_forms/widgets/form_coupon/form_coupon_renderer.dart';

class MessageDetailScreenIn extends MessageDetailScreen {
  MessageDetailScreenIn({super.key, required super.arguments});

  @override
  List<FormElementRenderer> getInboxRenderers(BoxConstraints constraints) {
    return super.getInboxRenderers(constraints)..add(FormCouponRenderer(constraints));
  }
}
