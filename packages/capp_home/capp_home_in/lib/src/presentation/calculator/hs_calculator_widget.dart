import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../application/calculator/calculator_hs_bloc.dart';
import 'hs_calculator_component.dart';

class HsCalculatorWidget extends StatefulWidget {
  final String? title;
  const HsCalculatorWidget({
    super.key,
    this.title,
  });

  @override
  State<HsCalculatorWidget> createState() => _HsCalculatorWidgetState();
}

class _HsCalculatorWidgetState extends State<HsCalculatorWidget> {
  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalHorizontal(
      child: BlocProvider<CalculatorHsBloc>.value(
        value: context.get<CalculatorHsBloc>()..add(const CalculatorHsEvent.started()),
        child: BlocBuilder<CalculatorHsBloc, CalculatorHsState>(
          builder: (context, state) {
            return AdvancedRetryContainer(
              screenName: 'products_screen',
              isError: state is CalculatorHsFailure,
              isLoading: state is CalculatorHsLoadInProgress,
              onRetry: () => BlocProvider.of<CalculatorHsBloc>(context).add(const CalculatorHsEvent.started()),
              child: switch (state) {
                final CalculatorHsSuccess loaded => KoyalPadding.normalVertical(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.title != null)
                          KoyalPadding.normalVertical(
                            child: KoyalText.subtitle1(
                              widget.title!,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        HsCalculatorComponent(
                          calculator: loaded.calculator,
                        ),
                      ],
                    ),
                  ),
                _ => KoyalPadding.normalVertical(
                    child: KoyalElevation.koyal3Dp(
                      child: KoyalShimmer(
                        child: Container(
                          width: double.maxFinite,
                          height: 200,
                          decoration: BoxDecoration(
                            color: ColorTheme.of(context).backgroundColor,
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                      ),
                    ),
                  ),
              },
            );
          },
        ),
      ),
    );
  }
}
