name: capp_home
owner: Con_A
version: 0.0.1
publish_to: none
description: Home screen and related widgets
environment:
  sdk: ">=3.2.6 <4.0.0"
dependencies:
  multiple_localization: ^0.5.0
  get_it: ^7.3.0
  animations: ^2.0.3
  flutter_svg: ^2.1.0
  visibility_detector: ^0.4.0+2
  flutter_bloc: ^8.1.3
  freezed_annotation: ^2.2.0
  flutter:
    sdk: flutter
  capp_home_core:
    path: ../capp_home_core
  koyal_auth:
    path: ../../koyal_auth
  koyal_core:
    path: ../../koyal_core
  koyal_shared:
    path: ../../koyal_shared/koyal_shared
  capp_ui_core:
    path: ../../capp_ui/capp_ui_core
  koyal_localizations:
    path: ../../koyal_localizations
  capp_domain:
    path: ../../capp_domain
  capp_tracking:
    path: ../../capp_tracking
dev_dependencies:
  test: ^1.17.12
  build_runner: ^2.4.11
  freezed: ^2.3.2
  json_serializable: ^6.6.1

  bloc_test: ^9.1.6
  flutter_test:
    sdk: flutter
  gma_lints:
    path: ../../gma_lints
  gen_lang:
    git:
      url: https://<EMAIL>/hci-iap/koyal/_git/gen_lang
      ref: 083df52ba70218fcb91121e5c59b3e3764d1f241
flutter:
  uses-material-design: true
