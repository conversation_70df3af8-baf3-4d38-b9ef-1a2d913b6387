name: capp_home_core
owner: Con_A
version: 0.0.1
publish_to: none
description: Package containing home screen and related widgets
environment:
  sdk: ">=3.2.6 <4.0.0"
dependencies:
  multiple_localization: ^0.5.0
  get_it: ^7.3.0
  animations: ^2.0.3
  flutter_svg: ^2.1.0
  in_app_review: ^2.0.6
  qr_flutter: ^4.0.0
  string_unescape: ^2.0.0
  provider: ^6.1.1
  capp_bigdata:
    path: ../../capp_bigdata/capp_bigdata_vn
  flutter_app_badger:
    path: ../../../forks/flutter_app_badger
  custom_refresh_indicator: ^4.0.1
  cool_devtool: 0.0.4
  flutter:
    sdk: flutter
  koyal_auth:
    path: ../../koyal_auth
  koyal_core:
    path: ../../koyal_core
  koyal_dynamic_forms:
    path: ../../koyal_dynamic_forms
  koyal_shared:
    path: ../../koyal_shared/koyal_shared
  koyal_otp:
    path: ../../koyal_otp
  koyal_localizations:
    path: ../../koyal_localizations
  koyal_lock:
    path: ../../koyal_lock
  koyal_messaging:
    path: ../../koyal_messaging
  capp_appsflyer_core:
    path: ../../capp_appsflyer/capp_appsflyer_core
  capp_content_core:
    path: ../../capp_content/capp_content_core
  capp_config_core:
    path: ../../capp_config/capp_config_core
  capp_datascore_core:
    path: ../../capp_datascore/capp_datascore_core
  capp_legal_permission_core:
    path: ../../capp_legal_permission/capp_legal_permission_core
  capp_domain:
    path: ../../capp_domain
  capp_finbox_core:
    path: ../../capp_finbox/capp_finbox_core
  capp_loan_origination_unified_core:
    path: ../../capp_loan_origination_unified/capp_loan_origination_unified_core
  capp_self_service_core:
    path: ../../capp_self_service/capp_self_service_core
  capp_transaction_history_core:
    path: ../../capp_transaction_history/capp_transaction_history_core
  capp_auth_core:
    path: ../../capp_auth/capp_auth_core
  capp_cards_core:
    path: ../../capp_cards/capp_cards_core
  capp_personal_core:
    path: ../../capp_personal/capp_personal_core
  capp_products_core:
    path: ../../capp_products/capp_products_core
  capp_vas_core:
    path: ../../capp_vas/capp_vas_core
  capp_api:
    path: ../../capp_api
  capp_face_guard:
    path: ../../capp_face_guard
  gma_storage:
    path: ../../gma_storage
  gma_platform:
    path: ../../gma_platform
  flutter_dynamic_forms:
    path: ../../../plugins/flutter_dynamic_forms/packages/flutter_dynamic_forms
  flutter_dynamic_forms_components:
    path: ../../../plugins/flutter_dynamic_forms/packages/flutter_dynamic_forms_components

dev_dependencies:
  build_runner: ^2.4.11
  freezed: ^2.3.2
  json_serializable: ^6.6.1

  bloc_test: ^9.1.6
  gma_lints:
    path: ../../gma_lints
  flutter_test:
    sdk: flutter
  dynamic_forms_generator:
    path: ../../../plugins/flutter_dynamic_forms/packages/dynamic_forms_generator
  gen_lang:
    git:
      url: https://<EMAIL>/hci-iap/koyal/_git/gen_lang
      ref: 083df52ba70218fcb91121e5c59b3e3764d1f241
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/icons/
    - assets/images/
    - assets/images/feedback/
