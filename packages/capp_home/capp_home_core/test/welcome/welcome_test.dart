import 'package:bloc_test/bloc_test.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:mocktail/mocktail.dart';

import '../capp_home_shared_mocks.dart';

void main() {
  late HomeBloc bloc;
  late MockIdentityRepository mockIdentityRepository;
  late MockContentRepository mockContentRepository;
  late MockImageServiceBase mockImageServiceBase;
  late MockCappTrackingService mockCappTrackingService;
  late MockUserWidgetsRepository mockUserWidgetsRepository;
  late MockFeatureFlagRepository mockFeatureFlagRepository;
  late MockKoyalRemoteNotificationService mockKoyalRemoteNotificationService;
  late MockLogger mockLogger;
  late MockAppsflyerService mockAppsflyerService;
  late MockFirebasePerformanceMonitoring mockFirebasePerformanceMonitoring;
  late MockOfferBannerRepository mockOfferBannerRepository;
  late MockCurrentUserRepository mockCurrentUserRepository;
  late MockLocalizationRepository mockLocalizationRepository;
  late MockUserSegmentationRepository mockUserSegmentationRepository;
  late MockNewPendingActionsRepository mockNewPendingActionsRepository;
  late MockSurveyRepository mockSurveyRepository;
  late MockSasCollectorPlugin mockSasCollectorPlugin;

  setUpAll(() {
    mockIdentityRepository = MockIdentityRepository();
    mockContentRepository = MockContentRepository();
    mockImageServiceBase = MockImageServiceBase();
    mockCappTrackingService = MockCappTrackingService();
    mockUserWidgetsRepository = MockUserWidgetsRepository();
    mockFeatureFlagRepository = MockFeatureFlagRepository();
    mockKoyalRemoteNotificationService = MockKoyalRemoteNotificationService();
    mockLogger = MockLogger();
    mockAppsflyerService = MockAppsflyerService();
    mockFirebasePerformanceMonitoring = MockFirebasePerformanceMonitoring();
    mockOfferBannerRepository = MockOfferBannerRepository();
    mockCurrentUserRepository = MockCurrentUserRepository();
    mockLocalizationRepository = MockLocalizationRepository();
    mockUserSegmentationRepository = MockUserSegmentationRepository();
    mockNewPendingActionsRepository = MockNewPendingActionsRepository();
    mockSurveyRepository = MockSurveyRepository();
    mockSasCollectorPlugin = MockSasCollectorPlugin();
  });

  const incomeDummyOffer = IncomeOfferBannerData(
    id: '5',
    type: OfferBannerType.income,
    journeyStartPoint: 'EXIST_WO_SCORING_OTP',
    workflowPath: '',
    subtitleAmount: '',
    imageUrl: '',
    offerImageUrl: '',
    scoringDescription: '',
    userInputDescription: '',
    offerId: '',
    productType: '',
    loanType: '',
    workflowVersion: null,
  );
  const incomeOffer = IncomeOfferBannerData(
    id: '5',
    type: OfferBannerType.income,
    journeyStartPoint: 'EXIST_SCORING_OTP',
    workflowPath: '',
    subtitleAmount: '',
    imageUrl: '',
    offerImageUrl: '',
    scoringDescription: '',
    userInputDescription: '',
    offerId: '',
    productType: '',
    loanType: '',
    workflowVersion: null,
  );
  final clxOffer = LoanOfferModel(
    id: '1',
    journeyStartPoint: '',
  );
  final ccxOffer = LoanOfferModel(
    id: '2',
    journeyStartPoint: '',
  );

  const emptyOfferBanner = OfferBannerData();
  const incomeDummyOfferBanner = OfferBannerData(incomeBanner: incomeDummyOffer);
  const incomeOfferBanner = OfferBannerData(incomeBanner: incomeOffer);
  final clxOfferBanner = OfferBannerData(offerBanners: [clxOffer]);
  final ccxOfferBanner = OfferBannerData(offerBanners: [ccxOffer]);
  final multiOfferBanner = OfferBannerData(offerBanners: [clxOffer, ccxOffer]);

  const metaDataOfPA = <String, String?>{
    'ApplicationCode': '1',
    'ProductType': 'PersonalCard',
    'Amount': '10000000.0',
  };
  const pendingActionWithActiveContract = PendingActionModel(
    type: PendingActionType.loanPending,
    dataLoanPending: DataLoanPending.loanSigning,
    id: '1',
    metadata: metaDataOfPA,
  );
  const pendingActionWithActiveContractForSigning = PendingActionModel(
    type: PendingActionType.signContract,
    id: '1',
    metadata: metaDataOfPA,
  );
  const pendingActionWithoutActiveContract = PendingActionModel(type: PendingActionType.loanPending, id: '1');

  final financialCardStatesWithActive = CreditCardState(
    currency: '',
    contractInfo: const ContractInfo(
      contractType: ContractType.creditCard,
      contractNumber: '',
      currency: '',
      isBlocked: false,
      isTerminated: false,
      contractStatus: ContractStatus.active,
    ),
    repaymentEligible: null,
    available: Decimal.parse('0'),
    actualSpend: Decimal.parse('0'),
    dueAmount: null,
    dueDate: null,
    isOverdue: false,
    signatureDate: null,
    creditCardData: const CreditCardData(),
  );
  final financialCardStatesNoActive = CreditCardState(
    currency: '',
    contractInfo: const ContractInfo(
      contractType: ContractType.creditCard,
      contractNumber: '',
      currency: '',
      isBlocked: false,
      isTerminated: false,
    ),
    repaymentEligible: null,
    available: Decimal.parse('0'),
    actualSpend: Decimal.parse('0'),
    dueAmount: null,
    dueDate: null,
    isOverdue: false,
    signatureDate: null,
    creditCardData: const CreditCardData(),
  );

  void commonMock() {
    for (final type in WelcomeBannerType.values) {
      when(() => mockFeatureFlagRepository.hasFeatureFlag(type.featureFlag)).thenAnswer(
        (_) => Future.value(right(true)),
      );
      when(() => mockFeatureFlagRepository.isEnabledCached(type.featureFlag)).thenAnswer(
        (_) => true,
      );
      when(
        () => mockOfferBannerRepository.setIsWelcomeBannerFirstShow(type, isFirstShow: false),
      ).thenAnswer(
        (_) => Future.value(),
      );
      when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(type)).thenAnswer(
        (_) => Future.value(DateTime.now().subtract(const Duration(days: 7))),
      );
      when(() => mockFeatureFlagRepository.getFeatureFlagValue(type.featureFlag)).thenAnswer(
        (_) => Future.value(right('')),
      );
      when(
        () => mockOfferBannerRepository.setIsWelcomeBannerFirstShow(type, isFirstShow: true),
      ).thenAnswer(
        (_) => Future.value(),
      );
      when(
        () => mockOfferBannerRepository.getIsWelcomeBannerFirstShow(
          type,
        ),
      ).thenAnswer(
        (_) => Future.value(true),
      );

      when(
        () => mockOfferBannerRepository.setAllWelcomeBannerFirstShowLoaded(excludeType: type),
      ).thenAnswer(
        (_) => Future.value(),
      );
    }
    when(() => mockCurrentUserRepository.isCurrentUserAnonymous()).thenAnswer(
      (_) => Future.value(false),
    );
    when(() => mockOfferBannerRepository.getPendingActionIdsFirstFetched()).thenAnswer(
      (_) => Future.value([]),
    );
    when(() => mockOfferBannerRepository.setPendingActionIdsFirstFetched([pendingActionWithActiveContract.id]))
        .thenAnswer(
      (_) => Future.value(),
    );
    when(() => mockOfferBannerRepository.setPendingActionIdsFirstFetched([])).thenAnswer(
      (_) => Future.value(),
    );

    when(() => mockOfferBannerRepository.getOfferIdsFirstFetched()).thenAnswer(
      (_) => Future.value([]),
    );
    when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([])).thenAnswer(
      (_) => Future.value(),
    );
  }

  void initBloc() {
    bloc = HomeBloc(
      identityRepository: mockIdentityRepository,
      contentRepository: mockContentRepository,
      imageService: mockImageServiceBase,
      trackingService: mockCappTrackingService,
      userWidgetsRepository: mockUserWidgetsRepository,
      featureFlagRepository: mockFeatureFlagRepository,
      notificationService: mockKoyalRemoteNotificationService,
      logger: mockLogger,
      appsflyerService: mockAppsflyerService,
      firebasePerformanceMonitoring: mockFirebasePerformanceMonitoring,
      offerBannerRepository: mockOfferBannerRepository,
      currentUserRepository: mockCurrentUserRepository,
      localizationRepository: mockLocalizationRepository,
      userSegmentationRepository: mockUserSegmentationRepository,
      userSegmentationEnabled: true,
      pendingActionsRepository: mockNewPendingActionsRepository,
      surveyRepository: mockSurveyRepository,
      sasCollectorPlugin: mockSasCollectorPlugin,
    );
  }

  group(
    'Welcome Offer - Signing',
    () {
      setUp(
        initBloc,
      );
      tearDown(
        () {
          bloc.close();
        },
      );
      blocTest<HomeBloc, HomeState>(
        'Show Singing Welcome Screen with Active Contract',
        build: () {
          commonMock();
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              emptyOfferBanner,
              [pendingActionWithActiveContract],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.signing),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Show Singing Welcome Screen with Active Contract Signing',
        build: () {
          commonMock();
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              emptyOfferBanner,
              [pendingActionWithActiveContractForSigning],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.signing),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Dont show Singing Welcome Screen without Active Contract',
        build: () {
          commonMock();
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              emptyOfferBanner,
              [pendingActionWithoutActiveContract],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
        ],
      );
    },
  );
  group(
    'Welcome Offer - IdCard',
    () {
      setUp(
        initBloc,
      );
      tearDown(
        () {
          bloc.close();
        },
      );
      blocTest<HomeBloc, HomeState>(
        'Show Id Card Update Welcome Screen with Active Contract',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );

          const valueFF = '''
          {
            "Frequency": "7",
            "TemplateId": "21"
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '21'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            HomeEvent.checkShowWelcomeOffer(
              emptyOfferBanner,
              [],
              [financialCardStatesWithActive],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.idCard),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Dont show Id Card Update Welcome Screen without Active Contract',
        build: () {
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          commonMock();
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            HomeEvent.checkShowWelcomeOffer(
              emptyOfferBanner,
              [],
              [financialCardStatesNoActive],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
        ],
      );
    },
  );
  group(
    'Welcome Offer - Single product',
    () {
      setUp(
        initBloc,
      );
      tearDown(
        () {
          bloc.close();
        },
      );
      blocTest<HomeBloc, HomeState>(
        'Show CLX Welcome Screen with CLX Offer',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([clxOfferBanner.offerBanners.first.id]))
              .thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Frequency": "7",
            "TemplateId": "17"
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.cash.featureFlag)).thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '17'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            HomeEvent.checkShowWelcomeOffer(
              clxOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.cash),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Show CCX Welcome Screen with CCX Offer',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([ccxOfferBanner.offerBanners.first.id]))
              .thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Frequency": "7",
            "TemplateId": "17"
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.creditCard.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '17'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            HomeEvent.checkShowWelcomeOffer(
              ccxOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.creditCard),
        ],
      );
    },
  );

  group(
    'Welcome Offer - Multi products',
    () {
      setUp(
        initBloc,
      );
      tearDown(
        () {
          bloc.close();
        },
      );
      blocTest<HomeBloc, HomeState>(
        'Show CLX Welcome Screen with Version is A',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashScoringAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.creditCardAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([])).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched(multiOfferBanner.getAllIds())).thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Version": "A",
            "Frequency": 14,
            "Data": [
              {
                "Name": "CLX",
                "TemplateId": 17,
                "Priority": 1
              }
            ]
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.cashScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.creditCardAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.cashAB)).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.creditCardAB))
              .thenAnswer(
            (_) => Future.value(),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '17'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );

          return bloc;
        },
        act: (bloc) {
          bloc.add(
            HomeEvent.checkShowWelcomeOffer(
              multiOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.cashAB),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Show CCX Welcome Screen with Version is B',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.creditCardAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([])).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched(multiOfferBanner.getAllIds())).thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Version": "B",
            "Frequency": 7,
            "Data": [
              {
                "Name": "CCX",
                "TemplateId": 23,
                "Priority": 1
              },
              {
                "Name": "CLX",
                "TemplateId": 18,
                "Priority": 2
              }
            ]
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.cashAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(valueFF)),
          );
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.creditCardAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '23'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );

          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.cashAB)).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.creditCardAB))
              .thenAnswer(
            (_) => Future.value(),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            HomeEvent.checkShowWelcomeOffer(
              multiOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.creditCardAB),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Show CLX Welcome Screen with Version is B',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.creditCardAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([])).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched(multiOfferBanner.getAllIds())).thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Version": "B",
            "Frequency": 7,
            "Data": [
              {
                "Name": "CCX",
                "TemplateId": 23,
                "Priority": 1
              },
              {
                "Name": "CLX",
                "TemplateId": 18,
                "Priority": 2
              }
            ]
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.cashAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(valueFF)),
          );
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.creditCardAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '18'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );

          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.cashAB)).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.creditCardAB))
              .thenAnswer(
            (_) => Future.value(DateTime.now().subtract(const Duration(days: 7))),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            HomeEvent.checkShowWelcomeOffer(
              multiOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.cashAB),
        ],
      );
    },
  );
  group(
    'Welcome Offer - Dummy Scoring',
    () {
      setUp(
        initBloc,
      );
      tearDown(
        () {
          bloc.close();
        },
      );
      blocTest<HomeBloc, HomeState>(
        'Show Income Welcome Screen with Dummy Income Offer With FF of AB On',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );

          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashScoringAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );

          when(() => mockFeatureFlagRepository.isEnabledCached(FeatureFlag.welcomeCashAndCreditCardScoringOffer))
              .thenAnswer(
            (_) => true,
          );
          when(() => mockFeatureFlagRepository.isEnabledCached(FeatureFlag.stopShowingWSDummyScoring)).thenAnswer(
            (_) => false,
          );
          when(() => mockFeatureFlagRepository.isEnabledCached(FeatureFlag.improveDisplayingWelcomeScreen)).thenAnswer(
            (_) => false,
          );

          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([incomeDummyOfferBanner.incomeBanner?.id ?? '']))
              .thenAnswer(
            (_) => Future.value(),
          );

          const valueFF = '''
          {
            "Frequency": "7",
            "TemplateId": "17"
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.scoring.featureFlag)).thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '17'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              incomeDummyOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.scoring),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Show Income Welcome Screen with Dummy Income Offer With FF of AB Off',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );

          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashScoringAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );

          when(() => mockFeatureFlagRepository.isEnabledCached(FeatureFlag.welcomeCashAndCreditCardScoringOffer))
              .thenAnswer(
            (_) => false,
          );
          when(() => mockFeatureFlagRepository.isEnabledCached(FeatureFlag.stopShowingWSDummyScoring)).thenAnswer(
            (_) => false,
          );
          when(() => mockFeatureFlagRepository.isEnabledCached(FeatureFlag.improveDisplayingWelcomeScreen)).thenAnswer(
            (_) => false,
          );

          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([incomeDummyOfferBanner.incomeBanner?.id ?? '']))
              .thenAnswer(
            (_) => Future.value(),
          );

          const valueFF = '''
          {
            "Frequency": "7",
            "TemplateId": "17"
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.scoring.featureFlag)).thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '17'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              incomeDummyOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.scoring),
        ],
      );
    },
  );
  group(
    'Welcome Offer - AB for Real Scoring',
    () {
      setUp(
        initBloc,
      );
      tearDown(
        () {
          bloc.close();
        },
      );
      blocTest<HomeBloc, HomeState>(
        'Show CLX Scoring Welcome Screen with Version is A',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashScoringAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.creditCardScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([])).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched(incomeOfferBanner.getAllIds())).thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Version": "A",
            "Frequency": 14,
            "Data": [
              {
                "Name": "CLX",
                "TemplateId": 17,
                "Priority": 1
              }
            ]
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.cashScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.creditCardScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.cashScoringAB))
              .thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.creditCardScoringAB))
              .thenAnswer(
            (_) => Future.value(),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '17'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );

          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              incomeOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.cashScoringAB),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Show CCX Scoring Welcome Screen with Version is B',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashScoringAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.creditCardScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([])).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched(incomeOfferBanner.getAllIds())).thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Version": "B",
            "Frequency": 7,
            "Data": [
              {
                "Name": "CCX",
                "TemplateId": 23,
                "Priority": 1
              },
              {
                "Name": "CLX",
                "TemplateId": 18,
                "Priority": 2
              }
            ]
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.cashScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.creditCardScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '23'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );

          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.cashScoringAB))
              .thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.creditCardScoringAB))
              .thenAnswer(
            (_) => Future.value(),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              incomeOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.creditCardScoringAB),
        ],
      );
      blocTest<HomeBloc, HomeState>(
        'Show CLX Scoring Welcome Screen with Version is B',
        build: () {
          commonMock();
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.signing.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.idCard.featureFlag)).thenAnswer(
            (_) => Future.value(right(false)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.cashScoringAB.featureFlag)).thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockFeatureFlagRepository.hasFeatureFlag(WelcomeBannerType.creditCardScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(true)),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched([])).thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.setOfferIdsFirstFetched(incomeOfferBanner.getAllIds())).thenAnswer(
            (_) => Future.value(),
          );
          const valueFF = '''
          {
            "Version": "B",
            "Frequency": 7,
            "Data": [
              {
                "Name": "CCX",
                "TemplateId": 23,
                "Priority": 1
              },
              {
                "Name": "CLX",
                "TemplateId": 18,
                "Priority": 2
              }
            ]
          }
          ''';
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.cashScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );
          when(() => mockFeatureFlagRepository.getFeatureFlagValue(WelcomeBannerType.creditCardScoringAB.featureFlag))
              .thenAnswer(
            (_) => Future.value(right(valueFF)),
          );

          when(
            () => mockOfferBannerRepository.getBannerContent(id: '18'),
          ).thenAnswer(
            (_) => Future.value(right(ContentReferential(id: '1'))),
          );

          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.cashScoringAB))
              .thenAnswer(
            (_) => Future.value(),
          );
          when(() => mockOfferBannerRepository.getIsWelcomeBannerLastTimeShow(WelcomeBannerType.creditCardScoringAB))
              .thenAnswer(
            (_) => Future.value(DateTime.now().subtract(const Duration(days: 7))),
          );
          return bloc;
        },
        act: (bloc) {
          bloc.add(
            const HomeEvent.checkShowWelcomeOffer(
              incomeOfferBanner,
              [],
              [],
            ),
          );
        },
        expect: () => <TypeMatcher>[
          const TypeMatcher<HomeState>().having((s) => s.welcomeBannerType, 'welcomeBannerType', null),
          const TypeMatcher<HomeState>()
              .having((s) => s.welcomeBannerType, 'welcomeBannerType', WelcomeBannerType.cashScoringAB),
        ],
      );
    },
  );
}
