import 'package:capp_home_core/capp_home_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:mocktail/mocktail.dart';

import '../../capp_home_shared_mocks.dart';

void main() {
  late ICurrentUserRepository mockCurrentUserRepository;
  late FeedbackStatusStorage mockFeedbackStatusStorage;
  late FeedbackRepository mockFeedbackRepository;
  late FeedbackJourneyService feedbackJourneyService;

  setUpAll(() {
    mockCurrentUserRepository = MockCurrentUserRepository();
    mockFeedbackStatusStorage = MockFeedbackStatusStorage();
    mockFeedbackRepository = MockFeedbackRepository();

    feedbackJourneyService = FeedbackJourneyService(
      currentUserRepository: mockCurrentUserRepository,
      feedbackStatusStorage: mockFeedbackStatusStorage,
      feedbackRepository: mockFeedbackRepository,
    );
  });

  group('setComplete', () {
    test('verify setComplete() call 1 times with userId', () async {
      const fakeUserId = '123456';
      const fakeJourneyId = '111111';
      when(() => mockCurrentUserRepository.userId())
          .thenAnswer((_) async => fakeUserId);

      when(
            () => mockFeedbackStatusStorage.setComplete(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => Future.value());

      await feedbackJourneyService.setComplete(fakeJourneyId);

      verify(() => mockCurrentUserRepository.userId()).called(1);
      verify(
            () => mockFeedbackStatusStorage.setComplete(
          userId: fakeUserId,
          journeyId: fakeJourneyId,
        ),
      ).called(1);
    });
  });

  group('postponeJourney', () {
    test('verify postponeJourney() call 1 times with userId', () async {
      const fakeUserId = '123456';
      const fakeJourneyId = '111111';
      when(() => mockCurrentUserRepository.userId())
          .thenAnswer((_) async => fakeUserId);

      when(
            () => mockFeedbackStatusStorage.postpone(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
          postponedWhen: any(named: 'postponedWhen'),
        ),
      ).thenAnswer((_) async => Future.value());

      await feedbackJourneyService.postponeJourney(journeyId: fakeJourneyId);

      verify(() => mockCurrentUserRepository.userId()).called(1);
      verify(
            () => mockFeedbackStatusStorage.postpone(
          userId: fakeUserId,
          journeyId: fakeJourneyId,
          postponedWhen: any(named: 'postponedWhen'),
        ),
      ).called(1);
    });
  });

  group('getJourneyToComplete', () {
    const fakeUserId = '123456';
    const fakeJourneyIds = ['11111'];
    final fakePostponeWhen = DateTime.utc(2023);
    final fakeUserFeedbackJourneyStatus = UserFeedbackJourneyStatus(
      journeyId: fakeJourneyIds[0],
      attempt: 1,
      postponedWhen: fakePostponeWhen,
      isComplete: false,
    );

    test('verify getJourneyToComplete() call 1 times with userId', () async {
      when(() => mockCurrentUserRepository.userId())
          .thenAnswer((_) async => fakeUserId);
      when(
            () => mockFeedbackStatusStorage.getUserJourneys(userId: fakeUserId),
      ).thenAnswer((_) async => fakeJourneyIds);

      when(
            () => mockFeedbackStatusStorage.getStatus(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => fakeUserFeedbackJourneyStatus);

      when(() => mockFeedbackRepository.feedbackExist(fakeJourneyIds[0]))
          .thenAnswer((_) async => const Right(false));

      when(
            () => mockFeedbackStatusStorage.getStatus(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => fakeUserFeedbackJourneyStatus);

      final result =
      await feedbackJourneyService.getJourneyToComplete(userId: fakeUserId);

      verifyNever(() => mockCurrentUserRepository.userId());
      verify(
            () => mockFeedbackStatusStorage.getUserJourneys(
          userId: fakeUserId,
        ),
      ).called(1);
      expect(result, equals(fakeUserFeedbackJourneyStatus));
    });

    test('verify getJourneyToComplete() return null when userId is null',
            () async {
          when(() => mockCurrentUserRepository.userId())
              .thenAnswer((_) async => null);

          when(
                () => mockFeedbackStatusStorage.postpone(
              userId: any(named: 'userId'),
              journeyId: any(named: 'journeyId'),
              postponedWhen: any(named: 'postponedWhen'),
            ),
          ).thenAnswer((_) async => Future.value());

          final result = await feedbackJourneyService.getJourneyToComplete();
          expect(result, isNull);
        });
  });

  group('resetStatus', () {
    test('verify resetStatus() call 1 times with userId', () async {
      const fakeUserId = '123456';
      const fakeJournalId = '123456';
      when(() => mockCurrentUserRepository.userId())
          .thenAnswer((_) async => fakeUserId);
      when(
            () => mockFeedbackStatusStorage.resetStatus(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => Future.value());

      await feedbackJourneyService.resetStatus(fakeJournalId);

      verify(
            () => mockFeedbackStatusStorage.resetStatus(
          userId: fakeUserId,
          journeyId: fakeJournalId,
        ),
      ).called(1);
    });

    test('verify getJourneyToComplete() return null when userId is null',
            () async {
          when(() => mockCurrentUserRepository.userId())
              .thenAnswer((_) async => null);

          when(
                () => mockFeedbackStatusStorage.postpone(
              userId: any(named: 'userId'),
              journeyId: any(named: 'journeyId'),
              postponedWhen: any(named: 'postponedWhen'),
            ),
          ).thenAnswer((_) async => Future.value());

          final result = await feedbackJourneyService.getJourneyToComplete();
          expect(result, isNull);
        });
  });

  group('isEligibleToDisplay', () {
    const fakeUserId = '123456';
    const fakeJourneyIds = ['11111'];
    final fakePostponeWhen = DateTime.utc(2023);
    final fakeUserFeedbackJourneyStatus = UserFeedbackJourneyStatus(
      journeyId: fakeJourneyIds[0],
      attempt: 1,
      postponedWhen: fakePostponeWhen,
      isComplete: false,
    );

    test('verify status is null then return true', () async {
      when(() => mockFeedbackRepository.feedbackExist(fakeJourneyIds.first))
          .thenAnswer((_) async => const Right(false));

      when(() => mockCurrentUserRepository.userId())
          .thenAnswer((_) async => fakeUserId);

      when(
            () => mockFeedbackStatusStorage.getStatus(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => Future.value());

      final result =
      await feedbackJourneyService.isEligibleToDisplay(fakeJourneyIds.first);
      expect(result, true);
    });

    test('verify status is not null and valid for then return true', () async {
      final fakeUserFeedbackJourneyStatus = UserFeedbackJourneyStatus(
        journeyId: fakeJourneyIds.first,
        attempt: 1,
        postponedWhen: DateTime.utc(2023),
        isComplete: false,
      );
      when(() => mockFeedbackRepository.feedbackExist(fakeJourneyIds.first))
          .thenAnswer((_) async => const Right(false));

      when(
            () => mockFeedbackStatusStorage.getStatus(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => fakeUserFeedbackJourneyStatus);

      when(() => mockCurrentUserRepository.userId())
          .thenAnswer((_) async => fakeUserId);

      final result =
      await feedbackJourneyService.isEligibleToDisplay(fakeJourneyIds.first);
      expect(result, true);
    });

    test('verify isEligibleToDisplay() is false', () async {
      when(() => mockFeedbackRepository.feedbackExist(fakeJourneyIds.first))
          .thenAnswer((_) async => const Right(true));

      when(
            () => mockFeedbackStatusStorage.getStatus(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => fakeUserFeedbackJourneyStatus);

      final result2 =
      await feedbackJourneyService.isEligibleToDisplay(fakeJourneyIds.first);
      expect(result2, false);
    });
  });

  group('getJourneyStatus', () {
    const fakeUserId = '123456';
    const fakeJourneyIds = ['11111'];
    final fakePostponeWhen = DateTime.utc(2023);
    final fakeUserFeedbackJourneyStatus = UserFeedbackJourneyStatus(
      journeyId: fakeJourneyIds[0],
      attempt: 1,
      postponedWhen: fakePostponeWhen,
      isComplete: false,
    );

    test('should return the status when userId is valid', () async {
      when(() => mockCurrentUserRepository.userId())
          .thenAnswer((_) async => fakeUserId);
      when(
            () => mockFeedbackStatusStorage.getStatus(
          userId: any(named: 'userId'),
          journeyId: any(named: 'journeyId'),
        ),
      ).thenAnswer((_) async => fakeUserFeedbackJourneyStatus);

      final result =
      await feedbackJourneyService.getJourneyStatus(fakeJourneyIds[0]);

      expect(result, fakeUserFeedbackJourneyStatus);
      verify(
            () => mockFeedbackStatusStorage.getStatus(
          userId: fakeUserId,
          journeyId: fakeJourneyIds[0],
        ),
      ).called(1);
    });
  });

  group('canRateApp', () {
    const daysPeriod = 365;
    const maxRatingCount = 3;

    test('should return false when there is an error (Left)', () async {
      when(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: any(named: 'daysPeriod')))
          .thenAnswer((_) async => const Left(FeedbackFailure.unexpected()));

      final result = await feedbackJourneyService.canRateApp();

      expect(result, false);
      verify(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: daysPeriod)).called(1);
    });

    test('should return true when rating count is less than maxRatingCount', () async {
      const ratingCount = 2;
      when(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: any(named: 'daysPeriod')))
          .thenAnswer((_) async => const Right(ratingCount));

      final result = await feedbackJourneyService.canRateApp();

      expect(result, true);
      verify(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: daysPeriod)).called(1);
    });

    test('should return false when rating count is equal to maxRatingCount', () async {
      when(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: any(named: 'daysPeriod')))
          .thenAnswer((_) async => const Right(maxRatingCount));

      final result = await feedbackJourneyService.canRateApp();

      expect(result, false);
      verify(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: daysPeriod)).called(1);
    });

    test('should return false when rating count is greater than maxRatingCount', () async {
      const ratingCount = 5;
      when(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: any(named: 'daysPeriod')))
          .thenAnswer((_) async => const Right(ratingCount));

      final result = await feedbackJourneyService.canRateApp();

      expect(result, false);
      verify(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: daysPeriod)).called(1);
    });

    test('should use default values when no parameters are provided', () async {
      const ratingCount = 0;
      when(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: any(named: 'daysPeriod')))
          .thenAnswer((_) async => const Right(ratingCount));

      final result = await feedbackJourneyService.canRateApp();

      expect(result, true);
      verify(() => mockFeedbackRepository.getAppFeedbackRatingCount(daysPeriod: daysPeriod)).called(1);
    });
  });
}
