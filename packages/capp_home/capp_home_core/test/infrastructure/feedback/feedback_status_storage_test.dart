import 'package:capp_home_core/capp_home_core.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:mocktail/mocktail.dart';

import '../../capp_home_shared_mocks.dart';

void main() {
  late GmaStorageProvider mockStorage;
  late FeedbackStatusStorage feedbackStatusStorage;

  setUpAll(() {
    mockStorage = MockGmaStorageProvider();
    feedbackStatusStorage = FeedbackStatusStorage(storage: mockStorage);
  });

  group('getStatus', () {
    const userId = 'user123';
    const journeyId = 'journey123';

    test(
        'should return UserFeedbackJourneyStatus when storage returns valid data',
            () async {
          final userFeedbackStatus = UserFeedbackJourneyStatus();
          when(
                () => mockStorage.get<UserFeedbackJourneyStatus?>(
              any(),
              fromMap: any(named: 'fromMap'),
            ),
          ).thenAnswer((_) async => userFeedbackStatus);

          final result = await feedbackStatusStorage.getStatus(
            userId: userId,
            journeyId: journeyId,
          );
          expect(result, userFeedbackStatus);
        });

    test('should return null when storage returns null', () async {
      when(
            () => mockStorage.get<UserFeedbackJourneyStatus?>(
          any(),
          fromMap: any(named: 'fromMap'),
        ),
      ).thenAnswer((_) async => null);

      final result = await feedbackStatusStorage.getStatus(
        userId: userId,
        journeyId: journeyId,
      );

      expect(result, isNull);
    });
  });

  group('resetStatus', () {
    const userId = 'user123';
    const journeyId = 'journey123';

    test('should call storage.delete with correct key', () async {
      when(() => mockStorage.delete(any()))
          .thenAnswer((_) async => Future.value());
      await feedbackStatusStorage.resetStatus(userId: userId, journeyId: journeyId);
      const expectedKey = 'user:$userId;journey:$journeyId';
      verify(() => mockStorage.delete(expectedKey)).called(1);
    });

    test('should complete without throwing any error', () async {
      when(() => mockStorage.delete(any()))
          .thenAnswer((_) async => Future.value());
      final result = feedbackStatusStorage.resetStatus(
        userId: userId,
        journeyId: journeyId,
      );
      expect(result, completes);
    });
  });
}
