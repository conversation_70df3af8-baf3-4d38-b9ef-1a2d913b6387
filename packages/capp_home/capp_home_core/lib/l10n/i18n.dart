// DO NOT EDIT. This is code generated via package:gen_lang/generate.dart

import 'dart:async';

import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:multiple_localization/multiple_localization.dart';

import 'messages_all.dart';

class L10nCappHome {
  static Map<String, Map<String, String>> translations = {};
  static late Locale _locale;
  static bool showKeys = false;

  String? overridenTranslation(
      String key, String languageCode, String? countryCode) {
    var languageTag = getLanguageTag(languageCode, countryCode);
    var languageTranslations = translations[languageTag];
    if (languageTranslations != null &&
        languageTranslations.containsKey(key) &&
        languageTranslations[key]!.isNotEmpty) {
      return languageTranslations[key]!;
    }

    return null;
  }

  String getLanguageTag(String languageCode, String? countryCode) {
    if (countryCode == null) {
      return languageCode;
    }

    return '$languageCode-$countryCode';
  }

  static const GeneratedLocalizationsDelegate delegate = GeneratedLocalizationsDelegate();

  static L10nCappHome of(BuildContext context) {
    return Localizations.of<L10nCappHome>(context, L10nCappHome)!;
  }
  
  static L10nCappHome load(Locale locale) {
    _locale = locale;
    return L10nCappHome();
  }
  
  String get applianceProtection {
    if(showKeys){
      return 'capp_home.appliance_protection';
    }
    var ot = overridenTranslation(
        'capp_home.appliance_protection', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo hiểm trang thiết bị nội thất", name: 'capp_home.appliance_protection');
    return result != '' ? result : 'appliance_protection';
  }

  String get bills {
    if(showKeys){
      return 'capp_home.bills';
    }
    var ot = overridenTranslation(
        'capp_home.bills', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hóa đơn", name: 'capp_home.bills');
    return result != '' ? result : 'bills';
  }

  String get cancel {
    if(showKeys){
      return 'capp_home.cancel';
    }
    var ot = overridenTranslation(
        'capp_home.cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hủy", name: 'capp_home.cancel');
    return result != '' ? result : 'cancel';
  }

  String get cappMainRecoveredPasswordTitle {
    if(showKeys){
      return 'capp_home.capp_main__recovered_password_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__recovered_password_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mật khẩu của bạn đã được khôi phục thành công", name: 'capp_home.capp_main__recovered_password_title');
    return result != '' ? result : 'capp_main__recovered_password_title';
  }

  String get care360 {
    if(showKeys){
      return 'capp_home.care360';
    }
    var ot = overridenTranslation(
        'capp_home.care360', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Care 360", name: 'capp_home.care360');
    return result != '' ? result : 'care360';
  }

  String get createAvatar {
    if(showKeys){
      return 'capp_home.create_avatar';
    }
    var ot = overridenTranslation(
        'capp_home.create_avatar', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tạo ảnh đại diện", name: 'capp_home.create_avatar');
    return result != '' ? result : 'create_avatar';
  }

  String get creditCards {
    if(showKeys){
      return 'capp_home.credit_cards';
    }
    var ot = overridenTranslation(
        'capp_home.credit_cards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ tín dụng", name: 'capp_home.credit_cards');
    return result != '' ? result : 'credit_cards';
  }

  String get digitalGold {
    if(showKeys){
      return 'capp_home.digital_gold';
    }
    var ot = overridenTranslation(
        'capp_home.digital_gold', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vàng kĩ thuật số", name: 'capp_home.digital_gold');
    return result != '' ? result : 'digital_gold';
  }

  String get discounts {
    if(showKeys){
      return 'capp_home.discounts';
    }
    var ot = overridenTranslation(
        'capp_home.discounts', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giảm giá", name: 'capp_home.discounts');
    return result != '' ? result : 'discounts';
  }

  String get electricity {
    if(showKeys){
      return 'capp_home.electricity';
    }
    var ot = overridenTranslation(
        'capp_home.electricity', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tiền điện", name: 'capp_home.electricity');
    return result != '' ? result : 'electricity';
  }

  String get finishAvatar {
    if(showKeys){
      return 'capp_home.finish_avatar';
    }
    var ot = overridenTranslation(
        'capp_home.finish_avatar', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoàn tất tạo ảnh đại diện", name: 'capp_home.finish_avatar');
    return result != '' ? result : 'finish_avatar';
  }

  String get finishDeals {
    if(showKeys){
      return 'capp_home.finish_deals';
    }
    var ot = overridenTranslation(
        'capp_home.finish_deals', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục tìm kiếm các ưu đãi hấp dẫn", name: 'capp_home.finish_deals');
    return result != '' ? result : 'finish_deals';
  }

  String get finishNow {
    if(showKeys){
      return 'capp_home.finish_now';
    }
    var ot = overridenTranslation(
        'capp_home.finish_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục", name: 'capp_home.finish_now');
    return result != '' ? result : 'finish_now';
  }

  String get finishWizard {
    if(showKeys){
      return 'capp_home.finish_wizard';
    }
    var ot = overridenTranslation(
        'capp_home.finish_wizard', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục hành trình bạn đã dừng lại lần trước", name: 'capp_home.finish_wizard');
    return result != '' ? result : 'finish_wizard';
  }

  String get freeCreditScore {
    if(showKeys){
      return 'capp_home.free_credit_score';
    }
    var ot = overridenTranslation(
        'capp_home.free_credit_score', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Điểm tín dụng miễn phí", name: 'capp_home.free_credit_score');
    return result != '' ? result : 'free_credit_score';
  }

  String get gamesBannerTitle {
    if(showKeys){
      return 'capp_home.games_banner_title';
    }
    var ot = overridenTranslation(
        'capp_home.games_banner_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Game hot của tháng", name: 'capp_home.games_banner_title');
    return result != '' ? result : 'games_banner_title';
  }

  String get gamesSection {
    if(showKeys){
      return 'capp_home.games_section';
    }
    var ot = overridenTranslation(
        'capp_home.games_section', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Play for Fun", name: 'capp_home.games_section');
    return result != '' ? result : 'games_section';
  }

  String get gamingVoucher {
    if(showKeys){
      return 'capp_home.gaming_voucher';
    }
    var ot = overridenTranslation(
        'capp_home.gaming_voucher', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phiếu chơi game", name: 'capp_home.gaming_voucher');
    return result != '' ? result : 'gaming_voucher';
  }

  String get getALoan {
    if(showKeys){
      return 'capp_home.get_a_loan';
    }
    var ot = overridenTranslation(
        'capp_home.get_a_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng ký vay", name: 'capp_home.get_a_loan');
    return result != '' ? result : 'get_a_loan';
  }

  String get goShoppingMessage {
    if(showKeys){
      return 'capp_home.go_shopping_message';
    }
    var ot = overridenTranslation(
        'capp_home.go_shopping_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hãy để chúng tôi hiểu bạn hơn bằng việc thiết lập sở thích mua sắm dành riêng cho bạn hoặc có thể mua hàng ngay bạn nhé", name: 'capp_home.go_shopping_message');
    return result != '' ? result : 'go_shopping_message';
  }

  String get goShopping {
    if(showKeys){
      return 'capp_home.go_shopping';
    }
    var ot = overridenTranslation(
        'capp_home.go_shopping', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mua hàng", name: 'capp_home.go_shopping');
    return result != '' ? result : 'go_shopping';
  }

  String get goldLoan {
    if(showKeys){
      return 'capp_home.gold_loan';
    }
    var ot = overridenTranslation(
        'capp_home.gold_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay đảm bảo bằng vàng", name: 'capp_home.gold_loan');
    return result != '' ? result : 'gold_loan';
  }

  String get gotItButton {
    if(showKeys){
      return 'capp_home.got_it_button';
    }
    var ot = overridenTranslation(
        'capp_home.got_it_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã hiểu", name: 'capp_home.got_it_button');
    return result != '' ? result : 'got_it_button';
  }

  String get governmentTax {
    if(showKeys){
      return 'capp_home.government_tax';
    }
    var ot = overridenTranslation(
        'capp_home.government_tax', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thuế chính phủ và doanh thu ngoài thuế", name: 'capp_home.government_tax');
    return result != '' ? result : 'government_tax';
  }

  String get healthInsurance {
    if(showKeys){
      return 'capp_home.health_insurance';
    }
    var ot = overridenTranslation(
        'capp_home.health_insurance', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo hiểm y tế", name: 'capp_home.health_insurance');
    return result != '' ? result : 'health_insurance';
  }

  String get healthServices {
    if(showKeys){
      return 'capp_home.health_services';
    }
    var ot = overridenTranslation(
        'capp_home.health_services', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Các dịch vụ sức khoẻ", name: 'capp_home.health_services');
    return result != '' ? result : 'health_services';
  }

  String get helpdeskCallNumber {
    if(showKeys){
      return 'capp_home.helpdesk_call_number';
    }
    var ot = overridenTranslation(
        'capp_home.helpdesk_call_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("**********", name: 'capp_home.helpdesk_call_number');
    return result != '' ? result : 'helpdesk_call_number';
  }

  String get homeApplianceProtection {
    if(showKeys){
      return 'capp_home.home_appliance_protection';
    }
    var ot = overridenTranslation(
        'capp_home.home_appliance_protection', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo hiểm trang thiết bị nội thất", name: 'capp_home.home_appliance_protection');
    return result != '' ? result : 'home_appliance_protection';
  }

  String get homeLoan {
    if(showKeys){
      return 'capp_home.home_loan';
    }
    var ot = overridenTranslation(
        'capp_home.home_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay mua nhà", name: 'capp_home.home_loan');
    return result != '' ? result : 'home_loan';
  }

  String get hotDeals {
    if(showKeys){
      return 'capp_home.hot_deals';
    }
    var ot = overridenTranslation(
        'capp_home.hot_deals', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi lớn", name: 'capp_home.hot_deals');
    return result != '' ? result : 'hot_deals';
  }

  String get instalmentDetails {
    if(showKeys){
      return 'capp_home.instalment_details';
    }
    var ot = overridenTranslation(
        'capp_home.instalment_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết trả góp", name: 'capp_home.instalment_details');
    return result != '' ? result : 'instalment_details';
  }

  String get insurance {
    if(showKeys){
      return 'capp_home.insurance';
    }
    var ot = overridenTranslation(
        'capp_home.insurance', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo hiểm", name: 'capp_home.insurance');
    return result != '' ? result : 'insurance';
  }

  String get internetAndTv {
    if(showKeys){
      return 'capp_home.internet_and_TV';
    }
    var ot = overridenTranslation(
        'capp_home.internet_and_TV', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Internet và cáp TV", name: 'capp_home.internet_and_TV');
    return result != '' ? result : 'internet_and_TV';
  }

  String get loads {
    if(showKeys){
      return 'capp_home.loads';
    }
    var ot = overridenTranslation(
        'capp_home.loads', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nạp tiền điện thoại", name: 'capp_home.loads');
    return result != '' ? result : 'loads';
  }

  String get loanPayment {
    if(showKeys){
      return 'capp_home.loan_payment';
    }
    var ot = overridenTranslation(
        'capp_home.loan_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán khoản vay", name: 'capp_home.loan_payment');
    return result != '' ? result : 'loan_payment';
  }

  String get loginRegister {
    if(showKeys){
      return 'capp_home.login_register';
    }
    var ot = overridenTranslation(
        'capp_home.login_register', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập / Đăng ký", name: 'capp_home.login_register');
    return result != '' ? result : 'login_register';
  }

  String get myHomeCredit {
    if(showKeys){
      return 'capp_home.my_home_credit';
    }
    var ot = overridenTranslation(
        'capp_home.my_home_credit', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trang chủ", name: 'capp_home.my_home_credit');
    return result != '' ? result : 'my_home_credit';
  }

  String get myLoans {
    if(showKeys){
      return 'capp_home.my_loans';
    }
    var ot = overridenTranslation(
        'capp_home.my_loans', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quản lý khoản vay", name: 'capp_home.my_loans');
    return result != '' ? result : 'my_loans';
  }

  String get noThanks {
    if(showKeys){
      return 'capp_home.no_thanks';
    }
    var ot = overridenTranslation(
        'capp_home.no_thanks', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không, cảm ơn", name: 'capp_home.no_thanks');
    return result != '' ? result : 'no_thanks';
  }

  String get no {
    if(showKeys){
      return 'capp_home.no';
    }
    var ot = overridenTranslation(
        'capp_home.no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không", name: 'capp_home.no');
    return result != '' ? result : 'no';
  }

  String get notNow {
    if(showKeys){
      return 'capp_home.not_now';
    }
    var ot = overridenTranslation(
        'capp_home.not_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không phải bây giờ", name: 'capp_home.not_now');
    return result != '' ? result : 'not_now';
  }

  String get nowIKnowABitAboutYou {
    if(showKeys){
      return 'capp_home.now_i_know_a_bit_about_you';
    }
    var ot = overridenTranslation(
        'capp_home.now_i_know_a_bit_about_you', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giờ tôi đã biết một chút về bạn. Hãy tạo ảnh đại diện để nhận thêm phần thưởng và mở khóa những tính năng khác của ứng dụng", name: 'capp_home.now_i_know_a_bit_about_you');
    return result != '' ? result : 'now_i_know_a_bit_about_you';
  }

  String get okGotIt {
    if(showKeys){
      return 'capp_home.ok_got_it';
    }
    var ot = overridenTranslation(
        'capp_home.ok_got_it', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã hiểu", name: 'capp_home.ok_got_it');
    return result != '' ? result : 'ok_got_it';
  }

  String get otherServices {
    if(showKeys){
      return 'capp_home.other_services';
    }
    var ot = overridenTranslation(
        'capp_home.other_services', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Các dịch vụ khác", name: 'capp_home.other_services');
    return result != '' ? result : 'other_services';
  }

  String get personalize {
    if(showKeys){
      return 'capp_home.personalize';
    }
    var ot = overridenTranslation(
        'capp_home.personalize', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cài đặt riêng của bạn", name: 'capp_home.personalize');
    return result != '' ? result : 'personalize';
  }

  String get postpaidLoan {
    if(showKeys){
      return 'capp_home.postpaid_loan';
    }
    var ot = overridenTranslation(
        'capp_home.postpaid_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay trả sau", name: 'capp_home.postpaid_loan');
    return result != '' ? result : 'postpaid_loan';
  }

  String get prepaidLoan {
    if(showKeys){
      return 'capp_home.prepaid_loan';
    }
    var ot = overridenTranslation(
        'capp_home.prepaid_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay trả trước", name: 'capp_home.prepaid_loan');
    return result != '' ? result : 'prepaid_loan';
  }

  String get rechargeYourPhone {
    if(showKeys){
      return 'capp_home.recharge_your_phone';
    }
    var ot = overridenTranslation(
        'capp_home.recharge_your_phone', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nạp tiền điện thoại", name: 'capp_home.recharge_your_phone');
    return result != '' ? result : 'recharge_your_phone';
  }

  String get reservationExpired {
    if(showKeys){
      return 'capp_home.reservation_expired';
    }
    var ot = overridenTranslation(
        'capp_home.reservation_expired', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đặt chỗ của bạn đã hết hạn :(", name: 'capp_home.reservation_expired');
    return result != '' ? result : 'reservation_expired';
  }

  String get reservationOf {
    if(showKeys){
      return 'capp_home.reservation_of';
    }
    var ot = overridenTranslation(
        'capp_home.reservation_of', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đặt trước", name: 'capp_home.reservation_of');
    return result != '' ? result : 'reservation_of';
  }

  String get selectNewProduct {
    if(showKeys){
      return 'capp_home.select_new_product';
    }
    var ot = overridenTranslation(
        'capp_home.select_new_product', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn một sản phẩm mới", name: 'capp_home.select_new_product');
    return result != '' ? result : 'select_new_product';
  }

  String get selfService {
    if(showKeys){
      return 'capp_home.self_service';
    }
    var ot = overridenTranslation(
        'capp_home.self_service', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tự phục vụ", name: 'capp_home.self_service');
    return result != '' ? result : 'self_service';
  }

  String get showAll {
    if(showKeys){
      return 'capp_home.show_all';
    }
    var ot = overridenTranslation(
        'capp_home.show_all', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hiển thị tất cả", name: 'capp_home.show_all');
    return result != '' ? result : 'show_all';
  }

  String get signNow {
    if(showKeys){
      return 'capp_home.sign_now';
    }
    var ot = overridenTranslation(
        'capp_home.sign_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ký ngay ", name: 'capp_home.sign_now');
    return result != '' ? result : 'sign_now';
  }

  String get streaming {
    if(showKeys){
      return 'capp_home.streaming';
    }
    var ot = overridenTranslation(
        'capp_home.streaming', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phát trực tuyến", name: 'capp_home.streaming');
    return result != '' ? result : 'streaming';
  }

  String get tipsAndStories {
    if(showKeys){
      return 'capp_home.tips_and_stories';
    }
    var ot = overridenTranslation(
        'capp_home.tips_and_stories', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tin tức và Khuyến Mãi", name: 'capp_home.tips_and_stories');
    return result != '' ? result : 'tips_and_stories';
  }

  String get twoWheelerInsurance {
    if(showKeys){
      return 'capp_home.two_wheeler_insurance';
    }
    var ot = overridenTranslation(
        'capp_home.two_wheeler_insurance', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo hiểm xe máy", name: 'capp_home.two_wheeler_insurance');
    return result != '' ? result : 'two_wheeler_insurance';
  }

  String get unlockInfobox {
    if(showKeys){
      return 'capp_home.unlock_infobox';
    }
    var ot = overridenTranslation(
        'capp_home.unlock_infobox', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập/ Đăng ký Home Zui để xem nội dung dành riêng cho bạn", name: 'capp_home.unlock_infobox');
    return result != '' ? result : 'unlock_infobox';
  }

  String get walletSecurity {
    if(showKeys){
      return 'capp_home.wallet_security';
    }
    var ot = overridenTranslation(
        'capp_home.wallet_security', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo mật ví", name: 'capp_home.wallet_security');
    return result != '' ? result : 'wallet_security';
  }

  String get water {
    if(showKeys){
      return 'capp_home.water';
    }
    var ot = overridenTranslation(
        'capp_home.water', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán nước", name: 'capp_home.water');
    return result != '' ? result : 'water';
  }

  String get yes {
    if(showKeys){
      return 'capp_home.yes';
    }
    var ot = overridenTranslation(
        'capp_home.yes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có", name: 'capp_home.yes');
    return result != '' ? result : 'yes';
  }

  String get allowCameraPermissionsToContinueAndroid {
    if(showKeys){
      return 'capp_home.allow_camera_permissions_to_continue_android';
    }
    var ot = overridenTranslation(
        'capp_home.allow_camera_permissions_to_continue_android', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Để tiếp tục, bạn cần cho phép quyền chụp ảnh theo đường dẫn trên điện thoại: Cài đặt -> Ứng dụng -> Home Credit", name: 'capp_home.allow_camera_permissions_to_continue_android');
    return result != '' ? result : 'allow_camera_permissions_to_continue_android';
  }

  String get allowCameraPermissionsToContinueIos {
    if(showKeys){
      return 'capp_home.allow_camera_permissions_to_continue_ios';
    }
    var ot = overridenTranslation(
        'capp_home.allow_camera_permissions_to_continue_ios', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Để tiếp tục, bạn cần cho phép quyền chụp ảnh theo đường dẫn trên điện thoại: Cài đặt-> Home Credit", name: 'capp_home.allow_camera_permissions_to_continue_ios');
    return result != '' ? result : 'allow_camera_permissions_to_continue_ios';
  }

  String get posLoanBannerTitle {
    if(showKeys){
      return 'capp_home.pos_loan_banner_title';
    }
    var ot = overridenTranslation(
        'capp_home.pos_loan_banner_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Check this out", name: 'capp_home.pos_loan_banner_title');
    return result != '' ? result : 'pos_loan_banner_title';
  }

  String get financialLiteracy {
    if(showKeys){
      return 'capp_home.financial_literacy';
    }
    var ot = overridenTranslation(
        'capp_home.financial_literacy', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kiến thức tài chính", name: 'capp_home.financial_literacy');
    return result != '' ? result : 'financial_literacy';
  }

  String get epfsInitTitle {
    if(showKeys){
      return 'capp_home.epfs_init_title';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_init_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Protect yourself", name: 'capp_home.epfs_init_title');
    return result != '' ? result : 'epfs_init_title';
  }

  String get epfsInitDesc {
    if(showKeys){
      return 'capp_home.epfs_init_desc';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_init_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Don’t let cybercriminals steal your personal information. Here are some useful tips on how to protect yourself and your hard-earned money from these threats.", name: 'capp_home.epfs_init_desc');
    return result != '' ? result : 'epfs_init_desc';
  }

  String get epfsInitFooter {
    if(showKeys){
      return 'capp_home.epfs_init_footer';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_init_footer', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("In Compliance with BSP Circular 1140 (Amendments to Regulations on Information Technology Risk Management) series of 2022", name: 'capp_home.epfs_init_footer');
    return result != '' ? result : 'epfs_init_footer';
  }

  String get epfsCompleteTitle {
    if(showKeys){
      return 'capp_home.epfs_complete_title';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_complete_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Yay! You’re almost there", name: 'capp_home.epfs_complete_title');
    return result != '' ? result : 'epfs_complete_title';
  }

  String get epfsCompleteDesc {
    if(showKeys){
      return 'capp_home.epfs_complete_desc';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_complete_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Complete Wais sa Home onboarding course to start enjoying Home Credit’s offers!", name: 'capp_home.epfs_complete_desc');
    return result != '' ? result : 'epfs_complete_desc';
  }

  String get epfsApproval {
    if(showKeys){
      return 'capp_home.epfs_approval';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_approval', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("I understand my roles and responsibilities to secure my transactions and personal information", name: 'capp_home.epfs_approval');
    return result != '' ? result : 'epfs_approval';
  }

  String get epfsGoNextButton {
    if(showKeys){
      return 'capp_home.epfs_go_next_button';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_go_next_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Go to Wais sa Home", name: 'capp_home.epfs_go_next_button');
    return result != '' ? result : 'epfs_go_next_button';
  }

  String get epfsFinishButton {
    if(showKeys){
      return 'capp_home.epfs_finish_button';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_finish_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Finish course", name: 'capp_home.epfs_finish_button');
    return result != '' ? result : 'epfs_finish_button';
  }

  String get epfsCompleteButton {
    if(showKeys){
      return 'capp_home.epfs_complete_button';
    }
    var ot = overridenTranslation(
        'capp_home.epfs_complete_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Complete course", name: 'capp_home.epfs_complete_button');
    return result != '' ? result : 'epfs_complete_button';
  }

  String get accountScreenTitle {
    if(showKeys){
      return 'capp_home.account_screen_title';
    }
    var ot = overridenTranslation(
        'capp_home.account_screen_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản", name: 'capp_home.account_screen_title');
    return result != '' ? result : 'account_screen_title';
  }

  String get assistantChallenges {
    if(showKeys){
      return 'capp_home.assistant_challenges';
    }
    var ot = overridenTranslation(
        'capp_home.assistant_challenges', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thử thách ", name: 'capp_home.assistant_challenges');
    return result != '' ? result : 'assistant_challenges';
  }

  String get mainTabAccount {
    if(showKeys){
      return 'capp_home.main_tab_account';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_account', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản", name: 'capp_home.main_tab_account');
    return result != '' ? result : 'main_tab_account';
  }

  String get mainTabDeals {
    if(showKeys){
      return 'capp_home.main_tab_deals';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_deals', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi", name: 'capp_home.main_tab_deals');
    return result != '' ? result : 'main_tab_deals';
  }

  String get mainTabHome {
    if(showKeys){
      return 'capp_home.main_tab_home';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_home', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trang chủ", name: 'capp_home.main_tab_home');
    return result != '' ? result : 'main_tab_home';
  }

  String get mainTabLoans {
    if(showKeys){
      return 'capp_home.main_tab_loans';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_loans', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay", name: 'capp_home.main_tab_loans');
    return result != '' ? result : 'main_tab_loans';
  }

  String get mainTabNearby {
    if(showKeys){
      return 'capp_home.main_tab_nearby';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_nearby', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Gần đây", name: 'capp_home.main_tab_nearby');
    return result != '' ? result : 'main_tab_nearby';
  }

  String get mainTabRewards {
    if(showKeys){
      return 'capp_home.main_tab_rewards';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_rewards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phần thưởng", name: 'capp_home.main_tab_rewards');
    return result != '' ? result : 'main_tab_rewards';
  }

  String get mainTabShop {
    if(showKeys){
      return 'capp_home.main_tab_shop';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_shop', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cửa hàng", name: 'capp_home.main_tab_shop');
    return result != '' ? result : 'main_tab_shop';
  }

  String get mainTabPromo {
    if(showKeys){
      return 'capp_home.main_tab_promo';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_promo', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khuyến mãi", name: 'capp_home.main_tab_promo');
    return result != '' ? result : 'main_tab_promo';
  }

  String get mainTabSuperCupo {
    if(showKeys){
      return 'capp_home.main_tab_super_cupo';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_super_cupo', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Home Zui", name: 'capp_home.main_tab_super_cupo');
    return result != '' ? result : 'main_tab_super_cupo';
  }

  String get mainTabActivity {
    if(showKeys){
      return 'capp_home.main_tab_activity';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_activity', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Activity", name: 'capp_home.main_tab_activity');
    return result != '' ? result : 'main_tab_activity';
  }

  String get mainTabContactUs {
    if(showKeys){
      return 'capp_home.main_tab_contact_us';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_contact_us', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Contact Us", name: 'capp_home.main_tab_contact_us');
    return result != '' ? result : 'main_tab_contact_us';
  }

  String get cappMainAssistantLoginRegister {
    if(showKeys){
      return 'capp_home.capp_main__assistant_login_register';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__assistant_login_register', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập / Đăng ký", name: 'capp_home.capp_main__assistant_login_register');
    return result != '' ? result : 'capp_main__assistant_login_register';
  }

  String get cappMainAssistantSubtitle {
    if(showKeys){
      return 'capp_home.capp_main__assistant_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__assistant_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn cần hỗ trợ gì?", name: 'capp_home.capp_main__assistant_subtitle');
    return result != '' ? result : 'capp_main__assistant_subtitle';
  }

  String get cappMainAssistantTitle {
    if(showKeys){
      return 'capp_home.capp_main__assistant_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__assistant_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Home Zui sẵn sàng hỗ trợ bạn!", name: 'capp_home.capp_main__assistant_title');
    return result != '' ? result : 'capp_main__assistant_title';
  }

  String get cappMainChatWithUs {
    if(showKeys){
      return 'capp_home.capp_main__chat_with_us';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__chat_with_us', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chat với chúng tôi", name: 'capp_home.capp_main__chat_with_us');
    return result != '' ? result : 'capp_main__chat_with_us';
  }

  String get cappMainContactHomeCredit {
    if(showKeys){
      return 'capp_home.capp_main__contact_home_credit';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__contact_home_credit', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Liên hệ Home Credit", name: 'capp_home.capp_main__contact_home_credit');
    return result != '' ? result : 'capp_main__contact_home_credit';
  }

  String get cappMainLoanRegistration {
    if(showKeys){
      return 'capp_home.capp_main__loan_registration';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__loan_registration', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng ký vay", name: 'capp_home.capp_main__loan_registration');
    return result != '' ? result : 'capp_main__loan_registration';
  }

  String get cappMainEmptyLoansButtonText {
    if(showKeys){
      return 'capp_home.capp_main__empty_loans_button_text';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__empty_loans_button_text', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập/Đăng ký", name: 'capp_home.capp_main__empty_loans_button_text');
    return result != '' ? result : 'capp_main__empty_loans_button_text';
  }

  String get cappMainEmptyLoansSubtitle {
    if(showKeys){
      return 'capp_home.capp_main__empty_loans_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__empty_loans_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập hoặc đăng ký để xem các khoản vay hiện có của bạn.", name: 'capp_home.capp_main__empty_loans_subtitle');
    return result != '' ? result : 'capp_main__empty_loans_subtitle';
  }

  String get cappMainEmptyLoansTitle {
    if(showKeys){
      return 'capp_home.capp_main__empty_loans_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__empty_loans_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn chưa có khoản vay nào", name: 'capp_home.capp_main__empty_loans_title');
    return result != '' ? result : 'capp_main__empty_loans_title';
  }

  String get cappMainEmptyOffersButtonText {
    if(showKeys){
      return 'capp_home.capp_main__empty_offers_button_text';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__empty_offers_button_text', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tìm cửa hàng đối tác gần nhất", name: 'capp_home.capp_main__empty_offers_button_text');
    return result != '' ? result : 'capp_main__empty_offers_button_text';
  }

  String get cappMainEmptyOffersButtonUrl {
    if(showKeys){
      return 'capp_home.capp_main__empty_offers_button_url';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__empty_offers_button_url', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("https://www.homecredit.vn/map", name: 'capp_home.capp_main__empty_offers_button_url');
    return result != '' ? result : 'capp_main__empty_offers_button_url';
  }

  String get cappMainEmptyOffersSubtitle {
    if(showKeys){
      return 'capp_home.capp_main__empty_offers_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__empty_offers_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Rất tiếc, chúng tôi chưa tìm thấy ưu đãi nào phù hợp với bạn", name: 'capp_home.capp_main__empty_offers_subtitle');
    return result != '' ? result : 'capp_main__empty_offers_subtitle';
  }

  String get cappMainEmptyOffersTitle {
    if(showKeys){
      return 'capp_home.capp_main__empty_offers_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__empty_offers_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không có ưu đãi nào", name: 'capp_home.capp_main__empty_offers_title');
    return result != '' ? result : 'capp_main__empty_offers_title';
  }

  String get cappMainExitAppTitle {
    if(showKeys){
      return 'capp_home.capp_main__exit_app_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__exit_app_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có chắc chắn muốn đóng ứng dụng không?", name: 'capp_home.capp_main__exit_app_title');
    return result != '' ? result : 'capp_main__exit_app_title';
  }

  String get cappMainFinanceAppbarTitle {
    if(showKeys){
      return 'capp_home.capp_main__finance_appbar_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__finance_appbar_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay", name: 'capp_home.capp_main__finance_appbar_title');
    return result != '' ? result : 'capp_main__finance_appbar_title';
  }

  String get cappMainGuestAssistantSubtitle {
    if(showKeys){
      return 'capp_home.capp_main__guest_assistant_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__guest_assistant_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập ngay để có thể sử dụng hết tất cả tính năng của ứng dụng", name: 'capp_home.capp_main__guest_assistant_subtitle');
    return result != '' ? result : 'capp_main__guest_assistant_subtitle';
  }

  String get cappMainGuestAssistantTitle {
    if(showKeys){
      return 'capp_home.capp_main__guest_assistant_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__guest_assistant_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chào mừng bạn đến với Home Credit", name: 'capp_home.capp_main__guest_assistant_title');
    return result != '' ? result : 'capp_main__guest_assistant_title';
  }

  String get cappMainLoanOffers {
    if(showKeys){
      return 'capp_home.capp_main__loan_offers';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__loan_offers', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Gói vay ưu đãi", name: 'capp_home.capp_main__loan_offers');
    return result != '' ? result : 'capp_main__loan_offers';
  }

  String get cappMainLoans {
    if(showKeys){
      return 'capp_home.capp_main__loans';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__loans', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quản lý khoản vay", name: 'capp_home.capp_main__loans');
    return result != '' ? result : 'capp_main__loans';
  }

  String get cappMainLoansGuestHplReloginPopupTitle {
    if(showKeys){
      return 'capp_home.capp_main__loans_guest_hpl_relogin_popup_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__loans_guest_hpl_relogin_popup_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông báo", name: 'capp_home.capp_main__loans_guest_hpl_relogin_popup_title');
    return result != '' ? result : 'capp_main__loans_guest_hpl_relogin_popup_title';
  }

  String get cappMainLoansGuestHplReloginPopupContent {
    if(showKeys){
      return 'capp_home.capp_main__loans_guest_hpl_relogin_popup_content';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__loans_guest_hpl_relogin_popup_content', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng đăng nhập bằng tài khoản bạn vừa tạo để tiếp tục trải nghiệm app", name: 'capp_home.capp_main__loans_guest_hpl_relogin_popup_content');
    return result != '' ? result : 'capp_main__loans_guest_hpl_relogin_popup_content';
  }

  String get cappMainLoansGuestHplReloginPopupLoginButton {
    if(showKeys){
      return 'capp_home.capp_main__loans_guest_hpl_relogin_popup_login_button';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__loans_guest_hpl_relogin_popup_login_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập", name: 'capp_home.capp_main__loans_guest_hpl_relogin_popup_login_button');
    return result != '' ? result : 'capp_main__loans_guest_hpl_relogin_popup_login_button';
  }

  String get cappMainMyCoupons {
    if(showKeys){
      return 'capp_home.capp_main__my_coupons';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__my_coupons', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phiếu giảm giá của tôi", name: 'capp_home.capp_main__my_coupons');
    return result != '' ? result : 'capp_main__my_coupons';
  }

  String get cappMainNoLoansSubtitle {
    if(showKeys){
      return 'capp_home.capp_main__no_loans_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__no_loans_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đây là chỗ bạn sẽ theo dõi khoản vay của mình", name: 'capp_home.capp_main__no_loans_subtitle');
    return result != '' ? result : 'capp_main__no_loans_subtitle';
  }

  String get cappMainNotificationsSubtitle {
    if(showKeys){
      return 'capp_home.capp_main__notifications_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__notifications_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cung cấp thêm thông tin để nhìn thấy khoản vay", name: 'capp_home.capp_main__notifications_subtitle');
    return result != '' ? result : 'capp_main__notifications_subtitle';
  }

  String get cappMainNotificationsTitle {
    if(showKeys){
      return 'capp_home.capp_main__notifications_title';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__notifications_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bấm vào đây nếu bạn không tìm thấy khoản vay", name: 'capp_home.capp_main__notifications_title');
    return result != '' ? result : 'capp_main__notifications_title';
  }

  String get cappMainNotifications {
    if(showKeys){
      return 'capp_home.capp_main__notifications';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__notifications', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hộp thư đến", name: 'capp_home.capp_main__notifications');
    return result != '' ? result : 'capp_main__notifications';
  }

  String get cappMainOffersForYou {
    if(showKeys){
      return 'capp_home.capp_main__offers_for_you';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__offers_for_you', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi dành cho bạn", name: 'capp_home.capp_main__offers_for_you');
    return result != '' ? result : 'capp_main__offers_for_you';
  }

  String get cappMainOffers {
    if(showKeys){
      return 'capp_home.capp_main__offers';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__offers', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sản phẩm ưu đãi", name: 'capp_home.capp_main__offers');
    return result != '' ? result : 'capp_main__offers';
  }

  String get cappMainOffersSubtitle {
    if(showKeys){
      return 'capp_home.capp_main__offers_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__offers_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message(" ", name: 'capp_home.capp_main__offers_subtitle');
    return result != '' ? result : 'capp_main__offers_subtitle';
  }

  String get cappMainPayEmi {
    if(showKeys){
      return 'capp_home.capp_main__pay_emi';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__pay_emi', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán khoản vay", name: 'capp_home.capp_main__pay_emi');
    return result != '' ? result : 'capp_main__pay_emi';
  }

  String get cappMainSeeMyChallenges {
    if(showKeys){
      return 'capp_home.capp_main__see_my_challenges';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__see_my_challenges', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem thử thách", name: 'capp_home.capp_main__see_my_challenges');
    return result != '' ? result : 'capp_main__see_my_challenges';
  }

  String get cappMainSeeMyRewards {
    if(showKeys){
      return 'capp_home.capp_main__see_my_rewards';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__see_my_rewards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem điểm thưởng", name: 'capp_home.capp_main__see_my_rewards');
    return result != '' ? result : 'capp_main__see_my_rewards';
  }

  String get cappMainSelectLoan {
    if(showKeys){
      return 'capp_home.capp_main__select_loan';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__select_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn sản phẩm vay", name: 'capp_home.capp_main__select_loan');
    return result != '' ? result : 'capp_main__select_loan';
  }

  String get cappMainWarningzone {
    if(showKeys){
      return 'capp_home.capp_main__warningzone';
    }
    var ot = overridenTranslation(
        'capp_home.capp_main__warningzone', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Góc cảnh báo", name: 'capp_home.capp_main__warningzone');
    return result != '' ? result : 'capp_main__warningzone';
  }

  String get close {
    if(showKeys){
      return 'capp_home.close';
    }
    var ot = overridenTranslation(
        'capp_home.close', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đóng", name: 'capp_home.close');
    return result != '' ? result : 'close';
  }

  String get commonAdd {
    if(showKeys){
      return 'capp_home.common__add';
    }
    var ot = overridenTranslation(
        'capp_home.common__add', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thêm vào", name: 'capp_home.common__add');
    return result != '' ? result : 'common__add';
  }

  String get commonBoost {
    if(showKeys){
      return 'capp_home.common__boost';
    }
    var ot = overridenTranslation(
        'capp_home.common__boost', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay ngay", name: 'capp_home.common__boost');
    return result != '' ? result : 'common__boost';
  }

  String get commonChallenges {
    if(showKeys){
      return 'capp_home.common__challenges';
    }
    var ot = overridenTranslation(
        'capp_home.common__challenges', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhiệm vụ", name: 'capp_home.common__challenges');
    return result != '' ? result : 'common__challenges';
  }

  String get commonChllngs {
    if(showKeys){
      return 'capp_home.common__chllngs';
    }
    var ot = overridenTranslation(
        'capp_home.common__chllngs', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhiệm vụ", name: 'capp_home.common__chllngs');
    return result != '' ? result : 'common__chllngs';
  }

  String get commonContact {
    if(showKeys){
      return 'capp_home.common__contact';
    }
    var ot = overridenTranslation(
        'capp_home.common__contact', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Liên hệ", name: 'capp_home.common__contact');
    return result != '' ? result : 'common__contact';
  }

  String get commonEdit {
    if(showKeys){
      return 'capp_home.common__edit';
    }
    var ot = overridenTranslation(
        'capp_home.common__edit', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chỉnh sửa", name: 'capp_home.common__edit');
    return result != '' ? result : 'common__edit';
  }

  String get commonGame {
    if(showKeys){
      return 'capp_home.common__game';
    }
    var ot = overridenTranslation(
        'capp_home.common__game', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trò chơi", name: 'capp_home.common__game');
    return result != '' ? result : 'common__game';
  }

  String get commonGoals {
    if(showKeys){
      return 'capp_home.common__goals';
    }
    var ot = overridenTranslation(
        'capp_home.common__goals', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhiệm vụ", name: 'capp_home.common__goals');
    return result != '' ? result : 'common__goals';
  }

  String get commonHome {
    if(showKeys){
      return 'capp_home.common__home';
    }
    var ot = overridenTranslation(
        'capp_home.common__home', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trang chủ", name: 'capp_home.common__home');
    return result != '' ? result : 'common__home';
  }

  String get commonInbox {
    if(showKeys){
      return 'capp_home.common__inbox';
    }
    var ot = overridenTranslation(
        'capp_home.common__inbox', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông báo", name: 'capp_home.common__inbox');
    return result != '' ? result : 'common__inbox';
  }

  String get commonNo {
    if(showKeys){
      return 'capp_home.common__no';
    }
    var ot = overridenTranslation(
        'capp_home.common__no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không", name: 'capp_home.common__no');
    return result != '' ? result : 'common__no';
  }

  String get commonProfile {
    if(showKeys){
      return 'capp_home.common__profile';
    }
    var ot = overridenTranslation(
        'capp_home.common__profile', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hồ sơ", name: 'capp_home.common__profile');
    return result != '' ? result : 'common__profile';
  }

  String get commonQr {
    if(showKeys){
      return 'capp_home.common__qr';
    }
    var ot = overridenTranslation(
        'capp_home.common__qr', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("QR", name: 'capp_home.common__qr');
    return result != '' ? result : 'common__qr';
  }

  String get commonRemove {
    if(showKeys){
      return 'capp_home.common__remove';
    }
    var ot = overridenTranslation(
        'capp_home.common__remove', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bỏ ra", name: 'capp_home.common__remove');
    return result != '' ? result : 'common__remove';
  }

  String get commonRepay {
    if(showKeys){
      return 'capp_home.common__repay';
    }
    var ot = overridenTranslation(
        'capp_home.common__repay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán", name: 'capp_home.common__repay');
    return result != '' ? result : 'common__repay';
  }

  String get commonSave {
    if(showKeys){
      return 'capp_home.common__save';
    }
    var ot = overridenTranslation(
        'capp_home.common__save', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Lưu", name: 'capp_home.common__save');
    return result != '' ? result : 'common__save';
  }

  String get commonScan {
    if(showKeys){
      return 'capp_home.common__scan';
    }
    var ot = overridenTranslation(
        'capp_home.common__scan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quét", name: 'capp_home.common__scan');
    return result != '' ? result : 'common__scan';
  }

  String get commonWallet {
    if(showKeys){
      return 'capp_home.common__wallet';
    }
    var ot = overridenTranslation(
        'capp_home.common__wallet', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ví", name: 'capp_home.common__wallet');
    return result != '' ? result : 'common__wallet';
  }

  String get commonYes {
    if(showKeys){
      return 'capp_home.common__yes';
    }
    var ot = overridenTranslation(
        'capp_home.common__yes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đúng", name: 'capp_home.common__yes');
    return result != '' ? result : 'common__yes';
  }

  String get commonNotNow {
    if(showKeys){
      return 'capp_home.common_not_now';
    }
    var ot = overridenTranslation(
        'capp_home.common_not_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không phải bây giờ", name: 'capp_home.common_not_now');
    return result != '' ? result : 'common_not_now';
  }

  String get contractHistory {
    if(showKeys){
      return 'capp_home.contract_history';
    }
    var ot = overridenTranslation(
        'capp_home.contract_history', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Lịch sử khoản vay", name: 'capp_home.contract_history');
    return result != '' ? result : 'contract_history';
  }

  String get electricityPayment {
    if(showKeys){
      return 'capp_home.electricity_payment';
    }
    var ot = overridenTranslation(
        'capp_home.electricity_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tiền điện", name: 'capp_home.electricity_payment');
    return result != '' ? result : 'electricity_payment';
  }

  String get errorCheckNetworkTitle {
    if(showKeys){
      return 'capp_home.error_check_network_title';
    }
    var ot = overridenTranslation(
        'capp_home.error_check_network_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Something went wrong", name: 'capp_home.error_check_network_title');
    return result != '' ? result : 'error_check_network_title';
  }

  String get errorCheckNetworkBody {
    if(showKeys){
      return 'capp_home.error_check_network_body';
    }
    var ot = overridenTranslation(
        'capp_home.error_check_network_body', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Please check your internet connection and try again.", name: 'capp_home.error_check_network_body');
    return result != '' ? result : 'error_check_network_body';
  }

  String get feedbackCompleteDone {
    if(showKeys){
      return 'capp_home.feedback_complete_done';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_complete_done', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác nhận", name: 'capp_home.feedback_complete_done');
    return result != '' ? result : 'feedback_complete_done';
  }

  String get feedbackCompleteSubtitle {
    if(showKeys){
      return 'capp_home.feedback_complete_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_complete_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Việc phản hồi sẽ giúp chúng tôi cải thiện chất lượng sản phẩm tốt hơn", name: 'capp_home.feedback_complete_subtitle');
    return result != '' ? result : 'feedback_complete_subtitle';
  }

  String get feedbackCompleteTitle {
    if(showKeys){
      return 'capp_home.feedback_complete_title';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_complete_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cảm ơn phản hồi của bạn", name: 'capp_home.feedback_complete_title');
    return result != '' ? result : 'feedback_complete_title';
  }

  String get feedbackFormHeader {
    if(showKeys){
      return 'capp_home.feedback_form_header';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_form_header', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có hài lòng với ứng dụng Home Credit không?", name: 'capp_home.feedback_form_header');
    return result != '' ? result : 'feedback_form_header';
  }

  String get feedbackFormHint {
    if(showKeys){
      return 'capp_home.feedback_form_hint';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_form_hint', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hãy cho chúng tôi biết ý kiến khác của bạn!", name: 'capp_home.feedback_form_hint');
    return result != '' ? result : 'feedback_form_hint';
  }

  String get feedbackFormSubtitle {
    if(showKeys){
      return 'capp_home.feedback_form_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_form_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn một hoặc nhiều tiêu chí dưới đây", name: 'capp_home.feedback_form_subtitle');
    return result != '' ? result : 'feedback_form_subtitle';
  }

  String get feedbackFormTitle {
    if(showKeys){
      return 'capp_home.feedback_form_title';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_form_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chúng tôi cần cải thiện điều gì?", name: 'capp_home.feedback_form_title');
    return result != '' ? result : 'feedback_form_title';
  }

  String get feedbackFormTitleGreat {
    if(showKeys){
      return 'capp_home.feedback_form_title_great';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_form_title_great', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chia sẻ lý do bạn hài lòng với trải nghiệm này", name: 'capp_home.feedback_form_title_great');
    return result != '' ? result : 'feedback_form_title_great';
  }

  String get feedbackFormTitlePositive {
    if(showKeys){
      return 'capp_home.feedback_form_title_positive';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_form_title_positive', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chia sẻ lý do bạn thích?", name: 'capp_home.feedback_form_title_positive');
    return result != '' ? result : 'feedback_form_title_positive';
  }

  String get feedbackFormWarning {
    if(showKeys){
      return 'capp_home.feedback_form_warning';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_form_warning', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không chia sẻ bất kỳ thông tin cá nhân nào", name: 'capp_home.feedback_form_warning');
    return result != '' ? result : 'feedback_form_warning';
  }

  String get feedback {
    if(showKeys){
      return 'capp_home.feedback';
    }
    var ot = overridenTranslation(
        'capp_home.feedback', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chia sẻ phản hồi", name: 'capp_home.feedback');
    return result != '' ? result : 'feedback';
  }

  String get feedbackRating1 {
    if(showKeys){
      return 'capp_home.feedback_rating_1';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_rating_1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Rất tệ", name: 'capp_home.feedback_rating_1');
    return result != '' ? result : 'feedback_rating_1';
  }

  String get feedbackRating2 {
    if(showKeys){
      return 'capp_home.feedback_rating_2';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_rating_2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tệ", name: 'capp_home.feedback_rating_2');
    return result != '' ? result : 'feedback_rating_2';
  }

  String get feedbackRating3 {
    if(showKeys){
      return 'capp_home.feedback_rating_3';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_rating_3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bình thường", name: 'capp_home.feedback_rating_3');
    return result != '' ? result : 'feedback_rating_3';
  }

  String get feedbackRating4 {
    if(showKeys){
      return 'capp_home.feedback_rating_4';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_rating_4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tốt", name: 'capp_home.feedback_rating_4');
    return result != '' ? result : 'feedback_rating_4';
  }

  String get feedbackRating5 {
    if(showKeys){
      return 'capp_home.feedback_rating_5';
    }
    var ot = overridenTranslation(
        'capp_home.feedback_rating_5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Rất tốt!", name: 'capp_home.feedback_rating_5');
    return result != '' ? result : 'feedback_rating_5';
  }

  String get giftCard {
    if(showKeys){
      return 'capp_home.gift_card';
    }
    var ot = overridenTranslation(
        'capp_home.gift_card', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Gift Card", name: 'capp_home.gift_card');
    return result != '' ? result : 'gift_card';
  }

  String get menuAboutus {
    if(showKeys){
      return 'capp_home.menu_aboutus';
    }
    var ot = overridenTranslation(
        'capp_home.menu_aboutus', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Về chúng tôi", name: 'capp_home.menu_aboutus');
    return result != '' ? result : 'menu_aboutus';
  }

  String get menuContactus {
    if(showKeys){
      return 'capp_home.menu_contactus';
    }
    var ot = overridenTranslation(
        'capp_home.menu_contactus', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Liên hệ chúng tôi", name: 'capp_home.menu_contactus');
    return result != '' ? result : 'menu_contactus';
  }

  String get menuDocuments {
    if(showKeys){
      return 'capp_home.menu_documents';
    }
    var ot = overridenTranslation(
        'capp_home.menu_documents', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Các tài liệu", name: 'capp_home.menu_documents');
    return result != '' ? result : 'menu_documents';
  }

  String get menuFaq {
    if(showKeys){
      return 'capp_home.menu_faq';
    }
    var ot = overridenTranslation(
        'capp_home.menu_faq', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Câu hỏi thường gặp", name: 'capp_home.menu_faq');
    return result != '' ? result : 'menu_faq';
  }

  String get menuGroupGeneral {
    if(showKeys){
      return 'capp_home.menu_group_general';
    }
    var ot = overridenTranslation(
        'capp_home.menu_group_general', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chung", name: 'capp_home.menu_group_general');
    return result != '' ? result : 'menu_group_general';
  }

  String get menugGroupServices {
    if(showKeys){
      return 'capp_home.menug_group_services';
    }
    var ot = overridenTranslation(
        'capp_home.menug_group_services', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Services", name: 'capp_home.menug_group_services');
    return result != '' ? result : 'menug_group_services';
  }

  String get menuGroupHelpSupport {
    if(showKeys){
      return 'capp_home.menu_group_help_support';
    }
    var ot = overridenTranslation(
        'capp_home.menu_group_help_support', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trợ giúp & Hỗ trợ", name: 'capp_home.menu_group_help_support');
    return result != '' ? result : 'menu_group_help_support';
  }

  String get menuGroupSpecialOffers {
    if(showKeys){
      return 'capp_home.menu_group_special_offers';
    }
    var ot = overridenTranslation(
        'capp_home.menu_group_special_offers', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Special Offers", name: 'capp_home.menu_group_special_offers');
    return result != '' ? result : 'menu_group_special_offers';
  }

  String get menuHelpCenter {
    if(showKeys){
      return 'capp_home.menu_help_center';
    }
    var ot = overridenTranslation(
        'capp_home.menu_help_center', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trung tâm trợ giúp", name: 'capp_home.menu_help_center');
    return result != '' ? result : 'menu_help_center';
  }

  String get menuIprice {
    if(showKeys){
      return 'capp_home.menu_iprice';
    }
    var ot = overridenTranslation(
        'capp_home.menu_iprice', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("iPrice", name: 'capp_home.menu_iprice');
    return result != '' ? result : 'menu_iprice';
  }

  String get menuLogin {
    if(showKeys){
      return 'capp_home.menu_login';
    }
    var ot = overridenTranslation(
        'capp_home.menu_login', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập / Đăng ký", name: 'capp_home.menu_login');
    return result != '' ? result : 'menu_login';
  }

  String get menuLogout {
    if(showKeys){
      return 'capp_home.menu_logout';
    }
    var ot = overridenTranslation(
        'capp_home.menu_logout', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng xuất", name: 'capp_home.menu_logout');
    return result != '' ? result : 'menu_logout';
  }

  String get menuLockAccount {
    if(showKeys){
      return 'capp_home.menu_lock_account';
    }
    var ot = overridenTranslation(
        'capp_home.menu_lock_account', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng xuất", name: 'capp_home.menu_lock_account');
    return result != '' ? result : 'menu_lock_account';
  }

  String get menuManageCards {
    if(showKeys){
      return 'capp_home.menu_manage_cards';
    }
    var ot = overridenTranslation(
        'capp_home.menu_manage_cards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quản lý thẻ", name: 'capp_home.menu_manage_cards');
    return result != '' ? result : 'menu_manage_cards';
  }

  String get menuMyloans {
    if(showKeys){
      return 'capp_home.menu_myloans';
    }
    var ot = overridenTranslation(
        'capp_home.menu_myloans', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay", name: 'capp_home.menu_myloans');
    return result != '' ? result : 'menu_myloans';
  }

  String get menuNotifications {
    if(showKeys){
      return 'capp_home.menu_notifications';
    }
    var ot = overridenTranslation(
        'capp_home.menu_notifications', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hộp thư đến", name: 'capp_home.menu_notifications');
    return result != '' ? result : 'menu_notifications';
  }

  String get menuPersonalDetails {
    if(showKeys){
      return 'capp_home.menu_personal_details';
    }
    var ot = overridenTranslation(
        'capp_home.menu_personal_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin cá nhân", name: 'capp_home.menu_personal_details');
    return result != '' ? result : 'menu_personal_details';
  }

  String get menuPriceLink {
    if(showKeys){
      return 'capp_home.menu_price_link';
    }
    var ot = overridenTranslation(
        'capp_home.menu_price_link', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("https://iprice.vn", name: 'capp_home.menu_price_link');
    return result != '' ? result : 'menu_price_link';
  }

  String get menuPromo {
    if(showKeys){
      return 'capp_home.menu_promo';
    }
    var ot = overridenTranslation(
        'capp_home.menu_promo', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khuyến mại", name: 'capp_home.menu_promo');
    return result != '' ? result : 'menu_promo';
  }

  String get menuRetrieveMyAccount {
    if(showKeys){
      return 'capp_home.menu_retrieve_my_account';
    }
    var ot = overridenTranslation(
        'capp_home.menu_retrieve_my_account', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khôi phục tài khoản", name: 'capp_home.menu_retrieve_my_account');
    return result != '' ? result : 'menu_retrieve_my_account';
  }

  String get menuRewards {
    if(showKeys){
      return 'capp_home.menu_rewards';
    }
    var ot = overridenTranslation(
        'capp_home.menu_rewards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phần thưởng", name: 'capp_home.menu_rewards');
    return result != '' ? result : 'menu_rewards';
  }

  String get menuSelfService {
    if(showKeys){
      return 'capp_home.menu_self_service';
    }
    var ot = overridenTranslation(
        'capp_home.menu_self_service', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Dịch vụ", name: 'capp_home.menu_self_service');
    return result != '' ? result : 'menu_self_service';
  }

  String get menuServicesHcProtect {
    if(showKeys){
      return 'capp_home.menu_services_hc_protect';
    }
    var ot = overridenTranslation(
        'capp_home.menu_services_hc_protect', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Home Credit Protect", name: 'capp_home.menu_services_hc_protect');
    return result != '' ? result : 'menu_services_hc_protect';
  }

  String get menuSettingsBiometric {
    if(showKeys){
      return 'capp_home.menu_settings_biometric';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_biometric', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mở ứng dụng bằng sinh trắc học", name: 'capp_home.menu_settings_biometric');
    return result != '' ? result : 'menu_settings_biometric';
  }

  String get menuSettingsBiometricSetup {
    if(showKeys){
      return 'capp_home.menu_settings_biometric_setup';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_biometric_setup', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thiết lập sinh trắc học", name: 'capp_home.menu_settings_biometric_setup');
    return result != '' ? result : 'menu_settings_biometric_setup';
  }

  String get menuBiometricUnavailableDialogTitle {
    if(showKeys){
      return 'capp_home.menu_biometric_unavailable_dialog_title';
    }
    var ot = overridenTranslation(
        'capp_home.menu_biometric_unavailable_dialog_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thiết lập sinh trắc học chưa được cấp quyền", name: 'capp_home.menu_biometric_unavailable_dialog_title');
    return result != '' ? result : 'menu_biometric_unavailable_dialog_title';
  }

  String get menuBiometricUnavailableDialogSubtitle {
    if(showKeys){
      return 'capp_home.menu_biometric_unavailable_dialog_subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.menu_biometric_unavailable_dialog_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Để sử dụng thiết lập sinh trắc học trong app, bạn cần cấp quyền cho tính năng này trong phần Cài đặt của thiết bị đang sử dụng (Nếu thiếu bị có tính năng sinh trắc học)", name: 'capp_home.menu_biometric_unavailable_dialog_subtitle');
    return result != '' ? result : 'menu_biometric_unavailable_dialog_subtitle';
  }

  String get menuBiometricUnavailableDialogSettings {
    if(showKeys){
      return 'capp_home.menu_biometric_unavailable_dialog_settings';
    }
    var ot = overridenTranslation(
        'capp_home.menu_biometric_unavailable_dialog_settings', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đi đến cài đặt", name: 'capp_home.menu_biometric_unavailable_dialog_settings');
    return result != '' ? result : 'menu_biometric_unavailable_dialog_settings';
  }

  String get menuBiometricUnavailableDialogCancel {
    if(showKeys){
      return 'capp_home.menu_biometric_unavailable_dialog_cancel';
    }
    var ot = overridenTranslation(
        'capp_home.menu_biometric_unavailable_dialog_cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Huỷ", name: 'capp_home.menu_biometric_unavailable_dialog_cancel');
    return result != '' ? result : 'menu_biometric_unavailable_dialog_cancel';
  }

  String get menuSettingsChangeLanguage {
    if(showKeys){
      return 'capp_home.menu_settings_change_language';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_change_language', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thay đổi ngôn ngữ", name: 'capp_home.menu_settings_change_language');
    return result != '' ? result : 'menu_settings_change_language';
  }

  String get menuSettingsChangePasswordPin {
    if(showKeys){
      return 'capp_home.menu_settings_change_password_pin';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_change_password_pin', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thay đổi mật khẩu / mã PIN", name: 'capp_home.menu_settings_change_password_pin');
    return result != '' ? result : 'menu_settings_change_password_pin';
  }

  String get menuSettingsDisableUser {
    if(showKeys){
      return 'capp_home.menu_settings_disable_user';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_disable_user', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xoá tài khoản", name: 'capp_home.menu_settings_disable_user');
    return result != '' ? result : 'menu_settings_disable_user';
  }

  String get menuSettingsPreferredCommunication {
    if(showKeys){
      return 'capp_home.menu_settings_preferred_communication';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_preferred_communication', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn kênh nhận thông tin", name: 'capp_home.menu_settings_preferred_communication');
    return result != '' ? result : 'menu_settings_preferred_communication';
  }

  String get menuSettings {
    if(showKeys){
      return 'capp_home.menu_settings';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cài đặt", name: 'capp_home.menu_settings');
    return result != '' ? result : 'menu_settings';
  }

  String get menuTransactionHistory {
    if(showKeys){
      return 'capp_home.menu_transaction_history';
    }
    var ot = overridenTranslation(
        'capp_home.menu_transaction_history', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Transaction History", name: 'capp_home.menu_transaction_history');
    return result != '' ? result : 'menu_transaction_history';
  }

  String get menuPurchasedInsurances {
    if(showKeys){
      return 'capp_home.menu_purchased_insurances';
    }
    var ot = overridenTranslation(
        'capp_home.menu_purchased_insurances', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo hiểm đã mua", name: 'capp_home.menu_purchased_insurances');
    return result != '' ? result : 'menu_purchased_insurances';
  }

  String get menuSettingsPrivacySettings {
    if(showKeys){
      return 'capp_home.menu_settings_privacy_settings';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_privacy_settings', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Privacy Settings", name: 'capp_home.menu_settings_privacy_settings');
    return result != '' ? result : 'menu_settings_privacy_settings';
  }

  String get menuSettingsAllowDataProcessing {
    if(showKeys){
      return 'capp_home.menu_settings_allow_data_processing';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_allow_data_processing', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Allow Data Processing", name: 'capp_home.menu_settings_allow_data_processing');
    return result != '' ? result : 'menu_settings_allow_data_processing';
  }

  String get menuSettingsTermsAndConditions {
    if(showKeys){
      return 'capp_home.menu_settings_terms_and_conditions';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_terms_and_conditions', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Terms and Conditions", name: 'capp_home.menu_settings_terms_and_conditions');
    return result != '' ? result : 'menu_settings_terms_and_conditions';
  }

  String get menuSettingsRegistrationConsentForm {
    if(showKeys){
      return 'capp_home.menu_settings_registration_consent_form';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_registration_consent_form', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Registration Consent Form", name: 'capp_home.menu_settings_registration_consent_form');
    return result != '' ? result : 'menu_settings_registration_consent_form';
  }

  String get menuSettingsPrivacyPolicy {
    if(showKeys){
      return 'capp_home.menu_settings_privacy_policy';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_privacy_policy', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Privacy Policy", name: 'capp_home.menu_settings_privacy_policy');
    return result != '' ? result : 'menu_settings_privacy_policy';
  }

  String get menuSettingsReceiveMarketingUpdates {
    if(showKeys){
      return 'capp_home.menu_settings_receive_marketing_updates';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_receive_marketing_updates', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Receive Marketing Updates", name: 'capp_home.menu_settings_receive_marketing_updates');
    return result != '' ? result : 'menu_settings_receive_marketing_updates';
  }

  String get menuSettingsMarketingConsentForm {
    if(showKeys){
      return 'capp_home.menu_settings_marketing_consent_form';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_marketing_consent_form', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Marketing Consent Form", name: 'capp_home.menu_settings_marketing_consent_form');
    return result != '' ? result : 'menu_settings_marketing_consent_form';
  }

  String get menuSettingsPrivacyErrorDialogTitle {
    if(showKeys){
      return 'capp_home.menu_settings_privacy_error_dialog_title';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_privacy_error_dialog_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Error", name: 'capp_home.menu_settings_privacy_error_dialog_title');
    return result != '' ? result : 'menu_settings_privacy_error_dialog_title';
  }

  String get menuSettingsPrivacyErrorDialogText {
    if(showKeys){
      return 'capp_home.menu_settings_privacy_error_dialog_text';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_privacy_error_dialog_text', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Something went wrong please try later", name: 'capp_home.menu_settings_privacy_error_dialog_text');
    return result != '' ? result : 'menu_settings_privacy_error_dialog_text';
  }

  String get menuSettingsPrivacyErrorDialogBtn {
    if(showKeys){
      return 'capp_home.menu_settings_privacy_error_dialog_btn';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_privacy_error_dialog_btn', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("OK, got it", name: 'capp_home.menu_settings_privacy_error_dialog_btn');
    return result != '' ? result : 'menu_settings_privacy_error_dialog_btn';
  }

  String get menuSettingsPrivacyBtnIAgree {
    if(showKeys){
      return 'capp_home.menu_settings_privacy_btn_i_agree';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_privacy_btn_i_agree', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("I Agree", name: 'capp_home.menu_settings_privacy_btn_i_agree');
    return result != '' ? result : 'menu_settings_privacy_btn_i_agree';
  }

  String get menuSettingsPrivacyBtnWithdrawConsent {
    if(showKeys){
      return 'capp_home.menu_settings_privacy_btn_withdraw_consent';
    }
    var ot = overridenTranslation(
        'capp_home.menu_settings_privacy_btn_withdraw_consent', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Withdraw consent", name: 'capp_home.menu_settings_privacy_btn_withdraw_consent');
    return result != '' ? result : 'menu_settings_privacy_btn_withdraw_consent';
  }

  String get notAvailable {
    if(showKeys){
      return 'capp_home.not_available';
    }
    var ot = overridenTranslation(
        'capp_home.not_available', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("chưa khả dụng", name: 'capp_home.not_available');
    return result != '' ? result : 'not_available';
  }

  String get rateAppCancel {
    if(showKeys){
      return 'capp_home.rate_app_cancel';
    }
    var ot = overridenTranslation(
        'capp_home.rate_app_cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hủy", name: 'capp_home.rate_app_cancel');
    return result != '' ? result : 'rate_app_cancel';
  }

  String get rateAppConfirm {
    if(showKeys){
      return 'capp_home.rate_app_confirm';
    }
    var ot = overridenTranslation(
        'capp_home.rate_app_confirm', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Gửi", name: 'capp_home.rate_app_confirm');
    return result != '' ? result : 'rate_app_confirm';
  }

  String get rateAppMessageAndroid {
    if(showKeys){
      return 'capp_home.rate_app_message_android';
    }
    var ot = overridenTranslation(
        'capp_home.rate_app_message_android', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn sao để đánh giá ứng dụng trên Google Play", name: 'capp_home.rate_app_message_android');
    return result != '' ? result : 'rate_app_message_android';
  }

  String get rateAppTitle {
    if(showKeys){
      return 'capp_home.rate_app_title';
    }
    var ot = overridenTranslation(
        'capp_home.rate_app_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có thích ứng dụng Home Credit không?", name: 'capp_home.rate_app_title');
    return result != '' ? result : 'rate_app_title';
  }

  String get register {
    if(showKeys){
      return 'capp_home.register';
    }
    var ot = overridenTranslation(
        'capp_home.register', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng ký", name: 'capp_home.register');
    return result != '' ? result : 'register';
  }

  String get repayment {
    if(showKeys){
      return 'capp_home.repayment';
    }
    var ot = overridenTranslation(
        'capp_home.repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán khoản vay", name: 'capp_home.repayment');
    return result != '' ? result : 'repayment';
  }

  String get waterPayment {
    if(showKeys){
      return 'capp_home.water_payment';
    }
    var ot = overridenTranslation(
        'capp_home.water_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tiền nước", name: 'capp_home.water_payment');
    return result != '' ? result : 'water_payment';
  }

  String get cappMessagingArchived {
    if(showKeys){
      return 'capp_home.capp_messaging__archived';
    }
    var ot = overridenTranslation(
        'capp_home.capp_messaging__archived', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tin nhắn đã lưu", name: 'capp_home.capp_messaging__archived');
    return result != '' ? result : 'capp_messaging__archived';
  }

  String get cappMessagingEmptyInbox {
    if(showKeys){
      return 'capp_home.capp_messaging__empty_inbox';
    }
    var ot = overridenTranslation(
        'capp_home.capp_messaging__empty_inbox', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn chưa có thư nào. Đừng lo lắng, bạn sẽ nhận được thư sớm thôi :)", name: 'capp_home.capp_messaging__empty_inbox');
    return result != '' ? result : 'capp_messaging__empty_inbox';
  }

  String get cappMessagingInbox {
    if(showKeys){
      return 'capp_home.capp_messaging__inbox';
    }
    var ot = overridenTranslation(
        'capp_home.capp_messaging__inbox', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hộp thư đến", name: 'capp_home.capp_messaging__inbox');
    return result != '' ? result : 'capp_messaging__inbox';
  }

  String get collect {
    if(showKeys){
      return 'capp_home.collect';
    }
    var ot = overridenTranslation(
        'capp_home.collect', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn", name: 'capp_home.collect');
    return result != '' ? result : 'collect';
  }

  String get continueString {
    if(showKeys){
      return 'capp_home.continue_string';
    }
    var ot = overridenTranslation(
        'capp_home.continue_string', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục", name: 'capp_home.continue_string');
    return result != '' ? result : 'continue_string';
  }

  String get delete {
    if(showKeys){
      return 'capp_home.delete';
    }
    var ot = overridenTranslation(
        'capp_home.delete', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xóa", name: 'capp_home.delete');
    return result != '' ? result : 'delete';
  }

  String get dontWorryApplyLater {
    if(showKeys){
      return 'capp_home.dont_worry_apply_later';
    }
    var ot = overridenTranslation(
        'capp_home.dont_worry_apply_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đừng lo, bạn vẫn có thể lấy lại mã QR này sau", name: 'capp_home.dont_worry_apply_later');
    return result != '' ? result : 'dont_worry_apply_later';
  }

  String get expiresIn {
    if(showKeys){
      return 'capp_home.expires_in';
    }
    var ot = overridenTranslation(
        'capp_home.expires_in', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hết hạn sau", name: 'capp_home.expires_in');
    return result != '' ? result : 'expires_in';
  }

  String get goal {
    if(showKeys){
      return 'capp_home.goal';
    }
    var ot = overridenTranslation(
        'capp_home.goal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhiệm vụ", name: 'capp_home.goal');
    return result != '' ? result : 'goal';
  }

  String get inboxEmpty {
    if(showKeys){
      return 'capp_home.inbox__empty';
    }
    var ot = overridenTranslation(
        'capp_home.inbox__empty', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hộp thư trống", name: 'capp_home.inbox__empty');
    return result != '' ? result : 'inbox__empty';
  }

  String get inboxEmptySubtitle {
    if(showKeys){
      return 'capp_home.inbox_empty__subtitle';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_empty__subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có thể theo dõi dạng thông báo mới nhất tại mục này", name: 'capp_home.inbox_empty__subtitle');
    return result != '' ? result : 'inbox_empty__subtitle';
  }

  String get inboxConfirmDelete {
    if(showKeys){
      return 'capp_home.inbox_confirm_delete';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_confirm_delete', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có chắc mình muốn xóa tin nhắn này không?", name: 'capp_home.inbox_confirm_delete');
    return result != '' ? result : 'inbox_confirm_delete';
  }

  String get inboxFilterAll {
    if(showKeys){
      return 'capp_home.inbox_filter__all';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_filter__all', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tất cả", name: 'capp_home.inbox_filter__all');
    return result != '' ? result : 'inbox_filter__all';
  }

  String get inboxFilterArchieved {
    if(showKeys){
      return 'capp_home.inbox_filter__archieved';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_filter__archieved', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã lưu", name: 'capp_home.inbox_filter__archieved');
    return result != '' ? result : 'inbox_filter__archieved';
  }

  String get inboxFilterCoupons {
    if(showKeys){
      return 'capp_home.inbox_filter__coupons';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_filter__coupons', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phiếu giảm giá", name: 'capp_home.inbox_filter__coupons');
    return result != '' ? result : 'inbox_filter__coupons';
  }

  String get inboxFilterMerchants {
    if(showKeys){
      return 'capp_home.inbox_filter__merchants';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_filter__merchants', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Từ người bán", name: 'capp_home.inbox_filter__merchants');
    return result != '' ? result : 'inbox_filter__merchants';
  }

  String get inboxFilterNotifications {
    if(showKeys){
      return 'capp_home.inbox_filter__notifications';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_filter__notifications', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông báo", name: 'capp_home.inbox_filter__notifications');
    return result != '' ? result : 'inbox_filter__notifications';
  }

  String get inboxFilterRewards {
    if(showKeys){
      return 'capp_home.inbox_filter__rewards';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_filter__rewards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phần thưởng", name: 'capp_home.inbox_filter__rewards');
    return result != '' ? result : 'inbox_filter__rewards';
  }

  String get inboxMarkAllReadDialogTitle {
    if(showKeys){
      return 'capp_home.inbox_mark_all_read_dialog__title';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_mark_all_read_dialog__title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đánh dấu đã đọc tất cả", name: 'capp_home.inbox_mark_all_read_dialog__title');
    return result != '' ? result : 'inbox_mark_all_read_dialog__title';
  }

  String get inboxMarkAllReadDialogMarkAllQuestion {
    if(showKeys){
      return 'capp_home.inbox_mark_all_read_dialog__mark_all_question';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_mark_all_read_dialog__mark_all_question', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có muốn đánh dấu đã đọc tất cả tin nhắn không?", name: 'capp_home.inbox_mark_all_read_dialog__mark_all_question');
    return result != '' ? result : 'inbox_mark_all_read_dialog__mark_all_question';
  }

  String inboxMarkAllReadSnackMarkedMessages(dynamic count) {
  if(showKeys){
      return 'capp_home.inbox_mark_all_read_snack__marked_messages';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_mark_all_read_snack__marked_messages', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Đã đọc ${count} tin được đánh dấu", name: 'capp_home.inbox_mark_all_read_snack__marked_messages', args: [count]);
  }

  String get inboxMarkAllReadDialogYes {
    if(showKeys){
      return 'capp_home.inbox_mark_all_read_dialog__yes';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_mark_all_read_dialog__yes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có", name: 'capp_home.inbox_mark_all_read_dialog__yes');
    return result != '' ? result : 'inbox_mark_all_read_dialog__yes';
  }

  String get inboxMarkAllReadDialogNo {
    if(showKeys){
      return 'capp_home.inbox_mark_all_read_dialog__no';
    }
    var ot = overridenTranslation(
        'capp_home.inbox_mark_all_read_dialog__no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không", name: 'capp_home.inbox_mark_all_read_dialog__no');
    return result != '' ? result : 'inbox_mark_all_read_dialog__no';
  }

  String get messageCategoryAll {
    if(showKeys){
      return 'capp_home.message_category_all';
    }
    var ot = overridenTranslation(
        'capp_home.message_category_all', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tất cả", name: 'capp_home.message_category_all');
    return result != '' ? result : 'message_category_all';
  }

  String get messageDeleted {
    if(showKeys){
      return 'capp_home.message_deleted';
    }
    var ot = overridenTranslation(
        'capp_home.message_deleted', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tin nhắn vừa được xóa", name: 'capp_home.message_deleted');
    return result != '' ? result : 'message_deleted';
  }

  String get messageDetail {
    if(showKeys){
      return 'capp_home.message_detail';
    }
    var ot = overridenTranslation(
        'capp_home.message_detail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết tin nhắn", name: 'capp_home.message_detail');
    return result != '' ? result : 'message_detail';
  }

  String get mysteryBox {
    if(showKeys){
      return 'capp_home.mystery_box';
    }
    var ot = overridenTranslation(
        'capp_home.mystery_box', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hộp quà bí mật", name: 'capp_home.mystery_box');
    return result != '' ? result : 'mystery_box';
  }

  String get timePassedDayAgo {
    if(showKeys){
      return 'capp_home.time_passed__day_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__day_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("ngày trước", name: 'capp_home.time_passed__day_ago');
    return result != '' ? result : 'time_passed__day_ago';
  }

  String get timePassedDaysAgo {
    if(showKeys){
      return 'capp_home.time_passed__days_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__days_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vài ngày trước", name: 'capp_home.time_passed__days_ago');
    return result != '' ? result : 'time_passed__days_ago';
  }

  String get timePassedHourAgo {
    if(showKeys){
      return 'capp_home.time_passed__hour_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__hour_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("giờ trước", name: 'capp_home.time_passed__hour_ago');
    return result != '' ? result : 'time_passed__hour_ago';
  }

  String get timePassedHoursAgo {
    if(showKeys){
      return 'capp_home.time_passed__hours_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__hours_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("giờ trước", name: 'capp_home.time_passed__hours_ago');
    return result != '' ? result : 'time_passed__hours_ago';
  }

  String get timePassedLastYear {
    if(showKeys){
      return 'capp_home.time_passed__last_year';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__last_year', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Năm ngoái", name: 'capp_home.time_passed__last_year');
    return result != '' ? result : 'time_passed__last_year';
  }

  String get timePassedMinuteAgo {
    if(showKeys){
      return 'capp_home.time_passed__minute_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__minute_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("phút trước", name: 'capp_home.time_passed__minute_ago');
    return result != '' ? result : 'time_passed__minute_ago';
  }

  String get timePassedMinutesAgo {
    if(showKeys){
      return 'capp_home.time_passed__minutes_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__minutes_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("phút trước", name: 'capp_home.time_passed__minutes_ago');
    return result != '' ? result : 'time_passed__minutes_ago';
  }

  String get timePassedMonthAgo {
    if(showKeys){
      return 'capp_home.time_passed__month_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__month_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("tháng trước", name: 'capp_home.time_passed__month_ago');
    return result != '' ? result : 'time_passed__month_ago';
  }

  String get timePassedMonthsAgo {
    if(showKeys){
      return 'capp_home.time_passed__months_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__months_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("tháng trước", name: 'capp_home.time_passed__months_ago');
    return result != '' ? result : 'time_passed__months_ago';
  }

  String get timePassedToday {
    if(showKeys){
      return 'capp_home.time_passed__today';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__today', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hôm nay", name: 'capp_home.time_passed__today');
    return result != '' ? result : 'time_passed__today';
  }

  String get timePassedWeekAgo {
    if(showKeys){
      return 'capp_home.time_passed__week_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__week_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tuần trước", name: 'capp_home.time_passed__week_ago');
    return result != '' ? result : 'time_passed__week_ago';
  }

  String get timePassedWeeksAgo {
    if(showKeys){
      return 'capp_home.time_passed__weeks_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__weeks_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("tuần trước", name: 'capp_home.time_passed__weeks_ago');
    return result != '' ? result : 'time_passed__weeks_ago';
  }

  String get timePassedYearAgo {
    if(showKeys){
      return 'capp_home.time_passed__year_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__year_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("năm trước", name: 'capp_home.time_passed__year_ago');
    return result != '' ? result : 'time_passed__year_ago';
  }

  String get timePassedYearsAgo {
    if(showKeys){
      return 'capp_home.time_passed__years_ago';
    }
    var ot = overridenTranslation(
        'capp_home.time_passed__years_ago', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("năm trước", name: 'capp_home.time_passed__years_ago');
    return result != '' ? result : 'time_passed__years_ago';
  }

  String get today {
    if(showKeys){
      return 'capp_home.today';
    }
    var ot = overridenTranslation(
        'capp_home.today', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hôm nay", name: 'capp_home.today');
    return result != '' ? result : 'today';
  }

  String get undo {
    if(showKeys){
      return 'capp_home.undo';
    }
    var ot = overridenTranslation(
        'capp_home.undo', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoàn tác", name: 'capp_home.undo');
    return result != '' ? result : 'undo';
  }

  String get unread {
    if(showKeys){
      return 'capp_home.unread';
    }
    var ot = overridenTranslation(
        'capp_home.unread', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chưa đọc", name: 'capp_home.unread');
    return result != '' ? result : 'unread';
  }

  String get validUntil {
    if(showKeys){
      return 'capp_home.valid_until';
    }
    var ot = overridenTranslation(
        'capp_home.valid_until', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có hiệu lực cho đến khi", name: 'capp_home.valid_until');
    return result != '' ? result : 'valid_until';
  }

  String get yourGold {
    if(showKeys){
      return 'capp_home.your_gold';
    }
    var ot = overridenTranslation(
        'capp_home.your_gold', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vàng", name: 'capp_home.your_gold');
    return result != '' ? result : 'your_gold';
  }

  String get permissions {
    if(showKeys){
      return 'capp_home.permissions';
    }
    var ot = overridenTranslation(
        'capp_home.permissions', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("App permissions", name: 'capp_home.permissions');
    return result != '' ? result : 'permissions';
  }

  String get qrLoginHeader {
    if(showKeys){
      return 'capp_home.qr_login_header';
    }
    var ot = overridenTranslation(
        'capp_home.qr_login_header', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chào mừng bạn đến với Home Credit", name: 'capp_home.qr_login_header');
    return result != '' ? result : 'qr_login_header';
  }

  String get qrLoginSubheader {
    if(showKeys){
      return 'capp_home.qr_login_subheader';
    }
    var ot = overridenTranslation(
        'capp_home.qr_login_subheader', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập ngay để có thể sử dụng hết tất cả tính năng của ứng dụng", name: 'capp_home.qr_login_subheader');
    return result != '' ? result : 'qr_login_subheader';
  }

  String get qrLoginButton {
    if(showKeys){
      return 'capp_home.qr_login_button';
    }
    var ot = overridenTranslation(
        'capp_home.qr_login_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng nhập / Đăng ký", name: 'capp_home.qr_login_button');
    return result != '' ? result : 'qr_login_button';
  }

  String get submitAndRateButton {
    if(showKeys){
      return 'capp_home.submit_and_rate_button';
    }
    var ot = overridenTranslation(
        'capp_home.submit_and_rate_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Submit & rate us", name: 'capp_home.submit_and_rate_button');
    return result != '' ? result : 'submit_and_rate_button';
  }

  String get submitAndCloseButton {
    if(showKeys){
      return 'capp_home.submit_and_close_button';
    }
    var ot = overridenTranslation(
        'capp_home.submit_and_close_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Submit & close", name: 'capp_home.submit_and_close_button');
    return result != '' ? result : 'submit_and_close_button';
  }

  String get actionBeltItemMore {
    if(showKeys){
      return 'capp_home.action_belt_item_more';
    }
    var ot = overridenTranslation(
        'capp_home.action_belt_item_more', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhiều hơn", name: 'capp_home.action_belt_item_more');
    return result != '' ? result : 'action_belt_item_more';
  }

  String get homeGamesItemCaptionText {
    if(showKeys){
      return 'capp_home.home_games_item_caption_text';
    }
    var ot = overridenTranslation(
        'capp_home.home_games_item_caption_text', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Plays", name: 'capp_home.home_games_item_caption_text');
    return result != '' ? result : 'home_games_item_caption_text';
  }

  String get homeGamesSectionHeader {
    if(showKeys){
      return 'capp_home.home_games_section_header';
    }
    var ot = overridenTranslation(
        'capp_home.home_games_section_header', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Learn & play", name: 'capp_home.home_games_section_header');
    return result != '' ? result : 'home_games_section_header';
  }

  String get actionBeltEWallet {
    if(showKeys){
      return 'capp_home.action_belt_e_wallet';
    }
    var ot = overridenTranslation(
        'capp_home.action_belt_e_wallet', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("E-Wallet", name: 'capp_home.action_belt_e_wallet');
    return result != '' ? result : 'action_belt_e_wallet';
  }

  String get productsAndServicesTitle {
    if(showKeys){
      return 'capp_home.products_and_services_title';
    }
    var ot = overridenTranslation(
        'capp_home.products_and_services_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sản phẩm và Dịch vụ", name: 'capp_home.products_and_services_title');
    return result != '' ? result : 'products_and_services_title';
  }

  String get viewAll {
    if(showKeys){
      return 'capp_home.view_all';
    }
    var ot = overridenTranslation(
        'capp_home.view_all', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem tất cả", name: 'capp_home.view_all');
    return result != '' ? result : 'view_all';
  }

  String get enableLoanOffer {
    if(showKeys){
      return 'capp_home.enable_loan_offer';
    }
    var ot = overridenTranslation(
        'capp_home.enable_loan_offer', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Enable loan offer", name: 'capp_home.enable_loan_offer');
    return result != '' ? result : 'enable_loan_offer';
  }

  String get welcome {
    if(showKeys){
      return 'capp_home.welcome';
    }
    var ot = overridenTranslation(
        'capp_home.welcome', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xin chào!", name: 'capp_home.welcome');
    return result != '' ? result : 'welcome';
  }

  String welcomeUser(dynamic name) {
  if(showKeys){
      return 'capp_home.welcome_user';
    }
    var ot = overridenTranslation(
        'capp_home.welcome_user', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Xin chào, ${name}!", name: 'capp_home.welcome_user', args: [name]);
  }

  String get menuAda {
    if(showKeys){
      return 'capp_home.menu_ada';
    }
    var ot = overridenTranslation(
        'capp_home.menu_ada', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tự động", name: 'capp_home.menu_ada');
    return result != '' ? result : 'menu_ada';
  }

  String get myProducts {
    if(showKeys){
      return 'capp_home.my_products';
    }
    var ot = overridenTranslation(
        'capp_home.my_products', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quản lý khoản vay", name: 'capp_home.my_products');
    return result != '' ? result : 'my_products';
  }

  String get menuPersonalDocuments {
    if(showKeys){
      return 'capp_home.menu_personal_documents';
    }
    var ot = overridenTranslation(
        'capp_home.menu_personal_documents', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài liệu phụ lục cá nhân", name: 'capp_home.menu_personal_documents');
    return result != '' ? result : 'menu_personal_documents';
  }

  String get mainTabQrScan {
    if(showKeys){
      return 'capp_home.main_tab_qr_scan';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_qr_scan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quét QR", name: 'capp_home.main_tab_qr_scan');
    return result != '' ? result : 'main_tab_qr_scan';
  }

  String get mainTabQrVietQr {
    if(showKeys){
      return 'capp_home.main_tab_qr_viet_qr';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_qr_viet_qr', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("VietQR", name: 'capp_home.main_tab_qr_viet_qr');
    return result != '' ? result : 'main_tab_qr_viet_qr';
  }

  String get mainTabQrTgdd {
    if(showKeys){
      return 'capp_home.main_tab_qr_tgdd';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_qr_tgdd', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("TGDĐ", name: 'capp_home.main_tab_qr_tgdd');
    return result != '' ? result : 'main_tab_qr_tgdd';
  }

  String get mainTabQrDmx {
    if(showKeys){
      return 'capp_home.main_tab_qr_dmx';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_qr_dmx', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("DienMayXanh", name: 'capp_home.main_tab_qr_dmx');
    return result != '' ? result : 'main_tab_qr_dmx';
  }

  String get mainTabQrAvakids {
    if(showKeys){
      return 'capp_home.main_tab_qr_avakids';
    }
    var ot = overridenTranslation(
        'capp_home.main_tab_qr_avakids', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("AvaKids", name: 'capp_home.main_tab_qr_avakids');
    return result != '' ? result : 'main_tab_qr_avakids';
  }

  String get emiCalculatorCost {
    if(showKeys){
      return 'capp_home.emi_calculator_cost';
    }
    var ot = overridenTranslation(
        'capp_home.emi_calculator_cost', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Product Cost", name: 'capp_home.emi_calculator_cost');
    return result != '' ? result : 'emi_calculator_cost';
  }

  String get emiCalculatorTerm {
    if(showKeys){
      return 'capp_home.emi_calculator_term';
    }
    var ot = overridenTranslation(
        'capp_home.emi_calculator_term', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Term", name: 'capp_home.emi_calculator_term');
    return result != '' ? result : 'emi_calculator_term';
  }

  String get emiCalculatorRate {
    if(showKeys){
      return 'capp_home.emi_calculator_rate';
    }
    var ot = overridenTranslation(
        'capp_home.emi_calculator_rate', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Interest rate", name: 'capp_home.emi_calculator_rate');
    return result != '' ? result : 'emi_calculator_rate';
  }

  String get emiCalculatorMonth {
    if(showKeys){
      return 'capp_home.emi_calculator_month';
    }
    var ot = overridenTranslation(
        'capp_home.emi_calculator_month', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("months", name: 'capp_home.emi_calculator_month');
    return result != '' ? result : 'emi_calculator_month';
  }

  String get emiCalculatorInstallment {
    if(showKeys){
      return 'capp_home.emi_calculator_installment';
    }
    var ot = overridenTranslation(
        'capp_home.emi_calculator_installment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Monthly installment", name: 'capp_home.emi_calculator_installment');
    return result != '' ? result : 'emi_calculator_installment';
  }

  String emiCalculatorDisclamer(dynamic percent) {
  if(showKeys){
      return 'capp_home.emi_calculator_disclamer';
    }
    var ot = overridenTranslation(
        'capp_home.emi_calculator_disclamer', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Loan estimate: ${percent}% flat annual interest rate, may vary from actual amount, excluding fees.", name: 'capp_home.emi_calculator_disclamer', args: [percent]);
  }

  String repaymentRepayBefore(dynamic date) {
  if(showKeys){
      return 'capp_home.repayment_repay_before';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_repay_before', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Thanh toán trước ${date}", name: 'capp_home.repayment_repay_before', args: [date]);
  }

  String get repaymentProductCreditCard {
    if(showKeys){
      return 'capp_home.repayment_product_credit_card';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_product_credit_card', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ tín dụng", name: 'capp_home.repayment_product_credit_card');
    return result != '' ? result : 'repayment_product_credit_card';
  }

  String get repaymentProductBnplNew {
    if(showKeys){
      return 'capp_home.repayment_product_bnpl_new';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_product_bnpl_new', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản trả sau", name: 'capp_home.repayment_product_bnpl_new');
    return result != '' ? result : 'repayment_product_bnpl_new';
  }

  String get repaymentProductBnpl {
    if(showKeys){
      return 'capp_home.repayment_product_bnpl';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_product_bnpl', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Home PayLater", name: 'capp_home.repayment_product_bnpl');
    return result != '' ? result : 'repayment_product_bnpl';
  }

  String get repaymentPersonalLoan {
    if(showKeys){
      return 'capp_home.repayment_personal_loan';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_personal_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay tiền mặt", name: 'capp_home.repayment_personal_loan');
    return result != '' ? result : 'repayment_personal_loan';
  }

  String get repaymentInsuranceLoan {
    if(showKeys){
      return 'capp_home.repayment_insurance_loan';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_insurance_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay gói An Tâm Tài Chính", name: 'capp_home.repayment_insurance_loan');
    return result != '' ? result : 'repayment_insurance_loan';
  }

  String get repaymentConsumerLoan {
    if(showKeys){
      return 'capp_home.repayment_consumer_loan';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_consumer_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay tiêu dùng", name: 'capp_home.repayment_consumer_loan');
    return result != '' ? result : 'repayment_consumer_loan';
  }

  String get repaymentNow {
    if(showKeys){
      return 'capp_home.repayment_now';
    }
    var ot = overridenTranslation(
        'capp_home.repayment_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán", name: 'capp_home.repayment_now');
    return result != '' ? result : 'repayment_now';
  }



  Map<String, String> get _translate => <String, String>{
		'appliance_protection' : applianceProtection,
		'bills' : bills,
		'cancel' : cancel,
		'capp_main__recovered_password_title' : cappMainRecoveredPasswordTitle,
		'care360' : care360,
		'create_avatar' : createAvatar,
		'credit_cards' : creditCards,
		'digital_gold' : digitalGold,
		'discounts' : discounts,
		'electricity' : electricity,
		'finish_avatar' : finishAvatar,
		'finish_deals' : finishDeals,
		'finish_now' : finishNow,
		'finish_wizard' : finishWizard,
		'free_credit_score' : freeCreditScore,
		'games_banner_title' : gamesBannerTitle,
		'games_section' : gamesSection,
		'gaming_voucher' : gamingVoucher,
		'get_a_loan' : getALoan,
		'go_shopping_message' : goShoppingMessage,
		'go_shopping' : goShopping,
		'gold_loan' : goldLoan,
		'got_it_button' : gotItButton,
		'government_tax' : governmentTax,
		'health_insurance' : healthInsurance,
		'health_services' : healthServices,
		'helpdesk_call_number' : helpdeskCallNumber,
		'home_appliance_protection' : homeApplianceProtection,
		'home_loan' : homeLoan,
		'hot_deals' : hotDeals,
		'instalment_details' : instalmentDetails,
		'insurance' : insurance,
		'internet_and_TV' : internetAndTv,
		'loads' : loads,
		'loan_payment' : loanPayment,
		'login_register' : loginRegister,
		'my_home_credit' : myHomeCredit,
		'my_loans' : myLoans,
		'no_thanks' : noThanks,
		'no' : no,
		'not_now' : notNow,
		'now_i_know_a_bit_about_you' : nowIKnowABitAboutYou,
		'ok_got_it' : okGotIt,
		'other_services' : otherServices,
		'personalize' : personalize,
		'postpaid_loan' : postpaidLoan,
		'prepaid_loan' : prepaidLoan,
		'recharge_your_phone' : rechargeYourPhone,
		'reservation_expired' : reservationExpired,
		'reservation_of' : reservationOf,
		'select_new_product' : selectNewProduct,
		'self_service' : selfService,
		'show_all' : showAll,
		'sign_now' : signNow,
		'streaming' : streaming,
		'tips_and_stories' : tipsAndStories,
		'two_wheeler_insurance' : twoWheelerInsurance,
		'unlock_infobox' : unlockInfobox,
		'wallet_security' : walletSecurity,
		'water' : water,
		'yes' : yes,
		'allow_camera_permissions_to_continue_android' : allowCameraPermissionsToContinueAndroid,
		'allow_camera_permissions_to_continue_ios' : allowCameraPermissionsToContinueIos,
		'pos_loan_banner_title' : posLoanBannerTitle,
		'financial_literacy' : financialLiteracy,
		'epfs_init_title' : epfsInitTitle,
		'epfs_init_desc' : epfsInitDesc,
		'epfs_init_footer' : epfsInitFooter,
		'epfs_complete_title' : epfsCompleteTitle,
		'epfs_complete_desc' : epfsCompleteDesc,
		'epfs_approval' : epfsApproval,
		'epfs_go_next_button' : epfsGoNextButton,
		'epfs_finish_button' : epfsFinishButton,
		'epfs_complete_button' : epfsCompleteButton,
		'account_screen_title' : accountScreenTitle,
		'assistant_challenges' : assistantChallenges,
		'main_tab_account' : mainTabAccount,
		'main_tab_deals' : mainTabDeals,
		'main_tab_home' : mainTabHome,
		'main_tab_loans' : mainTabLoans,
		'main_tab_nearby' : mainTabNearby,
		'main_tab_rewards' : mainTabRewards,
		'main_tab_shop' : mainTabShop,
		'main_tab_promo' : mainTabPromo,
		'main_tab_super_cupo' : mainTabSuperCupo,
		'main_tab_activity' : mainTabActivity,
		'main_tab_contact_us' : mainTabContactUs,
		'capp_main__assistant_login_register' : cappMainAssistantLoginRegister,
		'capp_main__assistant_subtitle' : cappMainAssistantSubtitle,
		'capp_main__assistant_title' : cappMainAssistantTitle,
		'capp_main__chat_with_us' : cappMainChatWithUs,
		'capp_main__contact_home_credit' : cappMainContactHomeCredit,
		'capp_main__loan_registration' : cappMainLoanRegistration,
		'capp_main__empty_loans_button_text' : cappMainEmptyLoansButtonText,
		'capp_main__empty_loans_subtitle' : cappMainEmptyLoansSubtitle,
		'capp_main__empty_loans_title' : cappMainEmptyLoansTitle,
		'capp_main__empty_offers_button_text' : cappMainEmptyOffersButtonText,
		'capp_main__empty_offers_button_url' : cappMainEmptyOffersButtonUrl,
		'capp_main__empty_offers_subtitle' : cappMainEmptyOffersSubtitle,
		'capp_main__empty_offers_title' : cappMainEmptyOffersTitle,
		'capp_main__exit_app_title' : cappMainExitAppTitle,
		'capp_main__finance_appbar_title' : cappMainFinanceAppbarTitle,
		'capp_main__guest_assistant_subtitle' : cappMainGuestAssistantSubtitle,
		'capp_main__guest_assistant_title' : cappMainGuestAssistantTitle,
		'capp_main__loan_offers' : cappMainLoanOffers,
		'capp_main__loans' : cappMainLoans,
		'capp_main__loans_guest_hpl_relogin_popup_title' : cappMainLoansGuestHplReloginPopupTitle,
		'capp_main__loans_guest_hpl_relogin_popup_content' : cappMainLoansGuestHplReloginPopupContent,
		'capp_main__loans_guest_hpl_relogin_popup_login_button' : cappMainLoansGuestHplReloginPopupLoginButton,
		'capp_main__my_coupons' : cappMainMyCoupons,
		'capp_main__no_loans_subtitle' : cappMainNoLoansSubtitle,
		'capp_main__notifications_subtitle' : cappMainNotificationsSubtitle,
		'capp_main__notifications_title' : cappMainNotificationsTitle,
		'capp_main__notifications' : cappMainNotifications,
		'capp_main__offers_for_you' : cappMainOffersForYou,
		'capp_main__offers' : cappMainOffers,
		'capp_main__offers_subtitle' : cappMainOffersSubtitle,
		'capp_main__pay_emi' : cappMainPayEmi,
		'capp_main__see_my_challenges' : cappMainSeeMyChallenges,
		'capp_main__see_my_rewards' : cappMainSeeMyRewards,
		'capp_main__select_loan' : cappMainSelectLoan,
		'capp_main__warningzone' : cappMainWarningzone,
		'close' : close,
		'common__add' : commonAdd,
		'common__boost' : commonBoost,
		'common__challenges' : commonChallenges,
		'common__chllngs' : commonChllngs,
		'common__contact' : commonContact,
		'common__edit' : commonEdit,
		'common__game' : commonGame,
		'common__goals' : commonGoals,
		'common__home' : commonHome,
		'common__inbox' : commonInbox,
		'common__no' : commonNo,
		'common__profile' : commonProfile,
		'common__qr' : commonQr,
		'common__remove' : commonRemove,
		'common__repay' : commonRepay,
		'common__save' : commonSave,
		'common__scan' : commonScan,
		'common__wallet' : commonWallet,
		'common__yes' : commonYes,
		'common_not_now' : commonNotNow,
		'contract_history' : contractHistory,
		'electricity_payment' : electricityPayment,
		'error_check_network_title' : errorCheckNetworkTitle,
		'error_check_network_body' : errorCheckNetworkBody,
		'feedback_complete_done' : feedbackCompleteDone,
		'feedback_complete_subtitle' : feedbackCompleteSubtitle,
		'feedback_complete_title' : feedbackCompleteTitle,
		'feedback_form_header' : feedbackFormHeader,
		'feedback_form_hint' : feedbackFormHint,
		'feedback_form_subtitle' : feedbackFormSubtitle,
		'feedback_form_title' : feedbackFormTitle,
		'feedback_form_title_great' : feedbackFormTitleGreat,
		'feedback_form_title_positive' : feedbackFormTitlePositive,
		'feedback_form_warning' : feedbackFormWarning,
		'feedback' : feedback,
		'feedback_rating_1' : feedbackRating1,
		'feedback_rating_2' : feedbackRating2,
		'feedback_rating_3' : feedbackRating3,
		'feedback_rating_4' : feedbackRating4,
		'feedback_rating_5' : feedbackRating5,
		'gift_card' : giftCard,
		'menu_aboutus' : menuAboutus,
		'menu_contactus' : menuContactus,
		'menu_documents' : menuDocuments,
		'menu_faq' : menuFaq,
		'menu_group_general' : menuGroupGeneral,
		'menug_group_services' : menugGroupServices,
		'menu_group_help_support' : menuGroupHelpSupport,
		'menu_group_special_offers' : menuGroupSpecialOffers,
		'menu_help_center' : menuHelpCenter,
		'menu_iprice' : menuIprice,
		'menu_login' : menuLogin,
		'menu_logout' : menuLogout,
		'menu_lock_account' : menuLockAccount,
		'menu_manage_cards' : menuManageCards,
		'menu_myloans' : menuMyloans,
		'menu_notifications' : menuNotifications,
		'menu_personal_details' : menuPersonalDetails,
		'menu_price_link' : menuPriceLink,
		'menu_promo' : menuPromo,
		'menu_retrieve_my_account' : menuRetrieveMyAccount,
		'menu_rewards' : menuRewards,
		'menu_self_service' : menuSelfService,
		'menu_services_hc_protect' : menuServicesHcProtect,
		'menu_settings_biometric' : menuSettingsBiometric,
		'menu_settings_biometric_setup' : menuSettingsBiometricSetup,
		'menu_biometric_unavailable_dialog_title' : menuBiometricUnavailableDialogTitle,
		'menu_biometric_unavailable_dialog_subtitle' : menuBiometricUnavailableDialogSubtitle,
		'menu_biometric_unavailable_dialog_settings' : menuBiometricUnavailableDialogSettings,
		'menu_biometric_unavailable_dialog_cancel' : menuBiometricUnavailableDialogCancel,
		'menu_settings_change_language' : menuSettingsChangeLanguage,
		'menu_settings_change_password_pin' : menuSettingsChangePasswordPin,
		'menu_settings_disable_user' : menuSettingsDisableUser,
		'menu_settings_preferred_communication' : menuSettingsPreferredCommunication,
		'menu_settings' : menuSettings,
		'menu_transaction_history' : menuTransactionHistory,
		'menu_purchased_insurances' : menuPurchasedInsurances,
		'menu_settings_privacy_settings' : menuSettingsPrivacySettings,
		'menu_settings_allow_data_processing' : menuSettingsAllowDataProcessing,
		'menu_settings_terms_and_conditions' : menuSettingsTermsAndConditions,
		'menu_settings_registration_consent_form' : menuSettingsRegistrationConsentForm,
		'menu_settings_privacy_policy' : menuSettingsPrivacyPolicy,
		'menu_settings_receive_marketing_updates' : menuSettingsReceiveMarketingUpdates,
		'menu_settings_marketing_consent_form' : menuSettingsMarketingConsentForm,
		'menu_settings_privacy_error_dialog_title' : menuSettingsPrivacyErrorDialogTitle,
		'menu_settings_privacy_error_dialog_text' : menuSettingsPrivacyErrorDialogText,
		'menu_settings_privacy_error_dialog_btn' : menuSettingsPrivacyErrorDialogBtn,
		'menu_settings_privacy_btn_i_agree' : menuSettingsPrivacyBtnIAgree,
		'menu_settings_privacy_btn_withdraw_consent' : menuSettingsPrivacyBtnWithdrawConsent,
		'not_available' : notAvailable,
		'rate_app_cancel' : rateAppCancel,
		'rate_app_confirm' : rateAppConfirm,
		'rate_app_message_android' : rateAppMessageAndroid,
		'rate_app_title' : rateAppTitle,
		'register' : register,
		'repayment' : repayment,
		'water_payment' : waterPayment,
		'capp_messaging__archived' : cappMessagingArchived,
		'capp_messaging__empty_inbox' : cappMessagingEmptyInbox,
		'capp_messaging__inbox' : cappMessagingInbox,
		'collect' : collect,
		'continue_string' : continueString,
		'delete' : delete,
		'dont_worry_apply_later' : dontWorryApplyLater,
		'expires_in' : expiresIn,
		'goal' : goal,
		'inbox__empty' : inboxEmpty,
		'inbox_empty__subtitle' : inboxEmptySubtitle,
		'inbox_confirm_delete' : inboxConfirmDelete,
		'inbox_filter__all' : inboxFilterAll,
		'inbox_filter__archieved' : inboxFilterArchieved,
		'inbox_filter__coupons' : inboxFilterCoupons,
		'inbox_filter__merchants' : inboxFilterMerchants,
		'inbox_filter__notifications' : inboxFilterNotifications,
		'inbox_filter__rewards' : inboxFilterRewards,
		'inbox_mark_all_read_dialog__title' : inboxMarkAllReadDialogTitle,
		'inbox_mark_all_read_dialog__mark_all_question' : inboxMarkAllReadDialogMarkAllQuestion,
		'inbox_mark_all_read_dialog__yes' : inboxMarkAllReadDialogYes,
		'inbox_mark_all_read_dialog__no' : inboxMarkAllReadDialogNo,
		'message_category_all' : messageCategoryAll,
		'message_deleted' : messageDeleted,
		'message_detail' : messageDetail,
		'mystery_box' : mysteryBox,
		'time_passed__day_ago' : timePassedDayAgo,
		'time_passed__days_ago' : timePassedDaysAgo,
		'time_passed__hour_ago' : timePassedHourAgo,
		'time_passed__hours_ago' : timePassedHoursAgo,
		'time_passed__last_year' : timePassedLastYear,
		'time_passed__minute_ago' : timePassedMinuteAgo,
		'time_passed__minutes_ago' : timePassedMinutesAgo,
		'time_passed__month_ago' : timePassedMonthAgo,
		'time_passed__months_ago' : timePassedMonthsAgo,
		'time_passed__today' : timePassedToday,
		'time_passed__week_ago' : timePassedWeekAgo,
		'time_passed__weeks_ago' : timePassedWeeksAgo,
		'time_passed__year_ago' : timePassedYearAgo,
		'time_passed__years_ago' : timePassedYearsAgo,
		'today' : today,
		'undo' : undo,
		'unread' : unread,
		'valid_until' : validUntil,
		'your_gold' : yourGold,
		'permissions' : permissions,
		'qr_login_header' : qrLoginHeader,
		'qr_login_subheader' : qrLoginSubheader,
		'qr_login_button' : qrLoginButton,
		'submit_and_rate_button' : submitAndRateButton,
		'submit_and_close_button' : submitAndCloseButton,
		'action_belt_item_more' : actionBeltItemMore,
		'home_games_item_caption_text' : homeGamesItemCaptionText,
		'home_games_section_header' : homeGamesSectionHeader,
		'action_belt_e_wallet' : actionBeltEWallet,
		'products_and_services_title' : productsAndServicesTitle,
		'view_all' : viewAll,
		'enable_loan_offer' : enableLoanOffer,
		'welcome' : welcome,
		'menu_ada' : menuAda,
		'my_products' : myProducts,
		'menu_personal_documents' : menuPersonalDocuments,
		'main_tab_qr_scan' : mainTabQrScan,
		'main_tab_qr_viet_qr' : mainTabQrVietQr,
		'main_tab_qr_tgdd' : mainTabQrTgdd,
		'main_tab_qr_dmx' : mainTabQrDmx,
		'main_tab_qr_avakids' : mainTabQrAvakids,
		'emi_calculator_cost' : emiCalculatorCost,
		'emi_calculator_term' : emiCalculatorTerm,
		'emi_calculator_rate' : emiCalculatorRate,
		'emi_calculator_month' : emiCalculatorMonth,
		'emi_calculator_installment' : emiCalculatorInstallment,
		'repayment_product_credit_card' : repaymentProductCreditCard,
		'repayment_product_bnpl_new' : repaymentProductBnplNew,
		'repayment_product_bnpl' : repaymentProductBnpl,
		'repayment_personal_loan' : repaymentPersonalLoan,
		'repayment_insurance_loan' : repaymentInsuranceLoan,
		'repayment_consumer_loan' : repaymentConsumerLoan,
		'repayment_now' : repaymentNow,

  };

  @deprecated
  String tr(String key) {
    return _translate[key] ?? key;
  }
}

class GeneratedLocalizationsDelegate extends LocalizationsDelegate<L10nCappHome> {
  const GeneratedLocalizationsDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
			Locale("vi", "VN"),
			Locale("hi", "IN"),
			Locale("en", "VN"),
			Locale("en", "IN"),

    ];
  }

  LocaleListResolutionCallback listResolution({Locale? fallback}) {
    return (locales, supported) {
      if (locales == null || locales.isEmpty) {
        return fallback ?? supported.first;
      } else {
        return _resolve(locales.first, fallback, supported);
      }
    };
  }

  LocaleResolutionCallback resolution({Locale? fallback}) {
    return (locale, supported) {
      return _resolve(locale, fallback, supported);
    };
  }

  Locale _resolve(Locale? locale, Locale? fallback, Iterable<Locale> supported) {
    if (locale == null || !isSupported(locale)) {
      return fallback ?? supported.first;
    }

    final Locale languageLocale = Locale(locale.languageCode, "");
    if (supported.contains(locale)) {
      return locale;
    } else if (supported.contains(languageLocale)) {
      return languageLocale;
    } else {
      final Locale fallbackLocale = fallback ?? supported.first;
      return fallbackLocale;
    }
  }

  @override
  Future<L10nCappHome> load(Locale locale) {
    return MultipleLocalizations.load(
        initializeMessages, locale, (l) => L10nCappHome.load(locale),
        setDefaultLocale: true);
  }

  @override
  bool isSupported(Locale locale) =>
    locale != null && supportedLocales.contains(locale);

  @override
  bool shouldReload(GeneratedLocalizationsDelegate old) => false;
}

// ignore_for_file: unnecessary_brace_in_string_interps
