import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_localizations/koyal_localizations.dart';

import '../../domain/products/i_products_repository.dart';

part 'products_bloc.freezed.dart';
part 'products_event.dart';
part 'products_state.dart';

class ProductsBloc extends Bloc<ProductsEvent, ProductsState> {
  final IProductsRepository productsRepository;
  final ILocalizationRepository localizationRepository;
  final IIdentityRepository identityRepository;

  ProductsBloc({
    required this.productsRepository,
    required this.localizationRepository,
    required this.identityRepository,
  }) : super(
          const ProductsState(),
        ) {
    on<_Init>(_init);
    on<_LoadProducts>(_loadProducts);
    on<_UserChanged>(_userChanged);
    on<_LanguageChanged>(_languageChanged);
  }

  Future<void> _init(_Init event, Emitter<ProductsState> emit) async {
    emit(
      state.copyWith(
        isLoading: true,
        isError: false,
        products: [],
      ),
    );
    await _userSubscription?.cancel();
    await identityRepository.streamIdentity()?.then(
          (stream) => _userSubscription = stream.listen((user) => add(ProductsEvent.userChanged(user: user))),
        );
    await selectedLanguageSubscription?.cancel();
    selectedLanguageSubscription = localizationRepository.selectedLanguageStream.listen(
      (model) => add(ProductsEvent.languageChanged(language: model.localeInfo.languageCode)),
    );
    emit(
      state.copyWith(
        isLoading: false,
      ),
    );
  }

  Future<void> _loadProducts(_LoadProducts event, Emitter<ProductsState> emit) async {
    emit(
      state.copyWith(
        isLoading: true,
      ),
    );
    final resultProducts = await productsRepository.getProducts(
      userStatus: state.user?.signOnState ?? SignOnStatus.anonymous,
      language: '',
    );
    emit(
      resultProducts.fold(
        (l) => state.copyWith(isLoading: false, isError: true),
        (r) {
          return state.copyWith(
            isLoading: false,
            isError: false,
            products: r,
          );
        },
      ),
    );
  }

  Future<void> _userChanged(_UserChanged e, Emitter<ProductsState> emit) async {
    if (e.user != null && e.user!.id != null && e.user!.id! != state.user?.id) {
      emit(state.copyWith(user: e.user));
    }
  }

  Future<void> _languageChanged(_LanguageChanged event, Emitter<ProductsState> emit) async {
    emit(state.copyWith(language: event.language));
    add(const ProductsEvent.loadProducts());
  }

  StreamSubscription<CurrentUser?>? _userSubscription;
  StreamSubscription<SelectedLanguageModel>? selectedLanguageSubscription;
}
