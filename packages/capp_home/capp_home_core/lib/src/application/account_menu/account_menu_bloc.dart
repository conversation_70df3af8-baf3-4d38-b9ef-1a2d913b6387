import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:equatable/equatable.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

part 'account_menu_event.dart';
part 'account_menu_state.dart';

class AccountMenuBloc extends Bloc<AccountMenuEvent, AccountMenuState> {
  final ICurrentUserRepository repository;
  final IFeatureFlagRepository featureFlagRepository;
  final IBiometricProvider biometricProvider;

  StreamSubscription<void>? _featureFlagsChangesSubscription;

  AccountMenuBloc({
    required this.repository,
    required this.featureFlagRepository,
    required this.biometricProvider,
  }) : super(const AccountMenuState.init()) {
    on<AccountMenuStarted>(_onStarted);

    _featureFlagsChangesSubscription = featureFlagRepository.featureFlagsChanged.stream.listen((event) {
      add(const AccountMenuEvent.init());
    });
  }

  Future<void> _onStarted(AccountMenuStarted e, Emitter<AccountMenuState> emit) async {
    emit(const AccountMenuState.inProgress());

    {
      final user = await repository.getCurrentUser();

      emit(
        AccountMenuState.success(
          isAnonymous: user?.isAnonymous ?? true,
          isInsider: user?.isInsider ?? false,
          isProspect: user?.signOnState == SignOnStatus.registered,
          isClient: user?.signOnState == SignOnStatus.client,
          isIDInsider: featureFlagRepository.isEnabledCached(FeatureFlag.loansBanner),
        ),
      );
    }
  }

  @override
  Future<void> close() async {
    await _featureFlagsChangesSubscription?.cancel();
    _featureFlagsChangesSubscription = null;

    return super.close();
  }
}
