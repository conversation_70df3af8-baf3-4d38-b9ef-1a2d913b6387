part of 'account_menu_bloc.dart';

sealed class AccountMenuState extends Equatable {
  const AccountMenuState();

  const factory AccountMenuState.init() = AccountMenuInitial;
  const factory AccountMenuState.inProgress() = AccountMenuLoadInProgress;
  const factory AccountMenuState.success({
    required bool isAnonymous,
    required bool isInsider,
    required bool isProspect,
    required bool isClient,
    required bool isIDInsider,
  }) = AccountMenuLoadSuccess;

  @override
  List<Object?> get props => [];
}

final class AccountMenuInitial extends AccountMenuState {
  const AccountMenuInitial();
}

final class AccountMenuLoadInProgress extends AccountMenuState {
  const AccountMenuLoadInProgress();
}

final class AccountMenuLoadSuccess extends AccountMenuState {
  final bool isAnonymous;
  final bool isInsider;
  final bool isProspect;
  final bool isClient;
  final bool isIDInsider;

  const AccountMenuLoadSuccess({
    required this.isAnonymous,
    required this.isInsider,
    required this.isProspect,
    required this.isClient,
    required this.isIDInsider,
  });

  @override
  List<Object?> get props => [isAnonymous, isInsider, isProspect, isClient, isIDInsider];
}
