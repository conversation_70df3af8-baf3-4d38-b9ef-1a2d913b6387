import 'package:koyal_auth/koyal_auth.dart';

class MainGuardState {}

class MainGuardStateLoaded extends MainGuardState {
  final bool secondIdSettingRequired;
  final bool passwordChangeRequired;
  final bool showEPFS;
  final bool? showPermissions;
  final String? userId;
  final String? phoneNumber;
  final bool? showLegal;
  final String? consentVersion;
  final String? consentTitle;
  final String? consentData;
  final MaintenanceWindow? maintenanceWindow;

  MainGuardStateLoaded({
    this.secondIdSettingRequired = false,
    this.passwordChangeRequired = false,
    this.userId,
    this.phoneNumber,
    this.showEPFS = false,
    this.showPermissions,
    this.showLegal,
    this.consentVersion,
    this.consentTitle,
    this.consentData,
    this.maintenanceWindow,
  });
}

class MainGuardStateLoading extends MainGuardState {}

class MainGuardStateFinished extends MainGuardState {}
