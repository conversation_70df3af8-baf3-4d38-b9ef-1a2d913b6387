import 'package:bloc/bloc.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:equatable/equatable.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

part 'change_password_event.dart';
part 'change_password_state.dart';

class ChangePasswordBloc extends Bloc<ChangePasswordEvent, ChangePasswordState> {
  final IChangePasswordSettingsRepository changePasswordSettingsRepository;

  ChangePasswordBloc({
    required this.changePasswordSettingsRepository,
  }) : super(const ChangePasswordState.initial()) {
    on<ChangePasswordStarted>(_onStarted);
  }

  Future<void> _onStarted(ChangePasswordStarted e, Emitter<ChangePasswordState> emit) async {
    emit(const ChangePasswordState.inProgress());

    final response = await changePasswordSettingsRepository.postChangePasswordSessionStart();
    final newState = response.fold(
      (l) => ChangePasswordState.failed(failure: l),
      (r) => ChangePasswordState.success(passwordResponse: r),
    );

    // emit the new state and make sure to yield one more with cleared response
    // there is logic in the UI that checks the session on each reload and navigates to the change email flow
    // if the response is not cleared, it will open New Email screen even after change Phone is completed
    emit(newState);
    if (newState is ChangePasswordLoadSuccess && newState.passwordResponse != null) {
      emit(
        const ChangePasswordState.success(),
      );
    }
  }
}
