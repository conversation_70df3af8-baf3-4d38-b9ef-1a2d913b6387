part of 'change_password_bloc.dart';

sealed class ChangePasswordState extends Equatable {
  const ChangePasswordState();

  const factory ChangePasswordState.initial() = ChangePasswordInitial;
  const factory ChangePasswordState.inProgress() = ChangePasswordLoadInProgress;

  const factory ChangePasswordState.success({
    ChangePasswordUrlResponse? passwordResponse,
  }) = ChangePasswordLoadSuccess;

  const factory ChangePasswordState.failed({
    required Failure<UserVerificationFailureType, void> failure,
  }) = ChangePasswordFailure;

  @override
  List<Object?> get props => [];
}

final class ChangePasswordInitial extends ChangePasswordState {
  const ChangePasswordInitial();
}

final class ChangePasswordLoadInProgress extends ChangePasswordState {
  const ChangePasswordLoadInProgress();
}

final class ChangePasswordLoadSuccess extends ChangePasswordState {
  final ChangePasswordUrlResponse? passwordResponse;

  const ChangePasswordLoadSuccess({this.passwordResponse});
  @override
  List<Object?> get props => [passwordResponse];
}

final class ChangePasswordFailure extends ChangePasswordState {
  final Failure<UserVerificationFailureType, void> failure;

  const ChangePasswordFailure({required this.failure});
  @override
  List<Object?> get props => [failure];
}
