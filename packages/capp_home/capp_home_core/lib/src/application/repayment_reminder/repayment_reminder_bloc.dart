import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../capp_home_core.dart';

part 'repayment_reminder_bloc.freezed.dart';

part 'repayment_reminder_event.dart';

part 'repayment_reminder_state.dart';

class RepaymentReminderBloc extends Bloc<RepaymentReminderEvent, RepaymentReminderState> {
  final IRepaymentReminderStorage storage;
  final IContractRepository contractRepository;
  final _ContractMapper _contractMapper;
  final _EligibilityProcessor _eligibilityProcessor;

  InstallmentOverviewBloc? installmentOverviewBloc;

  RepaymentReminderBloc({
    required this.storage,
    required this.contractRepository,
  })  : _contractMapper = _ContractMapper(),
        _eligibilityProcessor = _EligibilityProcessor(storage: storage),
        super(const RepaymentReminderState()) {
    on<_Initialize>(_onInitialize);
    on<_DismissReminder>(_onDismissReminder);
  }

  Future<void> _onInitialize(
    _Initialize event,
    Emitter<RepaymentReminderState> emit,
  ) async {
    final contractsOverview = await contractRepository.getContractsOverviewDetails(refresh: true);
    print(contractsOverview);
    final contracts = contractsOverview.fold<List<RepaymentReminderContract>>(
      (failure) => [],
      _contractMapper.mapContracts,
    );

    final displayEligibility = <String, bool>{};
    for (final contract in contracts) {
      await _eligibilityProcessor.processContract(contract, displayEligibility);
    }
    emit(
      state.copyWith(
        contracts: contracts,
        displayEligibility: displayEligibility,
      ),
    );
  }

  Future<void> _onDismissReminder(
    _DismissReminder event,
    Emitter<RepaymentReminderState> emit,
  ) async {
    await storage.increaseDismissCount(event.contractNumber);
    final updatedEligibility = Map<String, bool>.from(state.displayEligibility)
      ..[event.contractNumber] = false;
    emit(state.copyWith(displayEligibility: updatedEligibility));
  }
}

class _ContractMapper {
  final L10nCappHome localization = L10nCappHome();

  List<RepaymentReminderContract> mapContracts(ContractOverviewDetails details) {
    final contracts = <RepaymentReminderContract>[];

    // Map revolving contracts
    if (details.revolvingContracts != null) {
      contracts.addAll(
        details.revolvingContracts!.map(
          (contract) => RepaymentReminderContract(
            contractNumber: contract.contractInfo.contractNumber,
            contractType: RepaymentReminderContractType.revolving,
            productName: _getRevolvingContractName(contract),
            repaymentEligible: contract.repaymentEligible,
            dueAmount: contract.dueAmount,
            dueDate: contract.dueDate,
          ),
        ),
      );
    }

    // Map loan contracts
    if (details.loanContracts != null) {
      contracts.addAll(
        details.loanContracts!.map(
          (contract) => RepaymentReminderContract(
            contractNumber: contract.contractNumber,
            contractType: RepaymentReminderContractType.loan,
            productName: _getLoanContractName(contract),
            repaymentEligible: contract.repaymentEligible,
            dueAmount: contract.dueAmount?.value,
            dueDate: contract.dueDate,
            productType: contract.productCode,
          ),
        ),
      );
    }

    return contracts;
  }

  String _getRevolvingContractName(RevolvingContractOverviewDetail contract) {
    switch (contract.contractType!) {
      case ContractType.creditCard:
        return localization.repaymentProductCreditCard;
      case ContractType.bnpl:
        return localization.repaymentProductBnplNew;
      default:
        return '';
    }
  }

  String _getLoanContractName(LoanContractOverviewItem contract) {
    switch (contract.contractInfo.contractType) {
      case ContractType.mobile:
        return contract.commodityDescription ??
            contract.productName ??
            localization.repaymentConsumerLoan;
      case ContractType.cash:
        return localization.repaymentPersonalLoan;
      default:
        return '';
    }
  }
}

class _EligibilityProcessor {
  final IRepaymentReminderStorage storage;

  _EligibilityProcessor({required this.storage});

  Future<void> processContract(
    RepaymentReminderContract contract,
    Map<String, bool> displayEligibility,
  ) async {
    await _handleDueDateChange(contract);
    await _handleZeroDueAmount(contract);
    await _updateContractEligibility(contract, displayEligibility);
  }

  Future<void> _handleDueDateChange(RepaymentReminderContract contract) async {
    final lastDueDate = await storage.getStoredDueDate(contract.contractNumber);
    if (contract.dueDate != null &&
        lastDueDate != null &&
        contract.dueDate!.compareTo(lastDueDate) != 0) {
      await storage.clearDismissCount(contract.contractNumber);
    }
    if (contract.dueDate != null) {
      await storage.storeDueDate(contract.contractNumber, contract.dueDate!);
    }
  }

  Future<void> _handleZeroDueAmount(RepaymentReminderContract contract) async {
    if ((contract.repaymentEligible ?? false) &&
        (contract.dueAmount ?? Decimal.zero) == Decimal.zero) {
      await storage.clearDismissCount(contract.contractNumber);
    }
  }

  Future<void> _updateContractEligibility(
    RepaymentReminderContract contract,
    Map<String, bool> displayEligibility,
  ) async {
    final closeCount = await storage.getDismissCount(contract.contractNumber);
    displayEligibility[contract.contractNumber] =
        _isEligible(contract, closeCount);
  }

  bool _isEligible(RepaymentReminderContract contract, int closeCount) {
    if (!(contract.repaymentEligible ?? false)) return false;
    if (closeCount >= 3) return false;
    if ((contract.dueAmount ?? Decimal.zero) == Decimal.zero) return false;

    switch (contract.contractType) {
      case RepaymentReminderContractType.revolving:
        return true;
      case RepaymentReminderContractType.loan:
        if (contract.dueDate == null) return false;
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final daysDiff = contract.dueDate!.difference(today).inDays;
        return daysDiff <= 5 || today.isAfter(contract.dueDate!);
      default:
        return false;
    }
  }
}
