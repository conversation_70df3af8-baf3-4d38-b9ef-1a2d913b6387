// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_reminder_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentReminderEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String contractNumber) dismissReminder,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String contractNumber)? dismissReminder,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String contractNumber)? dismissReminder,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_DismissReminder value) dismissReminder,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_DismissReminder value)? dismissReminder,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_DismissReminder value)? dismissReminder,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentReminderEventCopyWith<$Res> {
  factory $RepaymentReminderEventCopyWith(RepaymentReminderEvent value,
          $Res Function(RepaymentReminderEvent) then) =
      _$RepaymentReminderEventCopyWithImpl<$Res, RepaymentReminderEvent>;
}

/// @nodoc
class _$RepaymentReminderEventCopyWithImpl<$Res,
        $Val extends RepaymentReminderEvent>
    implements $RepaymentReminderEventCopyWith<$Res> {
  _$RepaymentReminderEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentReminderEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize();

  @override
  String toString() {
    return 'RepaymentReminderEvent.initialize()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Initialize);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String contractNumber) dismissReminder,
  }) {
    return initialize();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String contractNumber)? dismissReminder,
  }) {
    return initialize?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String contractNumber)? dismissReminder,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_DismissReminder value) dismissReminder,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_DismissReminder value)? dismissReminder,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_DismissReminder value)? dismissReminder,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentReminderEvent {
  const factory _Initialize() = _$_Initialize;
}

/// @nodoc
abstract class _$$_DismissReminderCopyWith<$Res> {
  factory _$$_DismissReminderCopyWith(
          _$_DismissReminder value, $Res Function(_$_DismissReminder) then) =
      __$$_DismissReminderCopyWithImpl<$Res>;
  @useResult
  $Res call({String contractNumber});
}

/// @nodoc
class __$$_DismissReminderCopyWithImpl<$Res>
    extends _$RepaymentReminderEventCopyWithImpl<$Res, _$_DismissReminder>
    implements _$$_DismissReminderCopyWith<$Res> {
  __$$_DismissReminderCopyWithImpl(
      _$_DismissReminder _value, $Res Function(_$_DismissReminder) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
  }) {
    return _then(_$_DismissReminder(
      null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_DismissReminder implements _DismissReminder {
  const _$_DismissReminder(this.contractNumber);

  @override
  final String contractNumber;

  @override
  String toString() {
    return 'RepaymentReminderEvent.dismissReminder(contractNumber: $contractNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DismissReminder &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DismissReminderCopyWith<_$_DismissReminder> get copyWith =>
      __$$_DismissReminderCopyWithImpl<_$_DismissReminder>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String contractNumber) dismissReminder,
  }) {
    return dismissReminder(contractNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String contractNumber)? dismissReminder,
  }) {
    return dismissReminder?.call(contractNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String contractNumber)? dismissReminder,
    required TResult orElse(),
  }) {
    if (dismissReminder != null) {
      return dismissReminder(contractNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_DismissReminder value) dismissReminder,
  }) {
    return dismissReminder(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_DismissReminder value)? dismissReminder,
  }) {
    return dismissReminder?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_DismissReminder value)? dismissReminder,
    required TResult orElse(),
  }) {
    if (dismissReminder != null) {
      return dismissReminder(this);
    }
    return orElse();
  }
}

abstract class _DismissReminder implements RepaymentReminderEvent {
  const factory _DismissReminder(final String contractNumber) =
      _$_DismissReminder;

  String get contractNumber;
  @JsonKey(ignore: true)
  _$$_DismissReminderCopyWith<_$_DismissReminder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentReminderState {
  List<RepaymentReminderContract> get contracts =>
      throw _privateConstructorUsedError;
  Map<String, bool> get displayEligibility =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentReminderStateCopyWith<RepaymentReminderState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentReminderStateCopyWith<$Res> {
  factory $RepaymentReminderStateCopyWith(RepaymentReminderState value,
          $Res Function(RepaymentReminderState) then) =
      _$RepaymentReminderStateCopyWithImpl<$Res, RepaymentReminderState>;
  @useResult
  $Res call(
      {List<RepaymentReminderContract> contracts,
      Map<String, bool> displayEligibility});
}

/// @nodoc
class _$RepaymentReminderStateCopyWithImpl<$Res,
        $Val extends RepaymentReminderState>
    implements $RepaymentReminderStateCopyWith<$Res> {
  _$RepaymentReminderStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contracts = null,
    Object? displayEligibility = null,
  }) {
    return _then(_value.copyWith(
      contracts: null == contracts
          ? _value.contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentReminderContract>,
      displayEligibility: null == displayEligibility
          ? _value.displayEligibility
          : displayEligibility // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentReminderStateCopyWith<$Res>
    implements $RepaymentReminderStateCopyWith<$Res> {
  factory _$$_RepaymentReminderStateCopyWith(_$_RepaymentReminderState value,
          $Res Function(_$_RepaymentReminderState) then) =
      __$$_RepaymentReminderStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<RepaymentReminderContract> contracts,
      Map<String, bool> displayEligibility});
}

/// @nodoc
class __$$_RepaymentReminderStateCopyWithImpl<$Res>
    extends _$RepaymentReminderStateCopyWithImpl<$Res,
        _$_RepaymentReminderState>
    implements _$$_RepaymentReminderStateCopyWith<$Res> {
  __$$_RepaymentReminderStateCopyWithImpl(_$_RepaymentReminderState _value,
      $Res Function(_$_RepaymentReminderState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contracts = null,
    Object? displayEligibility = null,
  }) {
    return _then(_$_RepaymentReminderState(
      contracts: null == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentReminderContract>,
      displayEligibility: null == displayEligibility
          ? _value._displayEligibility
          : displayEligibility // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
    ));
  }
}

/// @nodoc

class _$_RepaymentReminderState implements _RepaymentReminderState {
  const _$_RepaymentReminderState(
      {final List<RepaymentReminderContract> contracts = const [],
      final Map<String, bool> displayEligibility = const {}})
      : _contracts = contracts,
        _displayEligibility = displayEligibility;

  final List<RepaymentReminderContract> _contracts;
  @override
  @JsonKey()
  List<RepaymentReminderContract> get contracts {
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contracts);
  }

  final Map<String, bool> _displayEligibility;
  @override
  @JsonKey()
  Map<String, bool> get displayEligibility {
    if (_displayEligibility is EqualUnmodifiableMapView)
      return _displayEligibility;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_displayEligibility);
  }

  @override
  String toString() {
    return 'RepaymentReminderState(contracts: $contracts, displayEligibility: $displayEligibility)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentReminderState &&
            const DeepCollectionEquality()
                .equals(other._contracts, _contracts) &&
            const DeepCollectionEquality()
                .equals(other._displayEligibility, _displayEligibility));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_contracts),
      const DeepCollectionEquality().hash(_displayEligibility));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentReminderStateCopyWith<_$_RepaymentReminderState> get copyWith =>
      __$$_RepaymentReminderStateCopyWithImpl<_$_RepaymentReminderState>(
          this, _$identity);
}

abstract class _RepaymentReminderState implements RepaymentReminderState {
  const factory _RepaymentReminderState(
      {final List<RepaymentReminderContract> contracts,
      final Map<String, bool> displayEligibility}) = _$_RepaymentReminderState;

  @override
  List<RepaymentReminderContract> get contracts;
  @override
  Map<String, bool> get displayEligibility;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentReminderStateCopyWith<_$_RepaymentReminderState> get copyWith =>
      throw _privateConstructorUsedError;
}
