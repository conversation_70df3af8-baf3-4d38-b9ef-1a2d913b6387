import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../presentation/action_belt/widgets/action_belt_debouncer.dart';

part 'action_belt_bloc.freezed.dart';
part 'action_belt_event.dart';
part 'action_belt_state.dart';

class ActionBeltBloc extends Bloc<ActionBeltEvent, ActionBeltState> {
  final IIdentityRepository identityRepository;
  final ILocalizationRepository localizationRepository;
  final IActionBeltRepository actionBeltRepository;
  final IFeatureFlagRepository featureFlagRepository;
  final IUserRepository userRepository;
  final ICurrentUserRepository currentUserRepository;
  final bool actionBeltSegmentationEnabled;

  static const _defaultLanguage = 'en';

  StreamSubscription<CurrentUser?>? _userSubscription;
  StreamSubscription<SelectedLanguageModel>? selectedLanguageSubscription;

  StreamSubscription<void>? _refreshStreamSubscription;

  ActionBeltBloc({
    required this.identityRepository,
    required this.localizationRepository,
    required this.actionBeltRepository,
    required this.featureFlagRepository,
    required this.userRepository,
    required this.currentUserRepository,
    required this.actionBeltSegmentationEnabled,
  }) : super(const ActionBeltState()) {
    on<_Init>(_init);
    on<_UserChanged>(_userChanged);
    on<_LanguageChanged>(_languageChanged);
    on<_LoadWidgets>(_loadWidgets);
    on<_SubscribeToRefresh>(_subscribeToRefresh);
  }

  Future<void> _init(_Init event, Emitter<ActionBeltState> emit) async {
    {
      final selectedLocale = await localizationRepository.loadSelectedLocale();
      final languageCode = selectedLocale?.languageCode ?? _defaultLanguage;

      // Used for PH shopingmall url
      final userCuid = await userRepository.userCuid();
      final userGuid = await currentUserRepository.userId();
      if (userCuid != null && actionBeltSegmentationEnabled) {
        final userSegment = await actionBeltRepository.getUserSegment();
        emit(
          userSegment.fold(
            (l) => state.copyWith(userSegment: UserSegment.none),
            (r) => state.copyWith(
              userSegment: r,
              useUserSegmentation: true,
            ),
          ),
        );
      }

      emit(state.copyWith(language: languageCode, userCuid: userCuid, userGuid: userGuid));
      await _userSubscription?.cancel();
      await identityRepository.streamIdentity()?.then(
            (stream) => _userSubscription = stream.listen((user) async {
              add(ActionBeltEvent.userChanged(user: user));
            }),
          );

      await selectedLanguageSubscription?.cancel();
      selectedLanguageSubscription = localizationRepository.selectedLanguageStream.listen(
        (model) {
          if (model.localeInfo.languageCode != languageCode) {
            add(ActionBeltEvent.languageChanged(language: model.localeInfo.languageCode));
          }
        },
      );
    }
  }

  Future<void> _userChanged(_UserChanged e, Emitter<ActionBeltState> emit) async {
    {
      final updateUserState = featureFlagRepository.isEnabledCached(FeatureFlag.actionBeltUpdateUserState);

      if (e.user != null && e.user!.id != null && (e.user!.id! != state.user?.id || updateUserState)) {
        emit(state.copyWith(user: e.user, isLoading: true));
        add(ActionBeltEvent.loadWidgets(userStatus: e.user!.signOnState ?? SignOnStatus.anonymous));
      }
    }
  }

  Future<void> _languageChanged(_LanguageChanged event, Emitter<ActionBeltState> emit) async {
    emit(state.copyWith(language: event.language));
    add(ActionBeltEvent.loadWidgets(userStatus: state.user!.signOnState ?? SignOnStatus.anonymous));
  }

  Future<void> _loadWidgets(_LoadWidgets event, Emitter<ActionBeltState> emit) async {
    final onlyEnabled = featureFlagRepository.isEnabledCached(FeatureFlag.actionBeltShowDisabledItems) == false;
    final response = await actionBeltRepository.getActionBeltItems(
      userStatus: event.userStatus,
      language: state.language ?? _defaultLanguage,
      onlyEnabled: onlyEnabled,
      useSegmentation: state.useUserSegmentation,
      userSegment: state.userSegment,
    );
    emit(
      response.fold(
        (l) => state.copyWith(
          isLoading: false,
          actionBeltWidgets: null,
        ),
        (r) => state.copyWith(isLoading: false, actionBeltWidgets: r, widgets: null),
      ),
    );
  }

  Future<void> _subscribeToRefresh(_SubscribeToRefresh event, Emitter<ActionBeltState> emit) async {
    await _refreshStreamSubscription?.cancel();
    _refreshStreamSubscription = event.refreshStream.listen((event) {
      if (state.user != null && state.user!.signOnState != null) {
        add(ActionBeltEvent.loadWidgets(userStatus: state.user!.signOnState!));
      }
    });
  }

  @override
  Future<void> close() {
    _userSubscription?.cancel();
    selectedLanguageSubscription?.cancel();
    _refreshStreamSubscription?.cancel();

    // Clear action belt debounce timers when bloc is disposed
    ActionBeltDebouncer.clearAll();

    return super.close();
  }
}
