part of 'action_belt_bloc.dart';

@freezed
class ActionBeltState with _$ActionBeltState {
  const factory ActionBeltState({
    @Default(true) bool isLoading,
    @Default(false) bool useUserSegmentation,
    CurrentUser? user,
    String? language,
    List<String>? widgets,
    List<UserActionBeltWidget>? actionBeltWidgets,
    String? userCuid,
    String? userGuid,
    @Default(UserSegment.none) UserSegment userSegment,
  }) = _ActionBeltState;
}
