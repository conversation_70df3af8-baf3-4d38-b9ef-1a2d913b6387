part of 'action_belt_bloc.dart';

@freezed
class ActionBeltEvent with _$ActionBeltEvent {
  const factory ActionBeltEvent.init() = _Init;
  const factory ActionBeltEvent.userChanged({required CurrentUser? user}) = _UserChanged;
  const factory ActionBeltEvent.languageChanged({String? language}) = _LanguageChanged;
  const factory ActionBeltEvent.loadWidgets({required SignOnStatus userStatus}) = _LoadWidgets;
  const factory ActionBeltEvent.subscribeToRefresh({required Stream<void> refreshStream}) = _SubscribeToRefresh;
}
