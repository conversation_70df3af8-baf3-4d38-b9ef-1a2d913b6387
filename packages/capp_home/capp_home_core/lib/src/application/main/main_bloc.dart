import 'dart:async';

import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

part 'main_bloc.freezed.dart';
part 'main_event.dart';
part 'main_state.dart';

class MainBloc extends Bloc<MainEvent, MainState> {
  final IAppStateRepository appStateRepository;
  final ICurrentUserRepository profileRepository;
  final StartupMeasurementService startupMeasurementService;
  StreamSubscription<CurrentUser?>? _userProfileSubscription;

  MainBloc({
    required this.appStateRepository,
    required this.profileRepository,
    required this.startupMeasurementService,
  }) : super(MainState.initialize()) {
    on<_InitMain>(_initMain);
    on<_SelectTab>(_selectTab);
    on<_KeyboardHasClosed>(_keyboardHasClosed);
  }

  Future<void> _initMain(_InitMain event, Emitter<MainState> emit) async {
    await appStateRepository.completeAppStart();
    final isAnonymous = await profileRepository.isCurrentUserAnonymous();
    startupMeasurementService.stopMeasure(StartupMeasurementOrigin.mainScreen);
    emit(state.copyWith(selectedTab: event.initialTab, anonymous: isAnonymous));
  }

  Future<void> _selectTab(_SelectTab event, Emitter<MainState> emit) async {
    emit(state.copyWith(selectedTab: event.tabIndex));
  }

  Future<void> _keyboardHasClosed(_KeyboardHasClosed event, Emitter<MainState> emit) async {
    if (event.closed != state.hasKeyboardClosed) {
      emit(state.copyWith(hasKeyboardClosed: event.closed));
    }
  }

  @override
  Future close() {
    _userProfileSubscription?.cancel();
    return super.close();
  }
}
