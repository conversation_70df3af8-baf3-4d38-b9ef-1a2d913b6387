part of 'games_banner_bloc.dart';

sealed class GamesBannerState extends Equatable {
  const GamesBannerState();

  const factory GamesBannerState.initial() = GameBannerInitial;
  const factory GamesBannerState.inProgress() = GameBannerLoadInProgress;
  const factory GamesBannerState.success({
    required String bannerImageUrl,
  }) = GameBannerLoadSuccess;
  const factory GamesBannerState.failure() = GameBannerFailure;

  @override
  List<Object?> get props => [];
}

final class GameBannerInitial extends GamesBannerState {
  const GameBannerInitial();
}

final class GameBannerLoadInProgress extends GamesBannerState {
  const GameBannerLoadInProgress();
}

final class GameBannerLoadSuccess extends GamesBannerState {
  final String bannerImageUrl;
  const GameBannerLoadSuccess({required this.bannerImageUrl});

  @override
  List<Object?> get props => [bannerImageUrl];
}

final class GameBannerFailure extends GamesBannerState {
  const GameBannerFailure();
}
