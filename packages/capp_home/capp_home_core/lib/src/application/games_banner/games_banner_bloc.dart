import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../domain/games_banner/i_games_banner_repository.dart';

part 'games_banner_event.dart';
part 'games_banner_state.dart';

class GamesBannerBloc extends Bloc<GamesBannerEvent, GamesBannerState> {
  final IGamesBannerRepository gamesBannerRepository;
  final ImageServiceBase imageService;

  StreamSubscription<void>? _refreshStreamSubscription;

  GamesBannerBloc({
    required this.gamesBannerRepository,
    required this.imageService,
  }) : super(const GamesBannerState.initial()) {
    on<GameBannerStarted>(_onInitLoading);
    on<GameBannerSubscribeToRefresh>((event, _) => _onSubscribeToRefresh(event));
  }

  Future<void> _onInitLoading(GameBannerStarted event, Emitter<GamesBannerState> emit) async {
    emit(const GamesBannerState.inProgress());

    final response = await gamesBannerRepository.getGamesBanner();
    emit(
      response.fold(
        (_) => const GamesBannerState.failure(),
        (r) => GamesBannerState.success(bannerImageUrl: imageService.getUrlFromId(r.image?.id ?? '') ?? ''),
      ),
    );
  }

  void _onSubscribeToRefresh(GameBannerSubscribeToRefresh event) {
    _refreshStreamSubscription = event.refreshStream.listen((event) {
      add(const GamesBannerEvent.initLoading());
    });
  }

  @override
  Future<void> close() async {
    await _refreshStreamSubscription?.cancel();
    _refreshStreamSubscription = null;

    return super.close();
  }
}
