part of 'games_banner_bloc.dart';

sealed class GamesBannerEvent {
  const GamesBannerEvent();

  const factory GamesBannerEvent.initLoading() = GameBannerStarted;
  const factory GamesBannerEvent.subscribeToRefresh({required Stream<void> refreshStream}) =
      GameBannerSubscribeToRefresh;
}

final class GameBannerStarted extends GamesBannerEvent {
  const GameBannerStarted();
}

final class GameBannerSubscribeToRefresh extends GamesBannerEvent {
  final Stream<void> refreshStream;
  const GameBannerSubscribeToRefresh({required this.refreshStream});
}
