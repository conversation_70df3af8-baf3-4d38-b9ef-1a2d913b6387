import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:collection/collection.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../domain/feedback/feedback_enums.dart';
import '../../infrastructure/feedback/i_feedback_journey_service.dart';
import '../../infrastructure/feedback/i_feedback_repository.dart';

part 'feedback_bloc.freezed.dart';
part 'feedback_event.dart';
part 'feedback_state.dart';

class FeedbackBloc extends Bloc<FeedbackEvent, FeedbackState> {
  final IEnumerationsRepository enumerationsRepository;
  final IFeedbackRepository feedbackRepository;
  final IFeedbackJourneyService feedbackJourneyService;
  final CappTrackingService cappTrackingService;
  final IAppVersion appVersion;

  FeedbackBloc({
    required this.enumerationsRepository,
    required this.feedbackRepository,
    required this.feedbackJourneyService,
    required this.cappTrackingService,
    required this.appVersion,
  }) : super(const FeedbackState()) {
    on<_Init>(_init);
    on<_LoadFeedbackCategories>(_loadFeedbackCategories);
    on<_SelectRating>(_selectRating);
    on<_CategorySelected>(_selectCategory);
    on<_FeedbackMessageChanged>((event, emit) => emit(state.copyWith(feedbackMessage: event.feedbackMessage)));
    on<_SendPositiveFeedback>(_sendPositiveFeedback);
    on<_SendNegativeFeedback>(_sendNegativeFeedback);
    on<_SendFeedback>(_sendFeedback);
    on<_Postpone>(_postpone);
  }

  FeedbackPolarity _resolveFeedbackPolarity(bool isJourneyIdNull, int? rating) {
    if (rating == null) return FeedbackPolarity.none;
    if (isJourneyIdNull) {
      return rating > 0 ? FeedbackPolarity.positive : FeedbackPolarity.negative;
    } else {
      // when rating between 1 and 5, only top 2 are considered positive
      return rating > 3 ? FeedbackPolarity.positive : FeedbackPolarity.negative;
    }
  }

  String? _resolveFeedbackMessage(String? feedbackMessage) =>
      (feedbackMessage?.isNotEmpty ?? false) ? feedbackMessage : null;

  Future<void> _selectRating(_SelectRating event, Emitter<FeedbackState> emit) async {
    emit(
      state.copyWith(
        selectedRating: event.rating,
        feedbackPolarity: _resolveFeedbackPolarity(state.journeyId == null, event.rating),
      ),
    );
  }

  Future<void> _init(_Init event, Emitter<FeedbackState> emit) async {
    final flows = await enumerationsRepository.getFeedbackFlows();
    final question = flows.fold(
      (l) => '',
      (r) => r.items?.where((x) => x.id == event.journeyId).map((x) => x.title).firstOrNull,
    );
    emit(state.copyWith(journeyId: event.journeyId, question: question ?? ''));
    if (state.isAppRatingLimitLoading) return;
  }

  Future<void> _loadFeedbackCategories(_LoadFeedbackCategories event, Emitter<FeedbackState> emit) async {
    if (state.isCategoriesLoading) return;
    if (event.journeyId != null && state.isCategoriesLoaded) return;
    final isAppRatingAvailable = await feedbackJourneyService.canRateApp();
    emit(
      state.copyWith(
        isAppRatingLimitLoading: false,
        canRateApp: isAppRatingAvailable,
        appStoreId: appVersion.appStoreId,
        googlePlayId: appVersion.googlePlayId,
        isHuawei: Platform.isAndroid && appVersion.appGalleryId != null,
      ),
    );
    if (event.journeyId != null) {
      final response = await feedbackRepository.getFeedbackCategories(journeyId: event.journeyId!);
      emit(
        response.fold(
          (_) => state.copyWith(isCategoriesLoading: false, isError: true),
          (response) => state.copyWith(
            isCategoriesLoading: false,
            isCategoriesLoaded: true,
            feedbackCategories: <FeedbackCategory, bool>{for (final item in response) item: false},
          ),
        ),
      );
    } else {
      final response = await enumerationsRepository.getFeedbackCategories();
      emit(
        response.fold(
          (_) => state.copyWith(isCategoriesLoading: false, isError: true),
          (response) {
            final categories =
                response.items?.map((item) => FeedbackCategory(id: item.id, title: item.title)).toList() ?? [];
            return state.copyWith(
              isCategoriesLoading: false,
              feedbackCategories: <FeedbackCategory, bool>{for (final item in categories) item: false},
            );
          },
        ),
      );
    }
  }

  Future<void> _selectCategory(_CategorySelected event, Emitter<FeedbackState> emit) async {
    if (event.category == null) return;
    final map = {for (final entry in state.feedbackCategories.entries) entry.key: entry.value};
    map[event.category!] = event.selected ?? true;
    emit(state.copyWith(feedbackCategories: map));
  }

  Future<void> _sendPositiveFeedback(_SendPositiveFeedback event, Emitter<FeedbackState> emit) async {
    if (state.isSending) return;
    emit(state.copyWith(isSending: true));
    await cappTrackingService.trackFeedbackEvent(
      event: KoyalEvent.appFeedbackPositiveAdditionalCommentEn,
      category: KoyalTrackingCategories.feedbackPositive,
      action: KoyalAnalyticsConstants.enter,
      label: 'additional_comment',
      feedbackJourneyId: state.journeyId,
      negativeFeedbackText: _resolveFeedbackMessage(state.feedbackMessage),
    );
    final response = await feedbackRepository.sendPositiveFeedback(
      feedbackCategoryIds: state.feedbackCategories.entries.where((x) => x.value).map((y) => y.key.id).toList(),
      text: _resolveFeedbackMessage(state.feedbackMessage),
    );
    emit(
      response.fold(
        (_) => state.copyWith(isSending: false, isError: true),
        (response) =>
            state.copyWith(isSending: false, isFeedbackCompleted: true, feedbackPolarity: FeedbackPolarity.positive),
      ),
    );
  }

  Future<void> _sendNegativeFeedback(_SendNegativeFeedback event, Emitter<FeedbackState> emit) async {
    if (state.isSending) return;
    emit(state.copyWith(isSending: true));
    await cappTrackingService.trackFeedbackEvent(
      event: KoyalEvent.appFeedbackNegativeConcernsEnter,
      category: KoyalTrackingCategories.feedbackNegative,
      action: KoyalAnalyticsConstants.enter,
      label: 'concerns',
      feedbackJourneyId: state.journeyId,
      negativeFeedbackText: _resolveFeedbackMessage(state.feedbackMessage),
      sas: '1',
    );
    final response = await feedbackRepository.sendNegativeFeedback(
      feedbackCategoryIds: state.feedbackCategories.entries.where((x) => x.value).map((y) => y.key.id).toList(),
      text: _resolveFeedbackMessage(state.feedbackMessage),
    );
    emit(
      response.fold(
        (_) => state.copyWith(isSending: false, isError: true),
        (response) =>
            state.copyWith(isSending: false, isFeedbackCompleted: true, feedbackPolarity: FeedbackPolarity.negative),
      ),
    );
  }

  Future<void> _sendFeedback(_SendFeedback event, Emitter<FeedbackState> emit) async {
    if (state.selectedRating == null || state.journeyId == null) return;
    emit(state.copyWith(isSending: true));
    await cappTrackingService.trackFeedbackEvent(
      event: KoyalEvent.appFeatureFeedbackTextEnter,
      category: KoyalTrackingCategories.featureFeedback,
      action: KoyalAnalyticsConstants.enter,
      label: 'written_feedback',
      feedbackJourneyId: state.journeyId,
      featureFeedbackText: _resolveFeedbackMessage(state.feedbackMessage),
    );
    final categoryIds = <String>[];
    for (final category in state.feedbackCategories.entries) {
      if (category.value) {
        categoryIds.add(category.key.id);
      }
    }
    final response = await feedbackRepository.sendFeedback(
      journeyId: state.journeyId!,
      rating: state.selectedRating!,
      comment: _resolveFeedbackMessage(state.feedbackMessage),
      categoryIds: categoryIds,
    );
    final success = response.fold((l) => false, (r) => true);
    if (success) {
      await feedbackJourneyService.setComplete(state.journeyId!);
    }
    emit(
      state.copyWith(
        isSending: false,
        isFeedbackCompleted: true,
        feedbackPolarity: _resolveFeedbackPolarity(false, state.selectedRating),
      ),
    );
  }

  Future<void> _postpone(_Postpone event, Emitter<FeedbackState> emit) async {
    if (state.journeyId == null) return;
    await feedbackJourneyService.postponeJourney(journeyId: state.journeyId!);
  }
}
