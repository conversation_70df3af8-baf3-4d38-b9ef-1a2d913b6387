import 'package:meta/meta.dart';

import '../../capp_home_feedback.dart';

abstract class FeedbackSubmitCriteria {
  bool canSubmit(FeedbackState state);

  // @protected
  // bool ratedAndNotLoading(FeedbackState state) =>
  //     state.selectedRating != null &&
  //     ((state.selectedRating == 0) || !(state.isAppRatingLimitLoading || state.isCategoriesLoading));
  @protected
  bool ratedAndNotLoading(FeedbackState state) =>
      state.selectedRating != null && !(state.isAppRatingLimitLoading || state.isCategoriesLoading);

  @protected
  bool feedbackPositive(FeedbackState state) => state.feedbackPolarity.isPositive;

  @protected
  bool feedbackNegative(FeedbackState state) => state.feedbackPolarity.isNegative;
}

/// expects user to just hit the submit button
class FeedbackSubmitCriteriaLoose extends FeedbackSubmitCriteria {
  @override
  bool canSubmit(FeedbackState state) => ratedAndNotLoading(state);
}

/// expects user to select at least one category chip or enter a comment
class FeedbackSubmitCriteriaStrict extends FeedbackSubmitCriteria {
  @override
  bool canSubmit(FeedbackState state) {
    return ratedAndNotLoading(state) &&
        ((state.feedbackMessage ?? '').trim().isNotEmpty ||
            state.feedbackCategories.values.where((selected) => selected).isNotEmpty);
  }
}
