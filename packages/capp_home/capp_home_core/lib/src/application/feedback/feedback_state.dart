part of 'feedback_bloc.dart';

@freezed
class FeedbackState with _$FeedbackState {
  const factory FeedbackState({
    @Default(false) bool isLoading,
    @Default(false) bool isCategoriesLoading,
    @Default(false) bool isAppRatingLimitLoading,
    @Default(false) bool isError,
    @Default(false) bool isSending,
    @Default(false) bool canRateApp,
    @Default(<FeedbackCategory, bool>{}) Map<FeedbackCategory, bool> feedbackCategories,
    @Default('') String question,
    String? journeyId,
    String? appStoreId,
    String? googlePlayId,
    @Default(false) bool isHuawei,
    int? selectedRating,
    String? feedbackMessage,
    @Default(false) bool isFeedbackCompleted,
    @Default(false) bool isCategoriesLoaded,
    @Default(FeedbackPolarity.none) FeedbackPolarity feedbackPolarity,
  }) = _FeedbackState;
}
