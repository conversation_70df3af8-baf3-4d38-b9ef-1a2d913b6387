// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'feedback_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$FeedbackEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedbackEventCopyWith<$Res> {
  factory $FeedbackEventCopyWith(
          FeedbackEvent value, $Res Function(FeedbackEvent) then) =
      _$FeedbackEventCopyWithImpl<$Res, FeedbackEvent>;
}

/// @nodoc
class _$FeedbackEventCopyWithImpl<$Res, $Val extends FeedbackEvent>
    implements $FeedbackEventCopyWith<$Res> {
  _$FeedbackEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitCopyWith<$Res> {
  factory _$$_InitCopyWith(_$_Init value, $Res Function(_$_Init) then) =
      __$$_InitCopyWithImpl<$Res>;
  @useResult
  $Res call({String journeyId});
}

/// @nodoc
class __$$_InitCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_Init>
    implements _$$_InitCopyWith<$Res> {
  __$$_InitCopyWithImpl(_$_Init _value, $Res Function(_$_Init) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? journeyId = null,
  }) {
    return _then(_$_Init(
      journeyId: null == journeyId
          ? _value.journeyId
          : journeyId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Init implements _Init {
  const _$_Init({required this.journeyId});

  @override
  final String journeyId;

  @override
  String toString() {
    return 'FeedbackEvent.init(journeyId: $journeyId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Init &&
            (identical(other.journeyId, journeyId) ||
                other.journeyId == journeyId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, journeyId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitCopyWith<_$_Init> get copyWith =>
      __$$_InitCopyWithImpl<_$_Init>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return init(journeyId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return init?.call(journeyId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(journeyId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class _Init implements FeedbackEvent {
  const factory _Init({required final String journeyId}) = _$_Init;

  String get journeyId;
  @JsonKey(ignore: true)
  _$$_InitCopyWith<_$_Init> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_LoadFeedbackCategoriesCopyWith<$Res> {
  factory _$$_LoadFeedbackCategoriesCopyWith(_$_LoadFeedbackCategories value,
          $Res Function(_$_LoadFeedbackCategories) then) =
      __$$_LoadFeedbackCategoriesCopyWithImpl<$Res>;
  @useResult
  $Res call({String? journeyId});
}

/// @nodoc
class __$$_LoadFeedbackCategoriesCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_LoadFeedbackCategories>
    implements _$$_LoadFeedbackCategoriesCopyWith<$Res> {
  __$$_LoadFeedbackCategoriesCopyWithImpl(_$_LoadFeedbackCategories _value,
      $Res Function(_$_LoadFeedbackCategories) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? journeyId = freezed,
  }) {
    return _then(_$_LoadFeedbackCategories(
      journeyId: freezed == journeyId
          ? _value.journeyId
          : journeyId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_LoadFeedbackCategories implements _LoadFeedbackCategories {
  const _$_LoadFeedbackCategories({this.journeyId});

  @override
  final String? journeyId;

  @override
  String toString() {
    return 'FeedbackEvent.loadFeedbackCategories(journeyId: $journeyId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LoadFeedbackCategories &&
            (identical(other.journeyId, journeyId) ||
                other.journeyId == journeyId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, journeyId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LoadFeedbackCategoriesCopyWith<_$_LoadFeedbackCategories> get copyWith =>
      __$$_LoadFeedbackCategoriesCopyWithImpl<_$_LoadFeedbackCategories>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return loadFeedbackCategories(journeyId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return loadFeedbackCategories?.call(journeyId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (loadFeedbackCategories != null) {
      return loadFeedbackCategories(journeyId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return loadFeedbackCategories(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return loadFeedbackCategories?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (loadFeedbackCategories != null) {
      return loadFeedbackCategories(this);
    }
    return orElse();
  }
}

abstract class _LoadFeedbackCategories implements FeedbackEvent {
  const factory _LoadFeedbackCategories({final String? journeyId}) =
      _$_LoadFeedbackCategories;

  String? get journeyId;
  @JsonKey(ignore: true)
  _$$_LoadFeedbackCategoriesCopyWith<_$_LoadFeedbackCategories> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectRatingCopyWith<$Res> {
  factory _$$_SelectRatingCopyWith(
          _$_SelectRating value, $Res Function(_$_SelectRating) then) =
      __$$_SelectRatingCopyWithImpl<$Res>;
  @useResult
  $Res call({int? rating});
}

/// @nodoc
class __$$_SelectRatingCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_SelectRating>
    implements _$$_SelectRatingCopyWith<$Res> {
  __$$_SelectRatingCopyWithImpl(
      _$_SelectRating _value, $Res Function(_$_SelectRating) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rating = freezed,
  }) {
    return _then(_$_SelectRating(
      freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$_SelectRating implements _SelectRating {
  const _$_SelectRating(this.rating);

  @override
  final int? rating;

  @override
  String toString() {
    return 'FeedbackEvent.selectRating(rating: $rating)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectRating &&
            (identical(other.rating, rating) || other.rating == rating));
  }

  @override
  int get hashCode => Object.hash(runtimeType, rating);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectRatingCopyWith<_$_SelectRating> get copyWith =>
      __$$_SelectRatingCopyWithImpl<_$_SelectRating>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return selectRating(rating);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return selectRating?.call(rating);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (selectRating != null) {
      return selectRating(rating);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return selectRating(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return selectRating?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (selectRating != null) {
      return selectRating(this);
    }
    return orElse();
  }
}

abstract class _SelectRating implements FeedbackEvent {
  const factory _SelectRating(final int? rating) = _$_SelectRating;

  int? get rating;
  @JsonKey(ignore: true)
  _$$_SelectRatingCopyWith<_$_SelectRating> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_CategorySelectedCopyWith<$Res> {
  factory _$$_CategorySelectedCopyWith(
          _$_CategorySelected value, $Res Function(_$_CategorySelected) then) =
      __$$_CategorySelectedCopyWithImpl<$Res>;
  @useResult
  $Res call({FeedbackCategory? category, bool? selected});

  $FeedbackCategoryCopyWith<$Res>? get category;
}

/// @nodoc
class __$$_CategorySelectedCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_CategorySelected>
    implements _$$_CategorySelectedCopyWith<$Res> {
  __$$_CategorySelectedCopyWithImpl(
      _$_CategorySelected _value, $Res Function(_$_CategorySelected) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? category = freezed,
    Object? selected = freezed,
  }) {
    return _then(_$_CategorySelected(
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as FeedbackCategory?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $FeedbackCategoryCopyWith<$Res>? get category {
    if (_value.category == null) {
      return null;
    }

    return $FeedbackCategoryCopyWith<$Res>(_value.category!, (value) {
      return _then(_value.copyWith(category: value));
    });
  }
}

/// @nodoc

class _$_CategorySelected implements _CategorySelected {
  const _$_CategorySelected({this.category, this.selected});

  @override
  final FeedbackCategory? category;
  @override
  final bool? selected;

  @override
  String toString() {
    return 'FeedbackEvent.selectCategory(category: $category, selected: $selected)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CategorySelected &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.selected, selected) ||
                other.selected == selected));
  }

  @override
  int get hashCode => Object.hash(runtimeType, category, selected);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CategorySelectedCopyWith<_$_CategorySelected> get copyWith =>
      __$$_CategorySelectedCopyWithImpl<_$_CategorySelected>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return selectCategory(category, selected);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return selectCategory?.call(category, selected);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (selectCategory != null) {
      return selectCategory(category, selected);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return selectCategory(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return selectCategory?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (selectCategory != null) {
      return selectCategory(this);
    }
    return orElse();
  }
}

abstract class _CategorySelected implements FeedbackEvent {
  const factory _CategorySelected(
      {final FeedbackCategory? category,
      final bool? selected}) = _$_CategorySelected;

  FeedbackCategory? get category;
  bool? get selected;
  @JsonKey(ignore: true)
  _$$_CategorySelectedCopyWith<_$_CategorySelected> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FeedbackMessageChangedCopyWith<$Res> {
  factory _$$_FeedbackMessageChangedCopyWith(_$_FeedbackMessageChanged value,
          $Res Function(_$_FeedbackMessageChanged) then) =
      __$$_FeedbackMessageChangedCopyWithImpl<$Res>;
  @useResult
  $Res call({String? feedbackMessage});
}

/// @nodoc
class __$$_FeedbackMessageChangedCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_FeedbackMessageChanged>
    implements _$$_FeedbackMessageChangedCopyWith<$Res> {
  __$$_FeedbackMessageChangedCopyWithImpl(_$_FeedbackMessageChanged _value,
      $Res Function(_$_FeedbackMessageChanged) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? feedbackMessage = freezed,
  }) {
    return _then(_$_FeedbackMessageChanged(
      feedbackMessage: freezed == feedbackMessage
          ? _value.feedbackMessage
          : feedbackMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_FeedbackMessageChanged implements _FeedbackMessageChanged {
  const _$_FeedbackMessageChanged({this.feedbackMessage});

  @override
  final String? feedbackMessage;

  @override
  String toString() {
    return 'FeedbackEvent.feedbackMessageChanged(feedbackMessage: $feedbackMessage)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FeedbackMessageChanged &&
            (identical(other.feedbackMessage, feedbackMessage) ||
                other.feedbackMessage == feedbackMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, feedbackMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FeedbackMessageChangedCopyWith<_$_FeedbackMessageChanged> get copyWith =>
      __$$_FeedbackMessageChangedCopyWithImpl<_$_FeedbackMessageChanged>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return feedbackMessageChanged(feedbackMessage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return feedbackMessageChanged?.call(feedbackMessage);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (feedbackMessageChanged != null) {
      return feedbackMessageChanged(feedbackMessage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return feedbackMessageChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return feedbackMessageChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (feedbackMessageChanged != null) {
      return feedbackMessageChanged(this);
    }
    return orElse();
  }
}

abstract class _FeedbackMessageChanged implements FeedbackEvent {
  const factory _FeedbackMessageChanged({final String? feedbackMessage}) =
      _$_FeedbackMessageChanged;

  String? get feedbackMessage;
  @JsonKey(ignore: true)
  _$$_FeedbackMessageChangedCopyWith<_$_FeedbackMessageChanged> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SendPositiveFeedbackCopyWith<$Res> {
  factory _$$_SendPositiveFeedbackCopyWith(_$_SendPositiveFeedback value,
          $Res Function(_$_SendPositiveFeedback) then) =
      __$$_SendPositiveFeedbackCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isRating});
}

/// @nodoc
class __$$_SendPositiveFeedbackCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_SendPositiveFeedback>
    implements _$$_SendPositiveFeedbackCopyWith<$Res> {
  __$$_SendPositiveFeedbackCopyWithImpl(_$_SendPositiveFeedback _value,
      $Res Function(_$_SendPositiveFeedback) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRating = null,
  }) {
    return _then(_$_SendPositiveFeedback(
      isRating: null == isRating
          ? _value.isRating
          : isRating // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_SendPositiveFeedback implements _SendPositiveFeedback {
  const _$_SendPositiveFeedback({this.isRating = false});

  @override
  @JsonKey()
  final bool isRating;

  @override
  String toString() {
    return 'FeedbackEvent.sendPositiveFeedback(isRating: $isRating)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SendPositiveFeedback &&
            (identical(other.isRating, isRating) ||
                other.isRating == isRating));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRating);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SendPositiveFeedbackCopyWith<_$_SendPositiveFeedback> get copyWith =>
      __$$_SendPositiveFeedbackCopyWithImpl<_$_SendPositiveFeedback>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return sendPositiveFeedback(isRating);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return sendPositiveFeedback?.call(isRating);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (sendPositiveFeedback != null) {
      return sendPositiveFeedback(isRating);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return sendPositiveFeedback(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return sendPositiveFeedback?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (sendPositiveFeedback != null) {
      return sendPositiveFeedback(this);
    }
    return orElse();
  }
}

abstract class _SendPositiveFeedback implements FeedbackEvent {
  const factory _SendPositiveFeedback({final bool isRating}) =
      _$_SendPositiveFeedback;

  bool get isRating;
  @JsonKey(ignore: true)
  _$$_SendPositiveFeedbackCopyWith<_$_SendPositiveFeedback> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SendNegativeFeedbackCopyWith<$Res> {
  factory _$$_SendNegativeFeedbackCopyWith(_$_SendNegativeFeedback value,
          $Res Function(_$_SendNegativeFeedback) then) =
      __$$_SendNegativeFeedbackCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SendNegativeFeedbackCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_SendNegativeFeedback>
    implements _$$_SendNegativeFeedbackCopyWith<$Res> {
  __$$_SendNegativeFeedbackCopyWithImpl(_$_SendNegativeFeedback _value,
      $Res Function(_$_SendNegativeFeedback) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SendNegativeFeedback implements _SendNegativeFeedback {
  const _$_SendNegativeFeedback();

  @override
  String toString() {
    return 'FeedbackEvent.sendNegativeFeedback()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SendNegativeFeedback);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return sendNegativeFeedback();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return sendNegativeFeedback?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (sendNegativeFeedback != null) {
      return sendNegativeFeedback();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return sendNegativeFeedback(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return sendNegativeFeedback?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (sendNegativeFeedback != null) {
      return sendNegativeFeedback(this);
    }
    return orElse();
  }
}

abstract class _SendNegativeFeedback implements FeedbackEvent {
  const factory _SendNegativeFeedback() = _$_SendNegativeFeedback;
}

/// @nodoc
abstract class _$$_SendFeedbackCopyWith<$Res> {
  factory _$$_SendFeedbackCopyWith(
          _$_SendFeedback value, $Res Function(_$_SendFeedback) then) =
      __$$_SendFeedbackCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SendFeedbackCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_SendFeedback>
    implements _$$_SendFeedbackCopyWith<$Res> {
  __$$_SendFeedbackCopyWithImpl(
      _$_SendFeedback _value, $Res Function(_$_SendFeedback) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SendFeedback implements _SendFeedback {
  const _$_SendFeedback();

  @override
  String toString() {
    return 'FeedbackEvent.sendFeedback()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SendFeedback);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return sendFeedback();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return sendFeedback?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (sendFeedback != null) {
      return sendFeedback();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return sendFeedback(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return sendFeedback?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (sendFeedback != null) {
      return sendFeedback(this);
    }
    return orElse();
  }
}

abstract class _SendFeedback implements FeedbackEvent {
  const factory _SendFeedback() = _$_SendFeedback;
}

/// @nodoc
abstract class _$$_PostponeCopyWith<$Res> {
  factory _$$_PostponeCopyWith(
          _$_Postpone value, $Res Function(_$_Postpone) then) =
      __$$_PostponeCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_PostponeCopyWithImpl<$Res>
    extends _$FeedbackEventCopyWithImpl<$Res, _$_Postpone>
    implements _$$_PostponeCopyWith<$Res> {
  __$$_PostponeCopyWithImpl(
      _$_Postpone _value, $Res Function(_$_Postpone) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Postpone implements _Postpone {
  const _$_Postpone();

  @override
  String toString() {
    return 'FeedbackEvent.postpone()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Postpone);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String journeyId) init,
    required TResult Function(String? journeyId) loadFeedbackCategories,
    required TResult Function(int? rating) selectRating,
    required TResult Function(FeedbackCategory? category, bool? selected)
        selectCategory,
    required TResult Function(String? feedbackMessage) feedbackMessageChanged,
    required TResult Function(bool isRating) sendPositiveFeedback,
    required TResult Function() sendNegativeFeedback,
    required TResult Function() sendFeedback,
    required TResult Function() postpone,
  }) {
    return postpone();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String journeyId)? init,
    TResult? Function(String? journeyId)? loadFeedbackCategories,
    TResult? Function(int? rating)? selectRating,
    TResult? Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult? Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult? Function(bool isRating)? sendPositiveFeedback,
    TResult? Function()? sendNegativeFeedback,
    TResult? Function()? sendFeedback,
    TResult? Function()? postpone,
  }) {
    return postpone?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String journeyId)? init,
    TResult Function(String? journeyId)? loadFeedbackCategories,
    TResult Function(int? rating)? selectRating,
    TResult Function(FeedbackCategory? category, bool? selected)?
        selectCategory,
    TResult Function(String? feedbackMessage)? feedbackMessageChanged,
    TResult Function(bool isRating)? sendPositiveFeedback,
    TResult Function()? sendNegativeFeedback,
    TResult Function()? sendFeedback,
    TResult Function()? postpone,
    required TResult orElse(),
  }) {
    if (postpone != null) {
      return postpone();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Init value) init,
    required TResult Function(_LoadFeedbackCategories value)
        loadFeedbackCategories,
    required TResult Function(_SelectRating value) selectRating,
    required TResult Function(_CategorySelected value) selectCategory,
    required TResult Function(_FeedbackMessageChanged value)
        feedbackMessageChanged,
    required TResult Function(_SendPositiveFeedback value) sendPositiveFeedback,
    required TResult Function(_SendNegativeFeedback value) sendNegativeFeedback,
    required TResult Function(_SendFeedback value) sendFeedback,
    required TResult Function(_Postpone value) postpone,
  }) {
    return postpone(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Init value)? init,
    TResult? Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult? Function(_SelectRating value)? selectRating,
    TResult? Function(_CategorySelected value)? selectCategory,
    TResult? Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult? Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult? Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult? Function(_SendFeedback value)? sendFeedback,
    TResult? Function(_Postpone value)? postpone,
  }) {
    return postpone?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Init value)? init,
    TResult Function(_LoadFeedbackCategories value)? loadFeedbackCategories,
    TResult Function(_SelectRating value)? selectRating,
    TResult Function(_CategorySelected value)? selectCategory,
    TResult Function(_FeedbackMessageChanged value)? feedbackMessageChanged,
    TResult Function(_SendPositiveFeedback value)? sendPositiveFeedback,
    TResult Function(_SendNegativeFeedback value)? sendNegativeFeedback,
    TResult Function(_SendFeedback value)? sendFeedback,
    TResult Function(_Postpone value)? postpone,
    required TResult orElse(),
  }) {
    if (postpone != null) {
      return postpone(this);
    }
    return orElse();
  }
}

abstract class _Postpone implements FeedbackEvent {
  const factory _Postpone() = _$_Postpone;
}

/// @nodoc
mixin _$FeedbackState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isCategoriesLoading => throw _privateConstructorUsedError;
  bool get isAppRatingLimitLoading => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  bool get isSending => throw _privateConstructorUsedError;
  bool get canRateApp => throw _privateConstructorUsedError;
  Map<FeedbackCategory, bool> get feedbackCategories =>
      throw _privateConstructorUsedError;
  String get question => throw _privateConstructorUsedError;
  String? get journeyId => throw _privateConstructorUsedError;
  String? get appStoreId => throw _privateConstructorUsedError;
  String? get googlePlayId => throw _privateConstructorUsedError;
  bool get isHuawei => throw _privateConstructorUsedError;
  int? get selectedRating => throw _privateConstructorUsedError;
  String? get feedbackMessage => throw _privateConstructorUsedError;
  bool get isFeedbackCompleted => throw _privateConstructorUsedError;
  bool get isCategoriesLoaded => throw _privateConstructorUsedError;
  FeedbackPolarity get feedbackPolarity => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FeedbackStateCopyWith<FeedbackState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FeedbackStateCopyWith<$Res> {
  factory $FeedbackStateCopyWith(
          FeedbackState value, $Res Function(FeedbackState) then) =
      _$FeedbackStateCopyWithImpl<$Res, FeedbackState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isCategoriesLoading,
      bool isAppRatingLimitLoading,
      bool isError,
      bool isSending,
      bool canRateApp,
      Map<FeedbackCategory, bool> feedbackCategories,
      String question,
      String? journeyId,
      String? appStoreId,
      String? googlePlayId,
      bool isHuawei,
      int? selectedRating,
      String? feedbackMessage,
      bool isFeedbackCompleted,
      bool isCategoriesLoaded,
      FeedbackPolarity feedbackPolarity});
}

/// @nodoc
class _$FeedbackStateCopyWithImpl<$Res, $Val extends FeedbackState>
    implements $FeedbackStateCopyWith<$Res> {
  _$FeedbackStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isCategoriesLoading = null,
    Object? isAppRatingLimitLoading = null,
    Object? isError = null,
    Object? isSending = null,
    Object? canRateApp = null,
    Object? feedbackCategories = null,
    Object? question = null,
    Object? journeyId = freezed,
    Object? appStoreId = freezed,
    Object? googlePlayId = freezed,
    Object? isHuawei = null,
    Object? selectedRating = freezed,
    Object? feedbackMessage = freezed,
    Object? isFeedbackCompleted = null,
    Object? isCategoriesLoaded = null,
    Object? feedbackPolarity = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCategoriesLoading: null == isCategoriesLoading
          ? _value.isCategoriesLoading
          : isCategoriesLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isAppRatingLimitLoading: null == isAppRatingLimitLoading
          ? _value.isAppRatingLimitLoading
          : isAppRatingLimitLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSending: null == isSending
          ? _value.isSending
          : isSending // ignore: cast_nullable_to_non_nullable
              as bool,
      canRateApp: null == canRateApp
          ? _value.canRateApp
          : canRateApp // ignore: cast_nullable_to_non_nullable
              as bool,
      feedbackCategories: null == feedbackCategories
          ? _value.feedbackCategories
          : feedbackCategories // ignore: cast_nullable_to_non_nullable
              as Map<FeedbackCategory, bool>,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      journeyId: freezed == journeyId
          ? _value.journeyId
          : journeyId // ignore: cast_nullable_to_non_nullable
              as String?,
      appStoreId: freezed == appStoreId
          ? _value.appStoreId
          : appStoreId // ignore: cast_nullable_to_non_nullable
              as String?,
      googlePlayId: freezed == googlePlayId
          ? _value.googlePlayId
          : googlePlayId // ignore: cast_nullable_to_non_nullable
              as String?,
      isHuawei: null == isHuawei
          ? _value.isHuawei
          : isHuawei // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedRating: freezed == selectedRating
          ? _value.selectedRating
          : selectedRating // ignore: cast_nullable_to_non_nullable
              as int?,
      feedbackMessage: freezed == feedbackMessage
          ? _value.feedbackMessage
          : feedbackMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isFeedbackCompleted: null == isFeedbackCompleted
          ? _value.isFeedbackCompleted
          : isFeedbackCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isCategoriesLoaded: null == isCategoriesLoaded
          ? _value.isCategoriesLoaded
          : isCategoriesLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      feedbackPolarity: null == feedbackPolarity
          ? _value.feedbackPolarity
          : feedbackPolarity // ignore: cast_nullable_to_non_nullable
              as FeedbackPolarity,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FeedbackStateCopyWith<$Res>
    implements $FeedbackStateCopyWith<$Res> {
  factory _$$_FeedbackStateCopyWith(
          _$_FeedbackState value, $Res Function(_$_FeedbackState) then) =
      __$$_FeedbackStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isCategoriesLoading,
      bool isAppRatingLimitLoading,
      bool isError,
      bool isSending,
      bool canRateApp,
      Map<FeedbackCategory, bool> feedbackCategories,
      String question,
      String? journeyId,
      String? appStoreId,
      String? googlePlayId,
      bool isHuawei,
      int? selectedRating,
      String? feedbackMessage,
      bool isFeedbackCompleted,
      bool isCategoriesLoaded,
      FeedbackPolarity feedbackPolarity});
}

/// @nodoc
class __$$_FeedbackStateCopyWithImpl<$Res>
    extends _$FeedbackStateCopyWithImpl<$Res, _$_FeedbackState>
    implements _$$_FeedbackStateCopyWith<$Res> {
  __$$_FeedbackStateCopyWithImpl(
      _$_FeedbackState _value, $Res Function(_$_FeedbackState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isCategoriesLoading = null,
    Object? isAppRatingLimitLoading = null,
    Object? isError = null,
    Object? isSending = null,
    Object? canRateApp = null,
    Object? feedbackCategories = null,
    Object? question = null,
    Object? journeyId = freezed,
    Object? appStoreId = freezed,
    Object? googlePlayId = freezed,
    Object? isHuawei = null,
    Object? selectedRating = freezed,
    Object? feedbackMessage = freezed,
    Object? isFeedbackCompleted = null,
    Object? isCategoriesLoaded = null,
    Object? feedbackPolarity = null,
  }) {
    return _then(_$_FeedbackState(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCategoriesLoading: null == isCategoriesLoading
          ? _value.isCategoriesLoading
          : isCategoriesLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isAppRatingLimitLoading: null == isAppRatingLimitLoading
          ? _value.isAppRatingLimitLoading
          : isAppRatingLimitLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSending: null == isSending
          ? _value.isSending
          : isSending // ignore: cast_nullable_to_non_nullable
              as bool,
      canRateApp: null == canRateApp
          ? _value.canRateApp
          : canRateApp // ignore: cast_nullable_to_non_nullable
              as bool,
      feedbackCategories: null == feedbackCategories
          ? _value._feedbackCategories
          : feedbackCategories // ignore: cast_nullable_to_non_nullable
              as Map<FeedbackCategory, bool>,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      journeyId: freezed == journeyId
          ? _value.journeyId
          : journeyId // ignore: cast_nullable_to_non_nullable
              as String?,
      appStoreId: freezed == appStoreId
          ? _value.appStoreId
          : appStoreId // ignore: cast_nullable_to_non_nullable
              as String?,
      googlePlayId: freezed == googlePlayId
          ? _value.googlePlayId
          : googlePlayId // ignore: cast_nullable_to_non_nullable
              as String?,
      isHuawei: null == isHuawei
          ? _value.isHuawei
          : isHuawei // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedRating: freezed == selectedRating
          ? _value.selectedRating
          : selectedRating // ignore: cast_nullable_to_non_nullable
              as int?,
      feedbackMessage: freezed == feedbackMessage
          ? _value.feedbackMessage
          : feedbackMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isFeedbackCompleted: null == isFeedbackCompleted
          ? _value.isFeedbackCompleted
          : isFeedbackCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isCategoriesLoaded: null == isCategoriesLoaded
          ? _value.isCategoriesLoaded
          : isCategoriesLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      feedbackPolarity: null == feedbackPolarity
          ? _value.feedbackPolarity
          : feedbackPolarity // ignore: cast_nullable_to_non_nullable
              as FeedbackPolarity,
    ));
  }
}

/// @nodoc

class _$_FeedbackState implements _FeedbackState {
  const _$_FeedbackState(
      {this.isLoading = false,
      this.isCategoriesLoading = false,
      this.isAppRatingLimitLoading = false,
      this.isError = false,
      this.isSending = false,
      this.canRateApp = false,
      final Map<FeedbackCategory, bool> feedbackCategories =
          const <FeedbackCategory, bool>{},
      this.question = '',
      this.journeyId,
      this.appStoreId,
      this.googlePlayId,
      this.isHuawei = false,
      this.selectedRating,
      this.feedbackMessage,
      this.isFeedbackCompleted = false,
      this.isCategoriesLoaded = false,
      this.feedbackPolarity = FeedbackPolarity.none})
      : _feedbackCategories = feedbackCategories;

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isCategoriesLoading;
  @override
  @JsonKey()
  final bool isAppRatingLimitLoading;
  @override
  @JsonKey()
  final bool isError;
  @override
  @JsonKey()
  final bool isSending;
  @override
  @JsonKey()
  final bool canRateApp;
  final Map<FeedbackCategory, bool> _feedbackCategories;
  @override
  @JsonKey()
  Map<FeedbackCategory, bool> get feedbackCategories {
    if (_feedbackCategories is EqualUnmodifiableMapView)
      return _feedbackCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_feedbackCategories);
  }

  @override
  @JsonKey()
  final String question;
  @override
  final String? journeyId;
  @override
  final String? appStoreId;
  @override
  final String? googlePlayId;
  @override
  @JsonKey()
  final bool isHuawei;
  @override
  final int? selectedRating;
  @override
  final String? feedbackMessage;
  @override
  @JsonKey()
  final bool isFeedbackCompleted;
  @override
  @JsonKey()
  final bool isCategoriesLoaded;
  @override
  @JsonKey()
  final FeedbackPolarity feedbackPolarity;

  @override
  String toString() {
    return 'FeedbackState(isLoading: $isLoading, isCategoriesLoading: $isCategoriesLoading, isAppRatingLimitLoading: $isAppRatingLimitLoading, isError: $isError, isSending: $isSending, canRateApp: $canRateApp, feedbackCategories: $feedbackCategories, question: $question, journeyId: $journeyId, appStoreId: $appStoreId, googlePlayId: $googlePlayId, isHuawei: $isHuawei, selectedRating: $selectedRating, feedbackMessage: $feedbackMessage, isFeedbackCompleted: $isFeedbackCompleted, isCategoriesLoaded: $isCategoriesLoaded, feedbackPolarity: $feedbackPolarity)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FeedbackState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isCategoriesLoading, isCategoriesLoading) ||
                other.isCategoriesLoading == isCategoriesLoading) &&
            (identical(
                    other.isAppRatingLimitLoading, isAppRatingLimitLoading) ||
                other.isAppRatingLimitLoading == isAppRatingLimitLoading) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.isSending, isSending) ||
                other.isSending == isSending) &&
            (identical(other.canRateApp, canRateApp) ||
                other.canRateApp == canRateApp) &&
            const DeepCollectionEquality()
                .equals(other._feedbackCategories, _feedbackCategories) &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.journeyId, journeyId) ||
                other.journeyId == journeyId) &&
            (identical(other.appStoreId, appStoreId) ||
                other.appStoreId == appStoreId) &&
            (identical(other.googlePlayId, googlePlayId) ||
                other.googlePlayId == googlePlayId) &&
            (identical(other.isHuawei, isHuawei) ||
                other.isHuawei == isHuawei) &&
            (identical(other.selectedRating, selectedRating) ||
                other.selectedRating == selectedRating) &&
            (identical(other.feedbackMessage, feedbackMessage) ||
                other.feedbackMessage == feedbackMessage) &&
            (identical(other.isFeedbackCompleted, isFeedbackCompleted) ||
                other.isFeedbackCompleted == isFeedbackCompleted) &&
            (identical(other.isCategoriesLoaded, isCategoriesLoaded) ||
                other.isCategoriesLoaded == isCategoriesLoaded) &&
            (identical(other.feedbackPolarity, feedbackPolarity) ||
                other.feedbackPolarity == feedbackPolarity));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isCategoriesLoading,
      isAppRatingLimitLoading,
      isError,
      isSending,
      canRateApp,
      const DeepCollectionEquality().hash(_feedbackCategories),
      question,
      journeyId,
      appStoreId,
      googlePlayId,
      isHuawei,
      selectedRating,
      feedbackMessage,
      isFeedbackCompleted,
      isCategoriesLoaded,
      feedbackPolarity);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FeedbackStateCopyWith<_$_FeedbackState> get copyWith =>
      __$$_FeedbackStateCopyWithImpl<_$_FeedbackState>(this, _$identity);
}

abstract class _FeedbackState implements FeedbackState {
  const factory _FeedbackState(
      {final bool isLoading,
      final bool isCategoriesLoading,
      final bool isAppRatingLimitLoading,
      final bool isError,
      final bool isSending,
      final bool canRateApp,
      final Map<FeedbackCategory, bool> feedbackCategories,
      final String question,
      final String? journeyId,
      final String? appStoreId,
      final String? googlePlayId,
      final bool isHuawei,
      final int? selectedRating,
      final String? feedbackMessage,
      final bool isFeedbackCompleted,
      final bool isCategoriesLoaded,
      final FeedbackPolarity feedbackPolarity}) = _$_FeedbackState;

  @override
  bool get isLoading;
  @override
  bool get isCategoriesLoading;
  @override
  bool get isAppRatingLimitLoading;
  @override
  bool get isError;
  @override
  bool get isSending;
  @override
  bool get canRateApp;
  @override
  Map<FeedbackCategory, bool> get feedbackCategories;
  @override
  String get question;
  @override
  String? get journeyId;
  @override
  String? get appStoreId;
  @override
  String? get googlePlayId;
  @override
  bool get isHuawei;
  @override
  int? get selectedRating;
  @override
  String? get feedbackMessage;
  @override
  bool get isFeedbackCompleted;
  @override
  bool get isCategoriesLoaded;
  @override
  FeedbackPolarity get feedbackPolarity;
  @override
  @JsonKey(ignore: true)
  _$$_FeedbackStateCopyWith<_$_FeedbackState> get copyWith =>
      throw _privateConstructorUsedError;
}
