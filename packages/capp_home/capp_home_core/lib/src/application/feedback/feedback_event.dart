part of 'feedback_bloc.dart';

@freezed
class FeedbackEvent with _$FeedbackEvent {
  const factory FeedbackEvent.init({required String journeyId}) = _Init;
  const factory FeedbackEvent.loadFeedbackCategories({String? journeyId}) = _LoadFeedbackCategories;
  const factory FeedbackEvent.selectRating(int? rating) = _SelectRating;
  const factory FeedbackEvent.selectCategory({FeedbackCategory? category, bool? selected}) = _CategorySelected;
  const factory FeedbackEvent.feedbackMessageChanged({String? feedbackMessage}) = _FeedbackMessageChanged;
  const factory FeedbackEvent.sendPositiveFeedback({@Default(false) bool isRating}) = _SendPositiveFeedback;
  const factory FeedbackEvent.sendNegativeFeedback() = _SendNegativeFeedback;
  const factory FeedbackEvent.sendFeedback() = _SendFeedback;
  const factory FeedbackEvent.postpone() = _Postpone;
}
