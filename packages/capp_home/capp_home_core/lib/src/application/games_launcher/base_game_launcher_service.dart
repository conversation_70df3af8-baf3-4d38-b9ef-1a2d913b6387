import 'package:capp_domain/capp_domain.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import 'games_launcher_ff_handler.dart';
import 'games_launcher_strategy.dart';
import 'games_launcher_tracking.dart';

/// Base service for running games via url in custom tab
abstract class BaseGamesLauncherService with GamesLauncherTracking {
  GamesUrlVerificationStrategy strategy = GameNotVerifiedStrategy();

  /// Uses event tracking and game url based od [GamesViewType]
  Future<void> launchGame(
    BuildContext context,
    GamesViewType viewType,
  ) async {
    trackGamesEvent(context, viewType);
    await launchUrl(context, viewType.url);
  }

  Future<void> launchUrl(BuildContext context, String url) async {
    await checkAuthorizationNeeded(context, url);
    final gameUrl = await strategy.verifyUrl(url);
    if (gameUrl.isNotEmpty) {
      await CustomTabsHelper.openUrl(gameUrl);
    }
    // reset strategy to release potential references
    strategy = GameNotVerifiedStrategy();
  }

  // Template method where implementation should select correct strategy to use
  Future<void> checkAuthorizationNeeded(
    BuildContext context,
    String url, {
    GamesLauncherFfHandler ff = const GamesLauncherFfHandler.empty(),
  });
}
