abstract interface class GamesUrlVerificationStrategy {
  Future<String> verifyUrl(final String url);
}

/// Strategy used when authorization is not allowed
final class GameNotVerifiedStrategy implements GamesUrlVerificationStrategy {
  @override
  Future<String> verifyUrl(String url) async => url;
}

/// Strategy used in case of problem
final class GameEmptyStrategy implements GamesUrlVerificationStrategy {
  @override
  Future<String> verifyUrl(String url) async => '';
}
