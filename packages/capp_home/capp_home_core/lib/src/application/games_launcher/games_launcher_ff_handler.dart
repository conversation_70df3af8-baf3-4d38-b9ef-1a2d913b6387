import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/material.dart';
import 'package:koyal_shared/koyal_shared.dart';

/// Main purpose of this handler is to overcome feature bloc inside unit test
sealed class GamesLauncherFfHandler {
  const GamesLauncherFfHandler();

  const factory GamesLauncherFfHandler.empty() = GamesLauncherFfEmpty;
  const factory GamesLauncherFfHandler.allowed() = GamesLauncherFfAllowed;
  const factory GamesLauncherFfHandler.notAllowed() = GamesLauncherFfNotAllowed;
  factory GamesLauncherFfHandler.flagSmith(BuildContext context) => GamesLauncherFfFlagSmith(context);

  bool isAuthAllowed();

  bool get isEmpty => this is GamesLauncherFfEmpty;
}

final class GamesLauncherFfAllowed extends GamesLauncherFfHandler {
  const GamesLauncherFfAllowed();
  @override
  bool isAuthAllowed() => true;
}

final class GamesLauncherFfNotAllowed extends GamesLauncherFfHandler {
  const GamesLauncherFfNotAllowed();
  @override
  bool isAuthAllowed() => false;
}

final class GamesLauncherFfEmpty extends GamesLauncherFfHandler {
  const GamesLauncherFfEmpty();
  @override
  bool isAuthAllowed() => false;
}

final class GamesLauncherFfFlagSmith extends GamesLauncherFfHandler {
  final BuildContext context;

  GamesLauncherFfFlagSmith(this.context);

  @override
  bool isAuthAllowed() => context.isFlagEnabledRead(FeatureFlag.gamesGamificationUserAuthorizationVn);
}
