import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

mixin GamesLauncherTracking {
  void trackGamesEvent(BuildContext context, GamesViewType viewType) {
    unawaited(
      context.get<CappTrackingService>().trackEvent(
            event: _getEventId(viewType),
            eventCategory: KoyalTrackingCategories.screenView,
            eventAction: KoyalAnalyticsConstants.view,
            eventLabel: _getEventLabel(viewType),
          ),
    );
  }

  String _getEventLabel(GamesViewType type) {
    switch (type) {
      case GamesViewType.gameHub:
        return 'game_screen_gamehub';
      case GamesViewType.rewardWallet:
        return 'game_screen_reward';
      case GamesViewType.mission:
        return 'game_screen_mission';
      case GamesViewType.event:
        return 'game_screen_event';
      default:
        return 'game_screen_gamehub';
    }
  }

  KoyalEvent _getEventId(GamesViewType type) {
    switch (type) {
      case GamesViewType.gameHub:
        return KoyalEvent.appGameScreenViewGame;
      case GamesViewType.rewardWallet:
        return KoyalEvent.appGameScreenViewRewardWallet;
      case GamesViewType.mission:
        return KoyalEvent.appGameScreenViewMission;
      case GamesViewType.event:
        return KoyalEvent.appGameScreenViewEvent;
      default:
        return KoyalEvent.appGameScreenViewGame;
    }
  }
}
