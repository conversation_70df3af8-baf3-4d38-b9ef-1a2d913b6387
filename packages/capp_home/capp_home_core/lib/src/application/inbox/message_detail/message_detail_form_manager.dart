import 'package:dynamic_forms/dynamic_forms.dart';
import 'package:flutter_dynamic_forms_components/flutter_dynamic_forms_components.dart';

import '../../../services/dynamic_forms/widgets/form_author/form_author_parser.g.dart';
import '../../../services/dynamic_forms/widgets/form_header_title/form_header_title_parser.g.dart';
import '../../../services/dynamic_forms/widgets/form_image/form_image_parser.g.dart';
import '../../../services/dynamic_forms/widgets/form_text/form_text_parser.g.dart';
import '../../../services/dynamic_forms/widgets/form_video_player/form_video_player_parser.g.dart';
import '../../../services/dynamic_forms/widgets/redirect_to_browser_button/redirect_to_browser_button_parser.g.dart';

class MessageDetailFormManager extends ParsedFormManager {
  final bool isXml;

  MessageDetailFormManager({required this.isXml});

  List<FormElementParser<FormElement>> getParsers() =>
      <FormElementParser<FormElement>>[
        FormAuthorParser(),
        FormImageParser(),
        FormHeaderTitleParser(),
        FormTextParser(),
        FormVideoPlayerParser(),
        RedirectToBrowserButtonParser(),
      ] +
      getDefaultParserList();

  void initWithContent(String content) {
    init(content: content, parsers: getParsers());
  }

  @override
  FormParserService getFormParserService(List<FormElementParser<FormElement>> parsers) =>
      isXml ? XmlFormParserService(parsers) : JsonFormParserService(parsers);
}
