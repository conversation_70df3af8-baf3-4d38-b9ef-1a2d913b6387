part of 'message_detail_bloc.dart';

@freezed
class MessageDetailEvent with _$MessageDetailEvent {
  const factory MessageDetailEvent.init({required String messageId, required BuildContext context}) =
      _InitMessageDetail;
  const factory MessageDetailEvent.changeValue({
    required String elementId,
    required String propertyName,
    required dynamic value,
    @Default(false) bool ignoreLastChange,
  }) = ChangeValue;
}
