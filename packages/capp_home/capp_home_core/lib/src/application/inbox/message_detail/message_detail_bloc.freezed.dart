// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'message_detail_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$MessageDetailEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String messageId, BuildContext context) init,
    required TResult Function(String elementId, String propertyName,
            dynamic value, bool ignoreLastChange)
        changeValue,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String messageId, BuildContext context)? init,
    TResult? Function(String elementId, String propertyName, dynamic value,
            bool ignoreLastChange)?
        changeValue,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String messageId, BuildContext context)? init,
    TResult Function(String elementId, String propertyName, dynamic value,
            bool ignoreLastChange)?
        changeValue,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitMessageDetail value) init,
    required TResult Function(ChangeValue value) changeValue,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitMessageDetail value)? init,
    TResult? Function(ChangeValue value)? changeValue,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitMessageDetail value)? init,
    TResult Function(ChangeValue value)? changeValue,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageDetailEventCopyWith<$Res> {
  factory $MessageDetailEventCopyWith(
          MessageDetailEvent value, $Res Function(MessageDetailEvent) then) =
      _$MessageDetailEventCopyWithImpl<$Res, MessageDetailEvent>;
}

/// @nodoc
class _$MessageDetailEventCopyWithImpl<$Res, $Val extends MessageDetailEvent>
    implements $MessageDetailEventCopyWith<$Res> {
  _$MessageDetailEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitMessageDetailCopyWith<$Res> {
  factory _$$_InitMessageDetailCopyWith(_$_InitMessageDetail value,
          $Res Function(_$_InitMessageDetail) then) =
      __$$_InitMessageDetailCopyWithImpl<$Res>;
  @useResult
  $Res call({String messageId, BuildContext context});
}

/// @nodoc
class __$$_InitMessageDetailCopyWithImpl<$Res>
    extends _$MessageDetailEventCopyWithImpl<$Res, _$_InitMessageDetail>
    implements _$$_InitMessageDetailCopyWith<$Res> {
  __$$_InitMessageDetailCopyWithImpl(
      _$_InitMessageDetail _value, $Res Function(_$_InitMessageDetail) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? messageId = null,
    Object? context = null,
  }) {
    return _then(_$_InitMessageDetail(
      messageId: null == messageId
          ? _value.messageId
          : messageId // ignore: cast_nullable_to_non_nullable
              as String,
      context: null == context
          ? _value.context
          : context // ignore: cast_nullable_to_non_nullable
              as BuildContext,
    ));
  }
}

/// @nodoc

class _$_InitMessageDetail
    with DiagnosticableTreeMixin
    implements _InitMessageDetail {
  const _$_InitMessageDetail({required this.messageId, required this.context});

  @override
  final String messageId;
  @override
  final BuildContext context;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MessageDetailEvent.init(messageId: $messageId, context: $context)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MessageDetailEvent.init'))
      ..add(DiagnosticsProperty('messageId', messageId))
      ..add(DiagnosticsProperty('context', context));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InitMessageDetail &&
            (identical(other.messageId, messageId) ||
                other.messageId == messageId) &&
            (identical(other.context, context) || other.context == context));
  }

  @override
  int get hashCode => Object.hash(runtimeType, messageId, context);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitMessageDetailCopyWith<_$_InitMessageDetail> get copyWith =>
      __$$_InitMessageDetailCopyWithImpl<_$_InitMessageDetail>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String messageId, BuildContext context) init,
    required TResult Function(String elementId, String propertyName,
            dynamic value, bool ignoreLastChange)
        changeValue,
  }) {
    return init(messageId, context);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String messageId, BuildContext context)? init,
    TResult? Function(String elementId, String propertyName, dynamic value,
            bool ignoreLastChange)?
        changeValue,
  }) {
    return init?.call(messageId, context);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String messageId, BuildContext context)? init,
    TResult Function(String elementId, String propertyName, dynamic value,
            bool ignoreLastChange)?
        changeValue,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(messageId, context);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitMessageDetail value) init,
    required TResult Function(ChangeValue value) changeValue,
  }) {
    return init(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitMessageDetail value)? init,
    TResult? Function(ChangeValue value)? changeValue,
  }) {
    return init?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitMessageDetail value)? init,
    TResult Function(ChangeValue value)? changeValue,
    required TResult orElse(),
  }) {
    if (init != null) {
      return init(this);
    }
    return orElse();
  }
}

abstract class _InitMessageDetail implements MessageDetailEvent {
  const factory _InitMessageDetail(
      {required final String messageId,
      required final BuildContext context}) = _$_InitMessageDetail;

  String get messageId;
  BuildContext get context;
  @JsonKey(ignore: true)
  _$$_InitMessageDetailCopyWith<_$_InitMessageDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChangeValueCopyWith<$Res> {
  factory _$$ChangeValueCopyWith(
          _$ChangeValue value, $Res Function(_$ChangeValue) then) =
      __$$ChangeValueCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String elementId,
      String propertyName,
      dynamic value,
      bool ignoreLastChange});
}

/// @nodoc
class __$$ChangeValueCopyWithImpl<$Res>
    extends _$MessageDetailEventCopyWithImpl<$Res, _$ChangeValue>
    implements _$$ChangeValueCopyWith<$Res> {
  __$$ChangeValueCopyWithImpl(
      _$ChangeValue _value, $Res Function(_$ChangeValue) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? elementId = null,
    Object? propertyName = null,
    Object? value = freezed,
    Object? ignoreLastChange = null,
  }) {
    return _then(_$ChangeValue(
      elementId: null == elementId
          ? _value.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as String,
      propertyName: null == propertyName
          ? _value.propertyName
          : propertyName // ignore: cast_nullable_to_non_nullable
              as String,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as dynamic,
      ignoreLastChange: null == ignoreLastChange
          ? _value.ignoreLastChange
          : ignoreLastChange // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ChangeValue with DiagnosticableTreeMixin implements ChangeValue {
  const _$ChangeValue(
      {required this.elementId,
      required this.propertyName,
      required this.value,
      this.ignoreLastChange = false});

  @override
  final String elementId;
  @override
  final String propertyName;
  @override
  final dynamic value;
  @override
  @JsonKey()
  final bool ignoreLastChange;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MessageDetailEvent.changeValue(elementId: $elementId, propertyName: $propertyName, value: $value, ignoreLastChange: $ignoreLastChange)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MessageDetailEvent.changeValue'))
      ..add(DiagnosticsProperty('elementId', elementId))
      ..add(DiagnosticsProperty('propertyName', propertyName))
      ..add(DiagnosticsProperty('value', value))
      ..add(DiagnosticsProperty('ignoreLastChange', ignoreLastChange));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChangeValue &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.propertyName, propertyName) ||
                other.propertyName == propertyName) &&
            const DeepCollectionEquality().equals(other.value, value) &&
            (identical(other.ignoreLastChange, ignoreLastChange) ||
                other.ignoreLastChange == ignoreLastChange));
  }

  @override
  int get hashCode => Object.hash(runtimeType, elementId, propertyName,
      const DeepCollectionEquality().hash(value), ignoreLastChange);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChangeValueCopyWith<_$ChangeValue> get copyWith =>
      __$$ChangeValueCopyWithImpl<_$ChangeValue>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String messageId, BuildContext context) init,
    required TResult Function(String elementId, String propertyName,
            dynamic value, bool ignoreLastChange)
        changeValue,
  }) {
    return changeValue(elementId, propertyName, value, ignoreLastChange);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String messageId, BuildContext context)? init,
    TResult? Function(String elementId, String propertyName, dynamic value,
            bool ignoreLastChange)?
        changeValue,
  }) {
    return changeValue?.call(elementId, propertyName, value, ignoreLastChange);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String messageId, BuildContext context)? init,
    TResult Function(String elementId, String propertyName, dynamic value,
            bool ignoreLastChange)?
        changeValue,
    required TResult orElse(),
  }) {
    if (changeValue != null) {
      return changeValue(elementId, propertyName, value, ignoreLastChange);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitMessageDetail value) init,
    required TResult Function(ChangeValue value) changeValue,
  }) {
    return changeValue(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitMessageDetail value)? init,
    TResult? Function(ChangeValue value)? changeValue,
  }) {
    return changeValue?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitMessageDetail value)? init,
    TResult Function(ChangeValue value)? changeValue,
    required TResult orElse(),
  }) {
    if (changeValue != null) {
      return changeValue(this);
    }
    return orElse();
  }
}

abstract class ChangeValue implements MessageDetailEvent {
  const factory ChangeValue(
      {required final String elementId,
      required final String propertyName,
      required final dynamic value,
      final bool ignoreLastChange}) = _$ChangeValue;

  String get elementId;
  String get propertyName;
  dynamic get value;
  bool get ignoreLastChange;
  @JsonKey(ignore: true)
  _$$ChangeValueCopyWith<_$ChangeValue> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MessageDetailState {
  MessageDetailStatus get status => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MessageDetailStateCopyWith<MessageDetailState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageDetailStateCopyWith<$Res> {
  factory $MessageDetailStateCopyWith(
          MessageDetailState value, $Res Function(MessageDetailState) then) =
      _$MessageDetailStateCopyWithImpl<$Res, MessageDetailState>;
  @useResult
  $Res call({MessageDetailStatus status, bool isLoading});
}

/// @nodoc
class _$MessageDetailStateCopyWithImpl<$Res, $Val extends MessageDetailState>
    implements $MessageDetailStateCopyWith<$Res> {
  _$MessageDetailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? isLoading = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessageDetailStatus,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MessageDetailStateCopyWith<$Res>
    implements $MessageDetailStateCopyWith<$Res> {
  factory _$$_MessageDetailStateCopyWith(_$_MessageDetailState value,
          $Res Function(_$_MessageDetailState) then) =
      __$$_MessageDetailStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({MessageDetailStatus status, bool isLoading});
}

/// @nodoc
class __$$_MessageDetailStateCopyWithImpl<$Res>
    extends _$MessageDetailStateCopyWithImpl<$Res, _$_MessageDetailState>
    implements _$$_MessageDetailStateCopyWith<$Res> {
  __$$_MessageDetailStateCopyWithImpl(
      _$_MessageDetailState _value, $Res Function(_$_MessageDetailState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? isLoading = null,
  }) {
    return _then(_$_MessageDetailState(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as MessageDetailStatus,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_MessageDetailState
    with DiagnosticableTreeMixin
    implements _MessageDetailState {
  const _$_MessageDetailState({required this.status, required this.isLoading});

  @override
  final MessageDetailStatus status;
  @override
  final bool isLoading;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'MessageDetailState(status: $status, isLoading: $isLoading)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'MessageDetailState'))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('isLoading', isLoading));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MessageDetailState &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status, isLoading);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MessageDetailStateCopyWith<_$_MessageDetailState> get copyWith =>
      __$$_MessageDetailStateCopyWithImpl<_$_MessageDetailState>(
          this, _$identity);
}

abstract class _MessageDetailState implements MessageDetailState {
  const factory _MessageDetailState(
      {required final MessageDetailStatus status,
      required final bool isLoading}) = _$_MessageDetailState;

  @override
  MessageDetailStatus get status;
  @override
  bool get isLoading;
  @override
  @JsonKey(ignore: true)
  _$$_MessageDetailStateCopyWith<_$_MessageDetailState> get copyWith =>
      throw _privateConstructorUsedError;
}
