import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../capp_home_core.dart';

part 'message_detail_bloc.freezed.dart';
part 'message_detail_event.dart';
part 'message_detail_state.dart';

class MessageDetailBloc extends Bloc<MessageDetailEvent, MessageDetailState> {
  final IInboxRepository repository;
  final MessageDetailFormManager formManager;

  MessageDetailBloc({
    required this.formManager,
    required this.repository,
  }) : super(const MessageDetailState(isLoading: true, status: MessageDetailStatus.idle)) {
    on<_InitMessageDetail>(_init);
    on<_$ChangeValue>(_changeValue);
  }
  Future<void> _init(_InitMessageDetail event, Emitter<MessageDetailState> emit) async {
    emit(
      state.copyWith(
        isLoading: true,
        status: MessageDetailStatus.loadDetail,
      ),
    );
    final content = await repository.getInboxMessage(event.messageId);
    emit(
      content.fold(
        (l) {
          return l.maybeMap(
            unexpected: (_) => state.copyWith(isLoading: false, status: MessageDetailStatus.error),
            orElse: () => state.copyWith(isLoading: false, status: MessageDetailStatus.idle),
          );
        },
        (r) {
          try {
            formManager.initWithContent(r);
            return state.copyWith(isLoading: false, status: MessageDetailStatus.idle);
          } on Exception {
            return state.copyWith(isLoading: false, status: MessageDetailStatus.error);
          }
        },
      ),
    );
  }

  Future<void> _changeValue(ChangeValue e, Emitter<MessageDetailState> emit) async {
    formManager.changeValue<dynamic>(
      elementId: e.elementId,
      propertyName: e.propertyName,
      value: e.value,
      ignoreLastChange: e.ignoreLastChange,
    );
  }
}
