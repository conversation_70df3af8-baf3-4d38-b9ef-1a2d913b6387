part of 'inbox_bloc.dart';

@freezed
class InboxEvent with _$InboxEvent {
  const factory InboxEvent.init() = _Init;
  const factory InboxEvent.loadCategories() = _LoadCategories;
  const factory InboxEvent.loadUnreadCount() = _LoadUnreadCount;
  const factory InboxEvent.refresh() = _Refresh;
  const factory InboxEvent.loadNext() = _LoadNext;
  const factory InboxEvent.persistMessages(Either<InboxFailure, InboxMessageList> failureOrItems) = _PersistMessages;
  const factory InboxEvent.loaded() = _Loaded;
  const factory InboxEvent.switchFilterChange({required EnumerationItem? category, bool? selected}) =
      _SwitchFilterChange;
  const factory InboxEvent.markArchived({required String messageId}) = _MarkArchived;
  const factory InboxEvent.markUnarchived({required String messageId}) = _MarkUnarchived;
  const factory InboxEvent.markRead({required String messageId}) = _MarkRead;
  const factory InboxEvent.markUnread({required String messageId}) = _MarkUnread;
  const factory InboxEvent.markAllRead() = _MarkAllRead;
  const factory InboxEvent.changeUnreadCount({required String categoryId, required int count}) = _ChangeUnreadCount;
}
