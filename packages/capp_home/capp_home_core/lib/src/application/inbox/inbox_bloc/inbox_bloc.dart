import 'dart:async';

import 'package:async/async.dart';
import 'package:collection/collection.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_home_core.dart';

part 'inbox_bloc.freezed.dart';
part 'inbox_event.dart';
part 'inbox_state.dart';

class InboxBloc extends Bloc<InboxEvent, InboxState> {
  final IInboxRepository inboxRepository;
  final ILocalizationRepository localizationRepository;
  final IEnumerationsRepository enumerationsRepository;

  InboxBloc({
    required this.inboxRepository,
    required this.localizationRepository,
    required this.enumerationsRepository,
  }) : super(InboxState.initial()) {
    on<_Init>(_init);
    on<_LoadCategories>(_loadCategories);
    on<_LoadUnreadCount>(_loadUnreadCount);
    on<_Refresh>(_refresh);
    on<_LoadNext>(_loadNext);
    on<_PersistMessages>(_persistMessages);
    on<_Loaded>(_loaded);
    on<_SwitchFilterChange>(_switchFilterChange);
    on<_MarkArchived>(_markArchived);
    on<_MarkUnarchived>(_markUnarchived);
    on<_MarkRead>(_markRead);
    on<_MarkAllRead>(_markAllRead);
    on<_MarkUnread>(_markUnread);
    on<_ChangeUnreadCount>(_changeUnreadCount);
  }

  Future<void> _init(_Init e, Emitter<InboxState> emit) async {
    inboxRepository.clearInboxMessagesLocal();
    final selectedLocale = await localizationRepository.loadSelectedLocale();
    final languageCode = selectedLocale?.languageCode ?? 'en';
    emit(
      state.copyWith(
        locale: languageCode,
        isInitialized: true,
        isInitialLoading: true,
        category: _allCategory,
      ),
    );
    add(const InboxEvent.loadCategories());
  }

  Future<void> _loadCategories(
    _LoadCategories e,
    Emitter<InboxState> emit,
  ) async {
    final response = await enumerationsRepository.getInboxMessageCategories();
    emit(
      response.fold(
        (_) => state.copyWith(isError: true),
        (response) => state.copyWith(
          isError: false,
          categories: response.items != null ? [_allCategory, ...response.items!] : [_allCategory],
        ),
      ),
    );
    add(const InboxEvent.refresh());
  }

  Future<void> _loadUnreadCount(
    _LoadUnreadCount e,
    Emitter<InboxState> emit,
  ) async {
    if (state.categories != null && state.categories != null && state.categories!.isNotEmpty) {
      final unreadCount = <EnumerationItem, int>{};
      final ids = <String>[];
      state.categories?.forEach((category) {
        // API will not accept allCategoryId in list categories params
        if (category.id != allCategoryId) {
          ids.add(category.id);
        }
      });

      await inboxRepository.getUnreadCount(categories: ids).then((responses) {
        responses.fold((l) {
          emit(state.copyWith(isError: true));
        }, (r) {
          for (final c in r) {
            final category = c.categoryId == null
                ? state.categories!.firstWhere((item) => item.id == allCategoryId)
                : state.categories!.firstWhere((item) => item.id == c.categoryId);
            unreadCount.putIfAbsent(category, () => c.count);
          }
        });
        emit(state.copyWith(isInitialized: true, unreadCount: unreadCount));
        add(const InboxEvent.loaded());
      });
    } else {
      emit(state.copyWith(isError: true));
    }
  }

  Future<void> _refresh(_Refresh e, Emitter<InboxState> emit) async {
    emit(state.copyWith(currentPage: 0, messages: [], isInitialLoading: true));
    add(const InboxEvent.loadNext());
  }

  Future<void> _loadNext(_LoadNext e, Emitter<InboxState> emit) async {
    if (state.currentPage > 0 && state.hasReachedEnd) return;
    emit(state.copyWith(isLoading: true));
    _loadMessagesSubscription = CancelableOperation.fromFuture(
      inboxRepository.getInboxList(
        page: state.currentPage + 1,
        pageSize: state.pageSize,
        categoryId: state.category!.id == allCategoryId ? null : state.category!.id,
      ),
    ).then((value) => add(InboxEvent.persistMessages(value)));
  }

  Future<void> _persistMessages(
    _PersistMessages e,
    Emitter<InboxState> emit,
  ) async {
    emit(
      e.failureOrItems.fold(
        (l) => state.copyWith(
          isError: true,
          isLoading: false,
          isInitialLoading: false,
        ),
        (r) {
          final list = <InboxMessage>[...state.copyWith().messages];
          final existingId = <String>{for (final item in list) item.id};

          final previewItems = r.items
              .where((preview) => !existingId.contains(preview.id))
              .map(
                (msg) => msg.copyWith(
                  textPreview: (msg.text ?? '').removeMarkdown(),
                ),
              )
              .toList();
          list
            ..addAll(previewItems)
            ..sort((a, b) => b.dataReceived.compareTo(a.dataReceived));
          return state.copyWith(
            isError: false,
            messages: list,
            currentPage: r.page,
            hasReachedEnd: list.length >= r.totalCount,
          );
        },
      ),
    );
    add(const InboxEvent.loadUnreadCount());
  }

  Future<void> _loaded(_Loaded e, Emitter<InboxState> emit) async {
    emit(
      state.copyWith(
        isLoading: false,
        isError: false,
        isInitialLoading: false,
      ),
    );
  }

  Future<void> _switchFilterChange(
    _SwitchFilterChange e,
    Emitter<InboxState> emit,
  ) async {
    await _loadMessagesSubscription?.cancel();
    final inboxMessageCached = inboxRepository.getInboxMessagesLocal();
    final filterMessages = (e.category?.id == allCategoryId)
        ? inboxMessageCached
        : inboxMessageCached.where((m) => m.categoryId == e.category?.id).toList()
      ..sort((a, b) => b.dataReceived.compareTo(a.dataReceived));

    emit(
      state.copyWith(
        category: e.category,
        isInitialLoading: false,
        messages: filterMessages,
        isLoading: false,
        currentPage: 0,
      ),
    );
    // If the number of unread count is greater than current message,
    // app will automatically load more without display loading view
    final unreadCount = state.unreadCount?[e.category] ?? 0;
    final currentUnreadMessages = state.messages.where((m) => m.dateRead == null).toList().length;

    if (unreadCount > currentUnreadMessages) {
      // if current message is empty then load new, otherwise load more
      if (currentUnreadMessages == 0) {
        add(const InboxEvent.refresh());
      } else {
        _loadMessagesSubscription = CancelableOperation.fromFuture(
          inboxRepository.getInboxList(
            page: state.currentPage + 1,
            pageSize: state.pageSize,
            categoryId: state.category!.id == allCategoryId ? null : state.category!.id,
          ),
        ).then((value) => add(InboxEvent.persistMessages(value)));
      }
    }
  }

  Future<void> _markArchived(_MarkArchived e, Emitter<InboxState> emit) async {
    final result = await inboxRepository.markAsArchived(e.messageId);

    emit(
      result.fold(
        (l) => state,
        (r) {
          final list = <InboxMessage>[...state.copyWith().messages.toList()];
          final archivedMessage = list.firstWhereOrNull((msg) => msg.id == e.messageId);
          if (archivedMessage == null) {
            return state;
          }
          final archivedMessageIndex = list.indexOf(archivedMessage);
          list.removeWhere((msg) => msg.id == e.messageId);

          if (archivedMessage.dateRead == null) {
            add(
              InboxEvent.changeUnreadCount(
                count: -1,
                categoryId: archivedMessage.categoryId,
              ),
            );
          }

          return state.copyWith(
            messages: list,
            recentlyArchivedMessage: archivedMessage,
            recentlyArchivedMessageIndex: archivedMessageIndex,
          );
        },
      ),
    );
  }

  Future<void> _markUnarchived(
    _MarkUnarchived e,
    Emitter<InboxState> emit,
  ) async {
    final result = await inboxRepository.markAsUnarchived(e.messageId);

    emit(
      result.fold(
        // @TODO: handle error
        (l) => state,
        (r) {
          final list = List<InboxMessage>.from(state.messages);
          if (state.recentlyArchivedMessage != null) {
            list.insert(
              state.recentlyArchivedMessageIndex,
              state.recentlyArchivedMessage!,
            );
          }

          final archivedMessage = list.firstWhere((msg) => msg.id == e.messageId);
          if (archivedMessage.dateRead == null) {
            add(
              InboxEvent.changeUnreadCount(
                count: 1,
                categoryId: archivedMessage.categoryId,
              ),
            );
          }

          return state.copyWith(messages: list, recentlyArchivedMessage: null);
        },
      ),
    );
  }

  Future<void> _markRead(_MarkRead e, Emitter<InboxState> emit) async {
    final result = await inboxRepository.markAsRead(e.messageId);

    emit(
      result.fold(
        // @TODO: handle error
        (l) => state,
        (r) {
          add(const InboxEvent.loadUnreadCount());
          final msgIndex = state.messages.indexWhere((msg) => msg.id == e.messageId);
          if (msgIndex >= 0) {
            final list = state.copyWith().messages.toList();
            final msg = list[msgIndex];
            list[msgIndex] = msg.copyWith(dateRead: DateTime.now());

            if (msg.dateRead == null) {
              add(
                InboxEvent.changeUnreadCount(
                  count: -1,
                  categoryId: msg.categoryId,
                ),
              );
            }

            return state.copyWith(
              messages: list,
              version: DateTime.now().millisecondsSinceEpoch,
            );
          }
          return state;
        },
      ),
    );
  }

  Future<void> _markUnread(_MarkUnread e, Emitter<InboxState> emit) async {
    final result = await inboxRepository.markAsUnread(e.messageId);

    emit(
      result.fold(
        // @TODO: handle error
        (l) => state,
        (r) {
          add(const InboxEvent.loadUnreadCount());
          final msgIndex = state.messages.indexWhere((msg) => msg.id == e.messageId);
          if (msgIndex >= 0) {
            final list = state.copyWith().messages.toList();
            final msg = list[msgIndex];
            list[msgIndex] = msg.copyWith(dateRead: null);

            if (msg.dateRead != null) {
              add(
                InboxEvent.changeUnreadCount(
                  count: 1,
                  categoryId: msg.categoryId,
                ),
              );
            }

            return state.copyWith(
              messages: list,
              version: DateTime.now().millisecondsSinceEpoch,
            );
          }
          return state;
        },
      ),
    );
  }

  Future<void> _markAllRead(_MarkAllRead e, Emitter<InboxState> emit) async {
    final result = await inboxRepository.markAllRead();

    result.fold(
      (_) {},
      (r) {
        emit(
          state.copyWith(
            markAllReadCount: r,
          ),
        );
        add(const InboxEvent.refresh());
      },
    );
  }

  Future<void> _changeUnreadCount(
    _ChangeUnreadCount e,
    Emitter<InboxState> emit,
  ) async {
    final unreadCount = <EnumerationItem, int>{}
      ..addAll(
        state.unreadCount ?? {for (final category in state.categories!) category: 0},
      )
      ..update(
        _allCategory,
        (value) => value + e.count,
        ifAbsent: () => 0,
      );

    if (e.categoryId == allCategoryId) {
      emit(state.copyWith(unreadCount: unreadCount));
    } else {
      final category = state.categories!.firstWhereOrNull((c) => c.id == e.categoryId);
      if (category != null && unreadCount.containsKey(category)) {
        unreadCount.update(category, (value) => value + e.count);
      }
      emit(state.copyWith(unreadCount: unreadCount));
    }
  }

  CancelableOperation<void>? _loadMessagesSubscription;
}

const allCategoryId = 'allCategoryId';

EnumerationItem get _allCategory => EnumerationItem(
      id: allCategoryId,
      enumerationType: TypeEnumeration.inboxMessageCategories,
      title: '',
      key: 'all',
    );
