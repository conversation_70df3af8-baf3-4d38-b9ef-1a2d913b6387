// ignore_for_file: prefer_const_constructors

part of 'inbox_bloc.dart';

enum InboxStatus { openDetail, getMessages, idle }

@freezed
class InboxState with _$InboxState {
  const factory InboxState({
    @Default(InboxStatus.idle) InboxStatus status,
    @Default(false) bool isInitialized,
    @Default(false) bool isLoading,
    @Default(false) bool isError,
    @Default(false) bool isSearching,
    String? searchText,
    EnumerationItem? category,
    @Default(false) bool hasReachedEnd,
    @Default(0) int currentPage,
    @Default(10) int pageSize,
    int? version,
    @Default(<InboxMessage>[]) List<InboxMessage> messages,
    @Default(false) bool isInitialLoading,
    InboxMessage? recentlyArchivedMessage,
    @Default(0) int recentlyArchivedMessageIndex,
    @Default('') String locale,
    Map<EnumerationItem, int>? unreadCount,
    List<EnumerationItem>? categories,
    int? markAllReadCount,
  }) = _InboxState;

  factory InboxState.initial() => InboxState(version: 0, messages: []);
}
