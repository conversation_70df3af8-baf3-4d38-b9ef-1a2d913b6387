part of 'unread_bloc.dart';

@freezed
class UnreadState with _$UnreadState {
  const factory UnreadState({
    required int unreadCount,
    required bool hasUnread,
    required LoadingState loading,
    required bool isTest,
  }) = _UnreadState;

  factory UnreadState.init() => const UnreadState(
        unreadCount: 0,
        loading: LoadingState.isInitial,
        hasUnread: false,
        isTest: false,
      );
}
