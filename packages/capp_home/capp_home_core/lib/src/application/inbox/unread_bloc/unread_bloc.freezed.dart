// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unread_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$UnreadEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() reload,
    required TResult Function() hasUnreadMessages,
    required TResult Function(bool? isTest) setTest,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? reload,
    TResult? Function()? hasUnreadMessages,
    TResult? Function(bool? isTest)? setTest,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? reload,
    TResult Function()? hasUnreadMessages,
    TResult Function(bool? isTest)? setTest,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Reload value) reload,
    required TResult Function(_HasUnreadMesseges value) hasUnreadMessages,
    required TResult Function(_SetTest value) setTest,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Reload value)? reload,
    TResult? Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult? Function(_SetTest value)? setTest,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Reload value)? reload,
    TResult Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult Function(_SetTest value)? setTest,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnreadEventCopyWith<$Res> {
  factory $UnreadEventCopyWith(
          UnreadEvent value, $Res Function(UnreadEvent) then) =
      _$UnreadEventCopyWithImpl<$Res, UnreadEvent>;
}

/// @nodoc
class _$UnreadEventCopyWithImpl<$Res, $Val extends UnreadEvent>
    implements $UnreadEventCopyWith<$Res> {
  _$UnreadEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_ReloadCopyWith<$Res> {
  factory _$$_ReloadCopyWith(_$_Reload value, $Res Function(_$_Reload) then) =
      __$$_ReloadCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ReloadCopyWithImpl<$Res>
    extends _$UnreadEventCopyWithImpl<$Res, _$_Reload>
    implements _$$_ReloadCopyWith<$Res> {
  __$$_ReloadCopyWithImpl(_$_Reload _value, $Res Function(_$_Reload) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Reload implements _Reload {
  const _$_Reload();

  @override
  String toString() {
    return 'UnreadEvent.reload()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Reload);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() reload,
    required TResult Function() hasUnreadMessages,
    required TResult Function(bool? isTest) setTest,
  }) {
    return reload();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? reload,
    TResult? Function()? hasUnreadMessages,
    TResult? Function(bool? isTest)? setTest,
  }) {
    return reload?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? reload,
    TResult Function()? hasUnreadMessages,
    TResult Function(bool? isTest)? setTest,
    required TResult orElse(),
  }) {
    if (reload != null) {
      return reload();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Reload value) reload,
    required TResult Function(_HasUnreadMesseges value) hasUnreadMessages,
    required TResult Function(_SetTest value) setTest,
  }) {
    return reload(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Reload value)? reload,
    TResult? Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult? Function(_SetTest value)? setTest,
  }) {
    return reload?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Reload value)? reload,
    TResult Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult Function(_SetTest value)? setTest,
    required TResult orElse(),
  }) {
    if (reload != null) {
      return reload(this);
    }
    return orElse();
  }
}

abstract class _Reload implements UnreadEvent {
  const factory _Reload() = _$_Reload;
}

/// @nodoc
abstract class _$$_HasUnreadMessegesCopyWith<$Res> {
  factory _$$_HasUnreadMessegesCopyWith(_$_HasUnreadMesseges value,
          $Res Function(_$_HasUnreadMesseges) then) =
      __$$_HasUnreadMessegesCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_HasUnreadMessegesCopyWithImpl<$Res>
    extends _$UnreadEventCopyWithImpl<$Res, _$_HasUnreadMesseges>
    implements _$$_HasUnreadMessegesCopyWith<$Res> {
  __$$_HasUnreadMessegesCopyWithImpl(
      _$_HasUnreadMesseges _value, $Res Function(_$_HasUnreadMesseges) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_HasUnreadMesseges implements _HasUnreadMesseges {
  const _$_HasUnreadMesseges();

  @override
  String toString() {
    return 'UnreadEvent.hasUnreadMessages()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_HasUnreadMesseges);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() reload,
    required TResult Function() hasUnreadMessages,
    required TResult Function(bool? isTest) setTest,
  }) {
    return hasUnreadMessages();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? reload,
    TResult? Function()? hasUnreadMessages,
    TResult? Function(bool? isTest)? setTest,
  }) {
    return hasUnreadMessages?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? reload,
    TResult Function()? hasUnreadMessages,
    TResult Function(bool? isTest)? setTest,
    required TResult orElse(),
  }) {
    if (hasUnreadMessages != null) {
      return hasUnreadMessages();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Reload value) reload,
    required TResult Function(_HasUnreadMesseges value) hasUnreadMessages,
    required TResult Function(_SetTest value) setTest,
  }) {
    return hasUnreadMessages(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Reload value)? reload,
    TResult? Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult? Function(_SetTest value)? setTest,
  }) {
    return hasUnreadMessages?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Reload value)? reload,
    TResult Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult Function(_SetTest value)? setTest,
    required TResult orElse(),
  }) {
    if (hasUnreadMessages != null) {
      return hasUnreadMessages(this);
    }
    return orElse();
  }
}

abstract class _HasUnreadMesseges implements UnreadEvent {
  const factory _HasUnreadMesseges() = _$_HasUnreadMesseges;
}

/// @nodoc
abstract class _$$_SetTestCopyWith<$Res> {
  factory _$$_SetTestCopyWith(
          _$_SetTest value, $Res Function(_$_SetTest) then) =
      __$$_SetTestCopyWithImpl<$Res>;
  @useResult
  $Res call({bool? isTest});
}

/// @nodoc
class __$$_SetTestCopyWithImpl<$Res>
    extends _$UnreadEventCopyWithImpl<$Res, _$_SetTest>
    implements _$$_SetTestCopyWith<$Res> {
  __$$_SetTestCopyWithImpl(_$_SetTest _value, $Res Function(_$_SetTest) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isTest = freezed,
  }) {
    return _then(_$_SetTest(
      isTest: freezed == isTest
          ? _value.isTest
          : isTest // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_SetTest implements _SetTest {
  const _$_SetTest({this.isTest});

  @override
  final bool? isTest;

  @override
  String toString() {
    return 'UnreadEvent.setTest(isTest: $isTest)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetTest &&
            (identical(other.isTest, isTest) || other.isTest == isTest));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isTest);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetTestCopyWith<_$_SetTest> get copyWith =>
      __$$_SetTestCopyWithImpl<_$_SetTest>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() reload,
    required TResult Function() hasUnreadMessages,
    required TResult Function(bool? isTest) setTest,
  }) {
    return setTest(isTest);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? reload,
    TResult? Function()? hasUnreadMessages,
    TResult? Function(bool? isTest)? setTest,
  }) {
    return setTest?.call(isTest);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? reload,
    TResult Function()? hasUnreadMessages,
    TResult Function(bool? isTest)? setTest,
    required TResult orElse(),
  }) {
    if (setTest != null) {
      return setTest(isTest);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Reload value) reload,
    required TResult Function(_HasUnreadMesseges value) hasUnreadMessages,
    required TResult Function(_SetTest value) setTest,
  }) {
    return setTest(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Reload value)? reload,
    TResult? Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult? Function(_SetTest value)? setTest,
  }) {
    return setTest?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Reload value)? reload,
    TResult Function(_HasUnreadMesseges value)? hasUnreadMessages,
    TResult Function(_SetTest value)? setTest,
    required TResult orElse(),
  }) {
    if (setTest != null) {
      return setTest(this);
    }
    return orElse();
  }
}

abstract class _SetTest implements UnreadEvent {
  const factory _SetTest({final bool? isTest}) = _$_SetTest;

  bool? get isTest;
  @JsonKey(ignore: true)
  _$$_SetTestCopyWith<_$_SetTest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$UnreadState {
  int get unreadCount => throw _privateConstructorUsedError;
  bool get hasUnread => throw _privateConstructorUsedError;
  LoadingState get loading => throw _privateConstructorUsedError;
  bool get isTest => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $UnreadStateCopyWith<UnreadState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnreadStateCopyWith<$Res> {
  factory $UnreadStateCopyWith(
          UnreadState value, $Res Function(UnreadState) then) =
      _$UnreadStateCopyWithImpl<$Res, UnreadState>;
  @useResult
  $Res call(
      {int unreadCount, bool hasUnread, LoadingState loading, bool isTest});
}

/// @nodoc
class _$UnreadStateCopyWithImpl<$Res, $Val extends UnreadState>
    implements $UnreadStateCopyWith<$Res> {
  _$UnreadStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unreadCount = null,
    Object? hasUnread = null,
    Object? loading = null,
    Object? isTest = null,
  }) {
    return _then(_value.copyWith(
      unreadCount: null == unreadCount
          ? _value.unreadCount
          : unreadCount // ignore: cast_nullable_to_non_nullable
              as int,
      hasUnread: null == hasUnread
          ? _value.hasUnread
          : hasUnread // ignore: cast_nullable_to_non_nullable
              as bool,
      loading: null == loading
          ? _value.loading
          : loading // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isTest: null == isTest
          ? _value.isTest
          : isTest // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UnreadStateCopyWith<$Res>
    implements $UnreadStateCopyWith<$Res> {
  factory _$$_UnreadStateCopyWith(
          _$_UnreadState value, $Res Function(_$_UnreadState) then) =
      __$$_UnreadStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int unreadCount, bool hasUnread, LoadingState loading, bool isTest});
}

/// @nodoc
class __$$_UnreadStateCopyWithImpl<$Res>
    extends _$UnreadStateCopyWithImpl<$Res, _$_UnreadState>
    implements _$$_UnreadStateCopyWith<$Res> {
  __$$_UnreadStateCopyWithImpl(
      _$_UnreadState _value, $Res Function(_$_UnreadState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unreadCount = null,
    Object? hasUnread = null,
    Object? loading = null,
    Object? isTest = null,
  }) {
    return _then(_$_UnreadState(
      unreadCount: null == unreadCount
          ? _value.unreadCount
          : unreadCount // ignore: cast_nullable_to_non_nullable
              as int,
      hasUnread: null == hasUnread
          ? _value.hasUnread
          : hasUnread // ignore: cast_nullable_to_non_nullable
              as bool,
      loading: null == loading
          ? _value.loading
          : loading // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isTest: null == isTest
          ? _value.isTest
          : isTest // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_UnreadState implements _UnreadState {
  const _$_UnreadState(
      {required this.unreadCount,
      required this.hasUnread,
      required this.loading,
      required this.isTest});

  @override
  final int unreadCount;
  @override
  final bool hasUnread;
  @override
  final LoadingState loading;
  @override
  final bool isTest;

  @override
  String toString() {
    return 'UnreadState(unreadCount: $unreadCount, hasUnread: $hasUnread, loading: $loading, isTest: $isTest)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UnreadState &&
            (identical(other.unreadCount, unreadCount) ||
                other.unreadCount == unreadCount) &&
            (identical(other.hasUnread, hasUnread) ||
                other.hasUnread == hasUnread) &&
            (identical(other.loading, loading) || other.loading == loading) &&
            (identical(other.isTest, isTest) || other.isTest == isTest));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, unreadCount, hasUnread, loading, isTest);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UnreadStateCopyWith<_$_UnreadState> get copyWith =>
      __$$_UnreadStateCopyWithImpl<_$_UnreadState>(this, _$identity);
}

abstract class _UnreadState implements UnreadState {
  const factory _UnreadState(
      {required final int unreadCount,
      required final bool hasUnread,
      required final LoadingState loading,
      required final bool isTest}) = _$_UnreadState;

  @override
  int get unreadCount;
  @override
  bool get hasUnread;
  @override
  LoadingState get loading;
  @override
  bool get isTest;
  @override
  @JsonKey(ignore: true)
  _$$_UnreadStateCopyWith<_$_UnreadState> get copyWith =>
      throw _privateConstructorUsedError;
}
