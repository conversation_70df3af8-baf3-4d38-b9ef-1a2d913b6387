import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_home_core.dart';
import '../../../domain/constants.dart';

part 'unread_bloc.freezed.dart';
part 'unread_event.dart';
part 'unread_state.dart';

class UnreadBloc extends Bloc<UnreadEvent, UnreadState> {
  final IInboxRepository repository;
  UnreadBloc({required this.repository}) : super(UnreadState.init()) {
    on<_Reload>(_onReload, transformer: debounceSequential(const Duration(milliseconds: 500)));
    on<_HasUnreadMesseges>(_onHasUnreadMessages, transformer: debounceSequential(const Duration(milliseconds: 500)));
    on<_SetTest>(_onSetTests);
  }

  Future<void> _onReload(_Reload event, Emitter<UnreadState> emit) async {
    if (state.loading != LoadingState.isLoading) {
      emit(
        state.copyWith(loading: LoadingState.isLoading),
      );
      final result = await repository.getUnreadCount();
      if (result.isRight()) {
        final unreadCount = result.fold((l) => 0, (r) {
          if (r.isNotEmpty) {
            return r[0].count;
          }
          return 0;
        });
        if (!state.isTest) {
          if (unreadCount > 0) {
            await FlutterAppBadger.updateBadgeCount(
              unreadCount < NotificationConst.unreadLimitCount ? unreadCount : NotificationConst.unreadLimitCount,
            );
          } else {
            await FlutterAppBadger.removeBadge();
          }
        }
        emit(
          state.copyWith(unreadCount: unreadCount, hasUnread: unreadCount > 0),
        );
      }
      emit(
        state.copyWith(loading: LoadingState.isCompleted),
      );
    }
  }

  Future<void> _onHasUnreadMessages(_HasUnreadMesseges event, Emitter<UnreadState> emit) async {
    if (state.loading != LoadingState.isLoading) {
      emit(
        state.copyWith(loading: LoadingState.isLoading),
      );
      final result = await repository.hasUnreadMessages();

      if (result.isRight()) {
        final hasUnread = result.fold((l) => false, (r) => r);
        emit(
          state.copyWith(hasUnread: hasUnread),
        );
      }
      emit(
        state.copyWith(loading: LoadingState.isCompleted),
      );
    }
  }

  void _onSetTests(_SetTest event, Emitter<UnreadState> emit) {
    final isTest = event.isTest ?? state.isTest;
    emit(
      state.copyWith(isTest: isTest),
    );
  }
}
