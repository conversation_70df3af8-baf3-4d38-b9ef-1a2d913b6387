import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../domain/games_banner/i_games_banner_repository.dart';

part 'posloan_banner_bloc.freezed.dart';
part 'posloan_banner_event.dart';
part 'posloan_banner_state.dart';

class PosLoanBannerBloc extends Bloc<PosLoanBannerEvent, PosLoanBannerState> {
  final IGamesBannerRepository gamesBannerRepository;
  final ImageServiceBase imageService;

  StreamSubscription<void>? _refreshStreamSubscription;

  PosLoanBannerBloc({
    required this.gamesBannerRepository,
    required this.imageService,
  }) : super(const PosLoanBannerState()) {
    on<_InitLoading>(_onInitLoading);
    on<_SubscribeToRefresh>((event, _) => _onSubscribeToRefresh(event));
  }

  Future<void> _onInitLoading(_InitLoading event, Emitter<PosLoanBannerState> emit) async {
    emit(
      state.copyWith(isLoading: true),
    );

    final response = await gamesBannerRepository.getPosLoanBanner();
    emit(
      response.fold(
        (_) => state.copyWith(isError: true, isLoading: false),
        (r) => state.copyWith(
          isError: false,
          isLoading: false,
          bannerLink: r.link,
          bannerImageUrl: imageService.getUrlFromId(r.image?.id ?? ''),
        ),
      ),
    );
  }

  void _onSubscribeToRefresh(_SubscribeToRefresh event) {
    _refreshStreamSubscription = event.refreshStream.listen((event) {
      add(const PosLoanBannerEvent.initLoading());
    });
  }

  @override
  Future<void> close() {
    _refreshStreamSubscription?.cancel();
    return super.close();
  }
}
