// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'posloan_banner_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PosLoanBannerEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLoading,
    required TResult Function(Stream<void> refreshStream) subscribeToRefresh,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLoading,
    TResult? Function(Stream<void> refreshStream)? subscribeToRefresh,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLoading,
    TResult Function(Stream<void> refreshStream)? subscribeToRefresh,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLoading value) initLoading,
    required TResult Function(_SubscribeToRefresh value) subscribeToRefresh,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLoading value)? initLoading,
    TResult? Function(_SubscribeToRefresh value)? subscribeToRefresh,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLoading value)? initLoading,
    TResult Function(_SubscribeToRefresh value)? subscribeToRefresh,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PosLoanBannerEventCopyWith<$Res> {
  factory $PosLoanBannerEventCopyWith(
          PosLoanBannerEvent value, $Res Function(PosLoanBannerEvent) then) =
      _$PosLoanBannerEventCopyWithImpl<$Res, PosLoanBannerEvent>;
}

/// @nodoc
class _$PosLoanBannerEventCopyWithImpl<$Res, $Val extends PosLoanBannerEvent>
    implements $PosLoanBannerEventCopyWith<$Res> {
  _$PosLoanBannerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitLoadingCopyWith<$Res> {
  factory _$$_InitLoadingCopyWith(
          _$_InitLoading value, $Res Function(_$_InitLoading) then) =
      __$$_InitLoadingCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitLoadingCopyWithImpl<$Res>
    extends _$PosLoanBannerEventCopyWithImpl<$Res, _$_InitLoading>
    implements _$$_InitLoadingCopyWith<$Res> {
  __$$_InitLoadingCopyWithImpl(
      _$_InitLoading _value, $Res Function(_$_InitLoading) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_InitLoading implements _InitLoading {
  const _$_InitLoading();

  @override
  String toString() {
    return 'PosLoanBannerEvent.initLoading()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_InitLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLoading,
    required TResult Function(Stream<void> refreshStream) subscribeToRefresh,
  }) {
    return initLoading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLoading,
    TResult? Function(Stream<void> refreshStream)? subscribeToRefresh,
  }) {
    return initLoading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLoading,
    TResult Function(Stream<void> refreshStream)? subscribeToRefresh,
    required TResult orElse(),
  }) {
    if (initLoading != null) {
      return initLoading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLoading value) initLoading,
    required TResult Function(_SubscribeToRefresh value) subscribeToRefresh,
  }) {
    return initLoading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLoading value)? initLoading,
    TResult? Function(_SubscribeToRefresh value)? subscribeToRefresh,
  }) {
    return initLoading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLoading value)? initLoading,
    TResult Function(_SubscribeToRefresh value)? subscribeToRefresh,
    required TResult orElse(),
  }) {
    if (initLoading != null) {
      return initLoading(this);
    }
    return orElse();
  }
}

abstract class _InitLoading implements PosLoanBannerEvent {
  const factory _InitLoading() = _$_InitLoading;
}

/// @nodoc
abstract class _$$_SubscribeToRefreshCopyWith<$Res> {
  factory _$$_SubscribeToRefreshCopyWith(_$_SubscribeToRefresh value,
          $Res Function(_$_SubscribeToRefresh) then) =
      __$$_SubscribeToRefreshCopyWithImpl<$Res>;
  @useResult
  $Res call({Stream<void> refreshStream});
}

/// @nodoc
class __$$_SubscribeToRefreshCopyWithImpl<$Res>
    extends _$PosLoanBannerEventCopyWithImpl<$Res, _$_SubscribeToRefresh>
    implements _$$_SubscribeToRefreshCopyWith<$Res> {
  __$$_SubscribeToRefreshCopyWithImpl(
      _$_SubscribeToRefresh _value, $Res Function(_$_SubscribeToRefresh) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refreshStream = null,
  }) {
    return _then(_$_SubscribeToRefresh(
      refreshStream: null == refreshStream
          ? _value.refreshStream
          : refreshStream // ignore: cast_nullable_to_non_nullable
              as Stream<void>,
    ));
  }
}

/// @nodoc

class _$_SubscribeToRefresh implements _SubscribeToRefresh {
  const _$_SubscribeToRefresh({required this.refreshStream});

  @override
  final Stream<void> refreshStream;

  @override
  String toString() {
    return 'PosLoanBannerEvent.subscribeToRefresh(refreshStream: $refreshStream)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubscribeToRefresh &&
            (identical(other.refreshStream, refreshStream) ||
                other.refreshStream == refreshStream));
  }

  @override
  int get hashCode => Object.hash(runtimeType, refreshStream);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubscribeToRefreshCopyWith<_$_SubscribeToRefresh> get copyWith =>
      __$$_SubscribeToRefreshCopyWithImpl<_$_SubscribeToRefresh>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initLoading,
    required TResult Function(Stream<void> refreshStream) subscribeToRefresh,
  }) {
    return subscribeToRefresh(refreshStream);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initLoading,
    TResult? Function(Stream<void> refreshStream)? subscribeToRefresh,
  }) {
    return subscribeToRefresh?.call(refreshStream);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initLoading,
    TResult Function(Stream<void> refreshStream)? subscribeToRefresh,
    required TResult orElse(),
  }) {
    if (subscribeToRefresh != null) {
      return subscribeToRefresh(refreshStream);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitLoading value) initLoading,
    required TResult Function(_SubscribeToRefresh value) subscribeToRefresh,
  }) {
    return subscribeToRefresh(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitLoading value)? initLoading,
    TResult? Function(_SubscribeToRefresh value)? subscribeToRefresh,
  }) {
    return subscribeToRefresh?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitLoading value)? initLoading,
    TResult Function(_SubscribeToRefresh value)? subscribeToRefresh,
    required TResult orElse(),
  }) {
    if (subscribeToRefresh != null) {
      return subscribeToRefresh(this);
    }
    return orElse();
  }
}

abstract class _SubscribeToRefresh implements PosLoanBannerEvent {
  const factory _SubscribeToRefresh(
      {required final Stream<void> refreshStream}) = _$_SubscribeToRefresh;

  Stream<void> get refreshStream;
  @JsonKey(ignore: true)
  _$$_SubscribeToRefreshCopyWith<_$_SubscribeToRefresh> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PosLoanBannerState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  String? get bannerImageUrl => throw _privateConstructorUsedError;
  String? get bannerLink => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PosLoanBannerStateCopyWith<PosLoanBannerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PosLoanBannerStateCopyWith<$Res> {
  factory $PosLoanBannerStateCopyWith(
          PosLoanBannerState value, $Res Function(PosLoanBannerState) then) =
      _$PosLoanBannerStateCopyWithImpl<$Res, PosLoanBannerState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isError,
      String? bannerImageUrl,
      String? bannerLink});
}

/// @nodoc
class _$PosLoanBannerStateCopyWithImpl<$Res, $Val extends PosLoanBannerState>
    implements $PosLoanBannerStateCopyWith<$Res> {
  _$PosLoanBannerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isError = null,
    Object? bannerImageUrl = freezed,
    Object? bannerLink = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      bannerImageUrl: freezed == bannerImageUrl
          ? _value.bannerImageUrl
          : bannerImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerLink: freezed == bannerLink
          ? _value.bannerLink
          : bannerLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PosLoanBannerStateCopyWith<$Res>
    implements $PosLoanBannerStateCopyWith<$Res> {
  factory _$$_PosLoanBannerStateCopyWith(_$_PosLoanBannerState value,
          $Res Function(_$_PosLoanBannerState) then) =
      __$$_PosLoanBannerStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isError,
      String? bannerImageUrl,
      String? bannerLink});
}

/// @nodoc
class __$$_PosLoanBannerStateCopyWithImpl<$Res>
    extends _$PosLoanBannerStateCopyWithImpl<$Res, _$_PosLoanBannerState>
    implements _$$_PosLoanBannerStateCopyWith<$Res> {
  __$$_PosLoanBannerStateCopyWithImpl(
      _$_PosLoanBannerState _value, $Res Function(_$_PosLoanBannerState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isError = null,
    Object? bannerImageUrl = freezed,
    Object? bannerLink = freezed,
  }) {
    return _then(_$_PosLoanBannerState(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      bannerImageUrl: freezed == bannerImageUrl
          ? _value.bannerImageUrl
          : bannerImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerLink: freezed == bannerLink
          ? _value.bannerLink
          : bannerLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_PosLoanBannerState implements _PosLoanBannerState {
  const _$_PosLoanBannerState(
      {this.isLoading = true,
      this.isError = false,
      this.bannerImageUrl,
      this.bannerLink});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isError;
  @override
  final String? bannerImageUrl;
  @override
  final String? bannerLink;

  @override
  String toString() {
    return 'PosLoanBannerState(isLoading: $isLoading, isError: $isError, bannerImageUrl: $bannerImageUrl, bannerLink: $bannerLink)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PosLoanBannerState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.bannerImageUrl, bannerImageUrl) ||
                other.bannerImageUrl == bannerImageUrl) &&
            (identical(other.bannerLink, bannerLink) ||
                other.bannerLink == bannerLink));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isLoading, isError, bannerImageUrl, bannerLink);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PosLoanBannerStateCopyWith<_$_PosLoanBannerState> get copyWith =>
      __$$_PosLoanBannerStateCopyWithImpl<_$_PosLoanBannerState>(
          this, _$identity);
}

abstract class _PosLoanBannerState implements PosLoanBannerState {
  const factory _PosLoanBannerState(
      {final bool isLoading,
      final bool isError,
      final String? bannerImageUrl,
      final String? bannerLink}) = _$_PosLoanBannerState;

  @override
  bool get isLoading;
  @override
  bool get isError;
  @override
  String? get bannerImageUrl;
  @override
  String? get bannerLink;
  @override
  @JsonKey(ignore: true)
  _$$_PosLoanBannerStateCopyWith<_$_PosLoanBannerState> get copyWith =>
      throw _privateConstructorUsedError;
}
