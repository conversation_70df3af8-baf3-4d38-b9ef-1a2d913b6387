// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$HomeEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeEventCopyWith<$Res> {
  factory $HomeEventCopyWith(HomeEvent value, $Res Function(HomeEvent) then) =
      _$HomeEventCopyWithImpl<$Res, HomeEvent>;
}

/// @nodoc
class _$HomeEventCopyWithImpl<$Res, $Val extends HomeEvent>
    implements $HomeEventCopyWith<$Res> {
  _$HomeEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitHomeCopyWith<$Res> {
  factory _$$_InitHomeCopyWith(
          _$_InitHome value, $Res Function(_$_InitHome) then) =
      __$$_InitHomeCopyWithImpl<$Res>;
  @useResult
  $Res call({HomeRouteArguments? args});
}

/// @nodoc
class __$$_InitHomeCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_InitHome>
    implements _$$_InitHomeCopyWith<$Res> {
  __$$_InitHomeCopyWithImpl(
      _$_InitHome _value, $Res Function(_$_InitHome) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? args = freezed,
  }) {
    return _then(_$_InitHome(
      freezed == args
          ? _value.args
          : args // ignore: cast_nullable_to_non_nullable
              as HomeRouteArguments?,
    ));
  }
}

/// @nodoc

class _$_InitHome with DiagnosticableTreeMixin implements _InitHome {
  const _$_InitHome(this.args);

  @override
  final HomeRouteArguments? args;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.initHome(args: $args)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeEvent.initHome'))
      ..add(DiagnosticsProperty('args', args));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InitHome &&
            (identical(other.args, args) || other.args == args));
  }

  @override
  int get hashCode => Object.hash(runtimeType, args);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitHomeCopyWith<_$_InitHome> get copyWith =>
      __$$_InitHomeCopyWithImpl<_$_InitHome>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return initHome(args);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return initHome?.call(args);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (initHome != null) {
      return initHome(args);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return initHome(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return initHome?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (initHome != null) {
      return initHome(this);
    }
    return orElse();
  }
}

abstract class _InitHome implements HomeEvent {
  const factory _InitHome(final HomeRouteArguments? args) = _$_InitHome;

  HomeRouteArguments? get args;
  @JsonKey(ignore: true)
  _$$_InitHomeCopyWith<_$_InitHome> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_BannerSelectedCopyWith<$Res> {
  factory _$$_BannerSelectedCopyWith(
          _$_BannerSelected value, $Res Function(_$_BannerSelected) then) =
      __$$_BannerSelectedCopyWithImpl<$Res>;
  @useResult
  $Res call({int bannerIndex});
}

/// @nodoc
class __$$_BannerSelectedCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_BannerSelected>
    implements _$$_BannerSelectedCopyWith<$Res> {
  __$$_BannerSelectedCopyWithImpl(
      _$_BannerSelected _value, $Res Function(_$_BannerSelected) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bannerIndex = null,
  }) {
    return _then(_$_BannerSelected(
      null == bannerIndex
          ? _value.bannerIndex
          : bannerIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$_BannerSelected
    with DiagnosticableTreeMixin
    implements _BannerSelected {
  const _$_BannerSelected(this.bannerIndex);

  @override
  final int bannerIndex;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.bannerSelected(bannerIndex: $bannerIndex)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeEvent.bannerSelected'))
      ..add(DiagnosticsProperty('bannerIndex', bannerIndex));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BannerSelected &&
            (identical(other.bannerIndex, bannerIndex) ||
                other.bannerIndex == bannerIndex));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bannerIndex);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BannerSelectedCopyWith<_$_BannerSelected> get copyWith =>
      __$$_BannerSelectedCopyWithImpl<_$_BannerSelected>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return bannerSelected(bannerIndex);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return bannerSelected?.call(bannerIndex);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (bannerSelected != null) {
      return bannerSelected(bannerIndex);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return bannerSelected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return bannerSelected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (bannerSelected != null) {
      return bannerSelected(this);
    }
    return orElse();
  }
}

abstract class _BannerSelected implements HomeEvent {
  const factory _BannerSelected(final int bannerIndex) = _$_BannerSelected;

  int get bannerIndex;
  @JsonKey(ignore: true)
  _$$_BannerSelectedCopyWith<_$_BannerSelected> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_RefreshCopyWith<$Res> {
  factory _$$_RefreshCopyWith(
          _$_Refresh value, $Res Function(_$_Refresh) then) =
      __$$_RefreshCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_RefreshCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_Refresh>
    implements _$$_RefreshCopyWith<$Res> {
  __$$_RefreshCopyWithImpl(_$_Refresh _value, $Res Function(_$_Refresh) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Refresh with DiagnosticableTreeMixin implements _Refresh {
  const _$_Refresh();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.refresh()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'HomeEvent.refresh'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Refresh);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return refresh();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return refresh?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (refresh != null) {
      return refresh();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return refresh(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return refresh?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (refresh != null) {
      return refresh(this);
    }
    return orElse();
  }
}

abstract class _Refresh implements HomeEvent {
  const factory _Refresh() = _$_Refresh;
}

/// @nodoc
abstract class _$$_ReloadSasBannersCopyWith<$Res> {
  factory _$$_ReloadSasBannersCopyWith(
          _$_ReloadSasBanners value, $Res Function(_$_ReloadSasBanners) then) =
      __$$_ReloadSasBannersCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ReloadSasBannersCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_ReloadSasBanners>
    implements _$$_ReloadSasBannersCopyWith<$Res> {
  __$$_ReloadSasBannersCopyWithImpl(
      _$_ReloadSasBanners _value, $Res Function(_$_ReloadSasBanners) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ReloadSasBanners
    with DiagnosticableTreeMixin
    implements _ReloadSasBanners {
  const _$_ReloadSasBanners();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.reloadSasBanners()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'HomeEvent.reloadSasBanners'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ReloadSasBanners);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return reloadSasBanners();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return reloadSasBanners?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (reloadSasBanners != null) {
      return reloadSasBanners();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return reloadSasBanners(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return reloadSasBanners?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (reloadSasBanners != null) {
      return reloadSasBanners(this);
    }
    return orElse();
  }
}

abstract class _ReloadSasBanners implements HomeEvent {
  const factory _ReloadSasBanners() = _$_ReloadSasBanners;
}

/// @nodoc
abstract class _$$_CheckShowWelcomeOfferCopyWith<$Res> {
  factory _$$_CheckShowWelcomeOfferCopyWith(_$_CheckShowWelcomeOffer value,
          $Res Function(_$_CheckShowWelcomeOffer) then) =
      __$$_CheckShowWelcomeOfferCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {OfferBannerData offerBannerData,
      List<PendingActionModel> pendingActions,
      List<FinancialCardState> financialCardStates});
}

/// @nodoc
class __$$_CheckShowWelcomeOfferCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_CheckShowWelcomeOffer>
    implements _$$_CheckShowWelcomeOfferCopyWith<$Res> {
  __$$_CheckShowWelcomeOfferCopyWithImpl(_$_CheckShowWelcomeOffer _value,
      $Res Function(_$_CheckShowWelcomeOffer) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? offerBannerData = null,
    Object? pendingActions = null,
    Object? financialCardStates = null,
  }) {
    return _then(_$_CheckShowWelcomeOffer(
      null == offerBannerData
          ? _value.offerBannerData
          : offerBannerData // ignore: cast_nullable_to_non_nullable
              as OfferBannerData,
      null == pendingActions
          ? _value._pendingActions
          : pendingActions // ignore: cast_nullable_to_non_nullable
              as List<PendingActionModel>,
      null == financialCardStates
          ? _value._financialCardStates
          : financialCardStates // ignore: cast_nullable_to_non_nullable
              as List<FinancialCardState>,
    ));
  }
}

/// @nodoc

class _$_CheckShowWelcomeOffer
    with DiagnosticableTreeMixin
    implements _CheckShowWelcomeOffer {
  const _$_CheckShowWelcomeOffer(
      this.offerBannerData,
      final List<PendingActionModel> pendingActions,
      final List<FinancialCardState> financialCardStates)
      : _pendingActions = pendingActions,
        _financialCardStates = financialCardStates;

  @override
  final OfferBannerData offerBannerData;
  final List<PendingActionModel> _pendingActions;
  @override
  List<PendingActionModel> get pendingActions {
    if (_pendingActions is EqualUnmodifiableListView) return _pendingActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pendingActions);
  }

  final List<FinancialCardState> _financialCardStates;
  @override
  List<FinancialCardState> get financialCardStates {
    if (_financialCardStates is EqualUnmodifiableListView)
      return _financialCardStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_financialCardStates);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.checkShowWelcomeOffer(offerBannerData: $offerBannerData, pendingActions: $pendingActions, financialCardStates: $financialCardStates)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeEvent.checkShowWelcomeOffer'))
      ..add(DiagnosticsProperty('offerBannerData', offerBannerData))
      ..add(DiagnosticsProperty('pendingActions', pendingActions))
      ..add(DiagnosticsProperty('financialCardStates', financialCardStates));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckShowWelcomeOffer &&
            (identical(other.offerBannerData, offerBannerData) ||
                other.offerBannerData == offerBannerData) &&
            const DeepCollectionEquality()
                .equals(other._pendingActions, _pendingActions) &&
            const DeepCollectionEquality()
                .equals(other._financialCardStates, _financialCardStates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      offerBannerData,
      const DeepCollectionEquality().hash(_pendingActions),
      const DeepCollectionEquality().hash(_financialCardStates));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CheckShowWelcomeOfferCopyWith<_$_CheckShowWelcomeOffer> get copyWith =>
      __$$_CheckShowWelcomeOfferCopyWithImpl<_$_CheckShowWelcomeOffer>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return checkShowWelcomeOffer(
        offerBannerData, pendingActions, financialCardStates);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return checkShowWelcomeOffer?.call(
        offerBannerData, pendingActions, financialCardStates);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (checkShowWelcomeOffer != null) {
      return checkShowWelcomeOffer(
          offerBannerData, pendingActions, financialCardStates);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return checkShowWelcomeOffer(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return checkShowWelcomeOffer?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (checkShowWelcomeOffer != null) {
      return checkShowWelcomeOffer(this);
    }
    return orElse();
  }
}

abstract class _CheckShowWelcomeOffer implements HomeEvent {
  const factory _CheckShowWelcomeOffer(
          final OfferBannerData offerBannerData,
          final List<PendingActionModel> pendingActions,
          final List<FinancialCardState> financialCardStates) =
      _$_CheckShowWelcomeOffer;

  OfferBannerData get offerBannerData;
  List<PendingActionModel> get pendingActions;
  List<FinancialCardState> get financialCardStates;
  @JsonKey(ignore: true)
  _$$_CheckShowWelcomeOfferCopyWith<_$_CheckShowWelcomeOffer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_UserProfileObtainedCopyWith<$Res> {
  factory _$$_UserProfileObtainedCopyWith(_$_UserProfileObtained value,
          $Res Function(_$_UserProfileObtained) then) =
      __$$_UserProfileObtainedCopyWithImpl<$Res>;
  @useResult
  $Res call({HomeRouteArguments? args, CurrentUser? userProfileModel});

  $CurrentUserCopyWith<$Res>? get userProfileModel;
}

/// @nodoc
class __$$_UserProfileObtainedCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_UserProfileObtained>
    implements _$$_UserProfileObtainedCopyWith<$Res> {
  __$$_UserProfileObtainedCopyWithImpl(_$_UserProfileObtained _value,
      $Res Function(_$_UserProfileObtained) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? args = freezed,
    Object? userProfileModel = freezed,
  }) {
    return _then(_$_UserProfileObtained(
      args: freezed == args
          ? _value.args
          : args // ignore: cast_nullable_to_non_nullable
              as HomeRouteArguments?,
      userProfileModel: freezed == userProfileModel
          ? _value.userProfileModel
          : userProfileModel // ignore: cast_nullable_to_non_nullable
              as CurrentUser?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $CurrentUserCopyWith<$Res>? get userProfileModel {
    if (_value.userProfileModel == null) {
      return null;
    }

    return $CurrentUserCopyWith<$Res>(_value.userProfileModel!, (value) {
      return _then(_value.copyWith(userProfileModel: value));
    });
  }
}

/// @nodoc

class _$_UserProfileObtained
    with DiagnosticableTreeMixin
    implements _UserProfileObtained {
  const _$_UserProfileObtained({this.args, this.userProfileModel});

  @override
  final HomeRouteArguments? args;
  @override
  final CurrentUser? userProfileModel;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.userProfileObtained(args: $args, userProfileModel: $userProfileModel)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeEvent.userProfileObtained'))
      ..add(DiagnosticsProperty('args', args))
      ..add(DiagnosticsProperty('userProfileModel', userProfileModel));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserProfileObtained &&
            (identical(other.args, args) || other.args == args) &&
            (identical(other.userProfileModel, userProfileModel) ||
                other.userProfileModel == userProfileModel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, args, userProfileModel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserProfileObtainedCopyWith<_$_UserProfileObtained> get copyWith =>
      __$$_UserProfileObtainedCopyWithImpl<_$_UserProfileObtained>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return userProfileObtained(args, userProfileModel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return userProfileObtained?.call(args, userProfileModel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (userProfileObtained != null) {
      return userProfileObtained(args, userProfileModel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return userProfileObtained(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return userProfileObtained?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (userProfileObtained != null) {
      return userProfileObtained(this);
    }
    return orElse();
  }
}

abstract class _UserProfileObtained implements HomeEvent {
  const factory _UserProfileObtained(
      {final HomeRouteArguments? args,
      final CurrentUser? userProfileModel}) = _$_UserProfileObtained;

  HomeRouteArguments? get args;
  CurrentUser? get userProfileModel;
  @JsonKey(ignore: true)
  _$$_UserProfileObtainedCopyWith<_$_UserProfileObtained> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetBannerStateCopyWith<$Res> {
  factory _$$_SetBannerStateCopyWith(
          _$_SetBannerState value, $Res Function(_$_SetBannerState) then) =
      __$$_SetBannerStateCopyWithImpl<$Res>;
  @useResult
  $Res call({String spotId, bool visible});
}

/// @nodoc
class __$$_SetBannerStateCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_SetBannerState>
    implements _$$_SetBannerStateCopyWith<$Res> {
  __$$_SetBannerStateCopyWithImpl(
      _$_SetBannerState _value, $Res Function(_$_SetBannerState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? spotId = null,
    Object? visible = null,
  }) {
    return _then(_$_SetBannerState(
      spotId: null == spotId
          ? _value.spotId
          : spotId // ignore: cast_nullable_to_non_nullable
              as String,
      visible: null == visible
          ? _value.visible
          : visible // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_SetBannerState
    with DiagnosticableTreeMixin
    implements _SetBannerState {
  const _$_SetBannerState({required this.spotId, required this.visible});

  @override
  final String spotId;
  @override
  final bool visible;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.setBannerState(spotId: $spotId, visible: $visible)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeEvent.setBannerState'))
      ..add(DiagnosticsProperty('spotId', spotId))
      ..add(DiagnosticsProperty('visible', visible));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetBannerState &&
            (identical(other.spotId, spotId) || other.spotId == spotId) &&
            (identical(other.visible, visible) || other.visible == visible));
  }

  @override
  int get hashCode => Object.hash(runtimeType, spotId, visible);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetBannerStateCopyWith<_$_SetBannerState> get copyWith =>
      __$$_SetBannerStateCopyWithImpl<_$_SetBannerState>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return setBannerState(spotId, visible);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return setBannerState?.call(spotId, visible);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (setBannerState != null) {
      return setBannerState(spotId, visible);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return setBannerState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return setBannerState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (setBannerState != null) {
      return setBannerState(this);
    }
    return orElse();
  }
}

abstract class _SetBannerState implements HomeEvent {
  const factory _SetBannerState(
      {required final String spotId,
      required final bool visible}) = _$_SetBannerState;

  String get spotId;
  bool get visible;
  @JsonKey(ignore: true)
  _$$_SetBannerStateCopyWith<_$_SetBannerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetBannerUrlCopyWith<$Res> {
  factory _$$_SetBannerUrlCopyWith(
          _$_SetBannerUrl value, $Res Function(_$_SetBannerUrl) then) =
      __$$_SetBannerUrlCopyWithImpl<$Res>;
  @useResult
  $Res call({String url});
}

/// @nodoc
class __$$_SetBannerUrlCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_SetBannerUrl>
    implements _$$_SetBannerUrlCopyWith<$Res> {
  __$$_SetBannerUrlCopyWithImpl(
      _$_SetBannerUrl _value, $Res Function(_$_SetBannerUrl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? url = null,
  }) {
    return _then(_$_SetBannerUrl(
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_SetBannerUrl with DiagnosticableTreeMixin implements _SetBannerUrl {
  const _$_SetBannerUrl({required this.url});

  @override
  final String url;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.setBannerUrl(url: $url)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeEvent.setBannerUrl'))
      ..add(DiagnosticsProperty('url', url));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetBannerUrl &&
            (identical(other.url, url) || other.url == url));
  }

  @override
  int get hashCode => Object.hash(runtimeType, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetBannerUrlCopyWith<_$_SetBannerUrl> get copyWith =>
      __$$_SetBannerUrlCopyWithImpl<_$_SetBannerUrl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return setBannerUrl(url);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return setBannerUrl?.call(url);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (setBannerUrl != null) {
      return setBannerUrl(url);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return setBannerUrl(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return setBannerUrl?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (setBannerUrl != null) {
      return setBannerUrl(this);
    }
    return orElse();
  }
}

abstract class _SetBannerUrl implements HomeEvent {
  const factory _SetBannerUrl({required final String url}) = _$_SetBannerUrl;

  String get url;
  @JsonKey(ignore: true)
  _$$_SetBannerUrlCopyWith<_$_SetBannerUrl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_DeactivateSurveyCopyWith<$Res> {
  factory _$$_DeactivateSurveyCopyWith(
          _$_DeactivateSurvey value, $Res Function(_$_DeactivateSurvey) then) =
      __$$_DeactivateSurveyCopyWithImpl<$Res>;
  @useResult
  $Res call({PendingActionModel paModel});

  $PendingActionModelCopyWith<$Res> get paModel;
}

/// @nodoc
class __$$_DeactivateSurveyCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_DeactivateSurvey>
    implements _$$_DeactivateSurveyCopyWith<$Res> {
  __$$_DeactivateSurveyCopyWithImpl(
      _$_DeactivateSurvey _value, $Res Function(_$_DeactivateSurvey) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paModel = null,
  }) {
    return _then(_$_DeactivateSurvey(
      paModel: null == paModel
          ? _value.paModel
          : paModel // ignore: cast_nullable_to_non_nullable
              as PendingActionModel,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $PendingActionModelCopyWith<$Res> get paModel {
    return $PendingActionModelCopyWith<$Res>(_value.paModel, (value) {
      return _then(_value.copyWith(paModel: value));
    });
  }
}

/// @nodoc

class _$_DeactivateSurvey
    with DiagnosticableTreeMixin
    implements _DeactivateSurvey {
  const _$_DeactivateSurvey({required this.paModel});

  @override
  final PendingActionModel paModel;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.deactivateSurvey(paModel: $paModel)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeEvent.deactivateSurvey'))
      ..add(DiagnosticsProperty('paModel', paModel));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DeactivateSurvey &&
            (identical(other.paModel, paModel) || other.paModel == paModel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, paModel);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DeactivateSurveyCopyWith<_$_DeactivateSurvey> get copyWith =>
      __$$_DeactivateSurveyCopyWithImpl<_$_DeactivateSurvey>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return deactivateSurvey(paModel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return deactivateSurvey?.call(paModel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (deactivateSurvey != null) {
      return deactivateSurvey(paModel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return deactivateSurvey(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return deactivateSurvey?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (deactivateSurvey != null) {
      return deactivateSurvey(this);
    }
    return orElse();
  }
}

abstract class _DeactivateSurvey implements HomeEvent {
  const factory _DeactivateSurvey({required final PendingActionModel paModel}) =
      _$_DeactivateSurvey;

  PendingActionModel get paModel;
  @JsonKey(ignore: true)
  _$$_DeactivateSurveyCopyWith<_$_DeactivateSurvey> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetIsGuardLoadedCopyWith<$Res> {
  factory _$$_SetIsGuardLoadedCopyWith(
          _$_SetIsGuardLoaded value, $Res Function(_$_SetIsGuardLoaded) then) =
      __$$_SetIsGuardLoadedCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SetIsGuardLoadedCopyWithImpl<$Res>
    extends _$HomeEventCopyWithImpl<$Res, _$_SetIsGuardLoaded>
    implements _$$_SetIsGuardLoadedCopyWith<$Res> {
  __$$_SetIsGuardLoadedCopyWithImpl(
      _$_SetIsGuardLoaded _value, $Res Function(_$_SetIsGuardLoaded) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SetIsGuardLoaded
    with DiagnosticableTreeMixin
    implements _SetIsGuardLoaded {
  const _$_SetIsGuardLoaded();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeEvent.setIsGuardLoaded()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'HomeEvent.setIsGuardLoaded'));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SetIsGuardLoaded);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HomeRouteArguments? args) initHome,
    required TResult Function(int bannerIndex) bannerSelected,
    required TResult Function() refresh,
    required TResult Function() reloadSasBanners,
    required TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)
        checkShowWelcomeOffer,
    required TResult Function(
            HomeRouteArguments? args, CurrentUser? userProfileModel)
        userProfileObtained,
    required TResult Function(String spotId, bool visible) setBannerState,
    required TResult Function(String url) setBannerUrl,
    required TResult Function(PendingActionModel paModel) deactivateSurvey,
    required TResult Function() setIsGuardLoaded,
  }) {
    return setIsGuardLoaded();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HomeRouteArguments? args)? initHome,
    TResult? Function(int bannerIndex)? bannerSelected,
    TResult? Function()? refresh,
    TResult? Function()? reloadSasBanners,
    TResult? Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult? Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult? Function(String spotId, bool visible)? setBannerState,
    TResult? Function(String url)? setBannerUrl,
    TResult? Function(PendingActionModel paModel)? deactivateSurvey,
    TResult? Function()? setIsGuardLoaded,
  }) {
    return setIsGuardLoaded?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HomeRouteArguments? args)? initHome,
    TResult Function(int bannerIndex)? bannerSelected,
    TResult Function()? refresh,
    TResult Function()? reloadSasBanners,
    TResult Function(
            OfferBannerData offerBannerData,
            List<PendingActionModel> pendingActions,
            List<FinancialCardState> financialCardStates)?
        checkShowWelcomeOffer,
    TResult Function(HomeRouteArguments? args, CurrentUser? userProfileModel)?
        userProfileObtained,
    TResult Function(String spotId, bool visible)? setBannerState,
    TResult Function(String url)? setBannerUrl,
    TResult Function(PendingActionModel paModel)? deactivateSurvey,
    TResult Function()? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (setIsGuardLoaded != null) {
      return setIsGuardLoaded();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_InitHome value) initHome,
    required TResult Function(_BannerSelected value) bannerSelected,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_ReloadSasBanners value) reloadSasBanners,
    required TResult Function(_CheckShowWelcomeOffer value)
        checkShowWelcomeOffer,
    required TResult Function(_UserProfileObtained value) userProfileObtained,
    required TResult Function(_SetBannerState value) setBannerState,
    required TResult Function(_SetBannerUrl value) setBannerUrl,
    required TResult Function(_DeactivateSurvey value) deactivateSurvey,
    required TResult Function(_SetIsGuardLoaded value) setIsGuardLoaded,
  }) {
    return setIsGuardLoaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_InitHome value)? initHome,
    TResult? Function(_BannerSelected value)? bannerSelected,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult? Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult? Function(_UserProfileObtained value)? userProfileObtained,
    TResult? Function(_SetBannerState value)? setBannerState,
    TResult? Function(_SetBannerUrl value)? setBannerUrl,
    TResult? Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult? Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
  }) {
    return setIsGuardLoaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_InitHome value)? initHome,
    TResult Function(_BannerSelected value)? bannerSelected,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_ReloadSasBanners value)? reloadSasBanners,
    TResult Function(_CheckShowWelcomeOffer value)? checkShowWelcomeOffer,
    TResult Function(_UserProfileObtained value)? userProfileObtained,
    TResult Function(_SetBannerState value)? setBannerState,
    TResult Function(_SetBannerUrl value)? setBannerUrl,
    TResult Function(_DeactivateSurvey value)? deactivateSurvey,
    TResult Function(_SetIsGuardLoaded value)? setIsGuardLoaded,
    required TResult orElse(),
  }) {
    if (setIsGuardLoaded != null) {
      return setIsGuardLoaded(this);
    }
    return orElse();
  }
}

abstract class _SetIsGuardLoaded implements HomeEvent {
  const factory _SetIsGuardLoaded() = _$_SetIsGuardLoaded;
}

/// @nodoc
mixin _$HomeState {
  bool get areBannersLoading => throw _privateConstructorUsedError;
  bool get isRefreshing =>
      throw _privateConstructorUsedError; // Sas collector plugin
  bool get areSasBanner360Supported => throw _privateConstructorUsedError;
  ISasSpotViewFactory? get spotViewFactory =>
      throw _privateConstructorUsedError; // --------------------
  List<UserWidget> get widgets => throw _privateConstructorUsedError;
  List<ContentBanner> get contentBanners => throw _privateConstructorUsedError;
  int get selectedBannerIndex => throw _privateConstructorUsedError;
  CurrentUser? get userProfileModel => throw _privateConstructorUsedError;
  bool get showWelcomeOffer => throw _privateConstructorUsedError;
  Stream<CurrentUser?>? get curentUser => throw _privateConstructorUsedError;
  HomeRouteArguments? get homeRouteArguments =>
      throw _privateConstructorUsedError;
  Map<String, bool> get bannerState => throw _privateConstructorUsedError;
  bool get reloadSasBanners => throw _privateConstructorUsedError;
  String? get bannerUrl => throw _privateConstructorUsedError;
  ContentReferential? get contentReferential =>
      throw _privateConstructorUsedError;
  WelcomeBannerType? get welcomeBannerType =>
      throw _privateConstructorUsedError;
  bool get isGuardLoaded => throw _privateConstructorUsedError;
  List<PendingActionModel> get pendingActions =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $HomeStateCopyWith<HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) then) =
      _$HomeStateCopyWithImpl<$Res, HomeState>;
  @useResult
  $Res call(
      {bool areBannersLoading,
      bool isRefreshing,
      bool areSasBanner360Supported,
      ISasSpotViewFactory? spotViewFactory,
      List<UserWidget> widgets,
      List<ContentBanner> contentBanners,
      int selectedBannerIndex,
      CurrentUser? userProfileModel,
      bool showWelcomeOffer,
      Stream<CurrentUser?>? curentUser,
      HomeRouteArguments? homeRouteArguments,
      Map<String, bool> bannerState,
      bool reloadSasBanners,
      String? bannerUrl,
      ContentReferential? contentReferential,
      WelcomeBannerType? welcomeBannerType,
      bool isGuardLoaded,
      List<PendingActionModel> pendingActions});

  $CurrentUserCopyWith<$Res>? get userProfileModel;
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res, $Val extends HomeState>
    implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? areBannersLoading = null,
    Object? isRefreshing = null,
    Object? areSasBanner360Supported = null,
    Object? spotViewFactory = freezed,
    Object? widgets = null,
    Object? contentBanners = null,
    Object? selectedBannerIndex = null,
    Object? userProfileModel = freezed,
    Object? showWelcomeOffer = null,
    Object? curentUser = freezed,
    Object? homeRouteArguments = freezed,
    Object? bannerState = null,
    Object? reloadSasBanners = null,
    Object? bannerUrl = freezed,
    Object? contentReferential = freezed,
    Object? welcomeBannerType = freezed,
    Object? isGuardLoaded = null,
    Object? pendingActions = null,
  }) {
    return _then(_value.copyWith(
      areBannersLoading: null == areBannersLoading
          ? _value.areBannersLoading
          : areBannersLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshing: null == isRefreshing
          ? _value.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool,
      areSasBanner360Supported: null == areSasBanner360Supported
          ? _value.areSasBanner360Supported
          : areSasBanner360Supported // ignore: cast_nullable_to_non_nullable
              as bool,
      spotViewFactory: freezed == spotViewFactory
          ? _value.spotViewFactory
          : spotViewFactory // ignore: cast_nullable_to_non_nullable
              as ISasSpotViewFactory?,
      widgets: null == widgets
          ? _value.widgets
          : widgets // ignore: cast_nullable_to_non_nullable
              as List<UserWidget>,
      contentBanners: null == contentBanners
          ? _value.contentBanners
          : contentBanners // ignore: cast_nullable_to_non_nullable
              as List<ContentBanner>,
      selectedBannerIndex: null == selectedBannerIndex
          ? _value.selectedBannerIndex
          : selectedBannerIndex // ignore: cast_nullable_to_non_nullable
              as int,
      userProfileModel: freezed == userProfileModel
          ? _value.userProfileModel
          : userProfileModel // ignore: cast_nullable_to_non_nullable
              as CurrentUser?,
      showWelcomeOffer: null == showWelcomeOffer
          ? _value.showWelcomeOffer
          : showWelcomeOffer // ignore: cast_nullable_to_non_nullable
              as bool,
      curentUser: freezed == curentUser
          ? _value.curentUser
          : curentUser // ignore: cast_nullable_to_non_nullable
              as Stream<CurrentUser?>?,
      homeRouteArguments: freezed == homeRouteArguments
          ? _value.homeRouteArguments
          : homeRouteArguments // ignore: cast_nullable_to_non_nullable
              as HomeRouteArguments?,
      bannerState: null == bannerState
          ? _value.bannerState
          : bannerState // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      reloadSasBanners: null == reloadSasBanners
          ? _value.reloadSasBanners
          : reloadSasBanners // ignore: cast_nullable_to_non_nullable
              as bool,
      bannerUrl: freezed == bannerUrl
          ? _value.bannerUrl
          : bannerUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contentReferential: freezed == contentReferential
          ? _value.contentReferential
          : contentReferential // ignore: cast_nullable_to_non_nullable
              as ContentReferential?,
      welcomeBannerType: freezed == welcomeBannerType
          ? _value.welcomeBannerType
          : welcomeBannerType // ignore: cast_nullable_to_non_nullable
              as WelcomeBannerType?,
      isGuardLoaded: null == isGuardLoaded
          ? _value.isGuardLoaded
          : isGuardLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      pendingActions: null == pendingActions
          ? _value.pendingActions
          : pendingActions // ignore: cast_nullable_to_non_nullable
              as List<PendingActionModel>,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $CurrentUserCopyWith<$Res>? get userProfileModel {
    if (_value.userProfileModel == null) {
      return null;
    }

    return $CurrentUserCopyWith<$Res>(_value.userProfileModel!, (value) {
      return _then(_value.copyWith(userProfileModel: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_HomeStateCopyWith<$Res> implements $HomeStateCopyWith<$Res> {
  factory _$$_HomeStateCopyWith(
          _$_HomeState value, $Res Function(_$_HomeState) then) =
      __$$_HomeStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool areBannersLoading,
      bool isRefreshing,
      bool areSasBanner360Supported,
      ISasSpotViewFactory? spotViewFactory,
      List<UserWidget> widgets,
      List<ContentBanner> contentBanners,
      int selectedBannerIndex,
      CurrentUser? userProfileModel,
      bool showWelcomeOffer,
      Stream<CurrentUser?>? curentUser,
      HomeRouteArguments? homeRouteArguments,
      Map<String, bool> bannerState,
      bool reloadSasBanners,
      String? bannerUrl,
      ContentReferential? contentReferential,
      WelcomeBannerType? welcomeBannerType,
      bool isGuardLoaded,
      List<PendingActionModel> pendingActions});

  @override
  $CurrentUserCopyWith<$Res>? get userProfileModel;
}

/// @nodoc
class __$$_HomeStateCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$_HomeState>
    implements _$$_HomeStateCopyWith<$Res> {
  __$$_HomeStateCopyWithImpl(
      _$_HomeState _value, $Res Function(_$_HomeState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? areBannersLoading = null,
    Object? isRefreshing = null,
    Object? areSasBanner360Supported = null,
    Object? spotViewFactory = freezed,
    Object? widgets = null,
    Object? contentBanners = null,
    Object? selectedBannerIndex = null,
    Object? userProfileModel = freezed,
    Object? showWelcomeOffer = null,
    Object? curentUser = freezed,
    Object? homeRouteArguments = freezed,
    Object? bannerState = null,
    Object? reloadSasBanners = null,
    Object? bannerUrl = freezed,
    Object? contentReferential = freezed,
    Object? welcomeBannerType = freezed,
    Object? isGuardLoaded = null,
    Object? pendingActions = null,
  }) {
    return _then(_$_HomeState(
      areBannersLoading: null == areBannersLoading
          ? _value.areBannersLoading
          : areBannersLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshing: null == isRefreshing
          ? _value.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool,
      areSasBanner360Supported: null == areSasBanner360Supported
          ? _value.areSasBanner360Supported
          : areSasBanner360Supported // ignore: cast_nullable_to_non_nullable
              as bool,
      spotViewFactory: freezed == spotViewFactory
          ? _value.spotViewFactory
          : spotViewFactory // ignore: cast_nullable_to_non_nullable
              as ISasSpotViewFactory?,
      widgets: null == widgets
          ? _value._widgets
          : widgets // ignore: cast_nullable_to_non_nullable
              as List<UserWidget>,
      contentBanners: null == contentBanners
          ? _value._contentBanners
          : contentBanners // ignore: cast_nullable_to_non_nullable
              as List<ContentBanner>,
      selectedBannerIndex: null == selectedBannerIndex
          ? _value.selectedBannerIndex
          : selectedBannerIndex // ignore: cast_nullable_to_non_nullable
              as int,
      userProfileModel: freezed == userProfileModel
          ? _value.userProfileModel
          : userProfileModel // ignore: cast_nullable_to_non_nullable
              as CurrentUser?,
      showWelcomeOffer: null == showWelcomeOffer
          ? _value.showWelcomeOffer
          : showWelcomeOffer // ignore: cast_nullable_to_non_nullable
              as bool,
      curentUser: freezed == curentUser
          ? _value.curentUser
          : curentUser // ignore: cast_nullable_to_non_nullable
              as Stream<CurrentUser?>?,
      homeRouteArguments: freezed == homeRouteArguments
          ? _value.homeRouteArguments
          : homeRouteArguments // ignore: cast_nullable_to_non_nullable
              as HomeRouteArguments?,
      bannerState: null == bannerState
          ? _value._bannerState
          : bannerState // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      reloadSasBanners: null == reloadSasBanners
          ? _value.reloadSasBanners
          : reloadSasBanners // ignore: cast_nullable_to_non_nullable
              as bool,
      bannerUrl: freezed == bannerUrl
          ? _value.bannerUrl
          : bannerUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      contentReferential: freezed == contentReferential
          ? _value.contentReferential
          : contentReferential // ignore: cast_nullable_to_non_nullable
              as ContentReferential?,
      welcomeBannerType: freezed == welcomeBannerType
          ? _value.welcomeBannerType
          : welcomeBannerType // ignore: cast_nullable_to_non_nullable
              as WelcomeBannerType?,
      isGuardLoaded: null == isGuardLoaded
          ? _value.isGuardLoaded
          : isGuardLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      pendingActions: null == pendingActions
          ? _value._pendingActions
          : pendingActions // ignore: cast_nullable_to_non_nullable
              as List<PendingActionModel>,
    ));
  }
}

/// @nodoc

class _$_HomeState with DiagnosticableTreeMixin implements _HomeState {
  const _$_HomeState(
      {this.areBannersLoading = false,
      this.isRefreshing = false,
      this.areSasBanner360Supported = false,
      this.spotViewFactory,
      required final List<UserWidget> widgets,
      required final List<ContentBanner> contentBanners,
      this.selectedBannerIndex = 0,
      this.userProfileModel,
      this.showWelcomeOffer = false,
      this.curentUser,
      this.homeRouteArguments,
      final Map<String, bool> bannerState = const {
        'SPOT_HERO_01': false,
        'SPOT_HERO_02': false,
        'SPOT_HERO_03': false,
        'SPOT_HERO_04': false,
        'SPOT_HERO_05': false
      },
      this.reloadSasBanners = false,
      this.bannerUrl,
      this.contentReferential,
      this.welcomeBannerType,
      this.isGuardLoaded = false,
      final List<PendingActionModel> pendingActions = const []})
      : _widgets = widgets,
        _contentBanners = contentBanners,
        _bannerState = bannerState,
        _pendingActions = pendingActions;

  @override
  @JsonKey()
  final bool areBannersLoading;
  @override
  @JsonKey()
  final bool isRefreshing;
// Sas collector plugin
  @override
  @JsonKey()
  final bool areSasBanner360Supported;
  @override
  final ISasSpotViewFactory? spotViewFactory;
// --------------------
  final List<UserWidget> _widgets;
// --------------------
  @override
  List<UserWidget> get widgets {
    if (_widgets is EqualUnmodifiableListView) return _widgets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_widgets);
  }

  final List<ContentBanner> _contentBanners;
  @override
  List<ContentBanner> get contentBanners {
    if (_contentBanners is EqualUnmodifiableListView) return _contentBanners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contentBanners);
  }

  @override
  @JsonKey()
  final int selectedBannerIndex;
  @override
  final CurrentUser? userProfileModel;
  @override
  @JsonKey()
  final bool showWelcomeOffer;
  @override
  final Stream<CurrentUser?>? curentUser;
  @override
  final HomeRouteArguments? homeRouteArguments;
  final Map<String, bool> _bannerState;
  @override
  @JsonKey()
  Map<String, bool> get bannerState {
    if (_bannerState is EqualUnmodifiableMapView) return _bannerState;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_bannerState);
  }

  @override
  @JsonKey()
  final bool reloadSasBanners;
  @override
  final String? bannerUrl;
  @override
  final ContentReferential? contentReferential;
  @override
  final WelcomeBannerType? welcomeBannerType;
  @override
  @JsonKey()
  final bool isGuardLoaded;
  final List<PendingActionModel> _pendingActions;
  @override
  @JsonKey()
  List<PendingActionModel> get pendingActions {
    if (_pendingActions is EqualUnmodifiableListView) return _pendingActions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pendingActions);
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'HomeState(areBannersLoading: $areBannersLoading, isRefreshing: $isRefreshing, areSasBanner360Supported: $areSasBanner360Supported, spotViewFactory: $spotViewFactory, widgets: $widgets, contentBanners: $contentBanners, selectedBannerIndex: $selectedBannerIndex, userProfileModel: $userProfileModel, showWelcomeOffer: $showWelcomeOffer, curentUser: $curentUser, homeRouteArguments: $homeRouteArguments, bannerState: $bannerState, reloadSasBanners: $reloadSasBanners, bannerUrl: $bannerUrl, contentReferential: $contentReferential, welcomeBannerType: $welcomeBannerType, isGuardLoaded: $isGuardLoaded, pendingActions: $pendingActions)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'HomeState'))
      ..add(DiagnosticsProperty('areBannersLoading', areBannersLoading))
      ..add(DiagnosticsProperty('isRefreshing', isRefreshing))
      ..add(DiagnosticsProperty(
          'areSasBanner360Supported', areSasBanner360Supported))
      ..add(DiagnosticsProperty('spotViewFactory', spotViewFactory))
      ..add(DiagnosticsProperty('widgets', widgets))
      ..add(DiagnosticsProperty('contentBanners', contentBanners))
      ..add(DiagnosticsProperty('selectedBannerIndex', selectedBannerIndex))
      ..add(DiagnosticsProperty('userProfileModel', userProfileModel))
      ..add(DiagnosticsProperty('showWelcomeOffer', showWelcomeOffer))
      ..add(DiagnosticsProperty('curentUser', curentUser))
      ..add(DiagnosticsProperty('homeRouteArguments', homeRouteArguments))
      ..add(DiagnosticsProperty('bannerState', bannerState))
      ..add(DiagnosticsProperty('reloadSasBanners', reloadSasBanners))
      ..add(DiagnosticsProperty('bannerUrl', bannerUrl))
      ..add(DiagnosticsProperty('contentReferential', contentReferential))
      ..add(DiagnosticsProperty('welcomeBannerType', welcomeBannerType))
      ..add(DiagnosticsProperty('isGuardLoaded', isGuardLoaded))
      ..add(DiagnosticsProperty('pendingActions', pendingActions));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HomeState &&
            (identical(other.areBannersLoading, areBannersLoading) ||
                other.areBannersLoading == areBannersLoading) &&
            (identical(other.isRefreshing, isRefreshing) ||
                other.isRefreshing == isRefreshing) &&
            (identical(
                    other.areSasBanner360Supported, areSasBanner360Supported) ||
                other.areSasBanner360Supported == areSasBanner360Supported) &&
            (identical(other.spotViewFactory, spotViewFactory) ||
                other.spotViewFactory == spotViewFactory) &&
            const DeepCollectionEquality().equals(other._widgets, _widgets) &&
            const DeepCollectionEquality()
                .equals(other._contentBanners, _contentBanners) &&
            (identical(other.selectedBannerIndex, selectedBannerIndex) ||
                other.selectedBannerIndex == selectedBannerIndex) &&
            (identical(other.userProfileModel, userProfileModel) ||
                other.userProfileModel == userProfileModel) &&
            (identical(other.showWelcomeOffer, showWelcomeOffer) ||
                other.showWelcomeOffer == showWelcomeOffer) &&
            (identical(other.curentUser, curentUser) ||
                other.curentUser == curentUser) &&
            (identical(other.homeRouteArguments, homeRouteArguments) ||
                other.homeRouteArguments == homeRouteArguments) &&
            const DeepCollectionEquality()
                .equals(other._bannerState, _bannerState) &&
            (identical(other.reloadSasBanners, reloadSasBanners) ||
                other.reloadSasBanners == reloadSasBanners) &&
            (identical(other.bannerUrl, bannerUrl) ||
                other.bannerUrl == bannerUrl) &&
            (identical(other.contentReferential, contentReferential) ||
                other.contentReferential == contentReferential) &&
            (identical(other.welcomeBannerType, welcomeBannerType) ||
                other.welcomeBannerType == welcomeBannerType) &&
            (identical(other.isGuardLoaded, isGuardLoaded) ||
                other.isGuardLoaded == isGuardLoaded) &&
            const DeepCollectionEquality()
                .equals(other._pendingActions, _pendingActions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      areBannersLoading,
      isRefreshing,
      areSasBanner360Supported,
      spotViewFactory,
      const DeepCollectionEquality().hash(_widgets),
      const DeepCollectionEquality().hash(_contentBanners),
      selectedBannerIndex,
      userProfileModel,
      showWelcomeOffer,
      curentUser,
      homeRouteArguments,
      const DeepCollectionEquality().hash(_bannerState),
      reloadSasBanners,
      bannerUrl,
      contentReferential,
      welcomeBannerType,
      isGuardLoaded,
      const DeepCollectionEquality().hash(_pendingActions));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HomeStateCopyWith<_$_HomeState> get copyWith =>
      __$$_HomeStateCopyWithImpl<_$_HomeState>(this, _$identity);
}

abstract class _HomeState implements HomeState {
  const factory _HomeState(
      {final bool areBannersLoading,
      final bool isRefreshing,
      final bool areSasBanner360Supported,
      final ISasSpotViewFactory? spotViewFactory,
      required final List<UserWidget> widgets,
      required final List<ContentBanner> contentBanners,
      final int selectedBannerIndex,
      final CurrentUser? userProfileModel,
      final bool showWelcomeOffer,
      final Stream<CurrentUser?>? curentUser,
      final HomeRouteArguments? homeRouteArguments,
      final Map<String, bool> bannerState,
      final bool reloadSasBanners,
      final String? bannerUrl,
      final ContentReferential? contentReferential,
      final WelcomeBannerType? welcomeBannerType,
      final bool isGuardLoaded,
      final List<PendingActionModel> pendingActions}) = _$_HomeState;

  @override
  bool get areBannersLoading;
  @override
  bool get isRefreshing;
  @override // Sas collector plugin
  bool get areSasBanner360Supported;
  @override
  ISasSpotViewFactory? get spotViewFactory;
  @override // --------------------
  List<UserWidget> get widgets;
  @override
  List<ContentBanner> get contentBanners;
  @override
  int get selectedBannerIndex;
  @override
  CurrentUser? get userProfileModel;
  @override
  bool get showWelcomeOffer;
  @override
  Stream<CurrentUser?>? get curentUser;
  @override
  HomeRouteArguments? get homeRouteArguments;
  @override
  Map<String, bool> get bannerState;
  @override
  bool get reloadSasBanners;
  @override
  String? get bannerUrl;
  @override
  ContentReferential? get contentReferential;
  @override
  WelcomeBannerType? get welcomeBannerType;
  @override
  bool get isGuardLoaded;
  @override
  List<PendingActionModel> get pendingActions;
  @override
  @JsonKey(ignore: true)
  _$$_HomeStateCopyWith<_$_HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}
