part of 'home_bloc.dart';

@freezed
class HomeEvent with _$HomeEvent {
  const factory HomeEvent.initHome(HomeRouteArguments? args) = _InitHome;
  const factory HomeEvent.bannerSelected(int bannerIndex) = _BannerSelected;
  const factory HomeEvent.refresh() = _Refresh;
  const factory HomeEvent.reloadSasBanners() = _ReloadSasBanners;
  const factory HomeEvent.checkShowWelcomeOffer(
    OfferBannerData offerBannerData,
    List<PendingActionModel> pendingActions,
    List<FinancialCardState> financialCardStates,
  ) = _CheckShowWelcomeOffer;

  // events just for subscription handling
  const factory HomeEvent.userProfileObtained({HomeRouteArguments? args, CurrentUser? userProfileModel}) =
      _UserProfileObtained;
  const factory HomeEvent.setBannerState({required String spotId, required bool visible}) = _SetBannerState;
  const factory HomeEvent.setBannerUrl({required String url}) = _SetBannerUrl;
  const factory HomeEvent.deactivateSurvey({required PendingActionModel paModel}) = _DeactivateSurvey;
  const factory HomeEvent.setIsGuardLoaded() = _SetIsGuardLoaded;
}
