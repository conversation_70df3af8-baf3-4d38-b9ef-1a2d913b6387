import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_content/capp_content.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_personal_core/capp_personal_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_transactions_core/capp_transactions_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:capp_vas_core/capp_vas_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger_flutter/logger_flutter.dart';
import 'package:navigator/navigator.dart';

import '../../../capp_home_core.dart';

class AccountScreen extends StatefulWidget with RouteWrapper {
  const AccountScreen({Key? key}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => MultiBlocProvider(
        providers: [
          BlocProvider<AccountMenuBloc>(
            create: (context) => context.get<AccountMenuBloc>()..add(const AccountMenuEvent.init()),
          ),
          BlocProvider(
            create: (context) => context.get<BigDataCollectionBloc>(),
          ),
        ],
        child: this,
      );

  @override
  State<AccountScreen> createState() => AccountScreenState();
}

class AccountScreenState extends State<AccountScreen> {
  /// used for help chat icon based on country, default is VN version. After code split can be removed
  @protected
  bool get useCuppo => false;

  @protected
  bool get bigDataCollection => true;

  @override
  void initState() {
    super.initState();
    context.get<CappTrackingService>().trackEvent(
      event: KoyalEvent.accountScreenView,
      eventCategory: KoyalTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: 'account_screen',
      customDimensions: {
        TrackingProperties.propertyCdSas: '1',
      },
    );
  }

  @override
  Widget build(BuildContext context) => BlocConsumer<AccountMenuBloc, AccountMenuState>(
        listenWhen: (previous, current) => current is AccountMenuInitial,
        listener: (context, state) => context.read<AccountMenuBloc>().add(const AccountMenuEvent.init()),
        builder: (context, state) => BlocBuilder<FeatureFlagBloc, FeatureFlagState>(
          builder: (context, flagState) => BlocBuilder<UnreadBloc, UnreadState>(
            buildWhen: (previous, current) => previous.hasUnread != current.hasUnread,
            builder: (context, unreadState) {
              return KoyalScaffold(
                customIdentifier: 'account_screen',
                key: const Key('__accountScreen__'),
                safeAreaCustomSettings: const SafeAreaCustomSettings(bottom: false),
                safeAreaBarCustomSettings: const SafeAreaCustomSettings(bottom: false),
                appBar: KoyalAppBar(
                  key: const Key('__accountScreenAppBar__'),
                  title: L10nCappHome.of(context).accountScreenTitle,
                  leading: KoyalAppBarLeading.none,
                ),
                body: Builder(
                  builder: (context) {
                    return switch (state) {
                      AccountMenuLoadSuccess() => _buildBody(context, state, unreadState),
                      _ => const Center(child: KoyalProgressIndicator.large())
                    };
                  },
                ),
              );
            },
          ),
        ),
      );

  Widget _buildBody(BuildContext context, AccountMenuLoadSuccess state, UnreadState unreadState) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (context.isFlagEnabledWatch(FeatureFlag.accountGameRewardsWidget))
            KoyalPadding.normalHorizontal(
              child: GameRewardsBanner(
                title: L10nCappUi.of(context).gameRewardsBannerTitle,
                text: L10nCappUi.of(context).gameRewardsBannerText,
                buttonText: L10nCappUi.of(context).gameRewardsBannerButtonText,
                isLoading: false,
                onButtonPressed: () => GamesLauncherService.launchGameRewardWallet(context),
              ),
            ),
          MenuListView(
            key: const Key('__accountPageListView__'),
            items: _menuItems(context, state, unreadState),
            isRoot: false,
          ),
        ],
      ),
    );
  }

  List<ListMenuItem> _menuItems(
    BuildContext context,
    AccountMenuLoadSuccess state,
    UnreadState unreadState,
  ) {
    final map = {
      L10nCappHome.of(context).menuGroupGeneral: generalItems(context, state, unreadState),
      L10nCappHome.of(context).menuGroupSpecialOffers: specialOffersItems(context, state),
      L10nCappHome.of(context).menugGroupServices: _servicesItems(context, state, unreadState),
      L10nCappHome.of(context).menuGroupHelpSupport: helpAndSupportItems(context, state),
      '': _loginLogout(context, state),
      'Development': _debugItems(context, state),
    };

    // add empty item to shift separators one row up
    final result = [ListMenuItem(title: '')];
    var counter = 0;
    for (final groupTitle in map.keys) {
      ++counter;
      final items = map[groupTitle]!.mapWithIndex((i, item) {
        if (i == 0) {
          result.last.dividerTitle = groupTitle;
          result.last.thickDivider = counter > 1;
        }
        return item;
      });
      result.addAll(items);
    }
    return result;
  }

  List<ListMenuItem> _loginLogout(BuildContext context, AccountMenuLoadSuccess state) => [
        if (!state.isAnonymous)
          if (context.isFlagEnabledWatch(FeatureFlag.replaceLogoutWithLock))
            ListMenuItem(
              key: const Key('__lockAccountRow__'),
              title: L10nCappHome.of(context).menuLockAccount,
              onTap: (_) {
                context.read<LockStatusBloc>().add(
                      const LockStatusEvent.setLock(
                        locked: true,
                        replaceCurrentScreen: true,
                        disableAutoFaceBiometricScan: true,
                      ),
                    );
                if (bigDataCollection) {
                  context.get<BigDataCollectionBloc>().add(
                        BigDataCollectionEvent.process(
                          flow: BigDataFlow.logout.getEventCode(),
                          displayData: DeviceUtils.getDisplayData(context),
                        ),
                      );
                }
              },
              icon: Icon(KoyalIcons.account_lock_outline, color: ColorTheme.of(context).primaryColor),
            )
          else
            ListMenuItem(
              key: const Key('__logoutRow__'),
              title: L10nCappHome.of(context).menuLogout,
              onTap: (_) {
                _trackEvent(
                  context,
                  KoyalEvent.homeAccountLoginRegisterClick,
                  'login_register',
                );
                context.navigateToLogoutScreen(LogoutReason.userLogout);
              },
              icon: Icon(KoyalIcons.exit_outline, color: ColorTheme.of(context).primaryColor),
            ),
        if (state.isAnonymous)
          ListMenuItem(
            key: const Key('__loginRow__'),
            title: L10nCappHome.of(context).menuLogin,
            onTap: (_) {
              _trackEvent(
                context,
                KoyalEvent.homeAccountLoginRegisterClick,
                'login_register',
                obsoleteLabel: 'login',
              );
              context.navigateToAuthEntryPoint();
            },
            icon: Icon(KoyalIcons.enter_outline, color: ColorTheme.of(context).primaryColor),
          ),
      ];

  @protected
  List<ListMenuItem> helpAndSupportItems(
    BuildContext context,
    AccountMenuLoadSuccess state,
  ) {
    return [
      if (state.isIDInsider == true &&
          state.isProspect &&
          context.get<CappHomeSettings>().accountMenuItems.contains(AccountMenuItem.retrieveMyAccount))
        ListMenuItem(
          key: const Key('__retrieveMyAccountMenuRow__'),
          title: L10nCappHome.of(context).menuRetrieveMyAccount,
          icon: Icon(KoyalIcons.upload_document_text_outline, color: ColorTheme.of(context).primaryColor),
          onTap: (_) {
            _trackEvent(context, KoyalEvent.homeAccountRetrieveClick, 'retrieve');
            if (context.isFlagEnabledRead(FeatureFlag.retrieveAccountBos)) {
              context.navigator.push(
                path: NavigatorPath.cappAuth.retrieveAccount2ndIdentifierScreen,
                arguments: SecondIdentifierScreenArguments(),
              );
            } else {
              context.navigator.push(
                path: NavigatorPath.cappAuth.secondIdHoselPairingScreen,
                arguments: SecondIdHoselPairingScreenArguments(isFromAccount: true),
              );
            }
          },
        ),
      ListMenuItem(
        key: const Key('__editDocumentsRow__'),
        title: L10nCappHome.of(context).menuDocuments,
        onTap: (_) {
          _trackEvent(context, KoyalEvent.homeAccountDocumentsClick, 'documents');
          context.navigator.pushFromPackage(package: 'CappContent', screen: 'DocumentsMenuScreen');
        },
        icon: Icon(KoyalIcons.documents_outline, color: ColorTheme.of(context).primaryColor),
      ),
      if (state.isClient && context.isFlagEnabledWatch(FeatureFlag.loanSignatureAppendix))
        ListMenuItem(
          key: const Key('__editPersonalDocumentsRow__'),
          title: L10nCappHome.of(context).menuPersonalDocuments,
          onTap: (_) {
            //_trackEvent(context, KoyalEvent.homeAccountPersonalDocumentsClick, 'personaldocuments');
            context.navigator.pushFromPackage(package: 'CappSelfService', screen: 'PersonalDocumentsScreen');
          },
          icon: Icon(KoyalIcons.document_text_outline, color: ColorTheme.of(context).primaryColor),
        ),
      if (context.get<CappHomeSettings>().accountMenuItems.contains(AccountMenuItem.warningZone))
        ListMenuItem(
          key: const Key('__warningZoneMenuRow__'),
          title: L10nCappHome.of(context).cappMainWarningzone,
          icon: Icon(KoyalIcons.flame_outline, color: ColorTheme.of(context).primaryColor),
          onTap: (_) {
            _trackEvent(context, KoyalEvent.homeAccountWarningZoneClick, 'warningzone');
            context.navigator.pushFromPackage(
              package: 'CappContent',
              screen: 'BlogsScreen',
              arguments: BlogsArguments(initialCategoryId: context.get<CappHomeSettings>().blogsWarningZoneCategoryId),
            );
          },
        ),
      if (context.isFlagEnabledWatch(KoyalFeatureFlag.helpCenter))
        ListMenuItem(
          key: const Key('__helpCenterRow__'),
          title: L10nCappHome.of(context).menuHelpCenter,
          onTap: (_) {
            _trackEvent(context, KoyalEvent.accountClickHelpCenter, 'help_center');
            context.navigator.pushFromPackage(
              package: 'CappContent',
              screen: 'HelpCenterScreen',
              arguments: HelpCenterArguments(useCuppo: useCuppo),
            );
          },
          icon: Icon(KoyalIcons.help_circle_outline, color: ColorTheme.of(context).primaryColor),
        )
      else
        ListMenuItem(
          key: const Key('__faqRow__'),
          title: L10nCappHome.of(context).menuFaq,
          onTap: (_) {
            _trackEvent(context, KoyalEvent.homeAccountFaqClick, 'faq');
            context.navigator.pushFromPackage(package: 'CappContent', screen: 'FaqMenuScreen');
          },
          icon: Icon(KoyalIcons.help_circle_outline, color: ColorTheme.of(context).primaryColor),
        ),
      if (context.isFlagEnabledWatch(FeatureFlag.generalFeedbackFlow))
        ListMenuItem(
          key: const Key('__feedbackRow__'),
          title: L10nCappHome.of(context).feedback,
          onTap: (_) {
            context.get<CappTrackingService>().trackEvent(
                  event: KoyalEvent.appFeedbackAccountMenuClick,
                  eventCategory: KoyalTrackingCategories.accountScreen,
                  eventAction: KoyalAnalyticsConstants.click,
                  eventLabel: KoyalTrackingCategories.feedbackSuggestions,
                );
            showGeneralFeedbackOverlay(context);
          },
          icon: Icon(KoyalIcons.chatbubbles_new_outline, color: ColorTheme.of(context).primaryColor),
        ),
      ListMenuItem(
        key: const Key('__contactUsRow__'),
        title: L10nCappHome.of(context).menuContactus,
        icon: Icon(KoyalIcons.call_outline, color: ColorTheme.of(context).primaryColor),
        onTap: (_) {
          _trackEvent(context, KoyalEvent.homeAccountContactClick, 'contact', obsoleteLabel: 'contact_us');
          context.navigator.pushFromPackage(
            package: 'CappContent',
            screen: 'ContactUsScreen',
            arguments: ContactUsArguments(useCuppo: useCuppo),
          );
        },
      ),
      ListMenuItem(
        key: const Key('__aboutUsRow__'),
        title: L10nCappHome.of(context).menuAboutus,
        icon: Icon(KoyalIcons.information_circle_outline, color: ColorTheme.of(context).primaryColor),
        onTap: (_) {
          _trackEvent(context, KoyalEvent.homeAccountAboutUsClick, 'about_us');
          context.navigator.pushFromPackage(package: 'CappContent', screen: 'AboutUsScreen');
        },
      ),
      if (context.get<CappHomeSettings>().accountMenuItems.contains(AccountMenuItem.iPrice))
        ListMenuItem(
          key: const Key('__iPriceMenuRow__'),
          title: L10nCappHome.of(context).menuIprice,
          icon: Icon(KoyalIcons.pricetag_outline, color: ColorTheme.of(context).primaryColor),
          onTap: (_) {
            _trackEvent(context, KoyalEvent.homeAccountIPriceClick, 'iprice');
            GmaPlatform.launchUrl('https://iprice.vn');
          },
        ),
    ];
  }

  List<ListMenuItem> _debugItems(BuildContext context, AccountMenuLoadSuccess state) {
    return [
      if (state.isInsider && context.isFlagEnabledWatch(FeatureFlag.logConsole))
        ListMenuItem(
          key: const Key('__viewLogsRow__'),
          title: L10nCappPersonal.of(context).cappPersonalViewLogs,
          icon: Icon(
            KoyalIcons.coins_outline,
            color: ColorTheme.of(context).primaryColor,
          ),
          onTap: (_) {
            _trackEvent(context, null, 'viewLogs');
            LogConsole.open(context, logFilePath: 'temp/log.log');
          },
        ),
      if (state.isInsider && context.isFlagEnabledWatch(FeatureFlag.logConsole))
        ListMenuItem(
          key: const Key('__collectLogsRow__'),
          title: L10nCappPersonal.of(context).cappPersonalSendLogs,
          icon: Icon(
            KoyalIcons.swap_vertical_outline,
            color: ColorTheme.of(context).primaryColor,
          ),
          onTap: (_) {
            _trackEvent(context, null, 'collectLogs');
            try {
              context.get<IDiagnosticsRepository>().sendLogs();
              KoyalSnackBar.show(
                context: context,
                hasBottomButton: false,
                message: L10nCappPersonal.of(context).cappPersonalSuccess,
              );

              // ignore: avoid_catches_without_on_clauses
            } catch (err) {
              KoyalSnackBar.show(
                context: context,
                hasBottomButton: false,
                message: L10nCappPersonal.of(context).cappPersonalErrorUnexpected,
              );
            }
          },
        ),
      if (context.isModuleRegistred('CappDemo') &&
          (context.isFlagEnabledWatch(KoyalFeatureFlag.profileMenuDevelopmentDemo) ||
              Flavor.of(context).isTest ||
              kDebugMode))
        ListMenuItem(
          title: 'Demo',
          icon: Icon(
            // ignore: no-native-icons
            Icons.pest_control_rodent,
            color: ColorTheme.of(context).primaryColor,
          ),
          onTap: (context) => context.navigator.pushFromPackage(
            package: 'CappDemo',
            screen: 'DemoScreen',
          ),
        ),
    ];
  }

  @protected
  List<ListMenuItem> generalItems(BuildContext context, AccountMenuLoadSuccess state, UnreadState unreadState) => [
        if (!state.isAnonymous)
          ListMenuItem(
            key: const Key('__profileMenuRow__'),
            title: L10nCappHome.of(context).menuPersonalDetails,
            icon: Icon(KoyalIcons.person_outline, color: ColorTheme.of(context).primaryColor),
            onTap: (_) {
              _trackEvent(context, KoyalEvent.homeAccountPersonalDetailsClick, 'personal_details');
              context.navigator.push(
                path: NavigatorPath.cappPersonal.personalDetailsScreenV2,
                arguments: PersonalDetailsScreenArguments(isCustomer: state.isClient),
              );
            },
          ),
        ListMenuItem(
          key: const Key('__notificationsMenuRow__'),
          title: L10nCappHome.of(context).menuNotifications,
          icon: unreadState.hasUnread && state.isAnonymous
              ? Icon(KoyalIcons.notifications_new_outline, color: ColorTheme.of(context).primaryColor)
              : Icon(KoyalIcons.notifications_outline, color: ColorTheme.of(context).primaryColor),
          onTap: (_) {
            _trackEvent(context, KoyalEvent.homeAccountNofiticationsClick, 'notifications', obsoleteLabel: 'inbox');
            context.navigator.pushFromPackage(
              package: 'CappHomeCore',
              screen: 'InboxScreen',
              arguments: InboxScreenArguments(InboxMessageType.all),
            );
          },
        ),
        if (!state.isAnonymous && context.isFlagEnabledRead(FeatureFlag.showPurchaseInsuranceEntryPoint))
          ListMenuItem(
            key: const Key('__insuranceMenuRow__'),
            title: L10nCappHome.of(context).menuPurchasedInsurances,
            icon: Icon(KoyalIcons.shield_checkmark_outline, color: ColorTheme.of(context).primaryColor),
            onTap: (_) {
              _trackEvent(context, KoyalEvent.homeAccountPurchaseInsuranceClick, 'purchased_insurances');
              context.navigator.pushFromPackage(
                package: 'CappVas',
                screen: 'MyInsuranceListScreen',
                arguments: CspInsuranceListRouteArgs(type: InsuranceListType.valid),
              );
            },
          ),
        if (!state.isAnonymous)
          ListMenuItem(
            key: const Key('__loansMenuRow__'),
            title: L10nCappHome.of(context).menuMyloans,
            icon: Icon(KoyalIcons.receipt_outline, color: ColorTheme.of(context).primaryColor),
            onTap: (_) {
              _trackEvent(context, KoyalEvent.homeAccountLoansClick, 'loans');
              if (!context.isFlagEnabledRead(FeatureFlag.mainTabsActivity)) {
                context.navigator.toMainScreen(arguments: MainScreenArguments(initialTab: TabItem.loans));
              } else {
                // context.navigator.push(path: NavigatorPath.);
                context.navigator.pushFromPackage(
                  package: 'CappHomeCore',
                  screen: 'LoansScreen',
                );
              }
            },
          ),
        if (!state.isAnonymous && context.isFlagEnabledWatch(FeatureFlag.repaymentAdaManagementMenu))
          ListMenuItem(
            key: const Key('__adaMenuRow__'),
            title: L10nCappHome.of(context).menuAda,
            icon: Icon(KoyalIcons.wallet_outline, color: ColorTheme.of(context).primaryColor),
            onTap: (_) {
              context.navigator.pushFromPackage(
                package: 'CappRepayment',
                screen: 'RepaymentAdaManagementScreen',
                arguments: ScreenArguments(
                  map: {
                    'fromFlow': 'accountMenu',
                  },
                ),
              );
            },
          ),
        if (context.isFlagEnabledWatch(FeatureFlag.profileMenuSelfService))
          ListMenuItem(
            key: const Key('__selfServiceRow__'),
            title: L10nCappTransactions.of(context).selfService,
            onTap: (_) {
              // trackEvent
              context.navigator.pushFromPackage(package: 'CappTransactions', screen: 'SelfServiceMenuScreen');
            },
            icon: Icon(KoyalIcons.cash_outline, color: ColorTheme.of(context).primaryColor),
          ),
        if (!state.isAnonymous && context.get<CappHomeSettings>().accountMenuItems.contains(AccountMenuItem.promo))
          ListMenuItem(
            key: const Key('__promoMenuRow__'),
            title: L10nCappHome.of(context).menuPromo,
            icon: Icon(KoyalIcons.coupon_outline, color: ColorTheme.of(context).primaryColor),
            onTap: (_) {
              _trackEvent(context, KoyalEvent.homeAccountPromoClick, 'promo');
            },
          ),
        if (!state.isAnonymous &&
            context.get<CappHomeSettings>().accountMenuItems.contains(AccountMenuItem.transactionHistory) &&
            context.isFlagEnabledWatch(FeatureFlag.transactionAccountScreenTab))
          ListMenuItem(
            key: const Key('__transactionsHistoryRow__'),
            title: L10nCappHome.of(context).menuTransactionHistory,
            onTap: (_) {
              _trackEvent(context, KoyalEvent.homeAccountTransationHistoryClick, 'trans_history');
              context.navigator.pushFromPackage(package: 'CappTransactionHistory', screen: 'TransactionHistoryScreen');
            },
            icon: Icon(KoyalIcons.history_outline, color: ColorTheme.of(context).primaryColor),
          ),
        if (!state.isAnonymous ||
            context.isFlagEnabledWatch(FeatureFlag.disableUser) ||
            context.read<LocalizationBloc>().state.supportedLocales.length > 1 ||
            (state.isInsider && context.read<LocalizationBloc>().state.insiderLocales.isNotEmpty))
          ListMenuItem(
            key: const Key('__settingsRow__'),
            title: L10nCappHome.of(context).menuSettings,
            onTap: (_) {
              _trackEvent(context, KoyalEvent.homeAccountSettingsClick, 'settings');
              context.navigator.pushFromPackage(package: 'CappHome', screen: 'SettingsMenuScreen');
            },
            icon: Icon(KoyalIcons.setting_outline, color: ColorTheme.of(context).primaryColor),
          ),
        if (!state.isAnonymous &&
            context.get<CappHomeSettings>().accountMenuItems.contains(AccountMenuItem.manageCards) &&
            context.isFlagEnabledWatch(FeatureFlag.repaymentManageSavedCard))
          ListMenuItem(
            key: const Key('__manageCardsRow__'),
            title: L10nCappHome.of(context).menuManageCards,
            onTap: (_) {
              context.navigator.pushFromPackage(package: 'CappRepayment', screen: 'RepaymentCardManagementScreen');
            },
            icon: Icon(KoyalIcons.card_outline, color: ColorTheme.of(context).primaryColor),
          ),
      ];

  List<ListMenuItem> _servicesItems(BuildContext context, AccountMenuLoadSuccess state, UnreadState unreadState) => [
        if (!state.isProspect && !state.isAnonymous && context.isFlagEnabledWatch(FeatureFlag.hcProtectEntryPoint))
          ListMenuItem(
            key: const Key('__hcProtectRow__'),
            title: L10nCappHome.of(context).menuServicesHcProtect,
            icon: Icon(KoyalIcons.hc_protect_checkmark_outline, color: ColorTheme.of(context).primaryColor),
            onTap: (_) {
              context.navigator.pushFromPackage(
                package: 'CappProtectionPlan',
                screen: 'HomeCreditProtectDashboard',
              );
            },
          ),
      ];

  @protected
  List<ListMenuItem> specialOffersItems(BuildContext context, AccountMenuLoadSuccess state) => [];

  void _trackEvent(
    BuildContext context,
    KoyalEvent? event,
    String eventLabel, {
    String? obsoleteLabel,
  }) {
    final ts = context.get<CappTrackingService>()
      ..trackClickEvent(eventCategory: 'user_action_details', eventLabel: obsoleteLabel ?? eventLabel);
    if (event != null) {
      ts.trackEvent(
        event: event,
        eventCategory: KoyalTrackingCategories.accountScreen,
        eventAction: KoyalAnalyticsConstants.click,
        eventLabel: eventLabel,
      );
    }
  }
}
