import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../capp_home_core.dart';
import '../../infrastructure/action_belt/shopingmall_extension.dart';

final emptyConfigItem = ActionBeltItemConfig(label: emptyConfigItemLabel, onTap: () {}, trackingLabel: '');
const emptyConfigItemLabel = 'emptyConfigItemLabel';

typedef BuildActionBeltItems = List<ActionBeltItemConfig> Function({
  required BuildContext context,
  List<String> widgetIds,
  CurrentUser? user,
  String? language,
  bool returnAllExistingItems,
});

class CountryActionBelt extends StatefulWidget {
  final BuildActionBeltItems buildActionBeltItems;
  final String? title;
  final bool useNewDesign;

  const CountryActionBelt({
    Key? key,
    required this.buildActionBeltItems,
    this.title,
    this.useNewDesign = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _CountryActionBeltState();
}

class _CountryActionBeltState extends State<CountryActionBelt> with GameDeeplinkResolverMixin {
  List<String> sentViewEventLabels = [];

  @override
  Widget build(BuildContext context) => BlocProvider<ActionBeltBloc>.value(
        value: context.get<ActionBeltBloc>(),
        child: BlocBuilder<ActionBeltBloc, ActionBeltState>(
          builder: (context, state) {
            final List<IActionBeltItem> items =
                !state.isLoading && (state.widgets != null || state.actionBeltWidgets != null)
                    ? _buildActionBeltItems(context, state).where((item) {
                        if (item.featureFlagName != null) {
                          if (!context.isFlagEnabledWatch(item.featureFlagName!)) {
                            return false;
                          }
                        }
                        return true;
                      }).map(
                        (item) {
                          return ActionBeltItem(
                            labelBuilder: (_) => item.label,
                            icon: item.icon,
                            iconUrl: item.iconUrl,
                            key: Key('__actionBeltItem_${item.trackingLabel}'),
                            title: item.label,
                            badge: item.badge,
                            onTap: (context) {
                              item.onTap();
                            },
                            child: VisibilityDetector(
                              key: Key('__actionBeltVisibilityDetector_${item.trackingLabel}'),
                              onVisibilityChanged: (visibility) {
                                if (visibility.visibleFraction > 0.9) _trackViewEvent(context, item);
                              },
                              child: CircularIconButton(
                                icon: item.icon,
                                iconUrl: item.iconUrl,
                                label: item.label,
                                path: item.path,
                                package: item.package,
                                showIconWithoutFrame: widget.useNewDesign || item.showIconWithoutFrame,
                                containerColor: widget.useNewDesign ? item.bgColor : null,
                                showIconContainer: !widget.useNewDesign,
                                badge: item.badge,
                                minItemWidth: 48,
                                maxItemWidth: 64,
                                onTap: () {
                                  _trackClickEvent(context, item);
                                  item.onTap();
                                },
                              ),
                            ),
                          );
                        },
                      ).toList()
                    : loadingItemsIterable
                        .map((item) => ActionBeltItem(labelBuilder: (_) => '', child: const ActionBeltLoadingItem()))
                        .toList();

            var shorterList = <IActionBeltItem>[];
            if (items.length > 8) {
              shorterList = items.sublist(0, 8);
            }

            return HomeScreenWidgetPadding(
              useNewDesign: widget.useNewDesign,
              title: widget.useNewDesign || context.get<CappHomeSettings>().actionBeltHasTitle
                  ? widget.title ?? L10nCappHome.of(context).otherServices
                  : null,
              titleAction: (widget.useNewDesign && items.length > 4)
                  ? TertiaryButton(
                      key: const Key('__blogSectionViewAllButton__'),
                      text: '${L10nCappHome.of(context).viewAll} (${items.length})',
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        context.navigator.pushFromPackage(
                          package: 'CappHome',
                          screen: 'CountryActionBeltGridView',
                          arguments: CountryActionBeltScreenArguments(children: items),
                        );
                      },
                    )
                  : null,
              childHorizontalPadding: false,
              isChildPaddingBot: false,
              child: CappVisibilityDetector(
                key: Key('__actionBeltVisibilityDetector_${widget.key}'),
                onBecameVisible: () => _trackViewEvent(context, null),
                child: ActionBelt.autosize(
                  maximumItemWidth: 64,
                  minimumItemWidth: 48,
                  isOnHomescreen: !widget.useNewDesign,
                  displayOldDesign: !context.isFlagEnabledRead(FeatureFlag.actionBeltOldDesign),
                  moreItemsButton: ActionBeltItem(
                    labelBuilder: (_) => L10nCappHome.of(context).actionBeltItemMore,
                    icon: KoyalIcons.ellipsis_horizontal_outline,
                    key: const Key('__actionBeltItem_more'),
                    title: L10nCappHome.of(context).actionBeltItemMore,
                    onTap: (context) {},
                    child: VisibilityDetector(
                      key: const Key('__actionBeltVisibilityDetector_more'),
                      onVisibilityChanged: (visibility) {
                        // if (visibility.visibleFraction > 0.9) _trackViewEvent(context, item);
                      },
                      child: CircularIconButton(
                        icon: KoyalIcons.ellipsis_horizontal_outline,
                        label: L10nCappHome.of(context).actionBeltItemMore,
                        containerColor: widget.useNewDesign ? const Color(0xFFF8F8F8) : null,
                        badge: '',
                        minItemWidth: 48,
                        maxItemWidth: 64,
                        onTap: () {
                          context.navigator.pushFromPackage(
                            package: 'CappHome',
                            screen: 'CountryActionBeltGridView',
                            arguments: CountryActionBeltScreenArguments(
                              children: items,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  children: (widget.useNewDesign && items.length > 8) ? shorterList : items,
                ),
              ),
            );
          },
        ),
      );

  List<ActionBeltItemConfig> _buildActionBeltItems(BuildContext context, ActionBeltState state) {
    if (state.actionBeltWidgets != null) {
      return state.actionBeltWidgets!
          .map(
            (i) => ActionBeltItemConfig(
              label: i.title ?? '',
              trackingLabel: i.trackingLabel ?? '',
              showIconWithoutFrame: true,
              featureFlagName: i.featureFlagName,
              iconUrl: i.iconUrl ?? '',
              badge: i.badge ?? '',
              bgColor: i.bgColor?.getColor(),
              onTap: () async {
                if (i.actionUrl == null) {
                  return;
                } else {
                  await resolveGameDeeplink(
                    context,
                    i.trackingLabel == 'Marketplace'
                        ? i.actionUrl!.enrichShoppingMallUrl(state.userCuid, state.userGuid)
                        : i.actionUrl!,
                    linkTitle: i.title ?? '',
                    askForLoginCondition: () => (i.actionUrl?.contains('.vn') ?? false) && i.trackingLabel == 'game',
                  );
                }
              },
            ),
          )
          .toList();
    } else if (state.widgets != null) {
      return widget.buildActionBeltItems(
        context: context,
        widgetIds: state.widgets!,
        user: state.user,
        language: state.language,
      );
    }
    return [];
  }

  void _trackClickEvent(BuildContext context, ActionBeltItemConfig item) {
    context.get<CappTrackingService>().trackEvent(
      event: KoyalEvent.homeDashboardActionBeltClick,
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'action_belt',
      customDimensions: {
        TrackingProperties.propertyCdItemName: item.trackingLabel,
      },
    );
  }

  void _trackViewEvent(BuildContext context, ActionBeltItemConfig? item) {
    if (item == null) {
      _checkedViewEvent(
        context,
        KoyalEvent.homeDashboardActionBeltView,
        'action_belt',
        KoyalTrackingCategories.homeDashboard,
        'view_more',
      );
    } else {
      _checkedViewEvent(
        context,
        KoyalEvent.homeDashboardActionBeltView,
        'action_belt',
        KoyalTrackingCategories.homeDashboard,
        item.trackingLabel,
      );
    }
  }

  void _checkedViewEvent(
    BuildContext context,
    KoyalEvent? event,
    String eventLabel,
    String eventCategory,
    String trackingLabel,
  ) {
    if (!sentViewEventLabels.contains(eventLabel)) {
      sentViewEventLabels.add(eventLabel);
      context.get<CappTrackingService>().trackEvent(
        event: event,
        eventCategory: eventCategory,
        eventAction: KoyalAnalyticsConstants.view,
        eventLabel: 'action_belt',
        customDimensions: {
          TrackingProperties.propertyCdItemName: trackingLabel,
        },
      );
    }
  }
}
