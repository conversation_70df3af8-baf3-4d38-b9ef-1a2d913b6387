import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../capp_home_core.dart';

class CountryActionBeltGridView extends StatefulWidget {
  final CountryActionBeltScreenArguments arguments;
  const CountryActionBeltGridView({super.key, required this.arguments});

  @override
  State<CountryActionBeltGridView> createState() => _CountryActionBeltGridViewState();
}

class _CountryActionBeltGridViewState extends State<CountryActionBeltGridView> {
  @override
  void initState() {
    super.initState();
    widget.arguments.onInitScreen?.call();
  }

  @override
  Widget build(BuildContext context) {
    final items = widget.arguments.children;
    return KoyalScaffold(
      appBar: KoyalAppBar(),
      body: GridView.count(
        crossAxisCount: 4,
        children: items,
      ),
    );
  }
}
