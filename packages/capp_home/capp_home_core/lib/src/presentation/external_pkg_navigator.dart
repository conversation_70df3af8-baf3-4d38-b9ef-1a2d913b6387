import 'package:capp_content/capp_content.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared/koyal_shared.dart';

class OtpExternalPkgNavigator implements IOtpExternalPkgNavigator {
  @override
  Future toContactUsScreen(BuildContext context) => context.navigator.pushReplacementTyped(
        package: CappContent,
        screen: context.isFlagEnabledRead(FeatureFlag.dynamicContactUsPage) ? ContactUsScreen : ObsoleteContactUsScreen,
      );
}
