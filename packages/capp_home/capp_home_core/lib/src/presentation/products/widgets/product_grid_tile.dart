import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class ProductGridTile extends StatelessWidget {
  final String title;
  final String iconUrl;
  final VoidCallback onTapped;
  const ProductGridTile({
    super.key,
    required this.title,
    required this.iconUrl,
    required this.onTapped,
  });

  @override
  Widget build(BuildContext context) {
    return KoyalElevation.koyal3Dp(
      child: GestureDetector(
        onTap: onTapped,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: HciColors.supplementary0,
          ),
          child: KoyalPadding.normalHorizontal(
            child: Row(
              children: [
                Container(
                  constraints: const BoxConstraints(
                    maxWidth: 32,
                    maxHeight: 32,
                  ),
                  child: iconUrl.asImage(
                    width: 32,
                    height: 32,
                    fit: BoxFit.scaleDown,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 85,
                  child: KoyalText.caption1(title),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
