import 'dart:async';

import 'package:capp_products_core/capp_products_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';

class ProductsAndServicesGridScreen extends StatelessWidget {
  final ProductsAndServicesScreenArguments arguments;
  const ProductsAndServicesGridScreen({
    super.key,
    required this.arguments,
  });

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      appBar: KoyalAppBar(
        title: arguments.title,
      ),
      body: GridView.count(
        crossAxisCount: 2,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 2.8,
        padding: const EdgeInsets.all(KoyalPadding.paddingNormal),
        children: _buildGridTiles(context, arguments),
      ),
    );
  }

  List<Widget> _buildGridTiles(BuildContext context, ProductsAndServicesScreenArguments arguments) {
    final tiles = <Widget>[];
    for (final item in arguments.products) {
      tiles.add(
        ProductGridTile(
          title: item.title ?? '',
          iconUrl: item.iconUrl ?? '',
          onTapped: () async {
            unawaited(
              context.get<CappTrackingService>().trackEvent(
                event: KoyalEvent.homeDashboardProductsAndServicesClick,
                eventCategory: KoyalTrackingCategories.homeDashboard,
                eventAction: KoyalAnalyticsConstants.click,
                eventLabel: KoyalTrackingLabels.productsServices,
                customDimensions: {
                  TrackingProperties.propertyPropDynamicV1: item.trackingLabel ?? 'empty',
                },
              ),
            );
            if (item.isNativeView) {
              await context.navigator.pushFromPackage(
                package: 'CappProducts',
                screen: 'ProductsScreenCore',
                arguments: ProductsScreenCoreArguments(
                  productId: item.id ?? '',
                  title: item.title ?? '',
                ),
              );
            } else {
              await context.navigator.pushFromPackage(
                package: 'CappContent',
                screen: 'WebViewScreen',
                arguments: WebViewArguments(
                  url: item.actionUrl ?? '',
                ),
              );
            }
          },
        ),
      );
    }
    return tiles;
  }
}
