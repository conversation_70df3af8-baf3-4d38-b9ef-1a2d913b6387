import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_home_core.dart';

class RootDetectionScreen extends StatefulWidget {
  const RootDetectionScreen({Key? key}) : super(key: key);

  @override
  RootDetectionScreenState createState() => RootDetectionScreenState();
}

class RootDetectionScreenState extends State<RootDetectionScreen> {
  @override
  Widget build(BuildContext context) {
    final isRooted = context.get<IPlatformService>().isDeviceRooted;
    return KoyalScaffold(
      appBar: KoyalAppBar(
        title: 'Root Detection Demo Page',
      ),
      body: Center(
        child: SizedBox(
          height: 150,
          width: 300,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              KoyalText.body2(
                color: ColorTheme.of(context).defaultTextColor,
                ' Device is${isRooted ? '' : "n't"} Rooted!',
              ),
              KoyalCheckbox(
                value: isRooted,
              ),
              const Spacer(),
              MaterialButton(
                color: Colors.yellow,
                onPressed: () {
                  RootDetectionDialog.display(context);
                },
                child: KoyalText.body2(color: ColorTheme.of(context).defaultTextColor, 'Show root check dialog'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
