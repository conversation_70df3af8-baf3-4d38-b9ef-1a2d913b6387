import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import 'widgets/root_detection_content.dart';

// ignore: avoid_classes_with_only_static_members
class RootDetectionDialog {
  static void display(BuildContext context) {
    showBlockingDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const RootDetectionContent(),
    );
  }
}
