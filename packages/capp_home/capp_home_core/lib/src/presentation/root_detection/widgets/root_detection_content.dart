import 'dart:io';

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class RootDetectionContent extends StatelessWidget {
  const RootDetectionContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      actions: [
        // TODO(Unknown): Please check if we can wrap PrimaryButton by VerticalButtonsLayout/HorizontalButtonsLayout here
        //ignore: buttons-layout
        PrimaryButton(
          text: 'Okay',
          onPressed: () => Platform.isIOS ? exit(0) : SystemNavigator.pop(),
        ),
      ],
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          const KoyalPadding.large(
            top: false,
            left: false,
            right: false,
            child: Icon(
              KoyalIcons.alert_circle_outline,
              color: Colors.yellow,
              size: 68.0,
            ),
          ),
          KoyalText.header4(
            color: ColorTheme.of(context).defaultTextColor,
            'Device is rooted!',
          ),
          KoyalText.body2(
            color: ColorTheme.of(context).defaultTextColor,
            'Some Functionalities may not work for you.',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
