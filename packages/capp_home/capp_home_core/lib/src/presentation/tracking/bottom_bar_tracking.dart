import 'dart:async';

import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

mixin BottomBarTrackingMixin {
  void trackNavigationQrClick(BuildContext context) => trackNavigationItemClick(context, 'QR');

  void trackSupercupoClick(BuildContext context) => trackNavigationItemClick(context, 'Supercupo');

  // NAV003
  void trackNavigationItemClick(BuildContext context, String? navItemTrackingLabel) {
    unawaited(
      context.get<CappTrackingService>().trackEvent(
        event: KoyalEvent.homeMainNavigationItemClick,
        eventCategory: KoyalTrackingCategories.bottomNavigation,
        eventAction: KoyalAnalyticsConstants.click,
        eventLabel: KoyalTrackingLabels.homeBottomNavigationBar,
        customDimensions: {
          TrackingProperties.propertyCdItemName: navItemTrackingLabel ?? '',
        },
      ),
    );
  }
}
