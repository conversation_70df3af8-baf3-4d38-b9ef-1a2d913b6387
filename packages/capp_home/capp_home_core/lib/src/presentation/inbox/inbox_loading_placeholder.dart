import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class InboxLoadingPlaceholder extends StatelessWidget {
  final int itemCount;
  const InboxLoadingPlaceholder({
    super.key,
    this.itemCount = 10,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: itemCount,
      separatorBuilder: (context, index) => const ListDivider(),
      itemBuilder: (context, _) {
        return const InboxLoadingPlaceholderItem();
      },
    );
  }
}

class InboxLoadingPlaceholderItem extends StatelessWidget {
  const InboxLoadingPlaceholderItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorTheme.of(context).backgroundColor,
      padding: const EdgeInsets.all(KoyalPadding.paddingNormal),
      child: KoyalShimmer(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 32,
              width: 32,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
            const SizedBox(width: KoyalPadding.paddingNormal),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  KoyalPadding.smallVertical(
                    child: Container(
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                  Container(
                    width: 200,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
