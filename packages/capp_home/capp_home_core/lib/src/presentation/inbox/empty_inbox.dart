import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../capp_home_core.dart';

class EmptyInbox extends StatelessWidget {
  const EmptyInbox({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Center(
        // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
        //ignore: no-direct-padding
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: KoyalPadding.paddingLarge),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Spacer(),
              SvgPicture.asset(
                'assets/svg/mascot/mascot_search_fail.svg',
                width: 128,
                height: 128,
                package: 'capp_ui',
                fit: BoxFit.fitHeight,
              ),
              const SizedBox(height: KoyalPadding.paddingNormal),
              KoyalText.header6(
                color: ColorTheme.of(context).defaultTextColor,
                L10nCappHome.of(context).inboxEmpty,
                textAlign: TextAlign.center,
              ),
              KoyalText.body2(
                color: ColorTheme.of(context).defaultTextColor,
                L10nCappHome.of(context).inboxEmptySubtitle,
                textAlign: TextAlign.center,
              ),
              const Spacer(flex: 2),
            ],
          ),
        ),
      );
}
