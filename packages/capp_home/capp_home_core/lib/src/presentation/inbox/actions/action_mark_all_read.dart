import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_home_core.dart';

class ActionMarkAllRead extends StatelessWidget {
  const ActionMarkAllRead({super.key});

  @override
  Widget build(BuildContext context) {
    return KoyalIconButton(
      iconData: KoyalIcons.mail_open_outline,
      onPressed: () => _showMarkAllReadDialog(context),
    );
  }

  Future<void> _showMarkAllReadDialog(BuildContext context) async {
    return showKoyalOverlay<void>(
      context,
      title: L10nCappHome.of(context).inboxMarkAllReadDialogTitle,
      body: KoyalText.body2(
        L10nCappHome.of(context).inboxMarkAllReadDialogMarkAllQuestion,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (context) => PrimaryButton(
        text: L10nCappHome.of(context).inboxMarkAllReadDialogYes,
        onPressed: () {
          context.get<InboxBloc>().add(const InboxEvent.markAllRead());
          context.get<UnreadBloc>().add(const UnreadEvent.reload());
          context.navigator.pop();
        },
      ),
      tertiaryButtonBuilder: (context) => TertiaryButton(
        text: L10nCappHome.of(context).inboxMarkAllReadDialogNo,
        onPressed: () {
          context.navigator.pop();
        },
      ),
    );
  }
}
