import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_home_core.dart';
import '../../domain/constants.dart';
import 'badge.dart' as badge;
import 'inbox_content_wrapper.dart';
import 'inbox_tab_content.dart';
import 'inbox_tabs_loading.dart';

class InboxScreen extends StatelessWidget with RouteWrapper {
  final InboxScreenArguments arguments;

  const InboxScreen({required this.arguments, Key? key}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) =>
      BlocProvider.value(value: context.get<InboxBloc>()..add(const InboxEvent.init()), child: this);

  @override
  Widget build(BuildContext context) => KoyalScaffold(
    body: BlocBuilder<InboxBloc, InboxState>(
          builder: (context, state) => AdvancedRetryContainer(
            screenName: 'inbox_screen',
            isError: state.isError && !state.isLoading,
            isLoading: state.isError && state.isLoading,
            onRetry: () => context.get<InboxBloc>().add(const InboxEvent.init()),
            child: state.categories == null
                ? InboxTabsLoading(hideLeading: arguments.isTabView)
                : _Content(categories: state.categories!, hideLeading: arguments.isTabView),
          ),
        ),
  );
}

class _Content extends StatefulWidget {
  final List<EnumerationItem> categories;
  final bool hideLeading;

  const _Content({Key? key, required this.categories, required this.hideLeading}) : super(key: key);

  @override
  State<_Content> createState() => _ContentState();
}

class _ContentState extends State<_Content> with TickerProviderStateMixin {
  late TabController _controller;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _controller = TabController(
      length: widget.categories.length,
      vsync: this,
      animationDuration: Duration.zero,
    );
    _controller.addListener(
      () {
        // Skip the listener during ongoing tab changes to prevent duplicate triggers
        if (_controller.indexIsChanging) return;

        setState(
          () {
            _selectedIndex = _controller.index;
            final newMessageCategory = widget.categories[_selectedIndex];
            context.get<InboxBloc>().add(InboxEvent.switchFilterChange(category: newMessageCategory));

            context.get<CappTrackingService>().trackEvent(
              event: KoyalEvent.inboxScreenMenuItemClick,
              eventCategory: KoyalTrackingCategories.inboxScreen,
              eventAction: KoyalAnalyticsConstants.click,
              eventLabel: 'inbox_menu',
              customDimensions: {
                TrackingProperties.propertyCdItemName: '${newMessageCategory.key}',
              },
            );
          },
        );
      },
    );

    context.get<UnreadBloc>().add(const UnreadEvent.reload());
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => BlocBuilder<InboxBloc, InboxState>(
        builder: (context, state) => InboxContentWrapper(
          appbarBottom: Column(
            children: [
              Container(color: ColorTheme.of(context).foreground5Color, height: 1),
              TabBar(
                indicatorColor: ColorTheme.of(context).primaryColor,
                indicatorSize: TabBarIndicatorSize.tab,
                padding: const EdgeInsets.symmetric(horizontal: KoyalPadding.paddingSmall),
                tabAlignment: TabAlignment.start,
                isScrollable: true,
                controller: _controller,
                dividerHeight: 0,
                tabs: widget.categories.map((category) => _buildTab(state, category)).toList(),
              ),
            ],
          ),
          hideLeading: widget.hideLeading,
          body: TabBarView(
            physics: const NeverScrollableScrollPhysics(),
            controller: _controller,
            children: List.generate(widget.categories.length, (i) => const InboxTabContent()),
          ),
        ),
      );

  Widget _buildTab(InboxState state, EnumerationItem category) {
    final isSelected = _selectedIndex == widget.categories.indexOf(category);
    final unreadCount = state.unreadCount?[category] ?? 0;
    final title = category.id == allCategoryId ? L10nCappHome.of(context).messageCategoryAll : category.title;

    return Tab(
      key: Key('__inboxTab_${category.key}'),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          KoyalPadding.smallHorizontal(
            key: Key('__inboxTab_${category.key}_title_${isSelected ? 'selected' : ''}'),
            child: isSelected
                ? KoyalText.subtitle2(color: ColorTheme.of(context).defaultTextColor, title)
                : KoyalText.subtitle2(color: ColorTheme.of(context).secondaryTextColor, title),
          ),
          if (unreadCount > 0)
            badge.Badge(
              text: unreadCount < NotificationConst.unreadLimitCount
                  ? unreadCount.toString()
                  : NotificationConst.unreadLimitCount.toString(),
            ),
        ],
      ),
    );
  }
}
