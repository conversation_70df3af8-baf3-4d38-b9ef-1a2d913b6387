import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';
import 'package:flutter_dynamic_forms_components/flutter_dynamic_forms_components.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import '../../services/dynamic_forms/widgets/form_author/form_author_renderer.dart';
import '../../services/dynamic_forms/widgets/form_header_title/form_header_title_renderer.dart';
import '../../services/dynamic_forms/widgets/form_image/form_image_renderer.dart';
import '../../services/dynamic_forms/widgets/form_text/form_text_renderer.dart';
import '../../services/dynamic_forms/widgets/redirect_to_browser_button/redirect_to_browser_button_renderer.dart';

class MessageDetailScreen extends StatefulWidget with RouteWrapper {
  final ScreenArguments arguments;
  MessageDetailScreen({Key? key, required this.arguments}) : super(key: key);
  @override
  Widget wrappedRoute(BuildContext context) => FormProvider.value(
        value: context.get<MessageDetailFormManager>(),
        child: BlocProvider(create: (context) => context.get<MessageDetailBloc>(), child: this),
      );

  @override
  MessageDetailScreenState createState() => MessageDetailScreenState();

  List<FormElementRenderer> getInboxRenderers(BoxConstraints constraints) {
    return [
      FormAuthorRenderer(),
      FormImageRenderer(),
      FormTextRenderer(),
      FormHeaderTitleRenderer(),
      FormVideoPlayerRenderer(),
      RedirectToBrowserButtonRenderer(),
      ...getRenderers(),
    ];
  }
}

class MessageDetailScreenState extends State<MessageDetailScreen> {
  MessageDetailBloc? _bloc;
  bool _viewEventTracked = false;

  @override
  void initState() {
    _bloc = BlocProvider.of<MessageDetailBloc>(context);
    _bloc!.add(MessageDetailEvent.init(messageId: _messageId, context: context));
    if (widget.arguments is MessageDetailArguments) {
      final args = widget.arguments as MessageDetailArguments;
      if (args.message.dateRead == null) {
        context.get<InboxBloc>().add(InboxEvent.markRead(messageId: _messageId));
        context.get<UnreadBloc>().add(const UnreadEvent.reload());
      }
    } else {
      context.get<InboxBloc>().add(InboxEvent.markRead(messageId: _messageId));
    }

    super.initState();
  }

  void _trackViewEvent(MessageDetailState state) {
    if (!state.isLoading && !_viewEventTracked) {
      _viewEventTracked = true;
      context.get<CappTrackingService>().trackInboxEvent(
        event: KoyalEvent.inboxMessageScreenView,
        eventCategory: KoyalTrackingCategories.screenView,
        eventAction: KoyalAnalyticsConstants.view,
        eventLabel: 'message_detail',
        externalMessageId: _externalMessageId,
        customDimensions: {
          TrackingProperties.propertyCdItemId: _messageId,
        },
      );
    }
  }

  String get _messageId {
    var messageId = '';
    if (widget.arguments is MessageDetailArguments) {
      final args = widget.arguments as MessageDetailArguments;
      messageId = args.message.id;
    } else if (widget.arguments is NotificationMessageScreenArguments) {
      final args = widget.arguments as NotificationMessageScreenArguments;
      messageId = args.notification.data!.inboxItemId!;
    }
    return messageId;
  }

  String? get _externalMessageId {
    String? extMessageId;
    if (widget.arguments is MessageDetailArguments) {
      final args = widget.arguments as MessageDetailArguments;
      extMessageId = args.message.externalMessageId;
    } else if (widget.arguments is NotificationMessageScreenArguments) {
      final args = widget.arguments as NotificationMessageScreenArguments;
      extMessageId = args.notification.id;
    }
    try {
      final result = extMessageId ?? context.get<MessageDetailFormManager>().form.externalMessageId;
      return result;
    } catch (e) {
      return null;
    }
  }

  void _dispatchFormEvents(FormElementEvent event, BuildContext context) {
    if (event is ChangeValueEvent) {
      if (event.propertyName == FormTextRenderer.clickEventProperty ||
          event.propertyName == FormVideoPlayerRenderer.clickEventProperty) {
        final href = context.get<CappTrackingService>().getFormattedAnalyticsText(event.value.toString());
        context.get<CappTrackingService>().trackInboxEvent(
          event: KoyalEvent.inboxMessageScreenLinkClick,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: 'link_clicked',
          externalMessageId: _externalMessageId,
          eventCategory: KoyalTrackingCategories.messageDetail,
          customDimensions: {
            TrackingProperties.propertyCdItemId: _messageId,
            TrackingProperties.propertyCdUrl: href,
            TrackingProperties.propertyCdItemName: '${event.propertyName}',
          },
        );
      } else {
        BlocProvider.of<MessageDetailBloc>(context).add(
          MessageDetailEvent.changeValue(
            elementId: event.elementId,
            propertyName: event.propertyName!,
            value: event.value,
            ignoreLastChange: event.ignoreLastChange,
          ),
        );
      }
    }
  }

  Future<bool?> showConfirmDeleteDialog(BuildContext context) async {
    return showKoyalOverlay<bool>(
      context,
      key: const Key('__inboxDetailConfirmDeleteDialog__'),
      title: L10nCappHome.of(context).inboxConfirmDelete,
      tertiaryButtonBuilder: (c) => TertiaryButton(
        key: const Key('__inboxDetailDeleteNoButton__'),
        text: L10nCappHome.of(context).no,
        onPressed: () => c.navigator.pop(false),
      ),
      primaryButtonBuilder: (c) => PrimaryButton(
        key: const Key('__inboxDetailDeleteYesButton__'),
        text: L10nCappHome.of(context).yes,
        onPressed: () => c.navigator.pop(true),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      appBar: KoyalAppBar(
        title: L10nCappHome.of(context).messageDetail,
        actions: [
          KoyalIconButton(
            iconData: KoyalIcons.trash_outline,
            onPressed: () async {
              final confirmDelete = await showConfirmDeleteDialog(context);
              if (confirmDelete ?? false) {
                _deleteMessage();
              }
            },
          ),
        ],
      ),
      body: BlocBuilder<MessageDetailBloc, MessageDetailState>(
        bloc: _bloc,
        builder: (context, state) {
          _trackViewEvent(state);
          return ProgressContainer(
            isLoading: state.isLoading,
            showChildWhileLoading: false,
            child: LayoutBuilder(
              builder: (context, constraints) => SingleChildScrollView(
                child: state.isLoading
                    ? const SizedBox.shrink()
                    : (state.status == MessageDetailStatus.error)
                        ? GenericError(
                            errorId: 'message_detail_screen',
                            primaryButtonText: L10nCappUi.of(context).tryAgain,
                            primaryButtonOnClick: () => context.navigator.toMainScreen(),
                          )
                        : FormRenderer<MessageDetailFormManager>(
                            dispatcher: (e) => _dispatchFormEvents(e, context),
                            renderers: widget.getInboxRenderers(constraints),
                          ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _deleteMessage() {
    context.get<CappTrackingService>().trackInboxEvent(
      event: KoyalEvent.inboxMessageScreenDeleteClick,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'message_deletion',
      externalMessageId: _externalMessageId,
      eventCategory: KoyalTrackingCategories.messageDetail,
      customDimensions: {
        TrackingProperties.propertyCdItemId: _messageId,
      },
    );
    context.get<InboxBloc>().add(InboxEvent.markArchived(messageId: _messageId));
    context.get<UnreadBloc>().add(const UnreadEvent.reload());
    KoyalSnackBar.show(
      context: context,
      hasBottomButton: false,
      message: L10nCappHome.of(context).messageDeleted,
    );

    if (context.navigator.canPop() ?? false) {
      context.navigator.pop();
    } else {
      context.navigator.toMainScreen();
    }
  }
}
