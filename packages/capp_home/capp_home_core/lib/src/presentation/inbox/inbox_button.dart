import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';

class InboxButton extends StatelessWidget {
  final Color iconColor;
  const InboxButton({
    Key? key,
    required this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => FeatureFlagWidget(
        featureName: FeatureFlag.inbox,
        child: BlocBuilder<UnreadBloc, UnreadState>(
          buildWhen: (previous, current) => previous.hasUnread != current.hasUnread,
          builder: (context, state) => IconButton(
            icon: Icon(
              state.hasUnread ? KoyalIcons.notifications_new_outline : KoyalIcons.notifications_outline,
              color: iconColor,
            ),
            onPressed: () {
              context.get<CappTrackingService>().trackAnalyticsEvent(
                    eventCategory: 'usr_act_top_bar',
                    eventAction: KoyalAnalyticsConstants.click,
                    eventLabel: state.hasUnread ? 'inbox_new_message' : 'ibx_nonew_msg',
                  );
              context.get<CappTrackingService>().trackEvent(
                    event: KoyalEvent.homeDashboardInboxClick,
                    eventCategory: KoyalTrackingCategories.homeDashboard,
                    eventAction: KoyalAnalyticsConstants.click,
                    eventLabel: KoyalTrackingLabels.inbox,
                  );
              context.navigator.pushFromPackage(
                package: 'CappHomeCore',
                screen: 'InboxScreen',
                arguments: InboxScreenArguments(InboxMessageType.all),
              );
              context.read<UnreadBloc>().add(const UnreadEvent.reload());
            },
          ),
        ),
      );
}
