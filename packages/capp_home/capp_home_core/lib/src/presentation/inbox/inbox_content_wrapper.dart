import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import 'actions/action_mark_all_read.dart';

const double _bottomHeight = 50;

class InboxContentWrapper extends StatelessWidget {
  final Widget appbarBottom;
  final Widget body;
  final bool hideLeading;

  const InboxContentWrapper({Key? key, required this.appbarBottom, required this.body, required this.hideLeading})
      : super(key: key);

  @override
  Widget build(BuildContext context) => KoyalScaffold(
        key: const Key('__inboxScreen__'),
        appBar: KoyalAppBar(
          bottomHeight: _bottomHeight,
          barHeight: kToolbarHeight + _bottomHeight,
          title: L10nCappHome.of(context).cappMessagingInbox,
          key: const Key('__inboxTitle__'),
          leading: hideLeading ? KoyalAppBarLeading.none : KoyalAppBarLeading.goBack,
          actions: const [
            ActionMarkAllRead(),
          ],
          onGoBack: () {
            context.get<CappTrackingService>().trackEvent(
                  event: KoyalEvent.inboxScreenGoBackClick,
                  eventCategory: KoyalTrackingCategories.inboxScreen,
                  eventAction: KoyalAnalyticsConstants.click,
                  eventLabel: 'inbox_screen_go_back',
                );
            ScaffoldMessenger.of(context).clearSnackBars();
            context.navigator.maybePop();
          },
          bottom: appbarBottom,
        ),
        body: BlocListener<InboxBloc, InboxState>(
          listenWhen: (previous, current) => previous.markAllReadCount != current.markAllReadCount,
          listener: (context, state) {
            if (state.markAllReadCount != null) {
              KoyalSnackBar.show(
                context: context,
                message: L10nCappHome.of(context).inboxMarkAllReadSnackMarkedMessages(state.markAllReadCount),
                hasBottomButton: false,
              );
            }
          },
          child: body,
        ),
      );
}
