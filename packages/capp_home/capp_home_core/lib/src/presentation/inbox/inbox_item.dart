import 'dart:async';

import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';

class InboxItem extends StatelessWidget {
  final InboxMessage message;
  final VoidCallback onTap;
  final void Function() onUndoPressed;
  final int index;
  final String? imageCategoryUrl;

  const InboxItem({
    Key? key,
    required this.message,
    required this.onTap,
    required this.onUndoPressed,
    required this.index,
    required this.imageCategoryUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => message.archived
      ? _MessageTile(message: message, onTap: onTap, imageCategoryUrl: imageCategoryUrl)
      : Dismissible(
          key: Unique<PERSON><PERSON>(),
          confirmDismiss: (direction) => _unreadMessage(context, direction, index, message),
          onDismissed: (direction) => _deleteMessage(context, direction, index, message),
          direction: message.isNew ? DismissDirection.endToStart : DismissDirection.horizontal,
          background: _DismissibleWrapper(
            backgroundColor: ColorTheme.of(context).infoIndicatorColor,
            alignment: MainAxisAlignment.start,
            children: [
              Icon(KoyalIcons.notifications_new_outline, color: ColorTheme.of(context).backgroundColor, size: 24),
              const SizedBox(width: 8),
              KoyalText.caption2(color: ColorTheme.of(context).whiteTextColor, L10nCappHome.of(context).unread),
            ],
          ),
          secondaryBackground: _DismissibleWrapper(
            backgroundColor: ColorTheme.of(context).primaryColor,
            alignment: MainAxisAlignment.end,
            children: [
              KoyalText.caption2(color: ColorTheme.of(context).whiteTextColor, L10nCappHome.of(context).delete),
              const SizedBox(width: 8),
              Icon(KoyalIcons.trash_outline, color: ColorTheme.of(context).backgroundColor, size: 24),
            ],
          ),
          child: _MessageTile(message: message, onTap: onTap, imageCategoryUrl: imageCategoryUrl),
        );

  Future<bool> _unreadMessage(BuildContext context, DismissDirection direction, int index, InboxMessage message) async {
    if (direction == DismissDirection.startToEnd) {
      unawaited(
        context.get<CappTrackingService>().trackInboxEvent(
          event: KoyalEvent.inboxScreenUnreadSwipe,
          eventAction: KoyalAnalyticsConstants.swipe,
          eventLabel: 'inbox_unread',
          externalMessageId: message.externalMessageId,
          customDimensions: {
            TrackingProperties.propertyCdItemId: message.id,
            TrackingProperties.propertyCdItemIndex: '$index',
          },
        ),
      );

      final result = await Future<bool>.delayed(const Duration(milliseconds: 300), () {
        if (!context.mounted) return false;
        context.get<InboxBloc>().add(InboxEvent.markUnread(messageId: message.id));
        if (!context.mounted) return false;
        context.get<UnreadBloc>().add(const UnreadEvent.reload());
        return false;
      });

      return result;
    }
    return Future<bool>(() => true);
  }

  void _deleteMessage(BuildContext context, DismissDirection direction, int index, InboxMessage message) {
    if (direction == DismissDirection.endToStart) {
      context.get<CappTrackingService>().trackInboxEvent(
        event: KoyalEvent.inboxScreenDeleteSwipe,
        eventAction: KoyalAnalyticsConstants.swipe,
        eventLabel: 'inbox_delete',
        externalMessageId: message.externalMessageId,
        customDimensions: {
          TrackingProperties.propertyCdItemId: message.id,
          TrackingProperties.propertyCdItemIndex: '$index',
        },
      );

      context.get<InboxBloc>().add(InboxEvent.markArchived(messageId: message.id));
      context.get<UnreadBloc>().add(const UnreadEvent.reload());

      KoyalSnackBar.show(
        context: context,
        hasBottomButton: false,
        message: L10nCappHome.of(context).messageDeleted,
        action: onUndoPressed,
        actionLabel: L10nCappHome.of(context).undo,
        actionKey: const Key('__undoButton__'),
      );
    }
  }
}

class _MessageTile extends StatelessWidget {
  final InboxMessage message;
  final VoidCallback onTap;
  final String? imageCategoryUrl;

  const _MessageTile({
    Key? key,
    required this.message,
    required this.onTap,
    required this.imageCategoryUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => ListTile(
        dense: true,
        minVerticalPadding: 12,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12),
        leading: _Image(imageUrl: message.imageUrl, imageCategoryUrl: imageCategoryUrl),
        title: Row(
          children: [
            Expanded(
              child: message.isNew
                  ? KoyalText.subtitle2(
                      color: ColorTheme.of(context).defaultTextColor,
                      message.title,
                      overflow: TextOverflow.ellipsis,
                    )
                  : KoyalText.body1(
                      color: ColorTheme.of(context).defaultTextColor,
                      message.title,
                      overflow: TextOverflow.ellipsis,
                    ),
            ),
            if (message.isNew)
              Container(
                key: Key('__unreadIndicator_${message.id}__'),
                width: 8,
                height: 8,
                decoration: BoxDecoration(color: ColorTheme.of(context).primaryColor, shape: BoxShape.circle),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (message.textPreview != null)
              Row(
                children: [
                  Expanded(
                    child: message.isNew
                        ? KoyalText.body3(
                            color: ColorTheme.of(context).defaultTextColor,
                            message.textPreview!,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          )
                        : KoyalText.body2(
                            color: ColorTheme.of(context).secondaryTextColor,
                            message.textPreview!,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                  ),
                ],
              ),
            const SizedBox(height: 8),
            KoyalText.caption2(
              color: ColorTheme.of(context).secondaryTextColor,
              message.dataReceived.getPassedTimeText(context),
            ),
          ],
        ),
        onTap: onTap,
      );
}

class _DismissibleWrapper extends StatelessWidget {
  final Color backgroundColor;
  final MainAxisAlignment alignment;
  final List<Widget> children;

  const _DismissibleWrapper({Key? key, required this.backgroundColor, required this.alignment, required this.children})
      : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
        decoration: BoxDecoration(color: backgroundColor),
        child: KoyalPadding.normalHorizontal(child: Row(mainAxisAlignment: alignment, children: children)),
      );
}

class _Image extends StatelessWidget {
  final String? imageUrl;
  final String? imageCategoryUrl;

  const _Image({Key? key, required this.imageUrl, required this.imageCategoryUrl}) : super(key: key);

  Widget _buildImage(BuildContext context) {
    final errorImage = SvgPicture.asset(
      'assets/icons/other.svg',
      package: 'capp_home_core',
      height: _imageSize,
      width: _imageSize,
    );
    try {
      if (imageUrl != null) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: imageUrl.asImage(
            showShimmer: true,
            height: _imageSize,
            width: _imageSize,
          ),
        );
      } else if (imageCategoryUrl != null) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: imageCategoryUrl.asImage(
            showShimmer: true,
            height: _imageSize,
            width: _imageSize,
          ),
        );
      } else {
        return errorImage;
      }
    } catch (e) {
      return errorImage;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(constraints: const BoxConstraints(maxWidth: 150), child: _buildImage(context));
  }
}

extension on InboxMessage {
  bool get isNew => dateRead == null;
}

const double _imageSize = 40;
