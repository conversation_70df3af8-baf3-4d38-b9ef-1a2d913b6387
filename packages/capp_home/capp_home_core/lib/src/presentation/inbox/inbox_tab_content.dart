import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import 'inbox_loading_placeholder.dart';

class InboxTabContent extends StatefulWidget {
  const InboxTabContent({Key? key}) : super(key: key);

  @override
  State<InboxTabContent> createState() => _InboxTabContentState();
}

class _InboxTabContentState extends State<InboxTabContent> {
  final _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) => BlocBuilder<InboxBloc, InboxState>(
        builder: (context, state) => NotificationListener<ScrollNotification>(
          onNotification: (notification) => _handleScrollNotification(
            context,
            notification,
            state.hasReachedEnd,
          ),
          child: PullToRefresh(
            isLoading: state.isLoading,
            onRefreshStarted: () {
              context.get<InboxBloc>().add(const InboxEvent.refresh());
              context.get<UnreadBloc>().add(const UnreadEvent.reload());
            } ,
            child: AdvancedRetryContainer(
              screenName: 'inbox_screen',
              isError: state.isError && !state.isLoading,
              isLoading: state.isError && state.isLoading,
              onRetry: () => context.get<InboxBloc>().add(const InboxEvent.loadUnreadCount()),
              child: _getWidgetByState(state),
            ),
          ),
        ),
      );

  bool _handleScrollNotification(
    BuildContext context,
    ScrollNotification notification,
    bool hasReachedEnd,
  ) {
    if (notification is ScrollEndNotification && _scrollController.position.extentAfter == 0 && !hasReachedEnd) {
      context.get<InboxBloc>().add(const InboxEvent.loadNext());
    }
    return false;
  }

  Widget _getWidgetByState(InboxState state) {
    if (state.isInitialLoading) {
      return const _ProgressIndicator();
    } else {
      if (state.messages.isEmpty && !state.isLoading) {
        ScaffoldMessenger.of(context).clearSnackBars();
        return const EmptyInbox();
      } else {
        return _ListInboxContent(
          state: state,
          scrollController: _scrollController,
        );
      }
    }
  }
}

class _ProgressIndicator extends StatelessWidget {
  const _ProgressIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => const InboxLoadingPlaceholder();
}

class _ListInboxContent extends StatelessWidget {
  final InboxState state;
  final ScrollController scrollController;

  const _ListInboxContent({
    Key? key,
    required this.state,
    required this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      key: const Key('__inbox_messages__'),
      padding: const EdgeInsets.symmetric(vertical: 4),
      itemCount: state.hasReachedEnd || state.isError || state.messages.length < 10
          ? state.messages.length
          : state.messages.length + 1,
      //loading widget
      itemBuilder: (context, index) {
        if (index >= state.messages.length) {
          return const InboxLoadingPlaceholderItem();
        } else {
          final message = state.messages[index];
          final imageCategoryUrl = state.categories?.firstWhereOrNull((c) => c.id == message.categoryId)?.imageUrl;

          return index == 0 // we need to start with a separator, which normally sit between the items only
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTextDivider(
                      message.dataReceived.getPassedTimeSectionHeader(context),
                    ),
                    InboxItem(
                      key: Key('__inboxItem_${message.id}__'),
                      message: message,
                      onUndoPressed: () => _onUndoPressed(message, index, context),
                      onTap: () => _onInboxItemTap(message, index, context),
                      index: index,
                      imageCategoryUrl: imageCategoryUrl,
                    ),
                  ],
                )
              : InboxItem(
                  key: Key('__inboxItem_${message.id}__'),
                  message: message,
                  onUndoPressed: () => _onUndoPressed(message, index, context),
                  onTap: () => _onInboxItemTap(message, index, context),
                  index: index,
                  imageCategoryUrl: imageCategoryUrl,
                );
        }
      },
      separatorBuilder: (context, index) {
        final dates = _generateDateSeparators(
          context.get<InboxBloc>().state.messages,
          context,
        );
        return ListTextDivider(dates.containsKey(index) ? dates[index]! : null);
      },
      controller: scrollController,
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
    );
  }

  Map<int, String> _generateDateSeparators(
    List<InboxMessage> messages,
    BuildContext context,
  ) {
    final result = <int, String>{};
    var lastSeparator = '';

    messages.forEachIndexed((i, message) {
      final separator = message.dataReceived.getPassedTimeSectionHeader(context);
      if (separator != lastSeparator) {
        lastSeparator = separator;
        if (i > 0) {
          // omit first message - treated separately
          result[i - 1] = lastSeparator;
        }
      }
    });

    return result;
  }

  void _onUndoPressed(InboxMessage message, int index, BuildContext context) {
    context.get<CappTrackingService>().trackInboxEvent(
      event: KoyalEvent.inboxScreenUndeleteClick,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'undo_deletion',
      externalMessageId: message.externalMessageId,
      customDimensions: {
        TrackingProperties.propertyCdItemId: message.id,
        TrackingProperties.propertyCdItemIndex: '$index',
      },
    );
    context.get<InboxBloc>().add(InboxEvent.markUnarchived(messageId: message.id));
    context.get<UnreadBloc>().add(const UnreadEvent.reload());
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
  }

  void _onInboxItemTap(InboxMessage item, int index, BuildContext context) {
    context.get<CappTrackingService>().trackInboxEvent(
      event: KoyalEvent.inboxScreenMessageClick,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'inbox',
      externalMessageId: item.externalMessageId,
      customDimensions: {
        TrackingProperties.propertyCdItemId: '$item.id',
        TrackingProperties.propertyCdItemIndex: '$index',
      },
    );
    context.navigator.pushFromPackage(
      package: 'CappHome',
      screen: 'MessageDetailScreen',
      arguments: MessageDetailArguments(message: item),
    );
  }
}
