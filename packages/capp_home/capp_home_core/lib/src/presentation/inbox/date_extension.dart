import 'package:flutter/material.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_home_core.dart';

extension InboxDateTimeExtension on DateTime {
  String getPassedTimeSectionHeader(BuildContext context) {
    var result = '';
    var key = '';
    final now = DateTime.now().toUtc();
    final days = now.difference(this).inDays;
    if (now.day == day && now.month == month && now.year == year) {
      key = 'today';
    } else if (days < 7) {
      key = 'week_ago';
    } else if (days >= 7 && days < 14) {
      key = 'weeks_ago';
      result = '2';
    } else if (days >= 14 && days < 21) {
      key = 'weeks_ago';
      result = '3';
    } else if (days >= 21 && days < 31) {
      key = 'month_ago';
      result = '1';
    } else if (days >= 31 && days < 61) {
      key = 'months_ago';
      result = '2';
    } else if (days >= 61 && days < 366) {
      key = 'months_ago';
      result = '2+';
    } else if (days >= 366 && days < 731) {
      key = 'last_year';
    } else if (days >= 731) {
      key = 'years_ago';
      result = (days / 365).floor().toString();
    }

    if (key.isEmpty) {
      return ''; // return empty text for upcoming dates
    }
    final text = translateKey(key, context);
    return '$result $text'.trim();
  }

  String getPassedTimeText(BuildContext context) {
    final now = DateTime.now().toUtc();
    final daysDiff = DateTime(now.year, now.month, now.day).difference(DateTime(year, month, day)).inDays;
    if (daysDiff != 0) return DateTimeX(this).mediumDate();

    final minutes = now.difference(this).inMinutes;
    final hours = now.difference(this).inHours;

    var result = '';
    var key = '';
    if (minutes >= 0 && minutes < 60) {
      key = minutes > 1 ? 'minutes_ago' : 'minute_ago';
      if (minutes > 0) {
        result = minutes.toString();
      }
    } else if (hours > 0 && hours < 24) {
      key = hours > 1 ? 'hours_ago' : 'hour_ago';
      result = hours.toString();
    }
    if (key.isEmpty) return '';
    final text = translateKey(key, context);
    return '$result $text'.trim();
  }

  String translateKey(String key, BuildContext context) {
    if (key == 'minutes_ago') {
      return L10nCappHome.of(context).timePassedMinutesAgo;
    } else if (key == 'minute_ago') {
      return L10nCappHome.of(context).timePassedMinuteAgo;
    } else if (key == 'hours_ago') {
      return L10nCappHome.of(context).timePassedHoursAgo;
    } else if (key == 'hour_ago') {
      return L10nCappHome.of(context).timePassedHourAgo;
    } else if (key == 'days_ago') {
      return L10nCappHome.of(context).timePassedDaysAgo;
    } else if (key == 'day_ago') {
      return L10nCappHome.of(context).timePassedDayAgo;
    } else if (key == 'months_ago') {
      return L10nCappHome.of(context).timePassedMonthsAgo;
    } else if (key == 'month_ago') {
      return L10nCappHome.of(context).timePassedMonthAgo;
    } else if (key == 'years_ago') {
      return L10nCappHome.of(context).timePassedYearsAgo;
    } else if (key == 'year_ago') {
      return L10nCappHome.of(context).timePassedYearAgo;
    } else if (key == 'last_year') {
      return L10nCappHome.of(context).timePassedLastYear;
    } else if (key == 'today') {
      return L10nCappHome.of(context).timePassedToday;
    } else if (key == 'week_ago') {
      return L10nCappHome.of(context).timePassedWeekAgo;
    } else if (key == 'weeks_ago') {
      return L10nCappHome.of(context).timePassedWeeksAgo;
    }
    return '';
  }
}
