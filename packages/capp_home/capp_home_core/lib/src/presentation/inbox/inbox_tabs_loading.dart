import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import 'inbox_content_wrapper.dart';
import 'inbox_loading_placeholder.dart';

const int _numberOfLoadingTabs = 6;

class InboxTabsLoading extends StatelessWidget {
  final bool hideLeading;
  const InboxTabsLoading({Key? key, this.hideLeading = false}) : super(key: key);

  @override
  Widget build(BuildContext context) => DefaultTabController(
        length: _numberOfLoadingTabs,
        child: InboxContentWrapper(
          hideLeading: hideLeading,
          appbarBottom: Column(
            children: [
              Container(color: ColorTheme.of(context).foreground5Color, height: 1),
              TabBar(
                indicatorColor: ColorTheme.of(context).primaryColor,
                indicatorSize: TabBarIndicatorSize.tab,
                isScrollable: true,
                tabs: List.generate(
                  _numberOfLoadingTabs,
                  (i) => Tab(
                    child: Grey<PERSON>himmer(
                      child: Container(
                        decoration: BoxDecoration(
                          color: ColorTheme.of(context).backgroundColor,
                          borderRadius: const BorderRadius.all(
                            Radius.circular(KoyalRoundedCorner.buttonCorner),
                          ),
                        ),
                        height: 14,
                        width: 56,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: TabBarView(
            physics: const NeverScrollableScrollPhysics(),
            children: List.generate(_numberOfLoadingTabs, (i) => const InboxLoadingPlaceholder()),
          ),
        ),
      );
}
