import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class Badge extends StatelessWidget {
  const Badge({Key? key, this.text = ''}) : super(key: key);

  final String text;

  @override
  Widget build(BuildContext context) => Container(
        constraints: BoxConstraints(
          minWidth: text.isNotEmpty ? 16 : 8,
          maxWidth: text.isNotEmpty ? double.infinity : 8,
        ),
        height: text.isNotEmpty ? 16 : 8,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          color: ColorTheme.of(context).primaryColor,
        ),
        child: Center(
          child: text.isNotEmpty
              ? Baseline(
                  baseline: 10,
                  baselineType: TextBaseline.alphabetic,
                  child: KoyalPadding.xSmallHorizontal(
                    child: KoyalText.caption2(
                      color: ColorTheme.of(context).whiteTextColor,
                      text,
                      textAlign: TextAlign.center,
                    ),
                  ),
                )
              : const SizedBox(),
        ),
      );
}
