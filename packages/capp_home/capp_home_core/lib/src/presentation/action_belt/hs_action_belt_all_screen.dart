import 'package:capp_domain/capp_domain.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../capp_home_core.dart';
import '../../infrastructure/action_belt/shopingmall_extension.dart';

class HSActionBeltAllScreen extends StatefulWidget {
  final HSActionBeltAllScreenArguments arguments;
  const HSActionBeltAllScreen({
    super.key,
    required this.arguments,
  });

  @override
  State<HSActionBeltAllScreen> createState() => _HSActionBeltAllScreenState();
}

class _HSActionBeltAllScreenState extends State<HSActionBeltAllScreen> with GameDeeplinkResolverMixin {
  @override
  void initState() {
    super.initState();
    widget.arguments.onInitScreen?.call(context);
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - (2 * KoyalPadding.paddingNormal);
    // Calculate spacing that will evenly distribute items (3 spaces between 4 items)
    final itemWidth = widget.arguments.sizeParams.itemWidth;
    final spacing = (availableWidth - (4 * itemWidth)) / 3;

    return KoyalScaffold(
      appBar: KoyalAppBar(),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: KoyalPadding.paddingNormal,
            vertical: KoyalPadding.paddingSmall,
          ),
          child: Wrap(
            spacing: spacing,
            runSpacing: KoyalPadding.paddingSmall,
            children: widget.arguments.items.map((item) {
              return buildItem(item, context);
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget buildItem(UserActionBeltWidget item, BuildContext context) {
    return HSActionBeltItem(
      label: item.title,
      badgeText: item.badge,
      iconUrl: item.iconUrl,
      useNewDesign: widget.arguments.useNewDesign,
      containerColor: item.bgColor?.getColor(),
      itemWidth: widget.arguments.sizeParams.itemWidth,
      iconWidth: widget.arguments.sizeParams.iconWidth,
      useLargeText: widget.arguments.sizeParams.useLargeText,
      onTap: () async {
        if (item.actionUrl == null) {
          return;
        } else {
          await resolveGameDeeplink(
            context,
            item.trackingLabel == 'Marketplace'
                ? item.actionUrl!.enrichShoppingMallUrl(
                    widget.arguments.userCuid,
                    widget.arguments.userGuid,
                  )
                : item.actionUrl!,
            linkTitle: item.title ?? '',
            askForLoginCondition: () => (item.actionUrl?.contains('.vn') ?? false) && item.trackingLabel == 'game',
          );
        }
      },
    );
  }
}
