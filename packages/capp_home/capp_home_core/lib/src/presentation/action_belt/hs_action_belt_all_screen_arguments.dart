import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/widgets.dart';
import 'package:koyal_core/koyal_core.dart';

final class HSActionBeltSizeParams {
  final double itemWidth;
  final double iconWidth;
  final bool useLargeText;

  const HSActionBeltSizeParams({
    required this.itemWidth,
    required this.iconWidth,
    required this.useLargeText,
  });
}

class HSActionBeltAllScreenArguments extends ScreenArguments {
  final List<UserActionBeltWidget> items;
  final String? userGuid;
  final String? userCuid;
  final bool useNewDesign;
  void Function(BuildContext context)? onInitScreen;
  final HSActionBeltSizeParams sizeParams;

  HSActionBeltAllScreenArguments({
    required this.items,
    this.userGuid,
    this.userCuid,
    this.onInitScreen,
    this.useNewDesign = false,
    this.sizeParams = const HSActionBeltSizeParams(itemWidth: 64, iconWidth: 32, useLargeText: false),
  });
}
