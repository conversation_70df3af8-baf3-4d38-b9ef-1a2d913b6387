import 'dart:async';
import 'dart:ui';

/// A simple debouncer utility for action belt items to prevent rapid successive taps
class ActionBeltDebouncer {
  static final Map<String, Timer> _timers = {};

  /// Executes a callback with debouncing to prevent rapid successive calls
  /// [key] - unique identifier for the debounced action
  /// [duration] - debounce duration (default 500ms)
  /// [callback] - the function to execute
  static void debounce(
    String key,
    VoidCallback callback, {
    Duration duration = const Duration(milliseconds: 300),
  }) {
    // Cancel existing timer for this key
    _timers[key]?.cancel();

    // Execute callback immediately
    callback();

    // Set a timer to prevent subsequent calls
    _timers[key] = Timer(duration, () {
      _timers.remove(key);
    });
  }

  /// Checks if an action is currently debounced
  static bool isDebounced(String key) {
    return _timers.containsKey(key);
  }

  /// Clears debounce timers by prefix (useful for screen-specific cleanup)
  static void clearByPrefix(String prefix) {
    final keysToRemove = <String>[];
    for (final key in _timers.keys) {
      if (key.startsWith(prefix)) {
        _timers[key]?.cancel();
        keysToRemove.add(key);
      }
    }
    for (final key in keysToRemove) {
      _timers.remove(key);
    }
  }

  /// Clears all debounce timers (useful for app-level cleanup)
  static void clearAll() {
    for (final timer in _timers.values) {
      timer.cancel();
    }
    _timers.clear();
  }

  /// Gets the current number of active timers (useful for debugging)
  static int get activeTimerCount => _timers.length;
}
