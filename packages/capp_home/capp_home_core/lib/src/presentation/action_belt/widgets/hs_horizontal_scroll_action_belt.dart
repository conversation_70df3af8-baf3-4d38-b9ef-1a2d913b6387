import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_home_core.dart';

/// works for 5-8 items
class HsHorizontalScrollActionBelt extends StatelessWidget {
  final List<HSActionBeltItem> items;
  final double itemWidth;

  const HsHorizontalScrollActionBelt({
    super.key,
    required this.items,
    required this.itemWidth,
  });

  @override
  Widget build(BuildContext context) {
    // calculate padding between items (only 4.5 should be visible) (screen size - 2x outer paddding - 4.5 items width - 4x inner padding)
    final paddingBetweenItems = (MediaQuery.of(context).size.width - KoyalPadding.paddingNormal - 4.5 * itemWidth) / 4;

    return SingleChildScrollView(
      key: const Key('__actionBeltScrollable__'),
      scrollDirection: Axis.horizontal,
      clipBehavior: Clip.none,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items.take(8).mapWithIndex((index, item) {
          // paddings for first and last item
          final isLast = index == (items.take(8).length - 1);
          return KoyalPadding.normalAll(
            right: isLast,
            top: false,
            bottom: false,
            left: false,
            // padding between items (only 4.5 items should be visible)
            child: Padding(
              padding: EdgeInsets.only(right: isLast ? 0 : paddingBetweenItems),
              child: item,
            ),
          );
        }).toList(),
      ),
    );
  }
}
