import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../capp_home_core.dart';

/// works for 3-4 items
class HsRowActionBelt extends StatelessWidget {
  final List<HSActionBeltItem> items;

  const HsRowActionBelt({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalAll(
      bottom: false,
      top: false,
      left: false,
      child: Row(
        mainAxisAlignment: items.length > 3 ? MainAxisAlignment.spaceBetween : MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items,
      ),
    );
  }
}
