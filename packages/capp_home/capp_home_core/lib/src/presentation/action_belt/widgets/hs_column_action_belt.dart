import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class HsColumnActionBelt extends StatelessWidget {
  final List<MenuItemRow> items;

  const HsColumnActionBelt({
    super.key,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: items
          .map(
            (item) => Column(
              children: [
                item,
                if (item != items.last) const ListDivider(),
              ],
            ),
          )
          .toList(),
    );
  }
}
