import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../capp_home_core.dart';

/// works for 8 and more items
class HsGridActionBelt extends StatelessWidget {
  final List<HSActionBeltItem> items;
  final Function() onMoreButtonTap;
  final bool useNewDesignMoreButton;
  final double itemWidth;
  final double iconWidth;
  final bool useLargeText;

  const HsGridActionBelt({
    super.key,
    required this.items,
    required this.onMoreButtonTap,
    this.useNewDesignMoreButton = false,
    required this.itemWidth,
    required this.iconWidth,
    required this.useLargeText,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - (2 * KoyalPadding.paddingNormal);
    // Calculate spacing that will evenly distribute items (3 spaces between 4 items)
    final spacing = (availableWidth - (4 * itemWidth)) / 3;

    return Padding(
      padding: const EdgeInsets.only(right: KoyalPadding.paddingNormal),
      child: Wrap(
        key: const Key('__actionBeltGridView__'),
        spacing: spacing,
        runSpacing: KoyalPadding.paddingSmall,
        children: [
          ...items.length > 8 ? items.take(7) : items,
          if (items.length > 8) _buildMoreButton(context),
        ],
      ),
    );
  }

  HSActionBeltItem _buildMoreButton(BuildContext context) {
    return HSActionBeltItem(
      key: const Key('__actionBeltItem_more'),
      label: L10nCappHome.of(context).actionBeltItemMore,
      icon: KoyalIcons.ellipsis_horizontal_outline,
      onTap: onMoreButtonTap,
      useNewDesign: useNewDesignMoreButton,
      itemWidth: itemWidth,
      iconWidth: iconWidth,
      useLargeText: useLargeText,
    );
  }
}
