import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart' hide BadgeTheme;
import 'widgets/action_belt_debouncer.dart';

class HSActionBeltItem extends StatelessWidget {
  final String? label;
  final String? badgeText;
  final String? iconUrl;
  final IconData? icon;
  final VoidCallback? onTap;
  final bool useNewDesign;
  final Color? containerColor;
  final double itemWidth;
  final double iconWidth;
  final bool useLargeText;

  const HSActionBeltItem({
    super.key,
    required this.label,
    this.badgeText,
    this.iconUrl,
    this.icon,
    this.onTap,
    this.useNewDesign = false,
    this.containerColor,
    required this.itemWidth,
    required this.iconWidth,
    required this.useLargeText,
  });

  @override
  Widget build(BuildContext context) {
    if (useNewDesign) {
      return _ActionBeltItemWidget(
        label: label,
        badgeText: badgeText,
        iconUrl: iconUrl,
        icon: icon,
        onTap: onTap,
        containerColor: containerColor,
        itemWidth: itemWidth,
        iconWidth: iconWidth,
        useLargeText: useLargeText,
      );
    } else {
      return _ActionBeltItemWidget(
        label: label,
        badgeText: badgeText,
        iconUrl: iconUrl,
        icon: icon,
        onTap: onTap,
        containerColor: containerColor ?? ColorTheme.of(context).appbarBackgroundColor,
        isIconElevated: true,
        itemWidth: itemWidth,
        iconWidth: iconWidth,
        useLargeText: useLargeText,
      );
    }
  }
}

class _ActionBeltItemWidget extends StatelessWidget {
  final String? label;
  final String? badgeText;
  final String? iconUrl;
  final IconData? icon;
  final VoidCallback? onTap;
  final Color? containerColor;
  final bool isIconElevated;
  final double itemWidth;
  final double iconWidth;
  final bool useLargeText;

  const _ActionBeltItemWidget({
    required this.label,
    this.badgeText,
    this.iconUrl,
    this.icon,
    this.onTap,
    this.containerColor,
    this.isIconElevated = false,
    required this.itemWidth,
    required this.iconWidth,
    required this.useLargeText,
  });

  void _handleTap() {
    if (onTap == null) return;

    // Create a unique key for this button based on label
    final debounceKey = 'action_belt_${label ?? 'unknown'}';

    // Use debouncer to prevent rapid successive taps
    ActionBeltDebouncer.debounce(
      debounceKey,
      () {
        // Execute the callback
        onTap!();
      },
      duration: const Duration(milliseconds: 150), // Shorter debounce for better UX
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _handleTap,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            SizedBox(
              width: itemWidth,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  buildGraphicPart(context),
                  const SizedBox(height: KoyalPadding.paddingSmall),
                  buildLabel(context),
                ],
              ),
            ),
            if (badgeText != null && badgeText!.isNotEmpty) buildBadge(badgeText!),
          ],
        ),
      ),
    );
  }

  Widget buildGraphicPart(BuildContext context) {
    final iconWidget = containerColor != null ? buildIconWithBackground(context) : buildIcon(context);

    return isIconElevated ? KoyalElevation.koyal3Dp(child: iconWidget) : iconWidget;
  }

  Widget buildIconWithBackground(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(KoyalPadding.paddingSmall),
      decoration: BoxDecoration(
        color: containerColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: buildIcon(context),
    );
  }

  Widget buildIcon(BuildContext context) {
    return SizedBox(
      width: iconWidth,
      height: iconWidth,
      child:
          icon != null ? Icon(icon, color: ColorTheme.of(context).primaryColor) : iconUrl.asImage(fit: BoxFit.contain),
    );
  }

  Widget buildBadge(String badge) {
    return Positioned(
      top: -7,
      left: 22,
      child: SimpleBadge(
        label: badge,
        badgeTheme: BadgeTheme.info(),
      ),
    );
  }

  KoyalText buildLabel(BuildContext context) => useLargeText
      ? KoyalText.caption2(
          color: ColorTheme.of(context).defaultTextColor,
          label ?? '',
          textAlign: TextAlign.center,
          maxLines: 2,
        )
      : KoyalText.caption4(
          color: ColorTheme.of(context).defaultTextColor,
          label ?? '',
          textAlign: TextAlign.center,
          maxLines: 2,
        );
}
