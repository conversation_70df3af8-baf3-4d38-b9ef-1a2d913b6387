import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../capp_home_core.dart';
import '../../infrastructure/action_belt/shopingmall_extension.dart';
import '../skeleton_loading/widgets/skeleton_widgets.dart';

class HSActionBelt extends StatelessWidget with GameDeeplinkResolverMixin implements HomeScreenPadding {
  final String? title;
  final double itemWidth;
  final double iconWidth;
  final bool useLargeText;
  final double rowIconWidth;

  const HSActionBelt({
    super.key,
    this.title,
    this.itemWidth = 64,
    this.iconWidth = 32,
    this.useLargeText = false,
    this.rowIconWidth = 24,
  });

  // Alow child (Product AB) to set custom key for visibility detector
  @protected
  Key get visibilityKey => Key('__actionBeltVisibilityDetector_${title ?? ''}');

  @protected
  bool get useNewDesign => false;

  // Do not override build function directly, it is checking visibility for tracking
  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: visibilityKey,
      onVisibilityChanged: (visibility) {
        if (visibility.visibleFraction > 0.9) trackOnBecomeVisible(context); // _trackViewEvent(context, item);
      },
      child: buildBody(context),
    );
  }

  @protected
  List<HSActionBeltItem> buildActionBeltItems(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? userGuid,
    String? userCuid,
  ) {
    return items
        .map(
          (item) => HSActionBeltItem(
            key: Key('__actionBeltItem_${item.trackingLabel}'),
            label: item.title,
            badgeText: item.badge,
            iconUrl: item.iconUrl,
            containerColor: item.bgColor?.getColor(),
            useNewDesign: useNewDesign,
            onTap: () => onItemTap(context, item, userGuid, userCuid),
            itemWidth: itemWidth,
            iconWidth: iconWidth,
            useLargeText: useLargeText,
          ),
        )
        .toList();
  }

  // Function to override for custom body
  @protected
  Widget buildBody(BuildContext context) {
    return BlocProvider<ActionBeltBloc>.value(
      value: context.get<ActionBeltBloc>(),
      child: BlocBuilder<ActionBeltBloc, ActionBeltState>(
        builder: (context, state) {
          if (state.isLoading || state.actionBeltWidgets == null) return buildShimmering();

          return KoyalPadding.normalVertical(
            child: buildCountrySpecificAB(context, state.actionBeltWidgets!, title, state.userCuid, state.userGuid),
          );
        },
      ),
    );
  }

  Widget buildShimmering() => const ActionBeltItemsShimmer(numOfItems: 4);

  Future<void> onItemTap(
    BuildContext context,
    UserActionBeltWidget item,
    String? userGuid,
    String? userCuid,
  ) async {
    if (item.actionUrl == null) return;

    trackClickEvent(context, item);
    await resolveGameDeeplink(
      context,
      item.trackingLabel == 'Marketplace' ? item.actionUrl!.enrichShoppingMallUrl(userCuid, userGuid) : item.actionUrl!,
      linkTitle: item.title ?? '',
      askForLoginCondition: () => (item.actionUrl?.contains('.vn') ?? false) && item.trackingLabel == 'game',
    );
  }

  Widget buildCountrySpecificAB(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? title,
    String? userCuid,
    String? userGuid,
  ) {
    final numOfItems = items.length;
    if (numOfItems < 3) {
      return buildColumnActionBelt(context, items, userCuid, userGuid);
    } else if (numOfItems > 2 && numOfItems < 5) {
      return buildRowActionBelt(context, items, userCuid, userGuid);
    } else if (numOfItems > 4 && numOfItems < 8) {
      return buildListViewActionBelt(context, items, userCuid, userGuid);
    } else {
      // numOfItems > 7
      return buildGridViewActionBelt(context, items, userCuid, userGuid);
    }
  }

  Widget buildColumnActionBelt(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? userCuid,
    String? userGuid,
  ) =>
      HsColumnActionBelt(
        items: items
            .map(
              (item) => MenuItemRow(
                key: Key('__actionBeltItem_${item.trackingLabel}'),
                icon: item.iconUrl.asImage(fit: BoxFit.scaleDown),
                label: item.title ?? '',
                onTap: (context) => onItemTap(context, item, userGuid, userCuid),
                badge: item.badge ?? '',
                rowIconWidth: rowIconWidth,
              ),
            )
            .toList(),
      );

  Widget buildRowActionBelt(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? userCuid,
    String? userGuid,
  ) =>
      HsRowActionBelt(
        items: buildActionBeltItems(context, items, userGuid, userCuid),
      );

  Widget buildListViewActionBelt(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? userCuid,
    String? userGuid,
  ) =>
      HsHorizontalScrollActionBelt(
        items: buildActionBeltItems(context, items, userGuid, userCuid),
        itemWidth: itemWidth,
      );

  Widget buildGridViewActionBelt(
    BuildContext context,
    List<UserActionBeltWidget> items,
    String? userCuid,
    String? userGuid,
  ) =>
      HsGridActionBelt(
        useNewDesignMoreButton: useNewDesign,
        onMoreButtonTap: () {
          trackClickEvent(context, null);
          context.navigator.pushFromPackage(
            package: 'CappHome',
            screen: 'HSActionBeltAllScreen',
            arguments: HSActionBeltAllScreenArguments(
              items: items,
              userGuid: userGuid,
              userCuid: userCuid,
              useNewDesign: useNewDesign,
              onInitScreen: trackAllView,
              sizeParams: HSActionBeltSizeParams(
                itemWidth: itemWidth,
                iconWidth: iconWidth,
                useLargeText: useLargeText,
              ),
            ),
          );
        },
        items: buildActionBeltItems(context, items, userGuid, userCuid),
        itemWidth: itemWidth,
        iconWidth: iconWidth,
        useLargeText: useLargeText,
      );

  // DAS016
  @protected
  void trackClickEvent(BuildContext context, UserActionBeltWidget? item) {
    context.get<CappTrackingService>().trackEvent(
      event: KoyalEvent.homeDashboardActionBeltClick,
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: KoyalTrackingLabels.actionBelt,
      customDimensions: {
        TrackingProperties.propertyCdItemName: item?.trackingLabel ?? 'view_more',
      },
    );
  }

  // DAS015
  @protected
  void trackOnBecomeVisible(BuildContext context) {
    context.get<CappTrackingService>().trackEvent(
          event: KoyalEvent.homeDashboardActionBeltView,
          eventCategory: KoyalTrackingCategories.homeDashboard,
          eventAction: KoyalAnalyticsConstants.view,
          eventLabel: KoyalTrackingLabels.actionBelt,
        );
  }

  @protected
  void trackAllView(BuildContext context) {
    //not used for core
  }

  @override
  bool get disableRight => true;

  @override
  bool get isDisabled => false;
}
