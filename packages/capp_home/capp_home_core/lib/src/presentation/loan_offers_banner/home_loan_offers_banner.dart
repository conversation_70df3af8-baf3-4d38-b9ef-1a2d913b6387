import 'package:flutter/widgets.dart';

import 'loan_offers_banner.dart';

class HomeLoanOffersBanner extends StatelessWidget {
  final String? title;
  final bool isDummyBannerDisplayed;
  final bool isDummyBannerFFEnabled;
  final bool useNewDesign;

  const HomeLoanOffersBanner({
    super.key,
    required this.isDummyBannerDisplayed,
    required this.isDummyBannerFFEnabled,
    required this.useNewDesign,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    if (isDummyBannerDisplayed && isDummyBannerFFEnabled) {
      return const SizedBox();
    }
    return LoanOffersBanner(
      title: title,
      useNewDesign: useNewDesign,
    );
  }
}
