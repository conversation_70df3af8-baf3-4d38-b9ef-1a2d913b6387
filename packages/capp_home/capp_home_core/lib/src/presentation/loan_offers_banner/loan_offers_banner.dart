import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_loan_shared_core/capp_loan_shared_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';

class LoanOffersBanner extends StatelessWidget {
  final String? title;
  final bool hideTitle;
  final bool useNewDesign;

  const LoanOffersBanner({
    Key? key,
    this.title,
    this.hideTitle = false,
    this.useNewDesign = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => FeatureFlagWidget(
        featureName: FeatureFlag.offerBanner,
        child: BlocBuilder<OfferBannerBloc, OfferBannerState>(
          builder: (context, offerState) {
            return OfferBanners(
              key: const Key('__loanOffersBanner__'),
              title: hideTitle ? null : title ?? L10nCappLoanOriginationUnified.of(context).loanOffersBannerTitle,
              bannerTrackingType: BannerTrackingType.homeDashboard,
              incomeOffer: offerState.incomeOffer,
              phoneNumber: offerState.phoneNumber,
              loanOffers: offerState.loanOffers,
              showConsent: context.get<CappHomeSettings>().showBannerConset,
              showNewScoringBanner: context.get<CappHomeSettings>().usesNewScoringBanner &&
                  context.isFlagEnabledRead(FeatureFlag.scoringBannerAbTest),
              isLoading: offerState.isLoading,
              trackEvents: offerState.trackEvents,
              isError: offerState.isError,
              isHomeScreen: true,
              useNewDesign: useNewDesign,
            );
          },
        ),
      );
}
