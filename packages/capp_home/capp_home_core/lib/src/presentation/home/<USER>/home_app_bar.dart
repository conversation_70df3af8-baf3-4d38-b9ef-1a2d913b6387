import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_home_core.dart';

class HomeAppBar extends KoyalAppBar {
  static const double defaultBarHeight = 56;
  final bool isAnonymous;

  HomeAppBar({
    super.key,
    super.actions,
    super.moreActionsMenuItems = const [],
    super.barHeight = defaultBarHeight,
    super.bottomHeight = 0,
    super.showBottomDivider = true,
    required this.isAnonymous,
  });

  @override
  State<HomeAppBar> createState() => _HomeAppBarState();
}

class _HomeAppBarState extends State<HomeAppBar> with TickerProviderStateMixin {
  late AnimationController _colorAnimationController;
  late Animation<Color?> _backgroundColorTween, _foregroundColorTween;
  late Color iconColor;
  late AppBarTheme appBarTheme;
  late TextButtonThemeData textButtonColor;

  final startTextButtonColor = TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: HciColors.supplementary0,
      textStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
      ),
    ),
  );

  @override
  void initState() {
    _colorAnimationController = AnimationController(
      vsync: this,
      duration: Durations.short3,
    );
    _colorAnimationController.addListener(() {
      iconColor = _foregroundColorTween.value!;
      appBarTheme = appBarTheme.copyWith(
        backgroundColor: _backgroundColorTween.value,
        systemOverlayStyle: _colorAnimationController.value > 0.5
            ? SystemUiOverlayStyle.dark.copyWith(statusBarColor: Colors.transparent)
            : SystemUiOverlayStyle.light.copyWith(statusBarColor: Colors.transparent),
      );
      textButtonColor = TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _foregroundColorTween.value,
          textStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: iconColor,
          ),
        ),
      );
    });

    _backgroundColorTween =
        ColorTween(begin: HciColors.primary500, end: HciColors.supplementary0).animate(_colorAnimationController);
    _foregroundColorTween =
        ColorTween(begin: HciColors.supplementary0, end: HciColors.primary500).animate(_colorAnimationController);

    super.initState();
    appBarTheme = AppBarTheme(
      elevation: 0,
      backgroundColor: _backgroundColorTween.value,
      foregroundColor: HciColors.supplementary0,
      iconTheme: const IconThemeData(color: HciColors.supplementary0, size: 24),
      actionsIconTheme: const IconThemeData(color: HciColors.supplementary0, size: 24),
      systemOverlayStyle: SystemUiOverlayStyle.light.copyWith(statusBarColor: Colors.transparent),
    );
    textButtonColor = startTextButtonColor;
    iconColor = HciColors.supplementary0;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PendingActionsBloc, PendingActionsState>(
      builder: (context, state) {
        return AnimatedBuilder(
          animation: _colorAnimationController,
          builder: (context, _) {
            return Theme(
              data: Theme.of(context).copyWith(
                appBarTheme: appBarTheme,
                textButtonTheme: textButtonColor,
              ),
              child: AppBar(
                surfaceTintColor: Colors.transparent,
                notificationPredicate: (notification) {
                  if (notification.depth == 0) {
                    final scrollOffset = notification.metrics.pixels;
                    final percentageScrolled = (scrollOffset / 140).clamp(0.0, 1.0);
                    _colorAnimationController.value = percentageScrolled;
                    return true;
                  }
                  return false;
                },
                toolbarHeight: 50,
                leadingWidth: 80,
                elevation: 0,
                scrolledUnderElevation: state.items.isEmpty ? 2.0 : 0.0,
                shadowColor: ColorTheme.of(context).foreground50Color.withOpacity(0.25),
                leading: Builder(
                  builder: (context) {
                    return KoyalPadding.normalAll(
                      right: false,
                      top: false,
                      bottom: false,
                      child: AssetSvgImage(
                        'assets/svg/logo/appbar_homecredit_logo.svg',
                        key: const Key('__appBarHomeCreditLogo__'),
                        package: 'capp_ui',
                        color: iconColor,
                      ),
                    );
                  },
                ),
                actions: [
                  ConstrainedBox(
                    constraints: const BoxConstraints(minWidth: 48.0),
                    child: widget.isAnonymous
                        ? const SignInAction()
                        : AuthorizedAction(
                            iconColor: iconColor,
                          ),
                  ),
                  if (widget.moreActionsMenuItems.isNotEmpty) ...[
                    IconButton(
                      key: const Key('__KoyalAppBarMoreActionsButton__'),
                      icon: Icon(
                        KoyalIcons.ellipsis_horizontal_solid,
                        size: 24,
                        color: iconColor,
                      ),
                      onPressed: () => showKoyalBottomSheet<String>(
                        context: context,
                        builder: (context) => MenuListView(items: widget.moreActionsMenuItems),
                      ),
                      constraints: const BoxConstraints(maxWidth: 48.0),
                    ),
                    const SizedBox(width: 4.0),
                  ],
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _colorAnimationController.dispose();
    super.dispose();
  }
}
