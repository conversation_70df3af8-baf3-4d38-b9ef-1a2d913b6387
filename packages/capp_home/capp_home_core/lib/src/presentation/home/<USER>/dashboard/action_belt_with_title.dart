import 'package:flutter/material.dart';

import '../../../../../capp_home_core.dart';

class ActionBeltWithTitle extends StatelessWidget {
  final bool actionBeltHasTitle;
  final bool actionBeltHasAction;
  final Widget actionBelt;
  final String? title;

  const ActionBeltWithTitle({
    Key? key,
    required this.actionBeltHasTitle,
    required this.actionBelt,
    this.actionBeltHasAction = false,
    this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HomeScreenWidgetPadding(
      title: actionBeltHasTitle ? title ?? L10nCappHome.of(context).otherServices : null,
      childHorizontalPadding: false,
      isChildPaddingBot: false,
      child: actionBelt,
    );
  }
}
