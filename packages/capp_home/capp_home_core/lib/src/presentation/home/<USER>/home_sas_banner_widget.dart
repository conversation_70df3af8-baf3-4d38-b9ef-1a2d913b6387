import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_plugins_core/capp_plugins_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../application/home/<USER>';
import 'dashboard/index.dart';

class HomeSasBannerWidget extends StatefulWidget {
  final String? title;
  final bool useNewDesign;

  const HomeSasBannerWidget({
    super.key,
    this.title,
    this.useNewDesign = false,
  });

  @override
  State<HomeSasBannerWidget> createState() => _HomeSasBannerWidgetState();
}

class _HomeSasBannerWidgetState extends State<HomeSasBannerWidget> {
  @override
  Widget build(BuildContext context) {
    if (context.isFlagEnabledWatch(FeatureFlag.sasHomeBanners)) {
      return BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          if (state.areSasBanner360Supported && state.spotViewFactory != null) {
            final banners = state.bannerState;
            final values = banners.entries.where((e) => e.value).map((e) => e.key);
            if (widget.useNewDesign) {
              return sasHomeBannerWithTitle(
                title: widget.title,
                spots: [...values],
                spotViewFactory: state.spotViewFactory!,
              );
            }
            return SasHomeBanner(
              spots: [...values],
              useNewDesign: widget.useNewDesign,
              spotViewFactory: state.spotViewFactory!,
            );
          }
          return const SizedBox.shrink();
        },
      );
    }

    return const DailyDealDashboardBanner();
  }

  Widget sasHomeBannerWithTitle({
    String? title,
    required List<String> spots,
    required ISasSpotViewFactory spotViewFactory,
  }) {
    if (title != null && title.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 35,
            child: KoyalPadding.normalHorizontal(
              child: KoyalText.subtitle1(
                title,
                color: ColorTheme.of(context).defaultTextColor,
                textAlign: TextAlign.left,
              ),
            ),
          ),
          SasHomeBanner(
            spots: spots,
            useNewDesign: widget.useNewDesign,
            spotViewFactory: spotViewFactory,
          ),
        ],
      );
    }
    return SasHomeBanner(
      spots: spots,
      useNewDesign: widget.useNewDesign,
      spotViewFactory: spotViewFactory,
    );
  }
}
