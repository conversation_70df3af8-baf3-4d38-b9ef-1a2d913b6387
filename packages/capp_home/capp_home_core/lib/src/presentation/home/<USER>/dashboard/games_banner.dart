import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../../capp_home_core.dart';

const gamesBannerLink = 'https://songvuihomecredit.com/?utm_source=gamev7&utm_medium=GMApp';
const _borderRadius = BorderRadius.all(Radius.circular(8));

class GamesBanner extends StatefulWidget {
  final String? title;
  final bool useNewDesign;

  const GamesBanner({
    Key? key,
    this.title,
    this.useNewDesign = false,
  }) : super(key: key);

  @override
  State<GamesBanner> createState() => _GamesBannerState();
}

class _GamesBannerState extends State<GamesBanner> with GameDeeplinkResolverMixin {
  late Image placeholder;

  @override
  void initState() {
    super.initState();
    placeholder = Image.asset('assets/images/empty.png', fit: BoxFit.fill);
    BlocProvider.of<GamesBannerBloc>(context).add(const GamesBannerEvent.initLoading());
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width - 24;
    final height = width * 408 / 1080;

    return BlocBuilder<GamesBannerBloc, GamesBannerState>(
      key: const Key('__gamesBanner__'),
      builder: (context, state) => state is GameBannerFailure || state is GameBannerInitial
          ? const SizedBox.shrink()
          : HomeScreenWidgetPadding(
              title: widget.title ?? L10nCappHome.of(context).gamesBannerTitle,
              useNewDesign: widget.useNewDesign,
              isChildPaddingBot: false,
              child: state is GameBannerLoadInProgress
                  ? _GamesBannerLoading(height: height)
                  : CappVisibilityDetector(
                      key: const Key('__GamesBannerVisibilityDetector__'),
                      onBecameVisible: () {
                        context.get<CappTrackingService>().trackHomeDashboardEvent(
                              event: KoyalEvent.homeDashboardGamesBannerView,
                              eventAction: KoyalAnalyticsConstants.view,
                              eventLabel: 'games_banner',
                            );
                      },
                      child: ClipRRect(
                        borderRadius: _borderRadius,
                        child: SizedBox(
                          width: double.infinity,
                          height: height,
                          child: Stack(
                            children: <Widget>[
                              Positioned.fill(
                                child: (state as GameBannerLoadSuccess).bannerImageUrl.asImage(
                                      fit: BoxFit.fill,
                                      width: double.maxFinite,
                                      height: double.maxFinite,
                                      precachedPlaceholderImage: placeholder,
                                      showShimmer: true,
                                    ),
                              ),
                              Positioned.fill(
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(onTap: () => _onBannerTap(context)),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
            ),
    );
  }

  void _onBannerTap(BuildContext context) {
    context.get<CappTrackingService>().trackEvent(
          event: KoyalEvent.homeDashboardGamesBannerClick,
          eventCategory: KoyalTrackingCategories.homeDashboard,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: KoyalTrackingLabels.gamesBanner,
        );
    resolveGameDeeplink(
      context,
      gamesBannerLink,
      linkTitle: L10nCappHome.of(context).gamesBannerTitle,
      useCustomTabs: true,
    );
  }
}

class _GamesBannerLoading extends StatelessWidget {
  final double height;

  const _GamesBannerLoading({Key? key, required this.height}) : super(key: key);

  @override
  Widget build(BuildContext context) => KoyalPadding.normalHorizontal(
        child: ClipRRect(
          borderRadius: _borderRadius,
          child: KoyalShimmer(
            child: Container(width: double.infinity, height: height, color: ColorTheme.of(context).backgroundColor),
          ),
        ),
      );
}
