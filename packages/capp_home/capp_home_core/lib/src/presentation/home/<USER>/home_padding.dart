import 'package:flutter/widgets.dart';

import 'home_screen_padding.dart';

/// Simple widget for identifying widget supports new padding system at home screen
class HomePadding extends StatelessWidget implements HomeScreenPadding {
  final bool disableRightPadding;
  final bool isWidgetDisabled;
  final Widget child;

  const HomePadding({
    super.key,
    required this.child,
    this.disableRightPadding = false,
    this.isWidgetDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return child;
  }

  @override
  bool get disableRight => disableRightPadding;

  @override
  bool get isDisabled => isWidgetDisabled;
}
