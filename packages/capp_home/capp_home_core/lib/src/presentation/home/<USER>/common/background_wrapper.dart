import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class BackgroundWrapper extends StatelessWidget {
  final Widget child;
  final bool gradient;

  const BackgroundWrapper({Key? key, required this.child, this.gradient = true}) : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
        decoration: gradient
            ? BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: FractionalOffset.bottomCenter,
                  stops: const [0, 0.4],
                  colors: [ColorTheme.of(context).backgroundColor, ColorTheme.of(context).windowBackgroundColor],
                ),
              )
            : BoxDecoration(color: ColorTheme.of(context).backgroundColor),
        child: child,
      );
}
