import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../../capp_home_core.dart';

class Installments extends StatelessWidget {
  const Installments({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => BlocBuilder<HomeBloc, HomeState>(
        buildWhen: (previous, current) =>
            previous.userProfileModel?.signOnState != current.userProfileModel?.signOnState,
        builder: (context, state) {
          return state.userProfileModel?.signOnState == SignOnStatus.client
              ? CappVisibilityDetector(
                  key: const Key('__InstallmentOverviewVisibilityDetector__'),
                  onBecameVisible: () {
                    context.get<CappTrackingService>().trackHomeDashboardEvent(
                          event: KoyalEvent.homeDashboardInstallmentsOverviewBannerView,
                          eventAction: KoyalAnalyticsConstants.view,
                          eventLabel: 'installments_banner',
                        );
                  },
                  // TODORET_A When user has no installments/FF is off the padding
                  // is still visible causing empty space between home screen sections
                  child: KoyalPadding.normalAll(
                    bottom: false,
                    right: false,
                    left: false,
                    child: Column(
                      children: [
                        FeatureFlagWidget(
                          featureName: FeatureFlag.homeScreenVnNewDesign,
                          disabled: Column(
                            children: [
                              FeatureFlagWidget(
                                featureName: FeatureFlag.loanCardsHideOld,
                                disabled: InstallmentsOverview(
                                  showAppearAnimation: true,
                                  padBottom: true,
                                  title: KoyalPadding.normalHorizontal(
                                    child: KoyalPadding.xSmallVertical(
                                      child: Row(
                                        children: [
                                          KoyalText.header6(
                                            color: ColorTheme.of(context).defaultTextColor,
                                            L10nCappHome.of(context).myProducts,
                                          ),
                                          const Spacer(),
                                          TertiaryButton(
                                            key: const Key('__myLoansSectionShowAllButton__'),
                                            text: L10nCappHome.of(context).showAll,
                                            padding: EdgeInsets.zero,
                                            onPressed: () {
                                              context.get<CappTrackingService>().trackHomeDashboardEvent(
                                                    event: KoyalEvent.homeDashboardInstallmentsOverviewViewAllClick,
                                                    eventAction: KoyalAnalyticsConstants.click,
                                                    eventLabel: 'installments_view_all',
                                                  );
                                              context.get<CappTrackingService>().trackClickEvent(
                                                    eventCategory: 'user_action_dashboard',
                                                    eventLabel: 'see_all_loans',
                                                  );
                                              context.navigator.toMainScreen(
                                                arguments: MainScreenArguments(initialTab: TabItem.loans),
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                child: Container(),
                              ),
                              FeatureFlagWidget(
                                featureName: FeatureFlag.loanCardsNew,
                                child: InstallmentsOverviewNew(
                                  showAppearAnimation: true,
                                  padBottom: true,
                                  title: KoyalPadding.normalHorizontal(
                                    child: KoyalPadding.xSmallVertical(
                                      child: Row(
                                        children: [
                                          KoyalText.header6(
                                            color: ColorTheme.of(context).defaultTextColor,
                                            L10nCappHome.of(context).myProducts,
                                          ),
                                          const Spacer(),
                                          TertiaryButton(
                                            key: const Key('__myLoansSectionShowAllButton__'),
                                            text: L10nCappHome.of(context).showAll,
                                            padding: EdgeInsets.zero,
                                            onPressed: () {
                                              context.get<CappTrackingService>().trackHomeDashboardEvent(
                                                    event: KoyalEvent.homeDashboardInstallmentsOverviewViewAllClick,
                                                    eventAction: KoyalAnalyticsConstants.click,
                                                    eventLabel: 'installments_view_all',
                                                  );
                                              context.get<CappTrackingService>().trackClickEvent(
                                                    eventCategory: 'user_action_dashboard',
                                                    eventLabel: 'see_all_loans',
                                                  );
                                              context.navigator.toMainScreen(
                                                arguments: MainScreenArguments(initialTab: TabItem.loans),
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          child: BlocBuilder<InstallmentOverviewBloc, InstallmentOverviewState>(
                            builder: (context, state) {
                              return ProductCardsOverview(
                                showAppearAnimation: true,
                                title: KoyalPadding.normalHorizontal(
                                  child: SizedBox(
                                    height: 25,
                                    child: Row(
                                      children: [
                                        KoyalText.subtitle1(
                                          color: ColorTheme.of(context).defaultTextColor,
                                          L10nCappHome.of(context).myProducts,
                                        ),
                                        const Spacer(),
                                        TertiaryButton(
                                          key: const Key('__myLoansSectionShowAllButton__'),
                                          text: (state.financialCards.length > 1)
                                              ? '${L10nCappHome.of(context).viewAll} (${state.financialCards.length})'
                                              : L10nCappHome.of(context).viewAll,
                                          padding: EdgeInsets.zero,
                                          onPressed: () {
                                            context.get<CappTrackingService>().trackHomeDashboardEvent(
                                                  event: KoyalEvent.homeDashboardInstallmentsOverviewViewAllClick,
                                                  eventAction: KoyalAnalyticsConstants.click,
                                                  eventLabel: 'installments_view_all',
                                                );
                                            context.get<CappTrackingService>().trackClickEvent(
                                                  eventCategory: 'user_action_dashboard',
                                                  eventLabel: 'see_all_loans',
                                                );
                                            context.navigator.toMainScreen(
                                              arguments: MainScreenArguments(initialTab: TabItem.loans),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : const SizedBox.shrink();
        },
      );
}
