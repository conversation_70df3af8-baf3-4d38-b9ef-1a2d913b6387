import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class HomeScreenWidgetPadding extends StatelessWidget {
  final Widget child;
  final String? title;
  final Widget? titleAction;
  final bool childHorizontalPadding;
  final bool isTitlePaddingBot;
  final bool isChildPaddingBot;
  final bool useNewDesign;

  const HomeScreenWidgetPadding({
    super.key,
    required this.child,
    this.title,
    this.titleAction,
    this.childHorizontalPadding = true,
    this.isTitlePaddingBot = true,
    this.isChildPaddingBot = true,
    this.useNewDesign = false,
  });

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.xSmallVertical(
      child: Column(
        children: [
          if (title != null)
            KoyalPadding.normalHorizontal(
              child: SizedBox(
                height: 40,
                child: Row(
                  children: [
                    if (useNewDesign)
                      KoyalText.subtitle1(
                        title!,
                        color: ColorTheme.of(context).defaultTextColor,
                        textAlign: TextAlign.left,
                      )
                    else
                      KoyalText.header6(
                        title!,
                        color: ColorTheme.of(context).defaultTextColor,
                        textAlign: TextAlign.left,
                      ),
                    if (titleAction != null) const Spacer(),
                    if (titleAction != null) titleAction!,
                  ],
                ),
              ),
            ),
          KoyalPadding.normalAll(
            top: false,
            bottom: isChildPaddingBot,
            left: childHorizontalPadding,
            right: childHorizontalPadding,
            child: child,
          ),
        ],
      ),
    );
  }
}
