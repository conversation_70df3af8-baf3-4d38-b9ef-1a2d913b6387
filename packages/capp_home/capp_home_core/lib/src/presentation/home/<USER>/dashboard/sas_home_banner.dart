import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_plugins_core/capp_plugins_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:getwidget/components/carousel/gf_carousel.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:visibility_detector/visibility_detector.dart';

class SasHomeBanner extends StatelessWidget {
  final ISasSpotViewFactory spotViewFactory;
  final List<String> spots;
  final bool useNewDesign;
  const SasHomeBanner({
    Key? key,
    required this.spots,
    required this.spotViewFactory,
    this.useNewDesign = false,
  }) : super(key: key);

  void onBecomeVisible(BuildContext context, String id) {
    context.get<CappTrackingService>().trackHomeDashboardEvent(
      event: KoyalEvent.homeDashboardSasBannerView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: 'sas_hero_banner',
      customDimensions: {
        TrackingProperties.propertyCdItemId: id,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (context.isFlagEnabledWatch(FeatureFlag.homeSasBannersListview)) {
      final listSpots = ['SPOT_HERO_01', 'SPOT_HERO_02', 'SPOT_HERO_03'];
      return Container(
        height: MediaQuery.of(context).size.height * 0.287,
        color: ColorTheme.of(context).backgroundColor,
        child: ListView(
          padding: const EdgeInsets.only(
            bottom: KoyalPadding.paddingNormal,
            top: KoyalPadding.paddingNormal,
          ),
          scrollDirection: Axis.horizontal,
          children: listSpots
              .mapWithIndex(
                (index, x) => Container(
                  padding: EdgeInsets.only(
                    right: index == listSpots.length - 1 ? KoyalPadding.paddingNormal : 0,
                    left: index == 0 ? KoyalPadding.paddingNormal : KoyalPadding.paddingSmall,
                  ),
                  child: _SasBanner(
                    spotId: x,
                    useNewDesign: useNewDesign,
                    onBecomeVisible: (id) => onBecomeVisible(context, id),
                    spotViewFactory: spotViewFactory,
                  ),
                ),
              )
              .toList(),
        ),
      );
    } else {
      if (spots.isNotEmpty) {
        return KoyalPadding.small(
          top: false,
          left: false,
          right: false,
          child: GFCarousel(
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 8),
            viewportFraction: 1.0,
            aspectRatio: 16 / 12.2,
            hasPagination: true,
            activeIndicator: Theme.of(context).primaryColor,
            items: spots
                .map(
                  (x) => KoyalPadding.normalAll(
                    child: Column(
                      children: [
                        _SasBanner(
                          spotId: x,
                          useNewDesign: useNewDesign,
                          onBecomeVisible: (id) => onBecomeVisible(context, id),
                          spotViewFactory: spotViewFactory,
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ),
        );
      } else {
        return KoyalShimmer(
          child: KoyalPadding.normalHorizontal(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 100,
                  height: 25,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
                KoyalPadding.normalVertical(
                  child: Container(
                    width: double.maxFinite,
                    height: 210,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }
  }
}

class _SasBanner extends StatefulWidget {
  final String spotId;
  final bool useNewDesign;
  final ISasSpotViewFactory spotViewFactory;
  final void Function(String id)? onBecomeVisible;

  const _SasBanner({
    Key? key,
    required this.spotId,
    this.useNewDesign = false,
    this.onBecomeVisible,
    required this.spotViewFactory,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _SasBannerState();
}

class _SasBannerState extends State<_SasBanner> with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    late Widget banner;
    if (widget.useNewDesign) {
      banner = AspectRatio(
        aspectRatio: 68 / 47,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: <Widget>[
              Positioned.fill(
                child: Transform.scale(
                  scale: 0.98,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: KoyalShimmer(
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: HciColors.supplementary0,
                      ),
                    ),
                  ),
                ),
              ),
              Positioned.fill(
                child: widget.spotViewFactory.createSpotView(key: Key(widget.spotId), spotId: widget.spotId),
              ),
            ],
          ),
        ),
      );
    } else {
      banner = KoyalElevation.koyal3Dp(
        child: AspectRatio(
          aspectRatio: 16 / 11,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: widget.spotViewFactory.createSpotView(key: Key(widget.spotId), spotId: widget.spotId),
          ),
        ),
      );
    }

    return VisibilityDetector(
      key: Key('__SasBanner_${widget.spotId}__'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction == 1) {
          widget.onBecomeVisible?.call(widget.spotId);
        }
      },
      child: banner,
    );
  }

  @override
  bool get wantKeepAlive => true;
}
