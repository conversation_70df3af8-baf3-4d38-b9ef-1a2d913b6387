import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_home_core.dart';
import 'home_login_button.dart';

class SignInAction extends StatelessWidget {
  final Color? textColor;
  const SignInAction({Key? key, this.textColor}) : super(key: key);

  @override
  Widget build(BuildContext context) => HomeLoginButton(
        key: const Key('__homeScreenAppBarLoginRegisterButton__'),
        text: L10nCappHome.of(context).loginRegister,
        onPressed: () {
          context.get<CappTrackingService>().trackEvent(
                event: KoyalEvent.homeDashboardLoginRegisterClick,
                eventCategory: KoyalTrackingCategories.homeDashboard,
                eventAction: KoyalAnalyticsConstants.click,
                eventLabel: KoyalTrackingLabels.loginRegister,
              );
          context.navigateToAuthEntryPoint();
        },
        textColor: textColor,
      );
}

class AuthorizedAction extends StatelessWidget {
  final Color iconColor;
  const AuthorizedAction({
    Key? key,
    required this.iconColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InboxButton(
      key: const Key('__homeScreenAppBarInboxButton__'),
      iconColor: iconColor,
    );
  }
}
