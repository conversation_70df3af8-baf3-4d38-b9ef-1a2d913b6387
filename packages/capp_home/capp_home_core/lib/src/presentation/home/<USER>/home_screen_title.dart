import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class HomeScreenTitle extends StatelessWidget {
  final Widget child;
  final String? title;
  final Widget? titleAction;
  final bool useNewDesign;
  final bool enableRightPadding;

  const HomeScreenTitle({
    super.key,
    required this.child,
    this.title,
    this.titleAction,
    this.useNewDesign = false,
    this.enableRightPadding = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (title != null)
          Padding(
            padding: EdgeInsets.only(
              right: enableRightPadding ? KoyalPadding.paddingNormal : 0,
              bottom: KoyalPadding.paddingSmall,
            ),
            child: SizedBox(
              height: 40,
              child: Row(
                children: [
                  if (useNewDesign)
                    KoyalText.subtitle1(
                      title!,
                      color: ColorTheme.of(context).defaultTextColor,
                      textAlign: TextAlign.left,
                    )
                  else
                    KoyalText.header6(
                      title!,
                      color: ColorTheme.of(context).defaultTextColor,
                      textAlign: TextAlign.left,
                    ),
                  if (titleAction != null) const Spacer(),
                  if (titleAction != null) titleAction!,
                ],
              ),
            ),
          ),
        child,
      ],
    );
  }
}
