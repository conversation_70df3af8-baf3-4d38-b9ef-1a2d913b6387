import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../widgets/dashboard/pending_actions_widget.dart';
import 'home_body.dart';

/// This class was extracted from home screen code base
class HomeSliverWrapper extends StatelessWidget {
  final List<Widget> children;
  final bool showHomeScreenRevamp;
  const HomeSliverWrapper({
    Key? key,
    required this.children,
    required this.showHomeScreenRevamp,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (context.isFlagEnabledWatch(FeatureFlag.pendingActionsFacelift) || showHomeScreenRevamp) {
      return SliverToBoxAdapter(
        child: HomeBody(children: children),
      );
    } else {
      return context.isFlagEnabledWatch(FeatureFlag.homePendingActionsNew) && children.isNotEmpty
          ? SliverStickyHeader.builder(
              builder: (context, constraints) => PendingActionsWidget(
                key: const ValueKey('__homeScreenPedningActionsNew__'),
                isPinned: constraints.isPinned,
              ),
              sliver: SliverToBoxAdapter(child: HomeBody(children: children)),
            )
          : SliverToBoxAdapter(child: HomeBody(children: children));
    }
  }
}
