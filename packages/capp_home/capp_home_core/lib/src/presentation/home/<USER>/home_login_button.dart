import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class HomeLoginButton extends TertiaryButton {
  final Color? textColor;
  const HomeLoginButton({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    bool isInProgress = false,
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(
      vertical: KoyalPadding.paddingXSmall,
      horizontal: KoyalPadding.paddingNormal,
    ),
    AlignmentGeometry? alignment,
    this.textColor,
  }) : super(
          key: key,
          text: text,
          onPressed: onPressed,
          isInProgress: isInProgress,
          padding: padding,
          alignment: alignment,
          height: 48.0,
        );

  @override
  Widget buttonText(BuildContext context) {
    // ignore: koyal-text
    Widget result = Text(
      text,
      style: TextStyleTheme.of(context).button1.copyWith(color: textColor),
      overflow: TextOverflow.ellipsis,
      textAlign: TextAlign.center,
    );
    if (alignment != null) {
      result = Align(
        alignment: alignment!,
        child: result,
      );
    }
    return result;
  }
}
