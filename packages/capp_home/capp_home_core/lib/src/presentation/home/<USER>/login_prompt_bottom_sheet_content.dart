import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_home_core.dart';

class LoginPromptBottomSheetContent extends StatelessWidget {
  const LoginPromptBottomSheetContent({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = L10nCappHome.of(context);
    return KoyalPadding.normalAll(
      child: Column(
        key: const Key('__qrAnonymousUserBottomSheet__'),
        mainAxisSize: MainAxisSize.min,
        children: [
          const AssetSvgImage('assets/svg/mascot/mascot_qr_locked.svg', package: 'capp_ui'),
          KoyalPadding.normalAll(
            top: false,
            left: false,
            right: false,
            child: MainHeading(
              title: l10n.qrLoginHeader,
              subtitle: l10n.qrLoginSubheader,
            ),
          ),
          //ignore: buttons-layout
          PrimaryButton(
            key: const Key('__qrBottomSheetLoginRegisterButton__'),
            text: l10n.qrLoginButton,
            onPressed: () {
              context.get<CappTrackingService>().trackEvent(
                    event: KoyalEvent.homeDashboardLoginRegisterClick,
                    eventCategory: KoyalTrackingCategories.homeDashboard,
                    eventAction: KoyalAnalyticsConstants.click,
                    eventLabel: KoyalTrackingLabels.loginRegister,
                  );
              Navigator.pop(context);
              context.navigateToSignInScreen();
            },
          ),
        ],
      ),
    );
  }
}
