import 'package:flutter/material.dart';

import 'pending_actions_widget.dart';

class PendingActionsHeaderDelegate extends SliverPersistentHeaderDelegate {
  PendingActionsHeaderDelegate();

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Center(
      child: PendingActionsWidget(
        key: const ValueKey('__homeScreenPedningActionsNew__'),
        isPinned: shrinkOffset > 20,
      ),
    );
  }

  @override
  double get maxExtent => 90.0;

  @override
  double get minExtent => 72.0;

  @override
  bool shouldRebuild(PendingActionsHeaderDelegate oldDelegate) => true;
}
