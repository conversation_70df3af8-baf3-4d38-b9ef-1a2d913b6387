import 'package:capp_content/capp_content.dart';
import 'package:capp_content_core/capp_content_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../l10n/i18n.dart';
import '../../utilities/home_padding_provider.dart';
import '../../utilities/home_screen_title.dart';

class FinancialLiteracy extends StatelessWidget {
  final bool navigateToTab;
  final String? title;
  const FinancialLiteracy({Key? key, required this.navigateToTab, this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) => BlocBuilder<BlogSectionBloc, BlogSectionState>(
        builder: (context, state) => state.hasError ? _buildEmpty(context) : _buildBody(context, state),
      );

  Widget _buildBody(BuildContext context, BlogSectionState state) {
    context.read<HomePaddingProvider>().isVisible = true;
    return (!state.isLoading && state.financialLiteracyBlogPosts.isNotEmpty)
        ? HomeScreenTitle(
            title: title ?? L10nCappHome.of(context).financialLiteracy,
            enableRightPadding: true,
            titleAction: TertiaryButton(
              key: const Key('__financialLiteracyViewAllButton__'),
              text: L10nCappContent.of(context).viewAll,
              padding: EdgeInsets.zero,
              onPressed: () {
                context.get<CappTrackingService>().trackHomeDashboardEvent(
                      event: KoyalEvent.homeDashboardFinanceTipsViewAllClick,
                      eventAction: KoyalAnalyticsConstants.click,
                      eventLabel: KoyalTrackingLabels.financeTipsViewAll,
                    );
                final args = BlogsArguments(initialCategoryId: state.financialLiteracyCategoryId);
                if (navigateToTab) {
                  context.navigator.toMainScreen(
                    arguments: MainScreenArguments(initialTab: TabItem.promo, arguments: args),
                  );
                } else {
                  context.navigator.pushFromPackage(
                    package: 'CappContent',
                    screen: 'BlogsScreen',
                    arguments: args,
                  );
                }
              },
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: state.isLoading
                    ? Iterable<int>.generate(6)
                        .map<Widget>((_) => _shimmer(context))
                        .intersperse(const SizedBox(width: KoyalPadding.paddingNormal))
                        .toList()
                    : state.financialLiteracyBlogPosts
                        .mapWithIndex<Widget>(
                          (index, x) => CappVisibilityDetector(
                            key: Key('FinancialLiteracyVisibilityDetector_${x.id}'),
                            onBecameVisible: () {
                              context.get<CappTrackingService>().trackHomeDashboardEvent(
                                event: KoyalEvent.homeDashboardFinancialLiteracyItemView,
                                eventAction: KoyalAnalyticsConstants.view,
                                eventLabel: KoyalTrackingLabels.financeTips,
                                customDimensions: {
                                  TrackingProperties.propertyCdItemId: x.id.toString(),
                                },
                              );
                            },
                            child: InkWell(
                              onTap: () {
                                context.get<CappTrackingService>().trackHomeDashboardEvent(
                                  event: KoyalEvent.homeDashboardFinancialLiteracyItemClick,
                                  eventAction: KoyalAnalyticsConstants.click,
                                  eventLabel: KoyalTrackingLabels.financeTips,
                                  customDimensions: {
                                    TrackingProperties.propertyCdItemId: x.id.toString(),
                                  },
                                );
                                context.navigator.pushFromPackage(
                                  package: 'CappContent',
                                  screen: 'BlogDetailsScreen',
                                  arguments: BlogDetailsArguments(blogId: x.id),
                                );
                              },
                              child: Padding(
                                padding: EdgeInsets.only(
                                  right: state.financialLiteracyBlogPosts.length - 1 == index
                                      ? KoyalPadding.paddingNormal
                                      : 0,
                                ),
                                child: KoyalElevation.koyal3Dp(
                                  child: Container(
                                    width: _cardWidth,
                                    height: _cardHeight,
                                    decoration: BoxDecoration(
                                      color: ColorTheme.of(context).appbarBackgroundColor,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: _imageHeight,
                                          width: _cardWidth,
                                          child: ClipRRect(
                                            borderRadius: const BorderRadius.only(
                                              topLeft: _radius,
                                              topRight: _radius,
                                            ),
                                            child: x.imageUrl.asImage(
                                              fit: BoxFit.cover,
                                              showShimmer: true,
                                              useOptimized: true,
                                            ),
                                          ),
                                        ),
                                        KoyalPadding.small(
                                          child: KoyalText.subtitle2(
                                            x.title ?? '',
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                        .intersperse(
                          const SizedBox(
                            width: KoyalPadding.paddingNormal,
                          ),
                        )
                        .toList(),
              ),
            ),
          )
        : _buildEmpty(context);
  }

  Widget _buildEmpty(BuildContext context) {
    context.read<HomePaddingProvider>().isVisible = false;
    return const SizedBox.shrink();
  }

  Widget _shimmer(BuildContext context) {
    return KoyalPadding.normalAll(
      right: false,
      top: false,
      bottom: false,
      child: KoyalElevation.koyal3Dp(
        child: Container(
          height: _cardHeight,
          decoration: BoxDecoration(
            color: ColorTheme.of(context).backgroundColor,
            borderRadius: const BorderRadius.all(_radius),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.only(topLeft: _radius, topRight: _radius),
                child: GreyShimmer(
                  child:
                      Container(height: _imageHeight, width: _cardWidth, color: ColorTheme.of(context).backgroundColor),
                ),
              ),
              KoyalPadding.small(
                child: GreyShimmer(
                  child: Container(height: 12, width: _cardWidth * 0.8, color: ColorTheme.of(context).backgroundColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

const _cardWidth = 216.0;
const _cardHeight = 166.0;
const _imageHeight = 110.0;
const _radius = Radius.circular(8);
