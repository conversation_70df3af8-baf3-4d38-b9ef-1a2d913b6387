import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_home_core.dart';
import '../common/background_wrapper.dart';

class PendingActionsWidget extends StatelessWidget {
  final bool isPinned;

  const PendingActionsWidget({Key? key, required this.isPinned}) : super(key: key);

  @override
  Widget build(BuildContext context) => BlocBuilder<PendingActionsBloc, PendingActionsState>(
        builder: (context, state) {
          final cappHomeSettings = context.get<CappHomeSettings>();
          return state.items.isNotEmpty
              ? CappVisibilityDetector(
                  key: const ValueKey('__homeScreenPedningActionsVisibilityDetector__'),
                  onBecameVisible: () => _pendingActionImpressionEvent(context, state.items),
                  child: BackgroundWrapper(
                    gradient: false,
                    child: FeatureFlagWidget(
                      featureName: FeatureFlag.pendingActionsFacelift,
                      disabled: PendingActionBanner(
                        showAppearAnimation: true,
                        animatedIcon: cappHomeSettings.animatedPendingActionIcon,
                        duration: const Duration(milliseconds: 175),
                        count: state.items.length,
                        model: state.items.first,
                        isPinned: isPinned,
                        onTap: () => _onPendingActionTap(
                          context,
                          state.items,
                          facelifted: false,
                        ),
                      ),
                      child: PendingAction(
                        items: state.items,
                        fixed: isPinned,
                        animationIcon: cappHomeSettings.animatedPendingActionIcon,
                        onTap: () => _onPendingActionTap(context, state.items, facelifted: true),
                      ),
                    ),
                  ),
                )
              : const SizedBox.shrink();
        },
      );

  void _onPendingActionTap(BuildContext context, List<PendingActionModel> items, {required bool facelifted}) {
    final productTypeInMetaData = _getProductTypeFromPA(items.first);
    final productType = productTypeInMetaData.isNotEmpty ? GaUtils.findProductType(productTypeInMetaData) : '';
    final loanType = productTypeInMetaData.isNotEmpty ? GaUtils.findLoanType(productTypeInMetaData) : '';

    // DAS032
    context.get<CappTrackingService>().trackAnalyticsEvent(
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: 'pending_Action',
      userPropertyMap: {
        TrackingProperties.propertyUserType: 'existing_user',
        TrackingProperties.propertyCdLoanType: loanType,
        TrackingProperties.propertyCdProductType: productType,
      },
    );

    if (items.length == 1) {
      final type = items.first.type;
      // LOO124
      if (type == PendingActionType.transactionSignature || type == PendingActionType.loanApplication) {
        context.get<CappTrackingService>().trackAnalyticsEvent(
          eventCategory: KoyalTrackingCategories.looPenAct,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: KoyalTrackingLabels.tileCk,
          userPropertyMap: {
            TrackingProperties.propertyUserType: 'existing_user',
            TrackingProperties.propertyCdLoanType: loanType,
            TrackingProperties.propertyCdProductType: productType,
            TrackingProperties.propertyPropDynamicV1: items.first.title(L10nKoyalShared.of(context)),
          },
        );
      }
      // LOO150
      if (type == PendingActionType.loanApplication) {
        context.get<CappTrackingService>().trackAnalyticsEvent(
          eventCategory: KoyalTrackingCategories.looWelcomeBack,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: KoyalTrackingLabels.letsStart,
          userPropertyMap: {
            TrackingProperties.propertyUserType: 'existing_user',
            TrackingProperties.propertyCdLoanType: loanType,
            TrackingProperties.propertyCdProductType: productType,
          },
        );
      }
      if (type == PendingActionType.loanPending) {
        context.get<CappTrackingService>().trackAnalyticsEvent(
              eventCategory: KoyalTrackingLabels.looPendingAction,
              eventAction: KoyalAnalyticsConstants.click,
              eventLabel: items.first.title(L10nKoyalShared.of(context)),
              event: KoyalEvent.looPendingActionView,
            );
      }
      context.read<PendingActionsBloc>().add(PendingActionsEvent.open(item: items.first));
    } else {
      context.navigator.pushFromPackage(
        package: 'CappConfig',
        screen: 'PendingActionsScreen',
        arguments: PendingActionsScreenArguments(facelifted: facelifted),
      );
    }
  }

  void _pendingActionImpressionEvent(BuildContext context, List<PendingActionModel> actions) {
    final productTypeInMetaData = _getProductTypeFromPA(actions.first);
    final productType = productTypeInMetaData.isNotEmpty ? GaUtils.findProductType(productTypeInMetaData) : '';
    final loanType = productTypeInMetaData.isNotEmpty ? GaUtils.findLoanType(productTypeInMetaData) : '';

    //DAS031
    context.get<CappTrackingService>().trackAnalyticsEvent(
      eventCategory: KoyalTrackingCategories.homeDashboard,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: 'pending_Action',
      userPropertyMap: {TrackingProperties.propertyUserType: 'existing_user'},
    );

    if (actions.length == 1) {
      final type = actions.first.type;
      // LOO122
      if (type == PendingActionType.transactionSignature) {
        context.get<CappTrackingService>().trackAnalyticsEvent(
              eventCategory: KoyalTrackingCategories.screenView,
              eventAction: KoyalAnalyticsConstants.view,
              eventLabel: KoyalTrackingLabels.looPendingAction,
              userPropertyMap: {
                TrackingProperties.propertyUserType: 'existing_user',
                TrackingProperties.propertyCdLoanType: loanType,
                TrackingProperties.propertyCdProductType: productType,
              },
              event: KoyalEvent.looPendingActionView,
            );
      }
      // LOO149
      if (type == PendingActionType.loanApplication) {
        context.get<CappTrackingService>().trackAnalyticsEvent(
              eventCategory: KoyalTrackingCategories.screenView,
              eventAction: KoyalAnalyticsConstants.view,
              eventLabel: KoyalTrackingLabels.looWelcomeBack,
              userPropertyMap: {TrackingProperties.propertyUserType: 'existing_user'},
              event: KoyalEvent.looWelcomeBackView,
            );
      }
      if (type == PendingActionType.loanPending) {
        context.get<CappTrackingService>().trackAnalyticsEvent(
              eventCategory: KoyalTrackingCategories.screenView,
              eventAction: KoyalAnalyticsConstants.view,
              eventLabel: '${KoyalTrackingLabels.looPendingAction}_${actions.first.title(L10nKoyalShared.of(context))}',
              event: KoyalEvent.looPendingActionView,
            );
      }
      // LOO123
      if (type == PendingActionType.transactionSignature || type == PendingActionType.loanApplication) {
        context.get<CappTrackingService>().trackAnalyticsEvent(
          eventCategory: KoyalTrackingCategories.looPenAct,
          eventAction: KoyalTrackingActions.view,
          eventLabel: KoyalTrackingLabels.tileVw,
          userPropertyMap: {
            TrackingProperties.propertyUserType: 'existing_user',
            TrackingProperties.propertyCdLoanType: loanType,
            TrackingProperties.propertyCdProductType: productType,
            TrackingProperties.propertyPropDynamicV1: actions.first.title(L10nKoyalShared.of(context)),
          },
        );
      }
    }
  }

  String _getProductTypeFromPA(PendingActionModel? pendingActionModel) {
    if (pendingActionModel != null && pendingActionModel.metadata != null && pendingActionModel.metadata!.isNotEmpty) {
      return pendingActionModel.metadata!['productType'] ?? '';
    } else {
      return '';
    }
  }
}
