import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'home_padding_provider.dart';
import 'home_screen_padding.dart';

/// Widget responsible for adding default padding for widget at home screen
/// It also cares about visibility changes
class HomeBody extends StatelessWidget {
  final List<Widget> children;
  const HomeBody({super.key, required this.children});

  @override
  Widget build(BuildContext context) {
    children.removeWhere(
      (element) => element is SizedBox || (element is HomeScreenPadding) && (element as HomeScreenPadding).isDisabled,
    );
    return Column(
      children: children
          .map(
            (e) => (e is HomeScreenPadding) ? _SingleChildWrapper(child: e) : e,
          )
          .toList(),
    );
  }
}

class _SingleChildWrapper extends StatelessWidget {
  final Widget child;

  const _SingleChildWrapper({
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<HomePaddingProvider>(
      create: (context) => HomePaddingProvider(),
      child: Consumer<HomePaddingProvider>(
        builder: (context, value, child) => Offstage(
          offstage: !value.isVisible,
          child: child,
        ),
        child: Padding(
          padding: EdgeInsets.only(
            top: KoyalPadding.paddingSmall,
            left: KoyalPadding.paddingNormal,
            right: _disableRight ? 0 : KoyalPadding.paddingNormal,
          ),
          child: child,
        ),
      ),
    );
  }

  bool get _disableRight => child is HomeScreenPadding && (child as HomeScreenPadding).disableRight;
}
