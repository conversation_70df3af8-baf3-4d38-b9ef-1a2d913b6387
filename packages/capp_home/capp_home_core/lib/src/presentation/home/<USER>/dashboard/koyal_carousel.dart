import 'dart:async';

import 'package:flutter/material.dart';

class <PERSON>yalCarousel extends StatefulWidget {
  final List<Widget> items;
  final Function(int) onUserChangedPage;
  final Function(int)? onItemDisplayed;

  const KoyalCarousel({
    super.key,
    required this.items,
    required this.onUserChangedPage,
    this.onItemDisplayed,
  });

  @override
  State<KoyalCarousel> createState() => _KoyalCarouselState();
}

class _KoyalCarouselState extends State<KoyalCarousel> {
  final PageController _pageController = PageController();
  Timer? _timer;
  int _currentIndex = 0;
  bool _isUserInteraction = false;

  @override
  void initState() {
    super.initState();
    _startAutoScroll();

    // call onItemDisplayed for the first banner
    if (widget.items.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onItemDisplayed?.call(_currentIndex);
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_pageController.hasClients) {
        _isUserInteraction = false;
        _pageController.animateToPage(
          (_pageController.page!.toInt() + 1) % widget.items.length,
          duration: const Duration(milliseconds: 775),
          curve: Curves.fastOutSlowIn,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      children: <Widget>[
        AspectRatio(
          aspectRatio: 2,
          child: NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              if (notification is ScrollStartNotification && notification.dragDetails != null) {
                _isUserInteraction = true;
              }
              return false;
            },
            child: widget.items.length == 1
                ? widget.items[0]
                : PageView.builder(
                    controller: _pageController,
                    itemBuilder: (context, index) {
                      return widget.items[index % widget.items.length];
                    },
                    onPageChanged: (index) {
                      if (_isUserInteraction) {
                        widget.onUserChangedPage(index);
                      }
                      widget.onItemDisplayed?.call(index % widget.items.length);
                      setState(() {
                        _currentIndex = index % widget.items.length;
                      });
                    },
                  ),
          ),
        ),
        if (widget.items.length > 1)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.items.length,
              (index) {
                return GestureDetector(
                  excludeFromSemantics: true,
                  onTap: () {
                    _isUserInteraction = true;
                    _pageController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.fastOutSlowIn,
                    );
                  },
                  child: Container(
                    width: 8.0,
                    height: 16.0,
                    margin: const EdgeInsets.symmetric(
                      vertical: 16.0,
                      horizontal: 3,
                    ),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentIndex == index ? Theme.of(context).primaryColor : Colors.grey,
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}
