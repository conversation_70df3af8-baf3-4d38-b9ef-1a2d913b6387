import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../../capp_home_core.dart';

class PosLoanBanner extends StatefulWidget {
  final String? title;
  const PosLoanBanner({
    Key? key,
    this.title,
  }) : super(key: key);

  @override
  State<PosLoanBanner> createState() => _PosLoanBannerState();
}

class _PosLoanBannerState extends State<PosLoanBanner> {
  late Image placeholder;

  @override
  void initState() {
    super.initState();
    placeholder = Image.asset('assets/images/empty.png', fit: BoxFit.fill);
    BlocProvider.of<PosLoanBannerBloc>(context).add(const PosLoanBannerEvent.initLoading());
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width - 24;
    final height = width * 408 / 1080;

    return BlocBuilder<PosLoanBannerBloc, PosLoanBannerState>(
      key: const Key('__PosLoanBanner__'),
      builder: (context, state) => state.isError
          ? const SizedBox.shrink()
          : CappVisibilityDetector(
              key: const Key('__posLoanBannerVisibilityDetector'),
              onBecameVisible: () {
                context.get<CappTrackingService>().trackEvent(
                      event: KoyalEvent.homeDashboardPosLoanBannerView,
                      eventCategory: KoyalTrackingCategories.homeDashboard,
                      eventAction: KoyalAnalyticsConstants.view,
                      eventLabel: 'pos_loan_banner',
                    );
              },
              child: KoyalPadding.small(
                bottom: false,
                left: false,
                right: false,
                child: HomeScreenWidgetPadding(
                  title: widget.title ?? L10nCappHome.of(context).posLoanBannerTitle,
                  isChildPaddingBot: false,
                  child: state.isLoading
                      ? KoyalPadding.normalHorizontal(
                          child: ClipRRect(
                            borderRadius: const BorderRadius.all(Radius.circular(8)),
                            child: GreyShimmer(
                              child: Container(
                                width: double.infinity,
                                height: height,
                                color: ColorTheme.of(context).backgroundColor,
                              ),
                            ),
                          ),
                        )
                      : KoyalElevation.koyal3Dp(
                          child: ClipRRect(
                            borderRadius: const BorderRadius.all(Radius.circular(8)),
                            child: SizedBox(
                              width: double.infinity,
                              height: height,
                              child: Stack(
                                children: <Widget>[
                                  Positioned.fill(
                                    child: state.bannerImageUrl.asImage(
                                      fit: BoxFit.fill,
                                      precachedPlaceholderImage: placeholder,
                                      showShimmer: true,
                                    ),
                                  ),
                                  Positioned.fill(
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: () {
                                          context.get<CappTrackingService>().trackEvent(
                                                event: KoyalEvent.homeDashboardPosLoanBannerClick,
                                                eventCategory: KoyalTrackingCategories.homeDashboard,
                                                eventAction: KoyalAnalyticsConstants.click,
                                                eventLabel: 'pos_loan_banner',
                                              );
                                          final link = state.bannerLink ?? '';
                                          context.get<IDeeplinkService>().deeplinkOrLaunch(
                                                link,
                                                context,
                                              );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                ),
              ),
            ),
    );
  }
}
