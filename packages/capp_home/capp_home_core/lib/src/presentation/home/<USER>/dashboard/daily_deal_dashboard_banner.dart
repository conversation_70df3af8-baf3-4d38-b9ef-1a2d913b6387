import 'package:capp_domain/capp_domain.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../application/home/<USER>';
import '../common/home_screen_bottom_padding.dart';
import 'koyal_carousel.dart';

class DailyDealDashboardBanner extends StatefulWidget {
  const DailyDealDashboardBanner({
    Key? key,
  }) : super(key: key);

  @override
  State<DailyDealDashboardBanner> createState() => _DailyDealDashboardBannerState();
}

class _DailyDealDashboardBannerState extends State<DailyDealDashboardBanner> {
  late Image placeholder;
  List<int> viewedBannerIndexes = [];

  @override
  void initState() {
    super.initState();
    placeholder = Image.asset('assets/images/empty.png', fit: BoxFit.fill);
  }

  @override
  Widget build(BuildContext context) => BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          if (state.areBannersLoading) {
            return const _BannerLoading();
          } else if (state.contentBanners.isEmpty && !state.areBannersLoading) {
            return const SizedBox.shrink();
          }

          final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
          final imageWidth = MediaQuery.of(context).size.width.toInt();
          final cacheWidth = imageWidth * devicePixelRatio;

          final images = state.contentBanners
              .mapWithIndex(
                (index, banner) => banner.image?.url.asImage(
                  precachedPlaceholderImage: placeholder,
                  fit: BoxFit.fill,
                  cache: true,
                  resizeCacheWidth: cacheWidth.toInt(),
                ),
              )
              .where((i) => i != null)
              .toList();

          return Container(
            color: ColorTheme.of(context).backgroundColor,
            child: HomeScreenBottomPadding(
              child: Column(
                children: [
                  Column(children: images.map((e) => Offstage(child: e)).toList()),
                  KoyalCarousel(
                    items: state.contentBanners
                        .mapWithIndex(
                          (index, banner) => InkWell(
                            key: const Key('__homeScreenDailyDealBanner__'),
                            onTap: () {
                              _trackClickEvents(context, index, banner);

                              final link = banner.link ?? '';
                              context.get<IDeeplinkService>().deeplinkOrLaunch(
                                    link,
                                    context,
                                  );
                            },
                            child: images[index],
                          ),
                        )
                        .toList(),
                    onItemDisplayed: (index) {
                      final banner = state.contentBanners[index % state.contentBanners.length];
                      _trackViewEvents(context, index % state.contentBanners.length, banner);
                    },
                    onUserChangedPage: (index) {
                      final banner = state.contentBanners[index % state.contentBanners.length];

                      context.read<HomeBloc>().add(HomeEvent.bannerSelected(index % state.contentBanners.length));
                      _trackUserChangedPageEvent(context, index % state.contentBanners.length, banner);
                    },
                  ),
                ],
              ),
            ),
          );
        },
      );

  void _trackViewEvents(BuildContext context, int index, ContentBanner banner) {
    if (!viewedBannerIndexes.contains(index)) {
      viewedBannerIndexes.add(index);
      context.get<CappTrackingService>().trackEvent(
        event: KoyalEvent.homeDashboardPromotionBannerView,
        eventCategory: KoyalTrackingCategories.homeDashboard,
        eventAction: KoyalAnalyticsConstants.view,
        eventLabel: KoyalTrackingLabels.promotionBanner,
        customDimensions: {
          TrackingProperties.propertyCdItemId: '${banner.id}',
          TrackingProperties.propertyCdItemIndex: '${index + 1}',
        },
      );
    }
  }

  void _trackUserChangedPageEvent(BuildContext context, int index, ContentBanner banner) =>
      context.get<CappTrackingService>().trackEvent(
        event: KoyalEvent.homeDashboardPromotionBannerSwipe,
        eventCategory: KoyalTrackingCategories.homeDashboard,
        eventAction: KoyalAnalyticsConstants.swipe,
        eventLabel: KoyalTrackingLabels.promotionBanner,
        customDimensions: {
          TrackingProperties.propertyCdItemId: '${banner.id}',
          TrackingProperties.propertyCdItemIndex: '${index + 1}',
        },
      );

  void _trackClickEvents(BuildContext context, int index, ContentBanner banner) =>
      context.get<CappTrackingService>().trackEvent(
        event: KoyalEvent.homeDashboardPromotionBannerClick,
        eventCategory: KoyalTrackingCategories.homeDashboard,
        eventAction: KoyalAnalyticsConstants.click,
        eventLabel: KoyalTrackingLabels.promotionBanner,
        customDimensions: {
          TrackingProperties.propertyCdItemId: '${banner.id}',
          TrackingProperties.propertyCdItemIndex: '${index + 1}',
        },
      );
}

class _BannerLoading extends StatelessWidget {
  const _BannerLoading({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    return HomeScreenBottomPadding(
      child: KoyalShimmer(
        child: KoyalPadding.normalAll(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 100,
                height: 25,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                ),
              ),
              KoyalPadding.normalVertical(
                child: Container(
                  width: width,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
