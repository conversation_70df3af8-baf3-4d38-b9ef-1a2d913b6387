import 'package:capp_content/capp_content.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../../../capp_home_core.dart';

class BlogSectionWithTitle extends StatelessWidget implements HomeScreenPadding {
  final String? title;
  final int numberOfPosts;
  final CustomDeeplinkHandler? customDeeplinkHandler;
  const BlogSectionWithTitle({
    Key? key,
    this.title,
    required this.numberOfPosts,
    this.customDeeplinkHandler,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => BlogSection(
        key: const Key('__homeScreenBlogSection__'),
        title: KoyalText.header6(
          color: ColorTheme.of(context).defaultTextColor,
          title ?? L10nCappHome.of(context).tipsAndStories,
        ),
        numberOfPosts: numberOfPosts,
        isHomeDashboard: true,
        customDeeplinkHandler: customDeeplinkHandler,
        onVisibilityChange: ({required isVisible}) => context.read<HomePaddingProvider>().isVisible = isVisible,
      );

  @override
  bool get disableRight => false;

  @override
  bool get isDisabled => false;
}
