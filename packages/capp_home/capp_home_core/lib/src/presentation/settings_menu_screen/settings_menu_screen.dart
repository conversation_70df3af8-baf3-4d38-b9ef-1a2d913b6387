// ignore_for_file: use_build_context_synchronously

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_personal_core/capp_personal_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigator/navigator.dart';

import '../../../capp_home_core.dart';
import '../../application/change_password/change_password_bloc.dart';
import '../tracking/privacy_settings_tracking.dart';

const _changePasswordFlow = ChangePasswordFlow.settings;

class SettingsMenuScreen extends StatelessWidget with RouteWrapper {
  final bool requireFaceGuardChangePassword;

  const SettingsMenuScreen({
    super.key,
    this.requireFaceGuardChangePassword = false,
  });

  @override
  Widget wrappedRoute(BuildContext context) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: context.get<AccountMenuBloc>()..add(const AccountMenuEvent.init())),
          BlocProvider(
            create: (context) => context.get<EnableBiometricCubit>()..init(),
          ),
          BlocProvider.value(value: context.get<ChangePasswordBloc>()),
        ],
        child: this,
      );

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ChangePasswordBloc, ChangePasswordState>(
          listenWhen: (previous, current) => current is ChangePasswordLoadSuccess && current.passwordResponse != null,
          listener: (context, state) {
            ChangePinHandler.navigateToChangePin(
              context: context,
              sessionId: (state as ChangePasswordLoadSuccess).passwordResponse?.sessionId,
              flow: _changePasswordFlow,
            );
          },
        ),
        // BlocListener<ChangePasswordBloc, ChangePasswordState>(
        //   listenWhen: (previous, current) => previous != current,
        //   listener: (context, state) {
        //     switch (state) {
        //       case ChangePasswordFailure():
        //         _handleChangePasswordError(context, state);
        //         break;
        //       case ChangePasswordSuccess():
        //         Navigator.of(context).pop();
        //         break;
        //       default:
        //         dialogWithProgressIndicator(context);
        //         break;
        //     }
        //   },
        // ),
      ],
      child: BlocConsumer<ChangePasswordBloc, ChangePasswordState>(
        listenWhen: (previous, current) => previous != current,
        listener: (context, state) {
          switch (state) {
            case ChangePasswordFailure():
              _handleChangePasswordError(context, state);
              break;
            case ChangePasswordLoadSuccess():
              Navigator.of(context).pop();
              break;
            default:
              dialogWithProgressIndicator(context);
              break;
          }
        },
        builder: (context, passwdState) {
          return KoyalScaffold(
            key: const Key('__settingsMenuScreen__'),
            appBar: KoyalAppBar(
              title: L10nCappHome.of(context).menuSettings,
            ),
            body: BlocBuilder<AccountMenuBloc, AccountMenuState>(
              builder: (context, state) {
                return switch (state) {
                  AccountMenuLoadSuccess() => _buildBody(context, state, passwdState),
                  _ => const Center(child: KoyalProgressIndicator.large()),
                };
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, AccountMenuLoadSuccess state, ChangePasswordState passwdState) {
    final locState = context.read<LocalizationBloc>().state;
    final showDisableUser = context.isFlagEnabledWatch(FeatureFlag.disableUser);
    final sessionId = passwdState is ChangePasswordLoadSuccess ? passwdState.passwordResponse?.sessionId : null;
    final menuItems = buildMenuItems(
      context,
      state,
      showChangePassword: context.isFlagEnabledWatch(FeatureFlag.profileMenuChangePassword) && !state.isAnonymous,
      showLanguageSelection:
          locState.supportedLocales.length + (state.isInsider ? locState.insiderLocales.length : 0) > 1,
      showPreferredCommunication:
          context.isFlagEnabledWatch(FeatureFlag.profileMenuPreferredCommunication) && state.isClient,
      showDisableUser: showDisableUser,
      passwordSessionId: sessionId,
    );
    return MenuListView(items: showDisableUser ? _addDividerForTheLastItem(menuItems) : menuItems);
  }

  @protected
  ListMenuItem buildBiometricSettingItem({
    required SettingsMenuArguments? arguments,
    required BuildContext context,
  }) {
    return ListMenuItem(
      title: L10nCappHome.of(context).menuSettingsBiometric,
      icon: Icon(
        KoyalIcons.lock_open_outline,
        color: ColorTheme.of(context).primaryColor,
      ),
      onTap: (c) async {
        final cubit = context.read<EnableBiometricCubit>();
        if (cubit.state == true) {
          trackBiometricSettingsOffClick(context);
          await cubit.changeBiometricEnabled(enabled: false);
        } else {
          final biometricAvailable =
              (await context.get<IBiometricProvider>().availableBiometric()).fold((l) => false, (r) => true);
          if (biometricAvailable) {
            trackBiometricSettingsOnClick(context);
            await cubit.changeBiometricEnabled(enabled: true);
          }
        }
        final isEnabled = cubit.state ?? false;
        arguments?.gamificationCallback?.call(isEnabled: isEnabled);
      },
      trailing: BlocBuilder<EnableBiometricCubit, bool?>(
        builder: (context, state) {
          return SlideToggle(
            key: UniqueKey(),
            onChanged: (value) async {
              final cubit = context.read<EnableBiometricCubit>()..temporarilyEnable();
              if (value == false) {
                await cubit.changeBiometricEnabled(enabled: false);
              } else {
                final biometricAvailable =
                    (await context.get<IBiometricProvider>().availableBiometric()).fold((l) => false, (r) => true);
                if (biometricAvailable) {
                  await cubit.changeBiometricEnabled(enabled: true);
                }
              }
              final isEnabled = cubit.state ?? false;
              arguments?.gamificationCallback?.call(isEnabled: isEnabled);
            },
            value: state ?? false,
          );
        },
      ),
    );
  }

  List<ListMenuItem> buildMenuItems(
    BuildContext context,
    AccountMenuLoadSuccess state, {
    required bool showChangePassword,
    required bool showLanguageSelection,
    required bool showPreferredCommunication,
    required bool showDisableUser,
    SettingsMenuArguments? arguments,
    String? passwordSessionId,
  }) {
    final result = [
      if (showChangePassword)
        ListMenuItem(
          key: const Key('__changePasswordRow__'),
          title: L10nCappHome.of(context).menuSettingsChangePasswordPin,
          onTap: (_) async {
            _trackEvent(context, 'change_password');
            if (context.isFlagEnabledRead(FeatureFlag.changePasswordOtp)) {
              context.read<ChangePasswordBloc>().add(const ChangePasswordEvent.init());
            } else {
              await ChangePinHandler.navigateToChangePin(
                context: context,
                flow: _changePasswordFlow,
                sessionId: passwordSessionId,
                cleartextPassword: context.isFlagEnabledRead(FeatureFlag.cleartextPassword),
              );
            }
          },
          icon: Icon(
            KoyalIcons.lock_closed_outline,
            color: ColorTheme.of(context).primaryColor,
          ),
        ),
      if (showLanguageSelection)
        ListMenuItem(
          key: const Key('__selectLanguageRow__'),
          title: L10nCappHome.of(context).menuSettingsChangeLanguage,
          icon: Icon(
            KoyalIcons.earth_outline,
            color: ColorTheme.of(context).primaryColor,
          ),
          onTap: (_) {
            _trackEvent(context, 'select_language');

            context.navigator.push(
              path: context.isFlagEnabledRead(FeatureFlag.languageSelectionScreen)
                  ? NavigatorPath.cappPersonalCore.langaugeSelectionScreen
                  : NavigatorPath.cappPersonalCore.changeLanguageScreen,
            );
          },
        ),
      if (showPreferredCommunication)
        ListMenuItem(
          key: const Key('__preferredCommunicationRow__'),
          title: L10nCappHome.of(context).menuSettingsPreferredCommunication,
          icon: Icon(
            KoyalIcons.mail_open_outline,
            color: ColorTheme.of(context).primaryColor,
          ),
          onTap: (_) {
            context.navigator.push(path: NavigatorPath.cappPersonalCore.preferredCommunicationScreen);
          },
        ),
      if (context.isFlagEnabledWatch(FeatureFlag.biometricUnlock) && state.isAnonymous == false)
        buildBiometricSettingItem(context: context, arguments: arguments),
      if (context.isFlagEnabledWatch(FeatureFlag.productionEnrollSetting))
        ListMenuItem(
          title: L10nCappHome.of(context).enableLoanOffer,
          icon: Icon(
            KoyalIcons.in_store_payment_outline,
            color: ColorTheme.of(context).primaryColor,
          ),
          onTap: (c) {
            _trackEvent(context, 'enable_loan_offer');
            context.navigator.push(
              path: NavigatorPath.cappLoanOriginationUnified.productionEnrollScreen,
            );
          },
        ),
      if (context.isFlagEnabledWatch(FeatureFlag.permissionsEnabled))
        ListMenuItem(
          key: const Key('__permissionsRow__'),
          title: L10nCappHome.of(context).permissions,
          icon: Icon(KoyalIcons.permissions, color: ColorTheme.of(context).primaryColor),
          onTap: (_) {
            context.navigator.pushFromPackage(package: 'CappHome', screen: 'PermissionsScreen');
          },
        ),
      // Privacy settings menu item
      if (context.isFlagEnabledWatch(FeatureFlag.privacyEntryPointPh) && !state.isAnonymous)
        ListMenuItem(
          key: const Key('__privacySettingsRow__'),
          title: L10nCappHome.of(context).menuSettingsPrivacySettings,
          icon: Icon(KoyalIcons.shield_checkmark_outline, color: ColorTheme.of(context).primaryColor),
          onTap: (_) {
            context.get<CappTrackingService>().trackCappEvent(PrivacySettingsMenuItemClick());
            context.navigator.pushFromPackage(package: 'CappHome', screen: 'PrivacySettingsScreen');
          },
        ),
      if (showDisableUser)
        ListMenuItem(
          thickDivider: true,
          key: const Key('__disableUserRow__'),
          title: L10nCappHome.of(context).menuSettingsDisableUser,
          icon: Icon(
            KoyalIcons.trash_outline,
            color: ColorTheme.of(context).primaryColor,
          ),
          onTap: (_) {
            context.navigator.push(
              path: NavigatorPath.cappPersonal.disableUserScreen,
              arguments: DisableUserScreenArguments(
                isAnonymous: state.isAnonymous,
                withSelfie: !state.isProspect && !state.isAnonymous,
              ),
            );
          },
        ),
    ];

    return result;
  }

  List<ListMenuItem> _addDividerForTheLastItem(List<ListMenuItem> list) {
    if (list.length > 1) {
      list[list.length - 2].thickDivider = true;
    }
    return list;
  }

  void _trackEvent(BuildContext context, String label) => context.get<CappTrackingService>().trackClickEvent(
        eventCategory: 'user_action_details',
        eventLabel: label,
      );

  @protected
  void trackBiometricSettingsOnClick(BuildContext context) {}
  @protected
  void trackBiometricSettingsOffClick(BuildContext context) {}

  void _handleChangePasswordError(BuildContext context, ChangePasswordFailure state) {
    context.navigator.pop();
    switch (state.failure.type) {
      case UserVerificationFailureType.permanentlyBlocked: // 403
        showBosAuthenticationLimitReachedDialog(
          context,
          L10nCappPersonal.of(context).changeEmailAttemptsReachedDialogSubtitle,
        );
        break;
      case UserVerificationFailureType.sessionStartsRateExceeded: // 429
        showAuthErrorChangePersonalDetailsUnavailable(
          context,
          blockedUntil: state.failure.header,
          flowType: FlowType.password,
        );
        break;
      default:
        showAuthErrorUnexpectedErrorDialog(context);
        break;
    }
  }
}
