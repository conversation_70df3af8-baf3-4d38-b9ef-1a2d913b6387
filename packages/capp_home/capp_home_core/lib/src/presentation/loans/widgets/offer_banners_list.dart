import 'package:capp_domain/capp_domain.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_loan_shared_core/capp_loan_shared_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_home_core.dart';
import '../no_offers_view.dart';

class OfferBannersList extends StatelessWidget {
  final bool showNearestPartnerShopButton;
  final bool showScoringInputFromDeepLink;
  final List<OfferModel> bnplModels;
  final bool isLoading;

  const OfferBannersList({
    Key? key,
    required this.showNearestPartnerShopButton,
    required this.showScoringInputFromDeepLink,
    required this.bnplModels,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OfferBannerBloc, OfferBannerState>(
      builder: (context, offerBannerState) {
        return OfferBanners(
          isHorizontal: false,
          emptyView: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: NoOffersView(showNearestPartnerShopButton: showNearestPartnerShopButton),
          ),
          showIncomeBanner: true,
          bannerTrackingType: BannerTrackingType.loanDashboard,
          incomeOffer: offerBannerState.incomeOffer,
          phoneNumber: offerBannerState.phoneNumber,
          loanOffers: <OfferModel>[] + offerBannerState.loanOffers + bnplModels,
          showConsent: context.get<CappHomeSettings>().showBannerConset,
          isLoading: isLoading || offerBannerState.isLoading,
          showScoringInputFromDeepLink: showScoringInputFromDeepLink,
          trackEvents: offerBannerState.trackEvents,
          isError: offerBannerState.isError,
        );
      },
    );
  }
}
