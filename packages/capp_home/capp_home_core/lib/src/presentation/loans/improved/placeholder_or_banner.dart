import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_home_core.dart';

class PlaceholderOrBanner extends StatelessWidget {
  final VoidCallback? onPressed;
  const PlaceholderOrBanner({Key? key, this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) => BlocBuilder<PendingActionsBloc, PendingActionsState>(
        builder: (context, state) {
          final filteredActions = state.items.where((i) => i.type == PendingActionType.loanApplication).toList();
          return filteredActions.isEmpty
              ? ImprovedPlaceholder(onPressed: onPressed)
              : _buildPendingAction(context, filteredActions);
        },
      );

  Widget _buildPendingAction(BuildContext context, List<PendingActionModel> actions) => FeatureFlagWidget(
        featureName: FeatureFlag.pendingActionsFacelift,
        disabled: PendingActionBanner(
          showAppearAnimation: true,
          animatedIcon: context.get<CappHomeSettings>().animatedPendingActionIcon,
          duration: const Duration(milliseconds: 175),
          count: actions.length,
          model: actions.first,
          onTap: () => actions.length == 1
              ? context.read<PendingActionsBloc>().add(PendingActionsEvent.open(item: actions.first))
              : context.navigator.pushFromPackage(package: 'CappConfig', screen: 'PendingActionsScreen'),
        ),
        child: PendingAction(
          items: actions,
          animationIcon: context.get<CappHomeSettings>().animatedPendingActionIcon,
          onTap: () => actions.length == 1
              ? context.read<PendingActionsBloc>().add(PendingActionsEvent.open(item: actions.first))
              : context.navigator.pushFromPackage(package: 'CappConfig', screen: 'PendingActionsScreen'),
        ),
      );
}
