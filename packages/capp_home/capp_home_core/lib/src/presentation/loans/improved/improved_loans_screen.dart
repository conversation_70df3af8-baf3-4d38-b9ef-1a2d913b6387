import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_home_core.dart';
import 'placeholder_or_banner.dart';

class ImprovedLoansScreen extends StatelessWidget with RouteWrapper {
  const ImprovedLoansScreen({Key? key}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => MultiBlocProvider(
        providers: [
          BlocProvider<LoansBloc>.value(
            value: context.get<LoansBloc>()..add(const LoansEvent.init()),
          ),
          BlocProvider<InstallmentOverviewBloc>.value(value: context.get<InstallmentOverviewBloc>()),
        ],
        child: this,
      );

  @override
  Widget build(BuildContext context) => KoyalScaffold(
        appBar: KoyalAppBar(title: L10nCappHome.of(context).cappMainFinanceAppbarTitle),
        safeAreaCustomSettings: const SafeAreaCustomSettings(bottom: false),
        safeAreaBarCustomSettings: const SafeAreaCustomSettings(bottom: false),
        body: SingleChildScrollView(
          child: BlocBuilder<LoansBloc, LoansState>(
            builder: (context, loansState) => BlocBuilder<InstallmentOverviewBloc, InstallmentOverviewState>(
              builder: (context, installmentState) => Column(
                children: [
                  MyLoansHeader(
                    hasContractHistoryButton:
                        loansState.signOnStatus == SignOnStatus.client && installmentState.finishedCards.isNotEmpty,
                  ),
                  BlocBuilder<PendingActionsBloc, PendingActionsState>(
                    builder: (context, _) => buildContent(context, loansState.signOnStatus),
                  ),
                  SectionHeading(title: L10nCappHome.of(context).cappMainOffersForYou),
                  CountryActionBelt(
                    key: const Key('__loansScreenActionBelt__'),
                    buildActionBeltItems: context.get<CappHomeSettings>().buildActionBeltItems,
                  ),
                  const BottomSpace(),
                ],
              ),
            ),
          ),
        ),
      );

  Widget buildContent(BuildContext context, SignOnStatus status) {
    switch (status) {
      case SignOnStatus.anonymous:
        return PlaceholderOrBanner(onPressed: () => navigateToLoanJourney(context));
      case SignOnStatus.registered:
        return PlaceholderOrBanner(onPressed: () => navigateToLoanJourney(context));
      case SignOnStatus.client:
        return ImprovedCustomerContent(onPressed: () => navigateToLoanJourney(context));
    }
  }

  void navigateToLoanJourney(BuildContext context) => context.navigator.pushFromPackage(
        package: 'CappLoanOrigination',
        screen: 'GetLoanWizard',
      );
}
