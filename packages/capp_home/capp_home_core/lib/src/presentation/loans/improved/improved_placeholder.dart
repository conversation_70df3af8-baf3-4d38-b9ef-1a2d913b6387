import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../capp_home_core.dart';
import '../loans_placeholder.dart';

class ImprovedPlaceholder extends StatelessWidget {
  final VoidCallback? onPressed;

  const ImprovedPlaceholder({Key? key, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) => Column(
        children: [
          LoansPlaceholder(
            imagePath: 'assets/images/contract.svg',
            title: L10nCappHome.of(context).cappMainEmptyLoansTitle,
            subtitle: L10nCappHome.of(context).cappMainNoLoansSubtitle,
          ),
          const SizedBox(height: 12),
          // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
          //ignore: no-direct-padding
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 60),
            // TODO(Unknown): Please check if we can wrap PrimaryButton by VerticalButtonsLayout/HorizontalButtonsLayout here
            //ignore: buttons-layout
            child: onPressed == null
                ? const SizedBox.shrink()
                : PrimaryButton(
                    text: L10nCappHome.of(context).cappMainSelectLoan,
                    onPressed: onPressed,
                  ),
          ),
        ],
      );
}
