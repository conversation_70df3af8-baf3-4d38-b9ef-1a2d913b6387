import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class LoansPlaceholder extends StatelessWidget {
  final String imagePath;
  final String title;
  final String subtitle;

  const LoansPlaceholder({
    Key? key,
    required this.imagePath,
    required this.title,
    this.subtitle = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AssetSvgImage(imagePath, package: 'capp_home_core', height: _imageSize, width: _imageSize),
          const SizedBox(height: 4),
          KoyalText.subtitle2(color: ColorTheme.of(context).defaultTextColor, title, textAlign: TextAlign.center),
          const SizedBox(height: 4),
          KoyalText.body2(color: ColorTheme.of(context).secondaryTextColor, subtitle, textAlign: TextAlign.center),
        ],
      );
}

const double _imageSize = 56;
