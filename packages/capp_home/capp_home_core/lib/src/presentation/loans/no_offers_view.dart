import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_home_core.dart';
import 'loans_placeholder.dart';

class NoOffersView extends StatelessWidget {
  final bool showNearestPartnerShopButton;

  const NoOffersView({Key? key, required this.showNearestPartnerShopButton}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = L10nCappHome.of(context);

    return Column(
      children: [
        LoansPlaceholder(
          imagePath: 'assets/images/loan_promo.svg',
          title: l10n.cappMainEmptyOffersTitle,
          subtitle: l10n.cappMainEmptyOffersSubtitle,
        ),
        const SizedBox(height: 20),
        if (showNearestPartnerShopButton)
          TertiaryButton(
            text: l10n.cappMainEmptyOffersButtonText,
            onPressed: () {
              if (context.get<CappHomeSettings>().showPosList) {
                context.navigator.pushFromPackage(
                  package: 'CappLoanShared',
                  screen: 'PosCategoryfilter',
                );
              } else {
                context.navigator.pushFromPackage(
                  package: 'CappContent',
                  screen: 'WebViewScreen',
                  arguments: WebViewArguments(
                    url: l10n.cappMainEmptyOffersButtonUrl,
                    title: l10n.cappMainEmptyOffersButtonText,
                  ),
                );
              }
            },
          ),
      ],
    );
  }
}
