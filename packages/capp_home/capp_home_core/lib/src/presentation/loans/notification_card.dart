import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../capp_home_core.dart';

class NotificationCard extends StatelessWidget {
  final String imagePath;
  final String title;
  final String subtitle;
  final VoidCallback? onPressed;
  final VoidCallback? onClosePressed;

  const NotificationCard({
    Key? key,
    required this.imagePath,
    required this.title,
    required this.subtitle,
    this.onPressed,
    this.onClosePressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => ClipRRect(
        borderRadius: _cardBorderRadius,
        child: Container(
          decoration: const BoxDecoration(borderRadius: _cardBorderRadius),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildTitleAndButton(context),
              _buildContent(context),
            ],
          ),
        ),
      );

  Widget _buildTitleAndButton(BuildContext context) => Container(
        color: ColorTheme.of(context).infoIndicatorBackgroundColor,
        // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
        //ignore: no-direct-padding
        child: Padding(
          padding: const EdgeInsets.only(left: 12, right: 10, top: 4, bottom: 4),
          child: Row(
            children: [
              KoyalText.caption2(
                color: ColorTheme.of(context).informationTextColor,
                L10nCappHome.of(context).cappMainNotifications.toUpperCase(),
              ),
              const Spacer(),
              _CircularCloseButton(onTap: onClosePressed),
            ],
          ),
        ),
      );

  Widget _buildContent(BuildContext context) => Material(
        color: ColorTheme.of(context).backgroundColor,
        child: InkWell(
          onTap: onPressed,
          child: KoyalPadding.normalAll(
            child: Row(
              children: [
                _image,
                const SizedBox(width: 12),
                Flexible(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      KoyalText.subtitle2(color: ColorTheme.of(context).defaultTextColor, title),
                      const SizedBox(height: 4),
                      KoyalText.body2(color: ColorTheme.of(context).defaultTextColor, subtitle),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  Widget get _image => AssetSvgImage(imagePath, package: 'capp_home_core', height: _cardImageSize, width: _cardImageSize);
}

class _CircularCloseButton extends StatelessWidget {
  final VoidCallback? onTap;

  const _CircularCloseButton({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) => ClipOval(
        child: Material(
          color: ColorTheme.of(context).infoIndicatorBackgroundColor,
          child: InkWell(
            onTap: onTap,
            child: Icon(
              KoyalIcons.close_circle_outline,
              color: ColorTheme.of(context).infoIndicatorColor,
              size: _closeButtonRadius,
            ),
          ),
        ),
      );
}

const double _cardImageSize = 56;
const double _closeButtonRadius = 16;
const BorderRadius _cardBorderRadius = BorderRadius.all(Radius.circular(8));
