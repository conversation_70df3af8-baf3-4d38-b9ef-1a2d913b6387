import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import 'guest_content.dart';
import 'prospect_content.dart';

class LoansScreen extends StatelessWidget with RouteWrapper {
  final LoansScreenArguments? arguments;
  final bool showNearestPartnerShopButton;

  const LoansScreen({
    Key? key,
    required this.showNearestPartnerShopButton,
    this.arguments,
  }) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) {
    final loansBloc = context.get<LoansBloc>()
      ..add(
        const LoansEvent.init(
          offerModelList: [],
          fetchBnplOffer: true,
        ),
      );
    return MultiBlocProvider(
      providers: [
        BlocProvider<LoansBloc>.value(value: loansBloc),
        BlocProvider<InstallmentOverviewBloc>.value(value: context.get<InstallmentOverviewBloc>()),
      ],
      child: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (context.isRegistered<CappLoanOriginationTrackingService>()) {
      context.get<CappLoanOriginationTrackingService>().trackLoanDashboardView();
    }

    return KoyalScaffold(
      customIdentifier: 'loans_screen',
      appBar: KoyalAppBar(
        title: L10nCappHome.of(context).cappMainFinanceAppbarTitle,
        leading: context.isFlagEnabledWatch(FeatureFlag.mainTabsActivity)
            ? KoyalAppBarLeading.goBack
            : KoyalAppBarLeading.none,
      ),
      backgroundColor: HciColors.supplementary0,
      safeAreaCustomSettings: const SafeAreaCustomSettings(bottom: false),
      safeAreaBarCustomSettings: const SafeAreaCustomSettings(bottom: false),
      body: SingleChildScrollView(
        key: const Key('__loansScreenScrollable__'),
        child: BlocBuilder<LoansBloc, LoansState>(
          builder: (context, state) {
            return Column(
              children: [
                buildContent(
                  context,
                  state.signOnStatus,
                  state.offerModelList,
                  showNotification: state.isNotificationClosed,
                  isLoading: state.isLoading,
                ),
                const BottomSpace(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget buildContent(
    BuildContext context,
    SignOnStatus status,
    List<OfferModel> bnplModels, {
    bool showNotification = false,
    bool isLoading = false,
  }) {
    final showScoringInputFromDeepLink = arguments?.showScoringInputFromDeepLink ?? false;
    switch (status) {
      case SignOnStatus.anonymous:
        return GuestContent(
          showNearestPartnerShopButton: showNearestPartnerShopButton,
          showScoringInputFromDeepLink: showScoringInputFromDeepLink,
          bnplModels: bnplModels,
          isLoading: isLoading,
          showReloginPopupBNPL: arguments?.showReloginPopupBNPL ?? false,
        );
      case SignOnStatus.registered:
        return ProspectContent(
          showNotification: showNotification,
          showNearestPartnerShopButton: showNearestPartnerShopButton,
          showScoringInputFromDeepLink: showScoringInputFromDeepLink,
          bnplModels: bnplModels,
          isLoading: isLoading,
        );
      case SignOnStatus.client:
        return CustomerContent(
          showNearestPartnerShopButton: showNearestPartnerShopButton,
          showScoringInputFromDeepLink: showScoringInputFromDeepLink,
          bnplModels: bnplModels,
          isLoading: isLoading,
        );
    }
  }
}
