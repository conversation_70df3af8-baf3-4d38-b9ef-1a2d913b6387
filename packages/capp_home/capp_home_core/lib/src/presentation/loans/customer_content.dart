import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';

class CustomerContent extends StatelessWidget {
  final bool showNearestPartnerShopButton;
  final bool showScoringInputFromDeepLink;
  final List<OfferModel> bnplModels;
  final bool isLoading;
  const CustomerContent({
    Key? key,
    required this.showNearestPartnerShopButton,
    required this.showScoringInputFromDeepLink,
    required this.bnplModels,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = L10nCappHome.of(context);
    context.read<OfferBannerBloc>().add(const OfferBannerEvent.turnOnTracking());

    return Column(
      children: [
        BlocBuilder<InstallmentOverviewBloc, InstallmentOverviewState>(
          builder: (context, state) => MyLoansHeader(hasContractHistoryButton: state.finishedCards.isNotEmpty),
        ),
        FeatureFlagWidget(
          featureName: FeatureFlag.homeScreenVnNewDesign,
          disabled: FeatureFlagWidget(
            featureName: FeatureFlag.loanCardsHideOld,
            disabled: InstallmentsOverview(
              axis: Axis.vertical,
              emtyInstallmentsBuilder: (context, axis) => const ImprovedPlaceholder(
                onPressed: null,
              ),
            ),
            child: InstallmentsOverviewNew(
              axis: Axis.vertical,
              emtyInstallmentsBuilder: (context, axis) => const ImprovedPlaceholder(
                onPressed: null,
              ),
            ),
          ),
          child: ProductCardsOverview(
            axis: Axis.vertical,
            emtyInstallmentsBuilder: (context, axis) => const ImprovedPlaceholder(
              onPressed: null,
            ),
          ),
        ),
        MainHeading(
          title: l10n.cappMainOffers,
          centerAlign: false,
          subtitle: l10n.cappMainOffersSubtitle.trim().isEmpty ? null : l10n.cappMainOffersSubtitle,
        ),
        OfferBannersList(
          showNearestPartnerShopButton: showNearestPartnerShopButton,
          showScoringInputFromDeepLink: showScoringInputFromDeepLink,
          bnplModels: bnplModels,
          isLoading: isLoading,
        ),
      ],
    );
  }
}
