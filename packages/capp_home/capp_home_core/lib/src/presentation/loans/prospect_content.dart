import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_self_service_core/capp_self_service_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigator/navigator.dart';

import '../../../capp_home_core.dart';
import 'loans_placeholder.dart';
import 'notification_card.dart';

class ProspectContent extends StatelessWidget {
  final bool showNotification;
  final bool showNearestPartnerShopButton;
  final bool showScoringInputFromDeepLink;
  final List<OfferModel> bnplModels;
  final bool isLoading;
  const ProspectContent({
    Key? key,
    this.showNotification = false,
    required this.showNearestPartnerShopButton,
    required this.showScoringInputFromDeepLink,
    required this.bnplModels,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = L10nCappHome.of(context);
    context.read<OfferBannerBloc>().add(const OfferBannerEvent.turnOnTracking());

    return Column(
      children: [
        SectionHeading(title: l10n.myProducts),
        KoyalPadding.normalHorizontal(
          child: _buildNotificationOrPlaceholder(context, showNotification),
        ),
        SectionHeading(title: l10n.cappMainOffers),
        OfferBannersList(
          showNearestPartnerShopButton: showNearestPartnerShopButton,
          showScoringInputFromDeepLink: showScoringInputFromDeepLink,
          bnplModels: bnplModels,
          isLoading: isLoading,
        ),
      ],
    );
  }

  Widget _buildNotificationOrPlaceholder(BuildContext context, bool isNotificationClosed) {
    final l10n = L10nCappHome.of(context);

    return isNotificationClosed
        ? LoansPlaceholder(
            imagePath: 'assets/images/contract.svg',
            title: l10n.cappMainEmptyLoansTitle,
          )
        : NotificationCard(
            imagePath: 'assets/images/contract_colored.svg',
            title: l10n.cappMainNotificationsTitle,
            subtitle: l10n.cappMainNotificationsSubtitle,
            onPressed: () {
              if (context.isFlagEnabledRead(FeatureFlag.retrieveAccountBos)) {
                context.navigator.push(
                  path: NavigatorPath.cappAuth.retrieveAccount2ndIdentifierScreen,
                  arguments: SecondIdentifierScreenArguments(),
                );
              } else {
                context.navigator.push(path: NavigatorPath.cappAuth.secondIdHoselPairingScreen);
              }
            },
            onClosePressed: () => context.read<LoansBloc>().add(const LoansEvent.notificationClosed()),
          );
  }
}
