import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_home_core.dart';

class MyLoansHeader extends StatelessWidget {
  final bool hasContractHistoryButton;

  const MyLoansHeader({
    Key? key,
    this.hasContractHistoryButton = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [ColorTheme.of(context).backgroundColor, ColorTheme.of(context).foreground5Color],
          ),
        ),
        child: MainHeading(
          centerAlign: false,
          title: L10nCappHome.of(context).myProducts,
          actionLabel: L10nCappHome.of(context).contractHistory,
          action: hasContractHistoryButton
              ? () => context.navigator.pushFromPackage(package: 'CappSelfService', screen: 'ContractHistoryScreen')
              : null,
        ),
      );
}
