import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../capp_home_core.dart';
import 'loans_placeholder.dart';

class GuestContent extends StatelessWidget {
  final bool showNearestPartnerShopButton;
  final bool showScoringInputFromDeepLink;
  final List<OfferModel> bnplModels;
  final bool isLoading;
  final bool showReloginPopupBNPL;
  const GuestContent({
    Key? key,
    required this.showNearestPartnerShopButton,
    required this.showScoringInputFromDeepLink,
    required this.bnplModels,
    required this.isLoading,
    required this.showReloginPopupBNPL,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = L10nCappHome.of(context);
    context.read<OfferBannerBloc>().add(const OfferBannerEvent.turnOnTracking());

    return FirstShown(
      onShown: () {
        if (showReloginPopupBNPL) {
          showPopupRemind(context);
        }
      },
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [ColorTheme.of(context).backgroundColor, ColorTheme.of(context).foreground5Color],
              ),
            ),
            child: Column(
              children: [
                SectionHeading(title: l10n.myProducts),
              ],
            ),
          ),
          LoansPlaceholder(
            imagePath: 'assets/images/contract.svg',
            title: l10n.cappMainEmptyLoansTitle,
            subtitle: l10n.cappMainEmptyLoansSubtitle,
          ),
          const SizedBox(height: 20),
          TertiaryButton(
            text: l10n.cappMainEmptyLoansButtonText,
            onPressed: () => context.navigateToAuthEntryPoint(),
          ),
          SectionHeading(title: l10n.cappMainOffers),
          OfferBannersList(
            showNearestPartnerShopButton: showNearestPartnerShopButton,
            showScoringInputFromDeepLink: showScoringInputFromDeepLink,
            bnplModels: bnplModels,
            isLoading: isLoading,
          ),
        ],
      ),
    );
  }

  Future<void> showPopupRemind(
    BuildContext context,
  ) async {
    return showKoyalOverlay<void>(
      context,
      dismissible: false,
      title: L10nCappHome.of(context).cappMainLoansGuestHplReloginPopupTitle,
      body: KoyalText.body1(
        L10nCappHome.of(context).cappMainLoansGuestHplReloginPopupContent,
        color: ColorTheme.of(context).defaultTextColor,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (ctx) => PrimaryButton(
        onPressed: () {
          Navigator.pop(context);
          context.navigateToSignInScreen();
        },
        text: L10nCappHome.of(context).cappMainLoansGuestHplReloginPopupLoginButton,
      ),
    );
  }
}
