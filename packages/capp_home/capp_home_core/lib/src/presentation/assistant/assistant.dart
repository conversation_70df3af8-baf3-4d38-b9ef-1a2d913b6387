import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_content/capp_content.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import 'circular_close_button.dart';

enum AssistantIconItem {
  myCoupons,
  loanOffers,
  payEMI,
  chatbot,
}

enum AssistantChipItem {
  contact,
  warningZone,
  repayment,
  loanRegistration,
}

class Assistant extends StatelessWidget {
  final CappHomeSettings settings;
  final bool isAnonymous;

  const Assistant({Key? key, required this.settings, required this.isAnonymous}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = L10nCappHome.of(context);

    return KoyalPadding.normalAll(
      child: Column(
        key: const Key('__supercupoAssistant__'),
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isAnonymous) ...[
            AssetSvgImage(
              context.isFlagEnabledWatch(FeatureFlag.hideSuperCupo)
                  ? 'assets/svg/settings_security.svg'
                  : 'assets/svg/mascot/mascot_lock.svg',
              package: 'capp_ui',
            ),
            MainHeading(title: l10n.cappMainGuestAssistantTitle, subtitle: l10n.cappMainGuestAssistantSubtitle),
            //ignore: buttons-layout
            PrimaryButton(
              key: const Key('__assistantLoginRegisterButton__'),
              text: L10nCappHome.of(context).cappMainAssistantLoginRegister,
              onPressed: () {
                context.get<CappTrackingService>().trackEvent(
                      event: KoyalEvent.homeDashboardLoginRegisterClick,
                      eventCategory: KoyalTrackingCategories.homeDashboard,
                      eventAction: KoyalAnalyticsConstants.click,
                      eventLabel: KoyalTrackingLabels.loginRegister,
                    );
                Navigator.pop(context);
                context.navigateToSignInScreen();
              },
            ),
            if (settings.showAssistantGuestContactButton)
              KoyalPadding.normalAll(
                bottom: false,
                child: TertiaryButton(
                  key: const Key('__assistantContactHci__'),
                  text: L10nCappHome.of(context).cappMainContactHomeCredit,
                  onPressed: () {
                    _trackClickEvent(context, 'contact');
                    Navigator.pop(context);
                    context.navigator.pushFromPackage(package: 'CappContent', screen: 'ContactUsScreen');
                  },
                ),
              ),
          ] else ...[
            const AssetSvgImage(
              'assets/images/cupo_assistant.svg',
              package: 'capp_home_core',
              height: 100,
            ),
            MainHeading(title: l10n.cappMainAssistantTitle, subtitle: l10n.cappMainAssistantSubtitle),
            _Wrap(children: settings.assistantIconItems.map((i) => _iconButtonFromItem(context, i)).toList()),
            KoyalPadding.large(
              bottom: false,
              right: false,
              left: false,
              child: _Wrap(children: settings.assistantChipItems.map((i) => _chipFromItem(context, i)).toList()),
            ),
          ],
          KoyalPadding.large(
            bottom: false,
            child: CircularCloseButton(
              key: const Key('__assistantClose__'),
              onTap: () {
                context.get<CappTrackingService>().trackEvent(
                      event: KoyalEvent.homeSupercupoAssistantExitClick,
                      eventCategory: KoyalTrackingCategories.bottomNavigation,
                      eventAction: KoyalAnalyticsConstants.click,
                      eventLabel: 'super_cupo_exit',
                    );
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _iconButtonFromItem(BuildContext context, AssistantIconItem item) {
    switch (item) {
      case AssistantIconItem.myCoupons:
        return CircularIconButton(
          key: const Key('__assistantMyCoupons__'),
          icon: KoyalIcons.coupon_outline,
          label: L10nCappHome.of(context).cappMainMyCoupons,
          onTap: () {
            _trackClickEvent(context, 'coupons');
            Navigator.pop(context);
            // @TODO: add navigation
          },
        );
      case AssistantIconItem.loanOffers:
        return CircularIconButton(
          key: const Key('__assistantLoanOffers__'),
          icon: KoyalIcons.other_services_outline,
          label: L10nCappHome.of(context).cappMainLoanOffers,
          onTap: () {
            _trackClickEvent(context, 'loan');
            Navigator.pop(context);
            // @TODO: add navigation
          },
        );
      case AssistantIconItem.payEMI:
        return CircularIconButton(
          key: const Key('__assistantPayEmi__'),
          icon: KoyalIcons.cash_outline,
          label: L10nCappHome.of(context).cappMainPayEmi,
          onTap: () {
            _trackClickEvent(context, 'emi');
            Navigator.pop(context);
            if (isAnonymous) {
              context.navigateToSignInScreen();
            } else {
              NavigationUtils.navigateToRepaymentMain(context: context, viewType: RepaymentViewType.contractList);
            }
          },
        );
      case AssistantIconItem.chatbot:
        return context.isFlagEnabledWatch(FeatureFlag.chatbotEntryPoint)
            ? CircularIconButton(
                key: const Key('__assistantChatbot__'),
                icon: KoyalIcons.chatbubbles_outline,
                label: L10nCappHome.of(context).cappMainChatWithUs,
                onTap: () {
                  _trackClickEvent(context, 'chatbot');
                  Navigator.pop(context);
                  context.navigator.pushFromPackage(package: 'KoyalChatbot', screen: 'ChatbotScreen');
                },
              )
            : Container();
    }
  }

  Widget _chipFromItem(BuildContext context, AssistantChipItem item) {
    switch (item) {
      case AssistantChipItem.contact:
        return AssistantChip(
          key: const Key('__assistantContactHci__'),
          icon: KoyalIcons.call_outline,
          label: L10nCappHome.of(context).cappMainContactHomeCredit,
          onTap: () {
            _trackClickEvent(context, 'contact');

            Navigator.pop(context);
            context.navigator.pushFromPackage(package: 'CappContent', screen: 'ContactUsScreen');
          },
        );
      case AssistantChipItem.warningZone:
        return AssistantChip(
          key: const Key('__assistantWarningZone__'),
          icon: KoyalIcons.flame_outline,
          label: L10nCappHome.of(context).cappMainWarningzone,
          onTap: () {
            _trackClickEvent(context, 'warningzone');
            Navigator.pop(context);
            context.navigator.pushFromPackage(
              package: 'CappContent',
              screen: 'BlogsScreen',
              arguments: BlogsArguments(initialCategoryId: settings.blogsWarningZoneCategoryId),
            );
          },
        );
      case AssistantChipItem.repayment:
        return AssistantChip(
          key: const Key('__assistantPayEmi__'),
          icon: KoyalIcons.cash_outline,
          label: L10nCappHome.of(context).repayment,
          onTap: () {
            _trackClickEvent(context, 'emi');
            Navigator.pop(context);
            if (isAnonymous) {
              context.navigateToSignInScreen();
            } else {
              NavigationUtils.navigateToRepaymentMain(context: context, viewType: RepaymentViewType.contractList);
            }
          },
        );
      case AssistantChipItem.loanRegistration:
        return context.isFlagEnabledWatch(FeatureFlag.assistantShowLoanRegistration)
            ? AssistantChip(
                key: const Key('__assistantLoanRegistration__'),
                icon: KoyalIcons.other_services_outline,
                label: L10nCappHome.of(context).cappMainLoanRegistration,
                onTap: () {
                  _trackClickEvent(context, 'loanRegistration');
                  Navigator.pop(context);
                  context.navigator.toMainScreen(arguments: MainScreenArguments(initialTab: TabItem.loans));
                },
              )
            : Container();
    }
  }

  void _trackClickEvent(BuildContext context, String eventLabel) => context.get<CappTrackingService>().trackEvent(
        event: KoyalEvent.homeSupercupoAssistantItemClick,
        eventCategory: KoyalTrackingCategories.bottomNavigation,
        eventAction: KoyalAnalyticsConstants.click,
        eventLabel: 'super_cupo',
        customDimensions: {
          TrackingProperties.propertyCdItemName: eventLabel,
        },
      );
}

class _Wrap extends StatelessWidget {
  const _Wrap({Key? key, this.children = const []}) : super(key: key);

  final List<Widget> children;

  @override
  Widget build(BuildContext context) =>
      Wrap(alignment: WrapAlignment.center, spacing: 8, runSpacing: 8, children: children);
}
