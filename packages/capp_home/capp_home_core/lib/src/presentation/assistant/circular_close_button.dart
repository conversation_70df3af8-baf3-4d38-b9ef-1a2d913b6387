import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class CircularClose<PERSON>utton extends StatelessWidget {
  final VoidCallback? onTap;

  const CircularCloseButton({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = ColorTheme.of(context);

    return ClipOval(
      child: Material(
        color: ColorTheme.of(context).backgroundColor,
        child: InkWell(
          onTap: onTap,
          child: Container(
            width: _closeButtonRadius,
            height: _closeButtonRadius,
            decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: theme.primaryColor)),
            child: Icon(KoyalIcons.close_outline, color: theme.primaryColor),
          ),
        ),
      ),
    );
  }
}

const double _closeButtonRadius = 56;
