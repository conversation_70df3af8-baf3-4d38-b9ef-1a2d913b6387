// ignore_for_file: use_build_context_synchronously
import 'dart:async';

import 'package:animations/animations.dart';
import 'package:capp_content/capp_content.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';

class MainScreenBase extends StatefulWidget with RouteWrapper {
  final MainScreenArguments? arguments;
  const MainScreenBase({Key? key, this.arguments}) : super(key: key);

  @override
  MainScreenBaseState createState() => MainScreenBaseState();

  @override
  Widget wrappedRoute(BuildContext context) {
    final tabIndex = getTabIndex(context, arguments?.initialTab);
    bool? isAfterLogin;
    final dynamic args = arguments?.arguments;
    if (args is LegalPermissionCheckArguments) {
      isAfterLogin = args.isAfterLogin;
    }

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => context.get<MainBloc>()..add(MainEvent.initMain(initialTab: tabIndex)),
        ),
        BlocProvider(create: (context) => context.get<MainGuardCubit>()..init(isAfterLogin: isAfterLogin)),
        BlocProvider(
          create: (context) => context.get<OfferBannerBloc>()..add(const OfferBannerEvent.init()),
        ),
        ...getBlocProviders(context),
      ],
      child: this,
    );
  }

  int getTabIndex(BuildContext context, TabItem? tabItem) {
    if (tabItem == null) return 0;
    final index = getBottomBarItems(context).indexWhere((x) => x.id == tabItem.toString());
    return index < 0 ? 0 : index;
  }

  List<MainNavigationBarItem> getBottomBarItems(BuildContext context) {
    final b = BottomMenuItems(context);
    return [b.home, b.loans, b.promo, b.account];
  }

  List<BlocListener> getBlocListeners() {
    return [];
  }

  List<BlocProvider> getBlocProviders(BuildContext context) {
    return [];
  }
}

class MainScreenBaseState extends LifecycleWatcherState<MainScreenBase>
    with RouteAware, BottomBarTrackingMixin // ignore: prefer_mixin
{
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late KoyalRouterObserver _observer;
  List<Widget> _pages = [];
  int _index = 0;
  int _previousIndex = 0;
  Widget? _drawer;
  late PageController _pageController;
  bool isBottomScrolled = false;

  void setDrawer(Widget? drawer) => setState(() => _drawer = drawer);
  void openDrawer() => _scaffoldKey.currentState!.openDrawer();
  bool _keyboardNotVisible(BuildContext context) => MediaQuery.of(context).viewInsets.bottom == 0;

  @override
  void initState() {
    _observer = context.get<KoyalRouterObserver>();
    context.read<UnreadBloc?>()?.add(const UnreadEvent.reload());
    _pageController = PageController(initialPage: _index);
    _pages = getTabs(context);
    super.initState();
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    if (mounted) {
      _observer.subscribe(this, ModalRoute.of(context)! as PageRoute<dynamic>);
    }
  }

  @override
  void dispose() {
    _observer.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    context.read<UnreadBloc?>()?.add(const UnreadEvent.hasUnreadMessages());
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // ignore: deprecated_member_use
    final value = WidgetsBinding.instance.window.viewInsets.bottom;
    final mainBloc = context.read<MainBloc>();
    if (value > 0 && mainBloc.state.hasKeyboardClosed) {
      mainBloc.add(const MainEvent.keyboardHasClosed(closed: false));
    } else if (value == 0 && !mainBloc.state.hasKeyboardClosed) {
      mainBloc.add(const MainEvent.keyboardHasClosed(closed: true));
    }
  }

  @override
  void onResumed() {
    if (mounted) {
      context.read<UnreadBloc?>()?.add(const UnreadEvent.hasUnreadMessages());
    }
  }

  @override
  Widget build(BuildContext context) => MultiBlocListener(
        listeners: [
          BlocListener<MainBloc, MainState>(
            listenWhen: (previous, current) => previous.selectedTab != current.selectedTab,
            listener: (context, state) {
              _pageController.jumpToPage(state.selectedTab);
              setState(() {
                _drawer = null; // drawer is tab specific
                _previousIndex = _index == 0 ? -1 : _index;
                _index = state.selectedTab;
              });
            },
          ),
          // This is probably used to reload Main after FFs are switched from DevTools as Insiders
          BlocListener<FeatureFlagBloc, FeatureFlagState>(
            listenWhen: (previous, current) => previous.loadedDateTime != current.loadedDateTime,
            listener: (context, state) => setState(() {}),
          ),
          ...widget.getBlocListeners(),
        ],
        child: KoyalWillPopScope(
          key: const Key('__mainScreen__'),
          onWillPop: () async {
            if (context.read<MainBloc>().state.selectedTab != 0) {
              context.read<MainBloc>().add(const MainEvent.selectTab(tabIndex: 0));
              return false;
            } else {
              final locResponse = await showKoyalOverlay<bool>(
                context,
                title: L10nCappHome.of(context).cappMainExitAppTitle,
                dismissible: false,
                primaryButtonBuilder: (dialogContext) => PrimaryButton(
                  key: const Key('__exitAppYesButton__'),
                  onPressed: () => dialogContext.navigator.pop(true),
                  text: L10nCappHome.of(context).yes,
                ),
                tertiaryButtonBuilder: (dialogContext) => TertiaryButton(
                  key: const Key('__exitAppNoButton__'),
                  text: L10nCappHome.of(context).no,
                  onPressed: () => dialogContext.navigator.pop(false),
                ),
              );
              return locResponse ?? false;
            }
          },
          child: KoyalScaffold(
            safeAreaCustomSettings: const SafeAreaCustomSettings(bottom: false, top: false),
            safeAreaBarCustomSettings: const SafeAreaCustomSettings(bottom: false, left: false, right: false),
            extendBody: true,
            scaffoldKey: _scaffoldKey,
            drawer: _drawer,
            showInternetAlert: false,
            body: NotificationListener<ScrollNotification>(
              onNotification: (notification) {
                if (notification is ScrollUpdateNotification) {
                  final atBottom = notification.metrics.extentAfter < 4.0;
                  if (atBottom != isBottomScrolled) {
                    setState(() {
                      isBottomScrolled = atBottom;
                    });
                  }
                }
                return true;
              },
              child: PageTransitionSwitcher(
                reverse: _index < _previousIndex,
                transitionBuilder: (child, animation, secondaryAnimation) => SharedAxisTransition(
                  animation: animation,
                  secondaryAnimation: secondaryAnimation,
                  transitionType: SharedAxisTransitionType.horizontal,
                  child: child,
                ),
                child: PageView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _pageController,
                  itemBuilder: (context, i) => _pages[i],
                ),
              ),
            ),
            floatingActionButtonAnimator: const _NoScalingAnimation(),
            floatingActionButtonLocation: CustomCenterDockedFloatingActionButtonLocation(),
            floatingActionButton: hasFloatingActionButton(context)
                ? _keyboardNotVisible(context)
                    ? getContainFloatingActionButton(context)
                    : const SizedBox.shrink()
                : null,
            bottomNavigationBar: _keyboardNotVisible(context)
                ? Container(
                    padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: BlocBuilder<MainBloc, MainState>(
                      buildWhen: (previous, current) => previous.selectedTab != current.selectedTab,
                      builder: (context, state) => BlocBuilder<UnreadBloc, UnreadState>(
                        buildWhen: (previous, current) => previous.hasUnread != current.hasUnread,
                        builder: (context, unreadState) {
                          return getFABBottomAppBar(context, state.selectedTab);
                        },
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ),
      );

  // Is and should be overriden in each country Main Screen
  List<Widget> getTabs(BuildContext context) {
    throw UnimplementedError();
  }

  Widget getLoansTab(BuildContext context) {
    final dynamic args = widget.arguments?.arguments;
    return context.get<CappHomeSettings>().hasImprovedLoansScreen
        ? PersistantPageWrapper(child: const ImprovedLoansScreen(key: Key('__loansScreen__')).wrappedRoute(context))
        : PersistantPageWrapper(
            child: LoansScreen(
              key: const Key('__loansScreen__'),
              showNearestPartnerShopButton: context.get<CappHomeSettings>().showNearestPartnerShopButton,
              arguments: args is LoansScreenArguments ? args : LoansScreenArguments(),
            ).wrappedRoute(context),
          );
  }

  Widget getAccountTab(BuildContext context) {
    return const AccountScreen(key: PageStorageKey('__accountScreenTab__')).wrappedRoute(context);
  }

  Widget getPromoTab(BuildContext context) {
    final dynamic args = widget.arguments?.arguments;

    return PersistantPageWrapper(
      child: context.isFlagEnabledRead(FeatureFlag.promosEnableScreen)
          ? const PromotionsScreen(
              key: PageStorageKey('__promoScreenTab__'),
            ).wrappedRoute(context)
          : BlogsScreen(
              key: const PageStorageKey('__promoScreenTab__'),
              args: args is BlogsArguments
                  ? args
                  : BlogsArguments(
                      isTabView: true,
                      initialCategoryId: context.get<CappHomeSettings>().blogsPromoCategoryId,
                    ),
            ).wrappedRoute(context),
    );
  }

  Widget getHomeTab(BuildContext context) {
    final dynamic args = widget.arguments?.arguments;
    return PersistantPageWrapper(
      child: HomeScreenCore(
        key: const PageStorageKey('__homeScreenTab__'),
        arguments: args is HomeRouteArguments ? args : null,
      ).wrappedRoute(context),
    );
  }

  bool hasFloatingActionButton(BuildContext context) => true;

  Widget getFloatingActionButton(BuildContext context) {
    return FloatingActionButton(
      key: const Key('__mainScreenSuperCupoFab__'),
      heroTag: 'mainScreenFAB',
      shape: const CircleBorder(),
      elevation: 0,
      backgroundColor: ColorTheme.of(context).iconBackgroundColor,
      onPressed: () async {
        trackSupercupoClick(context);
        return showKoyalBottomSheet<void>(
          context: context,
          builder: (_) => Assistant(
            settings: context.get<CappHomeSettings>(),
            isAnonymous: context.read<MainBloc>().state.anonymous,
          ),
        );
      },
      child: ClipOval(
        child: Stack(
          children: [
            Positioned(
              top: 8,
              left: 3,
              child: SvgPicture.asset('assets/svg/superkupo.svg', package: 'capp_ui', width: 50, height: 50),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> onTabSelected(BuildContext ctx, int tabIndex) async {
    final tabItem = getBottomBarItems(context)[tabIndex];
    context.read<MainBloc>().add(MainEvent.selectTab(tabIndex: tabIndex));
    trackNavigationItemClick(ctx, tabItem.trackingLabel);

    if (tabItem.id == TabItem.home.toString()) {
      context.read<UnreadBloc?>()?.add(const UnreadEvent.hasUnreadMessages());
    }
  }

  NotchedShape getBottomAppBarNotchedShape(BuildContext context) {
    return const CircularNotchedRectangle();
  }

  Widget getContainFloatingActionButton(BuildContext context) {
    return SizedBox(height: 56, width: 56, child: getFloatingActionButton(context));
  }

  List<MainNavigationBarItem> getBottomBarItems(BuildContext context) {
    final b = BottomMenuItems(context);
    return [b.home, b.loans, b.promo, b.account];
  }

  Widget getFABBottomAppBar(BuildContext context, int selectedTab) {
    return FABBottomAppBar(
      key: const Key('__mainNavigationBar__'),
      hasFab: hasFloatingActionButton(context),
      defaultColor: ColorTheme.of(context).foreground90Color,
      selectedColor: ColorTheme.of(context).primaryColor,
      backgroundColor: ColorTheme.of(context).backgroundColor,
      notchedShape: getBottomAppBarNotchedShape(context),
      selectedTabIndex: selectedTab,
      onTabSelected: (i) => onTabSelected(context, i),
      items: widget.getBottomBarItems(context),
      hasElevation: !isBottomScrolled,
    );
  }
}

class _NoScalingAnimation extends FloatingActionButtonAnimator {
  const _NoScalingAnimation();

  @override
  Offset getOffset({Offset? begin, required Offset end, double? progress}) => end;

  @override
  Animation<double> getRotationAnimation({required Animation<double> parent}) =>
      Tween<double>(begin: 1, end: 1).animate(parent);

  @override
  Animation<double> getScaleAnimation({required Animation<double> parent}) =>
      Tween<double>(begin: 1, end: 1).animate(parent);
}

class PersistantPageWrapper extends StatefulWidget {
  final Widget child;
  const PersistantPageWrapper({Key? key, required this.child}) : super(key: key);

  @override
  State<PersistantPageWrapper> createState() => PersistantPageWrapperState();
}

class PersistantPageWrapperState extends State<PersistantPageWrapper> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}
