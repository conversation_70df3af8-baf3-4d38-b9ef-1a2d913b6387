import 'package:capp_domain/capp_domain.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../capp_home_core.dart';

class BottomMenuItems {
  final BuildContext context;
  const BottomMenuItems(this.context);

  MainNavigationBarItem get home => MainNavigationBarItem(
        id: TabItem.home.toString(),
        key: const Key('__homeTab__'),
        icon: KoyalIcons.home_outline,
        selectedIcon: KoyalIcons.home_solid,
        title: L10nCappHome.of(context).mainTabHome,
        trackingLabel: 'home',
      );

  MainNavigationBarItem get account => MainNavigationBarItem(
        id: TabItem.account.toString(),
        key: const Key('__profileTab__'),
        icon: KoyalIcons.account_outline,
        selectedIcon: KoyalIcons.account_solid,
        title: L10nCappHome.of(context).mainTabAccount,
        trackingLabel: 'account',
      );

  MainNavigationBarItem get promo => MainNavigationBarItem(
        id: TabItem.promo.toString(),
        key: const Key('__promoTab__'),
        icon: KoyalIcons.promo_outline,
        selectedIcon: KoyalIcons.promo_solid,
        title: L10nCappHome.of(context).mainTabPromo,
        trackingLabel: 'promo',
      );

  MainNavigationBarItem get loans => MainNavigationBarItem(
        id: TabItem.loans.toString(),
        key: const Key('__loansTab__'),
        icon: KoyalIcons.receipt_outline,
        selectedIcon: KoyalIcons.receipt_solid,
        title: L10nCappHome.of(context).mainTabLoans,
        trackingLabel: 'loans',
      );
}
