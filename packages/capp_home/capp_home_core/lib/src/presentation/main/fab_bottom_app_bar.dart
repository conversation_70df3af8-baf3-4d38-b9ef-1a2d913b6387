import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class FABBottomAppBar extends StatelessWidget {
  final List<MainNavigationBarItem> items;
  final double height;
  final double iconSize;
  final Color backgroundColor;
  final Color defaultColor;
  final Color selectedColor;
  final NotchedShape notchedShape;
  final ValueChanged<int> onTabSelected;
  final int selectedTabIndex;
  final bool hasFab;
  final bool hasElevation;
  final double fontSize;

  const FABBottomAppBar({
    required Key key,
    required this.items,
    this.height = 60.0,
    this.iconSize = 24.0,
    required this.backgroundColor,
    required this.defaultColor,
    required this.selectedColor,
    required this.notchedShape,
    required this.onTabSelected,
    required this.hasFab,
    this.selectedTabIndex = 0,
    this.hasElevation = true,
    this.fontSize = 10,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KoyalSemantics(
      excludeSemantics: true,
      customIdentifier: 'FABBottomAppBar',
      child: Builder(
        builder: (context) {
          final listItems = List.generate(
            items.length,
            (index) => _buildTabItem(
              context: context,
              item: items[index],
              index: index,
              onPressed: onTabSelected,
            ),
          );
          if (hasFab) {
            // 56 fab + 8 padding + 8 for rounded border of notch
            listItems.insert(items.length >> 1, const SizedBox(width: 72));
          }
          return Stack(
            children: [
              const Positioned(child: QRBackground()),
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: ColorTheme.of(context).foreground50Color.withOpacity(0.25),
                      blurRadius: 12,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: BottomAppBar(
                  elevation: 0.0,
                  shape: notchedShape,
                  color: backgroundColor,
                  height: 60.0,
                  padding: EdgeInsets.zero,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: listItems,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTabItem({
    required BuildContext context,
    required MainNavigationBarItem item,
    required int index,
    required ValueChanged<int> onPressed,
  }) {
    final isSelected = selectedTabIndex == index;
    final color = isSelected ? selectedColor : defaultColor;
    final icon = isSelected && item.selectedIcon != null ? item.selectedIcon : item.icon;
    return Expanded(
      child: KoyalSemantics(
        customIdentifier: 'FABBottomAppBarItem',
        title: item.title,
        child: SizedBox(
          height: height,
          child: Material(
            type: MaterialType.transparency,
            child: InkWell(
              highlightColor: ColorTheme.of(context).muteIndicatorColor,
              customBorder: const CircleBorder(),
              key: item.key,
              onTap: () => onPressed(index),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 24,
                    child: Stack(
                      children: [
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Icon(icon, color: color, size: iconSize),
                        ),
                        if (item.showNotification != null && item.showNotification == true)
                          Align(
                            alignment: Alignment.topRight,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: ColorTheme.of(context).backgroundColor,
                                borderRadius: BorderRadius.circular(50),
                              ),
                              child: Center(
                                child: Container(
                                  width: 5,
                                  height: 5,
                                  decoration: BoxDecoration(
                                    color: selectedColor,
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  FittedBox(
                    child: KoyalText.caption4CustomFontSize(
                      color: color,
                      item.title,
                      fontSize: fontSize,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
