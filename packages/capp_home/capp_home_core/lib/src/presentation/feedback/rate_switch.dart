import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../application/feedback/feedback_bloc.dart';

class RateSwitch extends StatelessWidget {
  const RateSwitch({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => BlocBuilder<FeedbackBloc, FeedbackState>(
        builder: (context, state) => KoyalPadding.normalVertical(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              FeedbackThumb(
                isNegative: true,
                key: const Key('__feedbackFormNegativeButton__'),
                onPressed: () => _onFeedbackTap(context, state, isNegative: true),
                isSelected: state.selectedRating == 0,
              ),
              const SizedBox(width: KoyalPadding.paddingLarge),
              FeedbackThumb(
                isNegative: false,
                key: const Key('__feedbackFormPositiveButton__'),
                onPressed: () => _onFeedbackTap(context, state),
                isSelected: state.selectedRating == 1,
              ),
            ],
          ),
        ),
      );

  void _onFeedbackTap(BuildContext context, FeedbackState state, {bool isNegative = false}) {
    _trackClick(context, negative: isNegative);
    context.read<FeedbackBloc>().add(FeedbackEvent.selectRating(isNegative ? 0 : 1));
    if (state.feedbackCategories.isEmpty) {
      context.read<FeedbackBloc>().add(const FeedbackEvent.loadFeedbackCategories());
    }
  }

  void _trackClick(BuildContext context, {bool negative = false}) {
    context.get<CappTrackingService>().trackEvent(
          event: negative ? KoyalEvent.appFeedbackThumbDownClick : KoyalEvent.appFeedbackThumbUpClick,
          eventCategory: KoyalTrackingCategories.feedback,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: negative ? 'thumb_down' : 'thumb_up',
        );
  }
}
