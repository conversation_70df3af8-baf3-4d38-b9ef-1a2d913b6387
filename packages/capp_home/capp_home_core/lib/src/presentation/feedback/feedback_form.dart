import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import 'feedback_input_field.dart';
import 'feedback_loader.dart';
import 'feedback_submit_criteria_mixin.dart';

class FeedbackForm extends StatefulWidget {
  final String journeyId;
  const FeedbackForm({Key? key, required this.journeyId}) : super(key: key);

  @override
  State<FeedbackForm> createState() => _FeedbackFormState();
}

class _FeedbackFormState extends State<FeedbackForm> with FeedbackSubmitCriteriaMixin {
  late TextEditingController _controller;
  static const int _topRating = 5;
  late bool _isLivechatButtonEnabled;

  @override
  void initState() {
    super.initState();
    _isLivechatButtonEnabled = context.isFlagEnabledRead(FeatureFlag.satisfactionFeedbackLivechatButton);
    _controller = TextEditingController(text: '');
    final gtp = context.get<GlobalTrackingProperties>();
    if (gtp.currScreen == null || gtp.currScreen != 'feature_feedback') {
      // Update current/previous screen manually before tracking screenView
      gtp
        ..prevScreen = gtp.currScreen
        ..currScreen = 'feature_feedback';
    }
    context.get<CappTrackingService>().trackFeedbackEvent(
          event: KoyalEvent.appFeatureFeedbackView,
          category: KoyalTrackingCategories.screenView,
          action: KoyalAnalyticsConstants.view,
          label: 'feature_feedback',
          feedbackJourneyId: widget.journeyId,
        );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _ratingTitle(BuildContext context, int rating) {
    final l10n = L10nCappHome.of(context);
    final texts = [
      l10n.feedbackRating1,
      l10n.feedbackRating2,
      l10n.feedbackRating3,
      l10n.feedbackRating4,
      l10n.feedbackRating5,
    ];
    return texts[rating - 1];
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => context.get<FeedbackBloc>()..add(FeedbackEvent.init(journeyId: widget.journeyId)),
      child: BlocBuilder<FeedbackBloc, FeedbackState>(
        builder: (context, state) => KoyalWillPopScope(
          onWillPop: () async {
            context.read<FeedbackBloc>().add(const FeedbackEvent.postpone());
            return true;
          },
          child: SingleChildScrollView(
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            child: state.isFeedbackCompleted
                ? FeedbackComplete(
                    feedbackType: FeedbackType.featureFeedback,
                    feedbackPolarity: state.feedbackPolarity,
                    isLivechatEnabled: _isLivechatButtonEnabled,
                  )
                : FeedbackLoader(
                    child: SafeArea(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () => FocusScope.of(context).unfocus(),
                        child: KoyalPadding.normalAll(
                          right: false,
                          left: false,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SectionHeading(
                                title: state.question,
                              ),
                              KoyalPadding.normalAll(
                                bottom: false,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: Iterable<int>.generate(_topRating).map((i) {
                                    final rating = i + 1;
                                    return InkWell(
                                      onTap: () {
                                        context.get<CappTrackingService>().trackFeedbackEvent(
                                              event: KoyalEvent.appFeatureFeedbackValueClick,
                                              category: KoyalTrackingCategories.featureFeedback,
                                              action: KoyalAnalyticsConstants.click,
                                              label: KoyalTrackingLabels.feedbackValue,
                                              feedbackJourneyId: widget.journeyId,
                                              sas: '1',
                                            );
                                        context.read<FeedbackBloc>().add(FeedbackEvent.selectRating(rating));
                                        context
                                            .read<FeedbackBloc>()
                                            .add(FeedbackEvent.loadFeedbackCategories(journeyId: widget.journeyId));
                                      },
                                      child: AssetSvgImage(
                                        'assets/images/feedback/rating_$rating${rating == state.selectedRating ? '_selected' : ''}.svg',
                                        package: 'capp_home_core',
                                        height: 40,
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                              const SizedBox(height: KoyalPadding.paddingSmall),
                              if (state.selectedRating == _topRating)
                                KoyalText.body2(
                                  color: ColorTheme.of(context).successTextColor,
                                  _ratingTitle(context, state.selectedRating!),
                                )
                              else if (state.selectedRating != null)
                                KoyalText.body3(
                                  color: ColorTheme.of(context).defaultTextColor,
                                  _ratingTitle(context, state.selectedRating!),
                                )
                              else
                                KoyalText.body3(color: ColorTheme.of(context).defaultTextColor, ''),
                              if (state.feedbackCategories.isNotEmpty && state.selectedRating != null)
                                SectionHeading(
                                  title: state.selectedRating == _topRating
                                      ? L10nCappHome.of(context).feedbackFormTitleGreat
                                      : L10nCappHome.of(context).feedbackFormTitle,
                                  subtitle: L10nCappHome.of(context).feedbackFormSubtitle,
                                ),
                              if (state.feedbackCategories.isNotEmpty)
                                KoyalPadding.normalAll(
                                  top: false,
                                  bottom: false,
                                  child: Wrap(
                                    alignment: WrapAlignment.center,
                                    spacing: KoyalPadding.paddingSmall,
                                    runSpacing: KoyalPadding.paddingSmall,
                                    children: state.feedbackCategories.entries.map(
                                      (category) {
                                        return KoyalFilterChip(
                                          key: Key('__feedbackCategoryChip_${category.key.id}__'),
                                          title: category.key.title,
                                          isSelected: category.value,
                                          onSelected: (selected) {
                                            context.get<CappTrackingService>().trackFeedbackEvent(
                                                  event: KoyalEvent.appFeatureFeedbackCategoriesClick,
                                                  category: KoyalTrackingCategories.featureFeedback,
                                                  action: KoyalAnalyticsConstants.click,
                                                  label: 'improve_options',
                                                  feedbackJourneyId: widget.journeyId,
                                                  feedbackImproveOptionsId: category.key.id,
                                                );
                                            context.read<FeedbackBloc>().add(
                                                  FeedbackEvent.selectCategory(
                                                    category: category.key,
                                                    selected: !category.value,
                                                  ),
                                                );
                                          },
                                        );
                                      },
                                    ).toList(),
                                  ),
                                ),
                              const SizedBox(height: KoyalPadding.paddingNormal),
                              FeedbackInputField(
                                controller: _controller,
                                hintText: L10nCappHome.of(context).feedbackFormHint,
                              ),
                              VerticalButtonsLayout(
                                primaryButton: PrimaryButton(
                                  text: L10nCappHome.of(context).rateAppConfirm,
                                  isInProgress: state.isSending,
                                  onPressed: !canSubmit(state)
                                      ? null
                                      : () {
                                          context.get<CappTrackingService>().trackFeedbackEvent(
                                                event: KoyalEvent.appFeatureFeedbackSubmitClick,
                                                category: KoyalTrackingCategories.featureFeedback,
                                                action: KoyalAnalyticsConstants.click,
                                                label: KoyalTrackingLabels.submit,
                                                feedbackJourneyId: widget.journeyId,
                                                featureFeedbackText: state.feedbackMessage,
                                                feedbackValueId: '${state.selectedRating!}',
                                                feedbackImproveOptionsId: state.feedbackCategories.entries
                                                    .where((element) => element.value)
                                                    .map((e) => e.key)
                                                    .toList()
                                                    .toString(),
                                                sas: '1',
                                              );
                                          context.read<FeedbackBloc>().add(const FeedbackEvent.sendFeedback());
                                        },
                                ),
                                tertiaryButton: TertiaryButton(
                                  text: L10nCappHome.of(context).commonNotNow,
                                  onPressed: () {
                                    context.get<CappTrackingService>().trackFeedbackEvent(
                                          event: KoyalEvent.appFeatureFeedbackNotNowClick,
                                          category: KoyalTrackingCategories.featureFeedback,
                                          action: KoyalAnalyticsConstants.click,
                                          label: 'not_now',
                                          feedbackJourneyId: widget.journeyId,
                                        );
                                    context.read<FeedbackBloc>().add(const FeedbackEvent.postpone());
                                    context.navigator.pop(true);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
