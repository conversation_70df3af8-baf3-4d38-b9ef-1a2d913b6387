import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../l10n/i18n.dart';
import '../../application/feedback/feedback_bloc.dart';

class FeedbackInputField extends StatelessWidget {
  final String hintText;
  final TextEditingController controller;
  const FeedbackInputField({Key? key, required this.hintText, required this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          KoyalPadding.normalAll(
            bottom: false,
            top: false,
            child: TextField(
              cursorColor: ColorTheme.of(context).defaultTextColor,
              textInputAction: TextInputAction.done,
              controller: controller,
              minLines: 3,
              maxLines: 10,
              keyboardType: TextInputType.multiline,
              style: TextStyleTheme.of(context).body2.copyWith(color: ColorTheme.of(context).defaultTextColor),
              decoration: InputDecoration(
                isDense: true,
                hintText: hintText,
              ),
              onEditingComplete: () => FocusManager.instance.primaryFocus?.unfocus(),
              onChanged: (text) =>
                  context.read<FeedbackBloc>().add(FeedbackEvent.feedbackMessageChanged(feedbackMessage: text)),
            ),
          ),
          if (context.watch<FeedbackBloc>().state.feedbackMessage?.isNotEmpty ?? false)
            Padding(
              padding: const EdgeInsets.fromLTRB(
                KoyalPadding.paddingNormal,
                // top is a bit over the top
                KoyalPadding.paddingSmall + KoyalPadding.paddingXSmall,
                KoyalPadding.paddingNormal,
                0,
              ),
              child: Alert(text: L10nCappHome.of(context).feedbackFormWarning, theme: AlertTheme.warning()),
            ),
        ],
      ),
    );
  }
}
