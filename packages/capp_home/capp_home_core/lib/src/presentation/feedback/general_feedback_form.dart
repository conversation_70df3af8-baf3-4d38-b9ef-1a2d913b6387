import 'dart:async';

import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flare_flutter/flare_cache_builder.dart';
import 'package:flare_flutter/provider/asset_flare.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import 'detailed_feedback_form.dart';
import 'feedback_loader.dart';
import 'feedback_submit_criteria_mixin.dart';
import 'rate_switch.dart';

class GeneralFeedbackForm extends StatelessWidget with FeedbackSubmitCriteriaMixin {
  const GeneralFeedbackForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: context.get<FeedbackBloc>(),
      child: BlocBuilder<FeedbackBloc, FeedbackState>(
        builder: (context, state) => SingleChildScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          child: state.isFeedbackCompleted
              ? FeedbackComplete(
                  feedbackPolarity: state.feedbackPolarity,
                  feedbackType: FeedbackType.generalFeedback,
                  isLivechatEnabled: context.isFlagEnabledRead(FeatureFlag.generalFeedbackLivechatButton),
                )
              : FeedbackLoader(
                  child: SafeArea(
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () => FocusScope.of(context).unfocus(),
                      child: KoyalPadding.normalAll(
                        right: false,
                        left: false,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SectionHeading(
                              title: L10nCappHome.of(context).feedbackFormHeader,
                            ),
                            FlareCacheBuilder(
                              [
                                AssetFlare(
                                  bundle: rootBundle,
                                  name: 'packages/capp_ui/assets/animation/thumb_up.flr',
                                ),
                                AssetFlare(
                                  bundle: rootBundle,
                                  name: 'packages/capp_ui/assets/animation/thumb_down.flr',
                                ),
                              ],
                              builder: (context, isWarm) {
                                return Column(
                                  children: !isWarm
                                      ? [_LoadingContent()]
                                      : [
                                          const RateSwitch(),
                                          if (state.selectedRating != null) ...{
                                            AppearAnimation(
                                              child: state.isCategoriesLoading
                                                  ? const KoyalPadding.large(
                                                      child: Center(child: KoyalProgressIndicator.large()),
                                                    )
                                                  : DetailedFeedbackForm(
                                                      initialText: state.feedbackMessage ?? '',
                                                    ),
                                            ),
                                          } else ...{
                                            const SizedBox(height: KoyalPadding.paddingSmall),
                                          },
                                          if (state.selectedRating != null)
                                            VerticalButtonsLayout(
                                              primaryButton: (state.selectedRating == 1 &&
                                                      context.isFlagEnabledRead(FeatureFlag.generalFeedbackRateApp) &&
                                                      context.isFlagEnabledRead(FeatureFlag.allowNativeRatingDialog))
                                                  ? _createPrimaryButton(
                                                      context,
                                                      key: const Key('__feedbackFormSubmitAndRateButon__'),
                                                      state: state,
                                                      isClosing: true,
                                                    )
                                                  : _createPrimaryButton(
                                                      context,
                                                      key: const Key('__feedbackFormSubmitButon__'),
                                                      state: state,
                                                      isClosing: context.isFlagEnabledRead(
                                                        FeatureFlag.allowNativeRatingDialog,
                                                      ),
                                                    ),
                                              secondaryButton: (state.selectedRating == 1 &&
                                                      context.isFlagEnabledRead(FeatureFlag.generalFeedbackRateApp) &&
                                                      context.isFlagEnabledRead(FeatureFlag.allowNativeRatingDialog))
                                                  ? TertiaryButton(
                                                      key: const Key('__feedbackFormSubmitAndCloseButon__'),
                                                      isInProgress: state.isSending,
                                                      text: L10nCappHome.of(context).submitAndCloseButton,
                                                      onPressed: !canSubmit(state)
                                                          ? null
                                                          : () async {
                                                              if (state.selectedRating == 0) {
                                                                _trackRatingSelected(
                                                                  context,
                                                                  negative: true,
                                                                  isClosing: true,
                                                                  state: state,
                                                                );
                                                                context
                                                                    .read<FeedbackBloc>()
                                                                    .add(const FeedbackEvent.sendNegativeFeedback());
                                                              } else {
                                                                _trackRatingSelected(
                                                                  context,
                                                                  isClosing: true,
                                                                  state: state,
                                                                );
                                                                context.read<FeedbackBloc>().add(
                                                                      const FeedbackEvent.sendPositiveFeedback(),
                                                                    );
                                                              }
                                                            },
                                                    )
                                                  : null,
                                            ),
                                        ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  PrimaryButton _createPrimaryButton(
    BuildContext context, {
    required Key key,
    required FeedbackState state,
    required bool isClosing,
  }) {
    return PrimaryButton(
      key: key,
      isInProgress: state.isSending,
      text: L10nCappHome.of(context).rateAppConfirm,
      onPressed: state.selectedRating == null || state.isCategoriesLoading || !canSubmit(state)
          ? null
          : () async {
              await _sendFeedback(
                context,
                state,
                isClosing,
              );
            },
    );
  }

  void _trackRatingSelected(
    BuildContext context, {
    bool negative = false,
    bool isClosing = false,
    bool isRatingApp = false,
    required FeedbackState state,
  }) {
    if (negative) {
      unawaited(
        context.get<CappTrackingService>().trackEvent(
          // NAV074
          event: KoyalEvent.appFeedbackNegativeSubmitClick,
          eventCategory: KoyalTrackingCategories.feedbackNegative,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: KoyalAnalyticsConstants.submit,
          customDimensions: {
            TrackingProperties.propertyCdSas: '1',
          },
        ),
      );
    } else {
      final label = isRatingApp
          ? KoyalAnalyticsConstants.submitAndRate
          : isClosing
              ? KoyalAnalyticsConstants.submitAndClose
              : KoyalAnalyticsConstants.submit;
      final event = isRatingApp
          // NAV102
          ? KoyalEvent.appFeedbackPositiveSubmitAndRate
          : isClosing
              // NAV103
              ? KoyalEvent.appFeedbackPositiveSubmitAndClose
              // NAV069
              : KoyalEvent.appFeedbackSubmitClick;

      final category =
          !isRatingApp && !isClosing ? KoyalTrackingCategories.feedback : KoyalTrackingCategories.feedbackPositive;

      unawaited(
        context.get<CappTrackingService>().trackEvent(
          event: event,
          eventCategory: category,
          eventAction: KoyalAnalyticsConstants.click,
          eventLabel: label,
          customDimensions: {TrackingProperties.propertyCdSas: '1'},
        ),
      );
    }
  }

  Future<void> _sendFeedback(
    BuildContext context,
    FeedbackState state,
    bool ratingEnabled, {
    bool isClosing = false,
  }) async {
    final bloc = context.read<FeedbackBloc>();
    final isNegative = state.selectedRating == 0;
    _trackRatingSelected(
      context,
      negative: isNegative,
      isRatingApp: ratingEnabled,
      isClosing: isClosing,
      state: state,
    );
    final isNativeRatingAvailable = ratingEnabled && !isClosing;
    if (isNegative) {
      bloc.add(const FeedbackEvent.sendNegativeFeedback());
    } else {
      if (isNativeRatingAvailable) {
        // Huawei does not support native rating dialog at this point
        if (state.isHuawei) {
          final appGalleryUrl = 'market://details?id=${state.googlePlayId}';

          unawaited(GmaPlatform.launchUrl(appGalleryUrl));
        } else {
          if (await InAppReview.instance.isAvailable() && state.canRateApp) {
            await InAppReview.instance.requestReview();
          } else {
            await InAppReview.instance.openStoreListing(appStoreId: state.appStoreId);
          }
        }
      }
      bloc.add(FeedbackEvent.sendPositiveFeedback(isRating: isNativeRatingAvailable));
    }
  }
}

class _LoadingContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: KoyalPadding.paddingSmall),
        KoyalPadding.large(
          child: Center(child: KoyalProgressIndicator.large()),
        ),
        SizedBox(height: KoyalPadding.paddingSmall),
      ],
    );
  }
}
