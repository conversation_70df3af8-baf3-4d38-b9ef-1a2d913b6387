import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_home_core.dart';
import 'feedback_input_field.dart';

class DetailedFeedbackForm extends StatefulWidget {
  final String initialText;
  const DetailedFeedbackForm({
    Key? key,
    required this.initialText,
  }) : super(key: key);

  @override
  State<DetailedFeedbackForm> createState() => _DetailedFeedbackFormState();
}

class _DetailedFeedbackFormState extends State<DetailedFeedbackForm> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    final viewEvent = _FeedbackViewEvent(
      isNegative: context.read<FeedbackBloc>().state.feedbackPolarity == FeedbackPolarity.negative,
    );

    _trackFeedbackEvent(
      context,
      viewEvent,
      customDimensions: {
        TrackingProperties.propertyCdSas: '1',
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FeedbackBloc, FeedbackState>(
      builder: (context, state) => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (state.feedbackCategories.isNotEmpty) ...[
            SectionHeading(
              title: state.feedbackPolarity == FeedbackPolarity.negative
                  ? L10nCappHome.of(context).feedbackFormTitle
                  : L10nCappHome.of(context).feedbackFormTitlePositive,
              subtitle: L10nCappHome.of(context).feedbackFormSubtitle,
            ),
            const SizedBox(height: KoyalPadding.paddingSmall),
            KoyalPadding.normalAll(
              top: false,
              bottom: false,
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: KoyalPadding.paddingSmall,
                runSpacing: KoyalPadding.paddingSmall,
                children: state.feedbackCategories.entries.map(
                  (category) {
                    return KoyalFilterChip(
                      key: Key('__feedbackCategoryChip_${category.key.id}__'),
                      title: category.key.title,
                      isSelected: category.value,
                      onSelected: (selected) {
                        final clickTrackingEvent = _FeedbackClickEvent(
                          isNegative: state.feedbackPolarity == FeedbackPolarity.negative,
                        );

                        _trackFeedbackEvent(
                          context,
                          clickTrackingEvent,
                          customDimensions: {
                            TrackingProperties.propertyCdItemId: category.key.id,
                            TrackingProperties.propertyCdSas: '1',
                          },
                        );
                        context.read<FeedbackBloc>().add(
                              FeedbackEvent.selectCategory(category: category.key, selected: !category.value),
                            );
                      },
                    );
                  },
                ).toList(),
              ),
            ),
          ],
          const SizedBox(height: KoyalPadding.paddingNormal),
          FeedbackInputField(
            key: const Key('__feedbackFormTextInput__'),
            controller: _controller,
            hintText: L10nCappHome.of(context).feedbackFormHint,
          ),
        ],
      ),
    );
  }

  void _trackFeedbackEvent(
    BuildContext context,
    _FeedbackEventFactory eventFactory, {
    Map<String, String> customDimensions = const {},
  }) {
    context.get<CappTrackingService>().trackEvent(
          event: eventFactory.event,
          eventCategory: eventFactory.category,
          eventAction: eventFactory.action,
          eventLabel: eventFactory.label,
          customDimensions: customDimensions,
        );
  }
}

abstract class _FeedbackEventFactory {
  final bool isNegative;

  const _FeedbackEventFactory({
    required this.isNegative,
  });

  KoyalEvent get event => isNegative ? negativeEvent : positiveEvent;
  String get category => isNegative ? negativeCategory : positiveCategory;

  String get label => isNegative ? negativeLabel : positiveLabel;

  String get action;

  KoyalEvent get negativeEvent;
  KoyalEvent get positiveEvent;

  String get negativeCategory;
  String get positiveCategory;

  String get negativeLabel;
  String get positiveLabel;
}

// Event for view called in initState
final class _FeedbackViewEvent extends _FeedbackEventFactory {
  const _FeedbackViewEvent({required super.isNegative});

  // NAV072
  @override
  KoyalEvent get negativeEvent => KoyalEvent.appFeedbackNegativeView;

  // NAV099
  @override
  KoyalEvent get positiveEvent => KoyalEvent.appFeedbackSuggestionsPositive;

  @override
  String get action => KoyalAnalyticsConstants.view;

  @override
  String get negativeCategory => KoyalTrackingCategories.feedbackNegative;

  @override
  String get positiveCategory => KoyalTrackingCategories.screenView;

  @override
  String get negativeLabel => KoyalTrackingLabels.feedbackNegative;

  @override
  String get positiveLabel => KoyalTrackingLabels.feedbackSuggestionPositive;
}

// Event for click on category
final class _FeedbackClickEvent extends _FeedbackEventFactory {
  const _FeedbackClickEvent({required super.isNegative});

  // NAV075
  @override
  KoyalEvent get negativeEvent => KoyalEvent.appFeedbackNegativeImproveOptionClick;

  // NAV100
  @override
  KoyalEvent get positiveEvent => KoyalEvent.appFeedbackPositiveFavourireOptions;

  @override
  String get action => KoyalAnalyticsConstants.click;

  @override
  String get negativeCategory => KoyalTrackingCategories.feedbackNegative;

  @override
  String get positiveCategory => KoyalTrackingCategories.feedbackPositive;

  @override
  String get negativeLabel => KoyalTrackingLabels.improveOptions;

  @override
  String get positiveLabel => KoyalTrackingLabels.favouriteOptions;
}
