// ignore_for_file: use_build_context_synchronously

import 'package:capp_tracking/capp_tracking.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../infrastructure/feedback/i_feedback_journey_service.dart';
import 'feedback_form.dart';
import 'general_feedback_form.dart';

Future showGeneralFeedbackOverlay(BuildContext context) {
  context.get<CappTrackingService>().trackEvent(
        event: KoyalEvent.appFeedbackSuggestionsView,
        eventCategory: KoyalTrackingCategories.feedback,
        eventAction: KoyalAnalyticsConstants.view,
        eventLabel: KoyalTrackingCategories.feedback,
      );
  return showKoyalBottomSheet<void>(
    reverse: true,
    context: context,
    builder: (context) => const GeneralFeedbackForm(key: Key('__generalFeedbackForm__')),
  );
}

Future showFeedbackOverlay(BuildContext context, {required String journeyId}) async {
  final shouldDisplay = await context.get<IFeedbackJourneyService>().isEligibleToDisplay(journeyId);
  if (shouldDisplay) {
    final result = await showKoyalBottomSheet<bool>(
          reverse: true,
          context: context,
          builder: (context) => FeedbackForm(key: const Key('__feedbackForm__'), journeyId: journeyId),
        ) ??
        false;
    if (!result) {
      // workaround for flutter issue: https://github.com/flutter/flutter/issues/106942
      // if the sheet was dismissed by dragging it down, FeedbackForm.onWillPop event is not called

      final service = context.get<IFeedbackJourneyService>();
      final status = await service.getJourneyStatus(journeyId);
      if (status?.isComplete != true) {
        await service.postponeJourney(journeyId: journeyId);
      }
    }
  }
}
