import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../application/feedback/feedback_bloc.dart';

class FeedbackLoader extends StatelessWidget {
  final Widget child;
  const FeedbackLoader({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) => BlocBuilder<FeedbackBloc, FeedbackState>(
        builder: (context, state) {
          return Stack(
            children: [
              child,
              if (state.isLoading)
                Positioned.fill(
                  child: Align(
                    child: Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      color: ColorTheme.of(context).foreground90Color.withAlpha(15),
                      child: const KoyalPadding.large(child: KoyalProgressIndicator.large()),
                    ),
                  ),
                ),
            ],
          );
        },
      );
}
