import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import 'widgets/skeleton_widgets.dart';

class SkeletonLoading extends StatelessWidget {
  const SkeletonLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorTheme.of(context).backgroundColor,
      ),
      child: <PERSON><PERSON><PERSON><PERSON>(
        controller: ScrollController(),
        children: [
          //SAS Shimmer banner
          KoyalPadding.small(
            left: false,
            right: false,
            bottom: false,
            child: homePageItemShimmer(200),
          ),

          // Action Belt Shimmer
          const ActionBeltItemsShimmer(
            numOfItems: 4,
          ),

          //Games Banner Shimmer
          homePageItemShimmer(165),

          //Hot Deals Shimmer
          homePageItemShimmer(165),

          //Promos Shimmer
          const ItemsListShimmer(
            numOfItems: 3,
          ),
        ],
      ),
    );
  }
}
