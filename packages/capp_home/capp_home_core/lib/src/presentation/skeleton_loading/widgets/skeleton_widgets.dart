import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

Widget homePageItemShimmer(double height) {
  return KoyalShimmer(
    child: KoyalPadding.normalHorizontal(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 100,
            height: 25,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
            ),
          ),
          KoyalPadding.normalVertical(
            child: Container(
              width: double.maxFinite,
              height: height,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

class ActionBeltItemsShimmer extends StatelessWidget {
  final int numOfItems;
  const ActionBeltItemsShimmer({super.key, required this.numOfItems});

  @override
  Widget build(BuildContext context) {
    return KoyalShimmer(
      child: KoyalPadding.normalAll(
        child: SizedBox(
          width: double.maxFinite,
          height: 90,
          child: ListView.builder(
            controller: ScrollController(),
            scrollDirection: Axis.horizontal,
            itemCount: numOfItems,
            itemBuilder: (context, index) {
              return KoyalPadding.normalAll(
                top: false,
                bottom: false,
                left: index != 0,
                right: index != numOfItems,
                child: actionBeltItem(),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget actionBeltItem() {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        KoyalPadding.smallVertical(
          child: Container(
            width: 70,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
            ),
          ),
        ),
      ],
    );
  }
}

class ItemsListShimmer extends StatelessWidget {
  final int numOfItems;
  const ItemsListShimmer({super.key, required this.numOfItems});

  @override
  Widget build(BuildContext context) {
    return KoyalShimmer(
      child: KoyalPadding.normalAll(
        child: Column(
          children: [
            KoyalPadding.small(
              left: false,
              right: false,
              top: false,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 100,
                    height: 25,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  Container(
                    width: 100,
                    height: 25,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                ],
              ),
            ),
            ...buildItems(),
          ],
        ),
      ),
    );
  }

  List<Widget> buildItems() {
    final items = <Widget>[];
    for (var i = 0; i < numOfItems; i++) {
      items.add(
        KoyalPadding.small(
          left: false,
          right: false,
          bottom: false,
          child: Container(
            width: double.maxFinite,
            height: 85,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
            ),
          ),
        ),
      );
    }
    return items;
  }
}
