// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_reminder_contract.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentReminderContract {
  String get contractNumber => throw _privateConstructorUsedError;
  RepaymentReminderContractType get contractType =>
      throw _privateConstructorUsedError;
  String get productName => throw _privateConstructorUsedError;
  bool? get repaymentEligible => throw _privateConstructorUsedError;
  Decimal? get dueAmount => throw _privateConstructorUsedError;
  DateTime? get dueDate => throw _privateConstructorUsedError;
  int get closeCount => throw _privateConstructorUsedError;
  String? get productType => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentReminderContractCopyWith<RepaymentReminderContract> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentReminderContractCopyWith<$Res> {
  factory $RepaymentReminderContractCopyWith(RepaymentReminderContract value,
          $Res Function(RepaymentReminderContract) then) =
      _$RepaymentReminderContractCopyWithImpl<$Res, RepaymentReminderContract>;
  @useResult
  $Res call(
      {String contractNumber,
      RepaymentReminderContractType contractType,
      String productName,
      bool? repaymentEligible,
      Decimal? dueAmount,
      DateTime? dueDate,
      int closeCount,
      String? productType});
}

/// @nodoc
class _$RepaymentReminderContractCopyWithImpl<$Res,
        $Val extends RepaymentReminderContract>
    implements $RepaymentReminderContractCopyWith<$Res> {
  _$RepaymentReminderContractCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
    Object? contractType = null,
    Object? productName = null,
    Object? repaymentEligible = freezed,
    Object? dueAmount = freezed,
    Object? dueDate = freezed,
    Object? closeCount = null,
    Object? productType = freezed,
  }) {
    return _then(_value.copyWith(
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
      contractType: null == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as RepaymentReminderContractType,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      repaymentEligible: freezed == repaymentEligible
          ? _value.repaymentEligible
          : repaymentEligible // ignore: cast_nullable_to_non_nullable
              as bool?,
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      closeCount: null == closeCount
          ? _value.closeCount
          : closeCount // ignore: cast_nullable_to_non_nullable
              as int,
      productType: freezed == productType
          ? _value.productType
          : productType // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentReminderContractCopyWith<$Res>
    implements $RepaymentReminderContractCopyWith<$Res> {
  factory _$$_RepaymentReminderContractCopyWith(
          _$_RepaymentReminderContract value,
          $Res Function(_$_RepaymentReminderContract) then) =
      __$$_RepaymentReminderContractCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String contractNumber,
      RepaymentReminderContractType contractType,
      String productName,
      bool? repaymentEligible,
      Decimal? dueAmount,
      DateTime? dueDate,
      int closeCount,
      String? productType});
}

/// @nodoc
class __$$_RepaymentReminderContractCopyWithImpl<$Res>
    extends _$RepaymentReminderContractCopyWithImpl<$Res,
        _$_RepaymentReminderContract>
    implements _$$_RepaymentReminderContractCopyWith<$Res> {
  __$$_RepaymentReminderContractCopyWithImpl(
      _$_RepaymentReminderContract _value,
      $Res Function(_$_RepaymentReminderContract) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
    Object? contractType = null,
    Object? productName = null,
    Object? repaymentEligible = freezed,
    Object? dueAmount = freezed,
    Object? dueDate = freezed,
    Object? closeCount = null,
    Object? productType = freezed,
  }) {
    return _then(_$_RepaymentReminderContract(
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
      contractType: null == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as RepaymentReminderContractType,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      repaymentEligible: freezed == repaymentEligible
          ? _value.repaymentEligible
          : repaymentEligible // ignore: cast_nullable_to_non_nullable
              as bool?,
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      closeCount: null == closeCount
          ? _value.closeCount
          : closeCount // ignore: cast_nullable_to_non_nullable
              as int,
      productType: freezed == productType
          ? _value.productType
          : productType // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_RepaymentReminderContract implements _RepaymentReminderContract {
  const _$_RepaymentReminderContract(
      {required this.contractNumber,
      required this.contractType,
      required this.productName,
      this.repaymentEligible,
      this.dueAmount,
      this.dueDate,
      this.closeCount = 0,
      this.productType});

  @override
  final String contractNumber;
  @override
  final RepaymentReminderContractType contractType;
  @override
  final String productName;
  @override
  final bool? repaymentEligible;
  @override
  final Decimal? dueAmount;
  @override
  final DateTime? dueDate;
  @override
  @JsonKey()
  final int closeCount;
  @override
  final String? productType;

  @override
  String toString() {
    return 'RepaymentReminderContract(contractNumber: $contractNumber, contractType: $contractType, productName: $productName, repaymentEligible: $repaymentEligible, dueAmount: $dueAmount, dueDate: $dueDate, closeCount: $closeCount, productType: $productType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentReminderContract &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.contractType, contractType) ||
                other.contractType == contractType) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.repaymentEligible, repaymentEligible) ||
                other.repaymentEligible == repaymentEligible) &&
            (identical(other.dueAmount, dueAmount) ||
                other.dueAmount == dueAmount) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.closeCount, closeCount) ||
                other.closeCount == closeCount) &&
            (identical(other.productType, productType) ||
                other.productType == productType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      contractNumber,
      contractType,
      productName,
      repaymentEligible,
      dueAmount,
      dueDate,
      closeCount,
      productType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentReminderContractCopyWith<_$_RepaymentReminderContract>
      get copyWith => __$$_RepaymentReminderContractCopyWithImpl<
          _$_RepaymentReminderContract>(this, _$identity);
}

abstract class _RepaymentReminderContract implements RepaymentReminderContract {
  const factory _RepaymentReminderContract(
      {required final String contractNumber,
      required final RepaymentReminderContractType contractType,
      required final String productName,
      final bool? repaymentEligible,
      final Decimal? dueAmount,
      final DateTime? dueDate,
      final int closeCount,
      final String? productType}) = _$_RepaymentReminderContract;

  @override
  String get contractNumber;
  @override
  RepaymentReminderContractType get contractType;
  @override
  String get productName;
  @override
  bool? get repaymentEligible;
  @override
  Decimal? get dueAmount;
  @override
  DateTime? get dueDate;
  @override
  int get closeCount;
  @override
  String? get productType;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentReminderContractCopyWith<_$_RepaymentReminderContract>
      get copyWith => throw _privateConstructorUsedError;
}
