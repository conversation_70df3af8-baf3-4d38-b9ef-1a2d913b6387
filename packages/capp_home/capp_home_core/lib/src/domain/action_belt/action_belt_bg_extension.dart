import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/material.dart';

extension ActionBeltBgColorExtension on ActionBeltBgColor {
  Color getColor() {
    switch (this) {
      case ActionBeltBgColor.blue:
        return const Color(0xFFEDF6FB);
      case ActionBeltBgColor.red:
        return const Color(0xFFFFF5F7);
      case ActionBeltBgColor.green:
        return const Color(0xFFF1FCEB);
      case ActionBeltBgColor.grey:
        return const Color(0xFFF8F8F8);
      case ActionBeltBgColor.greyBlue:
        return const Color(0xFFE9F1F4);
      case ActionBeltBgColor.orange:
        return const Color(0xFFFFF8EE);
      case ActionBeltBgColor.purple:
        return const Color(0xFFF8F1FE);
      default:
        return const Color(0xFFF8F8F8);
    }
  }
}
