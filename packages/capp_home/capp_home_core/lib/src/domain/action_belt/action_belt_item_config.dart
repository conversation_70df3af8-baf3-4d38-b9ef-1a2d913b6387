import 'package:flutter/widgets.dart';

class ActionBeltItemConfig {
  final String label;
  final String trackingLabel;
  final VoidCallback onTap;
  final IconData? icon;
  final String? path;
  final String? package;
  final String? iconUrl;
  final String? badge;
  final String? featureFlagName;
  final Color? bgColor;
  final bool showIconWithoutFrame;
  String get deeplinkAction => trackingLabel.toLowerCase();

  const ActionBeltItemConfig({
    required this.label,
    required this.trackingLabel,
    required this.onTap,
    this.icon,
    this.path,
    this.package,
    this.showIconWithoutFrame = false,
    this.iconUrl,
    this.badge,
    this.bgColor,
    this.featureFlagName,
  });
}
