import 'package:dartz/dartz.dart';

import '../../../../capp_home_core.dart';
import '../models/unread_count.dart';

abstract class IInboxRepository {
  Future<Either<InboxFailure, String>> getInboxMessage(String id);
  Future<Either<InboxFailure, Unit>> markAsArchived(String id);
  Future<Either<InboxFailure, Unit>> markAsUnarchived(String id);
  Future<Either<InboxFailure, Unit>> markAsRead(String id);
  Future<Either<InboxFailure, Unit>> markAsUnread(String id);
  Future<Either<InboxFailure, int>> markAllRead();
  Future<Either<InboxFailure, List<UnreadCount>>> getUnreadCount({List<String>? categories});
  Future<Either<InboxFailure, bool>> hasUnreadMessages({String? categoryId});
  Future<Either<InboxFailure, InboxMessageList>> getInboxList({int? page, int? pageSize, String? categoryId});
  List<InboxMessage> getInboxMessagesLocal();
  void clearInboxMessagesLocal();
}
