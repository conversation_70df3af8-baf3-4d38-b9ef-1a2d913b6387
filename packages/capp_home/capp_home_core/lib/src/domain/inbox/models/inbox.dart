import 'package:capp_domain/capp_domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'inbox.freezed.dart';

@freezed
class InboxMessageList with _$InboxMessageList {
  const factory InboxMessageList({
    @Default(10) int pageSize,
    @Default(1) int page,
    @Default(0) int totalCount,
    @Default(<InboxMessage>[]) List<InboxMessage> items,
  }) = _InboxMessageList;
}

@freezed
class InboxMessage with _$InboxMessage {
  const factory InboxMessage({
    required String id,
    required String title,
    String? text,
    String? imageUrl,
    @Default(InboxMessageType.all) InboxMessageType type,
    required String categoryId,
    @Default(false) bool archived,
    DateTime? dateRead,
    String? textPreview,
    String? externalMessageId,
    required DateTime dataReceived,
  }) = _InboxMessage;
}
