// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'inbox.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$InboxMessageList {
  int get pageSize => throw _privateConstructorUsedError;
  int get page => throw _privateConstructorUsedError;
  int get totalCount => throw _privateConstructorUsedError;
  List<InboxMessage> get items => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InboxMessageListCopyWith<InboxMessageList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InboxMessageListCopyWith<$Res> {
  factory $InboxMessageListCopyWith(
          InboxMessageList value, $Res Function(InboxMessageList) then) =
      _$InboxMessageListCopyWithImpl<$Res, InboxMessageList>;
  @useResult
  $Res call({int pageSize, int page, int totalCount, List<InboxMessage> items});
}

/// @nodoc
class _$InboxMessageListCopyWithImpl<$Res, $Val extends InboxMessageList>
    implements $InboxMessageListCopyWith<$Res> {
  _$InboxMessageListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? page = null,
    Object? totalCount = null,
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<InboxMessage>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InboxMessageListCopyWith<$Res>
    implements $InboxMessageListCopyWith<$Res> {
  factory _$$_InboxMessageListCopyWith(
          _$_InboxMessageList value, $Res Function(_$_InboxMessageList) then) =
      __$$_InboxMessageListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int pageSize, int page, int totalCount, List<InboxMessage> items});
}

/// @nodoc
class __$$_InboxMessageListCopyWithImpl<$Res>
    extends _$InboxMessageListCopyWithImpl<$Res, _$_InboxMessageList>
    implements _$$_InboxMessageListCopyWith<$Res> {
  __$$_InboxMessageListCopyWithImpl(
      _$_InboxMessageList _value, $Res Function(_$_InboxMessageList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageSize = null,
    Object? page = null,
    Object? totalCount = null,
    Object? items = null,
  }) {
    return _then(_$_InboxMessageList(
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      page: null == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<InboxMessage>,
    ));
  }
}

/// @nodoc

class _$_InboxMessageList implements _InboxMessageList {
  const _$_InboxMessageList(
      {this.pageSize = 10,
      this.page = 1,
      this.totalCount = 0,
      final List<InboxMessage> items = const <InboxMessage>[]})
      : _items = items;

  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final int page;
  @override
  @JsonKey()
  final int totalCount;
  final List<InboxMessage> _items;
  @override
  @JsonKey()
  List<InboxMessage> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'InboxMessageList(pageSize: $pageSize, page: $page, totalCount: $totalCount, items: $items)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InboxMessageList &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode => Object.hash(runtimeType, pageSize, page, totalCount,
      const DeepCollectionEquality().hash(_items));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InboxMessageListCopyWith<_$_InboxMessageList> get copyWith =>
      __$$_InboxMessageListCopyWithImpl<_$_InboxMessageList>(this, _$identity);
}

abstract class _InboxMessageList implements InboxMessageList {
  const factory _InboxMessageList(
      {final int pageSize,
      final int page,
      final int totalCount,
      final List<InboxMessage> items}) = _$_InboxMessageList;

  @override
  int get pageSize;
  @override
  int get page;
  @override
  int get totalCount;
  @override
  List<InboxMessage> get items;
  @override
  @JsonKey(ignore: true)
  _$$_InboxMessageListCopyWith<_$_InboxMessageList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$InboxMessage {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get text => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  InboxMessageType get type => throw _privateConstructorUsedError;
  String get categoryId => throw _privateConstructorUsedError;
  bool get archived => throw _privateConstructorUsedError;
  DateTime? get dateRead => throw _privateConstructorUsedError;
  String? get textPreview => throw _privateConstructorUsedError;
  String? get externalMessageId => throw _privateConstructorUsedError;
  DateTime get dataReceived => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $InboxMessageCopyWith<InboxMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InboxMessageCopyWith<$Res> {
  factory $InboxMessageCopyWith(
          InboxMessage value, $Res Function(InboxMessage) then) =
      _$InboxMessageCopyWithImpl<$Res, InboxMessage>;
  @useResult
  $Res call(
      {String id,
      String title,
      String? text,
      String? imageUrl,
      InboxMessageType type,
      String categoryId,
      bool archived,
      DateTime? dateRead,
      String? textPreview,
      String? externalMessageId,
      DateTime dataReceived});
}

/// @nodoc
class _$InboxMessageCopyWithImpl<$Res, $Val extends InboxMessage>
    implements $InboxMessageCopyWith<$Res> {
  _$InboxMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? text = freezed,
    Object? imageUrl = freezed,
    Object? type = null,
    Object? categoryId = null,
    Object? archived = null,
    Object? dateRead = freezed,
    Object? textPreview = freezed,
    Object? externalMessageId = freezed,
    Object? dataReceived = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as InboxMessageType,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      archived: null == archived
          ? _value.archived
          : archived // ignore: cast_nullable_to_non_nullable
              as bool,
      dateRead: freezed == dateRead
          ? _value.dateRead
          : dateRead // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      textPreview: freezed == textPreview
          ? _value.textPreview
          : textPreview // ignore: cast_nullable_to_non_nullable
              as String?,
      externalMessageId: freezed == externalMessageId
          ? _value.externalMessageId
          : externalMessageId // ignore: cast_nullable_to_non_nullable
              as String?,
      dataReceived: null == dataReceived
          ? _value.dataReceived
          : dataReceived // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InboxMessageCopyWith<$Res>
    implements $InboxMessageCopyWith<$Res> {
  factory _$$_InboxMessageCopyWith(
          _$_InboxMessage value, $Res Function(_$_InboxMessage) then) =
      __$$_InboxMessageCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String? text,
      String? imageUrl,
      InboxMessageType type,
      String categoryId,
      bool archived,
      DateTime? dateRead,
      String? textPreview,
      String? externalMessageId,
      DateTime dataReceived});
}

/// @nodoc
class __$$_InboxMessageCopyWithImpl<$Res>
    extends _$InboxMessageCopyWithImpl<$Res, _$_InboxMessage>
    implements _$$_InboxMessageCopyWith<$Res> {
  __$$_InboxMessageCopyWithImpl(
      _$_InboxMessage _value, $Res Function(_$_InboxMessage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? text = freezed,
    Object? imageUrl = freezed,
    Object? type = null,
    Object? categoryId = null,
    Object? archived = null,
    Object? dateRead = freezed,
    Object? textPreview = freezed,
    Object? externalMessageId = freezed,
    Object? dataReceived = null,
  }) {
    return _then(_$_InboxMessage(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as InboxMessageType,
      categoryId: null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      archived: null == archived
          ? _value.archived
          : archived // ignore: cast_nullable_to_non_nullable
              as bool,
      dateRead: freezed == dateRead
          ? _value.dateRead
          : dateRead // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      textPreview: freezed == textPreview
          ? _value.textPreview
          : textPreview // ignore: cast_nullable_to_non_nullable
              as String?,
      externalMessageId: freezed == externalMessageId
          ? _value.externalMessageId
          : externalMessageId // ignore: cast_nullable_to_non_nullable
              as String?,
      dataReceived: null == dataReceived
          ? _value.dataReceived
          : dataReceived // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$_InboxMessage implements _InboxMessage {
  const _$_InboxMessage(
      {required this.id,
      required this.title,
      this.text,
      this.imageUrl,
      this.type = InboxMessageType.all,
      required this.categoryId,
      this.archived = false,
      this.dateRead,
      this.textPreview,
      this.externalMessageId,
      required this.dataReceived});

  @override
  final String id;
  @override
  final String title;
  @override
  final String? text;
  @override
  final String? imageUrl;
  @override
  @JsonKey()
  final InboxMessageType type;
  @override
  final String categoryId;
  @override
  @JsonKey()
  final bool archived;
  @override
  final DateTime? dateRead;
  @override
  final String? textPreview;
  @override
  final String? externalMessageId;
  @override
  final DateTime dataReceived;

  @override
  String toString() {
    return 'InboxMessage(id: $id, title: $title, text: $text, imageUrl: $imageUrl, type: $type, categoryId: $categoryId, archived: $archived, dateRead: $dateRead, textPreview: $textPreview, externalMessageId: $externalMessageId, dataReceived: $dataReceived)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InboxMessage &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.archived, archived) ||
                other.archived == archived) &&
            (identical(other.dateRead, dateRead) ||
                other.dateRead == dateRead) &&
            (identical(other.textPreview, textPreview) ||
                other.textPreview == textPreview) &&
            (identical(other.externalMessageId, externalMessageId) ||
                other.externalMessageId == externalMessageId) &&
            (identical(other.dataReceived, dataReceived) ||
                other.dataReceived == dataReceived));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      text,
      imageUrl,
      type,
      categoryId,
      archived,
      dateRead,
      textPreview,
      externalMessageId,
      dataReceived);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InboxMessageCopyWith<_$_InboxMessage> get copyWith =>
      __$$_InboxMessageCopyWithImpl<_$_InboxMessage>(this, _$identity);
}

abstract class _InboxMessage implements InboxMessage {
  const factory _InboxMessage(
      {required final String id,
      required final String title,
      final String? text,
      final String? imageUrl,
      final InboxMessageType type,
      required final String categoryId,
      final bool archived,
      final DateTime? dateRead,
      final String? textPreview,
      final String? externalMessageId,
      required final DateTime dataReceived}) = _$_InboxMessage;

  @override
  String get id;
  @override
  String get title;
  @override
  String? get text;
  @override
  String? get imageUrl;
  @override
  InboxMessageType get type;
  @override
  String get categoryId;
  @override
  bool get archived;
  @override
  DateTime? get dateRead;
  @override
  String? get textPreview;
  @override
  String? get externalMessageId;
  @override
  DateTime get dataReceived;
  @override
  @JsonKey(ignore: true)
  _$$_InboxMessageCopyWith<_$_InboxMessage> get copyWith =>
      throw _privateConstructorUsedError;
}
