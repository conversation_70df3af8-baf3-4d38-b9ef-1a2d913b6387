// GENERATED CODE - DO NOT MODIFY BY HAND

import 'package:dynamic_forms/dynamic_forms.dart';
import '../components.dart';

class FormHeaderTitleParser<TFormHeaderTitle extends FormHeaderTitle>
    extends FormElementParser<TFormHeaderTitle> {
  @override
  String get name => 'formHeaderTitle';

  @override
  FormElement getInstance() => FormHeaderTitle();

  @override
  void fillProperties(
    TFormHeaderTitle formHeaderTitle, 
    ParserNode parserNode, 
    Element? parent,
    ElementParserFunction parser,
  ) {
    super.fillProperties(formHeaderTitle, parserNode, parent, parser);
    formHeaderTitle
      ..authorProperty = parserNode.getStringProperty(
        'author',
        defaultValue: () => 'false',
        isImmutable: true,
      )
      ..dateProperty = parserNode.getDateTimeProperty(
        'date',
        defaultValue: ParserNode.defaultDateTime,
        isImmutable: true,
        format: null,
      )
      ..titleProperty = parserNode.getStringProperty(
        'title',
        defaultValue: () => 'false',
        isImmutable: true,
      );
  }
}
