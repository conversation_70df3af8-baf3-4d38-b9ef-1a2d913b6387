// GENERATED CODE - DO NOT MODIFY BY HAND

import 'package:dynamic_forms/dynamic_forms.dart';
import '../components.dart';

class RedirectToBrowserButtonParser<TRedirectToBrowserButton extends RedirectToBrowserButton>
    extends FormElementParser<TRedirectToBrowserButton> {
  @override
  String get name => 'redirectToBrowserButton';

  @override
  FormElement getInstance() => RedirectToBrowserButton();

  @override
  void fillProperties(
    TRedirectToBrowserButton redirectToBrowserButton, 
    ParserNode parserNode, 
    Element? parent,
    ElementParserFunction parser,
  ) {
    super.fillProperties(redirectToBrowserButton, parserNode, parent, parser);
    redirectToBrowserButton
      ..labelProperty = parserNode.getStringProperty(
        'label',
        defaultValue: () => 'false',
        isImmutable: true,
      )
      ..urlProperty = parserNode.getStringProperty(
        'url',
        defaultValue: () => 'false',
        isImmutable: true,
      )
      ..valueProperty = parserNode.getBoolProperty(
        'value',        
        defaultValue: ParserNode.defaultFalse,
        isImmutable: false,
      );
  }
}
