import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'form_image.g.dart';

class FormImageRenderer extends FormElementRenderer<FormImage> {
  @override
  Widget render(
    FormImage element,
    BuildContext context,
    FormElementEventDispatcherFunction dispatcher,
    FormElementRendererFunction renderer,
  ) {
    return element.url.isNotEmpty
        ? Stack(
            children: [
              LimitedBox(
                maxWidth: MediaQuery.of(context).size.width,
                maxHeight: 190,
                child: Image.network(
                  element.url,
                  fit: BoxFit.fitWidth,
                  headers: imageAcceptHeader,
                ),
              ),
              Positioned(
                bottom: 10,
                left: 15,
                right: 15,
                child: KoyalText.header4(color: ColorTheme.of(context).whiteTextColor, element.title),
              ),
            ],
          )
        : const SizedBox.shrink();
  }
}
