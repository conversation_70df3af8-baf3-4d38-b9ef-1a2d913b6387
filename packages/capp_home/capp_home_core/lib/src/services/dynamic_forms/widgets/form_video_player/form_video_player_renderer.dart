import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';

import 'form_video_player.g.dart';

class FormVideoPlayerRenderer extends FormElementRenderer<FormVideoPlayer> {
  static const clickEventProperty = 'link_clicked';

  @override
  Widget render(
    FormVideoPlayer element,
    BuildContext context,
    FormElementEventDispatcherFunction dispatcher,
    FormElementRendererFunction renderer,
  ) {
    return element.url.isNotEmpty
        ? Container(
            key: const Key('__inboxVideoPlayer__'),
            padding: const EdgeInsets.only(left: 12, right: 12, bottom: 32, top: 16),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(14),
              child: GestureDetector(
                onTap: () => dispatcher(
                  ChangeValueEvent<String>(
                    elementId: element.id!,
                    value: element.url,
                    propertyName: clickEventProperty,
                  ),
                ),
                child: YoutubeVideoPlayer(autoPlay: element.autoplay, videoUrl: element.url, mute: element.mute),
              ),
            ),
          )
        : const SizedBox.shrink();
  }
}
