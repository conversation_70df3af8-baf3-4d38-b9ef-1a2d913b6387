// GENERATED CODE - DO NOT MODIFY BY HAND

import 'package:dynamic_forms/dynamic_forms.dart';
import '../components.dart';

class FormVideoPlayerParser<TFormVideoPlayer extends FormVideoPlayer>
    extends FormElementParser<TFormVideoPlayer> {
  @override
  String get name => 'formVideoPlayer';

  @override
  FormElement getInstance() => FormVideoPlayer();

  @override
  void fillProperties(
    TFormVideoPlayer formVideoPlayer, 
    ParserNode parserNode, 
    Element? parent,
    ElementParserFunction parser,
  ) {
    super.fillProperties(formVideoPlayer, parserNode, parent, parser);
    formVideoPlayer
      ..autoplayProperty = parserNode.getBoolProperty(
        'autoplay',        
        defaultValue: ParserNode.defaultFalse,
        isImmutable: true,
      )
      ..descriptionProperty = parserNode.getStringProperty(
        'description',
        defaultValue: () => 'false',
        isImmutable: true,
      )
      ..muteProperty = parserNode.getBoolProperty(
        'mute',        
        defaultValue: ParserNode.defaultFalse,
        isImmutable: true,
      )
      ..titleProperty = parserNode.getStringProperty(
        'title',
        defaultValue: () => 'false',
        isImmutable: true,
      )
      ..urlProperty = parserNode.getStringProperty(
        'url',
        defaultValue: () => 'false',
        isImmutable: true,
      );
  }
}
