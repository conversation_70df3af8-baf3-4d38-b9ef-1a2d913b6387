// GENERATED CODE - DO NOT MODIFY BY HAND

import 'package:dynamic_forms/dynamic_forms.dart';
import '../components.dart';

class FormImage extends FormElement {
  static const String titlePropertyName = 'title';
  static const String urlPropertyName = 'url';

  Property<String> get titleProperty => properties[titlePropertyName] as Property<String>;
  set titleProperty(Property<String> value) =>
      registerProperty(titlePropertyName, value);
  String get title =>
      titleProperty.value;
  Stream<String> get titleChanged => titleProperty.valueChanged;

  Property<String> get urlProperty => properties[urlPropertyName] as Property<String>;
  set urlProperty(Property<String> value) =>
      registerProperty(urlPropertyName, value);
  String get url =>
      urlProperty.value;
  Stream<String> get urlChanged => urlProperty.valueChanged;

  @override
  FormElement getInstance() {
    return FormImage();
  }
}
