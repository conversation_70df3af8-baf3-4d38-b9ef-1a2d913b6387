import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import 'form_text.g.dart';

class FormTextRenderer extends FormElementRenderer<FormText> {
  static const clickEventProperty = 'link_clicked';
  bool _isDeeplinkLaunched = false;

  @override
  Widget render(
    FormText element,
    BuildContext context,
    FormElementEventDispatcherFunction dispatcher,
    FormElementRendererFunction renderer,
  ) {
    if (element.id == 'deepLink' && context.isFlagEnabledWatch(FeatureFlag.inboxDeeplinkLaunch)) {
      final regExp = RegExp(r'\[(.*?)\]\((.*?)\)');
      final match = regExp.firstMatch(element.value);

      if (match != null) {
        final url = match.group(2)!;
        if (url != '' && !_isDeeplinkLaunched) {
          _isDeeplinkLaunched = true;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.get<IDeeplinkService>().deeplinkOrLaunch(url, context, linkTitle: match.group(1) ?? '');
          });
        }
      }
    }
    return KoyalPadding.normalAll(
      child: Markdown(
        data: element.value,
        onTapLink: (text, href, title) {
          dispatcher(
            ChangeValueEvent<String>(
              elementId: element.id!,
              value: href ?? '',
              propertyName: clickEventProperty,
            ),
          );
          context.get<IDeeplinkService>().deeplinkOrLaunch(href ?? '', context, linkTitle: title);
        },
      ),
    );
  }
}
