import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_home_core.dart';

class FormAuthorRenderer extends FormElementRenderer<FormAuthor> {
  @override
  Widget render(
    FormAuthor element,
    BuildContext context,
    FormElementEventDispatcherFunction dispatcher,
    FormElementRendererFunction renderer,
  ) {
    // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
    //ignore: no-direct-padding
    return Padding(
      padding: const EdgeInsets.all(5),
      child: Row(
        children: [
          const SizedBox(width: 8),
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image:
                  DecorationImage(fit: BoxFit.fill, image: NetworkImage(element.avatarUrl, headers: imageAcceptHeader)),
            ),
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              KoyalText.header5(element.name, color: ColorTheme.of(context).defaultTextColor),
              KoyalText.subtitle2(
                // ignore: deprecated_member_use_from_same_package
                L10nCappHome.of(context).tr(element.titleAssetId),
                color: ColorTheme.of(context).secondaryTextColor,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
