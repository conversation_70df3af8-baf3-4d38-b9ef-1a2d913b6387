import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';
import 'package:koyal_core/koyal_core.dart';

import 'redirect_to_browser_button.g.dart';

class RedirectToBrowserButtonRenderer extends FormElementRenderer<RedirectToBrowserButton> {
  @override
  Widget render(
    RedirectToBrowserButton element,
    BuildContext context,
    FormElementEventDispatcherFunction dispatcher,
    FormElementRendererFunction renderer,
  ) {
    // TODO(XX): Please check if we can wrap PrimaryButton by VerticalButtonsLayout/HorizontalButtonsLayout here
    //ignore: buttons-layout
    return SecondaryButton(
      text: element.label,
      onPressed: () => {
        dispatcher(ChangeValueEvent(value: true, elementId: element.id!)),
        dispatcher(ChangeValueEvent(value: false, elementId: element.id!)),
        context.navigator.pushFromPackage(
          package: 'CappConfig',
          screen: 'WebViewScreen',
          arguments: WebViewArguments(
            url: element.url,
            title: 'Web View',
          ),
        ),
      },
    );
  }
}
