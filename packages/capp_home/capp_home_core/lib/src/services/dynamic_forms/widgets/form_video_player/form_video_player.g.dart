// GENERATED CODE - DO NOT MODIFY BY HAND

import 'package:dynamic_forms/dynamic_forms.dart';
import '../components.dart';

class FormVideoPlayer extends FormElement {
  static const String autoplayPropertyName = 'autoplay';
  static const String descriptionPropertyName = 'description';
  static const String mutePropertyName = 'mute';
  static const String titlePropertyName = 'title';
  static const String urlPropertyName = 'url';

  Property<bool> get autoplayProperty => properties[autoplayPropertyName] as Property<bool>;
  set autoplayProperty(Property<bool> value) =>
      registerProperty(autoplayPropertyName, value);
  bool get autoplay =>
      autoplayProperty.value;
  Stream<bool> get autoplayChanged => autoplayProperty.valueChanged;

  Property<String> get descriptionProperty => properties[descriptionPropertyName] as Property<String>;
  set descriptionProperty(Property<String> value) =>
      registerProperty(descriptionPropertyName, value);
  String get description =>
      descriptionProperty.value;
  Stream<String> get descriptionChanged => descriptionProperty.valueChanged;

  Property<bool> get muteProperty => properties[mutePropertyName] as Property<bool>;
  set muteProperty(Property<bool> value) =>
      registerProperty(mutePropertyName, value);
  bool get mute =>
      muteProperty.value;
  Stream<bool> get muteChanged => muteProperty.valueChanged;

  Property<String> get titleProperty => properties[titlePropertyName] as Property<String>;
  set titleProperty(Property<String> value) =>
      registerProperty(titlePropertyName, value);
  String get title =>
      titleProperty.value;
  Stream<String> get titleChanged => titleProperty.valueChanged;

  Property<String> get urlProperty => properties[urlPropertyName] as Property<String>;
  set urlProperty(Property<String> value) =>
      registerProperty(urlPropertyName, value);
  String get url =>
      urlProperty.value;
  Stream<String> get urlChanged => urlProperty.valueChanged;

  @override
  FormElement getInstance() {
    return FormVideoPlayer();
  }
}
