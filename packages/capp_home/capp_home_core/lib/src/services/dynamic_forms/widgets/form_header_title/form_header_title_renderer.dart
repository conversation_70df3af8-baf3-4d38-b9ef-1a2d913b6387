import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dynamic_forms/flutter_dynamic_forms.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'form_header_title.g.dart';

class FormHeaderTitleRenderer extends FormElementRenderer<FormHeaderTitle> {
  @override
  Widget render(
    FormHeaderTitle element,
    BuildContext context,
    FormElementEventDispatcherFunction dispatcher,
    FormElementRendererFunction renderer,
  ) {
    return element.title.isNotEmpty
        ? KoyalPadding.normalAll(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 12),
                KoyalText.header5(color: ColorTheme.of(context).defaultTextColor, element.title),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    KoyalText.body1(element.date.mediumDate(), color: ColorTheme.of(context).secondaryTextColor),
                    KoyalPadding.xSmallHorizontal(
                      child: KoyalText.body1('\u00b7', color: ColorTheme.of(context).defaultTextColor),
                    ),
                    KoyalText.subtitle2(element.author, color: ColorTheme.of(context).defaultTextColor),
                  ],
                ),
              ],
            ),
          )
        : const SizedBox.shrink();
  }
}
