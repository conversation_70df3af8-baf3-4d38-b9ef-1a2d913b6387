import 'package:capp_api/capp_api.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as api_models;
import 'package:selfcareapi/selfcareapi.dart';

import '../../domain/games_banner/i_games_banner_repository.dart';
import '../../domain/models/games_banner_failure.dart';

class GamesBannerRepository implements IGamesBannerRepository {
  final BannerApi? bannerApi;
  final Logger logger;

  GamesBannerRepository({required this.bannerApi, required this.logger});

  @override
  Future<Either<GamesBannerFailure, ContentBanner>> getGamesBanner() =>
      _getBanner(api_models.BannerCtaType.homepageGame);

  @override
  Future<Either<GamesBannerFailure, ContentBanner>> getPosLoanBanner() =>
      _getBanner(api_models.BannerCtaType.homepagePosLoan);

  @override
  Future<Either<GamesBannerFailure, ContentBanner>> getLoanSectionBanner() =>
      _getBanner(api_models.BannerCtaType.loanSection);

  Future<Either<GamesBannerFailure, ContentBanner>> _getBanner(api_models.BannerCtaType ctaType) async {
    try {
      final result = await bannerApi!.contentActiveBannersGet(ctaType: ctaType);
      final items = result.items ?? [];
      if (items.isNotEmpty) {
        final banners = items.map((b) => b.toDomain()).toList();
        return right(banners.first);
      } else {
        return left(const GamesBannerFailure.unexpected(message: 'No banner found'));
      }
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in banners repository', e, s);
      return left(const GamesBannerFailure.unexpected(message: 'Unexpected error'));
    }
  }
}
