import 'package:gma_storage/gma_storage.dart';

class UserFeedbackJourneyStatus extends StorageItemBase {
  String? journeyId;
  DateTime? postponedWhen;
  int? attempt;
  bool? isComplete;

  UserFeedbackJourneyStatus({this.journeyId, this.attempt, this.postponedWhen, this.isComplete});

  @override
  StorageItemBase fromMap(JsonMap map) {
    return UserFeedbackJourneyStatus(
      isComplete: map['isComplete'] as bool?,
      attempt: map['attempt'] as int?,
      journeyId: map['journeyId'] as String?,
      postponedWhen: map['postponedWhen'] == null ? null : DateTime.parse(map['postponedWhen'] as String),
    );
  }

  @override
  JsonMap toMap() => <String, dynamic>{
        'isComplete': isComplete,
        'attempt': attempt,
        'journeyId': journeyId,
        'postponedWhen': postponedWhen?.toIso8601String(),
      };
}
