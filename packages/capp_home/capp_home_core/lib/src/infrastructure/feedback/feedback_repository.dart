import 'package:capp_api/capp_api.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../../capp_home_core.dart';

class FeedbackRepository implements IFeedbackRepository {
  final Logger logger;
  final AppFeedbackApi appFeedbackApi;
  final UserJourneyFeedbackApi userJourneyFeedbackApi;

  FeedbackRepository({required this.logger, required this.appFeedbackApi, required this.userJourneyFeedbackApi});

  @override
  Future<Either<FeedbackFailure, Unit>> sendPositiveFeedback({
    required List<String> feedbackCategoryIds,
    String? text,
  }) async {
    try {
      final request = PositiveFeedbackRequest(feedbackCategoryIds: feedbackCategoryIds, text: text);
      await appFeedbackApi.v2AppFeedbacksPositivePost(positiveFeedbackRequest: request);
      return right(unit);
    } on DioError catch (e, s) {
      logger.e('Dio Error in feedback repository', e, s);
      return left(const FeedbackFailure.unexpected());
    }
  }

  @override
  Future<Either<FeedbackFailure, Unit>> sendNegativeFeedback({
    required List<String> feedbackCategoryIds,
    String? text,
  }) async {
    try {
      final request = NegativeFeedbackRequest(feedbackCategoryIds: feedbackCategoryIds, text: text);
      await appFeedbackApi.appFeedbacksNegativePost(negativeFeedbackRequest: request);
      return right(unit);
    } on DioError catch (e, s) {
      logger.e('Dio Error in feedback repository', e, s);
      return left(const FeedbackFailure.unexpected());
    }
  }

  @override
  Future<Either<FeedbackFailure, bool>> feedbackExist(String journeyId) async {
    try {
      final result = await userJourneyFeedbackApi.userJourneyFeedbacksFeedbackExistsGet(journeyId);
      return right(result);
    } on DioError catch (e, s) {
      logger.e('Dio Error in feedback repository', e, s);
      return left(const FeedbackFailure.unexpected());
    }
  }

  @override
  Future<Either<FeedbackFailure, Unit>> sendFeedback({
    required String journeyId,
    String? comment,
    required int rating,
    List<String>? categoryIds,
  }) async {
    try {
      final request = SubmitUserJourneyFeedbackRequest(
        userJourneyId: journeyId,
        text: comment,
        rating: rating,
        categoryIds: categoryIds,
      );
      await userJourneyFeedbackApi.userJourneyFeedbacksPost(submitUserJourneyFeedbackRequest: request);
      return right(unit);
    } on DioError catch (e, s) {
      logger.e('Dio Error in feedback repository', e, s);
      return left(const FeedbackFailure.unexpected());
    }
  }

  @override
  Future<Either<FeedbackFailure, List<FeedbackCategory>>> getFeedbackCategories({
    required String journeyId,
  }) async {
    try {
      final result = await userJourneyFeedbackApi.userJourneysJourneyIdLocalizedFeedbackCategoriesGet(journeyId, '');
      final resultList = result.items?.map((item) => item.toDomain()).toList() ?? [];
      return right(resultList);
    } on DioError catch (e, s) {
      logger.e('Dio Error in feedback repository', e, s);
      return left(const FeedbackFailure.unexpected());
    }
  }

  @override
  Future<Either<FeedbackFailure, int>> getAppFeedbackRatingCount({required int daysPeriod}) async {
    try {
      final result = await appFeedbackApi.appFeedbacksRatesCountGet(daysPeriod: daysPeriod);
      return right(result);
    } on DioError catch (e, s) {
      logger.e('Dio Error in feedback repository', e, s);
      return left(const FeedbackFailure.unexpected());
    }
  }
}
