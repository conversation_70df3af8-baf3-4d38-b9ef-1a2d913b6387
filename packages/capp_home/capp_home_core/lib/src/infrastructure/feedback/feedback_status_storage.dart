import 'package:gma_storage/gma_storage.dart';

import '../../../capp_home_core.dart';

class FeedbackStatusStorage {
  final GmaStorageProvider storage;
  FeedbackStatusStorage({required this.storage});

  String _statusKey(String userId, String journeyId) => 'user:$userId;journey:$journeyId';
  String _userJourneyIdsKey(String userId) => 'userJourneyIds:$userId';

  Future<UserFeedbackJourneyStatus?> getStatus({required String userId, required String journeyId}) {
    return storage.get<UserFeedbackJourneyStatus?>(
      _statusKey(userId, journeyId),
      fromMap: UserFeedbackJourneyStatus().fromMap,
    );
  }

  Future resetStatus({required String userId, required String journeyId}) {
    return storage.delete(_statusKey(userId, journeyId));
  }

  Future setComplete({required String userId, required String journeyId}) async {
    var status = await getStatus(userId: userId, journeyId: journeyId);
    if (status == null) {
      status = UserFeedbackJourneyStatus(journeyId: journeyId, isComplete: true);
    } else {
      status.isComplete = true;
    }
    return _updateUserJourney(status, userId);
  }

  Future postpone({
    required String userId,
    required String journeyId,
    required DateTime postponedWhen,
  }) async {
    var status = await getStatus(userId: userId, journeyId: journeyId);
    if (status == null) {
      status = UserFeedbackJourneyStatus(
        journeyId: journeyId,
        postponedWhen: postponedWhen,
        attempt: 1,
        isComplete: false,
      );
    } else {
      status
        ..attempt = status.attempt! + 1
        ..postponedWhen = postponedWhen;
    }
    return _updateUserJourney(status, userId);
  }

  Future<List<String>?> getUserJourneys({required String userId}) async {
    final result = await storage.get<UserJourneyIds?>(_userJourneyIdsKey(userId), fromMap: UserJourneyIds().fromMap);
    return result?.journeyIds;
  }

  Future _updateUserJourney(UserFeedbackJourneyStatus status, String userId) async {
    final userJourneys = await getUserJourneys(userId: userId) ?? [];
    if (!userJourneys.contains(status.journeyId)) {
      await storage.insert(
        _userJourneyIdsKey(userId),
        UserJourneyIds(journeyIds: userJourneys..add(status.journeyId!)),
      );
    }
    return storage.insert(_statusKey(userId, status.journeyId!), status);
  }
}

class UserJourneyIds extends StorageItemBase {
  List<String>? journeyIds;
  UserJourneyIds({this.journeyIds});

  @override
  StorageItemBase fromMap(JsonMap map) {
    final list = map['journeyIds'] as List<dynamic>;
    return UserJourneyIds(journeyIds: list.map((dynamic x) => x.toString()).toList());
  }

  @override
  JsonMap toMap() => <String, dynamic>{'journeyIds': journeyIds};
}
