import 'user_feedback_journey_status.dart';

abstract class IFeedbackJourneyService {
  Future<UserFeedbackJourneyStatus?> getJourneyStatus(String journeyId);
  Future resetStatus(String journeyId);
  Future<UserFeedbackJourneyStatus?> getJourneyToComplete({String? userId});
  Future<bool> isEligibleToDisplay(String journeyId);
  Future setComplete(String journeyId);
  Future postponeJourney({required String journeyId});
  Future<bool> canRateApp({int daysPeriod, int maxRatingCount});
}
