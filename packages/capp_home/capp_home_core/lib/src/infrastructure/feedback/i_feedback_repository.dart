import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';

import '../../../capp_home_core.dart';

abstract class IFeedbackRepository {
  Future<Either<FeedbackFailure, Unit>> sendPositiveFeedback({required List<String> feedbackCategoryIds, String? text});
  Future<Either<FeedbackFailure, Unit>> sendNegativeFeedback({required List<String> feedbackCategoryIds, String? text});
  Future<Either<FeedbackFailure, bool>> feedbackExist(String journeyId);
  Future<Either<FeedbackFailure, Unit>> sendFeedback({
    required String journeyId,
    String? comment,
    required int rating,
    List<String>? categoryIds,
  });
  Future<Either<FeedbackFailure, List<FeedbackCategory>>> getFeedbackCategories({required String journeyId});
  Future<Either<FeedbackFailure, int>> getAppFeedbackRatingCount({required int daysPeriod});
}
