import 'package:koyal_auth/koyal_auth.dart';

import '../../../capp_home_core.dart';

class FeedbackJourneyService implements IFeedbackJourneyService {
  final ICurrentUserRepository currentUserRepository;
  final FeedbackStatusStorage feedbackStatusStorage;
  final IFeedbackRepository feedbackRepository;

  const FeedbackJourneyService({
    required this.currentUserRepository,
    required this.feedbackStatusStorage,
    required this.feedbackRepository,
  });

  @override
  Future setComplete(String journeyId) async {
    final userId = await currentUserRepository.userId() ?? '';
    return feedbackStatusStorage.setComplete(userId: userId, journeyId: journeyId);
  }

  @override
  Future postponeJourney({required String journeyId}) async {
    final userId = await currentUserRepository.userId() ?? '';
    return feedbackStatusStorage.postpone(
      userId: userId,
      journeyId: journeyId,
      postponedWhen: DateTime.now(),
    );
  }

  @override
  Future<UserFeedbackJourneyStatus?> getJourneyToComplete({String? userId}) async {
    userId = userId ?? await currentUserRepository.userId();
    if (userId == null) return null;

    final journeys = await feedbackStatusStorage.getUserJourneys(userId: userId) ?? [];
    for (final journeyId in journeys) {
      final status = await _validForRetry(journeyId, userId);
      if (status != null) {
        if (!await _feedbackExist(journeyId)) {
          return status;
        }
      }
    }
    return null;
  }

  @override
  Future resetStatus(String journeyId) async {
    final userId = await currentUserRepository.userId() ?? '';
    return feedbackStatusStorage.resetStatus(userId: userId, journeyId: journeyId);
  }

  @override
  Future<bool> isEligibleToDisplay(String journeyId) async {
    if (await _feedbackExist(journeyId)) return false;
    final userId = await currentUserRepository.userId() ?? '';
    final status = await feedbackStatusStorage.getStatus(userId: userId, journeyId: journeyId);
    // feedback not postponed OR ready for retry
    return status == null || await _validForRetry(journeyId, userId) != null;
  }

  @override
  Future<UserFeedbackJourneyStatus?> getJourneyStatus(String journeyId) async {
    final userId = await currentUserRepository.userId() ?? '';
    return feedbackStatusStorage.getStatus(userId: userId, journeyId: journeyId);
  }

  Future<UserFeedbackJourneyStatus?> _validForRetry(String journeyId, String userId) async {
    var shouldRetry = false;
    final status = await feedbackStatusStorage.getStatus(userId: userId, journeyId: journeyId);
    if (status != null && status.isComplete != true) {
      if (status.attempt == 1) {
        final dueDate = status.postponedWhen!.add(const Duration(hours: 1));
        shouldRetry = DateTime.now().isAfter(dueDate);
      } else if (status.attempt == 2) {
        final dueDate = status.postponedWhen!.add(const Duration(hours: 24));
        shouldRetry = DateTime.now().isAfter(dueDate);
      }
    }
    return shouldRetry ? status : null;
  }

  Future<bool> _feedbackExist(String journeyId) async {
    final response = await feedbackRepository.feedbackExist(journeyId);
    // in case of error we can't say whether feedback exists or not, then let's assume it does exist,
    // so the user will not be bothered multiple times by the feedback request
    return response.fold((l) => /* ! */ true, (r) => r);
  }

  @override
  Future<bool> canRateApp({int daysPeriod = 365, int maxRatingCount = 3}) async {
    final response = await feedbackRepository.getAppFeedbackRatingCount(daysPeriod: daysPeriod);
    // in case of error we can't say whether the app rating limit is reached or not, then let's assume it is,
    // so app will not be permitted to launch app rating dialog and redirects user to the store instead.
    return response.fold((l) => false, (r) => r < maxRatingCount);
  }
}
