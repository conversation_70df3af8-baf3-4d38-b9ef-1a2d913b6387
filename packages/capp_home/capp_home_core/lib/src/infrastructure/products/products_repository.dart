import 'package:capp_api/capp_api.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as models;
import 'package:selfcareapi/selfcareapi.dart';

import '../../domain/products/i_products_repository.dart';

class ProductsRepository implements IProductsRepository {
  final ProductWidgetsApi productWidgetsApi;
  final Logger logger;

  ProductsRepository({
    required this.productWidgetsApi,
    required this.logger,
  });

  @override
  Future<Either<Unit, List<ProductsItem>>> getProducts({
    required SignOnStatus userStatus,
    required String language,
  }) async {
    try {
      final response = await productWidgetsApi.productWidgetsGet(userStatus.toApi, language);
      final result = <ProductsItem>[];
      if (response.items != null) {
        for (final item in response.items!) {
          result.add(item.toDomain());
        }
      }
      return right(result);
    } on DioError catch (e) {
      logger.wtf('Unexpected error in products and services repository', e);
      return left(unit);
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in products and services repository', e, s);
      return left(unit);
    }
  }
}

extension on SignOnStatus {
  models.UserStatusEnum get toApi {
    switch (this) {
      case SignOnStatus.anonymous:
        return models.UserStatusEnum.guest;
      case SignOnStatus.client:
        return models.UserStatusEnum.client;
      case SignOnStatus.registered:
        return models.UserStatusEnum.registered;
    }
  }
}
