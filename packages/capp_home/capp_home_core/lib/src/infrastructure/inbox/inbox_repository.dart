import 'package:capp_api/capp_api.dart' as capp_api;
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';
import 'package:string_unescape/string_unescape.dart';

import '../../../capp_home_core.dart';
import '../../domain/inbox/models/unread_count.dart';

class InboxRepository implements IInboxRepository {
  Function? logoutCallback;
  final UserInboxApi api;
  final Logger logger;
  final IFeatureFlagRepository featureFlagRepository;
  ICurrentUserRepository currentUserRepository;
  final List<InboxMessage> _inboxMessagesLocal = [];
  final Map<String, Map<int, InboxMessage>> _deletedInboxMessageLocal = {};

  static const String _unexpectedInboxError = 'Unexpected error in inbox repository';

  InboxRepository({
    required this.api,
    required this.logger,
    required this.featureFlagRepository,
    required this.currentUserRepository,
  });

  @override
  Future<Either<InboxFailure, String>> getInboxMessage(String id) async {
    try {
      final response = await api.v3UserInboxFormIdGet(
        id,
        options: _createDefaultOptions(),
      );
      final unescapedXml = unescape(response);
      return right(unescapedXml);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, Unit>> markAsArchived(String id) async {
    try {
      await api.v3UserInboxArchiveIdPost(
        id,
        options: _createDefaultOptions(),
      );
      _markAsArchivedLocal(id);
      return right(unit);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, Unit>> markAsUnarchived(String id) async {
    try {
      await api.v3UserInboxUnarchiveIdPost(
        id,
        options: _createDefaultOptions(),
      );
      _markAsUnArchivedLocal(id);
      return right(unit);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, Unit>> markAsRead(String id) async {
    try {
      await api.v3UserInboxReadIdPost(
        id,
        options: _createDefaultOptions(),
      );
      _markAsReadLocal(id);
      return right(unit);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, Unit>> markAsUnread(String id) async {
    try {
      await api.v3UserInboxUnreadIdPost(
        id,
        options: _createDefaultOptions(),
      );
      _markAsUnreadLocal(id);
      return right(unit);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, int>> markAllRead() async {
    try {
      final result = await api.v3UserInboxMarkAllReadPost(
        options: _createDefaultOptions(),
      );
      _markAllReadLocal();
      return right(result);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, bool>> hasUnreadMessages({String? categoryId}) async {
    final currentUser = await currentUserRepository.getCurrentUser();
    try {
      final response = await api.v3UserInboxHasUnreadMessagesGet(
        category: categoryId,
        segment: capp_api.InboxMessageSegmentX.toApi(_toMessageSegment(currentUser?.signOnState)),
        options: _createDefaultOptions(),
      );

      return right(response);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, List<UnreadCount>>> getUnreadCount({List<String>? categories}) async {
    final currentUser = await currentUserRepository.getCurrentUser();
    try {
      final listCounter = <UnreadCount>[];
      if (categories == null || categories.isEmpty) {
        // use api v3 get [unreadCount] for app badge
        final response = await api.v3UserInboxUnreadCountGet(
          segment: capp_api.InboxMessageSegmentX.toApi(_toMessageSegment(currentUser?.signOnState)),
          options: _createDefaultOptions(),
        );
        listCounter.add(UnreadCount(count: response.count ?? 0, categoryId: response.categoryId));
      } else {
        // use api v4 get [unreadCount] for inbox screen
        final response = await api.v4UserInboxUnreadCountGet(
          categories,
          segment: capp_api.InboxMessageSegmentX.toApi(_toMessageSegment(currentUser?.signOnState)),
          options: _createDefaultOptions(),
        );
        response.unreadMessageCounts?.forEach((r) {
          listCounter.add(UnreadCount(count: r.count ?? 0, categoryId: r.categoryId));
        });
      }
      return right(listCounter);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  @override
  Future<Either<InboxFailure, InboxMessageList>> getInboxList({
    int? page,
    int? pageSize,
    String? categoryId,
  }) async {
    final currentUser = await currentUserRepository.getCurrentUser();

    try {
      final response = await api.v3UserInboxForUserGet(
        page: page,
        pageSize: pageSize,
        category: categoryId == allCategoryId ? null : categoryId,
        segment: capp_api.InboxMessageSegmentX.toApi(_toMessageSegment(currentUser?.signOnState)),
        options: _createDefaultOptions(),
      );
      final domain = response.toDomain();
      _saveListMessagesToLocal(domain.items);
      return right(domain);
    } on DioError catch (_) {
      return left(const InboxFailure.unexpected());
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedInboxError, e, s);
      return left(const InboxFailure.unexpected());
    }
  }

  Options? _createDefaultOptions() {
    return featureFlagRepository.isEnabledCached(FeatureFlag.inboxIsTest)
        ? Options(
            headers: <String, bool>{
              'X-Test': true,
            },
          )
        : null;
  }

  @override
  List<InboxMessage> getInboxMessagesLocal() {
    // Create copy to not share object reference
    // It may cause issue when the list is modified and outside world do not know that
    return List<InboxMessage>.of(_inboxMessagesLocal.map((e) => e.copyWith()));
  }

  void _markAsArchivedLocal(String msgId) {
    final index = _inboxMessagesLocal.indexWhere((m) => m.id == msgId);
    if (index != -1) {
      _deletedInboxMessageLocal[msgId] = {index: _inboxMessagesLocal[index]};
      _inboxMessagesLocal.removeAt(index);
    }
  }

  void _markAsUnArchivedLocal(String msgId) {
    final revertItemMap = _deletedInboxMessageLocal[msgId];
    if (revertItemMap != null) {
      final index = revertItemMap.keys.first;
      _inboxMessagesLocal.insert(index, revertItemMap[index]!);
    }
  }

  void _markAsReadLocal(String msgId) {
    final index = _inboxMessagesLocal.indexWhere((m) => m.id == msgId);
    if (index != -1) {
      final updated = _inboxMessagesLocal[index].copyWith(dateRead: DateTime.now());
      _inboxMessagesLocal[index] = updated;
    }
  }

  void _markAsUnreadLocal(String msgId) {
    final index = _inboxMessagesLocal.indexWhere((m) => m.id == msgId);
    if (index != -1) {
      final updated = _inboxMessagesLocal[index].copyWith(dateRead: null);
      _inboxMessagesLocal[index] = updated;
    }
  }

  void _markAllReadLocal() {
    for (final message in _inboxMessagesLocal) {
      markAsRead(message.id);
    }
  }

  void _saveListMessagesToLocal(List<InboxMessage> list) {
    for (final item in list) {
      final index = _inboxMessagesLocal.indexWhere((message) => message.id == item.id);
      final updatedPreviewItem = item.copyWith(textPreview: item.text?.removeMarkdown());
      if (index != -1) {
        _inboxMessagesLocal[index] = updatedPreviewItem;
      } else {
        _inboxMessagesLocal.add(updatedPreviewItem);
      }
    }
  }

  @override
  void clearInboxMessagesLocal() {
    _inboxMessagesLocal.clear();
    _deletedInboxMessageLocal.clear();
  }
}

MessageSegment _toMessageSegment(SignOnStatus? state) {
  switch (state) {
    case SignOnStatus.anonymous:
      return MessageSegment.guest;
    case SignOnStatus.client:
      return MessageSegment.customer;
    case SignOnStatus.registered:
      return MessageSegment.prospect;
    default:
      return MessageSegment.guest;
  }
}
