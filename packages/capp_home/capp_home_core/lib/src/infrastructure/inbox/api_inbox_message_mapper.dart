import 'package:selfcareapi/model/models.dart' as capp_api;

import '../../../capp_home_core.dart';

extension ApiInboxMessageMapper on capp_api.UserInboxItem {
  InboxMessage toDomain() => InboxMessage(
        id: id!,
        title: title ?? '',
        text: text,
        imageUrl: imageUrl,
        categoryId: categoryId ?? '',
        archived: archived!,
        dateRead: dateRead,
        dataReceived: dateReceived!,
        externalMessageId: externalMessageId,
      );
}
