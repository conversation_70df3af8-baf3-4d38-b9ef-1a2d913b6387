extension ShoppingMallExtension on String {
  String enrichShoppingMallUrl(
    String? userCuid,
    String? userGuid,
  ) {
    if (contains('shoppingmall.ph')) {
      var enrichedUrl = this;

      enrichedUrl += contains('?') ? '&' : '?';
      enrichedUrl += 'id_cuid=${userCuid ?? '0'}';
      enrichedUrl += '&';
      enrichedUrl += 'device_id=${userGuid ?? '0'}';
      return enrichedUrl;
    }
    return this;
  }
}
