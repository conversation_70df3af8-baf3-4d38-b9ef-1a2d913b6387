import 'package:capp_api/capp_api.dart' as api;
import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' hide ContractStatus;
import 'package:selfcareapi/selfcareapi.dart';

class ActionBeltRepository implements IActionBeltRepository {
  final IContractRepository contractRepository;
  final ActionBeltWidgetsApi actionBeltApi;
  final Logger logger;

  const ActionBeltRepository({
    required this.contractRepository,
    required this.actionBeltApi,
    required this.logger,
  });

  @override
  Future<Either<ActionBeltFailure, UserSegment>> getUserSegment() async {
    final response = await contractRepository.getContractsOverviewDetails(refresh: false);

    final segment = response.fold<UserSegment?>((l) => null, (r) {
      if ((r.revolvingContracts ?? []).isNotEmpty && (r.loanContracts ?? []).isNotEmpty) {
        return UserSegment.relCel;
      } else if ((r.revolvingContracts ?? []).isNotEmpty) {
        return UserSegment.rel;
      } else if ((r.loanContracts ?? []).isNotEmpty) {
        return UserSegment.cel;
      } else {
        return UserSegment.none;
      }
    });

    if (segment == null) {
      return left(const ActionBeltFailure.unexpected());
    }
    return right(segment);
  }

  @override
  Future<Either<ActionBeltFailure, List<String>>> getWidgets({
    required SignOnStatus userStatus,
    required bool onlyEnabled,
  }) async {
    try {
      final response = await actionBeltApi.actionBeltWidgetsGet(userStatus.toApi, onlyEnabled: onlyEnabled);
      return right(response.widgetIds ?? const []);
    } on DioError catch (e, s) {
      logger.e('Dio Error in blog section repository', e, s);
      return left(const ActionBeltFailure.unexpected());
    }
  }

  @override
  Future<Either<ActionBeltFailure, List<UserActionBeltWidget>>> getActionBeltItems({
    required SignOnStatus userStatus,
    required String language,
    required bool onlyEnabled,
    required bool useSegmentation,
    required UserSegment userSegment,
  }) async {
    try {
      final segment = !useSegmentation
          ? userStatus.toApi
          : userSegment != UserSegment.none
              ? userSegment.toApi
              : userStatus.toApi;

      final response = await actionBeltApi.v2ActionBeltWidgetsGet(
        segment,
        language,
        onlyEnabled: onlyEnabled,
      );
      return right(response.toDomainNew());
    } on DioError catch (e, s) {
      logger.e('Dio Error in blog section repository', e, s);
      return left(const ActionBeltFailure.unexpected());
    }
  }
}

extension on UserSegment {
  UserStatusDto get toApi {
    switch (this) {
      case UserSegment.rel:
        return UserStatusDto.rel;
      case UserSegment.cel:
        return UserStatusDto.cel;
      case UserSegment.relCel:
        return UserStatusDto.celPlusRel;
      case UserSegment.none:
        return UserStatusDto.celPlusRel;
    }
  }
}

extension on SignOnStatus {
  UserStatusDto get toApi {
    switch (this) {
      case SignOnStatus.anonymous:
        return UserStatusDto.guest;
      case SignOnStatus.client:
        return UserStatusDto.client;
      case SignOnStatus.registered:
        return UserStatusDto.registered;
    }
  }
}
