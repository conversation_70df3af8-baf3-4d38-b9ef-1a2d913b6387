import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_home_core.dart';
import '../../application/games_launcher/games_launcher_ff_handler.dart';
import '../../application/games_launcher/games_launcher_strategy.dart';
import 'game_authorized_strategy.dart';

final class GamesLauncherService extends BaseGamesLauncherService {
  final IGamesAuthRepository gamesAuthRepository;
  final ICurrentUserRepository currentUserRepository;
  final Logger logger;

  GamesLauncherService({
    required this.gamesAuthRepository,
    required this.currentUserRepository,
    required this.logger,
  });

  @override
  Future<void> checkAuthorizationNeeded(
    BuildContext context,
    String url, {
    GamesLauncherFfHandler ff = const GamesLauncherFfHandler.empty(),
  }) async {
    strategy = GameNotVerifiedStrategy();
    // is authorization allowed by FF?
    final ffHandler = ff.isEmpty ? GamesLauncherFfHandler.flagSmith(context) : ff;
    final isAuthorizationAllowed = ffHandler.isAuthAllowed();

    if (!isAuthorizationAllowed) {
      return;
    }

    // check user
    final isAnonymous = (await currentUserRepository.getCurrentUser())?.isAnonymous ?? true;

    if (isAnonymous) {
      strategy = GameEmptyStrategy();
      if (context.mounted) {
        _showNotLoggedInDialog(context);
      }
      return;
    }

    // Ready to go with authorized link
    strategy = GameAuthorizedStrategy(
      gamesAuthRepository: gamesAuthRepository,
      logger: logger,
    );
  }

  void _showNotLoggedInDialog(BuildContext context) {
    showKoyalBottomSheet<void>(
      context: context,
      builder: (_) => const LoginPromptBottomSheetContent(),
    );
  }

  static Future<void> _launchGameWithLogin(BuildContext context, GamesViewType viewType) =>
      context.get<BaseGamesLauncherService>().launchGame(context, viewType);

  static Future<void> _launchGameUrl(BuildContext context, String url) =>
      context.get<BaseGamesLauncherService>().launchUrl(context, url);

  // launch url
  static Future<void> launchGameEvent(BuildContext context) => _launchGameWithLogin(context, GamesViewType.event);
  static Future<void> launchGameUrl(BuildContext context, String url) => _launchGameUrl(context, url);
  // launch game with login
  static Future<void> launchGameHub(BuildContext context) => _launchGameWithLogin(context, GamesViewType.gameHub);
  static Future<void> launchGameMission(BuildContext context) => _launchGameWithLogin(context, GamesViewType.mission);
  static Future<void> launchGameRewardWallet(BuildContext context) =>
      _launchGameWithLogin(context, GamesViewType.rewardWallet);
}
