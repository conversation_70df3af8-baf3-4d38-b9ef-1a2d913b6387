import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_home_core.dart';

mixin GameDeeplinkResolverMixin {
  Future<void> resolveGameDeeplink(
    BuildContext context,
    String url, {
    String linkTitle = '',
    bool useCustomTabs = false,
    bool Function()? askForLoginCondition,
  }) async {
    try {
      final link = Uri.decodeFull(url);
      final linkUri = Uri.tryParse(link);

      if (linkUri == null) {
        debugPrint('GameDeeplinkResolver: Invalid URL format: $url');
        return;
      }

      // Check if context is still mounted before proceeding
      if (!context.mounted) return;

      if (askForLoginCondition?.call() ?? true) {
        if (linkUri.pathSegments.isNotEmpty) {
          final firstSegment = linkUri.pathSegments.first;
          switch (firstSegment) {
            case 'game':
              await GamesLauncherService.launchGameHub(context);
              break;
            case 'gameRewardWallet':
              await GamesLauncherService.launchGameRewardWallet(context);
              break;
            case 'gameMission':
              await GamesLauncherService.launchGameMission(context);
              break;
            case 'gameEvent':
              await GamesLauncherService.launchGameEvent(context);
              break;
            default:
              // Handle other game-related paths
              if (context.mounted) {
                context.get<IDeeplinkService>().deeplinkOrLaunch(url, context, linkTitle: linkTitle);
              }
          }
        } else if (useCustomTabs) {
          await GamesLauncherService.launchGameUrl(context, url);
        } else {
          if (context.mounted) {
            context.get<IDeeplinkService>().deeplinkOrLaunch(url, context, linkTitle: linkTitle);
          }
        }
      } else {
        if (context.mounted) {
          context.get<IDeeplinkService>().deeplinkOrLaunch(url, context, linkTitle: linkTitle);
        }
      }
    } catch (e) {
      debugPrint('GameDeeplinkResolver: Error resolving deeplink $url: $e');
      // Fallback to regular deeplink service if available
      if (context.mounted) {
        try {
          context.get<IDeeplinkService>().deeplinkOrLaunch(url, context, linkTitle: linkTitle);
        } catch (fallbackError) {
          debugPrint('GameDeeplinkResolver: Fallback also failed: $fallbackError');
        }
      }
    }
  }
}
