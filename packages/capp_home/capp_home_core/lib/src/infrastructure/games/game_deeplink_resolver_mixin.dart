import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_home_core.dart';

mixin GameDeeplinkResolverMixin {
  Future<void> resolveGameDeeplink(
    BuildContext context,
    String url, {
    String linkTitle = '',
    bool useCustomTabs = false,
    bool Function()? askForLoginCondition,
  }) async {
    final link = Uri.decodeFull(url);
    final linkUri = Uri.tryParse(link);
    if (linkUri != null) {
      if (askForLoginCondition?.call() ?? true) {
        if (linkUri.pathSegments.isNotEmpty) {
          if (linkUri.pathSegments.first == 'game') {
            await GamesLauncherService.launchGameHub(context);
          } else if (linkUri.pathSegments.first == 'gameRewardWallet') {
            await GamesLauncherService.launchGameRewardWallet(context);
          } else if (linkUri.pathSegments.first == 'gameMission') {
            await GamesLauncherService.launchGameMission(context);
          } else if (linkUri.pathSegments.first == 'gameEvent') {
            await GamesLauncherService.launchGameEvent(context);
          }
        } else if (useCustomTabs) {
          await GamesLauncherService.launchGameUrl(context, url);
        } else {
          context.get<IDeeplinkService>().deeplinkOrLaunch(url, context, linkTitle: linkTitle);
        }
      } else {
        context.get<IDeeplinkService>().deeplinkOrLaunch(url, context, linkTitle: linkTitle);
      }
    }
  }
}
