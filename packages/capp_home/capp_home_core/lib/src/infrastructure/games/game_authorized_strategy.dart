import 'package:logger/logger.dart';

import '../../../capp_home_core.dart';
import '../../application/games_launcher/games_launcher_strategy.dart';

/// Strategy where URL is send to endpoint to get proper auth token
final class GameAuthorizedStrategy implements GamesUrlVerificationStrategy {
  final IGamesAuthRepository gamesAuthRepository;
  final Logger logger;

  GameAuthorizedStrategy({
    required this.gamesAuthRepository,
    required this.logger,
  });

  @override
  Future<String> verifyUrl(String url) async {
    final urlResult = await gamesAuthRepository.getGameVerifiedUrl();
    return urlResult.fold(
      (failure) {
        logger.wtf('Unable to get valid gamification url $failure', failure);
        return url;
      },
      (verifiedUrl) {
        return verifiedUrl;
      },
    );
  }
}
