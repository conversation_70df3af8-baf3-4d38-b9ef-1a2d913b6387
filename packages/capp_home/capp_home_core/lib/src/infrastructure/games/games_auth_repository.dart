import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:identityapi/identityapi.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/woay_game_link_request.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../domain/games/i_games_auth_repository.dart';

const _grantType = 'refresh_token';
const _scope = 'gamification-api offline_access';
const _gamificationId = 'gamification-client-app';
const _gamificationSecret = 'secret-gamification';

class GamesAuthRepository implements IGamesAuthRepository {
  final TokenApi api;
  final GameWidgetsApi gamesApi;
  final ITokenManager tokenManager;
  final Logger logger;

  GamesAuthRepository({
    required this.api,
    required this.tokenManager,
    required this.gamesApi,
    required this.logger,
  });

  @override
  Future<Either<AuthFailure, String>> getGameVerifiedUrl() async {
    final tokenResponse = await _getGamesAuthToken();

    return tokenResponse.fold(
      left,
      _getGamesAuthUrl,
    );
  }

  Future<Either<AuthFailure, String>> _getGamesAuthToken() async {
    try {
      final tokens = await tokenManager.getTokens();
      final response = await api.connectTokenPost(
        client_id: _gamificationId,
        client_secret: _gamificationSecret,
        grant_type: _grantType,
        scope: _scope,
        refresh_token: tokens?.refreshToken,
      );
      final token = response.toDomain().accessToken;
      return right(token ?? '');
    } on DioError catch (e) {
      logger.wtf('Dio can not fetch games auth token', e);
    } on Exception catch (e) {
      logger.wtf('Can not fetch games auth token', e);
    }
    return left(const AuthFailure.unexpected());
  }

  Future<Either<AuthFailure, String>> _getGamesAuthUrl(final String jwt) async {
    try {
      final linkResponse = await gamesApi.gameWidgetsWoayGameLinkPost(
        woayGameLinkRequest: WoayGameLinkRequest(
          jwt: jwt,
        ),
      );

      return right(linkResponse.presignedUrl ?? '');
    } on DioError catch (e) {
      logger.wtf('Dio can not fetch verified url', e);
    } on Exception catch (e) {
      logger.wtf('Can not fetch verified url', e);
    }

    return left(const AuthFailure.unexpected());
  }
}
