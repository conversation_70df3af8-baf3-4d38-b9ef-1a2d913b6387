import 'dart:async';

import 'package:capp_api/capp_api.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/api/pending_actions_api.dart';

import 'survey/survey_pending_action_extensions.dart';

class NewPendingActionsRepository extends INewPendingActionsRepository {
  final PendingActionsApi? api;
  final Logger? logger;
  final StreamController<PendingActionModel> _openController = StreamController.broadcast();
  final StreamController<PendingActionModel> _expiredController = StreamController.broadcast();
  final IFeatureFlagRepository featureFlagRepository;

  NewPendingActionsRepository({required this.api, required this.logger, required this.featureFlagRepository});

  @override
  void close() {
    _openController.close();
    _expiredController.close();
  }

  bool _checkFeatureIsDisabled(ProductType type, String featureName, PendingActionModel model) {
    return type.name.equalsIgnoreCase(model.metadataProductType) &&
        featureFlagRepository.isEnabledCached(featureName) == false;
  }

  bool _checkProductExist(PendingActionModel model) {
    return ProductType.values.map((e) => e.name.toLowerCase()).contains(model.metadataProductType?.toLowerCase());
  }

  bool allowPendingActions(PendingActionModel model) {
    if (model.type == PendingActionType.cuidPairing &&
        featureFlagRepository.isEnabledCached(FeatureFlag.pairCuidLater) == false) {
      return false;
    }
    if (model.type == PendingActionType.signAppendix) {
      if (featureFlagRepository.isEnabledCached(FeatureFlag.loanSignatureAppendix) == false) {
        return false;
      }
    }
    if (model.type == PendingActionType.signContract) {
      if (featureFlagRepository.isEnabledCached(FeatureFlag.loanSignature) == false) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.buyNowPayLater, FeatureFlag.homePendingActionsBnplSignature, model)) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.qwarta, FeatureFlag.homePendingActionsQwartaSignature, model)) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.cashLoan, FeatureFlag.homePendingActionsCashLoanSignature, model)) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.productLoan, FeatureFlag.homePendingActionsPosLoanSignature, model)) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.payLater, FeatureFlag.homePendingActionsPayLaterSignature, model)) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.personalCard, FeatureFlag.homePendingActionsCreditCardSignature, model)) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.virtualCard, FeatureFlag.homePendingActionsCreditCardSignature, model)) {
        return false;
      }
      if (_checkFeatureIsDisabled(ProductType.instantCard, FeatureFlag.homePendingActionsCreditCardSignature, model)) {
        return false;
      }
      return _checkProductExist(model);
    }
    if (model.type == PendingActionType.webTransactions) {
      if (featureFlagRepository.isEnabledCached(FeatureFlag.homePendingActionsOpgTransactions) == false) {
        return false;
      }
    }

    if (model.type == PendingActionType.survey) {
      // During integration test, there seems _expiredController is already closed
      // and causing exception
      if (model.hasSurveyExpired() && !_expiredController.isClosed) {
        _expiredController.add(model);
        return false;
      }
    }
    if (model.type == PendingActionType.wKsurvey &&
        !featureFlagRepository.isEnabledCached(FeatureFlag.vnWelcomeKitSurvey)) {
      return false;
    }
    return true;
  }

  @override
  Future<Either<PendingActionsFailure, PendingActionListModel>> load({String? traceId}) async {
    try {
      final response = await api!.pendingActionsGet(
        options: Options(
          headers: <String, dynamic>{'traceparent': traceId},
        ),
      );
      final result = PendingActionListModel(
        items: response.toDomain().items.where(allowPendingActions).toList()..sort(_sortActionModel),
      );
      return right(result);
    } on DioError catch (e) {
      if (e.response?.statusCode == 401) {
        return left(const PendingActionsFailure.unauthorized());
      }
      logger!.wtf('PendingActions failed $e');
      return left(const PendingActionsFailure.unexpected());
    } on Exception catch (e) {
      logger!.wtf('PendingActions failed $e');
      return left(const PendingActionsFailure.unexpected());
    }
  }

  int _sortActionModel(PendingActionModel a, PendingActionModel b) {
    return _getOrderIndex(a).compareTo(_getOrderIndex(b));
  }

  int _getOrderIndex(PendingActionModel model) {
    if (model.type == PendingActionType.signContract) {
      return 0;
    } else {
      return 1;
    }
  }

  @override
  Stream<PendingActionModel> get opening => _openController.stream;

  @override
  Stream<PendingActionModel> get expired => _expiredController.stream;

  @override
  void open(PendingActionModel item) {
    _openController.add(item);
  }
}
