import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

final class SurveyPendingActionConst {
  SurveyPendingActionConst._();

  static const Duration expirationDuration = Duration(days: 3);
}

extension SurveyPendingActionExtensions on PendingActionModel {
  bool hasSurveyExpired({Logger? l}) {
    if (metadata?.containsKey(pendingActionMetadataKeyCreationDate) ?? false) {
      final creationDate = metadata![pendingActionMetadataKeyCreationDate];
      if (creationDate != null) {
        try {
          final creationDateTime = DateTime.parse(creationDate);
          final expirationDateTime = DateTime.now().toUtc().subtract(SurveyPendingActionConst.expirationDuration);

          return creationDateTime.isBefore(expirationDateTime);
        } on Exception catch (e, s) {
          l?.wtf('Can not parse creationDate for survey from pending action', e, s);
        }
      }
    }

    return false;
  }

  String? get surveySessionId {
    if (metadata?.containsKey(pendingActionMetadataKeySessionId) ?? false) {
      return metadata![pendingActionMetadataKeySessionId];
    }
    return null;
  }
}
