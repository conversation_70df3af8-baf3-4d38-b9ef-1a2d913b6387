import 'package:capp_domain/capp_domain.dart';

import '../capp_home_core.dart';

class CappHomeSettings {
  final List<AccountMenuItem> accountMenuItems;
  final TabItem? fabItem;
  final List<AssistantIconItem> assistantIconItems;
  final List<AssistantChipItem> assistantChipItems;
  final BuildActionBeltItems buildActionBeltItems;
  final bool showAssistantGuestContactButton;
  final bool actionBeltHasTitle;
  final bool hasImprovedLoansScreen;
  final bool showMyCoupons;
  final String? blogsPromoCategoryId;
  final String blogsFinancialLiteracyCategoryId;
  final String? blogsWarningZoneCategoryId;
  final bool showNearestPartnerShopButton;
  final bool hasActivityTab;
  final bool showBannerConset;
  final bool usesNewScoringBanner;
  final bool showPosList;
  final String animatedPendingActionIcon;
  final bool isSegmentationEnabled;

  const CappHomeSettings({
    this.fabItem,
    this.accountMenuItems = const [],
    this.assistantIconItems = const [],
    this.assistantChipItems = const [],
    this.showAssistantGuestContactButton = false,
    this.actionBeltHasTitle = false,
    this.hasImprovedLoansScreen = false,
    required this.buildActionBeltItems,
    this.showMyCoupons = false,
    this.hasActivityTab = false,
    this.blogsPromoCategoryId,
    required this.blogsFinancialLiteracyCategoryId,
    this.blogsWarningZoneCategoryId,
    this.showNearestPartnerShopButton = true,
    this.showBannerConset = false,
    this.usesNewScoringBanner = false,
    this.showPosList = false,
    this.animatedPendingActionIcon = 'packages/capp_ui/assets/animation/alarm_clock.flr',
    this.isSegmentationEnabled = false,
  });
}
