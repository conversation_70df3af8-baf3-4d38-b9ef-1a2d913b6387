class FeatureFlag {
  static const String traitKeyApp = 'app';
  static const String traitValueApp = 'CAPP';
  static const String traitKeyPlatform = 'platform';
  static const String traitKeyRegistered = 'registered';
  static const String mainTabsActivity = 'us_78791_activity_entry_point';
  static const String permissionDisclosure = '81944_disclosure_screen';
  static const String freshLoanJourneyStartSMSPermission = 'us_90357_loan_journey_start_permissions';

  /// Product display
  static const String productDetailsFacelift = 'f_79477_enable_product_display';
  static const String productDetailsRedesign = 'us_107719_flexi_calc_design';
  static const String vasEnhancement = 'us_109743_enhancing_value_added_service';

  /// Fake env
  static const String fakeDefaultSeed = 'fake.default.seed';

  /// Super Kupo buttons
  static const String superKupoGame = 'capp.user.super_kupo.game';
  // Super Kupo additional buttons
  static const String superKupoRepay = 'capp.user.super_kupo.repay';
  static const String superKupoServices = 'us_19811_financial_marketplace_access_point';

  // Personal details
  static const String profileMenuChangePassword = 'us_6861_change_password';
  static const String profileShowBirthPlace = 'us_163242_show_birthplace_in_personal_details';
  static const String profileShowNationality = 'us_163242_show_nationality_in_personal_details';
  static const String profileMenuPreferredCommunication = 'us_23724_pref_com';

  static const String changePasswordOtp = 'ff_146273_change_password_with_otp';
  static const String homescreenLegalCheck = 'us_44941_homescreen_legal_check';
  static const String homeScreenFinancialLiteracy = 'f_78339_display_financial_literacy';

  /// Homepage
  static const String homePendingActionsNew = 'capp.user.home.pending_actions.new';
  static const String homePendingActionsManualRefresh = 'us_67821_pending_actions_manual_refresh';
  static const String homeFinancialEmi = 'capp.user.home.financial.emi';
  static const String homeFinancialFlexi = 'capp.user.home.financial.flexi';
  static const String homeFinancialCashLoan = 'capp.user.home.financial.cashloan';
  static const String homeFinancialPosLoan = 'capp.user.home.financial.posloan';
  static const String homeFinancialCreditCard = 'us_36888_homepage_credit_card';
  static const String homeFinancialBnpl = 'us_64034_homepage_bnpl';
  static const String homeFinancialQwarta = 'us_38590_homepage_qwarta';
  static const String homeFinancialPayLater = 'us_74797_homepage_pay_later';
  static const String homeFinancialSappi = 'us_128357_homepage_sappi';
  static const String homeActionBeltDynamic = 'us_58939_action_belt_dynamic';
  static const String homeCardsPseudoLoading = 'us_167020_homepage_cards_pseudo_loading';
  static const String homeCardsPseudoLoadingPerCard = 'us_168062_homepage_cards_pseudo_loading_per_card';
  static const String qrScannerEntryPoint = 'us_67230_qr_scanner_entry_point';
  static const String qrScannerEntryPointId = 'us_91163_qr_scanner_entry_point';
  static const String actionBeltShowDisabledItems = 'us_68485_action_belt_show_disabled_items';
  static const String homeDealsBestSellerCarousel = 'capp.deals.dashboard.best.seller.carousel';
  static const String homeSasBannersListview = 'us_77304_homepage_banners_listview';
  static const String homePendingActionsQwartaSignature = 'us_78752_qwarta_signature';
  static const String homePendingActionsBnplSignature = 'us_94843_bnpl_signature';
  static const String homeEpfsCourseEnable = 'us_78541_epfs_onboarding_course';
  static const String homePendingActionsCashLoanSignature = 'us_84161_cashloan_signature';
  static const String homePendingActionsPosLoanSignature = 'us_84161_posloan_signature';
  static const String homePendingActionsPayLaterSignature = 'us_84161_paylater_signature';
  static const String pendingActionsFacelift = 'ff_76763_facelift_pending_actions';
  static const String homePendingActionsCreditCardSignature = 'us_87206_creditcard_signature';
  static const String homePendingActionsOpgTransactions = 'us_136372_pending_action_web_transactions';
  static const String scoringBannerAbTest = 'ab_us_97387_scoring_banner_flow';
  static const String hideSuperCupo = 'f_104193_remove_mascot_lo';
  static const String homeScreenProductsAndServices = 'f_100736_show_product_service_widget';
  static const String homeScreenVnNewDesign = 'e_115375_display_homescreen_revamp_for_new_app_vn';
  static const String magnoliaEnpointsCarousel = 'f_138842_magnolia_endpoints_carousel';
  static const String customizedBanner = '145822_customized_banner';
  static const String homepageWidgets = 'us_172462_homepage_widgets';
  static const String replaceBottomMenuPromoToShop = 'us_170745_replace_bottom_nav_promo_to_shop';
  // IN - banners visibility based on banners segmentation
  static const String disableHeroBannerFlexiUsers = 'e_168583_disable_hero_banner_flexi_users';
  static const String disableHeroBannerFlexiClxUsers = 'e_168583_disable_hero_banner_flexi_clx_users';
  // IN - in apps feature flags
  static const String homePageInAppEnabled = 'us_169334_homepage_inapp_enabled';
  static const String productDetailInAppEnabled = 'us_169334_productdetail_inapp_enabled';
  static const String bod0InAppEnabled = 'us_169334_0bod_submission_inapp_enabled';

  /// Homepage Assistant
  static const String assistantShowLoanRegistration = 'us_83176_button_loan_registration_cuppo_home_screen';

  /// Inbox
  static const String inboxIsTest = 'us_82942_inbox_recieve_test_messages';
  static const String inboxDeeplinkLaunch = 'feat_106229_push_and_inbox_message_direct_to_deeplink';

  /// loans
  static const String loansBanner = 'ff_91700_notifications_exist_loans';

  // Purschase insurance
  static const String showPurchaseInsuranceEntryPoint = 'us_109617_show_purchase_insurance_entry_point';

  /// Promotions
  static const String promosDisplayCategories = 'us_76983_promos_display_categories';
  static const String promosEnableScreen = 'us_76983_enable_promos_screen';

  /// Permissions
  static const String permissionsEnabled = 'us_89193_enable_permissions';
  static const String enableAndroidIdDisclosure = 'us_170734_enable_android_id_permission_disclosure';

  /// Deals
  static const String dealsDisableOrderDownloads = 'us_69849_deals_disable_order_downloads';

  /// Cleartext password
  static const String cleartextPassword = 'ff_162936_password_encrypted_in_transit';

  /// Chatbot -->
  static const String chatbotEntryPoint = 'f_44528_chatbot';
  static const String chatbot = 'ff_50304_vn_chatbot';
  static const String chatbotTestBe = 'capp.chatbot.test_be';

  /// Notifications
  static const String v2Notifications = 'us_39752_v2_notifications';

  /// Recover password
  static const String recoveryPassword = 'f_6846_recover_password';
  static const String passwordRecoveryV2 = 'f_30029_password_recovery_v2';

  static const String enableTruecaller = 'us_28415_enable_truecaller';
  static const String changePhone = 'f_27967_change_phone';
  static const String biometricUnlock = 'f_24380_biometric_unlock';
  static const String unlockRemindBiometricVn = 'f_111900_unlock_remind_biometrics';
  static const String disableUser = 'us_56822_disable_user';
  static const String pairCuidLater = 'us_60565_pair_cuid_later';
  static const String fakeQuestsApi = 'us_61173_fake_quests_api';
  static const String passwordValidationUx = 'us_23724_password_validation_ux';
  static const String loginAfterResetPass = 'f_63806_login_after_reset_pass';
  static const String fieldsAutofillHints = 'us_61320_fields_autofill_hints';
  static const String changePhoneSelfieStep = 'us_56879_change_phone_selfie_step';
  static const String crossroads = 'f_68420_crossroads';
  static const String prospectsWithout2ndId = 'f_78513_updated_prospect_handling';
  static const String supportProcessWithoutUsername = 'f_81457_support_processes_without_username';
  static const String changePhoneBos = 'f_130816_change_phone_bos';
  static const String changeEmailSelfieStep = 'f_136860_change_email_with_selfie';

  static const String invalidatePassword = 'ff_178382_invalidate_password';

  /// App upgrade
  static const String minRequiredVersion = 'capp.min_required_version';
  static const String onboardingLanguageSelection = 'us_83651_onboarding_language_selection';

  /// EMI screens
  static const String emiContractDetailEmail = 'us_1823_emi_contract_detail_email';

  // EMI switch
  static const String emiTransactionsV2 = 'us_60796_emi_transactions_v2';
  static const String emiSignatureV2 = 'us_60796_emi_signature_v2';
  static const String emiDocumentsV2 = 'us_60796_emi_documents_v2';
  static const String emiSignatureV4 = 'us_78350_emi_signature_v4';
  static const String emiSignatureSignPostV4 = 'us_80576_emi_signature_sign_v4';
  static const String emiSignatureSignPostV2 = 'us_80576_emi_signature_sign_v2';

  static const String accountContractualDocuments = 'us_12343_account_contractual_documents';

  static const String inbox = 'f_6932_inbox';
  static const String sasHomeBanners = 'f_54181_sas_hero_banner';
  static const String sasRTDMHomeBanners = 'us_140194_display_sas_rtdm_banners_on_homepage';
  static const String sasRTDMHomeBannersCache = 'us_140195_cache_sas_banners_on_local_device';

  /// Acl/Xcl screens
  static const String aclXclContractualDocuments = 'us_3389_contractual_documents';

  static const String logConsole = 'us_10402_insider_logs';

  static const String dynamicAvatarMultiWorkflow = 'dynamic_avatar_multi_workflow';

  static const String loanOrigination = 'us_16479_loan_origination';

  // Buy process redesign
  static const orderingOtpV2 = 'us_47411_ordering_v2';

  // Tracking
  static const String forwarderLogger = 'us_17968_events_gapp_to_dwh';
  static const String forwarderUrl = 'f_79479_forwarder_url';

  // Emi signed contract
  static const String emiSignedState = 'us_18838_emi_signed_state';

  // Cashloan signed contract
  static const String cashLoanSignedState = 'us_16050_cashloan_signed_state';

  static const String vasSafePayLite = 'us_33324_enable_vas_safe_pay_for_cashloan';
  static const String safePayLite = 'us_33324_enable_vas_safe_pay_healthlite_for_cashloan';
  static const String safePay = 'us_55570_enable_safe_pay_for_cashloan';
  static const String separateNomineeDetails = 'us_77460_separation_of_nominee_details';
  static const String dummyAppID = 'us_84558_store_dummy_app_id';
  static const String newApprovedScreenEnhancement = 'us_154640_enable_approved_screen_enhancement';
  static const String bannerNotifAdaIncentive = 'us_168196_banner_notif_ada_incentive';

  // Posloan signed contract
  static const String posLoanSignedState = 'us_22677_posloan_signed_state';

  static const String celVariationPh = 'us_65713_ph_cel_variation';

  static const String celVasDisplay = 'us_59152_vas_display';
  static const String newApprovedScreenDesign = 'us_158813_enable_approved_screen_new_design';

  // Ujjwal loan
  static const String ujjwalSectionPayment = 'us_107824_ujjwal_section_payment';
  static const String ujjwalSectionActionBelt = 'us_107824_ujjwal_section_action_belt';
  static const String ujjwalSectionDetails = 'us_107824_ujjwal_section_details';
  static const String ujjwalSectionTransactions = 'us_107824_ujjwal_section_transactions';
  static const String ujjwalSectionInstallments = 'us_107824_ujjwal_section_installments';
  static const String ujjwalSectionHelp = 'us_107824_ujjwal_section_help';

  static const String ujjwalActionMonthlyStatements = 'us_107824_ujjwal_action_monthly_statements';
  static const String ujjwalActionDocuments = 'us_107824_ujjwal_action_documents';
  static const String ujjwalActionDetails = 'us_107824_ujjwal_action_details';
  static const String ujjwalActionReminder = 'us_107824_ujjwal_action_reminder';
  static const String ujjwalActionTransactionHistory = 'us_107824_ujjwal_action_transaction_history';

  // Flexi loan
  static const String flexiSignedState = 'us_16943_flexi_signed_state';
  static const String flexiWithdrawalFeedback = 'us_108633_flexi_withdrawal_feedback';
  static const String flexiWithdrawalDefaultValue = 'us_155278_flexi_withdrawal_default_value';

  static const String flexiSectionPayment = 'us_104596_flexi_section_payment';
  static const String flexiSectionActionBelt = 'us_104596_flexi_section_action_belt';
  static const String flexiSectionDetails = 'us_104596_flexi_section_details';
  static const String flexiSectionTransactions = 'us_104596_flexi_section_transactions';
  static const String flexiSectionHelp = 'us_104596_flexi_section_help';
  static const String flexiSectionWithdrawal = 'us_104596_flexi_section_payment';

  static const String flexiActionMonthlyStatements = 'us_104596_flexi_action_monthly_statements';
  static const String flexiActionDocuments = 'us_104596_flexi_action_documents';
  static const String flexiActionBills = 'us_104596_flexi_action_bills';
  static const String flexiActionReminder = 'us_104596_flexi_action_reminder';
  static const String flexiActionDetails = 'us_127689_flexi_action_details';
  static const String flexiActionDetailsFixed = 'us_127689_flexi_action_details_fixed';
  static const String flexiActionInstallments = 'us_153141_flexi_action_installments';

  static const String flexiToFix = 'us_153137_flexi_to_fix';
  static const String flexiOmitFlexiSignatureDate = 'us_153137_omit_flexi_signature_date_for_conversion';

  static const String flexiNewAirAprMapping = 'us_162775_new_air_apr_mapping';

  //BNPL
  static const String bnplEarlyRepayment = 'us_53651_bnpl_early_repayment';
  static const String bnplOfferBanner = 'us_94843_signature_bnpl_banner';
  static const String bnplChangedProductName = 'us_172211_bnpl_changed_product_name';

  // Qwarta
  static const String qwartaIntro = 'us_58856_qwarta_intro';
  static const String qwartaActionBelt = 'us_41552_qwarta_action_belt';
  static const String qwartaDetailsAndDocuments = 'us_41552_qwarta_details_and_documents';
  static const String qwartaMonthlyStatements = 'us_60368_qwarta_montly_statements';
  static const String qwartaTransactionHistory = 'us_60368_qwarta_transaction_history';
  static const String qwartaPaymentReminder = 'us_60368_qwarta_payment_reminder';
  static const String qwartaHelpCenter = 'us_60368_qwarta_help_center';
  static const String evoucherQwartaRestrictionScreen = 'us_137516_evoucher_qwarta_restriction_screen';
  static const String lazadaQwartaRestrictionScreen = 'us_137516_lazada_qwarta_restriction_screen';
  static const String qwartaRenewal = 'us_170297_qwarta_renewal';

  // CEL
  static const String celActionInstallmentPlans = 'us_106368_cel_action_installment_plans';
  static const String celActionDetails = 'us_106368_cel_action_details';
  static const String celActionDocuments = 'us_106368_cel_action_documents';
  static const String celActionPaymentReminder = 'us_106368_cel_payment_reminder';
  static const String celFaceliftRepayment = 'us_129485_ui_facelift_repayment';
  static const String celActionAutoDebit = 'us_143222_cel_action_auto_debit';
  static const String celActionHomeCreditProtect = 'us_176205_vas_entrypoint_in_servicing';

  static const String celSectionHelp = 'us_106368_cel_section_help';
  static const String celSectionDetails = 'us_106368_cel_section_details';
  static const String celSectionActionBelt = 'us_106368_cel_section_action_belt';
  static const String celSectionPayment = 'us_106368_cel_section_payment';

  //Paylater
  static const String paylaterActionInstallmentPlans = 'us_74798_paylater_action_installment_plans';
  static const String paylaterActionDocuments = 'us_74798_paylater_action_documents';
  static const String paylaterActionBills = 'us_74798_paylater_action_bills';
  static const String paylaterActionTransactionHistory = 'us_74798_paylater_action_transaction_history';
  static const String paylaterActionShoppingNow = 'us_104902_paylater_shopping_now';

  static const String paylaterSectionPayment = 'us_74798_paylater_section_payment';
  static const String paylaterSectionActionBelt = 'us_74798_paylater_section_action_belt';
  static const String paylaterSectionDetails = 'us_74798_paylater_section_details';
  static const String paylaterSectionTransactions = 'us_74798_paylater_section_transactions';
  static const String paylaterSectionHelp = 'us_74798_paylater_section_help';

  static const String qwartaSectionPayment = 'us_94167_qwarta_section_payment';
  static const String qwartaSectionActionBelt = 'us_94167_qwarta_section_action_belt';
  static const String qwartaSectionDetails = 'us_94167_qwarta_section_details';
  static const String qwartaSectionTransactions = 'us_94167_qwarta_section_transactions';
  static const String qwartaSectionHelp = 'us_94167_qwarta_section_help';

  static const String qwartaActionDocuments = 'us_94277_qwarta_action_documents';
  static const String qwartaActionBills = 'us_94277_qwarta_action_bills';
  static const String qwartaActionDetails = 'us_94277_qwarta_action_details';
  static const String qwartaActionTransactions = 'us_94277_qwarta_action_transactions';

  //CreditCard
  static const String creditCardActionInstallmentPlans = 'us_89199_credit_card_action_installments';
  static const String creditCardActionDocuments = 'us_89199_credit_card_action_documents';
  static const String creditCardActionDetails = 'us_89199_credit_card_action_details';
  static const String creditCardActionBills = 'us_89199_credit_card_action_bills';
  static const String creditCardActionTransactionHistory = 'us_89199_credit_card_action_transaction_history';
  static const String creditCardActionManageCards = 'us_89199_credit_card_action_manage_cards';
  static const String creditCardActionLoyaltyPoints = 'us_89199_credit_card_loyalty_points';
  static const String creditCardActionHelpCenter = 'us_89199_credit_card_action_help_center';
  static const String creditCardActionShoppingNow = 'us_104902_credit_card_shopping_now';
  static const String creditCardActionAutoDebit = 'us_143222_credit_card_action_auto_debit';
  static const String creditCardActionConvertBalance = 'us_151647_credit_card_action_convert_balance';
  static const String creditCardConvertBalance = 'us_151647_credit_card_convert_balance';
  static const String creditCardConvertBalanceVariantB = 'us_176325_credit_card_convert_balance_variant_b';
  static const String creditCardCancelConvertBalance = 'us_161020_credit_card_cancel_convert_balance';
  static const String creditCardCancelHideCancelBalance = 'us_180281_credit_card_hide_cancel_balance';

  static const String creditCardSectionTransactions = 'us_89199_credit_card_section_transactions';
  static const String creditCardSectionPayments = 'us_89199_credit_card_section_payments';
  static const String creditCardSectionActionBelt = 'us_89199_credit_card_section_action_belt';
  static const String creditCardLoyaltyPoints = 'us_89199_credit_card_section_loyalty';
  static const String creditCardSectionDashboard = 'us_89199_credit_card_section_dashboard';
  static const String creditCardSectionHelp = 'us_89199_credit_card_section_help';
  static const String creditCardSectionDetails = 'us_89199_credit_card_section_details';

  // CC Daily Limit
  static const String creditCardDailyLimit = 'us_169781_set_cc_daily_limit';
  static const String creditCardInStoreLimit = 'us_169781_cc_in_store_shopping_limit';
  static const String creditCardOnlineLimit = 'us_169781_cc_online_shopping_limit';
  static const String creditCardWithdrawalLimit = 'us_169781_cc_cash_withdrawal_limit';

  //Txn Details
  static const String txnDetailsEReceiptPh = 'us_148212_ereceipt_ph';

  static const String ccAndQwartaReferenceNumberPh = 'us_152479_cc_qwarta_reference_number_ph';

  //Kyc Pending
  static const String digilocker2ndBod = 'us_57515_digilocker_2nd_bod';

  // Loan signature
  static const String loanSignatureOpenDetailAfterFinish = 'us_92772_open_product_overview_after_finish';
  static const String loanSignature = '43709_sign_contract_pending_action';
  static const String loanSignatureAppendix = 'us_133348_sign_contract_appendix';
  static const String redesignApplicationCompleteScreen = 'us_102091_redesign_application_complete_screen';
  static const String loanSignatureFlexiImprovements = 'us_109951_signature_flexi_improvements';

  static const String loanSignatureAdditionalCheckboxReviewFiles =
      'us_109963_ph_signature_additional_checkbox_review_files';

  static const String loanSignaturePosConsentToDp = 'us_158446_pos_consent_to_dp';

  static const String loanSignaturePosCheckPickUp = 'us_66940_pos_check_pick_up';

  static const String workflowMultiInstance = 'us_56664_multi_instance_workflow_enabled';

  static const String setCustomGiftcardAmount = 'us_23005_set_custom_giftcard_amount';

  static const String enableNotificationPooling = 'us_19373_loan_enable_notification_polling';

  static const String enableNotificationPush = 'us_24593_loan_enable_notification_push';

  static const String monthlyStatements = 'us_24967_monthly_statements';

  static const String celDisplayInfoNote = 'us_33583_cel_display_i_note';

  static const String newRepayment = 'us_34025_new_repayment';

  static const String creditCardConvertTransaction2IP = 'us_40036_credit_card_convert_transaction_2_ip';

  static const String vnEServiceWaf = 'us_55792_eservice_waf';

  static const String creditCardFlexibleInstallments = 'us_41665_credit_card_flexible_installments';
  static const String creditCardInstallmentsList = 'us_76531_credit_card_installments_list';
  static const String creditCardTransactionsV3 = 'us_178628_credit_card_transactions_v3';

  static const String creditCardDevelopmentPhDetailsAndDocuments =
      'us_60367_credit_card_development_ph_details_and_documents';
  static const String creditCardDevelopmentPhInstallmentPlans = 'us_60367_credit_card_development_ph_installment_plans';
  static const String creditCardDevelopmentPhMonthlyStatements =
      'us_60367_credit_card_development_ph_monthly_statements';
  static const String creditCardDevelopmentPhManageCard = 'us_60367_credit_card_development_ph_manage_card';
  static const String creditCardDevelopmentPhHelpCenter = 'us_84064_credit_card_development_ph_help_center';

  static const String loanCardsNew = 'us_77418_new_loan_cards';
  static const String loanCardsEnhancedPh = 'us_140482_enhanced_loan_cards_ph';
  static const String loanCardsHideOld = 'us_77418_hide_old_loan_cards';
  static const String loanCardsAndDashboardEnhancementPh = 'us_154702_account_cards_and_dashboard_enhancement_ph';
  static const String vnRevampBiggerCards = 'us_146565_vn_revamp_bigger_cards';

  static const String creditCardDisableActivationActive = 'us_93114_cc_disable_activation_active';
  static const String creditCardDisableActivationInactive = 'us_93114_cc_disable_activation_inactive';
  static const String creditCardDisableActivationRenewal = 'us_93114_cc_disable_activation_renewal';

  static const String creditCardCCRenewalActivation = 'f_158108_cc_renewal_activation';

  static const String accountCardRenewalActivation = 'us_173289_account_card_renewal_activation';

  static const String emiJourneyHideMap = 'us_77657_hide_map_emi_journey';

  static const String qwartaOfferBanner = 'us_85109_signature_qwarta_banner';

  static const String uloNewAddressVerification = 'f_91319_ulo_new_address_verification';
  static const String signatureSafePayLegalLog = 'us_106831_safe_pay_legal_log';

  /// Self-Service
  static const String profileMenuSelfService = 'capp.profile.self-service';
  static const String profileMenuSelfServiceEloads = 'us_29762_payment_eloads';
  static const String profileMenuSelfServiceEBills = 'us_32772_payment_ebills';
  static const String profileMenuSelfServiceQR = 'us_32771_payment_qr_scanner';
  static const String profileMenuSelfServiceRepayment = 'us_33006_payment_repayment';
  static const String repaymentDataSplit = 'us_53065_payment_split';
  static const String homeRefreshTimer = 'us_58686_homeRefreshTimer';
  static const String loanSignatureCreditCardLimit = 'us_58088_credit_card_limit';
  static const String contractSoldFilter = 'us_57001_contract_sold_filter';
  static const String relVnAdditionalRepaymentInformation = 'us_138844_vn_rel_additional_information_repayment';
  static const String bnplMonthlyStatements = 'us_67899_bnpl_statements';
  static const String bnplSectionHelp = 'us_95974_bnpl_section_help';
  static const String bnplSectionActionBelt = 'us_95974_bnpl_section_action_belt';
  static const String bnplSectionPayment = 'us_95974_bnpl_section_payment';
  static const String bnplSectionDetails = 'us_95974_bnpl_section_details';
  static const String bnplSectionTransactions = 'us_95974_bnpl_section_transactions';
  static const String bnplActionTransactionHistory = 'us_95974_bnpl_action_transaction_history';
  static const String bnplActionInstallmentPlans = 'us_95974_bnpl_action_installment_plans';
  static const String bnplActionDetails = 'us_104187_bnpl_action_details';
  static const String bnplActionDocuments = 'us_95974_bnpl_action_documents';
  static const String bnplActionBills = 'us_95974_bnpl_action_bills';
  static const String bnplActionAutoDebit = 'us_143222_bnpl_action_auto_debit';

  static const String apiCachingMinutes = 'us_149257_api_caching';
  static const String ujjwalApiCaching = 'us_149257_ujjwal_api_caching';
  static const String flexiApiCaching = 'us_149262_flexi_api_caching';
  static const String celApiCaching = 'us_152142_cel_api_caching';
  static const String creditCardApiCaching = 'us_152143_credit_card_api_caching';
  static const String bnplApiCaching = 'us_152144_bnpl_api_caching';
  static const String insuranceApiCaching = 'us_153986_insurance_api_caching';
  static const String qwartaApiCaching = 'us_153985_qwarta_api_caching';
  static const String paylaterApiCaching = 'us_153897_paylater_api_caching';

  static const String celGiftPayment = 'us_55993_gift_payment';
  static const String posCashEventName = 'us_88647_calendar_event_name';
  static const String celInstallmentNaming = 'us_90178_installment_naming';
  static const String actionPromiseToPay = 'us_93193_action_promise_to_pay';
  static const String actionPaymentReminder = 'us_93622_action_payment_reminder';
  static const String actionCreditCardManagement = 'us_93625_action_credit_card_management';
  static const String repayExistingLoanFeedbackForm = 'us_108634_repy_ext_loan';
  static const String useOutstandingPeriodAmount = 'us_167673_outstanding_period_amount_mapping_ph';

  // Transactions
  static const String transactionEbillsWaterBill = 'us_54426_payment_ebills_water_bill';
  static const String transactionEbillsElectricityBill = 'us_54086_payment_ebills_electricity_bill';
  static const String transactionAccountScreenTab = 'us_72831_transaction_history_account_tab';
  static const String recentLoadsCarousel = 'us_88823_recent_loads_carousel';
  static const String paymentELoadsBaoKim = 'us_90252_payment_eloads_baokim';
  static const String paymentQrWelcomeScreen = 'us_109154_payment_qr_welcome_screen';
  static const String findAStoreEntrypoint = 'us_168323_qr_barcode_find_a_store_entrypoint';
  static const String paymentQrListPartnersCta = 'us_109154_payment_qr_list_partners_cta';
  static const String qrBarcodeGeneration = '108227_barcode_generation_entrypoint';
  static const String qrPartnerLogoQrPh = 'us_128757_payment_qr_partner_qrph';
  static const String qrPartnerLogoMaya = 'us_128757_payment_qr_partner_maya';
  static const String qrPartnerLogoAub = 'us_128757_payment_qr_partner_aub';
  static const String qrPartnerLogoHomeCredit = 'us_128757_payment_qr_partner_hc';
  static const String qrPartnerLogoBdo = 'us_128757_payment_qr_partner_bdo';
  static const String qrUploadEntrypoint = 'us_169208_qr_upload_entrypoint';
  static const String transactionEbillsSaveBillRegister = 'us_133828_save_bill_register';
  static const String transactionEbillsSaveBillAll = 'us_134698_save_bill_all';
  static const String transactionPostpaid = 'us_123570_postpaid_service';
  static const String topupMaxLimitTransaction = 'us_147095_top_up_max_limit_trx';
  static const String checkSaraInstalledInDevice = 'us_152307_sara_device_checking';
  static const String transactionWaterBill = 'us_172708_entry_point_category_screen_water_bill';
  static const String transactionElectricBill = 'us_172708_entry_point_category_screen_electric_bill';
  static const String eloadsUserFeedbackJourney = 'us_163131_feedback_eloads';
  static const String ebillsUserFeedbackJourney = 'us_163130_feedback_ebills';
  static const String vietQrUserFeedbackJourney = 'us_163126_feedback_viet_qr';
  static const String partnerQrUserFeedbackJourney = 'us_163127_feedback_qr_partner';
  static const String hplTenorRevamp = 'f_174046_hpl_revamp_tenor';
  static const String hplPromotionQrCommodities = '174161_hpl_promotion_qr_commodities';
  static const String hplSignCreditLineContract = 'f_176136_sign_credit_line_contract';
  static const String topupPromotionBadge = 'us_179214_topup_promo_badge';

  // EBills
  static const String eBillerReminderField = 'us_139813_biller_reminder_field';
  static const String eBillMaxLimitTransaction = 'us_147095_bill_max_limit_trx';

  // Transactions history
  static const String homePageRecentTransactions = 'f_54409_recent_transactions';
  static const String homePageRecentTransactionsLoads = 'us_90079_recent_transaction_carousel_loads';
  static const String homePageRecentTransactionsBills = 'us_90080_recent_transaction_carousel_bills';
  static const String homePageRecentTransactionsLazadaTopup = 'us_90081_recent_transaction_carousel_lazada';
  static const String homePageRecentTransactionsViewAll = 'us_129102_homescreen_cta_view_all_transaction_dashboard';

  static const String contractDocumentEmail = 'us_32447_contract_document_email';

  // Static loan product selection
  static const String staticLoanProductSelection = 'ff_28791_static_loan_product_selection';
  static const String enableCashStaticProductSelection = 'ff_28791_enable_cash_static_product_selection';

  // Golden package

  static const String demoMenuGoldenFooView = 'us_30630_golden_foo_view';
  static const String demoMenuGoldenFooEdit = 'us_30631_golden_foo_edit';

  // Repayment
  static const String repaymentMomo = 'us_42005_repayment_momo';
  static const String repaymentOnepay = 'us_42005_repayment_onepay';
  static const String repaymentShopeepay = 'us_42005_repayment_shopeepay';
  static const String repaymentViettelmoney = 'us_42005_repayment_viettelmoney';
  static const String repaymentVnpay = 'us_42005_repayment_vnpay';
  static const String repaymentZalopay = 'us_42005_repayment_zalopay';
  static const String repaymentBankTransfer = 'us_43707_repayment_bank_transfer';
  static const String repaymentBankTransferQr = 'us_59342_repayment_bank_transfer_qr_code';
  static const String repaymentBankTransferApp2App = 'us_81715_repayment_bank_transfer_app2app';
  static const String repaymentBappPaymentMethod = 'us_131493_repayment_bapp_payment_method';

  static const String repaymentGoTagihan = 'us_52846_repayment_gotagihan';
  static const String repaymentAyopop = 'us_52846_repayment_ayopop';
  static const String repaymentBebasBayar = 'us_52846_repayment_bebasbayar';
  static const String repaymentTokopedia = 'us_52846_repayment_tokopedia';
  static const String repaymentBukalapak = 'us_52846_repayment_bukalapak';
  static const String repaymentLazada = 'us_52846_repayment_lazada';
  static const String repaymentBlibli = 'us_52846_repayment_blibli';
  static const String repaymentQris = 'us_52846_repayment_qris';

  static const String repaymentAda = 'us_59110_repayment_ada_method';
  static const String repaymentOTC = 'us_51128_repayment_otc';
  static const String repaymentOTCWebview = 'us_28732_repayment_otc_webview';

  static const String repaymentManageSavedCard = 'us_60441_repayment_manage_saved_card';
  static const String repaymentOnlinePayInApp = 'us_63329_repayment_online_pay_in_app';

  static const String repaymentEntryPointActionBelt = 'us_64788_repayment_entry_point_action_belt';
  static const String repaymentPromiseToPayEntryPointMainScreen =
      'us_51927_repayment_promise_to_pay_entry_point_main_screen';

  static const String repaymentBankTransferMethodId = 'us_62203_repayment_method_bank_transfer';
  static const String repaymentInAppPaymentMethodId = 'us_29501_repayment_method_inapp';
  static const String repaymentEmoneyEcommerceMethodId = 'us_62204_repayment_method_emoney_ecom';
  static const String repaymentRetailMethodId = 'us_28879_repayment_method_retail';
  static const String repaymentAdaOnlineBank = 'us_76790_ada_onlinebank_method_list';
  static const String repaymentAdaRegularBank = 'us_76790_ada_regularbank_method_list';
  static const String repaymentAdaDebitCard = 'us_76790_ada_debit_card_method_list';

  static const String repaymentMethodEWallet = 'us_77887_method_screen_ewallet_option';
  static const String repaymentMethodInternetBanking = 'us_77887_method_screen_internet_banking';
  static const String repaymentMethodAtmCard = 'us_77887_method_screen_atm';

  static const String repaymentNew = 'us_81850_new_repayment';
  static const String repaymentSatisfactionFeedback = 'us_82698_satisfaction_feedback_repayment';

  static const String repaymentInAppDoku = 'us_83082_inapp_doku_debit_card';
  static const String repaymentInAppBcaKlickpay = 'us_83082_inapp_bca_klickpay';
  static const String repaymentInAppBriEpay = 'us_83082_inapp_bri_epay';
  static const String repaymentInAppCimbOctoklick = 'us_83082_inapp_cimb_octoklick';
  static const String repaymentInAppDanamon = 'us_83082_inapp_danamon';
  static const String repaymentInAppPermatanet = 'us_83082_inapp_permatanet';

  static const String repaymentDirectDiscountMainScreen = 'us_77218_repayment_direct_discount';
  static const String repaymentMomoApiV2 = 'us_91729_repayment_momoapi_v2';

  static const String repaymentMainScreenBanner = 'us_101800_repayment_main_banner_nov23';
  static const String repaymentAllowDirectDiscountEL = 'us_93483_repayment_direct_discount_el_contract';
  static const String repaymentPopupPleaseStay = 'us_96222_repayment_popup_please_stay';
  static const String repaymentResultBanner = 'us_100521_repayment_result_banner';
  static const String repaymentEWalletAlfamart = 'us_101806_ewallet_topup_alfamart';
  static const String repaymentEWalletBca = 'us_101806_ewallet_topup_bca';
  static const String repaymentEWalletBni = 'us_101806_ewallet_topup_bni';
  static const String repaymentEWalletCimb = 'us_101806_ewallet_topup_cimb';
  static const String repaymentEWalletBersamaPrima = 'us_101806_ewallet_topup_bermasa_prima';

  static const String repaymentOnlinePayment07Mar = 'us_104458_online_payment_07mar';
  static const String repaymentBrankas = 'us_104458_repayment_brankas';
  static const String repaymentPaynamics = 'us_104458_repayment_paynamics_07mar';

  static const String repaymentSappiAlertBar = 'us_125284_repayment_sappi_alert_bar';
  static const String repaymentSappiPromotionAllowance = 'us_125284_repayment_sappi_promotion_allowance';
  static const String repaymentContractSourceGetFromPcs = 'us_125284_repayment_get_from_pcs';
  static const String repaymentOption = 'us_133265_payment_options_id';
  static const String repaymentPromotionFirstAdoption = 'f_132854_repayment_voucher';
  static const String repaymentAdaManagementMenu = 'us_121698_ada_mngt_ada_profile';
  static const String repaymentNewHomePayLaterName = 'us_172084_repayment_hpl_new_product_description';
  static const String repaymentCollectData = 'us_175828_repayment_collect_device_data';

  // Auto debit
  static const String autoDebitOnepay = 'us_142834_auto_debit_onepay';
  static const String autoDebitZalopay = 'us_142834_auto_debit_zalopay';

  // Barcode payment
  static const String paymentQrBarcodePartner711 = 'us_123836_payment_qr_barcode_partner_711';
  static const String paymentQrBarcodePartnerPureGold = 'us_123836_payment_qr_barcode_partner_puregold';

  //Error messge
  static const String showErrorMessage = 'us_44597_show_error_message';

  /// Generic
  /// valid values 'mobileNumberPackage' and 'googleCredentialsApi'
  static const String phoneNumberHintDialogType = 'us_32148_phone_number_hint_dialog';

  // Innovatrics
  static const String cameraAccessDeniedIos = 'us_75356_camera_access_denied_ios';
  static const String cropGreenFrameDocument = 'us_77408_crop_green_frame_document';
  static const String cameraAccessDeniedAndroid = 'us_75356_camera_access_denied_android';
  static const String nativeViewComponent = 'us_103798_native_innovatrics';
  static const String selfieNewDota = 'us_128411_selfie_new_dota';
  static const String idCardNewDota = 'us_128411_id_card_new_dota';

  // Selfie
  static const String threeDaysSelfieValidityNewUser = 'us_46952_3d_selfie_newclient';
  static const String threeDaysSelfieValidityExgUser = 'us_46952_3d_selfie_exgclient';
  static const String livelinessCheckHV = 'f_54010_liveliness_check';
  static const String activeLivenessPH = 'ff_69060_feat_active_liveness_score';
  static const String ekycGuidelineScreen = 'us_95024_ekyc_guideline_screen';
  static const String isActiveLiveness = 'us_139391_active_liveness';
  static const String isSmileEndpoint = 'us_150970_selfie_smile_endpoint';

  // ID Card
  static const String threeDaysIdCardValidityNewUser = 'us_46952_3d_id_newclient';
  static const String threeDaysIdCardValidityExgUser = 'us_46952_3d_id_exgclient';
  static const String changeAttemptTimes = 'us_85414_change_attempt_times';
  static const String cacheDocumentImages = 'us_83509_cache_document_async';
  static const String newInnovatricsSwitchAddress = 'us_122490_switch_address';
  static const String idCardBirthPlace = 'us_136025_birthplace';
  static const String idCardNationality = 'us_136025_citizenship';
  static const String idCardLandmark = 'us_138226_landmark';
  static const String idCardZipCode = 'us_138226_zipcode';
  static const String updateOcrIdNumberToDms = 'us_167212_update_nid_number_from_ocr_to_dms';
  // New feature flag for disabling the date
  static const String isExistingCustomerPrefillAndHideDate = 'us_158401_isexistingcustomer_prefillandhide_birthdate';
  static const String requestNfcChecking = 'us_152327_nfc_eli_checking_flag';
  static const String checkNfcSupportBeforeOcr = 'us_178205_check_nfc_device_support_b4_scan';

  // Ekyc Video Injection
  static const String doEvaluateSelfieVideoInjection = 'us_165725_ekyc_selfie_video_injection_api_call';
  static const String waitForSelfieVideoInjectionEvaluationCompleted =
      'us_165725_ekyc_selfie_video_injection_waiting_api_res';

  static const String doEvaluateDocumentVideoInjection = 'us_165725_ekyc_nid_video_injection_api_call';
  static const String waitForDocumentVideoInjectionEvaluationCompleted =
      'us_165725_ekyc_nid_video_injection_waiting_api_res';

  static const String updateMandatoryNfcInfoFor1BoDCCXAndUpdateIdCardNfc =
      'us_178206_nfc_id_info_popup_scope_updateidcard_n_application';
  static const String updateMandatoryDmsAndNfcInfoSignatureCcx = 'us_178798_ekyc_siging_popup_ccx_v2';
  static const String updateMandatoryDmsInfoSignatureOther = 'us_178798_ekyc_siging_popup_clx_v2';
  static const String eKycNewAddressComponent = 'us_180648_new_id_form_design';

  // scan mandate
  static const String scanMandate = 'us_34194_scan_mandate';
  // master mandate
  static const String masterMandate = 'f_98178_master_mandate';
  static const String upiMandate = '124718_upi_mandate';

  // PAN Info from Digilocker
  static const String panInfoFromDigio = '143466_pan_digilocker_telesales';

  // standalone journey
  static const String digilockerKyc = 'us_44161_digilockerkyc_handling';

  //Credit card
  static const String cardPinChange = 'f_39291_card_pin_change';
  static const String cardReplacement = 'f_23476_replace_card';
  static const String cardPanView = 'f_31052_card_pan_view';
  static const String cardPhysicalPanView = 'f_55362_physical_card_pan_view';
  static const String cardPhysicalIssuance = 'f_31055_physical_card_issue';
  static const String cardBlockPcc = 'f_22400_card_block';
  static const String cardUnblockPcc = 'f_28613_card_unblock';
  static const String cardBlockVcc = 'f_42670_card_block_vcc';
  static const String cardUnblockVcc = 'f_42671_card_unblock_vcc';
  static const String cardLoyaltyWidget = 'f_41350_loyalty_widget';
  static const String cardLoyaltyRedeem = 'f_41351_loyalty_redeem';
  static const String cardListWidget = 'f_38731_cardlist_widget';
  static const String cardDeliveryTracking = 'f_55366_card_delivery_tracking';
  static const String cir17EntryPoints = 'f_60343_cir17_entry_points';
  static const String cardSpendingLimit = 'f_70510_card_spending_limit';
  static const String cardChipSpendingLimit = 'f_70510_new_feature_chip_card_spending_limit';
  static const String cardVnTotalAmountDue = 'f_87758_vn_total_amount_due';
  static const String vasVocActivationEligibility = 'f_156351_call_to_get_voc_insurance_details';
  static const String conversionV3Endpoints = 'f_181128_enable_conversion_v3_enpoints';

  // EVoucher
  static const String evoucherSearchOnDashboard = 'us_76391_search_voucher_dashboard';
  static const String evoucherSearchOnMerchantDetail = 'us_81571_search_merchant_detail';
  static const String evoucherSearchOnCategoryDetail = 'us_78556_search_category_detail';
  static const String evoucherScreenshotVoucherCode = 'us_88974_screenshot_voucher_code';

  // EWallet
  static const String ewalletDashboardActionBelt = 'f_56847_ewallet_dashboard_action_belt';
  static const String ewalletDashboardTopup = 'f_56847_ewallet_dashboard_topup';
  static const String ewalletDashboardQris = 'f_56847_ewallet_dashboard_qris';
  static const String ewalletDashboardPpob = 'f_56847_ewallet_dashboard_ppob';
  static const String ewalletDashboardTrxHistory = 'f_56847_ewallet_dashboard_trx_history';

  // Crack Screen Protection
  static const String cspPromotionCampaign = 'f_92472_csp_campaign';
  static const String cspIntroAbtesting = 'f_135850_csp_intro_screen_abtesting';
  static const String cspDisplayReferralCode = 'f_132772_display_referral_code';

  // Promotion banner
  static const String bannerCCDashboard = 'us_99033_banner_cc_dashboard';
  static const String bannerLoanDashboard = 'us_99033_banner_loan_dashboard';
  static const String bannerLoyaltyDashboard = 'us_99033_banner_loyalty_dashboard';
  static const String bannerGamification = 'f_99032_enable_banner_gamification';

  // Data Scoring
  static const String runCredolab = 'f36229_run_credolab';
  static const String runDatascore = 'us51471_run_datascore';
  static const String legalConsentBapp = 'f_95101_split_legal_consents';
  static const String permissionGuidelineVn = 'us_96789_permission_guideline';

  // Legal consent and permission
  static const String triggerPermissionRevokePh = 'us_113640_trigger_permission_revoke';

  static const String cardReminder = 'us_50819_credit_card_reminder';

  // Action Belt FF,
  static const String actionBeltOldDesign = 'us_95659_two_lines_action_belt';

  // Action Belt FFs PH
  static const String actionBeltWalletId = 'action_belt_id_wallet';

  // Action Belt FFs PH
  static const String actionBeltRepaymentPh = 'action_belt_ph_repayment';
  static const String actionBeltHotDealsPh = 'action_belt_ph_hotdeals';
  static const String actionBeltDiscountsPh = 'action_belt_ph_discounts';
  static const String actionBeltLoadsPh = 'action_belt_ph_loads';
  static const String actionBeltBillsPh = 'action_belt_ph_bills';
  static const String actionBeltMaketplacePh = 'action_belt_ph_marketplace';
  static const String actionBeltTopUpPh = 'action_belt_ph_topUp';
  static const String actionBeltVouchersPh = 'action_belt_ph_vouchers';
  static const String actionBeltCebuPacificPh = 'action_belt_ph_cebu_pacific';

  // Action Belt FFs VN
  static const String actionBeltRepaymentVn = 'action_belt_vn_repayment';
  static const String actionBeltRegisterLoanVn = 'action_belt_vn_register_loan';
  static const String actionBeltPayLaterVn = 'action_belt_vn_pay_later';
  static const String actionBeltCreditCardVn = 'action_belt_vn_credit_card';
  static const String actionBeltGameVn = 'action_belt_vn_game';
  static const String actionBeltLoadsVn = 'action_belt_vn_loads';
  static const String actionBeltElectricity = 'action_belt_vn_electricity';
  static const String actionBeltWaterVn = 'action_belt_vn_water';
  static const String actionBeltBikeLoanVn = 'action_belt_vn_bike_loan';
  static const String actionBeltCDVn = 'action_belt_vn_cd';
  static const String actionBeltEBillsVn = 'action_belt_vn_ebills';
  static const String actionBeltCrackScreenProtectionInsurenceVN = 'action_belt_vn_crack_screen_protection_insurance';

  // Action Belt FFs IN
  static const String actionBeltCreditCardIn = 'action_belt_in_credit_card';
  static const String actionBeltFreeCreditScoreIn = 'action_belt_in_free_credit_score';
  static const String actionBeltHomeLoanIn = 'action_belt_in_home_loan';
  static const String actionBeltWalletSecurityIn = 'action_belt_in_wallet_security';
  static const String actionBeltGiftCardIn = 'action_belt_in_gift_card';
  static const String actionBeltGoldLoanIn = 'action_belt_in_gold_loan';
  static const String actionBeltDigitalGoldIn = 'action_belt_in_digital_gold';
  static const String actionBeltCare360In = 'action_belt_in_care_360';
  static const String actionBeltHealthInsuranceIn = 'action_belt_in_health_insurance';
  static const String actionBeltHomeProtectionIn = 'action_belt_in_home_protection';
  static const String actionBeltHealthServicesIn = 'action_belt_in_health_services';

  // Games gamification VN
  static const String gamesGamificationUserAuthorizationVn = 'f_113278_gamification_user_authorization';

  /// Dynamic contact us page
  static const String dynamicContactUsPage = 'us_57284_dynamic_contact_us_page';
  static const String generalFeedbackFlow = 'us_64046_general_feedback_flow';
  static const String allowNativeRatingDialog = 'us_64046_allow_native_rating_dialog';
  static const String showAppFeedbackOnStart = 'us_64046_show_appfeedback_on_start';
  static const String offerBanner = 'f_62872_offer_banner';
  static const String showBnplOfferOnMyLoans = 'f_83595_show_bnpl_offer_on_my_loans';
  static const String referralCode = 'f_59190_referral_code';
  static const String showProductDescriptionBanner = 'us_77242_show_product_description_banner';
  static const String productionEnroll = 'production_enroll';
  static const String productionEnrollSetting = 'f_114006_production_enroll_settings';
  static const String removeProductIntro = 'us_126956_remove_product_intro';

  static const String calculatorAmountHideButton = 'f_67134_calculator_amount_hide_buttons';
  static const String directFlow = 'f_98841_direct_flow';
  static const String agentCode = 'us_78751_enable_sa_code';

  static const String mockedFlows = 'f_80695_mocked_flows';

  static const String addressScreen = 'ff_77728_new_address_verification';

  // Additional Documents
  static const String socialInsurance = 'us_78987__acl_social_insurance';

  // Blur screen on backgroung
  static const String blurScreen = 'us_73572_secure_background';

  // Pos Selection
  static const String posFiltering = 'us_83811_pos_filtering';
  static const String posRemoveBookAppointment = 'us_83847_remove_book_appointment';

  static const String languageSelectionScreen = 'us_80760_language_selection';

  // Onboarding (using BOS)
  static const String onboardingV4Bos = 'f_85614_onboarding_v4';

  // bundled tnc
  static const String bundledTnc = 'f_121128_bundled_tnc';

  // bundled tnc marketing
  static const String bundledTncMarketing = 'f_144874_bundled_tnc_marketing';

  // Retrieve account (using BOS)
  static const String retrieveAccountBos = 'f_81456_retrieve_account_bos';

  static const String filterPosWard = 'us_92078_filter_pos_ward';

  // VN calculator popup on CLX
  static const String popupCalculatorCLX = 'ff_93603_popup_calc_clx';
  // VN calculator popup on CC
  static const String popupCalculatorCc = 'ff_103674_popup_calc_cc';

  // PH calculator popup on CLX
  static const String popupCalculatorCLXPh = 'us_166379_popup_calc_clx';

  //VN promo code on calculator
  static const String showPromoCode = 'ff_98642_promo_code';
  //VN promo code on calculator for credit card
  static const String showPromoCodeCc = 'ff_103748_promo_code_cc';

  // general feedback dialog submit & rate option
  static const String generalFeedbackRateApp = 'us_94148_user_choice_to_provide_app_rating';

  // livechat button for feedbacks
  static const String generalFeedbackLivechatButton = 'us_125783_live_chat_button_general_feedback';
  static const String satisfactionFeedbackLivechatButton = 'us_125784_live_chat_button_satisfaction_feedback';

  static const String cashLoanAbTestingDifferentOfferMix = 'epic_89273_cash_loan_ab_testing_different_offer_mix';

  /// loans VN
  static const String loansVNDeepLinkCashCard = 'f_98093_deeplink_cash_card_action_belt';
  static const String referralCodeACL = 'f_97318_referral_code_acl_df';
  static const String loanVNWaitingScreen = 'f_98015_waiting_screen_bod0_bod2_approval_improvement';
  static const String loanVNImproveSearchBank = 'f_138082_improve_search_logic_of_bank_name';
  static const String loanVNScoringLiveActivities = '153387_scoring_live_activities';
  static const String loanVNApplicationLiveActivities = '153388_application_live_activities';
  static const String loanVNNewAppFormUI = '174884_new_application_form_ui';
  static const String loanVNNewAccountVerificationUI = '174884_new_account_verification_form_ui';
  static const String loanVNCustomizedCCXScoringFlow = '180519_customized_ccx_scoring_flow';

  // Terms and conditions
  static const String newTermsAndConditions = 'f_92539_new_tnc';

  static const String accountGameRewardsWidget = 'us_87386_game_rewards_entry_point_account';
  static const String displayOfferBannerAtRepaymentSuccessfulScreen =
      'f_82832_display_offer_banner_at_repayment_successful_screen';

  // Replace logout with lock account
  static const String replaceLogoutWithLock = 'f_112190_replace_logout_with_lock_account_menu';

  // TW scoring new journey
  static const String removePopupSaConfirmationTW = 'us_132954__remove_sa_confirmation_for_tw';

  static const String hplVietQr = 'us_114245_hpl_vietqr';
  static const String paymentQrHplOnboardingCta = 'us_120011_payment_qr_hpl_onboarding_cta';

  static const String posAPIV2 = 'us_114424_switch_version_of_api_get_pos';
  static const String welcomeCashOffer = 'f_116129_ab_welcome_screen_product_offer';
  static const String welcomeScoringOffer = 'f_116129_ab_welcome_screen_scoring_offer';
  static const String welcomeIdCard = '163498_welcome_screen_for_id_card_update';
  static const String welcomeSigning = '175636_signing_ws';
  static const String welcomeCreditCardOffer = '179458_ccx_product_offer_ws';
  static const String welcomeCashAndCreditCardOffer = '179458_ab_clx_ccx_product_offer_ws';
  static const String welcomeCashAndCreditCardScoringOffer = '179850_ab_ccx_clx_scoring_ws';
  static const String improveDisplayingWelcomeScreen = 'ff_144718_improve_logic_of_displaying_welcome_screen';
  static const String stopShowingWSDummyScoring = 'f_178223_stop_showing_ws_for_dummy_scoring';
  //improve_way_of_displaying_ir
  static const String loanImproveDisplayIR = 'f_126462_improve_way_of_displaying_ir';
  static const String loanImproveDisplayIRNote = 'f_126462_display_ir_note';

  //Application form
  static const String addEmploymentDetails = 'us_126961_add_employment_details';

  // Privacy settings
  // Controls only visibility of the entry point in the Account menu as well as privacy settings screen
  static const String privacyEntryPointPh = 'f_118954_privacy_settings_entry_point';
  // Controls visibility of Data processing toggle button
  static const String privacyDataProcessingEnabled = 'us_151172_privacy_settings_data_processing_enabled';
  // Controls visibility of Marketing Updates toggle button
  static const String privacyMarketUpdatesEnabled = 'us_151172_privacy_settings_marketing_updates_enabled';

  // Disable user settings
  static const String disableUserReasonId = 'us_132527_disable_user_id';

  // vas redesign for PH
  static const String vasRedesign = 'ff_131066_vas_redesign';

  // LTU New UI Components
  static const String ltuNewUIComponents = 'ff_120704_ltu_new_ui_components';

  //Gamification onboarding guidelines
  static const String vnGamification = 'e_99174_enable_vn_gamification';

  static const String newBpVasTexts = 'ff_143465_new_bp_vas_texts';
  static const String calculatorSummaryScreen = 'ff_145849_loo_calc_summary_screen';

  //CCX
  static const String simplifyCCXJourney = 'ff_144650_simplify_ccx_journey';

  static const String hcProtectEntryPoint = 'us_143349_hc_protect_entry_point';

  static const String actionBeltUpdateUserState = 'us_149447_action_belt_update_user_state';

  static const String vnChangeNationalId = 'f_148367_national_id_personal_details';
  static const String vnChangeNationalIdLongterm = 'f_145414_update_national_id_longterm';
  static const String documentsExpirationsInAppform = 'ff_40658_documents_expirations_in_appform';
  static const String documentsExpirationsInAccountVerification =
      '155320_documents_expirations_in_account_verification';
  static const String removeZeroPhoneNumberFormat = 'us_153200_remove_0_phone_number_format';

  static const String trxDetailsFeedbackEntryPoint = 'us_154237_success_transactions_details_feedback_entrypoint';

  static const String livechatAuthen = 'us_155834_enable_vn_livechat_authen_by_userid';

  static const String errorMessageIfInvalidNumber = 'f_159940_error_message_if_invalid_number';

  // Biometrics for Digital Payments
  static const String biometricDigitalPayments = 'us_158835_biometric_digital_payments';

  static const String backupPartnerForDisbursementMethod = '165816_backup_partner_for_disbursement_method';

  static const String enablePromosEntryPointInAccount = 'us_176489_enable_promos_entry_point_in_account';

  static const String vnWelcomeKitSurvey = 'e_163730_enable_vn_welcomekitsurvey';

  // 3in1 VAS
  static const String ccx3In1Vas = 'f_159415_3in1_vas_on_card';
  static const String newPosSelectionUiV2 = '171054_new_pos_selection_ui';

  // Big ticket USP
  static const String bigTicketUSP = '173354_revise_logic_to_show_big_ticket_usp';

  //Loan Documents Password Protection (CEL & REL)
  static const String loanDocumentsPasswordProtectionPh = 'us_172133_loan_documents_password_protection';

  // Clx calculator amount input field for PH
  static const String calculatorAmountInputField = 'us_161273_cash_loan_amount_input_field';

  static const String minIncomeValidation = '178356_min_income_validation';

  static const String skipProductIntro = '180511_ab_skip_clx_intro';
}

class FeatureFlagValueConstants {
  static const String phoneNumberHintDialogTypeMobileNumberPackage = 'mobileNumberPackage';
  static const String phoneNumberHintDialogTypeGoogleCredentialsApi = 'googleCredentialsApi';
}

class DeeplinkConstants {
  static const deeplinkCategoryId = 'categoryID';
  static const deeplinkCategoryKey = 'categoryKey';
  static const deeplinkElementId = 'elementId';
  static const deeplinkIsFromBanner = 'isFromBanner';
  static const deeplinkContractType = 'contractType';
  static const deeplinkContractNumber = 'contractNumber';
  static const action = 'action';
  static const documentId = 'document';
  static const deeplinkApplicationCode = 'ApplicationCode';
  static const deeplinkProductType = 'ProductType';
  static const deeplinkProductTypecamel = 'productType';
  static const deeplinkJourneyType = 'journeyType';
  static const deeplinkReferenceNo = 'refNum';
  static const deeplinkPaymentOptionType = 'paymentOptionType';
}
