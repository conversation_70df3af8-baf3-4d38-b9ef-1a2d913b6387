import 'package:collection/collection.dart';
import 'package:flagsmith/flagsmith.dart';

class IFeatureFlagSeed {
  List<Flag> get defaultValues => [];

  List<Flag> mergeDefaultWithSpecific(List<Flag> defaultValues, List<Flag> specific) {
    final defaultWoSpecific = defaultValues
        .where((element) => specific.firstWhereOrNull((s) => s.feature.name == element.feature.name) == null);
    return [
      ...defaultWoSpecific,
      ...specific,
    ];
  }
}
