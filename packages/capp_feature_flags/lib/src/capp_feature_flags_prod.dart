import 'package:flagsmith/flagsmith.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

import 'capp_feature_flags_base.dart';
import 'capp_feature_flags_settings.dart';

class CappFeatureFlagsProd extends CappFeatureFlagsBase {
  final CappFeatureFlagsSettings _settings;
  CappFeatureFlagsProd({required CappFeatureFlagsSettings settings})
      : _settings = settings,
        super(settings: settings);

  @override
  void registerClient(GetIt c) {
    c
      ..registerSingleton<FlagsmithConfig>(
        FlagsmithConfig(
          baseURI: _settings.flagsmithUrl,
          baseStageURI: _settings.flagsmithStageUrl,
          basePerformanceURI: _settings.flagsmithPerformanceUrl,
          storeType: StoreType.persistant,
          isDebug: kDebugMode,
          isSelfSigned: true,
          password: _settings.encryptionPassword,
          caches: true,
          initHive: false,
          hideDisabledFlags: true,
        ),
      )
      ..registerSingleton<FlagsmithClient>(
        FlagsmithClient(
          apiKey: _settings.flagsmithApiKey,
          config: c<FlagsmithConfig>(),
          seeds: _settings.seeds,
        ),
      );
  }
}
