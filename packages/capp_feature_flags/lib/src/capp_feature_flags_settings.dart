import 'package:flagsmith/flagsmith.dart';

abstract class CappFeatureFlagsSettings {
  final String flagsmithUrl;
  final String? flagsmithStageUrl;
  final String? flagsmithPerformanceUrl;
  final String flagsmithApiKey;
  final String encryptionPassword;
  final List<Flag> seeds;

  CappFeatureFlagsSettings({
    required this.flagsmithUrl,
    required this.flagsmithStageUrl,
    required this.flagsmithPerformanceUrl,
    required this.flagsmithApiKey,
    required this.encryptionPassword,
    required this.seeds,
  });
}

abstract class CappFeatureFlagsFakeSettings extends CappFeatureFlagsSettings {
  final List<Flag> loggedInSeeds;

  CappFeatureFlagsFakeSettings({
    required this.loggedInSeeds,
    required super.flagsmithUrl,
    required super.flagsmithStageUrl,
    required super.flagsmithPerformanceUrl,
    required super.flagsmithApiKey,
    required super.encryptionPassword,
    required super.seeds,
  });
}
