import 'package:flagsmith/flagsmith.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

import 'capp_feature_flags_settings.dart';
import 'constants.dart';

abstract class CappFeatureFlagsBase extends ChildPackage {
  @override
  List<KoyalRoute> get routes => [];

  final CappFeatureFlagsSettings settings;
  CappFeatureFlagsBase({
    required this.settings,
  });
  @override
  Future<void> preRegisterDependencies(GetIt container) {
    registerSeed(container);
    registerClient(container);
    return Future.value();
  }

  @override
  void registerDependencies(GetIt container) {
    registerRepository(container);

    container.registerTrackingLazySingleton(
      () => FeatureFlagBloc(
        featureFlagRepository: container<IFeatureFlagRepository>(),
        appTraitValue: appTraitValue,
        featureFlagInitService: container<IFeatureFlagInitService>(),
      ),
    );
  }

  void registerSeed(GetIt c) {}
  void registerClient(GetIt c) {}

  void registerRepository(GetIt c) {
    c
      ..registerLazySingleton<IFeatureFlagRepository>(
        () => FeatureFlagRepository(
          client: c<FlagsmithClient>(),
          logger: c<Logger>(),
          currentUserRepository: c<ICurrentUserRepository>(),
        ),
      )
      ..registerLazySingleton<IFeatureFlagOverridesRepository>(
        () => FeatureFlagOverridesRepository(
          featureFlagsApi: c<FeatureFlagsApi>(),
          currentUserRepository: c<ICurrentUserRepository>(),
        ),
      );
  }
}
