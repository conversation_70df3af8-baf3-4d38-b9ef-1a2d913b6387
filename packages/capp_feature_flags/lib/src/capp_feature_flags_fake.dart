import 'package:flagsmith/flagsmith.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../capp_feature_flags.dart';

class CappFeatureFlagsFake extends CappFeatureFlagsBase {
  final CappFeatureFlagsFakeSettings _settings;

  CappFeatureFlagsFake({required CappFeatureFlagsFakeSettings settings})
      : _settings = settings,
        super(settings: settings);

  @override
  void registerClient(GetIt c) {
    c
      ..registerSingleton<FlagsmithConfig>(
        const FlagsmithConfig(),
      )
      ..registerSingleton<FlagsmithClient>(
        FakeFlagsmithClient(
          featureFlagSeeds: _settings.seeds,
          fakeLoggedInSeeds: _settings.loggedInSeeds,
        ),
      );
  }
}
