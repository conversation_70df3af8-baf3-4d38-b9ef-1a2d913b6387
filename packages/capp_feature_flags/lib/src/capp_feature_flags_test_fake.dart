import 'package:flagsmith/flagsmith.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:koyal_testing/koyal_testing.dart';
import 'package:logger/logger.dart';

import '../capp_feature_flags.dart';

class CappFeatureFlagsTestFake extends CappFeatureFlagsFake {
  CappFeatureFlagsTestFake({required CappFeatureFlagsFakeSettings settings}) : super(settings: settings);

  @override
  void registerRepository(GetIt c) {
    c.registerLazySingleton<IFeatureFlagRepository>(
      () => TestFeatureFlagRepository(
        client: c<FlagsmithClient>(),
        logger: c<Logger>(),
        currentUserRepository: c<ICurrentUserRepository>(),
        featureFlagOverrides: c.isRegistered<List<Flag>>(instanceName: featureFlagOverridesForTestInstanceName)
            ? c<List<Flag>>(instanceName: featureFlagOverridesForTestInstanceName)
            : [],
      ),
    );
  }
}
