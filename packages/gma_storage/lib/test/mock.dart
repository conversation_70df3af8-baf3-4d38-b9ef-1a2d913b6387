import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage_platform_interface/flutter_secure_storage_platform_interface.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';

const String kTemporaryPath = 'temporaryPath';
const String kApplicationSupportPath = 'applicationSupportPath';
const String kDownloadsPath = 'downloadsPath';
const String kLibraryPath = 'libraryPath';
const String kApplicationDocumentsPath = 'applicationDocumentsPath';
const String kExternalCachePath = 'externalCachePath';
const String kExternalStoragePath = 'externalStoragePath';
void setupStorageMocks() {
  if (!kIsWeb) {
    PathProviderPlatform.instance = _FakePathProviderPlatform();
  }

  FlutterSecureStoragePlatform.instance = _FakeFlutterSecureStoragePlatform();
}

class _FakePathProviderPlatform extends PathProviderPlatform {
  final String _tempPath = path.join('.dart_tool', 'fake_root');

  @override
  Future<String?> getTemporaryPath() async {
    return path.join(_tempPath, kTemporaryPath);
  }

  @override
  Future<String?> getApplicationSupportPath() async {
    return path.join(_tempPath, kApplicationSupportPath);
  }

  @override
  Future<String?> getLibraryPath() async {
    return path.join(_tempPath, kLibraryPath);
  }

  @override
  Future<String?> getApplicationDocumentsPath() async {
    return path.join(_tempPath, kApplicationDocumentsPath);
  }

  @override
  Future<String?> getExternalStoragePath() async {
    return path.join(_tempPath, kExternalStoragePath);
  }

  @override
  Future<List<String>?> getExternalCachePaths() async {
    return <String>[path.join(_tempPath, kExternalCachePath)];
  }

  @override
  Future<List<String>?> getExternalStoragePaths({
    StorageDirectory? type,
  }) async {
    return <String>[path.join(_tempPath, kExternalStoragePath)];
  }

  @override
  Future<String?> getDownloadsPath() async {
    return path.join(_tempPath, kDownloadsPath);
  }
}

class _FakeFlutterSecureStoragePlatform extends FlutterSecureStoragePlatform {
  final Map<String, String?> _data = <String, String?>{};
  @override
  Future<bool> containsKey({
    required String key,
    required Map<String, String> options,
  }) {
    return Future.value(_data.containsKey(key));
  }

  @override
  Future<void> delete({
    required String key,
    required Map<String, String> options,
  }) {
    _data.remove(key);
    return Future<void>.value();
  }

  @override
  Future<void> deleteAll({required Map<String, String> options}) {
    _data.clear();
    return Future<void>.value();
  }

  @override
  Future<String?> read({
    required String key,
    required Map<String, String> options,
  }) {
    return Future<String?>.value(_data[key]);
  }

  @override
  Future<Map<String, String>> readAll({required Map<String, String> options}) => Future.value(<String, String>{});

  @override
  Future<void> write({
    required String key,
    required String value,
    required Map<String, String> options,
  }) {
    _data[key] = value;
    return Future<void>.value();
  }
}
