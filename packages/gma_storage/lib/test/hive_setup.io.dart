import 'dart:io';

import 'package:hive_ce_flutter/hive_flutter.dart';

import 'package:path/path.dart' as path;

import 'hive_setup.dart';

Future<HiveTest> getHiveTestInstance() => HiveTestIo.initialze();

class HiveTestIo extends HiveTest {
  HiveTestIo._(this.currentFolder);
  static Future<Directory> getTempDir() async {
    final tempDir = await Directory.systemTemp.createTemp();
    final tempPath = path.join(tempDir.path, '.unit_test');
    final dir = Directory(path.join(tempPath, 'test_${DateTime.now().millisecondsSinceEpoch}'));
    if (dir.existsSync()) {
      dir.deleteSync(recursive: true);
    }
    dir.createSync(recursive: true);
    return dir;
  }

  static Future<HiveTestIo> initialze() async {
    final tempFolder = await getTempDir();
    return HiveTestIo._(tempFolder);
  }

  Directory currentFolder;

  @override
  Future<void> setUp() async {
    Hive.init(currentFolder.path);
    return Future.value();
  }

  /// Deletes the temporary [Hive].
  @override
  Future<void> tearDown() async {
    await Hive.deleteFromDisk();
  }
}
