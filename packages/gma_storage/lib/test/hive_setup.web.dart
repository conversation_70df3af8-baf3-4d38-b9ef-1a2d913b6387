import 'package:hive_ce_flutter/hive_flutter.dart';

import 'hive_setup.dart';

Future<HiveTest> getHiveTestInstance() => HiveTestWeb.initialze();

class HiveTestWeb extends HiveTest {
  HiveTestWeb._();
  static Future<HiveTestWeb> initialze() async {
    return HiveTestWeb._();
  }

  @override
  Future<void> setUp() async {
    await Hive.initFlutter('.hive_test');
  }

  /// Deletes the temporary [Hive].
  @override
  Future<void> tearDown() async {
    await Hive.close();
  }
}
