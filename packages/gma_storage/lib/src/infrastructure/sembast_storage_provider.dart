import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sembast/sembast.dart';
import 'package:sembast/sembast_io.dart';
import 'package:sembast_web/sembast_web.dart';

import '../domain/storage_exceptions.dart';
import '../domain/storage_provider_base.dart';

class SembastStorageProvider extends StorageProviderBase {
  final String dbPath;
  late Database _db;
  late StoreRef<String, dynamic> _store;

  SembastStorageProvider({
    this.dbPath = 'common-data.db',
    required super.logger,
  });

  Future<List<String>> getAllKeys() async {
    return _store.findKeys(_db);
  }

  Future<List<RecordSnapshot<String, dynamic>>> getSnapshots() async {
    return _store.find(_db);
  }

  @override
  Future<void> delete(String name) async {
    try {
      final recordExists = await _store.record(name).exists(_db);
      if (recordExists) {
        await _store.record(name).delete(_db);
      } else {
        logger.e('Record with key $name not found');
        throw StorageRecordNotFoundException(name);
      }
    } on Exception catch (e, s) {
      logger.e('Record with key $name not deleted', e, s);
      throw StorageRecordNotDeletedException(name, e);
    }
  }

  @override
  Future<T?> get<T>(String name) async {
    final recordExists = await _store.record(name).exists(_db);
    if (!recordExists) {
      return null;
    }
    final item = await _store.record(name).get(_db) as T;
    return item;
  }

  @override
  Future<void> insert<T>(String name, T item) async {
    try {
      final recordExists = await _store.record(name).exists(_db);
      if (recordExists) {
        await _store.record(name).update(_db, item);
        return;
      }
      await _store.record(name).put(_db, item);
    } on Exception catch (e, s) {
      logger.e('Record with key $name not saved', e, s);
      throw StorageRecordNotSavedException(name, e);
    }
  }

  @override
  Future<void> init() async {
    final path = await _getDatabasePath();
    if (kIsWeb) {
      _db = await databaseFactoryWeb.openDatabase(path);
      _store = stringMapStoreFactory.store();

      return;
    }
    _db = await databaseFactoryIo.openDatabase(path);
    _store = stringMapStoreFactory.store();
  }

  @override
  Future<void> clearStorage() async {
    await init();
    // Delete all the record, deleting _dp resolve in error due to async acces to db
    final deleted = await _store.delete(_db);
    logger.i('Cleared whole sembast storage #$deleted');
  }

  Future<String> _getDatabasePath() async {
    var path = '';
    if (kIsWeb) {
      path = dbPath;
    } else {
      final dir = await getApplicationDocumentsDirectory();
      await dir.create(recursive: true);
      path = join(dir.path, dbPath);
    }
    return path;
  }
}
