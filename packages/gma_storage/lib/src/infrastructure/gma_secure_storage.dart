import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_ce/hive.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';

import '../core/constants.dart';
import '../core/gma_crypto_pass_generator.dart';
import '../domain/i_gma_secure_storage.dart';

class GmaSecureStorage extends IGmaSecureStorage {
  GmaSecureStorage._() : _storage = constructFlutterSecureStorage();

  static FlutterSecureStorage constructFlutterSecureStorage() => const FlutterSecureStorage(
        aOptions: AndroidOptions(
          encryptedSharedPreferences: true,
        ),
        iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock),
      );

  static Future<IGmaSecureStorage> asyncInit() async {
    final instance = GmaSecureStorage._();
    await instance._init();
    return instance;
  }

  Future<void> _init() async {
    await storePath();
    await cipher();
  }

  late final FlutterSecureStorage _storage;
  static const String key = 'device_key/first_unlock';

  HiveAesCipher? _cipher;
  Uint8List? _encKey;
  String? _path;

  Future<Uint8List> encryptionKey() async {
    var encryptionKey = await _storage.read(key: key);
    if (encryptionKey == null) {
      encryptionKey = CryptoPassGenerator.createCryptoRandomString();
      await _storage.write(
        key: key,
        value: encryptionKey,
      );
    }
    return encryptionKey.toEncryptionKey();
  }

  Future<String> encryptionStringKey() async {
    final savedKey = _encKey ??= await encryptionKey();
    return savedKey.toEncryptionString();
  }

  @override
  Future<String> storePath() async {
    var path = '';
    if (kIsWeb) {
      path = _path ??= '';
    } else {
      final dir = await getApplicationDocumentsDirectory();
      await dir.create(recursive: true);
      path = _path ??= p.join(dir.path, storageFolder);
    }

    return path;
  }

  @override
  Future<HiveAesCipher> cipher() async {
    return _cipher ??= HiveAesCipher(await encryptionKey());
  }

  @override
  Future<bool> isMigrationNeeded() async {
    final result = await _storage.read(key: migrationKey);
    return result == null;
  }

  @override
  Future<void> migrationFinished() async {
    await _storage.write(key: migrationKey, value: 'done');
  }

  @override
  Future<void> migrationEnableTest() async {
    await _storage.delete(key: migrationKey);
  }
}

class FakeGmaSecureStorage extends IGmaSecureStorage {
  FakeGmaSecureStorage();
  FakeGmaSecureStorage._();
  static Future<IGmaSecureStorage> asyncInit() async {
    final instance = FakeGmaSecureStorage._();
    await instance._init();
    return instance;
  }

  final Map<String, String?> values = {};
  Uint8List? _encKey;
  HiveAesCipher? _cipher;

  Future<void> _init() async {
    await storePath();
    await cipher();
  }

  @override
  Future<HiveAesCipher?> cipher({bool init = false}) {
    final password = CryptoPassGenerator.createCryptoRandomString();
    final encKey = _encKey ??= password.toEncryptionKey();
    return Future.value(_cipher ??= HiveAesCipher(encKey));
  }

  @override
  Future<String?> storePath() => Future.value();

  @override
  Future<bool> isMigrationNeeded() {
    return Future.value(!values.containsKey(migrationKey));
  }

  @override
  Future<void> migrationFinished() {
    values[migrationKey] = 'done';
    return Future.value();
  }

  @override
  Future<void> migrationEnableTest() {
    if (values.containsKey(migrationKey)) {
      values.remove(migrationKey);
    }
    return Future.value();
  }
}

extension Uint8ListStringConverter on Uint8List {
  String toEncryptionString() => base64Url.encode(this);
}

extension StringUint8ListConverter on String {
  Uint8List toEncryptionKey() => base64Url.decode(this);
}
