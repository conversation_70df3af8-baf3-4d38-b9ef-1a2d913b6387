import 'package:rxdart/rxdart.dart';

import '../domain/storage_item_base.dart';
import '../domain/storage_provider_base.dart';

mixin RxBehaviorStorage on GmaStorageProvider {
  final Map<String, BehaviorSubject?> subjects = <String, BehaviorSubject?>{};
  void putSubject<T>(String name, dynamic item) {
    if (subjects[name] == null) {
      subjects[name] = BehaviorSubject<T?>.seeded(item as T);
    } else {
      subjects[name]?.add(item);
    }
  }

  void deleteSubject(String name) {
    if (subjects.contains<PERSON>ey(name)) {
      subjects[name]?.add(null);
      subjects[name]?.close();
      subjects.remove(name);
    }
  }

  Future<void> clearSubjects() async {
    for (final item in subjects.entries) {
      item.value?.add(null);
      await item.value?.close();
    }
    subjects.clear();
  }

  @override
  Future<BehaviorSubject<T?>> getBehaviorSubject<T>(String name, {ItemCreator? fromMap}) async {
    if (!subjects.contains<PERSON><PERSON>(name)) {
      final item = await get<T>(name, fromMap: fromMap);
      if (!subjects.contains<PERSON>ey(name)) {
        subjects[name] = BehaviorSubject<T?>.seeded(item);
      }
    }
    return subjects[name]! as BehaviorSubject<T?>;
  }

  @override
  Future<ValueStream<T?>> getStream<T>(String name, {ItemCreator? fromMap}) async {
    if (!subjects.containsKey(name)) {
      final item = await get<T>(name, fromMap: fromMap);
      if (!subjects.containsKey(name)) {
        subjects[name] = BehaviorSubject<T?>.seeded(item);
      }
    }
    return subjects[name]!.stream as ValueStream<T?>;
  }
}
