// ignore_for_file: no_runtimetype_tostring

import 'dart:async';

import 'package:hive_ce/hive.dart';
import 'package:logger/logger.dart';

import '../../gma_storage.dart';
import '../core/benchmark.dart';
import 'rx_behavior_mixin.dart';

class SessionInMemoryStorageProvider extends ReactiveInMemoryStorageProvider {
  SessionInMemoryStorageProvider({required super.logger});
}

class PersistantInMemoryStorageProvider extends ReactiveInMemoryStorageProvider {
  PersistantInMemoryStorageProvider({required super.logger});
}

class SecureInMemoryStorageProvider extends ReactiveInMemoryStorageProvider {
  SecureInMemoryStorageProvider({required super.logger});
}

class ReactiveInMemoryStorageProvider extends GmaStorageProvider with RxBehaviorStorage, BenchmarkMixin {
  final JsonMap _items = <String, dynamic>{};
  final StreamController<BoxEvent> _streamController = StreamController.broadcast();
  late final Logger? customLogger;

  ReactiveInMemoryStorageProvider({required super.logger})
      : super(boxName: 'RISP', secureStorage: FakeGmaSecureStorage()) {
    customLogger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 5,
      ),
    );
    initBenchmark('reactive_in_memory_storage', currentLogger);
  }
  Logger get currentLogger => customLogger ?? logger;
  @override
  Future<void> delete(String name) async {
    final benchmark = addDeleteBenchmark(name);
    _items.remove(name);

    deleteSubject(name);
    _streamController.add(BoxEvent(name, null, true));
    benchmark?.stop();
  }

  @override
  Future<T?> get<T>(String name, {ItemCreator? fromMap}) async {
    final benchmark = addGetBenchmark<T>(name);
    if (_items.containsKey(name)) {
      final dynamic json = _items[name];
      if (json is Map && fromMap != null) {
        final jsonMap = json as Map<String, dynamic>;
        benchmark?.stop();
        return fromMap(jsonMap) as T?;
      }
      benchmark?.stop();
      return json as T?;
    }
    benchmark?.stop();
    return Future.value();
  }

  @override
  Future<void> insert<T>(String name, T item) async {
    final benchmark = addInsertBenchmark<T>(name);
    if (item is StorageItemBase) {
      _items[name] = item.toMap();
    } else {
      _items[name] = item;
    }

    putSubject<T>(name, item);
    _streamController.add(BoxEvent(name, item, false));
    benchmark?.stop();
  }

  @override
  Future<void> insertAll(Map<String, Object> items) {
    final benchmark = addBenchmark('insert_all');
    final data = <String, dynamic>{};
    items.forEach((key, item) {
      final dynamic value;
      if (item is StorageItemBase) {
        value = item.toMap();
      } else {
        value = item;
      }
      data[key] = value;
      putSubject<dynamic>(key, item);
    });
    benchmark?.stop();
    return Future.value();
  }

  @override
  Future<void> open({String? parentName}) async {
    final benchmark = addInitBenchmark(parentName: parentName);
    benchmark?.stop();
    return;
  }

  @override
  Future<void> clearStorage() async {
    final benchmark = addClearBenchmark();
    _items.clear();
    await clearSubjects();
    benchmark?.stop();
  }

  @override
  Stream<T?> watch<T>({String? key}) {
    if (key != null) {
      return _streamController.stream.where((it) => it.key == key).map((e) {
        if (e.deleted) {
          return null;
        }
        return e.value as T?;
      });
    } else {
      return _streamController.stream.map((e) {
        if (e.deleted) {
          return null;
        }
        return e.value as T?;
      });
    }
  }

  @override
  Future<void> close() {
    _streamController.close();
    return Future.value();
  }

  @override
  Future<Map<String, dynamic>?> getMap(String name) async {
    final data = get<Map<String, dynamic>>(name);
    return data;
  }

  @override
  Future<List<String>> getKeys() async {
    return Future.value(_items.keys.toList());
  }
}
