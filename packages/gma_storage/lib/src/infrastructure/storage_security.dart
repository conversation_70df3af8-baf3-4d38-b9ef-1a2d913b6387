import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

class StorageSecurity {
  final _encryptPrefix = 'encrypt::';
  final _random = Random.secure();
  final String password;
  late Uint8List _encryptedPassword;
  late Encrypter _enc;

  StorageSecurity(this.password) {
    _encryptedPassword = _generateEncryptPassword(password);
    _enc = Encrypter(Salsa20(Key(_encryptedPassword)));
  }

  Uint8List _generateEncryptPassword(String password) {
    final blob = Uint8List.fromList(md5.convert(utf8.encode(password)).bytes);
    assert(blob.length == 16, 'length must be 16');
    return blob;
  }

  Uint8List _randBytes(int length) {
    return Uint8List.fromList(List<int>.generate(length, (i) => _random.nextInt(256)));
  }

  String encrypt(String value) {
    final iv = _randBytes(8);
    final ivEncoded = base64.encode(iv);
    assert(ivEncoded.length == 12, 'length must be 12');
    final encoded = _enc.encrypt(value, iv: IV(iv)).base64;
    return '$_encryptPrefix$ivEncoded$encoded';
  }

  String decrypt(String value) {
    var locValue = value;
    if (!locValue.startsWith(_encryptPrefix)) return locValue;
    locValue = locValue.substring(_encryptPrefix.length);
    final iv = base64.decode(locValue.substring(0, 12));
    locValue = locValue.substring(12);
    return _enc.decrypt64(locValue, iv: IV(iv));
  }

  bool isEncrypted(String value) => value.startsWith(_encryptPrefix);
}
