import 'dart:convert';

import 'package:gma_platform/gma_platform.dart';

import '../../gma_storage.dart';

class SecureStorageProvider extends StorageProviderBase {
  SecureStorageProvider({
    required this.storageProvider,
    required this.platformService,
    required super.logger,
  });
  final IPlatformService platformService;
  final StorageProviderBase storageProvider;
  late StorageSecurity _storageSecurity;

  @override
  Future<void> init() async {
    final deviceId = await platformService.deviceId();
    assert(deviceId.isNotEmpty, 'device id must not be empty');
    _storageSecurity = StorageSecurity(deviceId);
    return storageProvider.init();
  }

  @override
  Future<void> clearStorage() {
    return storageProvider.clearStorage();
  }

  @override
  Future<T?> get<T>(String name, {ItemCreator? fromMap}) async {
    dynamic json = await storageProvider.get<dynamic>(name);
    if (json is String) {
      if (_storageSecurity.isEncrypted(json)) {
        final decrypted = _storageSecurity.decrypt(json);
        try {
          json = jsonDecode(decrypted);
        } on FormatException catch (e) {
          logger.wtf(
            'Unable to parse json in secure storage provider ($name), ${e.message}. Deleting the item and returning null.',
          );
          await delete(name);
          return null;
        }
      }
    }
    if (json is Map && fromMap != null) {
      final jsonMap = json as Map<String, dynamic>;
      return fromMap(jsonMap) as T?;
    }
    final item = json as T?;
    return item;
  }

  @override
  Future<void> insert<T>(String name, T item) async {
    String stringifiedItem;
    if (item is StorageItemBase) {
      stringifiedItem = jsonEncode(item.toMap());
    } else {
      stringifiedItem = jsonEncode(item);
    }
    final encryptedItem = _storageSecurity.encrypt(stringifiedItem);
    return storageProvider.insert(name, encryptedItem);
  }

  @override
  Future<void> delete(String name) {
    return storageProvider.delete(name);
  }

  Future<Map<String, dynamic>> getItems() async {
    final data = <String, dynamic>{};
    if (storageProvider is SembastStorageProvider) {
      final keys = await (storageProvider as SembastStorageProvider).getAllKeys();
      for (final key in keys) {
        data[key] = await get<dynamic>(key);
      }
    }
    return Future.value(data);
  }
}
