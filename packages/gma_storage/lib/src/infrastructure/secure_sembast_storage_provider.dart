import 'package:gma_platform/gma_platform.dart';
import 'package:logger/logger.dart';

import 'secure_storage_provider.dart';
import 'sembast_storage_provider.dart';

class SecureSembastStorageProvider extends SembastStorageProvider {
  SecureSembastStorageProvider({
    required SembastStorageProvider sembastProvider,
    required IPlatformService platformService,
    required Logger logger,
  }) : super(logger: sembastProvider.logger) {
    _secureProvider = SecureStorageProvider(
      storageProvider: sembastProvider,
      platformService: platformService,
      logger: logger,
    );
  }
  late SecureStorageProvider _secureProvider;
  SecureStorageProvider get provider => _secureProvider;

  @override
  Future<void> delete(String name) async {
    return _secureProvider.delete(name);
  }

  @override
  Future<T?> get<T>(String name) async {
    return _secureProvider.get<T>(name);
  }

  @override
  Future<void> insert<T>(String name, T item) async {
    return _secureProvider.insert<T>(name, item);
  }

  @override
  Future<void> init() async {
    return _secureProvider.init();
  }

  @override
  Future<void> clearStorage() async {
    return _secureProvider.clearStorage();
  }

  Future<Map<String, dynamic>> getItems() async {
    await _secureProvider.init();
    final items = await _secureProvider.getItems();
    return items;
  }
}
