import 'dart:convert';
import 'dart:io';

import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

// ignore: avoid_classes_with_only_static_members
class FileUtils {
  const FileUtils._();

  static Future<File> createFileFromString(
    String pdf,
    String fileName, {
    bool saveToTemporaryFolder = true,
    String? extension = '.pdf',
  }) async {
    return createFileFromListInt(
      base64.decode(pdf),
      fileName,
      saveToTemporaryFolder: saveToTemporaryFolder,
      extension: extension,
    );
  }

  static Future<File> createFileFromListInt(
    List<int> pdf,
    String fileName, {
    bool saveToTemporaryFolder = true,
    String? extension = '.pdf',
  }) async {
    final dir =
        (GmaPlatform.isWeb) ? '' : (await getDownloadDirectory(getTemporaryFolder: saveToTemporaryFolder))!.path;
    String newFileName;
    if (fileName.endsWith(extension!)) {
      newFileName = basenameWithoutExtension(fileName);
    } else {
      newFileName = basenameWithoutExtension(fileName) + extension;
    }
    final path = '$dir/$newFileName';
    final file = File(path);

    if (file.existsSync()) {
      await file.delete();
    }
    await file.writeAsBytes(pdf, flush: true);
    return file;
  }

  static Future<Directory?> getDownloadDirectory({required bool getTemporaryFolder}) async {
    if (getTemporaryFolder) {
      final dir = await getTemporaryDirectory();
      final tempDirPath = '${dir.path}/temp';
      final tempDir = Directory(tempDirPath);
      await tempDir.create(recursive: true);
      return tempDir;
    }
    if (GmaPlatform.isAndroid) {
      return getExternalStorageDirectory();
    } else {
      return getApplicationDocumentsDirectory();
    }
  }

  static String getFileExtension(String path) {
    return path.substring(path.lastIndexOf('.'));
  }

  static Future<bool> saveFileAs({
    required Uint8List bytes,
    required String fileName,
    required String customMimeType,
    required String ext,
  }) async {
    try {
      final result = await FileSaver.instance.saveAs(
            name: fileName,
            bytes: bytes,
            ext: ext,
            mimeType: MimeType.custom,
            customMimeType: customMimeType,
          ) ??
          '';
      return result.isNotEmpty;
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  static Future<void> deleteFile({required File? file}) async {
    try {
      // Check if the file exists
      if (file != null && file.existsSync()) {
        await file.delete();
        debugPrint('File deleted successfully: ${file.path}');
      } else {
        debugPrint('File does not exist: ${file?.path}');
      }
    } catch (e) {
      debugPrint('Error deleting file: $e');
    }
  }
}
