import 'package:dartz/dartz.dart';
import 'package:flutter_document_picker/flutter_document_picker.dart';

import '../../../gma_storage.dart';

class FileRepository extends IFileRepository {
  @override
  Future<Either<Unit, String?>> getLocalFile({FlutterDocumentPickerParams? params}) async {
    try {
      return right(
        await FlutterDocumentPicker.openDocument(params: params),
      );
    } catch (_) {
      //invalid file selected
      return left(unit);
    }
  }
}
