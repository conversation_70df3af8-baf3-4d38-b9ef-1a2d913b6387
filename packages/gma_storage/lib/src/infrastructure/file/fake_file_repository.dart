import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:flutter_document_picker/flutter_document_picker.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

import '../../../gma_storage.dart';

class FakeFileRepository extends IFileRepository {
  static const String fakeDocumentUrl = 'https://files.in.hcgma.com/loan-origination/test-data/adhaar.zip';

  @override
  Future<Either<Unit, String?>> getLocalFile({required FlutterDocumentPickerParams params}) async {
    final extDir = await getTemporaryDirectory();
    final dirPath = '${extDir.path}/temp';
    await Directory(dirPath).create(recursive: true);
    final filePath = '$dirPath/${timestamp()}.${params.allowedFileExtensions!.first}';

    try {
      final url = Uri.parse(fakeDocumentUrl);
      final client = http.Client();
      final request = http.Request('GET', url);
      final response = await client.send(request);

      final file = File(filePath);
      await for (final data in response.stream) {
        await file.writeAsBytes(data, mode: FileMode.append);
      }
      client.close();
      return right(filePath);
    } on Exception {
      return left(unit);
    }
  }

  String timestamp() => DateTime.now().millisecondsSinceEpoch.toString();
}
