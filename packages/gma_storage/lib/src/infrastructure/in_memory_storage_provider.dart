// ignore_for_file: no_runtimetype_tostring

import 'dart:async';
import 'dart:convert';

import '../../gma_storage.dart';

class InMemoryStorageProvider extends StorageProviderBase {
  final JsonMap _items = <String, dynamic>{};
  InMemoryStorageProvider({required super.logger});
  @override
  Future<void> delete(String name) async {
    _items.remove(name);
  }

  @override
  Future<T?> get<T>(String name, {ItemCreator? fromMap}) async {
    if (_items.containsKey(name)) {
      dynamic json = _items[name];
      if (json is String) {
        try {
          json = jsonDecode(json);
        } on FormatException catch (e) {
          logger.wtf('Unable to parse json in secure storage provider ($name), ${e.message}');
        }
      }
      if (json is Map && fromMap != null) {
        final jsonMap = json as Map<String, dynamic>;
        return fromMap(jsonMap) as T?;
      }
      final item = json as T?;
      return item;
    }
    return null;
  }

  @override
  Future<void> insert<T>(String name, T item) async {
    String stringifiedItem;

    if (item is StorageItemBase) {
      stringifiedItem = jsonEncode(item.toMap());
    } else {
      stringifiedItem = jsonEncode(item);
    }

    _items[name] = stringifiedItem;
  }

  @override
  Future<void> init() async {
    return;
  }

  @override
  Future<void> clearStorage() async {
    _items.clear();
  }
}
