// ignore_for_file: no_runtimetype_tostring

import 'dart:async';

import 'package:hive_ce_flutter/hive_flutter.dart';

import 'package:logger/logger.dart';

import '../core/benchmark.dart';
import '../domain/i_gma_secure_storage.dart';
import '../domain/storage_exceptions.dart';
import '../domain/storage_item_base.dart';
import '../domain/storage_provider_base.dart';
import 'rx_behavior_mixin.dart';

enum GmaStorageType { persistant, secure }

/// This storage should be use only for user session releated data
/// all data will be destroyed on user logout.
class SessionHiveStorageProvider extends HiveStorageProvider {
  SessionHiveStorageProvider({
    required super.logger,
    required super.secureStorage,
  }) : super(boxName: 'gma_temporary_storage');
  static Future<SessionHiveStorageProvider> asyncInit({
    required Logger logger,
    required IGmaSecureStorage secureStorage,
  }) async {
    final storage = SessionHiveStorageProvider(logger: logger, secureStorage: secureStorage);
    await storage.open();
    return storage;
  }
}

/// This storage should be use only for app releated data
/// mostly stuff like app_state, permissions, logs, etc.
class PersistantHiveStorageProvider extends HiveStorageProvider {
  PersistantHiveStorageProvider({
    required super.logger,
    required super.secureStorage,
  }) : super(boxName: 'gma_persistant_storage');
  static Future<PersistantHiveStorageProvider> asyncInit({
    required Logger logger,
    required IGmaSecureStorage secureStorage,
  }) async {
    final storage = PersistantHiveStorageProvider(logger: logger, secureStorage: secureStorage);
    await storage.open();
    return storage;
  }
}

/// Secured storage should be use only for user data
/// Tokens, personal informations, etc.
class SecureHiveStorageProvider extends HiveStorageProvider {
  SecureHiveStorageProvider({
    required super.logger,
    required super.secureStorage,
  }) : super(boxName: 'gma_secure_storage');
  static Future<SecureHiveStorageProvider> asyncInit({
    required Logger logger,
    required IGmaSecureStorage secureStorage,
  }) async {
    final storage = SecureHiveStorageProvider(logger: logger, secureStorage: secureStorage);
    await storage.open();
    return storage;
  }
}

class HiveStorageProvider extends GmaStorageProvider with RxBehaviorStorage, BenchmarkMixin, GmaBenchmarkMixin {
  Box<dynamic>? box;
  late final Logger? customLogger;
  HiveStorageProvider({
    required super.boxName,
    required super.logger,
    required super.secureStorage,
  }) {
    customLogger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 5,
        noBoxingByDefault: true,
      ),
    );
    initBenchmark('$runtimeType#$hashCode', currentLogger);
  }

  @override
  Future<void> open({String? parentName}) async {
    final cipherBenchmark = addBenchmark('cipher');
    final cipher = await secureStorage.cipher();
    stopBenchmark(cipherBenchmark);

    final pathBenchmark = addBenchmark('path');
    final path = await secureStorage.storePath();
    stopBenchmark(pathBenchmark);

    final benchmark = addInitBenchmark(parentName: parentName);

    box ??= await Hive.openBox<dynamic>(
      boxName,
      encryptionCipher: cipher,
      path: path,
    );

    stopBenchmark(benchmark);
  }

  Logger get currentLogger => customLogger ?? logger;
  bool exists(String key) {
    final benchmark = addBenchmark('exists_$key');
    final value = box?.get(key) != null;
    stopBenchmark(benchmark);
    return value;
  }

  @override
  Stream<T?> watch<T>({String? key}) {
    if (box == null || !box!.isOpen) {
      throw StorageException(boxName, Exception('box $boxName is not open'));
    }

    return box!.watch(key: key).map((e) {
      if (e.deleted) {
        return null;
      }
      return e.value as T?;
    });
  }

  @override
  Future<void> delete(String name) async {
    final benchmark = addDeleteBenchmark(name);
    await box?.delete(name);
    if (exists(name)) {
      deleteSubject(name);
      stopBenchmark(benchmark);
    }
  }

  @override
  Future<T?> get<T>(String name, {ItemCreator? fromMap}) async {
    final benchmark = addGetBenchmark<T>(name);
    final dynamic data = box?.get(name);
    try {
      if (data == null) return null;
      if (data is Map) {
        final casted = data.cast<dynamic, dynamic>();
        final result =
            fromMap == null ? Map<String, dynamic>.from(casted) as T : fromMap(Map<String, dynamic>.from(casted)) as T;
        stopBenchmark(benchmark);
        return result;
      }
      final item = data as T?;
      stopBenchmark(benchmark);
      return item;
    } catch (e, s) {
      currentLogger.e('Record with key $name \n $data can`t get', e, s);
      stopBenchmark(benchmark);
      return null;
    }
  }

  @override
  Future<void> insert<T>(String name, T item) async {
    final benchmark = addInsertBenchmark<T>(name);

    final dynamic value;
    if (item is StorageItemBase) {
      value = item.toMap();
    } else {
      value = item;
    }

    await box?.put(name, value);
    stopBenchmark(benchmark);
    putSubject<T>(name, item);
  }

  @override
  Future<void> insertAll(Map<String, Object> items) async {
    final benchmark = addBenchmark('insert_all');
    final data = <String, dynamic>{};
    items.forEach((key, item) {
      final dynamic value;
      if (item is StorageItemBase) {
        value = item.toMap();
      } else {
        value = item;
      }
      data[key] = value;
      // putSubject<dynamic>(key, item);
    });
    await box?.putAll(data);
    stopBenchmark(benchmark);
  }

  @override
  Future<void> clearStorage() async {
    final benchmark = addClearBenchmark();
    await box?.clear();
    await clearSubjects();
    stopBenchmark(benchmark);
  }

  @override
  Future<void> close() async {
    if (Hive.isBoxOpen(boxName)) {
      await box?.close();
      box = null;
    }
    storageBenchmark.showResults();
  }

  @override
  void registerAdapter() {}

  @override
  Future<Map<String, dynamic>?> getMap(String name) async {
    return get<Map<String, dynamic>>(name);
  }

  @override
  Future<void> flush() async {
    await box?.flush();
  }

  @override
  Future<List<String>> getKeys() async {
    if (box == null) return Future.value(<String>[]);
    return Future.value(box!.keys.toList().map((dynamic e) => e as String).toList());
  }
}
