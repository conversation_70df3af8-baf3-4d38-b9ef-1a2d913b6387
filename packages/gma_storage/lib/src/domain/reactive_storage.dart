import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';

import '../infrastructure/in_memory_storage_provider.dart';
import 'storage_item_base.dart';
import 'storage_provider_base.dart';

class ReactiveStorage {
  final Map<String, BehaviorSubject?> _subjects = <String, BehaviorSubject?>{};
  bool _isInitialized = false;
  late StorageProviderBase _storageProvider;
  Logger logger;

  ReactiveStorage({
    StorageProviderBase? storageProvider,
    required this.logger,
  }) {
    _storageProvider = storageProvider ?? InMemoryStorageProvider(logger: logger);
  }
  Future<void> init() async {
    await _init();
  }

  Future<void> _init() async {
    logger.v('Initializing storage');
    if (!_isInitialized) {
      await _storageProvider.init();
      _isInitialized = true;
    }
  }

  Future<ValueStream<T?>> getStream<T>(String name, {ItemCreator? fromMap}) async {
    if (!_subjects.contains<PERSON><PERSON>(name)) {
      final item = await get<T>(name, fromMap: fromMap);
      // check again in case of race condition
      if (!_subjects.containsKey(name)) {
        _subjects[name] = BehaviorSubject<T?>.seeded(item);
      }
    }

    return _subjects[name]!.stream as ValueStream<T?>;
  }

  Future<BehaviorSubject<T?>> getBehaviorSubject<T>(String name, {ItemCreator? fromMap}) async {
    if (!_subjects.containsKey(name)) {
      final item = await get<T>(name, fromMap: fromMap);
      // check again in case of race condition
      if (!_subjects.containsKey(name)) {
        _subjects[name] = BehaviorSubject<T?>.seeded(item);
      }
    }

    return _subjects[name]! as BehaviorSubject<T?>;
  }

  Future<void> insert<T>(String name, T item) async {
    logger.v('Inserting item $name to the storage');

    await _init();

    if (item is StorageItemBase) {
      final locItem = item.toMap();
      await _storageProvider.insert(name, locItem);
    } else if (item is Map<String, dynamic>) {
      await _storageProvider.insert(name, item);
    } else {
      await _storageProvider.insert(name, item);
    }

    if (_subjects[name] == null) {
      _subjects[name] = BehaviorSubject<T?>.seeded(item);
    } else {
      _subjects[name]!.add(item);
    }
  }

  Future<void> delete(String name) async {
    logger.v('Deleting item $name from the storage');

    await _init();
    if (await get<dynamic>(name) != null) {
      await _storageProvider.delete(name);

      if (_subjects.containsKey(name)) {
        _subjects[name]!.add(null);
      }
    } else {
      logger.w('Item $name will not be deleted because it cannot be found');
    }
  }

  Future<T?> get<T>(String name, {ItemCreator? fromMap}) async {
    await _init();

    if (fromMap != null) {
      final item = await _storageProvider.get<Map<String, dynamic>>(name);

      if (item == null) {
        return null;
      }

      return fromMap(item) as T?;
    }

    final item = await _storageProvider.get<T>(name);

    return item;
  }

  Future<Map<String, dynamic>?> getMap(String name) {
    return _storageProvider.get<Map<String, dynamic>>(name);
  }
}
