import 'dart:async';

import '../core/benchmark.dart';
import 'storage_provider_base.dart';

class StorageProviderCleanerOriginal {
  final List<StorageProviderBase> _providers = [];

  void addProvider(StorageProviderBase provider) {
    _providers.add(provider);
  }

  Future cleanAll() async {
    await Future.wait<void>([
      ..._providers.whereType<SembastBenchmarkMixin>().map((e) => e.showAllResults()),
      ..._providers.map((e) => e.clearStorage()),
    ]);
  }
}

class StorageProviderCleaner {
  final List<GmaStorageProvider> _providers = [];

  void addProvider(GmaStorageProvider provider) {
    _providers.add(provider);
  }

  Future cleanAll() async {
    await Future.wait<void>([
      ..._providers.whereType<GmaBenchmarkMixin>().map((e) => e.showAllResults()),
      ..._providers.map((e) => e.clearStorage()),
    ]);
  }
}
