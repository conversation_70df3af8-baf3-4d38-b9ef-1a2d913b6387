class StorageException implements Exception {
  final String itemKey;
  final dynamic innerError;

  StorageException(this.itemKey, [this.innerError]);

  @override
  String toString() => 'Key: $itemKey ${innerError != null ? '\nInner error: $innerError' : ''}';
}

class StorageRecordNotFoundException extends StorageException {
  StorageRecordNotFoundException(String itemKey, [dynamic innerError]) : super(itemKey, innerError);
}

class StorageRecordNotDeletedException extends StorageException {
  StorageRecordNotDeletedException(String itemKey, [dynamic innerError]) : super(itemKey, innerError);
}

class StorageRecordNotSavedException extends StorageException {
  StorageRecordNotSavedException(String itemKey, [dynamic innerError]) : super(itemKey, innerError);
}
