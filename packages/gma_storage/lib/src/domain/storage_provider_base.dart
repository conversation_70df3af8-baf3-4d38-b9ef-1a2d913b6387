import 'dart:async';

import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';

import 'domain.dart';

abstract class StorageProviderBase {
  StorageProviderBase({required this.logger});
  final Logger logger;
  Future<void> insert<T>(String name, T item);
  Future<void> delete(String name);
  Future<T?> get<T>(String name);
  Future<void> init();
  Future<void> clearStorage();
}

abstract class ReactiveStorageProviderBase {
  Future<void> insert<T>(String name, T item);
  Future<void> delete(String name);
  Future<T?> get<T>(String name, {ItemCreator? fromMap});
  Future<void> open({String? parentName});
  Future<void> clearStorage();
}

abstract class GmaStorageProvider extends ReactiveStorageProviderBase {
  GmaStorageProvider({
    required this.boxName,
    required this.logger,
    required this.secureStorage,
  });
  final String boxName;
  final Logger logger;
  final IGmaSecureStorage secureStorage;
  Stream<T?> watch<T>({String? key});

  Future<void> insertAll(Map<String, Object> items);
  Future<ValueStream<T?>> getStream<T>(String name, {ItemCreator? fromMap});
  Future<BehaviorSubject<T?>> getBehaviorSubject<T>(String name, {ItemCreator? fromMap});
  Future<Map<String, dynamic>?> getMap(String name);

  //
  void registerAdapter() {}
  Future<void> close() => Future.value();
  Future<void> flush() => Future.value();
  Future<List<String>> getKeys();
}
