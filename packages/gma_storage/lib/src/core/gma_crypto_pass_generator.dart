import 'dart:convert';
import 'dart:math';


class CryptoPassGenerator {
  static final Random _random = Random.secure();
  static List<int> createCryptoRandom([int length = 32]) {
    final values = List<int>.generate(length, (i) => _random.nextInt(256));

    return values;
  }

  static String createCryptoRandomString([int length = 32]) {
    final values = createCryptoRandom(length);

    return base64Url.encode(values);
  }
}
