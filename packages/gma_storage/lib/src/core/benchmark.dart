// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: avoid_positional_boolean_parameters, use_setters_to_change_properties
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import '../domain/domain.dart';

@immutable
class BenchmarkItem {
  BenchmarkItem({
    required this.key,
    required this.value,
    this.parentName,
  }) : id = DateTime.now().millisecondsSinceEpoch;
  final int id;
  final String key;
  final String? parentName;
  final Stopwatch value;

  void stop() {
    value.stop();
  }

  String get message =>
      '${parentName != null ? '[$parentName]->' : ''}[$key] elapsed ${value.elapsed} / ${value.elapsedMilliseconds}ms';

  @override
  bool operator ==(covariant BenchmarkItem other) {
    if (identical(this, other)) return true;

    return other.key == key && other.id == id;
  }

  @override
  int get hashCode => key.hashCode ^ id.hashCode;
}

/// Storage benchmark for recording elpased time on db actions
///
class StorageBenchmark {
  StorageBenchmark(this.name, this.logger, {this.isBenchmarkEnabled = false});
  final Set<BenchmarkItem> items = {};
  final String name;

  final Logger logger;
  final Duration limit = const Duration(milliseconds: 32);
  bool isBenchmarkEnabled;
  void log(dynamic message) {
    if (isBenchmarkEnabled) {
      logger.i(message);
    }
  }

  /// Start benchmark for with action `name`
  /// return BenchmarkItem, if you want to stop current stopwatch
  ///
  /// ```dart
  /// final benchmark = start('action');
  /// benchmark?.stop();
  /// ```
  BenchmarkItem? start(String name, {String? parentName}) {
    if (!isBenchmarkEnabled) return null;
    final item = BenchmarkItem(
      key: name,
      value: Stopwatch()..start(),
      parentName: parentName,
    );
    items.add(item);
    return item;
  }

  /// Stop running and clear all records
  void clear() {
    if (!isBenchmarkEnabled) return;
    for (final item in items) {
      item.stop();
    }
    items.clear();
  }

  void showResults() {
    final buffer = StringBuffer()
      ..writeln('BENCHMARK RESULTS $name')
      ..writeln(' ' * 80);
    for (final item in items.toList()) {
      buffer.writeln('key: ${item.key.padRight(30)} \t ${item.message}');
    }
    log(buffer);
  }

  void stop(BenchmarkItem? item) {
    if (item == null) return;
    final value = items.firstWhereOrNull(
      (element) => element.id == item.id,
    );
    if (value != null) {
      value.stop();
      if (value.value.elapsed > limit) {
        log('BM[$name] ${value.message}');
      } else {
        log('BM[$name] ${value.message}');
      }
    } else {
      log('BM[$name] not found');
    }
  }
}

/// Extends GmaStorageProvider with Benchmark functions
mixin BenchmarkMixin {
  late final StorageBenchmark storageBenchmark;

  void log(dynamic message) => storageBenchmark.log(message);

  /// Initialization of the benchmark
  void initBenchmark(String name, Logger logger) => storageBenchmark = StorageBenchmark(name, logger);

  /// toggle logging for current benchmark
  void toggleBenchmark(bool enable) => storageBenchmark.isBenchmarkEnabled = enable;

  /// General addBenchmark action
  BenchmarkItem? addBenchmark(String name, {String? parentName}) {
    return storageBenchmark.start(name, parentName: parentName);
  }

  /// Init benchmark
  BenchmarkItem? addInitBenchmark({String? parentName}) => addBenchmark('init', parentName: parentName);

  /// Insert benchmark
  BenchmarkItem? addInsertBenchmark<T>(String name) => addBenchmark('insert<$T>($name)');

  /// Delete benchmark
  BenchmarkItem? addDeleteBenchmark(String name) => addBenchmark('delete($name)');

  /// Get benchmark
  BenchmarkItem? addGetBenchmark<T>(String name) => addBenchmark('get<$T>($name)');

  /// Clear benchmark
  BenchmarkItem? addClearBenchmark() => addBenchmark('clear_storage');

  void stopBenchmark(BenchmarkItem? item) => storageBenchmark.stop(item);
}

mixin GmaBenchmarkMixin on GmaStorageProvider, BenchmarkMixin {
  /// `showAllResults` get all recorded actions and print them to the console.
  Future<void> showAllResults() {
    if (!storageBenchmark.isBenchmarkEnabled) return Future.value();
    log('BM[${storageBenchmark.name}]:results');
    for (final item in storageBenchmark.items) {
      log('BM[${storageBenchmark.name}] $item.message');
    }
    return Future.value();
  }
}

mixin SembastBenchmarkMixin on StorageProviderBase, BenchmarkMixin {
  /// `showAllResults` get all recorded actions and print them to the console.
  Future<void> showAllResults() {
    if (!storageBenchmark.isBenchmarkEnabled) return Future.value();
    log('BM[${storageBenchmark.name}]:results');
    for (final item in storageBenchmark.items) {
      log('BM[${storageBenchmark.name}] $item.message');
    }
    return Future.value();
  }
}
