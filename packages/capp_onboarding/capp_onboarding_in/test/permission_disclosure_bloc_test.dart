
import 'package:bloc_test/bloc_test.dart';
import 'package:capp_onboarding/capp_onboarding.dart';
import 'package:mocktail/mocktail.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:test/test.dart';

import 'test_mock_onboarding.dart';

void main() {
  late PermissionDisclosureBloc permissionDisclosureBloc;
  late MockIIdentityRepository mockIIdentityRepository;
  late MockIPermissionsService mockIPermissionsService;
  late MockIPlatformService mockIPlatformService;
  late MockIPermissionDisclosureStorage mockIPermissionDisclosureStorage;
  late MockUserDefaultProperty mockUserDefaultProperty;
  late MockOnboardingTrackingService onboardingTrackingService;

  setUp(() {
    mockIIdentityRepository = MockIIdentityRepository();
    mockIPermissionsService = MockIPermissionsService();
    mockIPermissionDisclosureStorage = MockIPermissionDisclosureStorage();
    mockIPlatformService = MockIPlatformService();
    mockUserDefaultProperty = MockUserDefaultProperty();
    onboardingTrackingService = MockOnboardingTrackingService();
    permissionDisclosureBloc = PermissionDisclosureBloc(
      permissionsService: mockIPermissionsService,
      platformService: mockIPlatformService,
      trackingService: onboardingTrackingService,
      userDefaultProperty: mockUserDefaultProperty,
      permissionDisclosureStorage: mockIPermissionDisclosureStorage,
      identityRepository: mockIIdentityRepository,
    );
  });

  blocTest<PermissionDisclosureBloc, PermissionDisclosureState>(
    'check permission',
    build: () {
      when(() => mockIPermissionsService.getFinboxPermissions()).thenAnswer((invocation) => <Permission>[]);
      when(() => mockIPermissionDisclosureStorage.getTime()).thenAnswer((invocation) => Future.value());
      return permissionDisclosureBloc;
    },
    act: (bloc) {
      bloc.add(const PermissionDisclosureEvent.checkPermissions());
    },
    expect: () => <PermissionDisclosureState>[
      const PermissionDisclosureState(
        isLoading: true,
        permission: AllPermission.none,
        permissionList: <Permission>[],
        status: PermissionDisclosureStatus.preInit,
      ),
      const PermissionDisclosureState(
        isLoading: true,
        permission: AllPermission.none,
        permissionList: <Permission>[],
        status: PermissionDisclosureStatus.started,
      ),
    ],
  );

  blocTest<PermissionDisclosureBloc, PermissionDisclosureState>(
    'ask permissions',
    build: () {
      when(() => mockIPermissionsService.getFinboxPermissions()).thenAnswer((invocation) => <Permission>[]);
      return permissionDisclosureBloc;
    },
    act: (bloc) {
      bloc.add(const PermissionDisclosureEvent.startAskingFinboxPermission(secondTimeDenied: false));
    },
    expect: () => <PermissionDisclosureState>[
      const PermissionDisclosureState(
        isLoading: true,
        permission: AllPermission.none,
        permissionList: <Permission>[],
        status: PermissionDisclosureStatus.asking,
      ),
      const PermissionDisclosureState(
        isLoading: true,
        permission: AllPermission.none,
        permissionList: <Permission>[],
        status: PermissionDisclosureStatus.finished,
      ),
    ],
  );

  tearDown(() {
    permissionDisclosureBloc.close();
  });
}
