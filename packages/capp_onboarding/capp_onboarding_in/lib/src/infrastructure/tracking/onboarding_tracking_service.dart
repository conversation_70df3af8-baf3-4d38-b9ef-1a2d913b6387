import 'package:capp_tracking/capp_tracking.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';

class OnboardingTrackingService extends CappTrackingService {
  OnboardingTrackingService({
    required EventTrackingService eventTrackingService,
    required ICurrentUserRepository currentUserRepository,
    required IUserRepository userRepository,
  }) : super(
          eventTrackingService: eventTrackingService,
          currentUserRepository: currentUserRepository,
          userRepository: userRepository,
        );

  @override
  void trackClickEvent({
    Map<String, String>? userPropertyMap,
    required String eventCategory,
    required String eventLabel,
    KoyalEvent? event,
  }) {
    userPropertyMap ??= <String, String>{};
    _customUserPropertyMap.then((value) {
      userPropertyMap?.addAll(value);
      super.trackClickEvent(
        userPropertyMap: userPropertyMap,
        eventCategory: eventCategory,
        eventLabel: eventLabel,
        event: event,
      );
    });
  }

  @override
  void trackViewEvent({
    Map<String, String>? userPropertyMap,
    required String eventCategory,
    required String eventLabel,
    KoyalEvent? event,
  }) {
    userPropertyMap ??= <String, String>{};
    _customUserPropertyMap.then((value) {
      userPropertyMap?.addAll(value);
      super.trackViewEvent(
        userPropertyMap: userPropertyMap,
        eventCategory: eventCategory,
        eventLabel: eventLabel,
        event: event,
      );
    });
  }

  @override
  Future<void> trackScreenEvent({
    required String screenName,
    Map<String, String>? userPropertyMap,
  }) async {
    userPropertyMap ??= <String, String>{};
    userPropertyMap.addAll(await _customUserPropertyMap);

    return super.trackScreenEvent(
      screenName: screenName,
      userPropertyMap: userPropertyMap,
    );
  }

  Future<Map<String, String>> get _customUserPropertyMap async => {
        TrackingProperties.propertyCdIsProd: await isProdDimension,
        TrackingProperties.propertyUserType: await existingUser,
      };
}
