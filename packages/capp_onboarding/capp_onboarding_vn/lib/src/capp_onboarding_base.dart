import 'package:get_it/get_it.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_navigation_annotation/koyal_navigation_annotation.dart';

import '../capp_onboarding.dart';

part 'capp_onboarding_base.g.dart';

@PackageRouter(
  routes: [
    PackageRoute(
      OnboardingCarouselScreen,
      routeName: 'onboarding_carousel',
      useWrapper: true,
      bussinesRouteName: 'onb_carousel',
    ),
  ],
)
abstract class _CappOnboarding extends ChildPackage {
  @override
  void registerDependencies(GetIt c) {}
}
