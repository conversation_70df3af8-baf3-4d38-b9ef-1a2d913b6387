// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'consents_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ConsentsEvent {}

/// @nodoc

class _$_StartAsking with DiagnosticableTreeMixin implements _StartAsking {
  const _$_StartAsking();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ConsentsEvent.startAsking()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'ConsentsEvent.startAsking'));
  }
}

abstract class _StartAsking implements ConsentsEvent {
  const factory _StartAsking() = _$_StartAsking;
}

/// @nodoc

class _$_StartAskingForCommunication
    with DiagnosticableTreeMixin
    implements _StartAskingForCommunication {
  const _$_StartAskingForCommunication();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ConsentsEvent.startAskingForCommunication()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty(
        'type', 'ConsentsEvent.startAskingForCommunication'));
  }
}

abstract class _StartAskingForCommunication implements ConsentsEvent {
  const factory _StartAskingForCommunication() = _$_StartAskingForCommunication;
}

/// @nodoc

class _$_StartAskingForLocation
    with DiagnosticableTreeMixin
    implements _StartAskingForLocation {
  const _$_StartAskingForLocation();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ConsentsEvent.startAskingForLocation()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(
        DiagnosticsProperty('type', 'ConsentsEvent.startAskingForLocation'));
  }
}

abstract class _StartAskingForLocation implements ConsentsEvent {
  const factory _StartAskingForLocation() = _$_StartAskingForLocation;
}

/// @nodoc

class _$_ClearPermission
    with DiagnosticableTreeMixin
    implements _ClearPermission {
  const _$_ClearPermission();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ConsentsEvent.clearPermission()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
        .add(DiagnosticsProperty('type', 'ConsentsEvent.clearPermission'));
  }
}

abstract class _ClearPermission implements ConsentsEvent {
  const factory _ClearPermission() = _$_ClearPermission;
}

/// @nodoc
mixin _$ConsentsState {
  ConsentsStatus get status => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ConsentsStateCopyWith<ConsentsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConsentsStateCopyWith<$Res> {
  factory $ConsentsStateCopyWith(
          ConsentsState value, $Res Function(ConsentsState) then) =
      _$ConsentsStateCopyWithImpl<$Res, ConsentsState>;
  @useResult
  $Res call({ConsentsStatus status});
}

/// @nodoc
class _$ConsentsStateCopyWithImpl<$Res, $Val extends ConsentsState>
    implements $ConsentsStateCopyWith<$Res> {
  _$ConsentsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConsentsStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ConsentsStateCopyWith<$Res>
    implements $ConsentsStateCopyWith<$Res> {
  factory _$$_ConsentsStateCopyWith(
          _$_ConsentsState value, $Res Function(_$_ConsentsState) then) =
      __$$_ConsentsStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ConsentsStatus status});
}

/// @nodoc
class __$$_ConsentsStateCopyWithImpl<$Res>
    extends _$ConsentsStateCopyWithImpl<$Res, _$_ConsentsState>
    implements _$$_ConsentsStateCopyWith<$Res> {
  __$$_ConsentsStateCopyWithImpl(
      _$_ConsentsState _value, $Res Function(_$_ConsentsState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$_ConsentsState(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConsentsStatus,
    ));
  }
}

/// @nodoc

class _$_ConsentsState with DiagnosticableTreeMixin implements _ConsentsState {
  const _$_ConsentsState({required this.status});

  @override
  final ConsentsStatus status;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ConsentsState(status: $status)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ConsentsState'))
      ..add(DiagnosticsProperty('status', status));
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ConsentsState &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ConsentsStateCopyWith<_$_ConsentsState> get copyWith =>
      __$$_ConsentsStateCopyWithImpl<_$_ConsentsState>(this, _$identity);
}

abstract class _ConsentsState implements ConsentsState {
  const factory _ConsentsState({required final ConsentsStatus status}) =
      _$_ConsentsState;

  @override
  ConsentsStatus get status;
  @override
  @JsonKey(ignore: true)
  _$$_ConsentsStateCopyWith<_$_ConsentsState> get copyWith =>
      throw _privateConstructorUsedError;
}
