part of 'consents_bloc.dart';

@Freezed(
  copyWith: false,
  equal: false,
  fromJson: false,
  toJson: false,
  map: FreezedMapOptions.none,
  when: FreezedWhenOptions.none,
)
class ConsentsEvent with _$ConsentsEvent {
  const factory ConsentsEvent.startAsking() = _StartAsking;
  const factory ConsentsEvent.startAskingForCommunication() = _StartAskingForCommunication;
  const factory ConsentsEvent.startAskingForLocation() = _StartAskingForLocation;

  const factory ConsentsEvent.clearPermission() = _ClearPermission;
}
