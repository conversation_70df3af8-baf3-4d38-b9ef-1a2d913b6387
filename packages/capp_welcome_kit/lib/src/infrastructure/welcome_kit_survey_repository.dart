import 'dart:core';

import 'package:capp_content_core/capp_content_core.dart' as document;
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as api_models;
import 'package:selfcareapi/selfcareapi.dart';

import '../../capp_welcome_kit.dart';
import '../../src/core/constant.dart';
import '../../src/domain/i_welcome_kit_survey_repository.dart';
import '../../src/domain/welcome_kit_failure.dart';
import '../domain/mappers/insurance_mapper.dart';
import '../domain/models/insurance_info.dart';
import '../domain/models/welcome_kit_survey_detail.dart';
import 'welcome_kit_survey_mapper.dart';

class WelcomeKitSurveyRepository implements IWelcomeKitSurveyRepository {
  final document.IDocumentsRepository documentRepository;
  final IEnumerationsRepository enumerationsRepository;
  final IUserRepository userRepository;
  final Logger logger;
  final L10nCappWelcomeKit localizeText;
  final _insurances = <InsuranceInfo>[];

  final WelcomeKitSurveyApi api;

  WelcomeKitSurveyRepository({
    required this.documentRepository,
    required this.userRepository,
    required this.logger,
    required this.localizeText,
    required this.api,
    required this.enumerationsRepository,
  });

  static const String _unexpectedSurveyError = 'Unexpected error in welcomeKitSurvey repository';

  @override
  Future<Either<WelcomeKitFailure, List<InsuranceInfo>>> fetchInsuranceCodes() async {
    final result = await enumerationsRepository.getSurveyInsuranceCodes();
    _insurances.clear();
    result.fold(
      (_) {},
      (r) {
        r.items?.forEach((item) {
          _insurances.add(InsuranceInfoMapper.convertEnumerationToInsurance(item));
        });
      },
    );
    return right(_insurances);
  }

  @override
  Either<WelcomeKitFailure, WelcomeKitSurvey> fetchSurvey(
    TemplateId templateId,
    WelcomeKitSurveyDetail surveyDetail,
  ) {
    switch (templateId) {
      case TemplateId.posInsuranceOffer:
        return right(_getPOSInsuranceOfferSurvey(surveyDetail));
      case TemplateId.posInsuranceNoOffer:
        return right(_getPOSInsuranceNoOfferSurvey(surveyDetail));
      case TemplateId.posNoInsuranceOffer:
        return right(_getPOSNoInsuranceOfferSurvey(surveyDetail));
      case TemplateId.posNoInsuranceNoOffer:
        return right(_getPOSNoInsuranceNoOfferSurvey(surveyDetail));
      case TemplateId.clxsbInsurance:
        return right(_getBigTicketSurvey(surveyDetail));
      case TemplateId.clxsbNoInsurance:
        return right(_getBigTicketNoInsuranceSurvey(surveyDetail));
      case TemplateId.clInsurance:
        return right(_getCashLoanSurvey(surveyDetail));
      case TemplateId.clNoInsurance:
        return right(_getCashLoanNoInsuranceSurvey(surveyDetail));
    }
  }

// POS Survey
  WelcomeKitSurvey _getPOSInsuranceOfferSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final product = surveyDetail.contractInfo?.product ?? '';
    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: surveyFirstQuestion,
          questionText: localizeText.posLoanQuestion1(creationDate, gender, product),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion1Answer1,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: surveyQBH1,
          questionText: localizeText.posLoanQuestion2(
            gender,
            _getInsuranceLabel(surveyDetail.contractInfo?.insuranceCode ?? []),
            gender.capitalizeFirstLetter(),
          ),
          questionType: SurveyQuestionType.singleChoice,
          order: 2,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion2Answer1,
              nextQuestionId: 'Q2',
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion2Answer2,
              nextQuestionId: surveyHelpComplete,
            ),
          ],
          hasInsuranceInfo: true,
        ),
        WelcomeKitSurveyQuestion(
          id: 'Q2',
          questionText: localizeText.posLoanQuestion3(gender.capitalizeFirstLetter(), customerName),
          questionType: SurveyQuestionType.singleChoice,
          order: 3,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion3Answer1,
              nextQuestionId: surveyMyLoan,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion3Answer2,
              nextQuestionId: surveyComplete,
            ),
          ],
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }

  WelcomeKitSurvey _getPOSInsuranceNoOfferSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final product = surveyDetail.contractInfo?.product ?? '';

    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: surveyFirstQuestion,
          questionText: localizeText.posLoanQuestion1(creationDate, gender, product),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion1Answer1,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: surveyQBH1,
          questionText: localizeText.posLoanQuestion2(
            gender,
            _getInsuranceLabel(surveyDetail.contractInfo?.insuranceCode ?? []),
            gender.capitalizeFirstLetter(),
          ),
          questionType: SurveyQuestionType.singleChoice,
          order: 2,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion2Answer1,
              nextQuestionId: surveyComplete,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion2Answer2,
              nextQuestionId: surveyHelpComplete,
            ),
          ],
          hasInsuranceInfo: true,
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }

  WelcomeKitSurvey _getPOSNoInsuranceOfferSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final product = surveyDetail.contractInfo?.product ?? '';

    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: 'Q1',
          questionText: localizeText.posLoanQuestion1(creationDate, gender, product),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion1Answer1,
              nextQuestionId: 'Q2',
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: 'Q2',
          questionText: localizeText.posLoanQuestion3(gender.capitalizeFirstLetter(), customerName),
          questionType: SurveyQuestionType.singleChoice,
          order: 2,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion3Answer1,
              nextQuestionId: surveyMyLoan,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion3Answer2,
              nextQuestionId: surveyComplete,
            ),
          ],
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }

  WelcomeKitSurvey _getPOSNoInsuranceNoOfferSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final product = surveyDetail.contractInfo?.product ?? '';

    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: 'Q1',
          questionText: localizeText.posLoanQuestion1(creationDate, gender, product),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.posLoanQuestion1Answer1,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.posLoanQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }
// end POS survey

  WelcomeKitSurvey _getBigTicketSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final amount = surveyDetail.contractInfo?.loanAmount ?? '';

    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: surveyFirstQuestion,
          questionText: localizeText.bigTicketQuestion1(creationDate, gender, amount),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.bigTicketQuestion1Answer1,
              nextQuestionId: 'Q2',
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.bigTicketQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: 'Q2',
          questionText: localizeText.bigTicketQuestion2(gender),
          questionType: SurveyQuestionType.singleChoice,
          order: 2,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.bigTicketQuestion2Answer1,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.bigTicketQuestion2Answer2,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 3,
              text: localizeText.bigTicketQuestion2Answer3,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: surveyQBH1,
          questionText: localizeText.posLoanQuestion2(
            gender,
            _getInsuranceLabel(surveyDetail.contractInfo?.insuranceCode ?? []),
            gender.capitalizeFirstLetter(),
          ),
          questionType: SurveyQuestionType.singleChoice,
          order: 3,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.bigTicketQuestion3Answer1,
              nextQuestionId: surveyComplete,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.bigTicketQuestion3Answer2,
              nextQuestionId: surveyHelpComplete,
            ),
          ],
          hasInsuranceInfo: true,
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }

  WelcomeKitSurvey _getBigTicketNoInsuranceSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final amount = surveyDetail.contractInfo?.loanAmount ?? '';

    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: surveyFirstQuestion,
          questionText: localizeText.bigTicketQuestion1(creationDate, gender, amount),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.bigTicketQuestion1Answer1,
              nextQuestionId: 'Q2',
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.bigTicketQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: 'Q2',
          questionText: localizeText.bigTicketQuestion2(gender),
          questionType: SurveyQuestionType.singleChoice,
          order: 2,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.bigTicketQuestion2Answer1,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.bigTicketQuestion2Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 3,
              text: localizeText.bigTicketQuestion2Answer3,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
          ],
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }

  WelcomeKitSurvey _getCashLoanSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final amount = surveyDetail.contractInfo?.loanAmount ?? '';

    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: surveyFirstQuestion,
          questionText: localizeText.cashLoanQuestion1(creationDate, gender, amount),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.cashLoanQuestion1Answer1,
              nextQuestionId: 'Q2',
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.cashLoanQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: 'Q2',
          questionText: localizeText.cashLoanQuestion2(gender.capitalizeFirstLetter()),
          questionType: SurveyQuestionType.singleChoice,
          order: 2,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.cashLoanQuestion2Answer1,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.cashLoanQuestion2Answer2,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 3,
              text: localizeText.cashLoanQuestion2Answer3,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 4,
              text: localizeText.cashLoanQuestion2Answer4,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 5,
              text: localizeText.cashLoanQuestion2Answer5,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 6,
              text: localizeText.cashLoanQuestion2Answer6,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 7,
              text: localizeText.cashLoanQuestion2Answer7,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 8,
              text: localizeText.cashLoanQuestion2Answer8,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 9,
              text: localizeText.cashLoanQuestion2Answer9,
              nextQuestionId: surveyQBH1,
              surveyResult: surveyResultAlright,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: surveyQBH1,
          questionText: localizeText.cashLoanQuestion3(
            gender,
            _getInsuranceLabel(surveyDetail.contractInfo?.insuranceCode ?? []),
            gender.capitalizeFirstLetter(),
          ),
          questionType: SurveyQuestionType.singleChoice,
          order: 3,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.cashLoanQuestion3Answer1,
              nextQuestionId: surveyComplete,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.cashLoanQuestion3Answer2,
              nextQuestionId: surveyHelpComplete,
            ),
          ],
          hasInsuranceInfo: true,
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }

  WelcomeKitSurvey _getCashLoanNoInsuranceSurvey(WelcomeKitSurveyDetail surveyDetail) {
    final gender = surveyDetail.customerInfo?.gender ?? '';
    final customerName = surveyDetail.customerInfo?.firstName ?? '';
    final creationDate = surveyDetail.contractInfo?.loanOpenDate ?? '';
    final amount = surveyDetail.contractInfo?.loanAmount ?? '';

    return WelcomeKitSurvey(
      templateId: surveyDetail.templateID,
      surveyId: surveyDetail.surveyID,
      openingTitle: localizeText.surveyOpeningTitle(gender, customerName),
      openingText: localizeText.surveyOpeningText,
      closingTitle: localizeText.surveyClosingTitle(gender),
      closingText: localizeText.surveyClosingText(gender),
      closingHelpTitle: localizeText.surveyClosingHelpTitle(gender),
      currentQuestionId: surveyDetail.nextQuestionId ?? '',
      gender: gender,
      questions: [
        WelcomeKitSurveyQuestion(
          id: surveyFirstQuestion,
          questionText: localizeText.cashLoanQuestion1(creationDate, gender, amount),
          questionType: SurveyQuestionType.singleChoice,
          order: 1,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.cashLoanQuestion1Answer1,
              nextQuestionId: 'Q2',
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.cashLoanQuestion1Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultFraud,
            ),
          ],
        ),
        WelcomeKitSurveyQuestion(
          id: 'Q2',
          questionText: localizeText.cashLoanQuestion2(gender.capitalizeFirstLetter()),
          questionType: SurveyQuestionType.singleChoice,
          order: 2,
          options: [
            WelcomeKitSurveyAnswer(
              id: 1,
              text: localizeText.cashLoanQuestion2Answer1,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 2,
              text: localizeText.cashLoanQuestion2Answer2,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 3,
              text: localizeText.cashLoanQuestion2Answer3,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 4,
              text: localizeText.cashLoanQuestion2Answer4,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 5,
              text: localizeText.cashLoanQuestion2Answer5,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 6,
              text: localizeText.cashLoanQuestion2Answer6,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 7,
              text: localizeText.cashLoanQuestion2Answer7,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 8,
              text: localizeText.cashLoanQuestion2Answer8,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
            WelcomeKitSurveyAnswer(
              id: 9,
              text: localizeText.cashLoanQuestion2Answer9,
              nextQuestionId: surveyComplete,
              surveyResult: surveyResultAlright,
            ),
          ],
        ),
      ],
      insurances: _getInsuranceList(surveyDetail.contractInfo?.insuranceCode ?? []),
    );
  }

  String _getInsuranceLabel(List<String> insuranceCode) {
    if (insuranceCode.isEmpty) return '';
    var insuranceLabel = '';
    for (final item in _insurances) {
      for (final insuranceCodeItem in insuranceCode) {
        if (item.keys.contains(insuranceCodeItem)) {
          insuranceLabel += '${item.title} , ';
        }
      }
    }
    if (insuranceLabel.isNotEmpty) {
      insuranceLabel = insuranceLabel.substring(0, insuranceLabel.length - 3);
    }
    return insuranceLabel;
  }

  List<InsuranceInfo> _getInsuranceList(List<String> insuranceCode) {
    if (insuranceCode.isEmpty) return [];
    final results = <InsuranceInfo>[];
    for (final item in _insurances) {
      for (final insuranceCodeItem in insuranceCode) {
        if (item.keys.contains(insuranceCodeItem)) {
          results.add(item);
        }
      }
    }

    return results;
  }

  @override
  Future<Either<WelcomeKitFailure, WelcomeKitSurveyDetail>> getSurveyDetail(String surveyId) async {
    try {
      final response = await api.welcomeKitSurveyActiveSurveyIdGet(surveyId);
      return right(response.toDomain());
    } on DioError catch (e) {
      switch (e.response?.statusCode) {
        case 404:
          return left(WelcomeKitFailure.notFound);
        case 410:
          return left(WelcomeKitFailure.expired);
        default:
          return left(WelcomeKitFailure.unexpected);
      }
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedSurveyError, e, s);
      return left(WelcomeKitFailure.unexpected);
    }
  }

  @override
  Future<Either<WelcomeKitFailure, List<WelcomeKitSurveyItem>>> getSurveyList() async {
    try {
      final response = await api.welcomeKitSurveyActiveGet();
      return right(response.map((e) => e.toDomain()).toList());
    } on DioError catch (_) {
      return left(WelcomeKitFailure.unexpected);
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedSurveyError, e, s);
      return left(WelcomeKitFailure.unexpected);
    }
  }

  @override
  Future<Either<WelcomeKitFailure, void>> submitAnswer(
    String surveyId,
    String currentQuestionId,
    String currentAnswerId,
    String currentAnswerContent,
    String? nextQuestionId,
    String? surveyResult,
  ) async {
    try {
      final response = await api.welcomeKitSurveyActiveSurveyIdCapturePut(
        surveyId,
        addQuestionAnswerHistoryRequestV1: api_models.AddQuestionAnswerHistoryRequestV1(
          currentQuestionId: currentQuestionId,
          currentAnswerId: currentAnswerId,
          nextQuestionId: nextQuestionId,
          surveyResult: surveyResult,
          currentAnswerContent: currentAnswerContent,
        ),
      );

      return right(response);
    } on DioError catch (e) {
      switch (e.response?.statusCode) {
        case 404:
          return left(WelcomeKitFailure.notFound);
        case 410:
          return left(WelcomeKitFailure.expired);
        default:
          return left(WelcomeKitFailure.unexpected);
      }
    } on Exception catch (e, s) {
      logger.wtf(_unexpectedSurveyError, e, s);
      return left(WelcomeKitFailure.unexpected);
    }
  }
}
