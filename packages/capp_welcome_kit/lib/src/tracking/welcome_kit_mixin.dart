import 'dart:async';

import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import 'welcome_kit_tracking.dart';

mixin WelcomeKitTrackingMixin {
  void trackSurveyViewEvent(final BuildContext context, TrackSurveyScreenView event) => _trackEvent(context, event);

  void trackSurveyContinueClick(final BuildContext context, TrackSurveyContinueClick event) => _trackEvent(context, event);

  void trackSurveyDismissClick(final BuildContext context, TrackSurveyDismissClick event) => _trackEvent(context, event);

  void trackSurveyLiveChatClick(BuildContext context, TrackLiveChatClick event) => _trackEvent(context, event);

  void trackInsuranceViewEvent(BuildContext context, TrackInsuranceInfoScreenView event) => _trackEvent(context, event);

  void trackInsuranceContinueClick(BuildContext context, TrackInsuranceInfoContinueClick event) => _trackEvent(context, event);

  void trackInsuranceDismissClick(BuildContext context, TrackInsuranceInfoDismissClick event) => _trackEvent(context, event);

  void trackInsuranceDismissSwipe(BuildContext context, TrackInsuranceInfoDismissSwipe event) => _trackEvent(context, event);

  void trackEmptySurveyPopupView(BuildContext context, TrackEmptySurveyPopupView event) => _trackEvent(context, event);

  void trackEmptySurveyPopupClick(BuildContext context, TrackEmptySurveyPopupClick event) => _trackEvent(context, event);

  void _trackEvent(final BuildContext context, WKSurveyTrackingEvent event) {
    unawaited(context.get<CappTrackingService>().trackCappEvent(event));
  }
}
