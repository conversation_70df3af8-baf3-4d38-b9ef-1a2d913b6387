import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class SurveyProgressIndicator extends StatelessWidget {
  const SurveyProgressIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalAll(
      child: <PERSON>umn(
        children: [
          const SizedBox(
            height: 80,
          ),
          <PERSON><PERSON><PERSON>himmer(
            child: Container(
              width: 104,
              height: 104,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
            ),
          ),
          KoyalPadding.normalVertical(
            child: KoyalShimmer(
              child: Container(
                width: double.infinity,
                height: 20,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.white,
                ),
              ),
            ),
          ),
          KoyalShimmer(
            child: Container(
              width: double.infinity,
              height: 14,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
            ),
          ),
          KoyalPadding.smallVertical(
            child: Koyal<PERSON>himmer(
              child: Container(
                width: double.infinity,
                height: 14,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.white,
                ),
              ),
            ),
          ),
          KoyalShimmer(
            child: Container(
              width: 150,
              height: 14,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
