import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../capp_welcome_kit.dart';

class SurveyDemoView extends StatefulWidget {
  final List<WelcomeKitSurveyItem> surveys;
  final Function(WelcomeKitSurveyItem)? onSelected;

  const SurveyDemoView({super.key, required this.surveys, this.onSelected});

  @override
  State<SurveyDemoView> createState() => _SurveyDemoViewState();
}

class _SurveyDemoViewState extends State<SurveyDemoView> {
  String? selectedValue;

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.small(
      child: Column(
        children: [
          DropdownButton<String>(
            value: selectedValue,
            items: [
              DropdownMenuItem(
                child: KoyalText.body2(L10nCappWelcomeKit.of(context).surveyDemoViewChoose),
              ),
              ...widget.surveys
                  .map<DropdownMenuItem<String>>(
                    (e) => DropdownMenuItem(
                      value: e.contractId.toString(),
                      child: KoyalText.body2(e.contractId.toString()),
                    ),
                  )
                  .toList(),
            ],
            onChanged: (value) {
              setState(() {
                selectedValue = value;
              });
            },
          ),
          const Spacer(),
          PrimaryButton(
            text: L10nCappWelcomeKit.of(context).surveyDemoViewOpenButton,
            onPressed: selectedValue != null && (selectedValue?.isNotEmpty ?? false) ? () => _onPressed(context) : null,
          ),
        ],
      ),
    );
  }

  void _onPressed(BuildContext context) {
    if (selectedValue == null) {
      return;
    }
    final survey = widget.surveys.firstWhere((s) => s.contractId.toString() == selectedValue);

    widget.onSelected?.call(survey);

    // context.navigator.pushFromPackage(
    //   package: 'CappWelcomeKit',
    //   screen: 'WelcomeKitSurveyScreen',
    //   arguments: SurveyScreenArguments(
    //     templateId: survey.templateId,
    //     sur: survey.surveyId,
    //   ),
    // );
  }
}
