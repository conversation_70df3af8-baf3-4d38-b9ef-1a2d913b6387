import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_welcome_kit.dart';
import '../../tracking/welcome_kit_constants.dart';
import '../../tracking/welcome_kit_mixin.dart';
import '../../tracking/welcome_kit_tracking.dart';

class SurveyClosingView extends StatelessWidget with WelcomeKitTrackingMixin {
  final WelcomeKitSurveyState state;

  const SurveyClosingView({
    super.key,
    required this.state,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const AssetSvgImage(
                  'assets/survey_closing.svg',
                  package: 'capp_content',
                  height: 270,
                ),
                const SizedBox(height: KoyalPadding.paddingNormal),
                KoyalPadding.largeHorizontal(
                  child: state.isClosingHelp
                      ? _ClosingHelpTitle(state)
                      : KoyalText.header6(
                          state.personalizedSurvey!.closingTitle,
                          textAlign: TextAlign.center,
                        ),
                ),
                const SizedBox(height: KoyalPadding.paddingSmall),
                if (!state.isClosingHelp)
                  KoyalPadding.largeHorizontal(
                    child: KoyalText.body2(
                      state.personalizedSurvey!.closingText,
                      textAlign: TextAlign.center,
                      color: ColorTheme.of(context).secondaryTextColor,
                    ),
                  ),
              ],
            ),
          ),
        ),
        KoyalPadding.normalAll(
          child: PrimaryButton(
            text: L10nCappWelcomeKit.of(context).surveyClosingViewDoneButton,
            onPressed: () => onDonePressed(context),
          ),
        ),
      ],
    );
  }

  void onDonePressed(BuildContext context) {
    trackSurveyContinueClick(
      context,
      TrackSurveyContinueClick(
        categoryId: state.templateId,
        surveyId: state.survey?.surveyId ?? '',
        questionId: state.currentQuestion?.id ?? '',
        questionName: WKTrackingConstants.closingScreen,
        questionAnswer: state.selectedAnswer?.text ?? '',
        isSurveyCompleted: true,
      ),
    );
    context.navigator.toMainScreen();
  }
}

class _ClosingHelpTitle extends StatelessWidget with WelcomeKitTrackingMixin {
  final WelcomeKitSurveyState state;

  const _ClosingHelpTitle(this.state);

  @override
  Widget build(BuildContext context) {
    final highlight = L10nCappWelcomeKit.of(context).pressHere;
    final title = state.personalizedSurvey?.closingHelpTitle ?? '';
    final titles = title.split(highlight);
    return Center(
      child: GestureDetector(
        onTap: () => onSupportButtonPressed(context),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            text: titles[0],
            style: TextStyleTheme.of(context)
                .header6
                .copyWith(color: ColorTheme.of(context).defaultTextColor),
            children: [
              TextSpan(
                text: highlight,
                style: TextStyleTheme.of(context)
                    .header6
                    .copyWith(color: HciColors.secondary600),
              ),
              TextSpan(
                text: titles[1],
                style: TextStyleTheme.of(context)
                    .header6
                    .copyWith(color: ColorTheme.of(context).defaultTextColor),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void onSupportButtonPressed(BuildContext context) {
    trackSurveyLiveChatClick(
      context,
      TrackLiveChatClick(
        categoryId: state.survey?.templateId?.name,
        surveyId: state.survey?.surveyId,
        questionId: state.currentPersonalizedQuestion?.id,
        questionName: state.currentPersonalizedQuestion?.questionText ?? '',
        questionAnswer: state.selectedAnswer?.text ?? '',
        isSurveyCompleted: state.isSurveyCompleted,
      ),
    );
    context.navigator.pushFromPackage(
      package: 'KoyalChatbot',
      screen: 'ChatbotScreen',
    );
  }
}
