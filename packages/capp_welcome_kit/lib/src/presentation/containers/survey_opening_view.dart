import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../l10n/i18n.dart';
import '../../application/survey/welcome_kit_survey_bloc.dart';
import '../../tracking/welcome_kit_constants.dart';
import '../../tracking/welcome_kit_mixin.dart';
import '../../tracking/welcome_kit_tracking.dart';

class SurveyOpeningView extends StatelessWidget with WelcomeKitTrackingMixin {
  final WelcomeKitSurveyState state;

  const SurveyOpeningView({
    super.key,
    required this.state,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const AssetSvgImage(
                  'assets/survey_opening.svg',
                  package: 'capp_content',
                  height: 112,
                ),
                const SizedBox(height: KoyalPadding.paddingNormal),
                KoyalPadding.largeHorizontal(
                  child: KoyalText.header6(
                    state.personalizedSurvey?.openingTitle ?? '',
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: KoyalPadding.paddingSmall),
                KoyalPadding.largeHorizontal(
                  child: KoyalText.body2(
                    state.personalizedSurvey?.openingText ?? '',
                    textAlign: TextAlign.center,
                    color: ColorTheme.of(context).secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ),
        KoyalPadding.normalAll(
          child: PrimaryButton(
            text: L10nCappWelcomeKit.of(context).surveyOpeningViewStartButton,
            onPressed: () {
              trackSurveyContinueClick(
                context,
                TrackSurveyContinueClick(
                  categoryId: state.survey?.templateId?.name ?? '',
                  surveyId: state.survey?.surveyId ?? '',
                  questionId: '',
                  questionName: WKTrackingConstants.openingScreen,
                  questionAnswer: '',
                  isSurveyCompleted: false,
                ),
              );
              final isHavingQuestion = state.personalizedSurvey?.questions != null && state.personalizedSurvey!.questions.isNotEmpty;
              context.get<WelcomeKitSurveyBloc>().add(
                    isHavingQuestion
                        ? const WelcomeKitSurveyEvent.changeScreenType(
                            SurveyScreenType.question,
                          )
                        : const WelcomeKitSurveyEvent.changeScreenType(
                            SurveyScreenType.closingScreen,
                          ),
                  );
            },
          ),
        ),
      ],
    );
  }
}
