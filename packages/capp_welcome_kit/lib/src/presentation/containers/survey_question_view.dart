import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_welcome_kit.dart';
import '../../tracking/welcome_kit_mixin.dart';
import '../../tracking/welcome_kit_tracking.dart';
import '../widgets/insurance_info_view.dart';
import 'survey_question_content.dart';

class SurveyQuestionView extends StatelessWidget with WelcomeKitTrackingMixin {
  const SurveyQuestionView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WelcomeKitSurveyBloc, WelcomeKitSurveyState>(
      builder: (context, state) {
        final hasInsurance = state.currentQuestion?.hasInsuranceInfo == true;
        if (state.currentPersonalizedQuestion != null && state.currentQuestion != null) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildProgressIndicator(state),
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.5,
                ),
                child: Scrollbar(
                  thumbVisibility: true,
                  controller: ScrollController(),
                  child: SingleChildScrollView(
                    child: KoyalPadding.normalAll(
                      child: KoyalText.subtitle1(
                        state.currentPersonalizedQuestion!.questionText,
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ),
                ),
              ),
              if (hasInsurance)
                InsuranceInfoView(state: state),
              Expanded(child: getScreenContentByType(state)),
              KoyalPadding.normalAll(
                child: PrimaryButton(
                  text: L10nCappWelcomeKit.of(context).surveyScreenContinueBtn,
                  isInProgress: state.isQuestionLoading,
                  onPressed: state.selectedAnswer != null ||
                          state.selectedMultipleAnswer.isNotEmpty ||
                          state.selectedRating != null ||
                          (state.openAnswer != null && state.openAnswer!.isNotEmpty)
                      ? () => onContinueButtonPressed(context, state)
                      : null,
                ),
              ),
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget buildProgressIndicator(WelcomeKitSurveyState state) {
    final totalQuestions = state.survey?.questions.length ?? 0;
    final currentQuestionIndex = state.currentQuestion?.order ?? 0;

    return LinearProgressIndicator(
      value: currentQuestionIndex / totalQuestions,
      color: HciColors.semanticGreen500,
      backgroundColor: HciColors.supplementary50,
      minHeight: 4,
    );
  }

  void onContinueButtonPressed(BuildContext context, WelcomeKitSurveyState state) {
    trackSurveyContinueClick(
      context,
      TrackSurveyContinueClick(
        categoryId: state.survey?.templateId?.name ?? '',
        surveyId: state.survey?.surveyId ?? '',
        questionId: state.currentQuestion?.id ?? '',
        questionName: state.currentQuestion?.questionText ?? '',
        questionAnswer: state.selectedAnswer?.text ?? '',
        isSurveyCompleted: state.currentQuestion!.id == state.survey?.questions.last.id,
      ),
    );
    context.get<WelcomeKitSurveyBloc>().add(const WelcomeKitSurveyEvent.setNextQuestion());
  }
}

Widget getScreenContentByType(WelcomeKitSurveyState state) {
  switch (state.currentQuestion!.questionType) {
    case SurveyQuestionType.singleChoice:
      return const SurveyQuestionSingleChoice();
    case SurveyQuestionType.multipleChoice:
      return const SurveyQuestionMultipleChoice();
    case SurveyQuestionType.openQuestion:
      return const SurveyOpenQuestion();
    case SurveyQuestionType.emojiRating:
      return Container();
    case SurveyQuestionType.starRating:
      return const SurveyStarRating();
    default:
      return const SizedBox.shrink();
  }
}
