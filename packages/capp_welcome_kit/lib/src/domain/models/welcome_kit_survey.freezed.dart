// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'welcome_kit_survey.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$WelcomeKitSurvey {
  String get surveyId => throw _privateConstructorUsedError;
  TemplateId? get templateId => throw _privateConstructorUsedError;
  String get surveyTitle => throw _privateConstructorUsedError;
  String get openingTitle => throw _privateConstructorUsedError;
  String get openingText => throw _privateConstructorUsedError;
  String get closingTitle => throw _privateConstructorUsedError;
  String get closingText => throw _privateConstructorUsedError;
  List<WelcomeKitSurveyQuestion> get questions =>
      throw _privateConstructorUsedError;
  String get currentQuestionId => throw _privateConstructorUsedError;
  String get closingHelpTitle => throw _privateConstructorUsedError;
  String get gender => throw _privateConstructorUsedError;
  String get fullName => throw _privateConstructorUsedError;
  List<String> get insurancesCode => throw _privateConstructorUsedError;
  DateTime? get creationDate => throw _privateConstructorUsedError;
  List<InsuranceInfo> get insurances => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WelcomeKitSurveyCopyWith<WelcomeKitSurvey> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WelcomeKitSurveyCopyWith<$Res> {
  factory $WelcomeKitSurveyCopyWith(
          WelcomeKitSurvey value, $Res Function(WelcomeKitSurvey) then) =
      _$WelcomeKitSurveyCopyWithImpl<$Res, WelcomeKitSurvey>;
  @useResult
  $Res call(
      {String surveyId,
      TemplateId? templateId,
      String surveyTitle,
      String openingTitle,
      String openingText,
      String closingTitle,
      String closingText,
      List<WelcomeKitSurveyQuestion> questions,
      String currentQuestionId,
      String closingHelpTitle,
      String gender,
      String fullName,
      List<String> insurancesCode,
      DateTime? creationDate,
      List<InsuranceInfo> insurances});
}

/// @nodoc
class _$WelcomeKitSurveyCopyWithImpl<$Res, $Val extends WelcomeKitSurvey>
    implements $WelcomeKitSurveyCopyWith<$Res> {
  _$WelcomeKitSurveyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? surveyId = null,
    Object? templateId = freezed,
    Object? surveyTitle = null,
    Object? openingTitle = null,
    Object? openingText = null,
    Object? closingTitle = null,
    Object? closingText = null,
    Object? questions = null,
    Object? currentQuestionId = null,
    Object? closingHelpTitle = null,
    Object? gender = null,
    Object? fullName = null,
    Object? insurancesCode = null,
    Object? creationDate = freezed,
    Object? insurances = null,
  }) {
    return _then(_value.copyWith(
      surveyId: null == surveyId
          ? _value.surveyId
          : surveyId // ignore: cast_nullable_to_non_nullable
              as String,
      templateId: freezed == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as TemplateId?,
      surveyTitle: null == surveyTitle
          ? _value.surveyTitle
          : surveyTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingTitle: null == openingTitle
          ? _value.openingTitle
          : openingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingText: null == openingText
          ? _value.openingText
          : openingText // ignore: cast_nullable_to_non_nullable
              as String,
      closingTitle: null == closingTitle
          ? _value.closingTitle
          : closingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      closingText: null == closingText
          ? _value.closingText
          : closingText // ignore: cast_nullable_to_non_nullable
              as String,
      questions: null == questions
          ? _value.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<WelcomeKitSurveyQuestion>,
      currentQuestionId: null == currentQuestionId
          ? _value.currentQuestionId
          : currentQuestionId // ignore: cast_nullable_to_non_nullable
              as String,
      closingHelpTitle: null == closingHelpTitle
          ? _value.closingHelpTitle
          : closingHelpTitle // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      insurancesCode: null == insurancesCode
          ? _value.insurancesCode
          : insurancesCode // ignore: cast_nullable_to_non_nullable
              as List<String>,
      creationDate: freezed == creationDate
          ? _value.creationDate
          : creationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      insurances: null == insurances
          ? _value.insurances
          : insurances // ignore: cast_nullable_to_non_nullable
              as List<InsuranceInfo>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_WelcomeKitSurveyCopyWith<$Res>
    implements $WelcomeKitSurveyCopyWith<$Res> {
  factory _$$_WelcomeKitSurveyCopyWith(
          _$_WelcomeKitSurvey value, $Res Function(_$_WelcomeKitSurvey) then) =
      __$$_WelcomeKitSurveyCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String surveyId,
      TemplateId? templateId,
      String surveyTitle,
      String openingTitle,
      String openingText,
      String closingTitle,
      String closingText,
      List<WelcomeKitSurveyQuestion> questions,
      String currentQuestionId,
      String closingHelpTitle,
      String gender,
      String fullName,
      List<String> insurancesCode,
      DateTime? creationDate,
      List<InsuranceInfo> insurances});
}

/// @nodoc
class __$$_WelcomeKitSurveyCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyCopyWithImpl<$Res, _$_WelcomeKitSurvey>
    implements _$$_WelcomeKitSurveyCopyWith<$Res> {
  __$$_WelcomeKitSurveyCopyWithImpl(
      _$_WelcomeKitSurvey _value, $Res Function(_$_WelcomeKitSurvey) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? surveyId = null,
    Object? templateId = freezed,
    Object? surveyTitle = null,
    Object? openingTitle = null,
    Object? openingText = null,
    Object? closingTitle = null,
    Object? closingText = null,
    Object? questions = null,
    Object? currentQuestionId = null,
    Object? closingHelpTitle = null,
    Object? gender = null,
    Object? fullName = null,
    Object? insurancesCode = null,
    Object? creationDate = freezed,
    Object? insurances = null,
  }) {
    return _then(_$_WelcomeKitSurvey(
      surveyId: null == surveyId
          ? _value.surveyId
          : surveyId // ignore: cast_nullable_to_non_nullable
              as String,
      templateId: freezed == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as TemplateId?,
      surveyTitle: null == surveyTitle
          ? _value.surveyTitle
          : surveyTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingTitle: null == openingTitle
          ? _value.openingTitle
          : openingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      openingText: null == openingText
          ? _value.openingText
          : openingText // ignore: cast_nullable_to_non_nullable
              as String,
      closingTitle: null == closingTitle
          ? _value.closingTitle
          : closingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      closingText: null == closingText
          ? _value.closingText
          : closingText // ignore: cast_nullable_to_non_nullable
              as String,
      questions: null == questions
          ? _value._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<WelcomeKitSurveyQuestion>,
      currentQuestionId: null == currentQuestionId
          ? _value.currentQuestionId
          : currentQuestionId // ignore: cast_nullable_to_non_nullable
              as String,
      closingHelpTitle: null == closingHelpTitle
          ? _value.closingHelpTitle
          : closingHelpTitle // ignore: cast_nullable_to_non_nullable
              as String,
      gender: null == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      insurancesCode: null == insurancesCode
          ? _value._insurancesCode
          : insurancesCode // ignore: cast_nullable_to_non_nullable
              as List<String>,
      creationDate: freezed == creationDate
          ? _value.creationDate
          : creationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      insurances: null == insurances
          ? _value._insurances
          : insurances // ignore: cast_nullable_to_non_nullable
              as List<InsuranceInfo>,
    ));
  }
}

/// @nodoc

class _$_WelcomeKitSurvey implements _WelcomeKitSurvey {
  const _$_WelcomeKitSurvey(
      {this.surveyId = '',
      this.templateId = null,
      this.surveyTitle = '',
      this.openingTitle = '',
      this.openingText = '',
      this.closingTitle = '',
      this.closingText = '',
      final List<WelcomeKitSurveyQuestion> questions = const [],
      this.currentQuestionId = '',
      this.closingHelpTitle = '',
      this.gender = '',
      this.fullName = '',
      final List<String> insurancesCode = const [],
      this.creationDate = null,
      final List<InsuranceInfo> insurances = const []})
      : _questions = questions,
        _insurancesCode = insurancesCode,
        _insurances = insurances;

  @override
  @JsonKey()
  final String surveyId;
  @override
  @JsonKey()
  final TemplateId? templateId;
  @override
  @JsonKey()
  final String surveyTitle;
  @override
  @JsonKey()
  final String openingTitle;
  @override
  @JsonKey()
  final String openingText;
  @override
  @JsonKey()
  final String closingTitle;
  @override
  @JsonKey()
  final String closingText;
  final List<WelcomeKitSurveyQuestion> _questions;
  @override
  @JsonKey()
  List<WelcomeKitSurveyQuestion> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  @override
  @JsonKey()
  final String currentQuestionId;
  @override
  @JsonKey()
  final String closingHelpTitle;
  @override
  @JsonKey()
  final String gender;
  @override
  @JsonKey()
  final String fullName;
  final List<String> _insurancesCode;
  @override
  @JsonKey()
  List<String> get insurancesCode {
    if (_insurancesCode is EqualUnmodifiableListView) return _insurancesCode;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_insurancesCode);
  }

  @override
  @JsonKey()
  final DateTime? creationDate;
  final List<InsuranceInfo> _insurances;
  @override
  @JsonKey()
  List<InsuranceInfo> get insurances {
    if (_insurances is EqualUnmodifiableListView) return _insurances;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_insurances);
  }

  @override
  String toString() {
    return 'WelcomeKitSurvey(surveyId: $surveyId, templateId: $templateId, surveyTitle: $surveyTitle, openingTitle: $openingTitle, openingText: $openingText, closingTitle: $closingTitle, closingText: $closingText, questions: $questions, currentQuestionId: $currentQuestionId, closingHelpTitle: $closingHelpTitle, gender: $gender, fullName: $fullName, insurancesCode: $insurancesCode, creationDate: $creationDate, insurances: $insurances)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WelcomeKitSurvey &&
            (identical(other.surveyId, surveyId) ||
                other.surveyId == surveyId) &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.surveyTitle, surveyTitle) ||
                other.surveyTitle == surveyTitle) &&
            (identical(other.openingTitle, openingTitle) ||
                other.openingTitle == openingTitle) &&
            (identical(other.openingText, openingText) ||
                other.openingText == openingText) &&
            (identical(other.closingTitle, closingTitle) ||
                other.closingTitle == closingTitle) &&
            (identical(other.closingText, closingText) ||
                other.closingText == closingText) &&
            const DeepCollectionEquality()
                .equals(other._questions, _questions) &&
            (identical(other.currentQuestionId, currentQuestionId) ||
                other.currentQuestionId == currentQuestionId) &&
            (identical(other.closingHelpTitle, closingHelpTitle) ||
                other.closingHelpTitle == closingHelpTitle) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            const DeepCollectionEquality()
                .equals(other._insurancesCode, _insurancesCode) &&
            (identical(other.creationDate, creationDate) ||
                other.creationDate == creationDate) &&
            const DeepCollectionEquality()
                .equals(other._insurances, _insurances));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      surveyId,
      templateId,
      surveyTitle,
      openingTitle,
      openingText,
      closingTitle,
      closingText,
      const DeepCollectionEquality().hash(_questions),
      currentQuestionId,
      closingHelpTitle,
      gender,
      fullName,
      const DeepCollectionEquality().hash(_insurancesCode),
      creationDate,
      const DeepCollectionEquality().hash(_insurances));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WelcomeKitSurveyCopyWith<_$_WelcomeKitSurvey> get copyWith =>
      __$$_WelcomeKitSurveyCopyWithImpl<_$_WelcomeKitSurvey>(this, _$identity);
}

abstract class _WelcomeKitSurvey implements WelcomeKitSurvey {
  const factory _WelcomeKitSurvey(
      {final String surveyId,
      final TemplateId? templateId,
      final String surveyTitle,
      final String openingTitle,
      final String openingText,
      final String closingTitle,
      final String closingText,
      final List<WelcomeKitSurveyQuestion> questions,
      final String currentQuestionId,
      final String closingHelpTitle,
      final String gender,
      final String fullName,
      final List<String> insurancesCode,
      final DateTime? creationDate,
      final List<InsuranceInfo> insurances}) = _$_WelcomeKitSurvey;

  @override
  String get surveyId;
  @override
  TemplateId? get templateId;
  @override
  String get surveyTitle;
  @override
  String get openingTitle;
  @override
  String get openingText;
  @override
  String get closingTitle;
  @override
  String get closingText;
  @override
  List<WelcomeKitSurveyQuestion> get questions;
  @override
  String get currentQuestionId;
  @override
  String get closingHelpTitle;
  @override
  String get gender;
  @override
  String get fullName;
  @override
  List<String> get insurancesCode;
  @override
  DateTime? get creationDate;
  @override
  List<InsuranceInfo> get insurances;
  @override
  @JsonKey(ignore: true)
  _$$_WelcomeKitSurveyCopyWith<_$_WelcomeKitSurvey> get copyWith =>
      throw _privateConstructorUsedError;
}
