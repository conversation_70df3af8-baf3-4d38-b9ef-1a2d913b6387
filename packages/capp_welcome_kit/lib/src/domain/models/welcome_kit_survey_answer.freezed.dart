// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'welcome_kit_survey_answer.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$WelcomeKitSurveyAnswer {
  int get id => throw _privateConstructorUsedError;
  String get text => throw _privateConstructorUsedError;
  String get nextQuestionId => throw _privateConstructorUsedError;
  String? get surveyResult => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WelcomeKitSurveyAnswerCopyWith<WelcomeKitSurveyAnswer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WelcomeKitSurveyAnswerCopyWith<$Res> {
  factory $WelcomeKitSurveyAnswerCopyWith(WelcomeKitSurveyAnswer value,
          $Res Function(WelcomeKitSurveyAnswer) then) =
      _$WelcomeKitSurveyAnswerCopyWithImpl<$Res, WelcomeKitSurveyAnswer>;
  @useResult
  $Res call({int id, String text, String nextQuestionId, String? surveyResult});
}

/// @nodoc
class _$WelcomeKitSurveyAnswerCopyWithImpl<$Res,
        $Val extends WelcomeKitSurveyAnswer>
    implements $WelcomeKitSurveyAnswerCopyWith<$Res> {
  _$WelcomeKitSurveyAnswerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? nextQuestionId = null,
    Object? surveyResult = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      nextQuestionId: null == nextQuestionId
          ? _value.nextQuestionId
          : nextQuestionId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyResult: freezed == surveyResult
          ? _value.surveyResult
          : surveyResult // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_WelcomeKitSurveyAnswerCopyWith<$Res>
    implements $WelcomeKitSurveyAnswerCopyWith<$Res> {
  factory _$$_WelcomeKitSurveyAnswerCopyWith(_$_WelcomeKitSurveyAnswer value,
          $Res Function(_$_WelcomeKitSurveyAnswer) then) =
      __$$_WelcomeKitSurveyAnswerCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, String text, String nextQuestionId, String? surveyResult});
}

/// @nodoc
class __$$_WelcomeKitSurveyAnswerCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyAnswerCopyWithImpl<$Res,
        _$_WelcomeKitSurveyAnswer>
    implements _$$_WelcomeKitSurveyAnswerCopyWith<$Res> {
  __$$_WelcomeKitSurveyAnswerCopyWithImpl(_$_WelcomeKitSurveyAnswer _value,
      $Res Function(_$_WelcomeKitSurveyAnswer) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? nextQuestionId = null,
    Object? surveyResult = freezed,
  }) {
    return _then(_$_WelcomeKitSurveyAnswer(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      nextQuestionId: null == nextQuestionId
          ? _value.nextQuestionId
          : nextQuestionId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyResult: freezed == surveyResult
          ? _value.surveyResult
          : surveyResult // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_WelcomeKitSurveyAnswer implements _WelcomeKitSurveyAnswer {
  _$_WelcomeKitSurveyAnswer(
      {required this.id,
      required this.text,
      required this.nextQuestionId,
      this.surveyResult});

  @override
  final int id;
  @override
  final String text;
  @override
  final String nextQuestionId;
  @override
  final String? surveyResult;

  @override
  String toString() {
    return 'WelcomeKitSurveyAnswer(id: $id, text: $text, nextQuestionId: $nextQuestionId, surveyResult: $surveyResult)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WelcomeKitSurveyAnswer &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.nextQuestionId, nextQuestionId) ||
                other.nextQuestionId == nextQuestionId) &&
            (identical(other.surveyResult, surveyResult) ||
                other.surveyResult == surveyResult));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, id, text, nextQuestionId, surveyResult);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WelcomeKitSurveyAnswerCopyWith<_$_WelcomeKitSurveyAnswer> get copyWith =>
      __$$_WelcomeKitSurveyAnswerCopyWithImpl<_$_WelcomeKitSurveyAnswer>(
          this, _$identity);
}

abstract class _WelcomeKitSurveyAnswer implements WelcomeKitSurveyAnswer {
  factory _WelcomeKitSurveyAnswer(
      {required final int id,
      required final String text,
      required final String nextQuestionId,
      final String? surveyResult}) = _$_WelcomeKitSurveyAnswer;

  @override
  int get id;
  @override
  String get text;
  @override
  String get nextQuestionId;
  @override
  String? get surveyResult;
  @override
  @JsonKey(ignore: true)
  _$$_WelcomeKitSurveyAnswerCopyWith<_$_WelcomeKitSurveyAnswer> get copyWith =>
      throw _privateConstructorUsedError;
}
