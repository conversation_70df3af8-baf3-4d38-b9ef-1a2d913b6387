import 'package:freezed_annotation/freezed_annotation.dart';

import 'welcome_kit_survey_answer.dart';

part 'welcome_kit_survey_question.freezed.dart';

enum SurveyQuestionType {
  singleChoice,
  multipleChoice,
  starRating,
  emojiRating,
  openQuestion,
}

@freezed
class WelcomeKitSurveyQuestion with _$WelcomeKitSurveyQuestion {
  factory WelcomeKitSurveyQuestion({
    required String id,
    required String questionText,
    required SurveyQuestionType questionType,
    required int order,
    required List<WelcomeKitSurveyAnswer>? options,
    bool? hasInsuranceInfo,
  }) = _WelcomeKitSurveyQuestion;
}
