import 'package:dartz/dartz.dart';

import '../../capp_welcome_kit.dart';
import 'models/insurance_info.dart';
import 'models/welcome_kit_survey_detail.dart';
import 'welcome_kit_failure.dart';

abstract class IWelcomeKitSurveyRepository {
  Either<WelcomeKitFailure, WelcomeKitSurvey> fetchSurvey(
    TemplateId templateId,
    WelcomeKitSurveyDetail surveyDetail,
  );

  Future<Either<WelcomeKitFailure, List<WelcomeKitSurveyItem>>> getSurveyList();

  Future<Either<WelcomeKitFailure, WelcomeKitSurveyDetail>> getSurveyDetail(String surveyId);

  Future<Either<WelcomeKitFailure, void>> submitAnswer(
    String surveyId,
    String currentQuestionId,
    String currentAnswerId,
    String currentAnswerContent,
    String? nextQuestionId,
    String? surveyResult,
  );

  Future<Either<WelcomeKitFailure, List<InsuranceInfo>>> fetchInsuranceCodes();
}
