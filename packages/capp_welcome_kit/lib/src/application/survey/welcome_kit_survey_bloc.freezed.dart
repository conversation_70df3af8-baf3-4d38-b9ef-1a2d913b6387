// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'welcome_kit_survey_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$WelcomeKitSurveyEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WelcomeKitSurveyEventCopyWith<$Res> {
  factory $WelcomeKitSurveyEventCopyWith(WelcomeKitSurveyEvent value,
          $Res Function(WelcomeKitSurveyEvent) then) =
      _$WelcomeKitSurveyEventCopyWithImpl<$Res, WelcomeKitSurveyEvent>;
}

/// @nodoc
class _$WelcomeKitSurveyEventCopyWithImpl<$Res,
        $Val extends WelcomeKitSurveyEvent>
    implements $WelcomeKitSurveyEventCopyWith<$Res> {
  _$WelcomeKitSurveyEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_FetchSurveyCopyWith<$Res> {
  factory _$$_FetchSurveyCopyWith(
          _$_FetchSurvey value, $Res Function(_$_FetchSurvey) then) =
      __$$_FetchSurveyCopyWithImpl<$Res>;
  @useResult
  $Res call({TemplateId templateId, WelcomeKitSurveyDetail surveyDetail});
}

/// @nodoc
class __$$_FetchSurveyCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_FetchSurvey>
    implements _$$_FetchSurveyCopyWith<$Res> {
  __$$_FetchSurveyCopyWithImpl(
      _$_FetchSurvey _value, $Res Function(_$_FetchSurvey) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? templateId = null,
    Object? surveyDetail = null,
  }) {
    return _then(_$_FetchSurvey(
      templateId: null == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as TemplateId,
      surveyDetail: null == surveyDetail
          ? _value.surveyDetail
          : surveyDetail // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyDetail,
    ));
  }
}

/// @nodoc

class _$_FetchSurvey implements _FetchSurvey {
  const _$_FetchSurvey({required this.templateId, required this.surveyDetail});

  @override
  final TemplateId templateId;
  @override
  final WelcomeKitSurveyDetail surveyDetail;

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.fetchSurvey(templateId: $templateId, surveyDetail: $surveyDetail)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FetchSurvey &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.surveyDetail, surveyDetail) ||
                other.surveyDetail == surveyDetail));
  }

  @override
  int get hashCode => Object.hash(runtimeType, templateId, surveyDetail);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FetchSurveyCopyWith<_$_FetchSurvey> get copyWith =>
      __$$_FetchSurveyCopyWithImpl<_$_FetchSurvey>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return fetchSurvey(templateId, surveyDetail);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return fetchSurvey?.call(templateId, surveyDetail);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (fetchSurvey != null) {
      return fetchSurvey(templateId, surveyDetail);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return fetchSurvey(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return fetchSurvey?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (fetchSurvey != null) {
      return fetchSurvey(this);
    }
    return orElse();
  }
}

abstract class _FetchSurvey implements WelcomeKitSurveyEvent {
  const factory _FetchSurvey(
      {required final TemplateId templateId,
      required final WelcomeKitSurveyDetail surveyDetail}) = _$_FetchSurvey;

  TemplateId get templateId;
  WelcomeKitSurveyDetail get surveyDetail;
  @JsonKey(ignore: true)
  _$$_FetchSurveyCopyWith<_$_FetchSurvey> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ChangeScreenTypeCopyWith<$Res> {
  factory _$$_ChangeScreenTypeCopyWith(
          _$_ChangeScreenType value, $Res Function(_$_ChangeScreenType) then) =
      __$$_ChangeScreenTypeCopyWithImpl<$Res>;
  @useResult
  $Res call({SurveyScreenType type});
}

/// @nodoc
class __$$_ChangeScreenTypeCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_ChangeScreenType>
    implements _$$_ChangeScreenTypeCopyWith<$Res> {
  __$$_ChangeScreenTypeCopyWithImpl(
      _$_ChangeScreenType _value, $Res Function(_$_ChangeScreenType) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$_ChangeScreenType(
      null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as SurveyScreenType,
    ));
  }
}

/// @nodoc

class _$_ChangeScreenType implements _ChangeScreenType {
  const _$_ChangeScreenType(this.type);

  @override
  final SurveyScreenType type;

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.changeScreenType(type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChangeScreenType &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChangeScreenTypeCopyWith<_$_ChangeScreenType> get copyWith =>
      __$$_ChangeScreenTypeCopyWithImpl<_$_ChangeScreenType>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return changeScreenType(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return changeScreenType?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (changeScreenType != null) {
      return changeScreenType(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return changeScreenType(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return changeScreenType?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (changeScreenType != null) {
      return changeScreenType(this);
    }
    return orElse();
  }
}

abstract class _ChangeScreenType implements WelcomeKitSurveyEvent {
  const factory _ChangeScreenType(final SurveyScreenType type) =
      _$_ChangeScreenType;

  SurveyScreenType get type;
  @JsonKey(ignore: true)
  _$$_ChangeScreenTypeCopyWith<_$_ChangeScreenType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetNextQuestionCopyWith<$Res> {
  factory _$$_SetNextQuestionCopyWith(
          _$_SetNextQuestion value, $Res Function(_$_SetNextQuestion) then) =
      __$$_SetNextQuestionCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SetNextQuestionCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_SetNextQuestion>
    implements _$$_SetNextQuestionCopyWith<$Res> {
  __$$_SetNextQuestionCopyWithImpl(
      _$_SetNextQuestion _value, $Res Function(_$_SetNextQuestion) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SetNextQuestion implements _SetNextQuestion {
  const _$_SetNextQuestion();

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.setNextQuestion()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SetNextQuestion);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return setNextQuestion();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return setNextQuestion?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (setNextQuestion != null) {
      return setNextQuestion();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return setNextQuestion(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return setNextQuestion?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (setNextQuestion != null) {
      return setNextQuestion(this);
    }
    return orElse();
  }
}

abstract class _SetNextQuestion implements WelcomeKitSurveyEvent {
  const factory _SetNextQuestion() = _$_SetNextQuestion;
}

/// @nodoc
abstract class _$$_SetNextScreenCopyWith<$Res> {
  factory _$$_SetNextScreenCopyWith(
          _$_SetNextScreen value, $Res Function(_$_SetNextScreen) then) =
      __$$_SetNextScreenCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SetNextScreenCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_SetNextScreen>
    implements _$$_SetNextScreenCopyWith<$Res> {
  __$$_SetNextScreenCopyWithImpl(
      _$_SetNextScreen _value, $Res Function(_$_SetNextScreen) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SetNextScreen implements _SetNextScreen {
  const _$_SetNextScreen();

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.setNextScreen()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SetNextScreen);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return setNextScreen();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return setNextScreen?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (setNextScreen != null) {
      return setNextScreen();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return setNextScreen(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return setNextScreen?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (setNextScreen != null) {
      return setNextScreen(this);
    }
    return orElse();
  }
}

abstract class _SetNextScreen implements WelcomeKitSurveyEvent {
  const factory _SetNextScreen() = _$_SetNextScreen;
}

/// @nodoc
abstract class _$$_PersonalizeSurveyCopyWith<$Res> {
  factory _$$_PersonalizeSurveyCopyWith(_$_PersonalizeSurvey value,
          $Res Function(_$_PersonalizeSurvey) then) =
      __$$_PersonalizeSurveyCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_PersonalizeSurveyCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_PersonalizeSurvey>
    implements _$$_PersonalizeSurveyCopyWith<$Res> {
  __$$_PersonalizeSurveyCopyWithImpl(
      _$_PersonalizeSurvey _value, $Res Function(_$_PersonalizeSurvey) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_PersonalizeSurvey implements _PersonalizeSurvey {
  const _$_PersonalizeSurvey();

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.personalizeSurvey()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_PersonalizeSurvey);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return personalizeSurvey();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return personalizeSurvey?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (personalizeSurvey != null) {
      return personalizeSurvey();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return personalizeSurvey(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return personalizeSurvey?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (personalizeSurvey != null) {
      return personalizeSurvey(this);
    }
    return orElse();
  }
}

abstract class _PersonalizeSurvey implements WelcomeKitSurveyEvent {
  const factory _PersonalizeSurvey() = _$_PersonalizeSurvey;
}

/// @nodoc
abstract class _$$_PostUserAnswerCopyWith<$Res> {
  factory _$$_PostUserAnswerCopyWith(
          _$_PostUserAnswer value, $Res Function(_$_PostUserAnswer) then) =
      __$$_PostUserAnswerCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_PostUserAnswerCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_PostUserAnswer>
    implements _$$_PostUserAnswerCopyWith<$Res> {
  __$$_PostUserAnswerCopyWithImpl(
      _$_PostUserAnswer _value, $Res Function(_$_PostUserAnswer) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_PostUserAnswer implements _PostUserAnswer {
  const _$_PostUserAnswer();

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.postUserAnswer()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_PostUserAnswer);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return postUserAnswer();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return postUserAnswer?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (postUserAnswer != null) {
      return postUserAnswer();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return postUserAnswer(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return postUserAnswer?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (postUserAnswer != null) {
      return postUserAnswer(this);
    }
    return orElse();
  }
}

abstract class _PostUserAnswer implements WelcomeKitSurveyEvent {
  const factory _PostUserAnswer() = _$_PostUserAnswer;
}

/// @nodoc
abstract class _$$_SetSingleAnswerCopyWith<$Res> {
  factory _$$_SetSingleAnswerCopyWith(
          _$_SetSingleAnswer value, $Res Function(_$_SetSingleAnswer) then) =
      __$$_SetSingleAnswerCopyWithImpl<$Res>;
  @useResult
  $Res call({WelcomeKitSurveyAnswer answer});

  $WelcomeKitSurveyAnswerCopyWith<$Res> get answer;
}

/// @nodoc
class __$$_SetSingleAnswerCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_SetSingleAnswer>
    implements _$$_SetSingleAnswerCopyWith<$Res> {
  __$$_SetSingleAnswerCopyWithImpl(
      _$_SetSingleAnswer _value, $Res Function(_$_SetSingleAnswer) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? answer = null,
  }) {
    return _then(_$_SetSingleAnswer(
      null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyAnswer,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $WelcomeKitSurveyAnswerCopyWith<$Res> get answer {
    return $WelcomeKitSurveyAnswerCopyWith<$Res>(_value.answer, (value) {
      return _then(_value.copyWith(answer: value));
    });
  }
}

/// @nodoc

class _$_SetSingleAnswer implements _SetSingleAnswer {
  const _$_SetSingleAnswer(this.answer);

  @override
  final WelcomeKitSurveyAnswer answer;

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.setSingleAnswer(answer: $answer)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetSingleAnswer &&
            (identical(other.answer, answer) || other.answer == answer));
  }

  @override
  int get hashCode => Object.hash(runtimeType, answer);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetSingleAnswerCopyWith<_$_SetSingleAnswer> get copyWith =>
      __$$_SetSingleAnswerCopyWithImpl<_$_SetSingleAnswer>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return setSingleAnswer(answer);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return setSingleAnswer?.call(answer);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (setSingleAnswer != null) {
      return setSingleAnswer(answer);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return setSingleAnswer(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return setSingleAnswer?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (setSingleAnswer != null) {
      return setSingleAnswer(this);
    }
    return orElse();
  }
}

abstract class _SetSingleAnswer implements WelcomeKitSurveyEvent {
  const factory _SetSingleAnswer(final WelcomeKitSurveyAnswer answer) =
      _$_SetSingleAnswer;

  WelcomeKitSurveyAnswer get answer;
  @JsonKey(ignore: true)
  _$$_SetSingleAnswerCopyWith<_$_SetSingleAnswer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_CheckSurveyProgressionCopyWith<$Res> {
  factory _$$_CheckSurveyProgressionCopyWith(_$_CheckSurveyProgression value,
          $Res Function(_$_CheckSurveyProgression) then) =
      __$$_CheckSurveyProgressionCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_CheckSurveyProgressionCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_CheckSurveyProgression>
    implements _$$_CheckSurveyProgressionCopyWith<$Res> {
  __$$_CheckSurveyProgressionCopyWithImpl(_$_CheckSurveyProgression _value,
      $Res Function(_$_CheckSurveyProgression) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_CheckSurveyProgression implements _CheckSurveyProgression {
  const _$_CheckSurveyProgression();

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.checkSurveyProgression()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckSurveyProgression);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return checkSurveyProgression();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return checkSurveyProgression?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (checkSurveyProgression != null) {
      return checkSurveyProgression();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return checkSurveyProgression(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return checkSurveyProgression?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (checkSurveyProgression != null) {
      return checkSurveyProgression(this);
    }
    return orElse();
  }
}

abstract class _CheckSurveyProgression implements WelcomeKitSurveyEvent {
  const factory _CheckSurveyProgression() = _$_CheckSurveyProgression;
}

/// @nodoc
abstract class _$$_InitDataCopyWith<$Res> {
  factory _$$_InitDataCopyWith(
          _$_InitData value, $Res Function(_$_InitData) then) =
      __$$_InitDataCopyWithImpl<$Res>;
  @useResult
  $Res call({TemplateId templateId, WelcomeKitSurveyDetail surveyDetail});
}

/// @nodoc
class __$$_InitDataCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_InitData>
    implements _$$_InitDataCopyWith<$Res> {
  __$$_InitDataCopyWithImpl(
      _$_InitData _value, $Res Function(_$_InitData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? templateId = null,
    Object? surveyDetail = null,
  }) {
    return _then(_$_InitData(
      templateId: null == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as TemplateId,
      surveyDetail: null == surveyDetail
          ? _value.surveyDetail
          : surveyDetail // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyDetail,
    ));
  }
}

/// @nodoc

class _$_InitData implements _InitData {
  const _$_InitData({required this.templateId, required this.surveyDetail});

  @override
  final TemplateId templateId;
  @override
  final WelcomeKitSurveyDetail surveyDetail;

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.initData(templateId: $templateId, surveyDetail: $surveyDetail)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InitData &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.surveyDetail, surveyDetail) ||
                other.surveyDetail == surveyDetail));
  }

  @override
  int get hashCode => Object.hash(runtimeType, templateId, surveyDetail);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitDataCopyWith<_$_InitData> get copyWith =>
      __$$_InitDataCopyWithImpl<_$_InitData>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return initData(templateId, surveyDetail);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return initData?.call(templateId, surveyDetail);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (initData != null) {
      return initData(templateId, surveyDetail);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return initData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return initData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (initData != null) {
      return initData(this);
    }
    return orElse();
  }
}

abstract class _InitData implements WelcomeKitSurveyEvent {
  const factory _InitData(
      {required final TemplateId templateId,
      required final WelcomeKitSurveyDetail surveyDetail}) = _$_InitData;

  TemplateId get templateId;
  WelcomeKitSurveyDetail get surveyDetail;
  @JsonKey(ignore: true)
  _$$_InitDataCopyWith<_$_InitData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetStateCopyWith<$Res> {
  factory _$$_ResetStateCopyWith(
          _$_ResetState value, $Res Function(_$_ResetState) then) =
      __$$_ResetStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetStateCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_ResetState>
    implements _$$_ResetStateCopyWith<$Res> {
  __$$_ResetStateCopyWithImpl(
      _$_ResetState _value, $Res Function(_$_ResetState) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetState implements _ResetState {
  const _$_ResetState();

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.resetState()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetState implements WelcomeKitSurveyEvent {
  const factory _ResetState() = _$_ResetState;
}

/// @nodoc
abstract class _$$_InsuranceHelpingScreenCopyWith<$Res> {
  factory _$$_InsuranceHelpingScreenCopyWith(_$_InsuranceHelpingScreen value,
          $Res Function(_$_InsuranceHelpingScreen) then) =
      __$$_InsuranceHelpingScreenCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InsuranceHelpingScreenCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyEventCopyWithImpl<$Res, _$_InsuranceHelpingScreen>
    implements _$$_InsuranceHelpingScreenCopyWith<$Res> {
  __$$_InsuranceHelpingScreenCopyWithImpl(_$_InsuranceHelpingScreen _value,
      $Res Function(_$_InsuranceHelpingScreen) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_InsuranceHelpingScreen implements _InsuranceHelpingScreen {
  const _$_InsuranceHelpingScreen();

  @override
  String toString() {
    return 'WelcomeKitSurveyEvent.insuranceHelpingScreen()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_InsuranceHelpingScreen);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        fetchSurvey,
    required TResult Function(SurveyScreenType type) changeScreenType,
    required TResult Function() setNextQuestion,
    required TResult Function() setNextScreen,
    required TResult Function() personalizeSurvey,
    required TResult Function() postUserAnswer,
    required TResult Function(WelcomeKitSurveyAnswer answer) setSingleAnswer,
    required TResult Function() checkSurveyProgression,
    required TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)
        initData,
    required TResult Function() resetState,
    required TResult Function() insuranceHelpingScreen,
  }) {
    return insuranceHelpingScreen();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult? Function(SurveyScreenType type)? changeScreenType,
    TResult? Function()? setNextQuestion,
    TResult? Function()? setNextScreen,
    TResult? Function()? personalizeSurvey,
    TResult? Function()? postUserAnswer,
    TResult? Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult? Function()? checkSurveyProgression,
    TResult? Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult? Function()? resetState,
    TResult? Function()? insuranceHelpingScreen,
  }) {
    return insuranceHelpingScreen?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        fetchSurvey,
    TResult Function(SurveyScreenType type)? changeScreenType,
    TResult Function()? setNextQuestion,
    TResult Function()? setNextScreen,
    TResult Function()? personalizeSurvey,
    TResult Function()? postUserAnswer,
    TResult Function(WelcomeKitSurveyAnswer answer)? setSingleAnswer,
    TResult Function()? checkSurveyProgression,
    TResult Function(
            TemplateId templateId, WelcomeKitSurveyDetail surveyDetail)?
        initData,
    TResult Function()? resetState,
    TResult Function()? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (insuranceHelpingScreen != null) {
      return insuranceHelpingScreen();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_FetchSurvey value) fetchSurvey,
    required TResult Function(_ChangeScreenType value) changeScreenType,
    required TResult Function(_SetNextQuestion value) setNextQuestion,
    required TResult Function(_SetNextScreen value) setNextScreen,
    required TResult Function(_PersonalizeSurvey value) personalizeSurvey,
    required TResult Function(_PostUserAnswer value) postUserAnswer,
    required TResult Function(_SetSingleAnswer value) setSingleAnswer,
    required TResult Function(_CheckSurveyProgression value)
        checkSurveyProgression,
    required TResult Function(_InitData value) initData,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InsuranceHelpingScreen value)
        insuranceHelpingScreen,
  }) {
    return insuranceHelpingScreen(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_FetchSurvey value)? fetchSurvey,
    TResult? Function(_ChangeScreenType value)? changeScreenType,
    TResult? Function(_SetNextQuestion value)? setNextQuestion,
    TResult? Function(_SetNextScreen value)? setNextScreen,
    TResult? Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult? Function(_PostUserAnswer value)? postUserAnswer,
    TResult? Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult? Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult? Function(_InitData value)? initData,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
  }) {
    return insuranceHelpingScreen?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_FetchSurvey value)? fetchSurvey,
    TResult Function(_ChangeScreenType value)? changeScreenType,
    TResult Function(_SetNextQuestion value)? setNextQuestion,
    TResult Function(_SetNextScreen value)? setNextScreen,
    TResult Function(_PersonalizeSurvey value)? personalizeSurvey,
    TResult Function(_PostUserAnswer value)? postUserAnswer,
    TResult Function(_SetSingleAnswer value)? setSingleAnswer,
    TResult Function(_CheckSurveyProgression value)? checkSurveyProgression,
    TResult Function(_InitData value)? initData,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InsuranceHelpingScreen value)? insuranceHelpingScreen,
    required TResult orElse(),
  }) {
    if (insuranceHelpingScreen != null) {
      return insuranceHelpingScreen(this);
    }
    return orElse();
  }
}

abstract class _InsuranceHelpingScreen implements WelcomeKitSurveyEvent {
  const factory _InsuranceHelpingScreen() = _$_InsuranceHelpingScreen;
}

/// @nodoc
mixin _$WelcomeKitSurveyState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isQuestionLoading => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  bool get isSurveyCompleted => throw _privateConstructorUsedError;
  String get templateId => throw _privateConstructorUsedError;
  String get surveyId => throw _privateConstructorUsedError;
  String? get surveyProductId => throw _privateConstructorUsedError;
  String? get surveySessionId => throw _privateConstructorUsedError;
  WelcomeKitSurvey? get survey => throw _privateConstructorUsedError;
  WelcomeKitSurvey? get personalizedSurvey =>
      throw _privateConstructorUsedError;
  WelcomeKitSurveyQuestion? get currentQuestion =>
      throw _privateConstructorUsedError;
  WelcomeKitSurveyQuestion? get currentPersonalizedQuestion =>
      throw _privateConstructorUsedError;
  WelcomeKitSurveyAnswer? get selectedAnswer =>
      throw _privateConstructorUsedError;
  List<WelcomeKitSurveyAnswer> get selectedMultipleAnswer =>
      throw _privateConstructorUsedError;
  String? get openAnswer => throw _privateConstructorUsedError;
  int? get selectedRating => throw _privateConstructorUsedError;
  SurveyScreenType get surveyScreenType => throw _privateConstructorUsedError;
  Map<int, String> get starRatingTexts => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get productName => throw _privateConstructorUsedError;
  String? get insuranceName => throw _privateConstructorUsedError;
  String? get disbursementAmount => throw _privateConstructorUsedError;
  bool get isClosingHelp => throw _privateConstructorUsedError;
  List<InsuranceInfo> get insurances => throw _privateConstructorUsedError;
  WelcomeKitFailure? get submitAnswerFailure =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $WelcomeKitSurveyStateCopyWith<WelcomeKitSurveyState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WelcomeKitSurveyStateCopyWith<$Res> {
  factory $WelcomeKitSurveyStateCopyWith(WelcomeKitSurveyState value,
          $Res Function(WelcomeKitSurveyState) then) =
      _$WelcomeKitSurveyStateCopyWithImpl<$Res, WelcomeKitSurveyState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isQuestionLoading,
      bool isError,
      bool isSurveyCompleted,
      String templateId,
      String surveyId,
      String? surveyProductId,
      String? surveySessionId,
      WelcomeKitSurvey? survey,
      WelcomeKitSurvey? personalizedSurvey,
      WelcomeKitSurveyQuestion? currentQuestion,
      WelcomeKitSurveyQuestion? currentPersonalizedQuestion,
      WelcomeKitSurveyAnswer? selectedAnswer,
      List<WelcomeKitSurveyAnswer> selectedMultipleAnswer,
      String? openAnswer,
      int? selectedRating,
      SurveyScreenType surveyScreenType,
      Map<int, String> starRatingTexts,
      String? firstName,
      String? lastName,
      String? email,
      String? productName,
      String? insuranceName,
      String? disbursementAmount,
      bool isClosingHelp,
      List<InsuranceInfo> insurances,
      WelcomeKitFailure? submitAnswerFailure});

  $WelcomeKitSurveyCopyWith<$Res>? get survey;
  $WelcomeKitSurveyCopyWith<$Res>? get personalizedSurvey;
  $WelcomeKitSurveyQuestionCopyWith<$Res>? get currentQuestion;
  $WelcomeKitSurveyQuestionCopyWith<$Res>? get currentPersonalizedQuestion;
  $WelcomeKitSurveyAnswerCopyWith<$Res>? get selectedAnswer;
}

/// @nodoc
class _$WelcomeKitSurveyStateCopyWithImpl<$Res,
        $Val extends WelcomeKitSurveyState>
    implements $WelcomeKitSurveyStateCopyWith<$Res> {
  _$WelcomeKitSurveyStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isQuestionLoading = null,
    Object? isError = null,
    Object? isSurveyCompleted = null,
    Object? templateId = null,
    Object? surveyId = null,
    Object? surveyProductId = freezed,
    Object? surveySessionId = freezed,
    Object? survey = freezed,
    Object? personalizedSurvey = freezed,
    Object? currentQuestion = freezed,
    Object? currentPersonalizedQuestion = freezed,
    Object? selectedAnswer = freezed,
    Object? selectedMultipleAnswer = null,
    Object? openAnswer = freezed,
    Object? selectedRating = freezed,
    Object? surveyScreenType = null,
    Object? starRatingTexts = null,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? productName = freezed,
    Object? insuranceName = freezed,
    Object? disbursementAmount = freezed,
    Object? isClosingHelp = null,
    Object? insurances = null,
    Object? submitAnswerFailure = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isQuestionLoading: null == isQuestionLoading
          ? _value.isQuestionLoading
          : isQuestionLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSurveyCompleted: null == isSurveyCompleted
          ? _value.isSurveyCompleted
          : isSurveyCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      templateId: null == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyId: null == surveyId
          ? _value.surveyId
          : surveyId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyProductId: freezed == surveyProductId
          ? _value.surveyProductId
          : surveyProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      surveySessionId: freezed == surveySessionId
          ? _value.surveySessionId
          : surveySessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      survey: freezed == survey
          ? _value.survey
          : survey // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurvey?,
      personalizedSurvey: freezed == personalizedSurvey
          ? _value.personalizedSurvey
          : personalizedSurvey // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurvey?,
      currentQuestion: freezed == currentQuestion
          ? _value.currentQuestion
          : currentQuestion // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyQuestion?,
      currentPersonalizedQuestion: freezed == currentPersonalizedQuestion
          ? _value.currentPersonalizedQuestion
          : currentPersonalizedQuestion // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyQuestion?,
      selectedAnswer: freezed == selectedAnswer
          ? _value.selectedAnswer
          : selectedAnswer // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyAnswer?,
      selectedMultipleAnswer: null == selectedMultipleAnswer
          ? _value.selectedMultipleAnswer
          : selectedMultipleAnswer // ignore: cast_nullable_to_non_nullable
              as List<WelcomeKitSurveyAnswer>,
      openAnswer: freezed == openAnswer
          ? _value.openAnswer
          : openAnswer // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedRating: freezed == selectedRating
          ? _value.selectedRating
          : selectedRating // ignore: cast_nullable_to_non_nullable
              as int?,
      surveyScreenType: null == surveyScreenType
          ? _value.surveyScreenType
          : surveyScreenType // ignore: cast_nullable_to_non_nullable
              as SurveyScreenType,
      starRatingTexts: null == starRatingTexts
          ? _value.starRatingTexts
          : starRatingTexts // ignore: cast_nullable_to_non_nullable
              as Map<int, String>,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      insuranceName: freezed == insuranceName
          ? _value.insuranceName
          : insuranceName // ignore: cast_nullable_to_non_nullable
              as String?,
      disbursementAmount: freezed == disbursementAmount
          ? _value.disbursementAmount
          : disbursementAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      isClosingHelp: null == isClosingHelp
          ? _value.isClosingHelp
          : isClosingHelp // ignore: cast_nullable_to_non_nullable
              as bool,
      insurances: null == insurances
          ? _value.insurances
          : insurances // ignore: cast_nullable_to_non_nullable
              as List<InsuranceInfo>,
      submitAnswerFailure: freezed == submitAnswerFailure
          ? _value.submitAnswerFailure
          : submitAnswerFailure // ignore: cast_nullable_to_non_nullable
              as WelcomeKitFailure?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $WelcomeKitSurveyCopyWith<$Res>? get survey {
    if (_value.survey == null) {
      return null;
    }

    return $WelcomeKitSurveyCopyWith<$Res>(_value.survey!, (value) {
      return _then(_value.copyWith(survey: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $WelcomeKitSurveyCopyWith<$Res>? get personalizedSurvey {
    if (_value.personalizedSurvey == null) {
      return null;
    }

    return $WelcomeKitSurveyCopyWith<$Res>(_value.personalizedSurvey!, (value) {
      return _then(_value.copyWith(personalizedSurvey: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $WelcomeKitSurveyQuestionCopyWith<$Res>? get currentQuestion {
    if (_value.currentQuestion == null) {
      return null;
    }

    return $WelcomeKitSurveyQuestionCopyWith<$Res>(_value.currentQuestion!,
        (value) {
      return _then(_value.copyWith(currentQuestion: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $WelcomeKitSurveyQuestionCopyWith<$Res>? get currentPersonalizedQuestion {
    if (_value.currentPersonalizedQuestion == null) {
      return null;
    }

    return $WelcomeKitSurveyQuestionCopyWith<$Res>(
        _value.currentPersonalizedQuestion!, (value) {
      return _then(_value.copyWith(currentPersonalizedQuestion: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $WelcomeKitSurveyAnswerCopyWith<$Res>? get selectedAnswer {
    if (_value.selectedAnswer == null) {
      return null;
    }

    return $WelcomeKitSurveyAnswerCopyWith<$Res>(_value.selectedAnswer!,
        (value) {
      return _then(_value.copyWith(selectedAnswer: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_WelcomeKitSurveyStateCopyWith<$Res>
    implements $WelcomeKitSurveyStateCopyWith<$Res> {
  factory _$$_WelcomeKitSurveyStateCopyWith(_$_WelcomeKitSurveyState value,
          $Res Function(_$_WelcomeKitSurveyState) then) =
      __$$_WelcomeKitSurveyStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isQuestionLoading,
      bool isError,
      bool isSurveyCompleted,
      String templateId,
      String surveyId,
      String? surveyProductId,
      String? surveySessionId,
      WelcomeKitSurvey? survey,
      WelcomeKitSurvey? personalizedSurvey,
      WelcomeKitSurveyQuestion? currentQuestion,
      WelcomeKitSurveyQuestion? currentPersonalizedQuestion,
      WelcomeKitSurveyAnswer? selectedAnswer,
      List<WelcomeKitSurveyAnswer> selectedMultipleAnswer,
      String? openAnswer,
      int? selectedRating,
      SurveyScreenType surveyScreenType,
      Map<int, String> starRatingTexts,
      String? firstName,
      String? lastName,
      String? email,
      String? productName,
      String? insuranceName,
      String? disbursementAmount,
      bool isClosingHelp,
      List<InsuranceInfo> insurances,
      WelcomeKitFailure? submitAnswerFailure});

  @override
  $WelcomeKitSurveyCopyWith<$Res>? get survey;
  @override
  $WelcomeKitSurveyCopyWith<$Res>? get personalizedSurvey;
  @override
  $WelcomeKitSurveyQuestionCopyWith<$Res>? get currentQuestion;
  @override
  $WelcomeKitSurveyQuestionCopyWith<$Res>? get currentPersonalizedQuestion;
  @override
  $WelcomeKitSurveyAnswerCopyWith<$Res>? get selectedAnswer;
}

/// @nodoc
class __$$_WelcomeKitSurveyStateCopyWithImpl<$Res>
    extends _$WelcomeKitSurveyStateCopyWithImpl<$Res, _$_WelcomeKitSurveyState>
    implements _$$_WelcomeKitSurveyStateCopyWith<$Res> {
  __$$_WelcomeKitSurveyStateCopyWithImpl(_$_WelcomeKitSurveyState _value,
      $Res Function(_$_WelcomeKitSurveyState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isQuestionLoading = null,
    Object? isError = null,
    Object? isSurveyCompleted = null,
    Object? templateId = null,
    Object? surveyId = null,
    Object? surveyProductId = freezed,
    Object? surveySessionId = freezed,
    Object? survey = freezed,
    Object? personalizedSurvey = freezed,
    Object? currentQuestion = freezed,
    Object? currentPersonalizedQuestion = freezed,
    Object? selectedAnswer = freezed,
    Object? selectedMultipleAnswer = null,
    Object? openAnswer = freezed,
    Object? selectedRating = freezed,
    Object? surveyScreenType = null,
    Object? starRatingTexts = null,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? productName = freezed,
    Object? insuranceName = freezed,
    Object? disbursementAmount = freezed,
    Object? isClosingHelp = null,
    Object? insurances = null,
    Object? submitAnswerFailure = freezed,
  }) {
    return _then(_$_WelcomeKitSurveyState(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isQuestionLoading: null == isQuestionLoading
          ? _value.isQuestionLoading
          : isQuestionLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isSurveyCompleted: null == isSurveyCompleted
          ? _value.isSurveyCompleted
          : isSurveyCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      templateId: null == templateId
          ? _value.templateId
          : templateId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyId: null == surveyId
          ? _value.surveyId
          : surveyId // ignore: cast_nullable_to_non_nullable
              as String,
      surveyProductId: freezed == surveyProductId
          ? _value.surveyProductId
          : surveyProductId // ignore: cast_nullable_to_non_nullable
              as String?,
      surveySessionId: freezed == surveySessionId
          ? _value.surveySessionId
          : surveySessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      survey: freezed == survey
          ? _value.survey
          : survey // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurvey?,
      personalizedSurvey: freezed == personalizedSurvey
          ? _value.personalizedSurvey
          : personalizedSurvey // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurvey?,
      currentQuestion: freezed == currentQuestion
          ? _value.currentQuestion
          : currentQuestion // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyQuestion?,
      currentPersonalizedQuestion: freezed == currentPersonalizedQuestion
          ? _value.currentPersonalizedQuestion
          : currentPersonalizedQuestion // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyQuestion?,
      selectedAnswer: freezed == selectedAnswer
          ? _value.selectedAnswer
          : selectedAnswer // ignore: cast_nullable_to_non_nullable
              as WelcomeKitSurveyAnswer?,
      selectedMultipleAnswer: null == selectedMultipleAnswer
          ? _value._selectedMultipleAnswer
          : selectedMultipleAnswer // ignore: cast_nullable_to_non_nullable
              as List<WelcomeKitSurveyAnswer>,
      openAnswer: freezed == openAnswer
          ? _value.openAnswer
          : openAnswer // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedRating: freezed == selectedRating
          ? _value.selectedRating
          : selectedRating // ignore: cast_nullable_to_non_nullable
              as int?,
      surveyScreenType: null == surveyScreenType
          ? _value.surveyScreenType
          : surveyScreenType // ignore: cast_nullable_to_non_nullable
              as SurveyScreenType,
      starRatingTexts: null == starRatingTexts
          ? _value._starRatingTexts
          : starRatingTexts // ignore: cast_nullable_to_non_nullable
              as Map<int, String>,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      productName: freezed == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String?,
      insuranceName: freezed == insuranceName
          ? _value.insuranceName
          : insuranceName // ignore: cast_nullable_to_non_nullable
              as String?,
      disbursementAmount: freezed == disbursementAmount
          ? _value.disbursementAmount
          : disbursementAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      isClosingHelp: null == isClosingHelp
          ? _value.isClosingHelp
          : isClosingHelp // ignore: cast_nullable_to_non_nullable
              as bool,
      insurances: null == insurances
          ? _value._insurances
          : insurances // ignore: cast_nullable_to_non_nullable
              as List<InsuranceInfo>,
      submitAnswerFailure: freezed == submitAnswerFailure
          ? _value.submitAnswerFailure
          : submitAnswerFailure // ignore: cast_nullable_to_non_nullable
              as WelcomeKitFailure?,
    ));
  }
}

/// @nodoc

class _$_WelcomeKitSurveyState implements _WelcomeKitSurveyState {
  const _$_WelcomeKitSurveyState(
      {this.isLoading = false,
      this.isQuestionLoading = false,
      this.isError = false,
      this.isSurveyCompleted = false,
      this.templateId = '',
      this.surveyId = '',
      this.surveyProductId = null,
      this.surveySessionId = null,
      this.survey = null,
      this.personalizedSurvey = null,
      this.currentQuestion = null,
      this.currentPersonalizedQuestion = null,
      this.selectedAnswer = null,
      final List<WelcomeKitSurveyAnswer> selectedMultipleAnswer = const [],
      this.openAnswer = null,
      this.selectedRating = null,
      this.surveyScreenType = SurveyScreenType.openingScreen,
      final Map<int, String> starRatingTexts = const {},
      this.firstName = null,
      this.lastName = null,
      this.email = null,
      this.productName = null,
      this.insuranceName = null,
      this.disbursementAmount = null,
      this.isClosingHelp = false,
      final List<InsuranceInfo> insurances = const [],
      this.submitAnswerFailure = null})
      : _selectedMultipleAnswer = selectedMultipleAnswer,
        _starRatingTexts = starRatingTexts,
        _insurances = insurances;

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isQuestionLoading;
  @override
  @JsonKey()
  final bool isError;
  @override
  @JsonKey()
  final bool isSurveyCompleted;
  @override
  @JsonKey()
  final String templateId;
  @override
  @JsonKey()
  final String surveyId;
  @override
  @JsonKey()
  final String? surveyProductId;
  @override
  @JsonKey()
  final String? surveySessionId;
  @override
  @JsonKey()
  final WelcomeKitSurvey? survey;
  @override
  @JsonKey()
  final WelcomeKitSurvey? personalizedSurvey;
  @override
  @JsonKey()
  final WelcomeKitSurveyQuestion? currentQuestion;
  @override
  @JsonKey()
  final WelcomeKitSurveyQuestion? currentPersonalizedQuestion;
  @override
  @JsonKey()
  final WelcomeKitSurveyAnswer? selectedAnswer;
  final List<WelcomeKitSurveyAnswer> _selectedMultipleAnswer;
  @override
  @JsonKey()
  List<WelcomeKitSurveyAnswer> get selectedMultipleAnswer {
    if (_selectedMultipleAnswer is EqualUnmodifiableListView)
      return _selectedMultipleAnswer;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedMultipleAnswer);
  }

  @override
  @JsonKey()
  final String? openAnswer;
  @override
  @JsonKey()
  final int? selectedRating;
  @override
  @JsonKey()
  final SurveyScreenType surveyScreenType;
  final Map<int, String> _starRatingTexts;
  @override
  @JsonKey()
  Map<int, String> get starRatingTexts {
    if (_starRatingTexts is EqualUnmodifiableMapView) return _starRatingTexts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_starRatingTexts);
  }

  @override
  @JsonKey()
  final String? firstName;
  @override
  @JsonKey()
  final String? lastName;
  @override
  @JsonKey()
  final String? email;
  @override
  @JsonKey()
  final String? productName;
  @override
  @JsonKey()
  final String? insuranceName;
  @override
  @JsonKey()
  final String? disbursementAmount;
  @override
  @JsonKey()
  final bool isClosingHelp;
  final List<InsuranceInfo> _insurances;
  @override
  @JsonKey()
  List<InsuranceInfo> get insurances {
    if (_insurances is EqualUnmodifiableListView) return _insurances;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_insurances);
  }

  @override
  @JsonKey()
  final WelcomeKitFailure? submitAnswerFailure;

  @override
  String toString() {
    return 'WelcomeKitSurveyState(isLoading: $isLoading, isQuestionLoading: $isQuestionLoading, isError: $isError, isSurveyCompleted: $isSurveyCompleted, templateId: $templateId, surveyId: $surveyId, surveyProductId: $surveyProductId, surveySessionId: $surveySessionId, survey: $survey, personalizedSurvey: $personalizedSurvey, currentQuestion: $currentQuestion, currentPersonalizedQuestion: $currentPersonalizedQuestion, selectedAnswer: $selectedAnswer, selectedMultipleAnswer: $selectedMultipleAnswer, openAnswer: $openAnswer, selectedRating: $selectedRating, surveyScreenType: $surveyScreenType, starRatingTexts: $starRatingTexts, firstName: $firstName, lastName: $lastName, email: $email, productName: $productName, insuranceName: $insuranceName, disbursementAmount: $disbursementAmount, isClosingHelp: $isClosingHelp, insurances: $insurances, submitAnswerFailure: $submitAnswerFailure)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WelcomeKitSurveyState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isQuestionLoading, isQuestionLoading) ||
                other.isQuestionLoading == isQuestionLoading) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.isSurveyCompleted, isSurveyCompleted) ||
                other.isSurveyCompleted == isSurveyCompleted) &&
            (identical(other.templateId, templateId) ||
                other.templateId == templateId) &&
            (identical(other.surveyId, surveyId) ||
                other.surveyId == surveyId) &&
            (identical(other.surveyProductId, surveyProductId) ||
                other.surveyProductId == surveyProductId) &&
            (identical(other.surveySessionId, surveySessionId) ||
                other.surveySessionId == surveySessionId) &&
            (identical(other.survey, survey) || other.survey == survey) &&
            (identical(other.personalizedSurvey, personalizedSurvey) ||
                other.personalizedSurvey == personalizedSurvey) &&
            (identical(other.currentQuestion, currentQuestion) ||
                other.currentQuestion == currentQuestion) &&
            (identical(other.currentPersonalizedQuestion,
                    currentPersonalizedQuestion) ||
                other.currentPersonalizedQuestion ==
                    currentPersonalizedQuestion) &&
            (identical(other.selectedAnswer, selectedAnswer) ||
                other.selectedAnswer == selectedAnswer) &&
            const DeepCollectionEquality().equals(
                other._selectedMultipleAnswer, _selectedMultipleAnswer) &&
            (identical(other.openAnswer, openAnswer) ||
                other.openAnswer == openAnswer) &&
            (identical(other.selectedRating, selectedRating) ||
                other.selectedRating == selectedRating) &&
            (identical(other.surveyScreenType, surveyScreenType) ||
                other.surveyScreenType == surveyScreenType) &&
            const DeepCollectionEquality()
                .equals(other._starRatingTexts, _starRatingTexts) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.insuranceName, insuranceName) ||
                other.insuranceName == insuranceName) &&
            (identical(other.disbursementAmount, disbursementAmount) ||
                other.disbursementAmount == disbursementAmount) &&
            (identical(other.isClosingHelp, isClosingHelp) ||
                other.isClosingHelp == isClosingHelp) &&
            const DeepCollectionEquality()
                .equals(other._insurances, _insurances) &&
            (identical(other.submitAnswerFailure, submitAnswerFailure) ||
                other.submitAnswerFailure == submitAnswerFailure));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        isLoading,
        isQuestionLoading,
        isError,
        isSurveyCompleted,
        templateId,
        surveyId,
        surveyProductId,
        surveySessionId,
        survey,
        personalizedSurvey,
        currentQuestion,
        currentPersonalizedQuestion,
        selectedAnswer,
        const DeepCollectionEquality().hash(_selectedMultipleAnswer),
        openAnswer,
        selectedRating,
        surveyScreenType,
        const DeepCollectionEquality().hash(_starRatingTexts),
        firstName,
        lastName,
        email,
        productName,
        insuranceName,
        disbursementAmount,
        isClosingHelp,
        const DeepCollectionEquality().hash(_insurances),
        submitAnswerFailure
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WelcomeKitSurveyStateCopyWith<_$_WelcomeKitSurveyState> get copyWith =>
      __$$_WelcomeKitSurveyStateCopyWithImpl<_$_WelcomeKitSurveyState>(
          this, _$identity);
}

abstract class _WelcomeKitSurveyState implements WelcomeKitSurveyState {
  const factory _WelcomeKitSurveyState(
      {final bool isLoading,
      final bool isQuestionLoading,
      final bool isError,
      final bool isSurveyCompleted,
      final String templateId,
      final String surveyId,
      final String? surveyProductId,
      final String? surveySessionId,
      final WelcomeKitSurvey? survey,
      final WelcomeKitSurvey? personalizedSurvey,
      final WelcomeKitSurveyQuestion? currentQuestion,
      final WelcomeKitSurveyQuestion? currentPersonalizedQuestion,
      final WelcomeKitSurveyAnswer? selectedAnswer,
      final List<WelcomeKitSurveyAnswer> selectedMultipleAnswer,
      final String? openAnswer,
      final int? selectedRating,
      final SurveyScreenType surveyScreenType,
      final Map<int, String> starRatingTexts,
      final String? firstName,
      final String? lastName,
      final String? email,
      final String? productName,
      final String? insuranceName,
      final String? disbursementAmount,
      final bool isClosingHelp,
      final List<InsuranceInfo> insurances,
      final WelcomeKitFailure? submitAnswerFailure}) = _$_WelcomeKitSurveyState;

  @override
  bool get isLoading;
  @override
  bool get isQuestionLoading;
  @override
  bool get isError;
  @override
  bool get isSurveyCompleted;
  @override
  String get templateId;
  @override
  String get surveyId;
  @override
  String? get surveyProductId;
  @override
  String? get surveySessionId;
  @override
  WelcomeKitSurvey? get survey;
  @override
  WelcomeKitSurvey? get personalizedSurvey;
  @override
  WelcomeKitSurveyQuestion? get currentQuestion;
  @override
  WelcomeKitSurveyQuestion? get currentPersonalizedQuestion;
  @override
  WelcomeKitSurveyAnswer? get selectedAnswer;
  @override
  List<WelcomeKitSurveyAnswer> get selectedMultipleAnswer;
  @override
  String? get openAnswer;
  @override
  int? get selectedRating;
  @override
  SurveyScreenType get surveyScreenType;
  @override
  Map<int, String> get starRatingTexts;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get email;
  @override
  String? get productName;
  @override
  String? get insuranceName;
  @override
  String? get disbursementAmount;
  @override
  bool get isClosingHelp;
  @override
  List<InsuranceInfo> get insurances;
  @override
  WelcomeKitFailure? get submitAnswerFailure;
  @override
  @JsonKey(ignore: true)
  _$$_WelcomeKitSurveyStateCopyWith<_$_WelcomeKitSurveyState> get copyWith =>
      throw _privateConstructorUsedError;
}
