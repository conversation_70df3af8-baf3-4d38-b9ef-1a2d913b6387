// DO NOT EDIT. This is code generated via package:gen_lang/generate.dart

import 'dart:async';

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';
// ignore: implementation_imports
import 'package:intl/src/intl_helpers.dart';

final _$vi_VN = $vi_VN();

class $vi_VN extends MessageLookupByLibrary {
  get localeName => 'vi_VN';
  
  final Map<String, dynamic> messages = <String, dynamic>{
		"capp_welcome_kit.i_know" : MessageLookupByLibrary.simpleMessage("Tôi đã hiểu"),
		"capp_welcome_kit.concern_about_insurance" : MessageLookupByLibrary.simpleMessage("Thắc mắc về Bảo hiểm"),
		"capp_welcome_kit.view_insurance_info" : MessageLookupByLibrary.simpleMessage("(Xem thông tin về gói Bảo hiểm)"),
		"capp_welcome_kit.press_here" : MessageLookupByLibrary.simpleMessage("Ấn vào đây"),
		"capp_welcome_kit.survey_opening_view_title" : MessageLookupByLibrary.simpleMessage("Home Credit xin cảm ơn anh/chị đã sử dụng dịch vụ trả góp của Công ty."),
		"capp_welcome_kit.survey_opening_view_body" : MessageLookupByLibrary.simpleMessage("Nhằm tuân thủ theo yêu cầu của Ngân hàng nhà nước về việc giám sát mục đích sử dụng vốn vay cũng như nâng cao chất lượng dịch vụ, anh/chị vui lòng dành 2 phút để hoàn thành bản khảo sát này trước khi tiếp tục trải nghiệm ứng dụng."),
		"capp_welcome_kit.survey_opening_view_start_button" : MessageLookupByLibrary.simpleMessage("Bắt đầu"),
		"capp_welcome_kit.survey_closing_view_title" : MessageLookupByLibrary.simpleMessage("Cảm ơn phản hồi của bạn"),
		"capp_welcome_kit.survey_closing_view_body" : MessageLookupByLibrary.simpleMessage("Việc phản hồi sẽ giúp chúng tôi cải thiện chất lượng sản phẩm tốt hơn"),
		"capp_welcome_kit.survey_closing_view_done_button" : MessageLookupByLibrary.simpleMessage("Xong"),
		"capp_welcome_kit.survey_screen_title" : MessageLookupByLibrary.simpleMessage("Khảo sát"),
		"capp_welcome_kit.survey_screen_open_question_input_label" : MessageLookupByLibrary.simpleMessage("Please type your answer here"),
		"capp_welcome_kit.survey_screen_continue_btn" : MessageLookupByLibrary.simpleMessage("Tiếp tục"),
		"capp_welcome_kit.survey_demo_screen_title" : MessageLookupByLibrary.simpleMessage("Welcome Kit Survey"),
		"capp_welcome_kit.survey_demo_view_open_button" : MessageLookupByLibrary.simpleMessage("Open survey"),
		"capp_welcome_kit.survey_demo_view_choose" : MessageLookupByLibrary.simpleMessage("Choose Survey"),
		"capp_welcome_kit.survey_error_general_title" : MessageLookupByLibrary.simpleMessage("Có vấn đề đã xảy ra"),
		"capp_welcome_kit.survey_error_general_desc" : MessageLookupByLibrary.simpleMessage("Vui lòng thử lại"),
		"capp_welcome_kit.survey_error_not_found_title" : MessageLookupByLibrary.simpleMessage("Khảo sát hiện không khả dụng"),
		"capp_welcome_kit.survey_error_not_found_desc" : MessageLookupByLibrary.simpleMessage("Xin lỗi! Hiện tại khảo sát này đã kết thúc"),
		"capp_welcome_kit.survey_error_close" : MessageLookupByLibrary.simpleMessage("Đóng"),
		"capp_welcome_kit.survey_error_reload" : MessageLookupByLibrary.simpleMessage("Thử lại"),
		"capp_welcome_kit.survey_opening_title" : (dynamic gender, dynamic customerName) => "Cảm ơn ${gender} ${customerName} đã chọn Home Credit cho nhu cầu tài chính của mình",
		"capp_welcome_kit.survey_opening_text" : MessageLookupByLibrary.simpleMessage("Để xác nhận thông tin khoản vay, vui lòng dành 30 giây hoàn thành khảo sát này nhé!"),
		"capp_welcome_kit.survey_closing_title" : (dynamic gender) => "Cảm ơn ${gender} đã dành thời gian hoàn thành khảo sát.",
		"capp_welcome_kit.survey_closing_text" : (dynamic gender) => "Chúc ${gender} một ngày tốt lành!",
		"capp_welcome_kit.survey_closing_help_title" : (dynamic gender) => "Nếu ${gender} cần thêm thông tin về Bảo hiểm!\nVui lòng Ấn vào đây để được hỗ trợ.",
		"capp_welcome_kit.pos_loan_question_1" : (dynamic date, dynamic gender, dynamic product) => "Vào ngày ${date}, ${gender} đã vay trả góp của Home Credit để mua sản phẩm ${product} phải không?",
		"capp_welcome_kit.pos_loan_question_1_answer_1" : MessageLookupByLibrary.simpleMessage("Đúng"),
		"capp_welcome_kit.pos_loan_question_1_answer_2" : MessageLookupByLibrary.simpleMessage("Không đúng/ Không vay"),
		"capp_welcome_kit.pos_loan_question_2" : (dynamic gender, dynamic insuranceName, dynamic gender2) => "Bên cạnh khoản vay trả góp, ${gender} có tham gia ${insuranceName}. \n\n${gender2} có biết mình đã tham gia (các) bảo hiểm này không?",
		"capp_welcome_kit.pos_loan_question_2_answer_1" : MessageLookupByLibrary.simpleMessage("Đã biết"),
		"capp_welcome_kit.pos_loan_question_2_answer_2" : MessageLookupByLibrary.simpleMessage("Không biết/ Không mua"),
		"capp_welcome_kit.pos_loan_question_3" : (dynamic gender, dynamic customerName) => "Home Credit có hỗ trợ khoản vay lãi suất ưu đãi cho các Khách hàng uy tín. ${gender} ${customerName} có quan tâm đến ưu đãi này không?",
		"capp_welcome_kit.pos_loan_question_3_answer_1" : MessageLookupByLibrary.simpleMessage("Có"),
		"capp_welcome_kit.pos_loan_question_3_answer_2" : MessageLookupByLibrary.simpleMessage("Không"),
		"capp_welcome_kit.big_ticket_question_1" : (dynamic date, dynamic gender, dynamic amount) => "Vào ngày ${date}, ${gender} đã ký hợp đồng vay tiền mặt với Home Credit và được giải ngân ${amount} phải không?",
		"capp_welcome_kit.big_ticket_question_1_answer_1" : MessageLookupByLibrary.simpleMessage("Đúng"),
		"capp_welcome_kit.big_ticket_question_1_answer_2" : MessageLookupByLibrary.simpleMessage("Không đúng/ Không vay"),
		"capp_welcome_kit.big_ticket_question_2" : (dynamic gender) => "Tại thời điểm làm hồ sơ, ${gender} đã nộp loại chứng từ nào để chứng minh khoản chi trả của mình?",
		"capp_welcome_kit.big_ticket_question_2_answer_1" : MessageLookupByLibrary.simpleMessage("Hóa đơn đỏ"),
		"capp_welcome_kit.big_ticket_question_2_answer_2" : MessageLookupByLibrary.simpleMessage("Hóa đơn bán lẻ và hợp đồng dịch vụ"),
		"capp_welcome_kit.big_ticket_question_2_answer_3" : MessageLookupByLibrary.simpleMessage("Không nhớ"),
		"capp_welcome_kit.big_ticket_question_3" : (dynamic gender, dynamic insuranceName, dynamic gender2) => "Bên cạnh khoản vay trả góp, ${gender} có tham gia ${insuranceName}. \n\n${gender2} có biết mình đã tham gia (các) bảo hiểm này không?",
		"capp_welcome_kit.big_ticket_question_3_answer_1" : MessageLookupByLibrary.simpleMessage("Đã biết"),
		"capp_welcome_kit.big_ticket_question_3_answer_2" : MessageLookupByLibrary.simpleMessage("Không biết/ Không mua"),
		"capp_welcome_kit.big_ticket_question_4" : (dynamic gender) => "${gender} đã hoặc dự định sử dụng khoản vay cho mục đích nào sau đây?",
		"capp_welcome_kit.cash_loan_question_1" : (dynamic date, dynamic gender, dynamic amount) => "Vào ngày ${date}, ${gender} đã ký hợp đồng vay tiền mặt với Home Credit và được giải ngân ${amount} phải không?",
		"capp_welcome_kit.cash_loan_question_2" : (dynamic gender) => "${gender} đã hoặc dự định sử dụng khoản vay cho mục đích nào sau đây?",
		"capp_welcome_kit.cash_loan_question_3" : (dynamic gender, dynamic insuranceName, dynamic gender2) => "Bên cạnh khoản vay trả góp, ${gender} có tham gia ${insuranceName}. \n\n${gender2} có biết mình đã tham gia (các) bảo hiểm này không?",
		"capp_welcome_kit.cash_loan_question_1_answer_1" : MessageLookupByLibrary.simpleMessage("Đúng"),
		"capp_welcome_kit.cash_loan_question_1_answer_2" : MessageLookupByLibrary.simpleMessage("Không đúng/ Không vay"),
		"capp_welcome_kit.cash_loan_question_2_answer_1" : MessageLookupByLibrary.simpleMessage("Mua phương tiện đi lại (xe máy, xe đạp, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_2" : MessageLookupByLibrary.simpleMessage("Mua trang thiết bị gia đình (đồ nội thất, thiết bị điện máy, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_3" : MessageLookupByLibrary.simpleMessage("Mua đồ dùng (thức ăn, đồ uống, quần áo, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_4" : MessageLookupByLibrary.simpleMessage("Chi phí học tập"),
		"capp_welcome_kit.cash_loan_question_2_answer_5" : MessageLookupByLibrary.simpleMessage("Chi phí khám & chữa bệnh"),
		"capp_welcome_kit.cash_loan_question_2_answer_6" : MessageLookupByLibrary.simpleMessage("Chi phí du lịch"),
		"capp_welcome_kit.cash_loan_question_2_answer_7" : MessageLookupByLibrary.simpleMessage("Chi phí văn hóa (đám cưới, đám ma, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_8" : MessageLookupByLibrary.simpleMessage("Chi phí thể thao"),
		"capp_welcome_kit.cash_loan_question_2_answer_9" : MessageLookupByLibrary.simpleMessage("Chi phí sửa chữa nhà ở"),
		"capp_welcome_kit.cash_loan_question_3_answer_1" : MessageLookupByLibrary.simpleMessage("Đã biết"),
		"capp_welcome_kit.cash_loan_question_3_answer_2" : MessageLookupByLibrary.simpleMessage("Không biết/ Không mua"),
		"capp_welcome_kit.empty_survey_for_user" : MessageLookupByLibrary.simpleMessage("Hiện tại không có khảo sát dành cho bạn"),
		"capp_welcome_kit.back_to_home_screen" : MessageLookupByLibrary.simpleMessage("Quay lại Trang chủ ngay"),
		"capp_welcome_kit.insurance_save_finance_title" : MessageLookupByLibrary.simpleMessage("Gói An tâm Tài chính"),
		"capp_welcome_kit.insurance_save_finance_description" : MessageLookupByLibrary.simpleMessage("Gói An tâm Tài chính hỗ trợ chi trả toàn bộ hoặc một phần khoản vay còn lại trong trường hợp người vay hoặc vợ/chồng của người vay gặp tai nạn, thương tật ngoài ý muốn trong thời gian vay với Home Credit."),
		"capp_welcome_kit.insurance_tw_protect_title" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Bảo vệ Xe máy Toàn diện"),
		"capp_welcome_kit.insurance_tw_protect_description" : MessageLookupByLibrary.simpleMessage("Bảo hiểm bảo vệ xe máy toàn diện là sản phẩm hỗ trợ chi phí khi xe máy và người ngồi trên xe gặp các rủi ro và tai nạn không mong muốn. Bên cạnh đó, sản phẩm này còn cung cấp dịch vụ cứu hộ 24/7 miễn phí tại 56 tỉnh thành trên toàn quốc."),
		"capp_welcome_kit.insurance_cd_protect_title" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Trang thiết bị Nội thất"),
		"capp_welcome_kit.insurance_cd_protect_description" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Trang thiết bị Nội thất hỗ trợ chi phí sửa chữa hoặc thay thế lên đến 50 triệu đồng khi xảy ra sự kiện bảo hiểm cho toàn bộ thiết bị gia dụng bên trong căn nhà/căn hộ."),
		"capp_welcome_kit.insurance_mobile_protect_title" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Bảo vệ Toàn diện Thiết bị di động"),
		"capp_welcome_kit.insurance_mobile_protect_description" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Bảo Vệ Toàn Diện Thiết Bị Di Động sẽ hỗ trợ chi phí sửa chữa thiết bị hoặc hỗ trợ chi phí đổi thiết bị khác với giá trị tương đương khi không thể sửa chữa."),
		"capp_welcome_kit.insurance_label_title" : (dynamic insuranceName) => "Quyền lợi của ${insuranceName}:",
		"capp_welcome_kit.insurance_label_description" : (dynamic gender) => "Với một khoản chi phí rất nhỏ được trích từ số tiền góp hàng tháng, ${gender} sẽ được hưởng quyền lợi bảo hiểm ngay tại thời điểm đăng kí thành công và không phát sinh thêm chi phí nào khác.",
		"capp_welcome_kit.wlk_survey_submit_error_des" : MessageLookupByLibrary.simpleMessage("Khảo sát đã hết hạn"),

  };
}

final _$en_VN = $en_VN();

class $en_VN extends MessageLookupByLibrary {
  get localeName => 'en_VN';
  
  final Map<String, dynamic> messages = <String, dynamic>{
		"capp_welcome_kit.i_know" : MessageLookupByLibrary.simpleMessage("Tôi đã hiểu"),
		"capp_welcome_kit.concern_about_insurance" : MessageLookupByLibrary.simpleMessage("Thắc mắc về Bảo hiểm"),
		"capp_welcome_kit.view_insurance_info" : MessageLookupByLibrary.simpleMessage("(Xem thông tin về gói Bảo hiểm)"),
		"capp_welcome_kit.press_here" : MessageLookupByLibrary.simpleMessage("Ấn vào đây"),
		"capp_welcome_kit.survey_opening_view_title" : MessageLookupByLibrary.simpleMessage("Home Credit xin cảm ơn anh/chị đã sử dụng dịch vụ trả góp của Công ty."),
		"capp_welcome_kit.survey_opening_view_body" : MessageLookupByLibrary.simpleMessage("Nhằm tuân thủ theo yêu cầu của Ngân hàng nhà nước về việc giám sát mục đích sử dụng vốn vay cũng như nâng cao chất lượng dịch vụ, anh/chị vui lòng dành 2 phút để hoàn thành bản khảo sát này trước khi tiếp tục trải nghiệm ứng dụng."),
		"capp_welcome_kit.survey_opening_view_start_button" : MessageLookupByLibrary.simpleMessage("Bắt đầu"),
		"capp_welcome_kit.survey_closing_view_title" : MessageLookupByLibrary.simpleMessage("Cảm ơn phản hồi của bạn"),
		"capp_welcome_kit.survey_closing_view_body" : MessageLookupByLibrary.simpleMessage("Việc phản hồi sẽ giúp chúng tôi cải thiện chất lượng sản phẩm tốt hơn"),
		"capp_welcome_kit.survey_closing_view_done_button" : MessageLookupByLibrary.simpleMessage("Xong"),
		"capp_welcome_kit.survey_screen_title" : MessageLookupByLibrary.simpleMessage("Khảo sát"),
		"capp_welcome_kit.survey_screen_open_question_input_label" : MessageLookupByLibrary.simpleMessage("Please type your answer here"),
		"capp_welcome_kit.survey_screen_continue_btn" : MessageLookupByLibrary.simpleMessage("Tiếp tục"),
		"capp_welcome_kit.survey_demo_screen_title" : MessageLookupByLibrary.simpleMessage("Welcome Kit Survey"),
		"capp_welcome_kit.survey_demo_view_open_button" : MessageLookupByLibrary.simpleMessage("Open survey"),
		"capp_welcome_kit.survey_demo_view_choose" : MessageLookupByLibrary.simpleMessage("Choose Survey"),
		"capp_welcome_kit.survey_error_general_title" : MessageLookupByLibrary.simpleMessage("Có vấn đề đã xảy ra"),
		"capp_welcome_kit.survey_error_general_desc" : MessageLookupByLibrary.simpleMessage("Vui lòng thử lại"),
		"capp_welcome_kit.survey_error_not_found_title" : MessageLookupByLibrary.simpleMessage("Khảo sát hiện không khả dụng"),
		"capp_welcome_kit.survey_error_not_found_desc" : MessageLookupByLibrary.simpleMessage("Xin lỗi! Hiện tại khảo sát này đã kết thúc"),
		"capp_welcome_kit.survey_error_close" : MessageLookupByLibrary.simpleMessage("Đóng"),
		"capp_welcome_kit.survey_error_reload" : MessageLookupByLibrary.simpleMessage("Thử lại"),
		"capp_welcome_kit.survey_opening_title" : (dynamic gender, dynamic customerName) => "Cảm ơn ${gender} ${customerName} đã chọn Home Credit cho nhu cầu tài chính của mình",
		"capp_welcome_kit.survey_opening_text" : MessageLookupByLibrary.simpleMessage("Để xác nhận thông tin khoản vay, vui lòng dành 30 giây hoàn thành khảo sát này nhé!"),
		"capp_welcome_kit.survey_closing_title" : (dynamic gender) => "Cảm ơn ${gender} đã dành thời gian hoàn thành khảo sát.",
		"capp_welcome_kit.survey_closing_text" : (dynamic gender) => "Chúc ${gender} một ngày tốt lành!",
		"capp_welcome_kit.survey_closing_help_title" : (dynamic gender) => "Nếu ${gender} cần thêm thông tin về Bảo hiểm!\nVui lòng Ấn vào đây để được hỗ trợ.",
		"capp_welcome_kit.pos_loan_question_1" : (dynamic date, dynamic gender, dynamic product) => "Vào ngày ${date}, ${gender} đã vay trả góp của Home Credit để mua sản phẩm ${product} phải không?",
		"capp_welcome_kit.pos_loan_question_1_answer_1" : MessageLookupByLibrary.simpleMessage("Đúng"),
		"capp_welcome_kit.pos_loan_question_1_answer_2" : MessageLookupByLibrary.simpleMessage("Không đúng/ Không vay"),
		"capp_welcome_kit.pos_loan_question_2" : (dynamic gender, dynamic insuranceName, dynamic gender2) => "Bên cạnh khoản vay trả góp, ${gender} có tham gia ${insuranceName}. \n\n${gender2} có biết mình đã tham gia (các) bảo hiểm này không?",
		"capp_welcome_kit.pos_loan_question_2_answer_1" : MessageLookupByLibrary.simpleMessage("Đã biết"),
		"capp_welcome_kit.pos_loan_question_2_answer_2" : MessageLookupByLibrary.simpleMessage("Không biết/ Không mua"),
		"capp_welcome_kit.pos_loan_question_3" : (dynamic gender, dynamic customerName) => "Home Credit có hỗ trợ khoản vay lãi suất ưu đãi cho các Khách hàng uy tín. ${gender} ${customerName} có quan tâm đến ưu đãi này không?",
		"capp_welcome_kit.pos_loan_question_3_answer_1" : MessageLookupByLibrary.simpleMessage("Có"),
		"capp_welcome_kit.pos_loan_question_3_answer_2" : MessageLookupByLibrary.simpleMessage("Không"),
		"capp_welcome_kit.big_ticket_question_1" : (dynamic date, dynamic gender, dynamic amount) => "Vào ngày ${date}, ${gender} đã ký hợp đồng vay tiền mặt với Home Credit và được giải ngân ${amount} phải không?",
		"capp_welcome_kit.big_ticket_question_1_answer_1" : MessageLookupByLibrary.simpleMessage("Đúng"),
		"capp_welcome_kit.big_ticket_question_1_answer_2" : MessageLookupByLibrary.simpleMessage("Không"),
		"capp_welcome_kit.big_ticket_question_2" : (dynamic gender) => "Tại thời điểm làm hồ sơ, ${gender} đã nộp loại chứng từ nào để chứng minh khoản chi trả của mình?",
		"capp_welcome_kit.big_ticket_question_2_answer_1" : MessageLookupByLibrary.simpleMessage("Hóa đơn đỏ"),
		"capp_welcome_kit.big_ticket_question_2_answer_2" : MessageLookupByLibrary.simpleMessage("Hóa đơn bán lẻ và hợp đồng dịch vụ"),
		"capp_welcome_kit.big_ticket_question_2_answer_3" : MessageLookupByLibrary.simpleMessage("Không nhớ"),
		"capp_welcome_kit.big_ticket_question_3" : (dynamic gender, dynamic insuranceName, dynamic gender2) => "Bên cạnh khoản vay trả góp, ${gender} có tham gia ${insuranceName}. \n\n${gender2} có biết mình đã tham gia (các) bảo hiểm này không?",
		"capp_welcome_kit.big_ticket_question_3_answer_1" : MessageLookupByLibrary.simpleMessage("Đã biết"),
		"capp_welcome_kit.big_ticket_question_3_answer_2" : MessageLookupByLibrary.simpleMessage("Không biết/ Không mua"),
		"capp_welcome_kit.big_ticket_question_4" : (dynamic gender) => "${gender} đã hoặc dự định sử dụng khoản vay cho mục đích nào sau đây?",
		"capp_welcome_kit.cash_loan_question_1" : (dynamic date, dynamic gender, dynamic amount) => "Vào ngày ${date}, ${gender} đã ký hợp đồng vay tiền mặt với Home Credit và được giải ngân ${amount} phải không?",
		"capp_welcome_kit.cash_loan_question_2" : (dynamic gender) => "${gender} đã hoặc dự định sử dụng khoản vay cho mục đích nào sau đây?",
		"capp_welcome_kit.cash_loan_question_3" : (dynamic gender, dynamic insuranceName, dynamic gender2) => "Bên cạnh khoản vay trả góp, ${gender} có tham gia ${insuranceName}. \n\n${gender2} có biết mình đã tham gia (các) bảo hiểm này không?",
		"capp_welcome_kit.cash_loan_question_1_answer_1" : MessageLookupByLibrary.simpleMessage("Đúng"),
		"capp_welcome_kit.cash_loan_question_1_answer_2" : MessageLookupByLibrary.simpleMessage("Không đúng/ Không vay"),
		"capp_welcome_kit.cash_loan_question_2_answer_1" : MessageLookupByLibrary.simpleMessage("Mua phương tiện đi lại (xe máy, xe đạp, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_2" : MessageLookupByLibrary.simpleMessage("Mua trang thiết bị gia đình (đồ nội thất, thiết bị điện máy, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_3" : MessageLookupByLibrary.simpleMessage("Mua đồ dùng (thức ăn, đồ uống, quần áo, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_4" : MessageLookupByLibrary.simpleMessage("Chi phí học tập"),
		"capp_welcome_kit.cash_loan_question_2_answer_5" : MessageLookupByLibrary.simpleMessage("Chi phí khám & chữa bệnh"),
		"capp_welcome_kit.cash_loan_question_2_answer_6" : MessageLookupByLibrary.simpleMessage("Chi phí du lịch"),
		"capp_welcome_kit.cash_loan_question_2_answer_7" : MessageLookupByLibrary.simpleMessage("Chi phí văn hóa (đám cưới, đám ma, v.v.)"),
		"capp_welcome_kit.cash_loan_question_2_answer_8" : MessageLookupByLibrary.simpleMessage("Chi phí thể thao"),
		"capp_welcome_kit.cash_loan_question_2_answer_9" : MessageLookupByLibrary.simpleMessage("Chi phí sửa chữa nhà ở"),
		"capp_welcome_kit.cash_loan_question_3_answer_1" : MessageLookupByLibrary.simpleMessage("Đã biết"),
		"capp_welcome_kit.cash_loan_question_3_answer_2" : MessageLookupByLibrary.simpleMessage("Không biết/ Không mua"),
		"capp_welcome_kit.empty_survey_for_user" : MessageLookupByLibrary.simpleMessage("Hiện tại không có khảo sát dành cho bạn"),
		"capp_welcome_kit.back_to_home_screen" : MessageLookupByLibrary.simpleMessage("Back to Home page"),
		"capp_welcome_kit.insurance_save_finance_title" : MessageLookupByLibrary.simpleMessage("Gói An tâm Tài chính"),
		"capp_welcome_kit.insurance_save_finance_description" : MessageLookupByLibrary.simpleMessage("Gói An tâm Tài chính hỗ trợ chi trả toàn bộ hoặc một phần khoản vay còn lại trong trường hợp người vay hoặc vợ/chồng của người vay gặp tai nạn, thương tật ngoài ý muốn trong thời gian vay với Home Credit."),
		"capp_welcome_kit.insurance_tw_protect_title" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Bảo vệ Xe máy Toàn diện"),
		"capp_welcome_kit.insurance_tw_protect_description" : MessageLookupByLibrary.simpleMessage("Bảo hiểm bảo vệ xe máy toàn diện là sản phẩm hỗ trợ chi phí khi xe máy và người ngồi trên xe gặp các rủi ro và tai nạn không mong muốn. Bên cạnh đó, sản phẩm này còn cung cấp dịch vụ cứu hộ 24/7 miễn phí tại 56 tỉnh thành trên toàn quốc."),
		"capp_welcome_kit.insurance_cd_protect_title" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Trang thiết bị Nội thất"),
		"capp_welcome_kit.insurance_cd_protect_description" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Trang thiết bị Nội thất hỗ trợ chi phí sửa chữa hoặc thay thế lên đến 50 triệu đồng khi xảy ra sự kiện bảo hiểm cho toàn bộ thiết bị gia dụng bên trong căn nhà/căn hộ."),
		"capp_welcome_kit.insurance_mobile_protect_title" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Bảo vệ Toàn diện Thiết bị di động"),
		"capp_welcome_kit.insurance_mobile_protect_description" : MessageLookupByLibrary.simpleMessage("Bảo hiểm Bảo Vệ Toàn Diện Thiết Bị Di Động sẽ hỗ trợ chi phí sửa chữa thiết bị hoặc hỗ trợ chi phí đổi thiết bị khác với giá trị tương đương khi không thể sửa chữa."),
		"capp_welcome_kit.insurance_label_title" : (dynamic insuranceName) => "Quyền lợi của ${insuranceName}:",
		"capp_welcome_kit.insurance_label_description" : (dynamic gender) => "Với một khoản chi phí rất nhỏ được trích từ số tiền góp hàng tháng, ${gender} sẽ được hưởng quyền lợi bảo hiểm ngay tại thời điểm đăng kí thành công và không phát sinh thêm chi phí nào khác.",
		"capp_welcome_kit.wlk_survey_submit_error_des" : MessageLookupByLibrary.simpleMessage("This survey is no longer available"),

  };
}



typedef Future<dynamic> LibraryLoader();
Map<String, LibraryLoader?> _deferredLibraries = {
	"vi_VN": () => Future<LibraryLoader?>.value(null),
	"en_VN": () => Future<LibraryLoader?>.value(null),

};

MessageLookupByLibrary? _findExact(String localeName) {
  switch (localeName) {
    case "vi_VN":
        return _$vi_VN;
    case "en_VN":
        return _$en_VN;

    default:
      return null;
  }
}

/// User programs should call this before using [localeName] for messages.
Future<bool> initializeMessages(String localeName) async {
  var availableLocale = Intl.verifiedLocale(
      localeName,
          (locale) => _deferredLibraries[locale] != null,
      onFailure: (_) => null);
  if (availableLocale == null) {
    return Future.value(false);
  }
  var lib = _deferredLibraries[availableLocale];
  await (lib == null ? Future.value(false) : lib());

  initializeInternalMessageLookup(() => CompositeMessageLookup());
  messageLookup.addLocale(availableLocale, _findGeneratedMessagesFor);

  return Future.value(true);
}

bool _messagesExistFor(String locale) {
  try {
    return _findExact(locale) != null;
  } catch (e) {
    return false;
  }
}

MessageLookupByLibrary? _findGeneratedMessagesFor(String locale) {
  var actualLocale = Intl.verifiedLocale(locale, _messagesExistFor,
      onFailure: (_) => null);
  if (actualLocale == null) return null;
  return _findExact(actualLocale);
}

// ignore_for_file: unnecessary_brace_in_string_interps
