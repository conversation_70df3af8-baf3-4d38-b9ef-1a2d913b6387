// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'capp_repayment_base.dart';

// **************************************************************************
// <PERSON>yalRouterGenerator
// **************************************************************************

class CappRepayment extends _CappRepayment {
  @override
  List<KoyalRoute> get routes => [
        KoyalRoute(
          runtimeType,
          OnePayScreen,
          'onepay_screen',
          (context, arguments) =>
              OnePayScreen(arguments: arguments as OnePayRouteArgs)
                  .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'OnePayScreen',
          bussinesRouteName: 'rpy_atm_processing',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPaymentSummaryScreen,
          'repayment_payment_summary_screen',
          (context, arguments) => RepaymentPaymentSummaryScreen(
                  arguments: arguments as RepaymentPaymentSummaryRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPaymentSummaryScreen',
          bussinesRouteName: 'rpy_onl_pmt_smry',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentNoLoanScreen,
          'repayment_no_loan_screen',
          (context, arguments) => RepaymentNoLoanScreen(
              arguments: arguments != null
                  ? arguments as RepaymentNoLoanRouteArgs
                  : null),
          packageName: 'CappRepayment',
          screenName: 'RepaymentNoLoanScreen',
          bussinesRouteName: 'rpy_no_loan',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentTransactionFailedScreen,
          'repayment_transaction_failed_screen',
          (context, arguments) => RepaymentTransactionFailedScreen(
              arguments: arguments != null
                  ? arguments as RepaymentTransactionFailedRouteArgs
                  : null),
          packageName: 'CappRepayment',
          screenName: 'RepaymentTransactionFailedScreen',
          bussinesRouteName: 'rpy_trans_error',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentFailedScreen,
          'repayment_failed_screen',
          (context, arguments) => RepaymentFailedScreen(
              arguments: arguments as RepaymentFailedRouteArgs),
          packageName: 'CappRepayment',
          screenName: 'RepaymentFailedScreen',
          bussinesRouteName: 'rpy_error',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentBankTransferScreen,
          'repayment_bank_transfer_screen',
          (context, arguments) => RepaymentBankTransferScreen(
                  arguments: arguments as RepaymentBankTransferRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentBankTransferScreen',
          bussinesRouteName: 'rpy_bnk_trnsf',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPaymentResultScreen,
          'repayment_payment_result_screen',
          (context, arguments) => RepaymentPaymentResultScreen(
                  arguments: arguments as ScreenArguments)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPaymentResultScreen',
          bussinesRouteName: 'rpy_result',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentCheckResultScreen,
          'repayment_check_result_screen',
          (context, arguments) => RepaymentCheckResultScreen(
                  arguments: arguments as RepaymentCheckResultRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentCheckResultScreen',
          bussinesRouteName: 'rpy_result_check',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          ViettelMoneyScreen,
          'viettelmoney_screen',
          (context, arguments) =>
              ViettelMoneyScreen(arguments: arguments as ViettelMoneyRouteArgs)
                  .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'ViettelMoneyScreen',
          bussinesRouteName: 'rpy_viettel_processing',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentTransactionSuccessScreen,
          'repayment_transaction_success_screen',
          (context, arguments) => RepaymentTransactionSuccessScreen(
                  arguments: arguments as RepaymentTransactionSuccessRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentTransactionSuccessScreen',
          bussinesRouteName: 'rpy_trans_success',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentBankTransferQrScreen,
          'repayment_bank_transfer_qr_screen',
          (context, arguments) => RepaymentBankTransferQrScreen(
                  arguments: arguments as RepaymentBankTransferRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentBankTransferQrScreen',
          bussinesRouteName: 'rpy_bnk_trnsf',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentMainScreen,
          'repayment_main_screen',
          (context, arguments) => RepaymentMainScreen(
                  arguments: arguments as RepaymentMainRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentMainScreen',
          bussinesRouteName: 'rpy_main',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentBankingAppScreen,
          'repayment_banking_app_screen',
          (context, arguments) => RepaymentBankingAppScreen(
                  arguments: arguments as RepaymentBankingAppRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentBankingAppScreen',
          bussinesRouteName: 'rpy_mobbnk_apps',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPtpIntroductionScreen,
          'repayment_ptp_introduction_screen',
          (context, arguments) => RepaymentPtpIntroductionScreen(
                  arguments: arguments as RepaymentPtpIntroductionRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPtpIntroductionScreen',
          bussinesRouteName: 'ptp_intro_first_open',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPtpPaymentPlanScreen,
          'repayment_ptp_payment_plan_screen',
          (context, arguments) => RepaymentPtpPaymentPlanScreen(
                  arguments: arguments as RepaymentPtpPaymentPlanRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPtpPaymentPlanScreen',
          bussinesRouteName: 'ptp_plan',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPtpOptionsScreen,
          'repayment_ptp_options_screen',
          (context, arguments) => RepaymentPtpOptionsScreen(
                  arguments: arguments as RepaymentPtpOptionsRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPtpOptionsScreen',
          bussinesRouteName: 'ptp_information',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPtpProcessingScreen,
          'repayment_ptp_processing_screen',
          (context, arguments) => RepaymentPtpProcessingScreen(
                  arguments: arguments as RepaymentPtpProcessingRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPtpProcessingScreen',
          bussinesRouteName: 'ptp_loading',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPtpSuccessScreen,
          'repayment_ptp_success_screen',
          (context, arguments) => RepaymentPtpSuccessScreen(
                  arguments: arguments as RepaymentPtpSuccessRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPtpSuccessScreen',
          bussinesRouteName: 'ptp_success',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPtpEligibilityCheckingScreen,
          'repayment_ptp_eligibility_checking_screen',
          (context, arguments) => RepaymentPtpEligibilityCheckingScreen(
                  arguments:
                      arguments as RepaymentPtpEligibilityCheckingRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPtpEligibilityCheckingScreen',
          bussinesRouteName: 'ptp_check',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPromotionDetailScreen,
          'repayment_promotion_detail_screen',
          (context, arguments) => RepaymentPromotionDetailScreen(
                  arguments: arguments as RepaymentPromotionDetailRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPromotionDetailScreen',
          bussinesRouteName: 'rpy_promo_detail',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentPtpRejectedScreen,
          'repayment_ptp_rejected_screen',
          (context, arguments) => RepaymentPtpRejectedScreen(
                  arguments: arguments as RepaymentPtpRejectedRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentPtpRejectedScreen',
          bussinesRouteName: 'promise_to_pay_rejected',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentAdaIntroScreen,
          'repayment_ada_intro_screen',
          (context, arguments) => RepaymentAdaIntroScreen(
                  arguments: arguments as RepaymentAdaIntroRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentAdaIntroScreen',
          bussinesRouteName: 'ad_intro',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentAdaManagementScreen,
          'repayment_ada_management_screen',
          (context, arguments) => RepaymentAdaManagementScreen(
                  arguments:
                      arguments != null ? arguments as ScreenArguments : null)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentAdaManagementScreen',
          bussinesRouteName: 'ad_mgm',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentAdaSuccessScreen,
          'repayment_ada_success_screen',
          (context, arguments) => RepaymentAdaSuccessScreen(
              arguments: arguments as RepaymentAdaSuccessRouteArgs),
          packageName: 'CappRepayment',
          screenName: 'RepaymentAdaSuccessScreen',
          bussinesRouteName: 'ad_success',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentAdaMainScreen,
          'repayment_ada_main_screen',
          (context, arguments) => RepaymentAdaMainScreen(
                  arguments: arguments != null
                      ? arguments as RepaymentAdaMainRouteArgs
                      : null)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentAdaMainScreen',
          bussinesRouteName: 'ad_main',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentAdaCheckResultScreen,
          'repayment_ada_check_result_screen',
          (context, arguments) => RepaymentAdaCheckResultScreen(
                  arguments: arguments as ScreenArguments)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentAdaCheckResultScreen',
          bussinesRouteName: 'ad_check_result',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentAdaFailedScreen,
          'repayment_ada_failed_screen',
          (context, arguments) => RepaymentAdaFailedScreen(
              arguments: arguments as RepaymentAdaFailedRouteArgs),
          packageName: 'CappRepayment',
          screenName: 'RepaymentAdaFailedScreen',
          bussinesRouteName: 'ad_fail',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentAdaDetailScreen,
          'repayment_ada_detail_screen',
          (context, arguments) => RepaymentAdaDetailScreen(
                  arguments: arguments as RepaymentAdaDetailRouteArgs)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'RepaymentAdaDetailScreen',
          bussinesRouteName: 'ad_detail',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          AdaOnePayScreen,
          'ada_onepay_screen',
          (context, arguments) =>
              AdaOnePayScreen(arguments: arguments as AdaOnePayRouteArgs)
                  .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'AdaOnePayScreen',
          bussinesRouteName: 'ad_atm_processing',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          LooAdaMainScreen,
          'loo_ada_main_screen',
          (context, arguments) => LooAdaMainScreen(
                  arguments: arguments != null
                      ? arguments as LooAdaMainRouteArgs
                      : null)
              .wrappedRoute(context),
          packageName: 'CappRepayment',
          screenName: 'LooAdaMainScreen',
          bussinesRouteName: 'loo_ad_main',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentBankTransferQrIntroScreen,
          'repayment_bank_transfer_qr_intro_screen',
          (context, arguments) => RepaymentBankTransferQrIntroScreen(),
          packageName: 'CappRepayment',
          screenName: 'RepaymentBankTransferQrIntroScreen',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
        KoyalRoute(
          runtimeType,
          RepaymentBankTransferQrWaitingScreen,
          'repayment_bank_transfer_qr_waiting_screen',
          (context, arguments) => RepaymentBankTransferQrWaitingScreen(),
          packageName: 'CappRepayment',
          screenName: 'RepaymentBankTransferQrWaitingScreen',
          isFullscreenDialog: false,
          mainState: true,
          transition: KoyalNavigatorTransition.standard,
        ),
      ];
}
