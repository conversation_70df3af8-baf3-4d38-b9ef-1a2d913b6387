import 'package:decimal/decimal.dart';

class Constants {
  static final defaultMinThresholdAmount = Decimal.parse('50000');

  // OnePay
  static const String onePayResponseCodeKey = 'vpc_TxnResponseCode';
  static const String adaOnePayResponseCodeKey = 'ResponseCode';
  static const String adaOnePayResponseCodeApproved = '0';

  // VnPay
  static const String vnPayNotificationAppBackAction = 'AppBackAction';
  static const String vnPayNotificationCallMobileBankingAppAction = 'CallMobileBankingApp';
  static const String vnPayNotificationWebBackAction = 'WebBackAction';
  static const String vnPayNotificationFailBackAction = 'FaildBackAction';
  static const String vnPayNotificationSuccessBackAction = 'SuccessBackAction';
  static const String vnPayNoWebviewInstalled = 'NoWebViewInstalled';

  // ViettelMoney
  static const String viettelErrorCodeKey = 'error_code';
  static const String viettelCancelUrl = 'PaymentGateway/payment/cancel';
  static const String viettelSuccessCode = '00';
  static const String viettelTransactionIdPrefix = 'HCVTL';
  static const String viettelTransactionIdKey = 'vt_transaction_id';

  static const String virtualAccountSuccessResponseCode = '1';

  static const String adaBlogDeeplink = 'https://app.gma.homecredit.vn/blog/7c41beb5-c300-4cbc-ad69-08dcc8b05dab';
}
