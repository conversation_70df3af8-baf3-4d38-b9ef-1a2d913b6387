import 'package:decimal/decimal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'repayment_transaction_create.freezed.dart';

@freezed
class RepaymentTransactionCreate with _$RepaymentTransactionCreate {
  const factory RepaymentTransactionCreate({
    String? cuid,
    String? contractNumber,
    num? repaymentId,
    Decimal? amount,
    String? currencyCode,
    String? provider,
    String? voucherCode,
  }) = _RepaymentTransactionCreate;
}
