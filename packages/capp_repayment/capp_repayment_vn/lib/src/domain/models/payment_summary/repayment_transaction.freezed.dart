// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_transaction.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentTransaction {
  String? get gmaTransactionId => throw _privateConstructorUsedError;
  String? get cuid => throw _privateConstructorUsedError;
  String? get pcsTransactionId => throw _privateConstructorUsedError;
  String? get partnerTransactionId => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  String? get voucherCode => throw _privateConstructorUsedError;
  Decimal? get finalAmount =>
      throw _privateConstructorUsedError; // Amount after discount
  Decimal? get originalAmount => throw _privateConstructorUsedError;
  Decimal? get discountAmount => throw _privateConstructorUsedError;
  String? get currencyCode => throw _privateConstructorUsedError;
  String? get provider => throw _privateConstructorUsedError;
  String? get paymentStatus => throw _privateConstructorUsedError;
  String? get createdBy => throw _privateConstructorUsedError;
  num? get createdDate => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentTransactionCopyWith<RepaymentTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentTransactionCopyWith<$Res> {
  factory $RepaymentTransactionCopyWith(RepaymentTransaction value,
          $Res Function(RepaymentTransaction) then) =
      _$RepaymentTransactionCopyWithImpl<$Res, RepaymentTransaction>;
  @useResult
  $Res call(
      {String? gmaTransactionId,
      String? cuid,
      String? pcsTransactionId,
      String? partnerTransactionId,
      String? contractNumber,
      String? voucherCode,
      Decimal? finalAmount,
      Decimal? originalAmount,
      Decimal? discountAmount,
      String? currencyCode,
      String? provider,
      String? paymentStatus,
      String? createdBy,
      num? createdDate});
}

/// @nodoc
class _$RepaymentTransactionCopyWithImpl<$Res,
        $Val extends RepaymentTransaction>
    implements $RepaymentTransactionCopyWith<$Res> {
  _$RepaymentTransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gmaTransactionId = freezed,
    Object? cuid = freezed,
    Object? pcsTransactionId = freezed,
    Object? partnerTransactionId = freezed,
    Object? contractNumber = freezed,
    Object? voucherCode = freezed,
    Object? finalAmount = freezed,
    Object? originalAmount = freezed,
    Object? discountAmount = freezed,
    Object? currencyCode = freezed,
    Object? provider = freezed,
    Object? paymentStatus = freezed,
    Object? createdBy = freezed,
    Object? createdDate = freezed,
  }) {
    return _then(_value.copyWith(
      gmaTransactionId: freezed == gmaTransactionId
          ? _value.gmaTransactionId
          : gmaTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      cuid: freezed == cuid
          ? _value.cuid
          : cuid // ignore: cast_nullable_to_non_nullable
              as String?,
      pcsTransactionId: freezed == pcsTransactionId
          ? _value.pcsTransactionId
          : pcsTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerTransactionId: freezed == partnerTransactionId
          ? _value.partnerTransactionId
          : partnerTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
      finalAmount: freezed == finalAmount
          ? _value.finalAmount
          : finalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      originalAmount: freezed == originalAmount
          ? _value.originalAmount
          : originalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      discountAmount: freezed == discountAmount
          ? _value.discountAmount
          : discountAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
      provider: freezed == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentStatus: freezed == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as num?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentTransactionCopyWith<$Res>
    implements $RepaymentTransactionCopyWith<$Res> {
  factory _$$_RepaymentTransactionCopyWith(_$_RepaymentTransaction value,
          $Res Function(_$_RepaymentTransaction) then) =
      __$$_RepaymentTransactionCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? gmaTransactionId,
      String? cuid,
      String? pcsTransactionId,
      String? partnerTransactionId,
      String? contractNumber,
      String? voucherCode,
      Decimal? finalAmount,
      Decimal? originalAmount,
      Decimal? discountAmount,
      String? currencyCode,
      String? provider,
      String? paymentStatus,
      String? createdBy,
      num? createdDate});
}

/// @nodoc
class __$$_RepaymentTransactionCopyWithImpl<$Res>
    extends _$RepaymentTransactionCopyWithImpl<$Res, _$_RepaymentTransaction>
    implements _$$_RepaymentTransactionCopyWith<$Res> {
  __$$_RepaymentTransactionCopyWithImpl(_$_RepaymentTransaction _value,
      $Res Function(_$_RepaymentTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gmaTransactionId = freezed,
    Object? cuid = freezed,
    Object? pcsTransactionId = freezed,
    Object? partnerTransactionId = freezed,
    Object? contractNumber = freezed,
    Object? voucherCode = freezed,
    Object? finalAmount = freezed,
    Object? originalAmount = freezed,
    Object? discountAmount = freezed,
    Object? currencyCode = freezed,
    Object? provider = freezed,
    Object? paymentStatus = freezed,
    Object? createdBy = freezed,
    Object? createdDate = freezed,
  }) {
    return _then(_$_RepaymentTransaction(
      gmaTransactionId: freezed == gmaTransactionId
          ? _value.gmaTransactionId
          : gmaTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      cuid: freezed == cuid
          ? _value.cuid
          : cuid // ignore: cast_nullable_to_non_nullable
              as String?,
      pcsTransactionId: freezed == pcsTransactionId
          ? _value.pcsTransactionId
          : pcsTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      partnerTransactionId: freezed == partnerTransactionId
          ? _value.partnerTransactionId
          : partnerTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
      finalAmount: freezed == finalAmount
          ? _value.finalAmount
          : finalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      originalAmount: freezed == originalAmount
          ? _value.originalAmount
          : originalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      discountAmount: freezed == discountAmount
          ? _value.discountAmount
          : discountAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
      provider: freezed == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentStatus: freezed == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String?,
      createdDate: freezed == createdDate
          ? _value.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as num?,
    ));
  }
}

/// @nodoc

class _$_RepaymentTransaction implements _RepaymentTransaction {
  const _$_RepaymentTransaction(
      {this.gmaTransactionId,
      this.cuid,
      this.pcsTransactionId,
      this.partnerTransactionId,
      this.contractNumber,
      this.voucherCode,
      this.finalAmount,
      this.originalAmount,
      this.discountAmount,
      this.currencyCode,
      this.provider,
      this.paymentStatus,
      this.createdBy,
      this.createdDate});

  @override
  final String? gmaTransactionId;
  @override
  final String? cuid;
  @override
  final String? pcsTransactionId;
  @override
  final String? partnerTransactionId;
  @override
  final String? contractNumber;
  @override
  final String? voucherCode;
  @override
  final Decimal? finalAmount;
// Amount after discount
  @override
  final Decimal? originalAmount;
  @override
  final Decimal? discountAmount;
  @override
  final String? currencyCode;
  @override
  final String? provider;
  @override
  final String? paymentStatus;
  @override
  final String? createdBy;
  @override
  final num? createdDate;

  @override
  String toString() {
    return 'RepaymentTransaction(gmaTransactionId: $gmaTransactionId, cuid: $cuid, pcsTransactionId: $pcsTransactionId, partnerTransactionId: $partnerTransactionId, contractNumber: $contractNumber, voucherCode: $voucherCode, finalAmount: $finalAmount, originalAmount: $originalAmount, discountAmount: $discountAmount, currencyCode: $currencyCode, provider: $provider, paymentStatus: $paymentStatus, createdBy: $createdBy, createdDate: $createdDate)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentTransaction &&
            (identical(other.gmaTransactionId, gmaTransactionId) ||
                other.gmaTransactionId == gmaTransactionId) &&
            (identical(other.cuid, cuid) || other.cuid == cuid) &&
            (identical(other.pcsTransactionId, pcsTransactionId) ||
                other.pcsTransactionId == pcsTransactionId) &&
            (identical(other.partnerTransactionId, partnerTransactionId) ||
                other.partnerTransactionId == partnerTransactionId) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.voucherCode, voucherCode) ||
                other.voucherCode == voucherCode) &&
            (identical(other.finalAmount, finalAmount) ||
                other.finalAmount == finalAmount) &&
            (identical(other.originalAmount, originalAmount) ||
                other.originalAmount == originalAmount) &&
            (identical(other.discountAmount, discountAmount) ||
                other.discountAmount == discountAmount) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.createdDate, createdDate) ||
                other.createdDate == createdDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      gmaTransactionId,
      cuid,
      pcsTransactionId,
      partnerTransactionId,
      contractNumber,
      voucherCode,
      finalAmount,
      originalAmount,
      discountAmount,
      currencyCode,
      provider,
      paymentStatus,
      createdBy,
      createdDate);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentTransactionCopyWith<_$_RepaymentTransaction> get copyWith =>
      __$$_RepaymentTransactionCopyWithImpl<_$_RepaymentTransaction>(
          this, _$identity);
}

abstract class _RepaymentTransaction implements RepaymentTransaction {
  const factory _RepaymentTransaction(
      {final String? gmaTransactionId,
      final String? cuid,
      final String? pcsTransactionId,
      final String? partnerTransactionId,
      final String? contractNumber,
      final String? voucherCode,
      final Decimal? finalAmount,
      final Decimal? originalAmount,
      final Decimal? discountAmount,
      final String? currencyCode,
      final String? provider,
      final String? paymentStatus,
      final String? createdBy,
      final num? createdDate}) = _$_RepaymentTransaction;

  @override
  String? get gmaTransactionId;
  @override
  String? get cuid;
  @override
  String? get pcsTransactionId;
  @override
  String? get partnerTransactionId;
  @override
  String? get contractNumber;
  @override
  String? get voucherCode;
  @override
  Decimal? get finalAmount;
  @override // Amount after discount
  Decimal? get originalAmount;
  @override
  Decimal? get discountAmount;
  @override
  String? get currencyCode;
  @override
  String? get provider;
  @override
  String? get paymentStatus;
  @override
  String? get createdBy;
  @override
  num? get createdDate;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentTransactionCopyWith<_$_RepaymentTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}
