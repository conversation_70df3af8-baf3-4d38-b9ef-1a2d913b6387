import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../capp_repayment.dart';

part 'repayment_onepay_transaction_request.freezed.dart';

@freezed
class RepaymentOnePayTransactionRequest with _$RepaymentOnePayTransactionRequest {
  const factory RepaymentOnePayTransactionRequest({
    RepaymentTransactionCreate? transactionCreate,
    String? returnUrl,
  }) = _RepaymentOnePayTransactionRequest;
}
