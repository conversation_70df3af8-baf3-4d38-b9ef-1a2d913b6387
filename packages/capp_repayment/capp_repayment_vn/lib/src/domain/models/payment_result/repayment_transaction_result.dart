import 'package:json_annotation/json_annotation.dart';

part 'repayment_transaction_result.g.dart';

@JsonSerializable()
class RepaymentTransactionResult {
  final String transactionId;
  final String contractNo;
  final String customerName;
  final double finalAmount;
  String? voucherCode;
  double? originalAmount;
  double? discountAmount;
  final String dateProcessed;
  final String paymentOption;
  final String paymentOptionId;
  final String transactionNo;
  final bool isEWallet;

  RepaymentTransactionResult({
    required this.transactionId,
    required this.isEWallet,
    required this.contractNo,
    required this.customerName,
    this.voucherCode,
    this.discountAmount,
    this.originalAmount,
    required this.finalAmount,
    required this.dateProcessed,
    required this.paymentOption,
    required this.transactionNo,
    required this.paymentOptionId,
  });

  factory RepaymentTransactionResult.fromJson(Map<String, dynamic> json) => _$RepaymentTransactionResultFromJson(json);

  Map<String, dynamic> toJson() => _$RepaymentTransactionResultToJson(this);
}
