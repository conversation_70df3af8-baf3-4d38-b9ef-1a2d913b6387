import 'package:equatable/equatable.dart';

class RepaymentBank extends Equatable {
  final String shortName;
  final String name;
  final String iconName;
  final String thumbnail;

  const RepaymentBank({
    required this.shortName,
    required this.name,
    required this.iconName,
    required this.thumbnail,
  });

  @override
  List<Object?> get props => [shortName, name, iconName, thumbnail];
}
