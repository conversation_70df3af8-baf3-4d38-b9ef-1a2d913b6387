import 'package:decimal/decimal.dart';
import 'package:equatable/equatable.dart';

class RepaymentPtpEvaluationRequest extends Equatable {
  final String? entityId;
  final String? entityType;
  final String? promiseDate;
  final Decimal? promiseAmount;

  const RepaymentPtpEvaluationRequest({
    this.entityId,
    this.entityType,
    this.promiseDate,
    this.promiseAmount,
  });

  @override
  List<Object?> get props => [entityId];

  RepaymentPtpEvaluationRequest copyWith({
    String? entityId,
    String? entityType,
    String? promiseDate,
    Decimal? promiseAmount,
    String? paymentMethod,
    String? delinquencyReason,
    String? comment,
  }) {
    return RepaymentPtpEvaluationRequest(
      entityId: entityId ?? this.entityId,
      entityType: entityType ?? this.entityType,
      promiseDate: promiseDate ?? this.promiseDate,
      promiseAmount: promiseAmount ?? this.promiseAmount,
    );
  }
}
