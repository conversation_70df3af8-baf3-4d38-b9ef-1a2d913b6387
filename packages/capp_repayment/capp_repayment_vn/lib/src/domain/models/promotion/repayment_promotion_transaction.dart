import 'package:decimal/decimal.dart';

class RepaymentPromotionTransaction {
  final String? promotionTransactionId;
  final String? refTransactionId;
  final String? status;
  final String? voucherCode;
  final Decimal? originalAmount;
  final Decimal? discountAmount;
  final Decimal? finalAmount;
  final Decimal? actualAmount;
  final String? contractNumber;

  RepaymentPromotionTransaction({
    this.promotionTransactionId,
    this.refTransactionId,
    this.status,
    this.voucherCode,
    this.originalAmount,
    this.discountAmount,
    this.finalAmount,
    this.actualAmount,
    this.contractNumber,
  });

  RepaymentPromotionTransaction copyWith({
    String? promotionTransactionId,
    String? refTransactionId,
    String? status,
    Decimal? originalAmount,
    Decimal? discountAmount,
    Decimal? finalAmount,
    Decimal? actualAmount,
    String? contractNumber,
  }) =>
      RepaymentPromotionTransaction(
        promotionTransactionId: promotionTransactionId ?? this.promotionTransactionId,
        refTransactionId: refTransactionId ?? this.refTransactionId,
        status: status ?? this.status,
        originalAmount: originalAmount ?? this.originalAmount,
        discountAmount: discountAmount ?? this.discountAmount,
        finalAmount: finalAmount ?? this.finalAmount,
        actualAmount: actualAmount ?? this.actualAmount,
        contractNumber: contractNumber ?? this.contractNumber,
      );
}
