import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:decimal/decimal.dart';
import 'package:equatable/equatable.dart';

import 'ada_cel_contract.dart';
import 'ada_rel_contract.dart';

class AdaContract extends Equatable {
  final RepaymentContractType? contractType;
  final AdaCelContract? celContract;
  final AdaRelContract? relContract;

  const AdaContract({
    this.contractType,
    this.celContract,
    this.relContract,
  });

  // Copy with method
  AdaContract copyWith({
    RepaymentContractType? contractType,
    AdaCelContract? celContract,
    AdaRelContract? relContract,
  }) {
    return AdaContract(
      contractType: contractType ?? this.contractType,
      celContract: celContract ?? this.celContract,
      relContract: relContract ?? this.relContract,
    );
  }

  // FromJson method
  factory AdaContract.fromJson(Map<String, dynamic> json) {
    return AdaContract(
      contractType: json['contractType'] != null ? RepaymentContractTypeX.fromJson(json['contractType']) : null,
      celContract: json['celContract'] != null ? AdaCelContract.fromJson(json['celContract']) : null,
      relContract: json['relContract'] != null ? AdaRelContract.fromJson(json['relContract']) : null,
    );
  }

  // ToJson method
  Map<String, dynamic> toJson() {
    return {
      'contractType': contractType?.toJson(),
      'celContract': celContract?.toJson(),
      'relContract': relContract?.toJson(),
    };
  }

  @override
  List<Object?> get props => [contractType, celContract, relContract];
}

extension AdaContractX on AdaContract {
  String? get contractNumber {
    return contractType == RepaymentContractType.cel ? celContract?.contractNumber : relContract?.contractNumber;
  }

  DateTime? get dueDate {
    return contractType == RepaymentContractType.cel ? celContract?.dueDate : relContract?.dueDate;
  }

  int? get dueDay {
    return contractType == RepaymentContractType.cel ? celContract?.dueDay : relContract?.dueDay;
  }

  Decimal? get paymentAmount {
    return contractType == RepaymentContractType.cel ? celContract?.dueAmount : relContract?.minimumDueAmount;
  }
}
