import 'package:decimal/decimal.dart';
import 'package:equatable/equatable.dart';

class AdaEnrolledContract extends Equatable {
  final String? id;
  final String? productImageUrl;
  final String? productName;
  final String? contractNumber;
  final String? paymentPartner;
  final String? status;
  final Decimal? registeredAdaAmount;
  final int? registeredAdaDay; // Used to display payment day each month
  final String? productType;
  final String? loanType;
  final String? productCode;

  const AdaEnrolledContract({
    this.productImageUrl,
    this.productName,
    this.id,
    this.contractNumber,
    this.paymentPartner,
    this.status,
    this.registeredAdaAmount,
    this.registeredAdaDay,
    this.productType,
    this.loanType,
    this.productCode,
  });

  @override
  List<Object?> get props => [
        id,
        contractNumber,
        paymentPartner,
        status,
        registeredAdaAmount,
        registeredAdaDay,
        productType,
        productName,
        productImageUrl,
        loanType,
        productCode,
      ];
}
