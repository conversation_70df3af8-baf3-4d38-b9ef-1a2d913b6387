import 'package:decimal/decimal.dart';
import 'package:equatable/equatable.dart';

class AdaRelContract extends Equatable {
  final String? contractNumber;
  final String? gmaProductType;
  final String? currency;
  final Decimal? minimumDueAmount;
  final Decimal? totalAmountDue;
  final DateTime? dueDate;
  final int? dueDay;
  final int? billingDay;
  final int? dpd;
  final bool? isEligible;
  final Decimal? outstandingBalance;
  final String? productCode;

  const AdaRelContract({
    this.contractNumber,
    this.gmaProductType,
    this.currency,
    this.minimumDueAmount,
    this.totalAmountDue,
    this.dueDate,
    this.dueDay,
    this.dpd,
    this.outstandingBalance,
    this.billingDay,
    this.isEligible,
    this.productCode,
  });

  // Copy with method
  AdaRelContract copyWith({
    String? contractNumber,
    String? accountStatus,
    String? contractType,
    String? gmaProductType,
    String? contractStatus,
    String? currency,
    Decimal? minimumDueAmount,
    Decimal? totalAmountDue,
    DateTime? dueDate,
    int? dueDay,
    int? dpd,
    Decimal? outstandingBalance,
    int? billingDay,
    bool? isEligible,
    String? productCode,
  }) {
    return AdaRelContract(
      contractNumber: contractNumber ?? this.contractNumber,
      gmaProductType: gmaProductType ?? this.gmaProductType,
      billingDay: billingDay ?? this.billingDay,
      isEligible: isEligible ?? this.isEligible,
      currency: currency ?? this.currency,
      minimumDueAmount: minimumDueAmount ?? this.minimumDueAmount,
      totalAmountDue: totalAmountDue ?? this.totalAmountDue,
      dueDate: dueDate ?? this.dueDate,
      dueDay: dueDay ?? this.dueDay,
      dpd: dpd ?? this.dpd,
      outstandingBalance: outstandingBalance ?? this.outstandingBalance,
      productCode: productCode ?? this.productCode,
    );
  }

  // FromJson method
  factory AdaRelContract.fromJson(Map<dynamic, dynamic> json) {
    return AdaRelContract(
      contractNumber: json['contractNumber'] as String?,
      gmaProductType: json['gmaProductType'] as String?,
      billingDay: json['billingDay'] as int?,
      isEligible: json['isEligible'] as bool?,
      currency: json['currency'] as String?,
      minimumDueAmount: json['minimumDueAmount'] != null ? Decimal.parse(json['minimumDueAmount']) : null,
      totalAmountDue: json['totalAmountDue'] != null ? Decimal.parse(json['totalAmountDue']) : null,
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      dueDay: json['dueDay'],
      dpd: json['dpd'] as int?,
      outstandingBalance: json['outstandingBalance'] != null ? Decimal.parse(json['outstandingBalance']) : null,
      productCode: json['productCode'] as String?,
    );
  }

  // ToJson method
  Map<String, dynamic> toJson() {
    return {
      'contractNumber': contractNumber,
      'gmaProductType': gmaProductType,
      'billingDay': billingDay,
      'isEligible': isEligible,
      'currency': currency,
      'minimumDueAmount': minimumDueAmount?.toString(),
      'totalAmountDue': totalAmountDue?.toString(),
      'dueDate': dueDate?.toIso8601String(),
      'dueDay': dueDay,
      'dpd': dpd,
      'outstandingBalance': outstandingBalance?.toString(),
      'productCode': productCode,
    };
  }

  @override
  List<Object?> get props => [contractNumber];
}
