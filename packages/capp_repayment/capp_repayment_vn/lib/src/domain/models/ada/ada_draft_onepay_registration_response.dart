import 'package:equatable/equatable.dart';

class AdaDraftOnepayRegistrationResponse extends Equatable {
  final String? id;
  final String? ddmCode;
  final String? redirectLink;
  final bool? isEnrolled;

  const AdaDraftOnepayRegistrationResponse({
    this.id,
    this.ddmCode,
    this.redirectLink,
    this.isEnrolled,
  });

  @override
  List<Object?> get props => [id, ddmCode, redirectLink, isEnrolled];
}
