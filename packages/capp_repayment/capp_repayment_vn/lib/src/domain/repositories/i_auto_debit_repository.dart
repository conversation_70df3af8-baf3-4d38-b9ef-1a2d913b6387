import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';

import '../../../capp_repayment.dart';

abstract class IAutoDebitRepository {
  Future<Either<RepaymentFailure, AdaZaloRegistrationResponse>> createZaloBinding(AdaZaloRegistrationRequest request);
  Future<Either<RepaymentFailure, AdaOnepayRegistrationResponse>> registerOnepay(
    AdaOnepayRegistrationRequest request,
  );

  // Contract
  Future<AdaContract?> getContractFromCache();
  Future<bool> setContractToCache(AdaContract contract);
  Future<Either<RepaymentFailure, List<AdaContract>>> getAdaContracts();
  Future<Either<RepaymentFailure, List<AdaEligibleContract>>> getAdaEligibleContracts();

  // Payment method
  Future<List<RepaymentUserPaymentMethod>> getBackupUserPaymentMethods({
    required L10nCappRepayment l10nCappRepayment,
  });
  Future<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>> fetchUserPaymentMethods();

  Future<RepaymentUserPaymentMethod?> getPreviousPaymentMethodFromCache();
  Future<bool> setPaymentMethodToCache(RepaymentUserPaymentMethod? paymentMethod);

  // Enrolled ada contract
  Future<Either<RepaymentFailure, List<AdaEnrolledContract>>> fetchAdaEnrolledContracts();
  Future<Either<RepaymentFailure, bool>> cancelAdaZalo({String? adaId, String? flowId});

  Future<Either<RepaymentFailure, AdaOnepayCancelResponse>> cancelAdaOnePay({
    String? adaId,
    String? flowId,
    required String redirectLink,
  });

  // Save ada draft result
  Future<void> setAdaDraftResult(AdaDraftResult result);
  Future<void> removeAdaDraftResult();
  Future<AdaDraftResult?> getAdaDraftResult();

  // Intro
  Future<bool?> shouldDisplayIntro();
  Future<void> setDidDisplayIntro();

  Future<Either<RepaymentFailure, AdaDraftZaloRegistrationResponse>> createDraftZaloBinding(
      AdaDraftZaloRegistrationRequest request,);

  Future<Either<RepaymentFailure, AdaDraftOnepayRegistrationResponse>> registerDraftOnepay(
    AdaDraftOnepayRegistrationRequest request,
  );

  // Contract
  Future<AdaDraftContract?> getAdaDraftContract();
  Future<void> removeAdaDraftContract();
  Future<bool> setAdaDraftContractToCache(AdaDraftContract adaDraftContract);

  Future<void> setAdaFromFlow(AdaFromFlow fromFlow);
  Future<AdaFromFlow?> getAdaFromFlow();

  Future<Either<RepaymentFailure, AdaContractDetail>> getAdaContractDetail({required String id});
}
