import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_navigation_annotation/koyal_navigation_annotation.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../capp_repayment.dart';
import 'domain/index.dart' as vn_domain;
import 'infrastructure/index.dart' as vn_infrastructure;

part 'capp_repayment_base.g.dart';

@PackageRouter(
  routes: [
    PackageRoute(
      OnePayScreen,
      routeName: 'onepay_screen',
      useWrapper: true,
      arguments: OnePayRouteArgs,
      bussinesRouteName: 'rpy_atm_processing',
    ),
    PackageRoute(
      RepaymentPaymentSummaryScreen,
      routeName: 'repayment_payment_summary_screen',
      bussinesRouteName: 'rpy_onl_pmt_smry',
      useWrapper: true,
      arguments: RepaymentPaymentSummaryRouteArgs,
    ),
    PackageRoute(
      RepaymentNoLoanScreen,
      routeName: 'repayment_no_loan_screen',
      bussinesRouteName: 'rpy_no_loan',
      arguments: RepaymentNoLoanRouteArgs,
      argumentsNullable: true,
    ),
    PackageRoute(
      RepaymentTransactionFailedScreen,
      routeName: 'repayment_transaction_failed_screen',
      bussinesRouteName: 'rpy_trans_error',
      arguments: RepaymentTransactionFailedRouteArgs,
      argumentsNullable: true,
    ),
    PackageRoute(
      RepaymentFailedScreen,
      routeName: 'repayment_failed_screen',
      bussinesRouteName: 'rpy_error',
      arguments: RepaymentFailedRouteArgs,
    ),
    PackageRoute(
      RepaymentBankTransferScreen,
      routeName: 'repayment_bank_transfer_screen',
      bussinesRouteName: 'rpy_bnk_trnsf',
      useWrapper: true,
      arguments: RepaymentBankTransferRouteArgs,
    ),
    PackageRoute(
      RepaymentPaymentResultScreen,
      routeName: 'repayment_payment_result_screen',
      bussinesRouteName: 'rpy_result',
      useWrapper: true,
      arguments: ScreenArguments,
    ),
    PackageRoute(
      RepaymentCheckResultScreen,
      routeName: 'repayment_check_result_screen',
      bussinesRouteName: 'rpy_result_check',
      useWrapper: true,
      arguments: RepaymentCheckResultRouteArgs,
    ),
    PackageRoute(
      ViettelMoneyScreen,
      routeName: 'viettelmoney_screen',
      bussinesRouteName: 'rpy_viettel_processing',
      useWrapper: true,
      arguments: ViettelMoneyRouteArgs,
    ),
    PackageRoute(
      RepaymentTransactionSuccessScreen,
      routeName: 'repayment_transaction_success_screen',
      useWrapper: true,
      arguments: RepaymentTransactionSuccessRouteArgs,
      bussinesRouteName: 'rpy_trans_success',
    ),
    PackageRoute(
      RepaymentBankTransferQrScreen,
      routeName: 'repayment_bank_transfer_qr_screen',
      useWrapper: true,
      arguments: RepaymentBankTransferRouteArgs,
      bussinesRouteName: 'rpy_bnk_trnsf',
    ),
    PackageRoute(
      RepaymentMainScreen,
      routeName: 'repayment_main_screen',
      useWrapper: true,
      arguments: RepaymentMainRouteArgs,
      bussinesRouteName: 'rpy_main',
    ),
    PackageRoute(
      RepaymentBankingAppScreen,
      routeName: 'repayment_banking_app_screen',
      useWrapper: true,
      arguments: RepaymentBankingAppRouteArgs,
      bussinesRouteName: 'rpy_mobbnk_apps',
    ),
    PackageRoute(
      RepaymentPtpIntroductionScreen,
      routeName: 'repayment_ptp_introduction_screen',
      useWrapper: true,
      arguments: RepaymentPtpIntroductionRouteArgs,
      bussinesRouteName: 'ptp_intro_first_open',
    ),
    PackageRoute(
      RepaymentPtpPaymentPlanScreen,
      routeName: 'repayment_ptp_payment_plan_screen',
      useWrapper: true,
      arguments: RepaymentPtpPaymentPlanRouteArgs,
      bussinesRouteName: 'ptp_plan',
    ),
    PackageRoute(
      RepaymentPtpOptionsScreen,
      routeName: 'repayment_ptp_options_screen',
      useWrapper: true,
      arguments: RepaymentPtpOptionsRouteArgs,
      bussinesRouteName: 'ptp_information',
    ),
    PackageRoute(
      RepaymentPtpProcessingScreen,
      routeName: 'repayment_ptp_processing_screen',
      useWrapper: true,
      arguments: RepaymentPtpProcessingRouteArgs,
      bussinesRouteName: 'ptp_loading',
    ),
    PackageRoute(
      RepaymentPtpSuccessScreen,
      routeName: 'repayment_ptp_success_screen',
      useWrapper: true,
      arguments: RepaymentPtpSuccessRouteArgs,
      bussinesRouteName: 'ptp_success',
    ),
    PackageRoute(
      RepaymentPtpEligibilityCheckingScreen,
      routeName: 'repayment_ptp_eligibility_checking_screen',
      bussinesRouteName: 'ptp_check',
      useWrapper: true,
      arguments: RepaymentPtpEligibilityCheckingRouteArgs,
    ),
    PackageRoute(
      RepaymentPromotionDetailScreen,
      routeName: 'repayment_promotion_detail_screen',
      bussinesRouteName: 'rpy_promo_detail',
      useWrapper: true,
      arguments: RepaymentPromotionDetailRouteArgs,
    ),
    PackageRoute(
      RepaymentPtpRejectedScreen,
      routeName: 'repayment_ptp_rejected_screen',
      useWrapper: true,
      arguments: RepaymentPtpRejectedRouteArgs,
      bussinesRouteName: 'promise_to_pay_rejected',
    ),
    PackageRoute(
      RepaymentAdaIntroScreen,
      routeName: 'repayment_ada_intro_screen',
      useWrapper: true,
      arguments: RepaymentAdaIntroRouteArgs,
      bussinesRouteName: 'ad_intro',
    ),
    PackageRoute(
      RepaymentAdaManagementScreen,
      arguments: ScreenArguments,
      argumentsNullable: true,
      routeName: 'repayment_ada_management_screen',
      useWrapper: true,
      bussinesRouteName: 'ad_mgm',
    ),
    PackageRoute(
      RepaymentAdaSuccessScreen,
      routeName: 'repayment_ada_success_screen',
      arguments: RepaymentAdaSuccessRouteArgs,
      bussinesRouteName: 'ad_success',
    ),
    PackageRoute(
      RepaymentAdaMainScreen,
      arguments: RepaymentAdaMainRouteArgs,
      argumentsNullable: true,
      useWrapper: true,
      routeName: 'repayment_ada_main_screen',
      bussinesRouteName: 'ad_main',
    ),
    PackageRoute(
      RepaymentAdaCheckResultScreen,
      useWrapper: true,
      arguments: ScreenArguments,
      routeName: 'repayment_ada_check_result_screen',
      bussinesRouteName: 'ad_check_result',
    ),
    PackageRoute(
      RepaymentAdaFailedScreen,
      routeName: 'repayment_ada_failed_screen',
      arguments: RepaymentAdaFailedRouteArgs,
      bussinesRouteName: 'ad_fail',
    ),
    PackageRoute(
      RepaymentAdaDetailScreen,
      arguments: RepaymentAdaDetailRouteArgs,
      useWrapper: true,
      routeName: 'repayment_ada_detail_screen',
      bussinesRouteName: 'ad_detail',
    ),
    PackageRoute(
      AdaOnePayScreen,
      routeName: 'ada_onepay_screen',
      useWrapper: true,
      arguments: AdaOnePayRouteArgs,
      bussinesRouteName: 'ad_atm_processing',
    ),
    PackageRoute(
      LooAdaMainScreen,
      arguments: LooAdaMainRouteArgs,
      argumentsNullable: true,
      useWrapper: true,
      routeName: 'loo_ada_main_screen',
      bussinesRouteName: 'loo_ad_main',
    ),
    PackageRoute(
      RepaymentBankTransferQrIntroScreen,
      routeName: 'repayment_bank_transfer_qr_intro_screen',
    ),
    PackageRoute(
      RepaymentBankTransferQrWaitingScreen,
      routeName: 'repayment_bank_transfer_qr_waiting_screen',
    ),
  ],
)
abstract class _CappRepayment extends ChildPackage {
  late final CappRepaymentSettingsVn settings;

  @override
  bool get isPlaceholder => true;
  @override
  void onNewTranslations(Translations translations) {
    L10nCappRepayment.translations[translations.contentLanguage] = translations.forPackage('capp_repayment');
  }

  @override
  LocalizationsDelegate get localizationsDelegate => L10nCappRepayment.delegate;

  @override
  void registerDependencies(GetIt c) {
    registerCoreDependencies(c);
    registerStorage(c);
    registerApi(c);
    registerServices(c);
    registerRepositories(c);
    registerBlocs(c);
    registerDevTools(c);
  }

  void registerStorage(GetIt c) {
    c
      ..registerLazySingleton(
        () => RepaymentStorageOriginal(
          storage: ReactiveStorage(
            storageProvider: c<SembastStorageProvider>(instanceName: sembastStorageProviderNoCleanup),
            logger: c<Logger>(),
          ),
        ),
      )
      ..registerLazySingleton(
        () => RepaymentStorage(
          storage: c<GmaStorageProvider>(),
        ),
      )
      ..registerLazySingleton<IAutoDebitStorage>(
        () => AutoDebitStorage(
          storage: c<GmaStorageProvider>(),
        ),
      );
  }

  void registerApi(GetIt c) {}

  void registerBlocs(GetIt c) {
    c
      ..registerTrackingFactory(
        () => OnePayBloc(
          localizationBloc: c.get<LocalizationBloc>(),
          settings: settings,
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPaymentSummaryBloc(
          logger: c.get<Logger>(),
          lockStatusBloc: c.get<LockStatusBloc>(),
          settings: settings,
          userRepository: c<IUserRepository>(),
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentMethodSelectionBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
          featureFlagRepository: c.get<IFeatureFlagRepository>(),
          imageService: c<ImageServiceBase>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentSelectAmountCelBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentSelectAmountRelBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentBankTransferBloc(logger: c.get<Logger>()),
      )
      ..registerTrackingFactory(
        () => RepaymentPaymentResultBloc(
          lockStatusBloc: c.get<LockStatusBloc>(),
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
          userRepository: c<IUserRepository>(),
          logger: c.get<Logger>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentCheckResultBloc(repaymentRepository: c.get<vn_domain.IRepaymentRepository>()),
      )
      ..registerTrackingFactory(
        () => ViettelMoneyBloc(settings: settings, repaymentRepository: c.get<vn_domain.IRepaymentRepository>()),
      )
      ..registerTrackingFactory(
        () => RepaymentBankTransferQrBloc(
          logger: c.get<Logger>(),
          downloadService: c<DownloadService>(),
          promotionRepository: c.get<IPromotionRepository>(),
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentMainBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
          userRepository: c<IUserRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
          promotionRepository: c<IPromotionRepository>(),
          imageService: c<ImageServiceBase>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentContractListNewBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentAdaContractListBloc(
          logger: c.get<Logger>(),
          autoDebitRepository: c.get<IAutoDebitRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPaymentMethodBloc(
          logger: c.get<Logger>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentBankingAppBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPtpIntroductionBloc(
          repaymentRepository: c.get<IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPtpPaymentPlanBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        RepaymentPtpOptionsBloc.new,
      )
      ..registerTrackingFactory(
        () => RepaymentPtpProcessingBloc(
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
          identityRepository: c<IIdentityRepository>(),
          logger: c.get<Logger>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPtpSuccessBloc(
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPtpEligibilityCheckingBloc(
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
          identityRepository: c<IIdentityRepository>(),
          logger: c.get<Logger>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPromotionListBloc(
          logger: c.get<Logger>(),
          promotionRepository: c.get<vn_domain.IPromotionRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPromotionFirstAdoptionBloc(
          promotionRepository: c.get<vn_domain.IPromotionRepository>(),
          userRepository: c<IUserRepository>(),
          storage: c.get<RepaymentStorage>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPromotionDetailBloc(
          logger: c.get<Logger>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentPtpRejectedBloc(
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentTransactionSuccessBloc(
          featureFlagRepository: c<IFeatureFlagRepository>(),
          autoDebitRepository: c<IAutoDebitRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentAdaManagementBloc(
          logger: c.get<Logger>(),
          autoDebitRepository: c.get<IAutoDebitRepository>(),
          userRepository: c.get<IUserRepository>(),
          featureFlagRepository: c.get<IFeatureFlagRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentAdaMainBloc(
          lockStatusBloc: c.get<LockStatusBloc>(),
          logger: c.get<Logger>(),
          userRepository: c<IUserRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
          autoDebitRepository: c<IAutoDebitRepository>(),
          localizationRepository: c<ILocalizationRepository>(),
          settings: settings,
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentAdaCheckResultBloc(
          logger: c.get<Logger>(),
          lockStatusBloc: c.get<LockStatusBloc>(),
          autoDebitRepository: c<IAutoDebitRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentAdaPaymentMethodBloc(
          logger: c.get<Logger>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentAdaIntroBloc(
          autoDebitRepository: c<IAutoDebitRepository>(),
          logger: c.get<Logger>(),
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentAdaDetailBloc(
          logger: c.get<Logger>(),
          settings: settings,
          autoDebitRepository: c.get<IAutoDebitRepository>(),
        ),
      )
      ..registerTrackingFactory(
        () => AdaOnePayBloc(
          localizationBloc: c.get<LocalizationBloc>(),
          settings: settings,
        ),
      )
      ..registerTrackingFactory(
        () => LooAdaMainBloc(
          lockStatusBloc: c.get<LockStatusBloc>(),
          logger: c.get<Logger>(),
          userRepository: c<IUserRepository>(),
          featureFlagRepository: c<IFeatureFlagRepository>(),
          autoDebitRepository: c<IAutoDebitRepository>(),
          localizationRepository: c<ILocalizationRepository>(),
          settings: settings,
        ),
      )
      ..registerTrackingFactory(
        () => RepaymentQrBankBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<vn_domain.IRepaymentRepository>(),
        ),
      );
  }

  void registerServices(GetIt c) {
    c.registerLazySingleton<DownloadService>(() => DownloadService(dio: c<Dio>(instanceName: CappApi.dio)));
  }

  void registerRepositories(GetIt c) {
    c.get<ITranslationOverrideRepository>().registerTranslation(() {
      L10nCappRepayment.showKeys = !L10nCappRepayment.showKeys;
    });
    c
      ..registerLazySingleton<vn_domain.IRepaymentRepository>(
        () => vn_infrastructure.RepaymentRepository(
          txnApi: c.get<VnRepaymentTxnApi>(),
          api: c.get<RepaymentApi>(),
          repaymentStorage: c.get<RepaymentStorage>(),
          logger: c.get<Logger>(),
          ptpApi: c.get<VnRepaymentPtpApi>(),
          paymentMethodApi: c.get<PaymentMethodsApi>(),
          featureFlagRepository: c.get<IFeatureFlagRepository>(),
          localizationRepository: c.get<ILocalizationRepository>(),
        ),
      )
      ..registerLazySingleton<vn_domain.IAutoDebitRepository>(
        () => vn_infrastructure.AutoDebitRepository(
          adaApi: c.get<VnAdaApi>(),
          adaContractApi: c.get<VnAdaContractApi>(),
          autoDebitStorage: c.get<IAutoDebitStorage>(),
          logger: c.get<Logger>(),
          featureFlagRepository: c.get<IFeatureFlagRepository>(),
          localizationRepository: c.get<ILocalizationRepository>(),
          paymentMethodApi: c.get<PaymentMethodsApi>(),
        ),
      )
      ..registerLazySingleton<vn_domain.IPromotionRepository>(
        () => vn_infrastructure.PromotionRepository(
          logger: c.get<Logger>(),
          promotionApi: c.get<RepaymentPromotionApi>(),
        ),
      );
  }

  void registerDevTools(GetIt c) {}
}
