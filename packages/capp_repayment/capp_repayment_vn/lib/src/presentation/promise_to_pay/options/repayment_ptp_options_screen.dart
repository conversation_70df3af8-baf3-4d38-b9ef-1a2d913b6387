import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_repayment.dart';

class RepaymentPtpOptionsScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPtpOptionsRouteArgs arguments;

  const RepaymentPtpOptionsScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentPtpOptionsBloc>()
          ..add(
            RepaymentPtpOptionsEvent.initialize(
              ptpContract: arguments.ptpContract,
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentPtpOptionsScreen> createState() => _RepaymentPtpOptionsScreenState();
}

class _RepaymentPtpOptionsScreenState extends State<RepaymentPtpOptionsScreen> {
  late RepaymentPtpOptionsBloc _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of<RepaymentPtpOptionsBloc>(context);

    context.get<CappRepaymentTrackingService>().trackPromiseToPayInformationScreenView();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RepaymentPtpOptionsBloc, RepaymentPtpOptionsState>(
      builder: (ctx, state) {
        String promiseToPayTitle;
        String? promiseToPayMessage;
        final ptpContract = state.ptpContract;
        final dueDate = ptpContract?.dueDate;
        final totalAmount = state.ptpContract?.totalAmount;
        final dpd = ptpContract?.dpd?.abs();
        switch (state.type) {
          case RepaymentPtpType.early:
            {
              promiseToPayTitle = L10nCappRepayment.of(context)
                  .promiseToPayEarlyMessage(dpd)
                  .replaceAll('days', (dpd ?? 0) <= 1 ? 'day' : 'days');
              break;
            }
          case RepaymentPtpType.onTime:
            {
              promiseToPayTitle = L10nCappRepayment.of(context).promiseToPayOnTimeMessage;
              promiseToPayMessage = L10nCappRepayment.of(context).promiseToPayRemindMessage;
              break;
            }
          case RepaymentPtpType.late:
            {
              promiseToPayTitle = L10nCappRepayment.of(context)
                  .promiseToPayLateMessage(dpd)
                  .replaceAll('days', (dpd ?? 0) <= 1 ? 'day' : 'days');
              promiseToPayMessage = L10nCappRepayment.of(context).promiseToPayRemindMessage;
              break;
            }
          default:
            {
              promiseToPayTitle = '';
              break;
            }
        }
        return KoyalScaffold(
          appBar: KoyalAppBar(
            title: L10nCappRepayment.of(context).promiseToPay,
            actions: [
              KoyalIconButton(
                onPressed: () async {
                  context.get<CappRepaymentTrackingService>().trackPromiseToPayOptionsClickQuestion();
                  await showModalBottomSheet<bool?>(
                    isScrollControlled: true,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30.0),
                    ),
                    context: context,
                    useRootNavigator: true,
                    builder: (c) => Container(
                      color: ColorTheme.of(context).backgroundColor,
                      padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
                      child: RepaymentPtpIntroductionScreen(
                        arguments: RepaymentPtpIntroductionRouteArgs(
                          isDialog: true,
                          ptpContract: _bloc.state.ptpContract!,
                        ),
                      ).wrappedRoute(context),
                    ),
                  );
                },
                iconData: KoyalIcons.help_circle_outline,
              ),
            ],
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Image.asset(
                        'assets/images/bg_instalment_cancellation.png',
                        fit: BoxFit.fitWidth,
                        width: MediaQuery.of(context).size.width,
                        package: 'capp_repayment',
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: MainHeading(
                          title: promiseToPayTitle,
                          subtitle: promiseToPayMessage,
                        ),
                      ),
                      ExtendedInfoListItem(
                        title: L10nCappRepayment.of(context).repaymentDueDateNormal,
                        bodyText: dueDate?.toLocal().mediumDate() ?? '',
                      ),
                      const ListDivider(),
                      ExtendedInfoListItem(
                        title: L10nCappRepayment.of(context).repaymentTotalDueAmountNormal,
                        bodyText: totalAmount?.formatCurrency().replaceAll('', '\u200B') ?? '',
                        useBoldBodyText: true,
                      ),
                    ],
                  ),
                ),
              ),
              VerticalButtonsLayout(
                primaryButton: PrimaryButton(
                  text: L10nCappRepayment.of(context).promiseToPay,
                  onPressed: () async {
                    context.get<CappRepaymentTrackingService>().trackPromiseToPayOptionsClickPromise();
                    await context.navigator.pushTyped(
                      package: CappRepayment,
                      screen: RepaymentPtpPaymentPlanScreen,
                      arguments: RepaymentPtpPaymentPlanRouteArgs(
                        ptpContract: _bloc.state.ptpContract!,
                      ),
                    );
                  },
                ),
                tertiaryButton: TertiaryButton(
                  text: L10nCappRepayment.of(context).repaymentRepayNow,
                  onPressed: () async {
                    final contractNumber = state.ptpContract?.entityId ?? '';
                    if (contractNumber.isNotEmpty) {
                      // Temp contract to get contract number only in RepaymentMain
                      final contract = RepaymentContract(
                        relContract: RepaymentRelContract(accountNumber: contractNumber),
                      );
                      context.get<CappRepaymentTrackingService>().trackPromiseToPayOptionsClickRepayNow();
                      await NavigationUtils.navigateToRepaymentMain(
                        context: context,
                        isFromPayNow: true,
                        selectedContract: contract,
                        viewType: RepaymentViewType.methodSelection,
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
