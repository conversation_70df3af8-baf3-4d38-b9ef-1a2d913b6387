import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentPtpProcessingView extends StatelessWidget {
  final Widget? icon;
  final String title;
  final String subtitle;
  final bool isShowBackButton;
  final VoidCallback onBackButtonClicked;

  const RepaymentPtpProcessingView({
    this.icon,
    required this.title,
    required this.subtitle,
    required this.isShowBackButton,
    required this.onBackButtonClicked,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Spacer(),
          if (icon != null)
            SizedBox(
              height: 264,
              width: 264,
              child: icon,
            ),
          //const Spacer(),
          KoyalPadding.normalVertical(
            child: MainHeading(
              title: title,
              subtitle: subtitle,
            ),
          ),
          const Spacer(),
          if (isShowBackButton)
            VerticalButtonsLayout(
              primaryButton: SecondaryButton(
                key: const Key('__PTPDoneButton__'),
                text: L10nCappRepayment.of(context).repaymentPtpProcessBackButton,
                onPressed: onBackButtonClicked,
              ),
            )
          else
            const SizedBox(),
        ],
      ),
    );
  }
}
