import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_repayment.dart';
import 'widgets/repayment_ptp_processing_view.dart';

class RepaymentPtpProcessingScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPtpProcessingRouteArgs arguments;

  const RepaymentPtpProcessingScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentPtpProcessingBloc>()
          ..add(
            RepaymentPtpProcessingEvent.initialize(
              ptpContract: arguments.ptpContract,
              ptpEvaluation: arguments.submitPtpResult ?? arguments.ptpContract?.lastPTP,
            ),
          ),
        child: this,
      );

  @override
  RepaymentPtpProcessingScreenState createState() => RepaymentPtpProcessingScreenState();
}

class RepaymentPtpProcessingScreenState extends State<RepaymentPtpProcessingScreen> {
  late RepaymentPtpProcessingBloc _bloc;

  @override
  void initState() {
    _bloc = BlocProvider.of<RepaymentPtpProcessingBloc>(context);
    super.initState();

    context.get<CappRepaymentTrackingService>().trackPromiseToPayProcessingScreenView();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepaymentPtpProcessingBloc, RepaymentPtpProcessingState>(
      listener: (ctx, state) {
        final redirect = state.redirect ?? RepaymentPtpProcessingRedirect.none;
        if (redirect == RepaymentPtpProcessingRedirect.success) {
          _navigateToSuccessScreen(state.ptpContract, state.ptpEvaluation);
        } else if (redirect == RepaymentPtpProcessingRedirect.rejected) {
          _navigateToRejectedScreen(state.ptpContract, state.ptpEvaluation);
        } else if (redirect == RepaymentPtpProcessingRedirect.timeout) {
          context.navigator.toMainScreen();
        }
      },
      bloc: _bloc,
      builder: (context, state) {
        return KoyalWillPopScope(
          onWillPop: () async {
            return false;
          },
          child: KoyalScaffold(
            key: const Key('__PTPProcessingScreen__'),
            body: Container(
              color: Colors.transparent,
              width: double.infinity,
              height: double.infinity,
              child: _buildBody(context, state),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, RepaymentPtpProcessingState state) {
    return RepaymentPtpProcessingView(
      title: L10nCappRepayment.of(context).repaymentPtpProcessingTitle,
      subtitle: L10nCappRepayment.of(context).repaymentPtpProcessingSubTitle,
      icon: const LoadingWidget(),
      isShowBackButton: state.isShowBackButton == true,
      onBackButtonClicked: () {
        context.get<CappRepaymentTrackingService>().trackPromiseToPayProcessingClickClose();
        context.navigator.toMainScreen();
      },
    );
  }

  void _navigateToSuccessScreen(RepaymentPtpContract? ptpContract, RepaymentPtpEvaluation? submitPtpResult) {
    context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentPtpSuccessScreen,
      arguments: RepaymentPtpSuccessRouteArgs(ptpContract: ptpContract, submitPtpResult: submitPtpResult),
    );
  }

  void _navigateToRejectedScreen(RepaymentPtpContract? ptpContract, RepaymentPtpEvaluation? submitPtpResult) {
    context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentPtpRejectedScreen,
      arguments: RepaymentPtpRejectedRouteArgs(ptpContract: ptpContract, submitPtpResult: submitPtpResult),
    );
  }
}
