import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_repayment.dart';
import 'widgets/repayment_ptp_eligibility_checking_view.dart';

class RepaymentPtpEligibilityCheckingScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPtpEligibilityCheckingRouteArgs arguments;

  const RepaymentPtpEligibilityCheckingScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentPtpEligibilityCheckingBloc>()
          ..add(
            RepaymentPtpEligibilityCheckingEvent.initialize(contractNumber: arguments.contractNumber),
          ),
        child: this,
      );

  @override
  RepaymentPtpEligibilityCheckingScreenState createState() => RepaymentPtpEligibilityCheckingScreenState();
}

class RepaymentPtpEligibilityCheckingScreenState extends State<RepaymentPtpEligibilityCheckingScreen> {
  late RepaymentPtpEligibilityCheckingBloc _bloc;

  @override
  void initState() {
    _bloc = BlocProvider.of<RepaymentPtpEligibilityCheckingBloc>(context);
    super.initState();

    context.get<CappRepaymentTrackingService>().trackPromiseToPayEligibilityCheckingScreenView();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepaymentPtpEligibilityCheckingBloc, RepaymentPtpEligibilityCheckingState>(
      listener: (ctx, state) {
        if (state.isError ?? false) {
          context.get<CappRepaymentTrackingService>().trackPromiseToPayEligibilityCheckingPopupError();

          _showRetryPopup(ctx);
          return;
        }
        final redirect = state.redirect ?? RepaymentPtpEligibilityCheckingRedirect.none;

        if (redirect == RepaymentPtpEligibilityCheckingRedirect.intro) {
          _navigateToPTPIntroScreen(state);
        } else if (redirect == RepaymentPtpEligibilityCheckingRedirect.option) {
          _navigateToPTPOptionScreen(state);
        } else if (redirect == RepaymentPtpEligibilityCheckingRedirect.success) {
          _navigateToPTPSuccessScreen(state);
        } else if (redirect == RepaymentPtpEligibilityCheckingRedirect.processing) {
          _navigateToPTPProcressingScreen(state);
        } else if (redirect == RepaymentPtpEligibilityCheckingRedirect.failPopup) {
          _showEligibleFailPopup(ctx, state);
        }
      },
      bloc: _bloc,
      builder: (context, state) {
        return KoyalWillPopScope(
          onWillPop: () async {
            return false;
          },
          child: KoyalScaffold(
            appBar: KoyalAppBar(
              title: L10nCappRepayment.of(context).repaymentPtpEligibilityCheckingTitle,
              onClose: () => context.navigator.pop(),
              onGoBack: () {
                context.navigator.pop();
              },
            ),
            body: Container(
              color: Colors.transparent,
              width: double.infinity,
              height: double.infinity,
              child: _buildBody(context, state),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, RepaymentPtpEligibilityCheckingState state) {
    return state.loadingState == LoadingState.isLoading
        ? const RepaymentPtpEligibilityCheckingView(
            icon: LoadingWidget(),
          )
        : Container();
  }

  void _navigateToPTPIntroScreen(RepaymentPtpEligibilityCheckingState state) {
    if (state.ptpContract == null) {
      return;
    }
    context.navigator.pushReplacementTyped(
      package: CappRepayment,
      screen: RepaymentPtpIntroductionScreen,
      arguments: RepaymentPtpIntroductionRouteArgs(
        ptpContract: state.ptpContract!,
        isDialog: false,
      ),
    );
  }

  void _navigateToPTPOptionScreen(RepaymentPtpEligibilityCheckingState state) {
    if (state.ptpContract == null) {
      return;
    }
    context.navigator.pushReplacementTyped(
      package: CappRepayment,
      screen: RepaymentPtpOptionsScreen,
      arguments: RepaymentPtpOptionsRouteArgs(
        ptpContract: state.ptpContract!,
      ),
    );
  }

  void _navigateToPTPSuccessScreen(RepaymentPtpEligibilityCheckingState state) {
    if (state.ptpContract == null) {
      return;
    }
    context.navigator.pushReplacementTyped(
      package: CappRepayment,
      screen: RepaymentPtpSuccessScreen,
      arguments: RepaymentPtpSuccessRouteArgs(ptpContract: state.ptpContract),
    );
  }

  void _navigateToPTPProcressingScreen(RepaymentPtpEligibilityCheckingState state) {
    if (state.ptpContract == null) {
      return;
    }
    context.navigator.pushReplacementTyped(
      package: CappRepayment,
      screen: RepaymentPtpProcessingScreen,
      arguments: RepaymentPtpProcessingRouteArgs(ptpContract: state.ptpContract),
    );
  }

  void _showEligibleFailPopup(BuildContext context, RepaymentPtpEligibilityCheckingState state) {
    if (state.ptpContract == null) {
      return;
    }
    final title = L10nCappRepayment.of(context).repaymentPtpEligibilityPopupTitle;
    showKoyalOverlay<void>(
      context,
      key: const Key('__EligibleFailPopup__'),
      dismissible: false,
      title: title,
      primaryButtonBuilder: (context) => PrimaryButton(
        key: const Key('__EligibleFailPopupContinueButton__'),
        text: L10nCappRepayment.of(context).repaymentPtpEligibilityPopupISeeButton,
        onPressed: () {
          context.navigator.pop();
          Navigator.pop(context);
        },
      ),
    );
  }

  void _showRetryPopup(BuildContext context) {
    final title = L10nCappRepayment.of(context).repaymentPtpPopupRetryTitle;
    showKoyalOverlay<String>(
      context,
      title: title,
      dismissible: false,
      primaryButtonBuilder: (ctx) => PrimaryButton(
        key: const Key('__retry__'),
        onPressed: () {
          context.get<CappRepaymentTrackingService>().trackPromiseToPayEligibilityCheckingPopupErrorClickRetry();

          ctx.navigator.pop();
          context
              .read<RepaymentPtpEligibilityCheckingBloc>()
              .add(const RepaymentPtpEligibilityCheckingEvent.checkPtpEligibility());
        },
        text: L10nCappRepayment.of(context).repaymentPtpPopupRetryRetryButton,
      ),
      tertiaryButtonBuilder: (ctx) => TertiaryButton(
        text: L10nCappRepayment.of(context).repaymentPtpPopupRetryCancelButton,
        onPressed: () {
          context.get<CappRepaymentTrackingService>().trackPromiseToPayEligibilityCheckingPopupErrorClickBackToHome();

          ctx.navigator.pop();
          Navigator.pop(context);
        },
      ),
    );
  }
}
