import 'package:flutter/material.dart';

class RepaymentPtpEligibilityCheckingView extends StatelessWidget {
  final Widget? icon;

  const RepaymentPtpEligibilityCheckingView({
    this.icon,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          const Spacer(),
          if (icon != null)
            SizedBox(
              height: 264,
              width: 264,
              child: icon,
            ),
          const Spacer(),
        ],
      ),
    );
  }
}
