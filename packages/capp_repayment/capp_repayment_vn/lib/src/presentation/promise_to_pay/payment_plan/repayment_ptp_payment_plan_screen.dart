import 'dart:async';

import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_repayment.dart';

class RepaymentPtpPaymentPlanScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPtpPaymentPlanRouteArgs arguments;

  const RepaymentPtpPaymentPlanScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  RepaymentPtpPaymentPlanScreenState createState() {
    return RepaymentPtpPaymentPlanScreenState();
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentPtpPaymentPlanBloc>()
          ..add(
            RepaymentPtpPaymentPlanEvent.initialize(
              ptpContract: arguments.ptpContract,
            ),
          ),
        child: this,
      );
}

class RepaymentPtpPaymentPlanScreenState extends State<RepaymentPtpPaymentPlanScreen> {
  late RepaymentPtpPaymentPlanBloc _bloc;
  late FocusNode _focusNode;

  @override
  void initState() {
    _bloc = BlocProvider.of<RepaymentPtpPaymentPlanBloc>(context);
    _focusNode = FocusNode();
    super.initState();

    context.get<CappRepaymentTrackingService>().trackPromiseToPayPaymentPlanScreenView();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__paymentPlanScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__paymentPlanAppBar__'),
        title: L10nCappRepayment.of(context).repaymentPaymentPlan,
      ),
      body: BlocConsumer<RepaymentPtpPaymentPlanBloc, RepaymentPtpPaymentPlanState>(
        listener: (ctx, state) {
          if (state.shouldFocusCustomAmount) {
            _bloc.add(const RepaymentPtpPaymentPlanEvent.resetFocusCustomAmount());

            _focusNode.requestFocus();
          }
          if (state.loadingState == LoadingState.isCompleted) {
            state.failureOrSuccessSubmitPtpEvaluation.fold(() => null, (either) {
              either.fold(
                (l) {
                  unawaited(_showErrorPopup(context: context));
                  _bloc.add(const RepaymentPtpPaymentPlanEvent.resetState());
                },
                (r) {
                  _navigateToProcessScreen(state.ptpContract, r);
                },
              );
            });
          }
        },
        builder: (ctx, state) {
          final dueDateText = state.dueDate != null ? state.dueDate!.toLocal().mediumDate() : '';
          final extendUtilDateText = state.extensionDate != null ? state.extensionDate!.toLocal().mediumDate() : '---';
          final minPaymentAmount = state.minimumAmount.formatCurrency() ?? '';
          final extensionDayNumbers = state.extensionDayNumbers;
          final isValidInput = state.isInputValid;

          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            child: RepaymentProgressContainer(
              key: const Key('__paymentPlanContainer__'),
              isEnableBack: true,
              isLoading: state.loadingState == LoadingState.isLoading,
              child: Column(
                children: [
                  Expanded(
                    child: CustomScrollView(
                      slivers: [
                        SliverToBoxAdapter(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SectionHeading(
                                title: L10nCappRepayment.of(context).repaymentSelectPaymentDate,
                                subtitle: L10nCappRepayment.of(context).repaymentPleaseCommitAsSelected,
                              ),
                              const SizedBox(
                                height: KoyalPadding.paddingNormal,
                              ),
                              KoyalPadding.normalHorizontal(
                                child: KoyalChoiceChip(
                                  key: const Key('__paymentPlanDayChoiceChips__'),
                                  scrollable: false,
                                  options: extensionDayNumbers.map(
                                    (e) {
                                      final dayNum = e.toString();
                                      final dayLater = dayNum == '1'
                                          ? L10nCappRepayment.of(context).repaymentDayLater
                                          : L10nCappRepayment.of(context).repaymentDaysLater;
                                      return ChipFilterData(
                                        id: dayNum,
                                        label: '$e $dayLater',
                                      );
                                    },
                                  ).toList(),
                                  onSelectionChanged: (selectedOptions) {
                                    context
                                        .get<CappRepaymentTrackingService>()
                                        .trackPromiseToPayPlanClickAfterNumberOfDay(selectedOptions.first.id);
                                    _bloc.add(
                                      RepaymentPtpPaymentPlanEvent.selectExtensionDayNumber(
                                        number: int.tryParse(selectedOptions.first.id),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        SliverList(
                          delegate: SliverChildListDelegate([
                            const SizedBox(
                              height: KoyalPadding.paddingNormal,
                            ),
                            const ListDivider(),
                            const SizedBox(
                              height: KoyalPadding.paddingNormal,
                            ),
                            Row(
                              key: const Key('__paymentPlanDueDate__'),
                              children: [
                                const SizedBox(
                                  width: KoyalPadding.paddingNormal,
                                ),
                                KoyalText.body2(
                                  color: ColorTheme.of(context).secondaryTextColor,
                                  L10nCappRepayment.of(context).repaymentCurrentDueDate,
                                ),
                                const SizedBox(
                                  width: KoyalPadding.paddingNormal,
                                ),
                                Expanded(
                                  child: KoyalText.body2(
                                    textAlign: TextAlign.right,
                                    color: ColorTheme.of(context).defaultTextColor,
                                    dueDateText,
                                  ),
                                ),
                                const SizedBox(
                                  width: KoyalPadding.paddingNormal,
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: KoyalPadding.paddingNormal,
                            ),
                            const ListDivider(),
                            const SizedBox(
                              height: KoyalPadding.paddingNormal,
                            ),
                            Row(
                              key: const Key('__paymentPlanExtendUtilDate__'),
                              children: [
                                const SizedBox(
                                  width: KoyalPadding.paddingNormal,
                                ),
                                KoyalText.body2(
                                  color: ColorTheme.of(context).secondaryTextColor,
                                  L10nCappRepayment.of(context).repaymentExtendUtilDate,
                                ),
                                const SizedBox(
                                  width: KoyalPadding.paddingNormal,
                                ),
                                Expanded(
                                  child: KoyalText.subtitle1(
                                    textAlign: TextAlign.right,
                                    color: ColorTheme.of(context).defaultTextColor,
                                    extendUtilDateText,
                                  ),
                                ),
                                const SizedBox(
                                  width: KoyalPadding.paddingNormal,
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: KoyalPadding.paddingNormal,
                            ),
                            _buildAmountSelectionContainer(),
                            KoyalPadding.normalHorizontal(
                              child: Alert(
                                text: '${L10nCappRepayment.of(context).repaymentMinPaymentAmountIs} $minPaymentAmount',
                                theme: AlertTheme.info(),
                              ),
                            ),
                          ]),
                        ),
                      ],
                    ),
                  ),
                  VerticalButtonsLayout(
                    primaryButton: PrimaryButton(
                      key: const Key('__paymentPlanSendButton__'),
                      text: L10nCappRepayment.of(context).repaymentSend,
                      onPressed: isValidInput
                          ? () async {
                              context.get<CappRepaymentTrackingService>().trackPromiseToPayPlanClickSubmit();
                              FocusManager.instance.primaryFocus?.unfocus();
                              _showExtendWarningPopup();
                            }
                          : null,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAmountSelectionContainer() {
    final state = _bloc.state;
    final dueAmount = state.dueAmount;

    final amountOptions = <Widget>[
      const SectionDivider(),
      SectionHeading(
        title: L10nCappRepayment.of(context).repaymentMainPaymentAmount,
      ),
      RepaymentPtpAmountOption(
        key: const Key('__paymentPlanDueAmountOptions__'),
        subLabel: L10nCappRepayment.of(context).repaymentTotalDueAmount,
        option: RepaymentPtpAmountOptionType.dueAmount,
        selectedOption: state.selectedAmountOption,
        amount: dueAmount,
        isSelected: state.selectedAmountOption == RepaymentPtpAmountOptionType.dueAmount,
        onSelected: (option) {
          context.get<CappRepaymentTrackingService>().trackPromiseToPayPlanClickDueAmount();
          if (option != state.selectedAmountOption) {
            context
                .read<RepaymentPtpPaymentPlanBloc>()
                .add(RepaymentPtpPaymentPlanEvent.selectAmountOption(selectAmountOptionType: option));
          }
        },
      ),
      const SizedBox(height: 8),
      RepaymentPtpAmountOption(
        key: const Key('__paymentPlanCustomAmountOptions__'),
        subLabel: L10nCappRepayment.of(context).repaymentMainCustomAmountDescription,
        option: RepaymentPtpAmountOptionType.customAmount,
        selectedOption: state.selectedAmountOption,
        isSelected: state.selectedAmountOption == RepaymentPtpAmountOptionType.customAmount,
        onSelected: (option) {
          context.get<CappRepaymentTrackingService>().trackPromiseToPayPlanClickCustomAmount();
          if (option != state.selectedAmountOption) {
            context
                .read<RepaymentPtpPaymentPlanBloc>()
                .add(RepaymentPtpPaymentPlanEvent.selectAmountOption(selectAmountOptionType: option));
          }
        },
      ),
      Visibility(
        visible: state.selectedAmountOption == RepaymentPtpAmountOptionType.customAmount,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 4),
            RepaymentInputAmount(
              key: const Key('__paymentPlanCustomAmountInputText__'),
              inputKey: const Key('__paymentPlanSelectAmountCustomAmountInputText__'),
              currency: CurrencyFormatter().baseCurrency().code,
              focusNode: _focusNode,
              maxValue: dueAmount?.toDouble().toInt(),
              label: L10nCappRepayment.of(context).repaymentMainCustomAmountHeader,
              onChanged: (amount, ctx) {
                if (amount.isNotEmpty) {
                  context.get<CappRepaymentTrackingService>().trackPromiseToPayPlanEnterCustomAmount();
                  _bloc.add(RepaymentPtpPaymentPlanEvent.setAmount(Decimal.parse(amount)));
                } else {
                  _bloc.add(const RepaymentPtpPaymentPlanEvent.setAmount(null));
                }
              },
            ),
            const SizedBox(height: 4),
          ],
        ),
      ),
      const SizedBox(height: 16),
    ];

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: amountOptions,
    );
  }

  void _showExtendWarningPopup() {
    context.get<CappRepaymentTrackingService>().trackPromiseToPayPlanViewPopUpConfirmation();
    showKoyalOverlay<void>(
      context,
      key: const Key('__paymentPlanExtendPopup__'),
      dismissible: false,
      title: L10nCappRepayment.of(context).repaymentWantToExtendPaymentDate,
      body: KoyalText.body2(
        L10nCappRepayment.of(context).repaymentYouNeedToCommitPaymentAsSelectedDate,
        color: ColorTheme.of(context).secondaryTextColor,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (context) => PrimaryButton(
        key: const Key('__paymentPlanExtendPopupAgreeButton__'),
        text: L10nCappRepayment.of(context).repaymentIAgree,
        onPressed: () {
          context.get<CappRepaymentTrackingService>().trackPromiseToPayPlanClickPopUpConfirmationAgree();
          context.navigator.pop();
          FocusScope.of(context).unfocus();

          final ptpContract = _bloc.state.ptpContract;
          final promiseAmount = _bloc.state.selectedAmount;
          final promiseDate = _bloc.state.extensionDate?.toLocal().toIso8601String();
          if (ptpContract != null) {
            final request = RepaymentPtpEvaluationRequest(
              entityId: ptpContract.entityId,
              entityType: ptpContract.entityType,
              promiseAmount: promiseAmount,
              promiseDate: promiseDate,
            );
            _bloc.add(RepaymentPtpPaymentPlanEvent.submitPtpEvaluation(request: request));
          }
        },
      ),
      tertiaryButtonBuilder: (context) => TertiaryButton(
        text: L10nCappRepayment.of(context).repaymentCancel,
        onPressed: () {
          context.get<CappRepaymentTrackingService>().trackPromiseToPayPlanClickPopUpConfirmationCancel();
          context.navigator.pop();
        },
      ),
    );
  }

  void _navigateToProcessScreen(RepaymentPtpContract? ptpContract, RepaymentPtpEvaluation submitPtpResult) {
    context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentPtpProcessingScreen,
      arguments: RepaymentPtpProcessingRouteArgs(ptpContract: ptpContract, submitPtpResult: submitPtpResult),
    );
  }

  Future<void> _showErrorPopup({required BuildContext context}) async {
    return showKoyalOverlay<void>(
      context,
      dismissible: false,
      title: L10nCappRepayment.of(context).repaymentPtpSubmitError,
      primaryButtonBuilder: (c) => PrimaryButton(
        text: L10nKoyalShared.of(context).okGotIt,
        onPressed: () => c.navigator.pop(),
      ),
    );
  }
}
