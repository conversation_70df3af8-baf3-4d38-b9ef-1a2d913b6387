import 'package:capp_repayment_core/l10n/i18n.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_repayment.dart';

class RepaymentPtpAmountOption extends StatelessWidget {
  final Decimal? amount;
  final String subLabel;
  final RepaymentPtpAmountOptionType option;
  final RepaymentPtpAmountOptionType? selectedOption;
  final Function(RepaymentPtpAmountOptionType lable) onSelected;
  final bool isSelected;
  const RepaymentPtpAmountOption({
    Key? key,
    this.amount,
    required this.subLabel,
    required this.option,
    required this.selectedOption,
    required this.onSelected,
    required this.isSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final label = amount?.formatCurrency() ??
        (option == RepaymentPtpAmountOptionType.customAmount
            ? L10nCappRepayment.of(context).repaymentMainCustomAmount
            : '');
    return KoyalPadding.normalAll(
      top: false,
      bottom: false,
      child: GestureDetector(
        onTap: () {
          onSelected.call(option);
        },
        child: AnimatedContainer(
          padding: const EdgeInsets.all(16),
          duration: const Duration(milliseconds: 100),
          decoration: isSelected
              ? BoxDecoration(
                  color: ColorTheme.of(context).backgroundColor,
                  border: Border.all(
                    color: ColorTheme.of(context).infoIndicatorColor,
                  ),
                  borderRadius: BorderRadius.circular(8),
                )
              : BoxDecoration(
                  color: ColorTheme.of(context).backgroundColor,
                  border: Border.all(
                    color: ColorTheme.of(context).foreground15Color,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    KoyalText.subtitle2(
                      label,
                      color: ColorTheme.of(context).foreground90Color,
                    ),
                    const SizedBox(height: 4),
                    KoyalText.body2(
                      subLabel,
                      color: ColorTheme.of(context).foreground60Color,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 6),
              KoyalRadio<String>(
                value: option.toString(),
                groupValue: selectedOption?.toString(),
                onChanged: (_) {
                  onSelected.call(option);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
