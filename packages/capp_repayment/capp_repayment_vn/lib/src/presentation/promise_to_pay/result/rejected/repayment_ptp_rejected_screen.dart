import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_repayment.dart';

class RepaymentPtpRejectedScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPtpRejectedRouteArgs arguments;

  const RepaymentPtpRejectedScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentPtpRejectedBloc>()
          ..add(
            RepaymentPtpRejectedEvent.initialize(
              ptpContract: arguments.ptpContract,
              ptpEvaluation: arguments.submitPtpResult ?? arguments.ptpContract?.lastPTP,
            ),
          ),
        child: this,
      );

  @override
  RepaymentPtpRejectedScreenState createState() => RepaymentPtpRejectedScreenState();
}

class RepaymentPtpRejectedScreenState extends State<RepaymentPtpRejectedScreen> {
  late RepaymentPtpRejectedBloc _bloc;

  @override
  void initState() {
    _bloc = BlocProvider.of<RepaymentPtpRejectedBloc>(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepaymentPtpRejectedBloc, RepaymentPtpRejectedState>(
      listener: (ctx, state) {},
      bloc: _bloc,
      builder: (context, state) {
        final dueDate = state.ptpContract?.dueDate;
        final totalAmount = state.ptpContract?.totalAmount;
        return KoyalWillPopScope(
          onWillPop: () async {
            return false;
          },
          child: KoyalScaffold(
            appBar: KoyalAppBar(
              leading: KoyalAppBarLeading.none,
              title: L10nCappRepayment.of(context).repaymentPtpRejectedTitle,
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MainHeading(
                          title: L10nCappRepayment.of(context).repaymentPtpRejectedMainTitle,
                          subtitle: L10nCappRepayment.of(context).repaymentPtpRejectedMainDes,
                          avatar: const AssetSvgImage(
                            'assets/icons/ic_calendar_fail.svg',
                            fit: BoxFit.fitWidth,
                            package: 'capp_repayment',
                            width: 72,
                            alignment: Alignment.topCenter,
                          ),
                        ),
                        ExtendedInfoListItem(
                          title: L10nCappRepayment.of(context).repaymentPtpRejectedDetailDueDate,
                          bodyText: dueDate?.toLocal().mediumDate() ?? '',
                          useBoldBodyText: true,
                        ),
                        const ListDivider(),
                        ExtendedInfoListItem(
                          title: L10nCappRepayment.of(context).repaymentPtpRejectedDetailAmount,
                          bodyText: totalAmount?.formatCurrency().replaceAll('', '\u200B') ?? '',
                          useBoldBodyText: true,
                        ),
                      ],
                    ),
                  ),
                ),
                VerticalButtonsLayout(
                  primaryButton: PrimaryButton(
                    text: L10nCappRepayment.of(context).repaymentPtpRejectedDoneButton,
                    onPressed: () {
                      context.navigator.toMainScreen();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
