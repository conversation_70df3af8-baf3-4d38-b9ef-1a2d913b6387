import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_repayment.dart';

class RepaymentPtpSuccessScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPtpSuccessRouteArgs arguments;

  const RepaymentPtpSuccessScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentPtpSuccessBloc>()
          ..add(
            RepaymentPtpSuccessEvent.initialize(
              ptpContract: arguments.ptpContract,
              ptpEvaluation: arguments.submitPtpResult ?? arguments.ptpContract?.lastPTP,
            ),
          ),
        child: this,
      );

  @override
  RepaymentPtpSuccessScreenState createState() => RepaymentPtpSuccessScreenState();
}

class RepaymentPtpSuccessScreenState extends State<RepaymentPtpSuccessScreen> {
  late RepaymentPtpSuccessBloc _bloc;

  @override
  void initState() {
    _bloc = BlocProvider.of<RepaymentPtpSuccessBloc>(context);

    context.get<CappRepaymentTrackingService>().trackPromiseToPaySuccessScreenView();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepaymentPtpSuccessBloc, RepaymentPtpSuccessState>(
      listener: (ctx, state) {},
      bloc: _bloc,
      builder: (context, state) {
        final promiseDate = state.ptpEvaluation?.promiseDate;
        final promiseAmount = state.ptpEvaluation?.promiseAmount;
        return KoyalWillPopScope(
          onWillPop: () async {
            return false;
          },
          child: KoyalScaffold(
            appBar: KoyalAppBar(
              leading: KoyalAppBarLeading.none,
              title: L10nCappRepayment.of(context).repaymentPtpSuccessTitle,
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MainHeading(
                          title: L10nCappRepayment.of(context).repaymentPtpSuccessMainTitle,
                          subtitle: L10nCappRepayment.of(context).repaymentPtpSuccessMainDes,
                          avatar: const AssetSvgImage(
                            'assets/icons/ic_calendar_check.svg',
                            fit: BoxFit.fitWidth,
                            package: 'capp_repayment',
                            width: 72,
                            alignment: Alignment.topCenter,
                          ),
                        ),
                        ExtendedInfoListItem(
                          title: L10nCappRepayment.of(context).repaymentPtpSuccessDetailDueDate,
                          bodyText: promiseDate?.toLocal().mediumDate() ?? '',
                          useBoldBodyText: true,
                        ),
                        const ListDivider(),
                        ExtendedInfoListItem(
                          title: L10nCappRepayment.of(context).repaymentPtpSuccessDetailAmount,
                          bodyText: promiseAmount?.formatCurrency().replaceAll('', '\u200B') ?? '',
                          useBoldBodyText: true,
                        ),
                      ],
                    ),
                  ),
                ),
                VerticalButtonsLayout(
                  primaryButton: PrimaryButton(
                    text: L10nCappRepayment.of(context).repaymentPtpSuccessDoneButton,
                    onPressed: () {
                      context.get<CappRepaymentTrackingService>().trackPromiseToPaySuccessClickDone();
                      context.navigator.toMainScreen();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
