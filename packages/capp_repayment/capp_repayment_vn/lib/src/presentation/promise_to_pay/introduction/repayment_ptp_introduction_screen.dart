import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_repayment.dart';

class RepaymentPtpIntroductionScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPtpIntroductionRouteArgs arguments;
  const RepaymentPtpIntroductionScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentPtpIntroductionBloc>()
          ..add(
            RepaymentPtpIntroductionEvent.initialize(
              isDialog: arguments.isDialog,
              ptpContract: arguments.ptpContract,
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentPtpIntroductionScreen> createState() => _RepaymentPtpIntroductionScreenState();
}

class _RepaymentPtpIntroductionScreenState extends State<RepaymentPtpIntroductionScreen> {
  late RepaymentPtpIntroductionBloc _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of<RepaymentPtpIntroductionBloc>(context);

    context.get<CappRepaymentTrackingService>().trackPromiseToPayIntroScreenView();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RepaymentPtpIntroductionBloc, RepaymentPtpIntroductionState>(
      builder: (ctx, state) {
        return KoyalWillPopScope(
          onWillPop: () async {
            if (!state.isDialog) {
              _bloc.add(
                const RepaymentPtpIntroductionEvent.savePromiseToPayIsShown(),
              );
            }
            return true;
          },
          child: KoyalScaffold(
            appBar: KoyalAppBar(
              title: L10nCappRepayment.of(context).introduction,
              leading: state.isDialog ? KoyalAppBarLeading.close : KoyalAppBarLeading.goBack,
              onClose: () => context.navigator.pop(),
              onGoBack: () {
                if (!state.isDialog) {
                  _bloc.add(
                    const RepaymentPtpIntroductionEvent.savePromiseToPayIsShown(),
                  );
                }
                context.navigator.pop();
              },
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MainHeading(
                          title: L10nCappRepayment.of(context).whatIsPromiseToPay,
                          subtitle: L10nCappRepayment.of(context).promiseToPayDescription,
                          avatar: 'assets/icons/ic_calendar.png'.asImage(
                            fit: BoxFit.contain,
                            imagePackage: 'capp_repayment',
                          ),
                        ),
                        const SectionDivider(),
                        const SizedBox(
                          height: 16,
                        ),
                        KoyalPadding.normalHorizontal(
                          child: KoyalText.subtitle1(
                            L10nCappRepayment.of(context).detail,
                            textAlign: TextAlign.left,
                          ),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        IconedInfoListItem(
                          title: L10nCappRepayment.of(context).promiseToPayDetailTitle1,
                          bodyText: L10nCappRepayment.of(context).promiseToPayDetailMessage1,
                          icon: 'assets/icons/ic_cash_loan.png'.asImage(
                            imagePackage: 'capp_repayment',
                          ),
                        ),
                        IconedInfoListItem(
                          title: L10nCappRepayment.of(context).promiseToPayDetailTitle2,
                          bodyText: L10nCappRepayment.of(context).promiseToPayDetailMessage2,
                          icon: 'assets/icons/ic_document_approved.png'.asImage(
                            imagePackage: 'capp_repayment',
                          ),
                        ),
                        IconedInfoListItem(
                          title: L10nCappRepayment.of(context).promiseToPayDetailTitle3,
                          bodyText: L10nCappRepayment.of(context).promiseToPayDetailMessage3,
                          icon: 'assets/icons/ic_calendar.png'.asImage(
                            imagePackage: 'capp_repayment',
                          ),
                        ),
                        IconedInfoListItem(
                          title: L10nCappRepayment.of(context).promiseToPayDetailTitle4,
                          bodyText: L10nCappRepayment.of(context).promiseToPayDetailMessage4,
                          icon: 'assets/icons/ic_clock.png'.asImage(
                            imagePackage: 'capp_repayment',
                          ),
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                      ],
                    ),
                  ),
                ),
                VerticalButtonsLayout(
                  primaryButton: PrimaryButton(
                    text: state.isDialog
                        ? L10nCappRepayment.of(context).gotIt
                        : L10nCappRepayment.of(context).continueWord,
                    onPressed: () async {
                      if (!state.isDialog) {
                        context.get<CappRepaymentTrackingService>().trackPromiseToPayIntroductionClickContinue();
                        final ptpContract = state.ptpContract;
                        _bloc.add(
                          const RepaymentPtpIntroductionEvent.savePromiseToPayIsShown(),
                        );
                        // Navigate to payment plan screen
                        if (ptpContract != null) {
                          await context.navigator.pushTyped(
                            package: CappRepayment,
                            screen: RepaymentPtpOptionsScreen,
                            arguments: RepaymentPtpOptionsRouteArgs(
                              ptpContract: ptpContract,
                            ),
                          );
                        }
                      } else {
                        context.get<CappRepaymentTrackingService>().trackPromiseToPayIntroductionClickUnderstand();
                        context.navigator.pop();
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
