import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

class RepaymentTransactionSuccess extends StatelessWidget {
  final String messageTitle;
  final String messageBody;
  final Widget transactionSummarySection;
  final Widget subTransactionDetailSection;
  final Widget? loanOffersSection;
  final bool? isShowRepaymentBanner;
  final String? contractNumber;
  final Function()? onTapDone;
  final Function()? onTapExploreAda;
  final bool isLoadingCompleted;

  const RepaymentTransactionSuccess({
    Key? key,
    required this.messageTitle,
    required this.messageBody,
    required this.transactionSummarySection,
    required this.subTransactionDetailSection,
    this.onTapDone,
    this.onTapExploreAda,
    this.isLoadingCompleted = true,
    this.isShowRepaymentBanner,
    this.contractNumber,
    this.loanOffersSection,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__transactionSuccessBaseScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__transactionSuccessBaseAppBar__'),
        title: L10nCappRepayment.of(context).transactionDetails,
        leading: KoyalAppBarLeading.none,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Header section
                  MainHeading(
                    title: messageTitle,
                    subtitle: messageBody,
                    avatar: 'assets/icons/ic_approve.png'.asImage(
                      imagePackage: 'capp_repayment_core',
                    ),
                  ),
                  const SectionDivider(),
                  // Summary section
                  transactionSummarySection,
                  const SectionDivider(),
                  // Transaction detail section
                  subTransactionDetailSection,
                  _loansOfferSection(),
                  if (isShowRepaymentBanner ?? false)
                    RepaymentBanner(
                      bannerMode: BannerMode.multiple,
                      bannerType: api_models.BannerCtaType.loanRepayment,
                      contractNumber: contractNumber ?? '',
                    ),
                ],
              ),
            ),
          ),
          _vnBottomButtonsWidget(context),
        ],
      ),
    );
  }

  Widget _loansOfferSection() {
    return (loanOffersSection != null) ? loanOffersSection! : const SizedBox.shrink();
  }

  Widget _vnBottomButtonsWidget(BuildContext context) {
    if (!isLoadingCompleted) {
      return const SizedBox.shrink();
    }
    if (onTapExploreAda == null) {
      return VerticalButtonsLayout(
        secondaryButton: SecondaryButton(
          key: const Key('__doneButton__'),
          text: L10nCappRepayment.of(context).transactionDetailsDoneBtn,
          onPressed: onTapDone,
        ),
      );
    } else {
      return VerticalButtonsLayout(
        primaryButton: PrimaryButton(
          key: const Key('__doneButton__'),
          text: L10nCappRepayment.of(context).transactionDetailsDoneBtn,
          onPressed: onTapDone,
        ),
        secondaryButton: SecondaryButton(
          key: const Key('__exploreAdaButton__'),
          text: L10nCappRepayment.of(context).repaymentAdaExploreAutoDebit,
          onPressed: onTapExploreAda,
        ),
      );
    }
  }
}
