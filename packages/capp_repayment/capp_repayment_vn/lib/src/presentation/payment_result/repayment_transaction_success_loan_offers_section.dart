import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_loan_origination_unified_core/capp_loan_origination_unified_core.dart';
import 'package:capp_loan_shared_core/capp_loan_shared_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

class RepaymentTransactionSuccessLoanOffersSection extends StatelessWidget {
  final String title;
  final String contractCode;
  const RepaymentTransactionSuccessLoanOffersSection({Key? key, required this.contractCode, required this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) => context.isRegistered<OfferBannerBloc>()
      ? FeatureFlagWidget(
          featureName: FeatureFlag.displayOfferBannerAtRepaymentSuccessfulScreen,
          child: OfferBannerWrapper(
            key: const Key('__RepaymentSuccessLoanOffersSection_'),
            bannerTrackingType: BannerTrackingType.repaymentTransSuccess,
            triggerId: OfferBannerTriggerId.repayment,
            title: title,
            contractCode: contractCode,
            showIncomeBanner: true,
            bottomWidgets: const [],
            loadingView: Column(
              children: [
                const SectionHeading(
                  isLoading: true,
                  title: '',
                ),
                OfferCard(
                  arguments: OfferBannerArguments(
                    amount: '',
                    description: '',
                    buttonText: '',
                    onButtonPressed: () {},
                    isLoading: true,
                    title: '',
                    imageUrl: '',
                  ),
                ),
              ],
            ),
            onLoadingFinished: (_) {},
            titleWidget: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SectionDivider(),
                KoyalPadding.normalAll(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      KoyalText.subtitle1(
                        title,
                        color: ColorTheme.of(context).defaultTextColor,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
      : const SizedBox.shrink();
}
