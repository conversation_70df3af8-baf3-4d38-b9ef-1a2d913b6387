import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment.dart';

class RepaymentPaymentResultScreen extends StatefulWidget with RouteWrapper {
  final ScreenArguments arguments;

  const RepaymentPaymentResultScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) {
    final isEnableMomoV2 = context.isFlagEnabledRead(FeatureFlag.repaymentMomoApiV2);
    return BlocProvider(
      create: (_) => context.get<RepaymentPaymentResultBloc>()
        ..add(
          RepaymentPaymentResultEvent.initialize(
            deeplink: arguments.map!['deeplink']?.toString() ?? '',
            previousRouteName: arguments.map!['previousRouteName']?.toString() ?? '',
            isEnableMomoV2: isEnableMomoV2,
            repaymentLocalization: L10nCappRepayment.of(context),
          ),
        ),
      child: this,
    );
  }

  @override
  RepaymentPaymentResultScreenState createState() => RepaymentPaymentResultScreenState();
}

class RepaymentPaymentResultScreenState extends State<RepaymentPaymentResultScreen> {
  late RepaymentPaymentResultBloc _bloc;

  @override
  void initState() {
    _bloc = BlocProvider.of<RepaymentPaymentResultBloc>(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepaymentPaymentResultBloc, RepaymentPaymentResultState>(
      listener: (ctx, state) {
        state.failureOrSuccess.fold(() => null, (either) {
          either.fold((l) {
            // Handle ShopeePay error code
            if (state.paymentMethod?.gmaId == RepaymentPaymentMethod.shopee.getId() && l.isNotEmpty) {
              var errorString = '';
              var errorInfo = '';
              if (l == ShopeePayResultCode.cancel.code) {
                errorString = L10nCappRepayment.of(context).shopeepayErrorMessageCode201;
                errorInfo = ShopeePayResultCode.cancel.name;
              } else if (l == ShopeePayResultCode.fail.code) {
                errorString = L10nCappRepayment.of(context).shopeepayErrorMessageCode202;
                errorInfo = ShopeePayResultCode.fail.name;
              } else if (l == ShopeePayResultCode.expired.code) {
                errorString = L10nCappRepayment.of(context).shopeepayErrorMessageCode204;
                errorInfo = ShopeePayResultCode.expired.name;
              }
              _navigateToTransactionFailedScreen(
                message: errorString,
                rootRouteName: state.previousRouteName,
                errorType: TrackingUtils.getErrorType(
                  errorInfo: errorInfo,
                  paymentMethod: RepaymentPaymentMethod.shopee.getUserPaymentMethod(
                    userPaymentMethods: state.paymentMethods,
                    l10nCappRepayment: L10nCappRepayment.of(context),
                  ),
                ),
              );
            } else if (state.paymentMethod?.gmaId == RepaymentPaymentMethod.zalo.getId() && l.isNotEmpty) {
              var errorString = '';
              var errorInfo = '';
              if (l == ZaloPayResultCode.invalidResponse.code) {
                errorString = L10nCappRepayment.of(context).zalopayErrorMessageCodeInvalidResponse;
                errorInfo = ZaloPayResultCode.invalidResponse.name;
              } else if (l == ZaloPayResultCode.invalidOrder.code) {
                errorString = L10nCappRepayment.of(context).zalopayErrorMessageCodeInvalidOrder;
                errorInfo = ZaloPayResultCode.invalidOrder.name;
              } else if (l == ZaloPayResultCode.cancel.code || l == ZaloPayResultCode.cancelIos.code) {
                errorString = L10nCappRepayment.of(context).zalopayErrorMessageCodeCancel;
                errorInfo = l == ZaloPayResultCode.cancel.code
                    ? ZaloPayResultCode.cancel.name
                    : ZaloPayResultCode.cancelIos.name;
              } else if (l == ZaloPayResultCode.fail.code) {
                errorString = L10nCappRepayment.of(context).zalopayErrorMessageCodeFail;
                errorInfo = ZaloPayResultCode.fail.name;
              }
              _navigateToTransactionFailedScreen(
                message: errorString,
                rootRouteName: state.previousRouteName,
                errorType: TrackingUtils.getErrorType(
                  errorInfo: errorInfo,
                  paymentMethod: RepaymentPaymentMethod.zalo.getUserPaymentMethod(
                    userPaymentMethods: state.paymentMethods,
                    l10nCappRepayment: L10nCappRepayment.of(context),
                  ),
                ),
              );
            } else {
              _navigateToTransactionFailedScreen(
                rootRouteName: state.previousRouteName,
                errorType: TrackingUtils.getErrorType(
                  paymentMethod: state.paymentMethod,
                  errorInfo: RepaymentErrorType.unexpected.name,
                ),
              );
            }
          }, (r) {
            final contractNo = r.contractNo;
            final paymentOptionId = r.paymentOptionId;
            final paymentMethod = state.paymentMethod;
            final transactionId = state.transactionId ?? '';
            // Handle case cache not found for VnPay
            if (contractNo.isEmpty && paymentOptionId == RepaymentPaymentMethod.vnpay.getId()) {
              showToast(context, L10nCappRepayment.of(context).exceptionOccurred);
              context.get<GlobalTrackingProperties>().repaymentAbTest = null;
              context.navigator.toMainScreen();
            } else if (paymentMethod != null && transactionId.isNotEmpty) {
              final isPaymentWithPromotion = (state.voucherCode ?? '').isNotEmpty;
              // Do not need to call api get transaction detail in case payment without promo
              if (!isPaymentWithPromotion) {
                _navigateToTransactionSuccessScreen(
                  args: RepaymentTransactionSuccessRouteArgs(
                    isEWallet: r.isEWallet,
                    contractNo: r.contractNo,
                    finalAmount: r.finalAmount,
                    customerName: r.customerName,
                    phoneNumber: r.phoneNumber,
                    paymentOption: r.paymentOption,
                    paymentOptionId: paymentMethod.gmaId,
                    dateProcessed: r.dateProcessed.isNotEmpty
                        ? r.dateProcessed
                        : DateFormat(RepaymentFormatConfig.storageDateTimeFormat).format(DateTime.now()),
                    transactionNo: r.transactionNo,
                  ),
                );
              } else {
                // Get transaction detail in case get promotion info
                VnNavigationUtils.navigateToCheckResultScreen(
                  context: ctx,
                  paymentMethod: paymentMethod,
                  uuid: transactionId,
                  rootRouteName: state.previousRouteName,
                  phoneNumber: state.phoneNumber,
                );
              }
            } else {
              _navigateToTransactionFailedScreen(
                rootRouteName: state.previousRouteName,
                errorType: TrackingUtils.getErrorType(
                  paymentMethod: paymentMethod,
                  errorInfo: RepaymentErrorType.unexpected.name,
                ),
              );
            }
          });
        });

        state.failureOrSuccessTransactionResult.fold(() {}, (resp) {
          resp.fold((l) {
            _navigateToTransactionFailedScreen(
              rootRouteName: state.previousRouteName,
              errorType: TrackingUtils.getErrorType(
                paymentMethod: state.paymentMethod,
                errorInfo: RepaymentErrorType.unexpected.name,
              ),
            );
          }, (r) {
            if (r.paymentStatus == RepaymentTransactionStatus.fail.status) {
              _navigateToTransactionFailedScreen(
                rootRouteName: state.previousRouteName,
                errorType: TrackingUtils.getErrorType(
                  paymentMethod: state.paymentMethod,
                  errorInfo: RepaymentTransactionStatus.fail.name,
                ),
              );
              return;
            } else if (r.paymentStatus == RepaymentTransactionStatus.success.status) {
              _navigateToTransactionSuccessScreen(
                args: RepaymentTransactionSuccessRouteArgs(
                  isEWallet: false,
                  contractNo: r.contractNumber ?? '',
                  customerName: '',
                  phoneNumber: state.phoneNumber,
                  finalAmount: r.finalAmount!.toDouble(),
                  discountAmount: r.discountAmount?.toDouble(),
                  originalAmount: r.originalAmount?.toDouble(),
                  dateProcessed: DateFormat(RepaymentFormatConfig.storageDateTimeFormat).format(DateTime.now()),
                  paymentOption: state.paymentMethod?.title ?? '',
                  paymentOptionId: state.paymentMethod?.gmaId ?? '',
                  transactionNo: r.pcsTransactionId ?? '',
                ),
              );
            } else if (r.paymentStatus == RepaymentTransactionStatus.inprogress.status &&
                (state.paymentMethod?.gmaId == RepaymentPaymentMethod.shopee.getId() ||
                    state.paymentMethod?.gmaId == RepaymentPaymentMethod.momo.getId()) &&
                (state.transactionId ?? '').isNotEmpty &&
                state.paymentMethod != null) {
              VnNavigationUtils.navigateToCheckResultScreen(
                context: ctx,
                paymentMethod: state.paymentMethod!,
                uuid: state.transactionId!,
                rootRouteName: state.previousRouteName,
                phoneNumber: state.phoneNumber,
              );
            }
          });
        });
      },
      bloc: _bloc,
      builder: (context, state) {
        return KoyalScaffold(
          body: Container(
            color: Colors.transparent,
            width: double.infinity,
            height: double.infinity,
            child: Center(
              child: state.loadingState == LoadingState.isLoading ? const KoyalProgressIndicator.large() : Container(),
            ),
          ),
        );
      },
    );
  }

  void _navigateToTransactionFailedScreen({String? message, String? rootRouteName, String? errorType}) {
    context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentTransactionFailedScreen,
      arguments: RepaymentTransactionFailedRouteArgs(
        errorMessage: message ?? '',
        rootRouteName: rootRouteName,
        isShowFeedback: true,
        errorType: errorType,
      ),
    );
  }

  void _navigateToTransactionSuccessScreen({required RepaymentTransactionSuccessRouteArgs args}) {
    //Set transaction number custom dimension value to be send in Transaction Success Screen event tracking
    context.get<GlobalTrackingProperties>().transactionNumber = args.transactionNo;
    context.navigator.pushTyped(package: CappRepayment, screen: RepaymentTransactionSuccessScreen, arguments: args);
  }
}
