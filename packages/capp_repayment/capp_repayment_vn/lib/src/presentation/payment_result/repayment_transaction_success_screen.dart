import 'package:capp_bigdata/capp_bigdata.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment.dart';

class RepaymentTransactionSuccessScreen extends StatefulWidget with RouteWrapper {
  final RepaymentTransactionSuccessRouteArgs arguments;

  const RepaymentTransactionSuccessScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: context.get<RepaymentTransactionSuccessBloc>()
          ..add(
            RepaymentTransactionSuccessEvent.initialize(
              countryFlavor: CountryFlavor.vn,
              contractNumber: arguments.contractNo,
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentTransactionSuccessScreen> createState() => _RepaymentTransactionSuccessScreenState();
}

class _RepaymentTransactionSuccessScreenState extends State<RepaymentTransactionSuccessScreen> {
  @override
  void initState() {
    super.initState();
    if (context.isFlagEnabledRead(FeatureFlag.repaymentSatisfactionFeedback)) {
      Future.delayed(const Duration(seconds: 1), () async {
        if (mounted) {
          await showFeedbackOverlay(context, journeyId: CoreConstants.vnSuccessFailedFeedbackJourneyId);
        }
      });
    }
    context.get<CappRepaymentTrackingService>().trackOwnTransactionSuccessViewScreen();

    if (context.isFlagEnabledRead(FeatureFlag.repaymentCollectData)) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        GetIt.I.get<BigDataCollectionBloc>().add(
              BigDataCollectionEvent.process(
                displayData: DeviceUtils.getDisplayData(context),
                flow: BigDataFlow.repayment.getEventCode(),
              ),
            );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final originalAmount = widget.arguments.originalAmount ?? widget.arguments.finalAmount;
    final discountAmount = widget.arguments.discountAmount ?? 0;
    return KoyalWillPopScope(
      onWillPop: () async => false,
      child: BlocBuilder<RepaymentTransactionSuccessBloc, RepaymentTransactionSuccessState>(
        builder: (context, state) {
          final dateProcessed = widget.arguments.dateProcessed;

          return RepaymentTransactionSuccess(
            isShowRepaymentBanner: context.get<RepaymentTransactionSuccessBloc>().isShowTransactionSuccessBanner(),
            messageBody: L10nCappRepayment.of(context).repaymentPleaseWaitFewMinutes,
            messageTitle: L10nCappRepayment.of(context).repaymentSuccessful,
            subTransactionDetailSection: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                KoyalPadding.normalAll(
                  child: KoyalText.subtitle1(
                    L10nCappRepayment.of(context).repaymentTransactionInformation,
                    color: ColorTheme.of(context).defaultTextColor,
                  ),
                ),
                ExtendedInfoListItem(
                  title: L10nCappRepayment.of(context).repaymentContractNumber,
                  bodyText: widget.arguments.contractNo,
                ),
                const ListDivider(),
                if (widget.arguments.phoneNumber.isNotEmpty) ...[
                  ExtendedInfoListItem(
                    title: L10nCappRepayment.of(context).repaymentPhoneNumber,
                    bodyText: widget.arguments.phoneNumber,
                  ),
                  const ListDivider(),
                ],
                ExtendedInfoListItem(
                  title: L10nCappRepayment.of(context).repaymentProcessingDate,
                  bodyText: DateTime.parse(dateProcessed).toLocal().mediumDate(),
                ),
              ],
            ),
            transactionSummarySection: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                KoyalPadding.normalAll(
                  child: KoyalText.subtitle1(
                    L10nCappRepayment.of(context).repaymentLoanRepayment,
                    color: ColorTheme.of(context).defaultTextColor,
                  ),
                ),
                ExtendedInfoListItem(
                  title: L10nCappRepayment.of(context).repaymentAmount,
                  bodyText: Decimal.tryParse(originalAmount.toString())!.formatCurrency(),
                ),
                const ListDivider(),
                ExtendedInfoListItem(
                  title: L10nCappRepayment.of(context).repaymentPromotion,
                  bodyText: Decimal.tryParse(discountAmount.toString())!.formatCurrency(),
                ),
                const ListDivider(),
                ExtendedInfoListItem(
                  useBoldBodyText: true,
                  title: L10nCappRepayment.of(context).repaymentTotal,
                  bodyText: Decimal.tryParse(widget.arguments.finalAmount.toString())!.formatCurrency(),
                ),
              ],
            ),
            loanOffersSection: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RepaymentTransactionSuccessLoanOffersSection(
                  contractCode: widget.arguments.contractNo,
                  title: L10nCappRepayment.of(context).repaymentSuccessLoanOffersSectionTitle,
                ),
              ],
            ),
            onTapDone: () {
              context.get<CappRepaymentTrackingService>().trackOwnTransactionSuccessClickDone();
              if (state.isAdaEligible) {
                _showAdaReminderDialog(context: context);
              } else {
                _backToMainScreen();
              }
            },
            isLoadingCompleted: state.loadingState == LoadingState.isCompleted,
            onTapExploreAda: state.isAdaEligible
                ? () {
                    context.get<CappRepaymentTrackingService>().trackOwnTransactionSuccessClickExploreAda();
                    _navigateToAdaFlow();
                  }
                : null,
          );
        },
      ),
    );
  }

  Future _backToMainScreen() async {
    //Clear transactionNumber to prevent sending this custom dimension in other tracking
    context.get<GlobalTrackingProperties>().transactionNumber = null;
    context.get<GlobalTrackingProperties>().repaymentAbTest = null;
    await context.navigator.toMainScreen();
  }

  Future _navigateToAdaFlow() async {
    await context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentAdaMainScreen,
      arguments: RepaymentAdaMainRouteArgs(
        contractNumber: widget.arguments.contractNo,
      ),
    );
  }

  Future _showAdaReminderDialog({
    required BuildContext context,
  }) async {
    context.get<CappRepaymentTrackingService>().trackOwnTransactionSuccessViewRemindAdaPopup();
    return showKoyalOverlay<void>(
      context,
      key: const Key('__adaReminderDialog__'),
      title: L10nCappRepayment.of(context).repaymentAdaExploreAutoDebitFeature,
      body: KoyalText.body2(
        L10nCappRepayment.of(context).repaymentAdaExploreAutoDebitDescription,
        color: ColorTheme.of(context).secondaryTextColor,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (c) => PrimaryButton(
        key: const Key('__adaReminderDialogExplore__'),
        text: L10nCappRepayment.of(context).repaymentAdaExplore,
        onPressed: () {
          context.get<CappRepaymentTrackingService>().trackOwnTransactionSuccessClickRemindAdaPopupExplore();
          context.navigator.pop();
          _navigateToAdaFlow();
        },
      ),
      tertiaryButtonBuilder: (c) => TertiaryButton(
        key: const Key('__adaReminderDialogLater__'),
        text: L10nCappRepayment.of(context).repaymentAdaLater,
        onPressed: () {
          context.get<CappRepaymentTrackingService>().trackOwnTransactionSuccessClickRemindAdaPopupLater();
          _backToMainScreen();
        },
      ),
    );
  }
}
