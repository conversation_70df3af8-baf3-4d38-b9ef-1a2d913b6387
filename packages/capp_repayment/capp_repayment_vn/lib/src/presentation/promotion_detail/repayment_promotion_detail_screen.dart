import 'dart:io';

import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment.dart';

class RepaymentPromotionDetailScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPromotionDetailRouteArgs arguments;
  const RepaymentPromotionDetailScreen({
    super.key,
    required this.arguments,
  });

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider.value(
        value: context.get<RepaymentPromotionDetailBloc>()
          ..add(
            RepaymentPromotionDetailEvent.initialize(
              selectedVoucher: arguments.selectedVoucher,
              isApplied: arguments.isApplied,
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentPromotionDetailScreen> createState() => _RepaymentPromotionDetailScreenState();
}

class _RepaymentPromotionDetailScreenState extends State<RepaymentPromotionDetailScreen> {
  late InAppWebViewSettings options;
  late Set<Factory<OneSequenceGestureRecognizer>>? _gestureRecognizers;

  @override
  void initState() {
    final isApplied = widget.arguments.isApplied ?? false;
    context.get<CappRepaymentTrackingService>().trackDirectDiscountDetailScreenView(isApplied: isApplied);
    options = InAppWebViewSettings(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      clearCache: true,
      cacheEnabled: false,
      useHybridComposition: Platform.isAndroid,
      allowsInlineMediaPlayback: Platform.isIOS,
    );
    _gestureRecognizers = <Factory<OneSequenceGestureRecognizer>>{}
      ..add(const Factory<VerticalDragGestureRecognizer>(VerticalDragGestureRecognizer.new));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalWillPopScope(
      onWillPop: () async {
        final isApplied = widget.arguments.isApplied ?? false;
        context.get<CappRepaymentTrackingService>().trackDirectDiscountDetailClickBack(isApplied: isApplied);

        return true;
      },
      child: KoyalScaffold(
        key: const Key('__promotionDetailScreen__'),
        appBar: KoyalAppBar(
          key: const Key('__promotionDetailAppBar__'),
          leading: KoyalAppBarLeading.close,
          onClose: () {
            final isApplied = widget.arguments.isApplied ?? false;
            context.get<CappRepaymentTrackingService>().trackDirectDiscountDetailClickBack(isApplied: isApplied);
            context.navigator.pop();
          },
          title: L10nCappRepayment.of(context).repaymentDetails,
        ),
        body: BlocBuilder<RepaymentPromotionDetailBloc, RepaymentPromotionDetailState>(
          builder: (context, state) {
            final selectedVoucher = state.selectedVoucher;
            final htmlDes = selectedVoucher?.description ?? '';
            var htmlData = htmlDes;

            // Cover case data is not html and fix for case font size small
            if (htmlDes.isNotEmpty && !htmlDes.contains('<html>')) {
              htmlData =
                  '<!DOCTYPE html><html><head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head><body>$htmlData</body></html>';
            }
            final minRepaymentAmount = selectedVoucher?.minRequiredAmount ?? Decimal.zero;
            final title = selectedVoucher?.name ?? '';
            final subTitle =
                '${L10nCappRepayment.of(context).repaymentMinRepayment} ${minRepaymentAmount.formatCurrency()}';
            final isApplied = state.isApplied;
            final isValid = selectedVoucher?.isValid ?? false;

            return Column(
              children: [
                MainHeading(
                  key: const Key('__promotionDetailHeader__'),
                  title: title,
                  subtitle: subTitle,
                  centerAlign: false,
                ),
                Expanded(
                  child: htmlData.isNotEmpty
                      ? KoyalPadding.smallHorizontal(
                          child: !Platform.isLinux
                              ? InAppWebView(
                                  key: const Key('__promotionDetailWebviewDesc__'),
                                  gestureRecognizers: _gestureRecognizers,
                                  initialData: InAppWebViewInitialData(data: htmlData),
                                  initialSettings: options,
                                  onWebViewCreated: (controller) {},
                                  onLoadStart: (controller, url) {},
                                  onPermissionRequest: (controller, permissionRequest) async {
                                    return PermissionResponse(
                                      action: PermissionResponseAction.GRANT,
                                      resources: permissionRequest.resources,
                                    );
                                  },
                                  onLoadStop: (controller, url) async {},
                                  onReceivedError: (controller, request, error) {},
                                  onProgressChanged: (controller, progress) {},
                                  onUpdateVisitedHistory: (controller, url, androidIsReload) {},
                                  onConsoleMessage: (controller, consoleMessage) {
                                    if (kDebugMode) {
                                      print(consoleMessage);
                                    }
                                  },
                                )
                              : const SizedBox(),
                        )
                      : const SizedBox(),
                ),
                if (isApplied)
                  KoyalPadding.normalAll(
                    child: Alert(
                      key: const Key('__promotionDetailAlert__'),
                      text: L10nCappRepayment.of(context).repaymentThisVoucherIsApplied,
                      theme: AlertTheme.info(),
                    ),
                  ),
                if (isValid)
                  KoyalPadding.xSmall(
                    top: false,
                    child: VerticalButtonsLayout(
                      primaryButton: isApplied
                          ? null
                          : PrimaryButton(
                              key: const Key('__promotionDetailUseNow__'),
                              onPressed: () {
                                context
                                    .get<CappRepaymentTrackingService>()
                                    .trackDirectDiscountDetailClickApplyVoucher();
                                context.navigator.pop(true);
                              },
                              text: L10nCappRepayment.of(context).repaymentUseNow,
                            ),
                      secondaryButton: isApplied
                          ? SecondaryButton(
                              key: const Key('__promotionDetailUseLater__'),
                              text: L10nCappRepayment.of(context).repaymentUseLater,
                              onPressed: () {
                                context
                                    .get<CappRepaymentTrackingService>()
                                    .trackDirectDiscountDetailClickApplyVoucherLater();
                                context.navigator.pop(false);
                              },
                            )
                          : null,
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
