import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../../../capp_repayment.dart';

class OnePayPortalWidget extends StatefulWidget {
  final String url;
  final String returnUrl;
  final Function(String code) onResult;

  const OnePayPortalWidget(
    this.url,
    this.returnUrl,
    this.onResult, {
    Key? key,
  }) : super(key: key);

  @override
  State createState() {
    return OnePayPortalState();
  }
}

class OnePayPortalState extends State<OnePayPortalWidget> {
  late InAppWebViewSettings options;
  late Set<Factory<OneSequenceGestureRecognizer>>? _gestureRecognizers;

  @override
  void initState() {
    options = InAppWebViewSettings(
      allowsInlineMediaPlayback: Platform.isIOS,
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      clearCache: true,
      cacheEnabled: false,
    );
    _gestureRecognizers = <Factory<OneSequenceGestureRecognizer>>{}
      ..add(const Factory<VerticalDragGestureRecognizer>(VerticalDragGestureRecognizer.new));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return !Platform.isLinux
        ? InAppWebView(
            gestureRecognizers: _gestureRecognizers,
            initialUrlRequest: URLRequest(url: WebUri.uri(Uri.parse(widget.url))),
            initialSettings: options,
            onWebViewCreated: (controller) {},
            onLoadStart: (controller, url) {},
            onPermissionRequest: (controller, permissionRequest) async {
              return PermissionResponse(resources: permissionRequest.resources, action: PermissionResponseAction.GRANT);
            },
            shouldOverrideUrlLoading: (controller, navigationAction) async {
              final uri = navigationAction.request.url!;
              final code = uri.queryParameters[Constants.onePayResponseCodeKey];
              if (uri.host.isNotEmpty && widget.returnUrl.contains(uri.host) && code != null) {
                widget.onResult(code);
                return NavigationActionPolicy.CANCEL;
              }
              return NavigationActionPolicy.ALLOW;
            },
            onLoadStop: (controller, url) async {},
            onReceivedError: (controller, request, error) {},
            onProgressChanged: (controller, progress) {},
            onUpdateVisitedHistory: (controller, url, androidIsReload) {},
            onConsoleMessage: (controller, consoleMessage) {
              if (kDebugMode) {
                print(consoleMessage);
              }
            },
          )
        : const SizedBox();
  }
}
