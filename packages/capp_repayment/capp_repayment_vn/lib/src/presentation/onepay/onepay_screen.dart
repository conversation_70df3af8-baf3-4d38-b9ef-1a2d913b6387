import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:dartz/dartz.dart' hide State;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class OnePayScreen extends StatefulWidget with RouteWrapper {
  final OnePayRouteArgs arguments;

  const OnePayScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  OnePayScreenWidgetState createState() => OnePayScreenWidgetState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<OnePayBloc>()
          ..add(
            OnePayEvent.initialize(
              arguments.contractNumber,
              arguments.transactionId,
              arguments.amount,
              arguments.customerName,
              arguments.paymentUrl,
            ),
          ),
        child: this,
      );
}

class OnePayScreenWidgetState extends State<OnePayScreen> {
  late String _returnUrl;

  @override
  void initState() {
    super.initState();
    final settings = BlocProvider.of<OnePayBloc>(context).settings;
    _returnUrl = settings.onePaySettings.returnUrl;

    context.get<CappRepaymentTrackingService>().trackOnePayScreenView();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      resizeToAvoidBottomInset: !GmaPlatform.isIOS,
      safeAreaCustomSettings: SafeAreaCustomSettings(
        maintainBottomViewPadding: GmaPlatform.isIOS,
      ),
      appBar: KoyalAppBar(
        title: L10nCappRepayment.of(context).repaymentContractRepayment,
        onGoBack: () {
          context.get<CappRepaymentTrackingService>().trackOwnAtmCardProcessingClickBack();
          context.navigator.maybePop();
        },
      ),
      body: BlocBuilder<OnePayBloc, OnePayState>(
        builder: (ctx, state) {
          final onePayPortalUrl = state.onePayPortalUrl;
          if (onePayPortalUrl?.isNotEmpty ?? false) {
            return OnePayPortalWidget(onePayPortalUrl!, _returnUrl, (code) {
              if (code == '0') {
                context.navigator.pop(
                  right<Tuple2<String, String>, Map>(<String, dynamic>{
                    'transactionId': state.transactionId,
                    'amount': state.amount,
                    'customerName': state.customerName,
                    'contractNumber': state.contractNumber,
                    'date': DateTime.now().toString(),
                    'provider': state.provider,
                  }),
                );
              } else {
                context.navigator.pop(
                  left<Tuple2<String, String>, Map>(Tuple2<String, String>(code, _getErrorMessage(context, code))),
                );
              }
            });
          } else {
            return Container();
          }
        },
      ),
    );
  }

  String _getErrorMessage(BuildContext context, String code) {
    switch (code) {
      case '1':
        return L10nCappRepayment.of(context).onepayMessageCode1;
      case '3':
        return L10nCappRepayment.of(context).onepayMessageCode3;
      case '4':
        return L10nCappRepayment.of(context).onepayMessageCode4;
      case '5':
        return L10nCappRepayment.of(context).onepayMessageCode5;
      case '6':
        return L10nCappRepayment.of(context).onepayMessageCode6;
      case '7':
        return L10nCappRepayment.of(context).onepayMessageCode7;
      case '8':
        return L10nCappRepayment.of(context).onepayMessageCode8;
      case '9':
        return L10nCappRepayment.of(context).onepayMessageCode9;
      case '10':
        return L10nCappRepayment.of(context).onepayMessageCode10;
      case '11':
        return L10nCappRepayment.of(context).onepayMessageCode11;
      case '12':
        return L10nCappRepayment.of(context).onepayMessageCode12;
      case '13':
        return L10nCappRepayment.of(context).onepayMessageCode13;
      case '21':
        return L10nCappRepayment.of(context).onepayMessageCode21;
      case '22':
        return L10nCappRepayment.of(context).onepayMessageCode22;
      case '23':
        return L10nCappRepayment.of(context).onepayMessageCode23;
      case '24':
        return L10nCappRepayment.of(context).onepayMessageCode24;
      case '25':
        return L10nCappRepayment.of(context).onepayMessageCode25;
      case '99':
        return L10nCappRepayment.of(context).onepayMessageCode99;
      case '253':
        return L10nCappRepayment.of(context).onepayMessageCode253;
    }
    return '';
  }
}
