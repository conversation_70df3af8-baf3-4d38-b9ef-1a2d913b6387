import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_repayment.dart';

class RepaymentCheckResultScreen extends StatefulWidget with RouteWrapper {
  final RepaymentCheckResultRouteArgs? arguments;
  const RepaymentCheckResultScreen({Key? key, this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentCheckResultBloc>()
          ..add(
            RepaymentCheckResultEvent.initialize(
              arguments!.paymentMethod,
              arguments!.uuid,
              arguments!.rootRouteName ?? '',
              arguments!.phoneNumber ?? '',
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentCheckResultScreen> createState() => _RepaymentCheckResultScreenState();
}

class _RepaymentCheckResultScreenState extends State<RepaymentCheckResultScreen> {
  late RepaymentCheckResultBloc _bloc;

  @override
  void initState() {
    _bloc = BlocProvider.of<RepaymentCheckResultBloc>(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalWillPopScope(
      onWillPop: () async => false,
      child: KoyalScaffold(
        appBar: KoyalAppBar(
          leading: KoyalAppBarLeading.none,
          title: L10nCappRepayment.of(context).transactionDetailsSection,
        ),
        backgroundColor: ColorTheme.of(context).backgroundColor,
        body: Stack(
          children: [
            Positioned(
              top: 24,
              left: 24,
              right: 24,
              child: BlocConsumer<RepaymentCheckResultBloc, RepaymentCheckResultState>(
                bloc: _bloc,
                listener: (ctx, state) {
                  final paymentMethod = state.paymentMethod;

                  state.failureOrSuccessTransactionResult.fold(() {}, (resp) {
                    resp.fold((l) {
                      showToast(context, L10nCappRepayment.of(context).exceptionOccurred);
                    }, (r) {
                      if (r.paymentStatus == RepaymentTransactionStatus.fail.status) {
                        _bloc.add(const RepaymentCheckResultEvent.endTimer());
                        _navigateToTransactionFailedScreen(
                          context: context,
                          rootRouteName: state.previousRouteName,
                          errorType: TrackingUtils.getErrorType(
                            paymentMethod: paymentMethod,
                            errorInfo: RepaymentTransactionStatus.fail.status,
                          ),
                        );
                      } else if (r.paymentStatus == RepaymentTransactionStatus.success.status) {
                        _bloc.add(const RepaymentCheckResultEvent.endTimer());
                        _navigateToTransactionSuccessScreen(
                          context: context,
                          args: RepaymentTransactionSuccessRouteArgs(
                            isEWallet: false,
                            contractNo: r.contractNumber ?? '',
                            customerName: '',
                            phoneNumber: state.phoneNumber ?? '',
                            finalAmount: r.finalAmount!.toDouble(),
                            discountAmount: r.discountAmount?.toDouble(),
                            originalAmount: r.originalAmount?.toDouble(),
                            dateProcessed:
                                DateFormat(RepaymentFormatConfig.storageDateTimeFormat).format(DateTime.now()),
                            paymentOption: state.paymentMethod?.title ?? '',
                            paymentOptionId: state.paymentMethod?.gmaId ?? '',
                            transactionNo: r.partnerTransactionId ?? '',
                          ),
                        );
                      } else if (r.paymentStatus == RepaymentTransactionStatus.inprogress.status &&
                          state.paymentMethod?.gmaId == RepaymentPaymentMethod.shopee.getId() &&
                          _bloc.isTimeout(state.checkResultTimeCounter!)) {
                        _bloc.add(
                          const RepaymentCheckResultEvent.updateTransactionStatus(RepaymentTransactionStatus.timeout),
                        );
                      }
                    });
                  });
                },
                builder: (context, state) {
                  return Column(
                    children: [
                      KoyalText.header5(
                        state.status == RepaymentTransactionStatus.inprogress
                            ? L10nCappRepayment.of(context).repaymentProcessing
                            : L10nCappRepayment.of(context).repaymentTimeout,
                        color: ColorTheme.of(context).defaultTextColor,
                      ),
                      const SizedBox(height: 8),
                      KoyalText.body2(
                        state.status == RepaymentTransactionStatus.inprogress
                            ? L10nCappRepayment.of(context).repaymentYouWillBeNoticedWhen
                            : L10nCappRepayment.of(context).repaymentPleaseTryAgain,
                        textAlign: TextAlign.center,
                        color: ColorTheme.of(context).secondaryTextColor,
                      ),
                    ],
                  );
                },
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: VerticalButtonsLayout(
                primaryButton: PrimaryButton(
                  text: L10nCappRepayment.of(context).transactionFailedTryAgain,
                  onPressed: () {
                    final rootRouteName = widget.arguments?.rootRouteName;
                    if (rootRouteName == null || rootRouteName == 'repayment_payment_summary_screen') {
                      context.navigator.popUntilFromPackage(
                        'CappRepayment',
                        context.isFlagEnabledRead(FeatureFlag.repaymentNew)
                            ? 'RepaymentMainScreen'
                            : 'RepaymentOnlinePaymentScreen',
                      );
                    } else {
                      context.get<GlobalTrackingProperties>().repaymentAbTest = null;
                      context.navigator.toMainScreen();
                    }
                  },
                ),
                secondaryButton: SecondaryButton(
                  text: L10nCappRepayment.of(context).backToHomepage,
                  onPressed: () {
                    context.get<GlobalTrackingProperties>().repaymentAbTest = null;
                    context.navigator.toMainScreen();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToTransactionFailedScreen({
    required BuildContext context,
    String? message,
    String? rootRouteName,
    String? errorType,
  }) {
    context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentTransactionFailedScreen,
      arguments: RepaymentTransactionFailedRouteArgs(
        errorMessage: message ?? '',
        rootRouteName: rootRouteName,
        isShowFeedback: true,
        errorType: errorType,
      ),
    );
  }

  void _navigateToTransactionSuccessScreen({
    required BuildContext context,
    required RepaymentTransactionSuccessRouteArgs args,
  }) {
    //Set transaction number custom dimension value to be send in Transaction Success Screen event tracking
    context.get<GlobalTrackingProperties>().transactionNumber = args.transactionNo;
    context.navigator.pushTyped(package: CappRepayment, screen: RepaymentTransactionSuccessScreen, arguments: args);
  }
}
