import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_repayment.dart';

class RepaymentBankingAppScreen extends StatefulWidget with RouteWrapper {
  final RepaymentBankingAppRouteArgs arguments;

  RepaymentBankingAppScreen({
    Key? key,
    required this.arguments,
  }) : super(key: key);

  @override
  State<RepaymentBankingAppScreen> createState() => _RepaymentBankingAppScreenState();

  @override
  Widget wrappedRoute(BuildContext context) {
    context.get<CappRepaymentTrackingService>().trackMobileBankingAppScreenView();

    return BlocProvider(
      create: (_) {
        return context.get<RepaymentBankingAppBloc>()
          ..add(
            RepaymentBankingAppEvent.initialize(
              banks: RepaymentBankingAppBloc.getBaoKiemBanks(),
              selectedBank: arguments.selectedBank,
            ),
          );
      },
      child: this,
    );
  }
}

class _RepaymentBankingAppScreenState extends State<RepaymentBankingAppScreen> {
  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__bankingAppScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__bankingAppBar__'),
        title: L10nCappRepayment.of(context).repaymentMobileBankingApps,
      ),
      backgroundColor: ColorTheme.of(context).backgroundColor,
      body: Column(
        children: [
          Expanded(
            child: BlocBuilder<RepaymentBankingAppBloc, RepaymentBankingAppState>(
              builder: (context, state) {
                final banks = state.banks;
                final selectedBank = state.selectedBank;

                return CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(
                      child: KoyalPadding.normalAll(
                        key: const Key('__bankingAppTitle__'),
                        child: KoyalText.body2(
                          L10nCappRepayment.of(context).repaymentPleaseChooseYourBankingApp,
                          color: ColorTheme.of(context).defaultTextColor,
                        ),
                      ),
                    ),
                    SliverPadding(
                      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                      sliver: SliverGrid(
                        key: const Key('__bankingAppGrid__'),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 8,
                          childAspectRatio: 1.73,
                          mainAxisSpacing: 8,
                        ),
                        delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final selectedBankShortName = selectedBank?.shortName;
                            final bankShortName = banks[index].shortName;
                            return RepaymentBankingAppListItem(
                              key: Key(
                                '__bankingAppListItem-$bankShortName-${selectedBankShortName == bankShortName ? 'selected' : ''}__',
                              ),
                              iconPath: 'assets/icons/${banks[index].iconName}',
                              isSelected: selectedBankShortName == bankShortName,
                              onTap: () {
                                context.navigator.pop(banks[index]);
                              },
                            );
                          },
                          childCount: banks.length,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
