import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentBankingAppListItem extends StatelessWidget {
  final String iconPath;
  final Function()? onTap;

  final bool isSelected;

  const RepaymentBankingAppListItem({
    Key? key,
    required this.iconPath,
    required this.isSelected,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(4)),
          border: Border.all(
            color: isSelected ? ColorTheme.of(context).infoIndicatorColor : ColorTheme.of(context).foreground5Color,
          ),
        ),
        child: Image.asset(
          iconPath,
          package: 'capp_repayment',
          fit: BoxFit.fitWidth,
        ),
      ),
    );
  }
}
