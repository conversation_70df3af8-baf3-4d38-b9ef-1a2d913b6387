// ignore_for_file: use_build_context_synchronously
import 'dart:async';
import 'dart:math';

import 'package:capp_ui_core/capp_ui.dart';
import 'package:dotted_decoration/dotted_decoration.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:intl/number_symbols.dart';
import 'package:intl/number_symbols_data.dart' show numberFormatSymbols;
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class RepaymentInputAmount extends StatefulWidget {
  final Key? inputKey;
  final String currency;
  final String helpText;
  final bool enabled;
  final bool isError;
  final int maxDigits;
  final String? initValue;
  final Stream<bool>? shouldScrollStream;
  final Function(String, BuildContext)? onChanged;
  final ValueChanged<FocusNode>? onFocusChanged;
  final Stream<String>? valueStream;
  final String? label;
  final FocusNode? focusNode;
  final int? maxValue;
  final int? decimalDigits;
  final TextEditingController? controller;
  final ValueChanged<String>? onEditingComplete;
  const RepaymentInputAmount({
    this.inputKey,
    required this.currency,
    this.helpText = '',
    this.isError = false,
    this.enabled = true,
    this.maxDigits = 9,
    this.onChanged,
    this.shouldScrollStream,
    this.onFocusChanged,
    this.initValue,
    this.valueStream,
    this.label,
    this.focusNode,
    this.maxValue,
    this.decimalDigits,
    this.controller,
    this.onEditingComplete,
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => RepaymentInputAmountState();
}

class RepaymentInputAmountState extends State<RepaymentInputAmount> {
  RepaymentTextInputFormatter get formatter => RepaymentTextInputFormatter(
        locale: CurrencyFormatter().currencyLocale(widget.currency),
        maxDigits: widget.maxDigits,
        decimalDigits: widget.decimalDigits ?? 0,
        maxValue: widget.maxValue,
        allowNegatives: false,
      );
  TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.controller != null) {
      _controller = widget.controller!;
      _controller.text = _formatUnfocus(_controller.text);
    } else {
      final initValue = formatter
          .formatEditUpdate(
            TextEditingValue.empty,
            TextEditingValue(text: widget.initValue ?? ''),
          )
          .text;
      _controller.text = _formatUnfocus(initValue);
    }
  }

  void _formatOnFocusChanged(FocusNode focusNode) {
    if (!mounted) {
      return;
    }

    if (!focusNode.hasFocus) {
      _controller.text = _formatUnfocus(_controller.text);
    }
    widget.onFocusChanged?.call(focusNode);
  }

  String _formatUnfocus(String text) {
    if (text == '') {
      return '';
    }
    final locale = CurrencyFormatter().currencyLocale(widget.currency);
    final decimal = widget.decimalDigits ?? 0;
    var newText = text;
    final format = NumberFormat.currency(
      locale: locale,
      decimalDigits: widget.decimalDigits ?? 0,
      symbol: '',
    );
    final decimalCharacter = (numberFormatSymbols as Map<String, NumberSymbols>)[locale]?.DECIMAL_SEP ?? '';
    if (text.contains(decimalCharacter)) {
      final decimalPos = text.indexOf(decimalCharacter);
      //   // split number by decimal point
      final leftSide = text.substring(0, decimalPos);
      var rightSide = text.substring(decimalPos + 1);
      rightSide = rightSide.length > decimal ? rightSide.substring(0, decimal) : rightSide.padRight(decimal, '0');
      newText = '$leftSide$decimalCharacter$rightSide';
    } else {
      newText = text.replaceAll(RegExp(r'[^\d]+'), '');
      newText = format.format(int.parse(newText)).trim();
    }
    return newText;
  }

  @override
  Widget build(BuildContext context) {
    return RepaymentInputTextComponent(
      prefix: widget.currency != 'vnd' ? currencyContainer : null,
      suffix: widget.currency == 'vnd' ? currencyContainer : null,
      alignSuffixToText: true,
      helpText: widget.helpText,
      inputKey: widget.inputKey,
      onFocusChanged: _formatOnFocusChanged,
      isError: widget.isError,
      shouldScrollStream: widget.shouldScrollStream,
      enabled: widget.enabled,
      label: widget.label,
      inputFormates: [formatter],
      focusNode: widget.focusNode,
      onChanged: (value) {
        context.read<InactivityBloc>().add(const InactivityEvent.activityOccured());
        final parsedValue = _formatUnfocus(value);
        widget.onChanged?.call(
          parsedValue.replaceAll(RegExp(r'[^\d]+'), ''),
          context,
        );
      },
      onEditingComplete: widget.onEditingComplete,
      valueStream: widget.valueStream,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      controller: _controller,
    );
  }

  Container get currencyContainer => Container(
        alignment: Alignment.bottomLeft,
        width: 20,
        child: KoyalText.body1(
          CurrencyFormatter().currencySymbol(widget.currency) ?? widget.currency,
          color: ColorTheme.of(context).secondaryTextColor,
        ),
      );
}

class RepaymentTextInputFormatter extends TextInputFormatter {
  RepaymentTextInputFormatter({
    this.symbol = '',
    required this.locale,
    this.decimalDigits = 0,
    this.maxDigits = 0,
    this.allowNegatives = true,
    this.maxValue,
  });

  final String symbol;
  final String locale;
  final int decimalDigits;
  final int maxDigits;
  final int? maxValue;

  final bool allowNegatives;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // isRemovedCharacter true if delete is press
    // final isRemovedCharacter = oldValue.text.length - 1 == newValue.text.length;

    // get input value and format
    var newText = newValue.text;
    final isNegative = newValue.text.startsWith('-') && allowNegatives;
    final format = NumberFormat.currency(
      locale: locale,
      decimalDigits: decimalDigits,
      symbol: symbol,
    );
    final formatNoDecimal = NumberFormat.currency(
      locale: locale,
      decimalDigits: 0,
      symbol: symbol,
    );
    final decimalCharacter = (numberFormatSymbols as Map<String, NumberSymbols>)[locale]?.DECIMAL_SEP ?? '';

    String newString;
    int offset;

    final selectionIndexFromTheRight = newValue.text.length - newValue.selection.end;

    final noNumberStr = newText.replaceAll(RegExp(r'[^\d]+'), '').trim();
    // // don't validate empty input
    if (noNumberStr == '') {
      return newValue.copyWith(
        text: isNegative ? '-' : '',
        selection: TextSelection.collapsed(offset: isNegative ? 1 : 0),
      );
    }

    // // check for decimal
    if (newText.contains(decimalCharacter)) {
      // type decimal character is not allow in decimalDigits = 0
      if (decimalDigits <= 0) {
        return oldValue;
      }

      //   // get position of first decimal
      //   // this prevents multiple decimals from
      //   // being entered
      final decimalPos = newText.indexOf(decimalCharacter);
      //   // split number by decimal point
      var leftSide = newText.substring(0, decimalPos);
      var rightSide = newText.substring(decimalPos + 1);

      //  check num leftside
      leftSide = leftSide.replaceAll(RegExp(r'[^\d]+'), '');
      if (maxDigits - decimalDigits > 0 && leftSide.length > maxDigits - decimalDigits) {
        leftSide = leftSide.substring(0, maxDigits - decimalDigits);
      }
      final leftSideNumber = double.tryParse(leftSide) ?? 0;

      // check maxDigits
      // if (maxDigits != 0 && (decimalDigits + leftSide.length) >= maxDigits) {
      // }

      // handle leftSide for thousand seperator
      // some locale may leave extra space at top
      leftSide = formatNoDecimal.format(leftSideNumber).trim();

      //   // validate right side
      // prevent multiple decimals being enter
      rightSide = rightSide.replaceAll(RegExp(r'[^\d]+'), '');

      // validate right side
      // handle more or less characters of decimal rightSide
      // more will get cut off (subString)
      if (rightSide.length > decimalDigits) {
        rightSide = rightSide.substring(0, decimalDigits);
      }

      // combine left and right
      newString = '$leftSide$decimalCharacter$rightSide';

      if (maxValue != null) {
        final maxNumber = maxValue! / pow(10, decimalDigits);
        final newNumber = double.parse(
          '${leftSideNumber.floor()}$decimalCharacter$rightSide',
        );
        if (newNumber > maxNumber) {
          newString = format.format(maxNumber);
        }
      }

      newString = (isNegative ? '-' : '') + newString.trim();
      offset = newString.length - selectionIndexFromTheRight;
    } else {
      // handle number without decimal point
      newText = newText.replaceAll(RegExp(r'[^\d]+'), '');

      // handle maxDigits
      if (maxDigits - decimalDigits > 0 && newText.length > maxDigits - decimalDigits) {
        newText = newText.substring(0, maxDigits - decimalDigits);
      }
      var newNumber = double.tryParse(newText) ?? 0;

      // handle maxValue
      if (maxValue != null && (newNumber * pow(10, decimalDigits)) > maxValue!) {
        newNumber = maxValue! / pow(10, decimalDigits);
        newString = (isNegative ? '-' : '') + format.format(newNumber).trim();
      } else {
        newString = (isNegative ? '-' : '') + formatNoDecimal.format(newNumber).trim();
      }

      offset = newString.length - selectionIndexFromTheRight;
    }

    // panic mode offset < 0 : cause crash, set offset to another value
    if (offset < 0 || offset > newString.length) {
      offset = newValue.selection.end;
    }
    return TextEditingValue(
      text: newString,
      selection: TextSelection.collapsed(offset: offset),
    );
  }
}

class RepaymentInputTextComponent extends StatefulWidget {
  final Key? inputKey;
  final String? initValue;
  final int? maxLength;
  final bool isCapitalized;
  final RegExp? validationRegExp;
  final String? label;
  final bool enabled;
  final ValueChanged<String>? onChanged;
  final ValueChanged<FocusNode>? onFocusChanged;
  final ValueChanged<String>? onEditingComplete;
  final TextInputType? keyboardType;
  final TextAlign textAlign;
  final String helpText;
  final bool isError;
  final bool isReadOnly;
  final GestureTapCallback? onTap;
  final Widget? suffix;
  final Widget? prefix;
  final bool obscureText;
  final bool isAdvanced;
  final bool isVerified;
  final List<TextInputFormatter> inputFormates;
  final FocusNode? focusNode;
  final Stream<bool>? shouldScrollStream;
  final Stream<String>? valueStream;
  final String? prefixText;
  final bool alignSuffixToText;
  final bool allowTrailing;
  final bool validateOnBegin;
  final Iterable<String>? autofillHints;
  final bool showCounter;
  final TextEditingController? controller;
  final String? hintText;
  final bool autoTruncate;
  final bool requestFocus;
  // ignore: deprecated_member_use
  final ToolbarOptions? toolbarOptions;

  const RepaymentInputTextComponent({
    this.inputKey,
    this.initValue,
    this.maxLength,
    this.isCapitalized = false,
    this.validationRegExp,
    this.label,
    this.onChanged,
    this.enabled = true,
    this.shouldScrollStream,
    this.onFocusChanged,
    this.keyboardType,
    this.onEditingComplete,
    this.valueStream,
    this.helpText = '',
    this.isError = false,
    this.isReadOnly = false,
    this.textAlign = TextAlign.start,
    this.onTap,
    this.suffix,
    this.prefix,
    this.obscureText = false,
    this.isAdvanced = false,
    this.isVerified = false,
    this.inputFormates = const <TextInputFormatter>[],
    this.prefixText,
    this.focusNode,
    this.alignSuffixToText = false,
    this.allowTrailing = true,
    this.validateOnBegin = false,
    this.autofillHints,
    this.controller,
    this.showCounter = true,
    this.autoTruncate = true,
    this.hintText,
    this.requestFocus = true,
    this.toolbarOptions,
    Key? key,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _RepaymentInputTextComponentState();
}

class _RepaymentInputTextComponentState extends State<RepaymentInputTextComponent> {
  late final FocusNode focusNode;
  StreamSubscription<bool>? scrollStreamSubscription;
  StreamSubscription<String>? valueStreamSubscription;
  late final GlobalKey globalKey;
  late final TextEditingController controller;
  bool inputError = false;
  String helpText = '';
  InputDecoration defaulDecoration(BuildContext context) => InputDecoration(
        hintText: widget.hintText,
        suffixIcon: widget.alignSuffixToText ? null : suffix(),
        suffix: widget.alignSuffixToText ? suffix() : null,
        prefixIconConstraints: const BoxConstraints(),
        suffixIconConstraints: const BoxConstraints(),
        prefix: widget.prefix,
        prefixText: widget.prefixText,
        counterStyle: const TextStyle(
          height: double.minPositive,
        ),
        counterText: '',
        contentPadding: EdgeInsets.fromLTRB(
          widget.prefix != null ? 8 : 16,
          16,
          widget.suffix != null ? 0 : 16,
          16,
        ),
        labelText: widget.label,
        labelStyle: TextStyleTheme.of(context).body2.copyWith(color: ColorTheme.of(context).secondaryTextColor),
        floatingLabelStyle: TextStyleTheme.of(context).body2.copyWith(color: ColorTheme.of(context).secondaryTextColor),
        border: OutlineInputBorder(borderSide: BorderSide(color: ColorTheme.of(context).foreground15Color)),
        focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: ColorTheme.of(context).foreground15Color)),
        enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: ColorTheme.of(context).foreground15Color)),
      );

  @override
  void didUpdateWidget(covariant RepaymentInputTextComponent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initValue != widget.initValue && widget.initValue != controller.text) {
      final newText = widget.autoTruncate ? validateinputLength(widget.initValue) : widget.initValue ?? '';
      controller.text = newText;
      if (controller.selection.baseOffset == -1 && newText.isNotEmpty && focusNode.hasFocus) {
        controller.selection = TextSelection.collapsed(offset: newText.length);
      }
      //validate init value
      if (widget.validateOnBegin) {
        _validateInput();
      }
    }
    if (oldWidget.helpText != widget.helpText) {
      helpText = widget.helpText;
    }
    if (oldWidget.isError != widget.isError) {
      inputError = widget.isError;
    }
  }

  @override
  void initState() {
    super.initState();
    controller = widget.controller ?? TextEditingController(text: validateinputLength(widget.initValue));
    inputError = widget.isError;
    helpText = widget.helpText;

    scrollStreamSubscription = widget.shouldScrollStream?.listen((event) {
      selectWidget();
    });

    valueStreamSubscription = widget.valueStream?.listen((event) {
      controller.text = validateinputLength(event);
    });

    focusNode = widget.focusNode ?? FocusNode()
      ..addListener(() {
        if (!focusNode.hasFocus) {
          _validateInput();
        }
        if (widget.onFocusChanged != null) {
          widget.onFocusChanged!.call(focusNode);
        }
      });

    globalKey = GlobalKey();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.validateOnBegin) {
        _validateInput();
      }
    });
  }

  void _validateInput() {
    if (!mounted) {
      return;
    }

    if (widget.allowTrailing) {
      final oldText = controller.text;
      //Remove leading, trailing spaces and more than 1 spaces between words
      controller.text = controller.text.trim().replaceAll(RegExp(r'\s+'), ' ');
      if (oldText != controller.text) {
        widget.onChanged?.call(controller.value.text);
      }
    }

    if (widget.validationRegExp != null) {
      inputError = !widget.validationRegExp!.hasMatch(controller.text);
      if (inputError) {
        helpText = widget.helpText.isEmpty
            ? controller.text.isEmpty
                ? L10nCappUi.of(context).pleaseEnter(widget.label)
                : L10nCappUi.of(context).inputIsInvalid(widget.label)
            : widget.helpText;
      } else {
        helpText = widget.helpText;
        if (widget.isError) {
          inputError = true;
        }
      }
    }
    setState(() {});
  }

  @override
  void dispose() {
    controller.dispose();
    valueStreamSubscription?.cancel();
    scrollStreamSubscription?.cancel();
    if (widget.focusNode == null) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalHorizontal(
      child: Container(
        key: globalKey,
        padding: const EdgeInsets.only(top: 12, bottom: 1),
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _content(),
        ),
      ),
    );
  }

  List<Widget> _content() => [
        Container(
          decoration: !widget.enabled
              ? DottedDecoration(
                  dash: const [1, 1],
                  color: inputError ? Theme.of(context).primaryColor : ColorTheme.of(context).foreground60Color,
                )
              : null,
          // ignore: no-native-input-elements
          child: TextFormField(
            textInputAction: TextInputAction.next,
            textAlignVertical: TextAlignVertical.center,
            controller: controller,
            enabled: widget.enabled,
            textAlign: widget.textAlign,
            key: widget.inputKey,
            readOnly: widget.isReadOnly,
            focusNode: focusNode,
            obscureText: widget.obscureText,
            maxLength: (widget.maxLength ?? 0) > 0 ? widget.maxLength : null,
            autocorrect: false,
            enableSuggestions: false,
            keyboardType: widget.keyboardType,
            style: widget.enabled
                ? widget.isAdvanced
                    ? TextStyleTheme.of(context).header5.copyWith(
                          color: ColorTheme.of(context).defaultTextColor,
                        )
                    : TextStyleTheme.of(context).body2.copyWith(color: ColorTheme.of(context).defaultTextColor)
                : widget.isAdvanced
                    ? TextStyleTheme.of(context).header5.copyWith(
                          color: ColorTheme.of(context).secondaryTextColor,
                        )
                    : TextStyleTheme.of(context).body2.copyWith(color: ColorTheme.of(context).secondaryTextColor),
            inputFormatters: [
              if (widget.isCapitalized)
                TextInputFormatter.withFunction(
                  (oldValue, newValue) => newValue.copyWith(text: newValue.text.toUpperCase()),
                ),
              ...widget.inputFormates,
            ],
            decoration: defaulDecoration(context),
            onEditingComplete: () {
              FocusScope.of(context).unfocus();
              widget.onEditingComplete?.call(controller.value.text);
            },
            onChanged: (value) {
              if (widget.validationRegExp != null && widget.validationRegExp!.hasMatch(controller.text)) {
                setState(() {
                  inputError = false;
                  helpText = widget.helpText;
                });
              }
              widget.onChanged?.call(value);
            },
            onTap: widget.onTap,
            autofillHints: widget.autofillHints,
            // ignore: deprecated_member_use
            toolbarOptions: widget.toolbarOptions,
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: KoyalPadding.xSmall(
                left: false,
                bottom: false,
                child: InputHelpText(
                  widget.isVerified ? widget.helpText : helpText,
                  isError: inputError,
                  isVerified: widget.isVerified,
                ),
              ),
            ),
            if (widget.showCounter && (widget.maxLength ?? 0) != 0)
              KoyalPadding.normalHorizontal(
                child: KoyalText.caption2(
                  '${controller.text.length}/${widget.maxLength}',
                  color: ColorTheme.of(context).secondaryTextColor,
                ),
              ),
          ],
        ),
      ];

  void selectWidget() {
    final keyContext = globalKey.currentContext;
    if (keyContext != null) {
      Future<void>.delayed(const Duration(milliseconds: 350)).then((value) {
        Scrollable.ensureVisible(keyContext, duration: const Duration(milliseconds: 200));
      });
    }
    if (widget.requestFocus) {
      focusNode.requestFocus();
    }
  }

  Widget? suffix() {
    if (widget.suffix != null) {
      return widget.suffix!;
    }

    return inputError
        ? Icon(
            KoyalIcons.alert_circle_outline,
            color: Theme.of(context).primaryColor,
            size: 24,
            key: const Key('__inputErrorIcon__'),
          )
        : null;
  }

  String validateinputLength(String? value) {
    if (value != null && widget.maxLength != null && value.length > widget.maxLength!) {
      return value.substring(0, widget.maxLength);
    }

    return value ?? '';
  }
}
