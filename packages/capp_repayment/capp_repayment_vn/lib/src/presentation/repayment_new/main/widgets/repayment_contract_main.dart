import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart' hide ChipTheme;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_repayment.dart';

class RepaymentContractMain extends StatelessWidget {
  final RepaymentContract contract;
  final Function(RepaymentContract)? onTapExtend;
  final Function(RepaymentContract)? onTap;

  const RepaymentContractMain({
    Key? key,
    required this.contract,
    this.onTapExtend,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    DateTime? dueDate;
    var productName = '';
    String? productImageUrl;
    int? dpd;
    if (contract.contractType == RepaymentContractType.cel) {
      final con = contract.loanContract;
      dueDate = con?.dueDate;
      productImageUrl = con?.productImageUrl;
      productName = con?.productName ?? '';
      if (productName.isEmpty) {
        productName = LoanUtils.getCelProductNameFromLoanType(
          loanType: con?.loanType,
          repaymentLocalization: L10nCappRepayment.of(context),
        );
      }
      dpd = con?.dpd;
    } else {
      final con = contract.relContract;
      final gmaProductType = con?.gmaProductType;
      dpd = con?.dpd;
      if (con != null) {
        dueDate = con.dueDate;
        productName = gmaProductType == RepaymentGmaProductType.bnpl.id
            ? LoanUtils.getHomePayLaterContractName(context)
            : L10nCappRepayment.of(context).repaymentProductCreditCard;
        productImageUrl = '';
      }
    }

    var dueDateString =
        null != dueDate ? '${L10nCappRepayment.of(context).repaymentPayBefore} ${dueDate.toLocal().shortDate()}' : '';
    var imageExtension = '';
    if (null != productImageUrl && productImageUrl.isNotEmpty) {
      final parts = productImageUrl.split('.');
      if (parts.length > 1) {
        imageExtension = parts[parts.length - 1].toLowerCase();
      }
    }
    var imagePath = '';
    if (contract.contractType == RepaymentContractType.cel) {
      if (contract.loanContract?.loanType?.toLowerCase() == RepaymentLoanType.cash.id) {
        imagePath = 'assets/icons/ic_personal_loan_new.png';
      } else {
        imagePath = 'assets/icons/ic_generic_commodity_new.png';
      }
    } else if (contract.contractType == RepaymentContractType.rel) {
      if (contract.relContract?.gmaProductType == RepaymentGmaProductType.bnpl.id) {
        imagePath = 'assets/icons/ic_bnpl_new.png';
      } else {
        imagePath = 'assets/icons/ic_credit_card_black.png';
      }
      // REL and dpd > 0
      if ((dpd ?? 0) > 0) {
        dueDateString = L10nCappRepayment.of(context).repaymentOverdueDesc;
      } else if (dpd == 0 && dueDate == null) {
        dueDateString = '-';
      }
    } else {
      imagePath = 'assets/images/no_picture.jpg';
    }

    return KoyalPadding.normalAll(
      top: false,
      child: GestureDetector(
        onTap: () => onTap?.call(contract),
        child: Container(
          padding: const EdgeInsets.only(top: 16, left: 16, bottom: 16),
          decoration: BoxDecoration(
            color: ColorTheme.of(context).backgroundColor,
            border: Border.all(
              color: ColorTheme.of(context).foreground15Color,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 32,
                height: 32,
                child: null != productImageUrl && productImageUrl.isNotEmpty
                    ? imageExtension == 'svg'
                        ? SvgPicture.network(
                            productImageUrl,
                            width: 32,
                            placeholderBuilder: (context) {
                              return Image.asset(
                                'assets/images/no_picture.jpg',
                                fit: BoxFit.fill,
                                width: 32,
                                height: 32,
                              );
                            },
                          )
                        : Image.network(
                            productImageUrl,
                            errorBuilder: (context, exception, stackTrace) {
                              return Image.asset(
                                'assets/images/no_picture.jpg',
                                fit: BoxFit.fill,
                                width: 32,
                                height: 32,
                              );
                            },
                            fit: BoxFit.fitWidth,
                            width: 32,
                          )
                    : imagePath.asImage(
                        fit: BoxFit.fill,
                        height: 32,
                        width: 32,
                        imagePackage: imagePath == 'assets/images/no_picture.jpg' ? null : 'capp_repayment',
                      ),
              ),
              const SizedBox(
                width: 16,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    KoyalText.subtitle2(
                      productName,
                      color: ColorTheme.of(context).defaultTextColor,
                    ),
                    const SizedBox(
                      height: 4,
                    ),
                    KoyalText.body2(
                      dueDateString,
                      color: ColorTheme.of(context).secondaryTextColor,
                    ),
                    if (contract.contractType == RepaymentContractType.rel && (dpd ?? 0) > 0) ...[
                      const SizedBox(
                        height: KoyalPadding.paddingSmall,
                      ),
                      SimpleChip(
                        key: const Key('__overdueChip__'),
                        label: L10nCappRepayment.of(context).repaymentOverdue,
                        chipTheme: ChipTheme.error(),
                      ),
                    ],
                  ],
                ),
              ),
              // This component is for testing purpose only
              if (context.isFlagEnabledRead(FeatureFlag.repaymentPromiseToPayEntryPointMainScreen))
                TertiaryButton(
                  key: const Key('__promiseToPayButton__'),
                  text: 'Hứa',
                  onPressed: () async {
                    onTapExtend?.call(contract);
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }
}
