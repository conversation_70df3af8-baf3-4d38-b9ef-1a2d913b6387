import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../capp_repayment.dart';

class RepaymentVoucherItem extends StatelessWidget {
  final RepaymentVoucher voucher;
  final bool isApplicable;
  final Function() onRemove;

  const RepaymentVoucherItem({
    Key? key,
    required this.voucher,
    required this.isApplicable,
    required this.onRemove,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        KoyalPadding.normalAll(
          top: false,
          bottom: false,
          child: Container(
            padding: const EdgeInsets.only(top: 16, left: 16, bottom: 16, right: 8),
            decoration: BoxDecoration(
              color: ColorTheme.of(context).backgroundColor,
              border: Border.all(
                color: ColorTheme.of(context).foreground15Color,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                if (isApplicable)
                  SvgPicture.asset(
                    'assets/icons/ic_giftcard.svg',
                    fit: BoxFit.fill,
                    package: 'capp_repayment',
                  )
                else
                  ColorFiltered(
                    colorFilter: ImageUtils.getTintColorFilter(),
                    child: SvgPicture.asset(
                      'assets/icons/ic_giftcard.svg',
                      fit: BoxFit.fill,
                      package: 'capp_repayment',
                    ),
                  ),
                const SizedBox(
                  width: 16,
                ),
                Expanded(
                  child: KoyalText.subtitle2(
                    keyText: const Key('__voucherTitle__'),
                    voucher.name,
                    color: isApplicable
                        ? ColorTheme.of(context).defaultTextColor
                        : ColorTheme.of(context).foreground50Color,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                KoyalIconButton(
                  key: const Key('__voucherDeleteButton__'),
                  iconData: KoyalIcons.trash_outline,
                  onPressed: onRemove,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        if (!isApplicable)
          KoyalPadding.normalAll(
            key: const Key('__voucherInApplicableWarning__'),
            top: false,
            bottom: false,
            child: Alert(
              text: L10nCappRepayment.of(context).voucherIsNotApplicable,
              theme: AlertTheme.error(),
            ),
          )
        else
          const SizedBox.shrink(),
        SizedBox(
          height: isApplicable ? 16 : 24,
        ),
      ],
    );
  }
}
