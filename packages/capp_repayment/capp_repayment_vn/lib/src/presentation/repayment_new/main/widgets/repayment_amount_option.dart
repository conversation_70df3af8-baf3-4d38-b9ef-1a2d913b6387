import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_repayment.dart';

class RepaymentAmountOption extends StatelessWidget {
  final Decimal? amount;
  final String subLabel;
  final RepaymentAmountOptionType option;
  final RepaymentAmountOptionType? selectedOption;
  final Function(RepaymentAmountOptionType lable) onSelected;
  final bool isSelected;
  const RepaymentAmountOption({
    Key? key,
    this.amount,
    required this.subLabel,
    required this.option,
    required this.selectedOption,
    required this.onSelected,
    required this.isSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final label = amount?.formatCurrency() ?? '';
    return KoyalPadding.normalAll(
      top: false,
      bottom: false,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          onSelected.call(option);
        },
        child: SizedBox(
          height: 48,
          child: Row(
            children: [
              KoyalRadio<String>(
                value: option.toString(),
                groupValue: selectedOption?.toString(),
                onChanged: (_) {
                  onSelected.call(option);
                },
              ),
              const SizedBox(width: 6),
              KoyalText.body2(
                subLabel,
                color: isSelected ? ColorTheme.of(context).foreground90Color : ColorTheme.of(context).foreground60Color,
              ),
              const SizedBox(height: 4),
              Expanded(
                child: KoyalText.subtitle2(
                  label,
                  textAlign: TextAlign.right,
                  color: ColorTheme.of(context).foreground90Color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
