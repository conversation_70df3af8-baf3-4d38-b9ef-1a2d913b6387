import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

class RepaymentBankTransferQrIntroScreen extends StatelessWidget {
  const RepaymentBankTransferQrIntroScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__bankTransferQrIntroScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__bankTransferQrIntroAppBar__'),
        title: L10nCappRepayment.of(context).repaymentBankTransferIntroTitle,
        leading: KoyalAppBarLeading.close,
        onClose: () => context.navigator.pop(),
      ),
      backgroundColor: ColorTheme.of(context).backgroundColor,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              children: [
                SectionHeading(
                  title: L10nCappRepayment.of(context).repaymentBankTransferIntroGuideTitle,
                  subtitle: L10nCappRepayment.of(context).repaymentBankTransferIntroGuideSubtitle,
                ),
                KoyalStepInformation(
                  indicatorValue: 1,
                  data: KoyalStepData(text: L10nCappRepayment.of(context).repaymentBankTransferIntro1),
                ),
                'assets/images/bg_qr_intro_1.svg'.asImage(
                  width: 148,
                  height: 252,
                  fit: BoxFit.fill,
                  imagePackage: 'capp_repayment',
                ),
                const SizedBox(
                  height: 16,
                ),
                KoyalStepInformation(
                  indicatorValue: 2,
                  data: KoyalStepData(text: L10nCappRepayment.of(context).repaymentBankTransferIntro2),
                ),
                'assets/images/bg_qr_intro_2.svg'.asImage(
                  width: 148,
                  height: 252,
                  fit: BoxFit.fill,
                  imagePackage: 'capp_repayment',
                ),
                const SizedBox(
                  height: 16,
                ),
                KoyalStepInformation(
                  indicatorValue: 3,
                  data: KoyalStepData(text: L10nCappRepayment.of(context).repaymentBankTransferIntro3),
                ),
                'assets/images/bg_qr_intro_3.png'.asImage(
                  width: 148,
                  height: 252,
                  fit: BoxFit.fill,
                  imagePackage: 'capp_repayment',
                ),
                const SizedBox(
                  height: 96,
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              decoration: BoxDecoration(
                color: ColorTheme.of(context).backgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: HciColors.black.withOpacity(0.15),
                    blurRadius: 4,
                    offset: const Offset(0, -2), // changes position of shadow
                  ),
                ],
              ),
              child: KoyalPadding.normalAll(
                child: PrimaryButton(
                  text: L10nCappRepayment.of(context).repaymentBankTransferIntroOk,
                  onPressed: () {
                    context.navigator.pop();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
