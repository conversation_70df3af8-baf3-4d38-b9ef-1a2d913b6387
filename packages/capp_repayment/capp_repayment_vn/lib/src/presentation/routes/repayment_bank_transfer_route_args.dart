import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:decimal/decimal.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentBankTransferRouteArgs extends ScreenArguments {
  final Decimal? selectedAmount;
  final RepaymentContract selectedContract;
  final RepaymentContractVirtualAccount contractVirtualAccount;

  RepaymentBankTransferRouteArgs({
    this.selectedAmount,
    required this.selectedContract,
    required this.contractVirtualAccount,
  });
}
