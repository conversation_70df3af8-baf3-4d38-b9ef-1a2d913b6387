import 'package:flutter/cupertino.dart';
import 'package:koyal_core/koyal_core.dart';

@immutable
// ignore: must_be_immutable
class RepaymentTransactionSuccessRouteArgs extends ScreenArguments {
  final String contractNo;
  final String customerName;
  final String phoneNumber;
  final String? voucherCode;
  final double finalAmount;
  final double? originalAmount;
  final double? discountAmount;
  final String dateProcessed;
  final String paymentOption; // payment method display name
  final String paymentOptionId; // payment method id (gmaId)
  final String transactionNo;
  final bool isEWallet;

  RepaymentTransactionSuccessRouteArgs({
    required this.isEWallet,
    required this.contractNo,
    required this.customerName,
    required this.phoneNumber,
    this.voucherCode,
    required this.finalAmount,
    this.originalAmount,
    this.discountAmount,
    required this.dateProcessed,
    required this.paymentOption,
    required this.transactionNo,
    required this.paymentOptionId,
  });

  @override
  bool operator ==(Object other) {
    return (other is RepaymentTransactionSuccessRouteArgs) &&
        other.isEWallet == isEWallet &&
        other.contractNo == contractNo &&
        other.customerName == customerName &&
        other.phoneNumber == phoneNumber &&
        other.finalAmount == finalAmount &&
        other.dateProcessed == dateProcessed &&
        other.paymentOption == paymentOption &&
        other.transactionNo == transactionNo &&
        other.paymentOptionId == paymentOptionId;
  }

  @override
  int get hashCode => transactionNo.hashCode;
}
