import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:decimal/decimal.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentPaymentSummaryRouteArgs extends ScreenArguments {
  final Decimal selectedAmount;
  final RepaymentContract contract;
  final String fullName;
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  final RepaymentBank? selectedBank;
  final RepaymentContractVirtualAccount? virtualAccount;
  final RepaymentVoucherCalculationData? voucherCalculationData;
  final String? voucherCode;
  final bool fromRepaymentNew;

  RepaymentPaymentSummaryRouteArgs({
    required this.selectedAmount,
    required this.contract,
    required this.fullName,
    required this.fromRepaymentNew,
    this.selectedPaymentMethod,
    this.selectedBank,
    this.virtualAccount,
    this.voucherCalculationData,
    this.voucherCode,
  });
}
