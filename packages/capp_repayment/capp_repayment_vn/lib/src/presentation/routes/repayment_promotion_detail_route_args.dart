import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentPromotionDetailRouteArgs extends ScreenArguments {
  final RepaymentVoucher? selectedVoucher;
  final bool? isApplied;

  RepaymentPromotionDetailRouteArgs({
    this.selectedVoucher,
    this.isApplied,
  }) : super(businessRouteName: (isApplied ?? false) ? 'discnt_sel_vcher_detail' : 'discnt_unsel_vcher_detail');
}
