import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentAdaMainRouteArgs extends ScreenArguments {
  final String? contractNumber;
  final List<AdaContract>? adaContracts;
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  final List<RepaymentUserPaymentMethod>? userPaymentMethods;
  final bool? canChangeContract;
  final bool? isSkipDisplayIntro;
  final AdaFromFlow? fromFlow;

  RepaymentAdaMainRouteArgs({
    this.contractNumber,
    this.adaContracts,
    this.selectedPaymentMethod,
    this.userPaymentMethods,
    this.canChangeContract,
    this.isSkipDisplayIntro,
    this.fromFlow,
  });
}
