import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:decimal/decimal.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentPromotionListRouteArgs extends ScreenArguments {
  final RepaymentVoucher? appliedVoucher;
  final Decimal? selectedAmount;
  final RepaymentContract? selectedContract;
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  final RepaymentBank? selectedBank;

  RepaymentPromotionListRouteArgs({
    this.selectedAmount,
    this.selectedContract,
    this.selectedPaymentMethod,
    this.selectedBank,
    this.appliedVoucher,
  });
}
