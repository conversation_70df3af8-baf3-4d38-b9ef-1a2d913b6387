import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class LooAdaMainRouteArgs extends ScreenArguments {
  final String flowInstanceId;
  final String contractType; // CEL, REL
  final String? savedPaymentMethodId;
  final String? ddmCode;
  final AdaFromFlow? flow;

  LooAdaMainRouteArgs({
    required this.flowInstanceId,
    required this.contractType,
    this.savedPaymentMethodId,
    this.ddmCode,
    this.flow,
  });
}
