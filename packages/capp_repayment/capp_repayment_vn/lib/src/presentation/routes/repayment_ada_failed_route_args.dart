import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentAdaFailedRouteArgs extends ScreenArguments {
  final AdaContract? contract;
  final AdaDraftContract? draftContract;
  final RepaymentUserPaymentMethod paymentMethod;
  final String? rootRouteName;
  final String? tryAgainPopUntilScreenName;
  final String? errorType;

  RepaymentAdaFailedRouteArgs({
    required this.paymentMethod,
    this.rootRouteName,
    this.tryAgainPopUntilScreenName,
    this.errorType,
    this.contract,
    this.draftContract,
  });
}
