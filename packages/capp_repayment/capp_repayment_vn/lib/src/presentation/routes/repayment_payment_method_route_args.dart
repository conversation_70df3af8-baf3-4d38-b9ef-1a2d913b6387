import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentPaymentMethodRouteArgs extends ScreenArguments {
  final List<RepaymentUserPaymentMethod> paymentMethods;
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  final RepaymentBank? selectedBank;
  final RepaymentContract? selectedContract;

  RepaymentPaymentMethodRouteArgs({
    required this.paymentMethods,
    this.selectedPaymentMethod,
    this.selectedBank,
    required this.selectedContract,
  });
}
