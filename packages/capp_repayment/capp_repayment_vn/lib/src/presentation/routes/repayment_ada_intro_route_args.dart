import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class RepaymentAdaIntroRouteArgs extends ScreenArguments {
  final AdaFromFlow? flow;
  // For loan journey flow
  final String? flowInstanceId;
  final String? contractType; // CEL, REL
  final String? ddmCode;
  final String? savedPaymentMethodId; // gma id

  // For other flows
  final String? contractNumber;

  RepaymentAdaIntroRouteArgs({
    this.flow,
    this.flowInstanceId,
    this.contractType,
    this.ddmCode,
    this.contractNumber,
    this.savedPaymentMethodId,
  });
}
