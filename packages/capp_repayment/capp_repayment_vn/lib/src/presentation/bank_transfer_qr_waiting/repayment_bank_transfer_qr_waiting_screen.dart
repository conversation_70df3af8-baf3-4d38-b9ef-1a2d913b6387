import 'dart:async';

import 'package:capp_domain/capp_domain.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

class RepaymentBankTransferQrWaitingScreen extends StatelessWidget {
  const RepaymentBankTransferQrWaitingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return InfoScreen(
      title: L10nCappRepayment.of(context).repaymentBankTransferQrWaitingTitle,
      subTitle: L10nCappRepayment.of(context).repaymentBankTransferQrWaitingDesc,
      image: const LoadingWidget(),
      alignContentCenter: true,
      primaryButtonText: L10nCappRepayment.of(context).repaymentBankTransferQrWaitingPrimaryBtnTitle,
      primaryButtonOnClick: () {
        unawaited(context.navigator.toMainScreen(arguments: MainScreenArguments(initialTab: TabItem.loans)));
      },
      secondaryButtonText: L10nCappRepayment.of(context).repaymentBankTransferQrWaitingSecondBtnTitle,
      secondaryButtonOnClick: () {
        context.navigator.pop();
      },
    );
  }
}
