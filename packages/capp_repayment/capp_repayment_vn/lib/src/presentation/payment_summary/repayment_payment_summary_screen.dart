// ignore_for_file: use_build_context_synchronously

import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:dartz/dartz.dart' hide State;
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:intl/intl.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vnpay/vnpay_plugin.dart';
import 'package:zalopay/zalopay_plugin.dart';

import '../../../capp_repayment.dart';

typedef InitPayPluginInit = Future<void> Function(VnPaySettings setting);

class RepaymentPaymentSummaryScreen extends StatefulWidget with RouteWrapper {
  final RepaymentPaymentSummaryRouteArgs arguments;

  const RepaymentPaymentSummaryScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) {
    final enableDirectDiscount = context.isFlagEnabledRead(FeatureFlag.repaymentDirectDiscountMainScreen);
    final isEnableMomoV2 = context.isFlagEnabledRead(FeatureFlag.repaymentMomoApiV2);
    return BlocProvider(
      create: (_) => context.get<RepaymentPaymentSummaryBloc>()
        ..add(
          RepaymentPaymentSummaryEvent.initialize(
            selectedAmount: arguments.selectedAmount,
            contract: arguments.contract,
            fullName: arguments.fullName,
            selectedPaymentMethod: arguments.selectedPaymentMethod!,
            selectedBank: arguments.selectedBank,
            virtualAccount: arguments.virtualAccount,
            voucherCalculationData: arguments.voucherCalculationData,
            fromRepaymentNew: arguments.fromRepaymentNew,
            isEnableDirectDiscount: enableDirectDiscount,
            voucherCode: arguments.voucherCode,
            isEnableMomoV2: isEnableMomoV2,
            repaymentLocalization: L10nCappRepayment.of(context),
          ),
        ),
      child: this,
    );
  }

  @override
  RepaymentPaymentSummaryScreenState createState() => RepaymentPaymentSummaryScreenState();
}

class RepaymentPaymentSummaryScreenState extends State<RepaymentPaymentSummaryScreen> {
  late RepaymentPaymentSummaryBloc _bloc;
  bool isInitialized = false;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of<RepaymentPaymentSummaryBloc>(context);
    context.get<CappRepaymentTrackingService>().trackOwnOnlinePaymentSummaryScreenView();
  }

  @override
  void dispose() {
    GetIt.instance.get<LockStatusBloc>().add(const LockStatusEvent.setEnabled(enabled: true));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
          bloc: _bloc,
          listenWhen: (previous, current) =>
              current.loadingState == LoadingState.isInitial ||
              previous.loadingState != current.loadingState ||
              previous.failureOrSuccessTransactionResult != current.failureOrSuccessTransactionResult,
          listener: (context, state) async {
            if (state.loadingState == LoadingState.isInitial) {
              if (!isInitialized) {
                final isProd = state.isPartnerProdSetting;
                if (widget.arguments.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.vnpay.getId()) {
                  await _initVnPay(context, isProd);
                } else if (widget.arguments.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.zalo.getId()) {
                  await _initZaloPay(isProd);
                }
                isInitialized = true;
              }
            } else if (state.loadingState == LoadingState.isCompleted) {
              state.failureOrSuccessTransactionResult.fold(() {}, (resp) {
                resp.fold((l) {}, (r) {
                  final selectedPaymentMethod = state.selectedPaymentMethod;

                  if (r.paymentStatus == RepaymentTransactionStatus.fail.status ||
                      r.paymentStatus == RepaymentTransactionStatus.cancel.status) {
                    final errorInfo = r.paymentStatus == RepaymentTransactionStatus.fail.status
                        ? RepaymentTransactionStatus.fail.name
                        : RepaymentTransactionStatus.cancel.name;
                    _navigateToTransactionFailedScreen(
                      errorType: TrackingUtils.getErrorType(
                        errorInfo: errorInfo,
                        paymentMethod: selectedPaymentMethod,
                      ),
                    );
                  } else if (r.paymentStatus == RepaymentTransactionStatus.success.status) {
                    _navigateToTransactionSuccessScreen(
                      args: RepaymentTransactionSuccessRouteArgs(
                        isEWallet: false,
                        contractNo: r.contractNumber ?? '',
                        customerName: state.fullName,
                        phoneNumber: state.phoneNumber,
                        finalAmount: r.finalAmount!.toDouble(),
                        discountAmount: r.discountAmount?.toDouble(),
                        originalAmount: r.originalAmount?.toDouble(),
                        dateProcessed: DateFormat(RepaymentFormatConfig.storageDateTimeFormat).format(DateTime.now()),
                        paymentOption: selectedPaymentMethod!.title ?? '',
                        paymentOptionId: selectedPaymentMethod.gmaId,
                        transactionNo: r.pcsTransactionId ?? '',
                      ),
                    );
                  } else if (r.paymentStatus == RepaymentTransactionStatus.inprogress.status) {
                    if (selectedPaymentMethod != null) {
                      final transactionId = r.gmaTransactionId ?? '';
                      VnNavigationUtils.navigateToCheckResultScreen(
                        context: context,
                        paymentMethod: selectedPaymentMethod,
                        uuid: transactionId,
                        rootRouteName: 'repayment_payment_summary_screen',
                        phoneNumber: state.phoneNumber,
                      );
                    }
                  }
                });
              });
            }
          },
        ),
        BlocListener<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
          bloc: _bloc,
          listenWhen: (p, c) =>
              p.failureOrSuccessMomo != c.failureOrSuccessMomo &&
              p.loadingState != c.loadingState &&
              c.loadingState == LoadingState.isCompleted &&
              c.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.momo.getId(),
          listener: (context, state) {
            _handleMomoResult(state);
          },
        ),
        BlocListener<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
          bloc: _bloc,
          listenWhen: (p, c) =>
              p.failureOrSuccessOnePay != c.failureOrSuccessOnePay &&
              p.loadingState != c.loadingState &&
              c.loadingState == LoadingState.isCompleted &&
              c.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.onepay.getId(),
          listener: (context, state) {
            _handleOnePayResult(state);
          },
        ),
        BlocListener<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
          bloc: _bloc,
          listenWhen: (p, c) =>
              p.failureOrSuccessVnPay != c.failureOrSuccessVnPay &&
              p.loadingState != c.loadingState &&
              c.loadingState == LoadingState.isCompleted &&
              c.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.vnpay.getId(),
          listener: (context, state) async {
            await _handleVnPayResult(state);
          },
        ),
        BlocListener<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
          bloc: _bloc,
          listenWhen: (p, c) =>
              p.failureOrSuccessShopeePay != c.failureOrSuccessShopeePay &&
              p.loadingState != c.loadingState &&
              c.loadingState == LoadingState.isCompleted &&
              c.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.shopee.getId(),
          listener: (context, state) {
            _handleShopeePayResult(state);
          },
        ),
        BlocListener<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
          bloc: _bloc,
          listenWhen: (p, c) =>
              p.failureOrSuccessZaloPay != c.failureOrSuccessZaloPay &&
              p.loadingState != c.loadingState &&
              c.loadingState == LoadingState.isCompleted &&
              c.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.zalo.getId(),
          listener: (context, state) {
            _handleZaloPayResult(state);
          },
        ),
        BlocListener<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
          bloc: _bloc,
          listenWhen: (p, c) =>
              p.failureOrSuccessViettelMoney != c.failureOrSuccessViettelMoney &&
              p.loadingState != c.loadingState &&
              c.loadingState == LoadingState.isCompleted &&
              c.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.viettel.getId(),
          listener: (context, state) {
            _handleViettelMoneyResult(state);
          },
        ),
      ],
      child: BlocBuilder<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
        builder: (context, state) {
          var paymentMethodName = '';
          final selectedPaymentMethod = state.selectedPaymentMethod;
          if (selectedPaymentMethod != null) {
            if (selectedPaymentMethod.gmaId == RepaymentPaymentMethod.mobileBanking.getId()) {
              final selectedBank = state.selectedBank;
              paymentMethodName =
                  '${L10nCappRepayment.of(context).repaymentMobileBankingApp} - ${selectedBank?.shortName}';
            } else {
              paymentMethodName = state.selectedPaymentMethod!.title ?? '';
            }
          }
          final loadingText = state.isCheckingTransactionResult ?? false
              ? ''
              : '$paymentMethodName ${L10nCappRepayment.of(context).repaymentGatewayIsOpening}';

          final isShownVoucherInfo = state.fromRepaymentNew && state.isEnableDirectDiscount;
          final selectedAmount = state.selectedAmount;
          final finalAmount = state.voucherCalculationData?.finalAmount ?? state.selectedAmount;
          final discountAmount = state.voucherCalculationData?.discountAmount ?? Decimal.zero;
          final originalAmount = state.voucherCalculationData?.originalAmount ?? state.selectedAmount;

          return LifeCycleManager(
            onResumed: () {
              if (state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.vnpay.getId() &&
                  state.isCalledVnPayMobileBankingApp) {
                _bloc
                  ..add(const RepaymentPaymentSummaryEvent.checkTransactionResult())
                  ..add(
                    const RepaymentPaymentSummaryEvent.setCalledVnPayMobileBankingApp(
                      isCalledVnPayMobileBankingApp: false,
                    ),
                  );
              }
            },
            child: RepaymentProgressContainer(
              isLoading: state.loadingState == LoadingState.isLoading,
              loadingText: loadingText,
              child: KoyalScaffold(
                key: const Key('__paymentSummaryScreen__'),
                appBar: KoyalAppBar(
                  key: const Key('__paymentSummaryAppBar__'),
                  title: L10nCappRepayment.of(context).repaymentPaymentSummary,
                ),
                body: Container(
                  color: ColorTheme.of(context).backgroundColor,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SectionHeading(
                                key: const Key('__repaymentReviewInfoTitle__'),
                                title: L10nCappRepayment.of(context).repaymentPaymentInformation,
                                subtitle: L10nCappRepayment.of(context).repaymentPleaseReviewYourInformation,
                              ),
                              KoyalPadding.normalAll(
                                key: const Key('__contractNumberInfo__'),
                                child: Row(
                                  children: [
                                    KoyalText.body2(
                                      L10nCappRepayment.of(context).repaymentContractNumber,
                                      color: ColorTheme.of(context).secondaryTextColor,
                                    ),
                                    Expanded(
                                      child: KoyalText.body2(
                                        state.contract?.contractNumber ?? '',
                                        color: ColorTheme.of(context).defaultTextColor,
                                        textAlign: TextAlign.end,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const ListDivider(),
                              KoyalPadding.normalAll(
                                key: const Key('__phoneNumberInfo__'),
                                child: Row(
                                  children: [
                                    KoyalText.body2(
                                      L10nCappRepayment.of(context).repaymentPhoneNumber,
                                      color: ColorTheme.of(context).secondaryTextColor,
                                    ),
                                    Expanded(
                                      child: KoyalText.body2(
                                        state.phoneNumber,
                                        color: ColorTheme.of(context).defaultTextColor,
                                        textAlign: TextAlign.end,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const ListDivider(),
                              KoyalPadding.normalAll(
                                key: const Key('__paymentOptionInfo__'),
                                child: Row(
                                  children: [
                                    KoyalText.body2(
                                      L10nCappRepayment.of(context).repaymentPaymentOption,
                                      color: ColorTheme.of(context).secondaryTextColor,
                                    ),
                                    Expanded(
                                      child: KoyalText.body2(
                                        paymentMethodName,
                                        color: ColorTheme.of(context).defaultTextColor,
                                        textAlign: TextAlign.end,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SectionDivider(),
                              if (isShownVoucherInfo) ...[
                                KoyalPadding.normalAll(
                                  key: const Key('__totalAmountInfo__'),
                                  child: Row(
                                    children: [
                                      KoyalText.body2(
                                        L10nCappRepayment.of(context).paymentAmount,
                                        color: ColorTheme.of(context).secondaryTextColor,
                                      ),
                                      Expanded(
                                        child: KoyalText.body2(
                                          key: const Key('__totalAmountValue__'),
                                          originalAmount.formatCurrency(),
                                          color: ColorTheme.of(context).defaultTextColor,
                                          textAlign: TextAlign.end,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const ListDivider(),
                                KoyalPadding.normalAll(
                                  key: const Key('__discountAmountInfo__'),
                                  child: Row(
                                    children: [
                                      KoyalText.body2(
                                        L10nCappRepayment.of(context).repaymentDiscountAmount,
                                        color: ColorTheme.of(context).secondaryTextColor,
                                      ),
                                      Expanded(
                                        child: KoyalText.body2(
                                          key: const Key('__discountAmountValue__'),
                                          discountAmount.formatCurrency(),
                                          color: ColorTheme.of(context).defaultTextColor,
                                          textAlign: TextAlign.end,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const ListDivider(),
                                KoyalPadding.normalAll(
                                  key: const Key('__finalAmountInfo__'),
                                  child: Row(
                                    children: [
                                      KoyalText.body2(
                                        L10nCappRepayment.of(context).repaymentFinalAmount,
                                        color: ColorTheme.of(context).secondaryTextColor,
                                      ),
                                      Expanded(
                                        child: KoyalText.subtitle1(
                                          key: const Key('__finalAmountValue__'),
                                          finalAmount.formatCurrency(),
                                          color: ColorTheme.of(context).defaultTextColor,
                                          textAlign: TextAlign.end,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ] else
                                KoyalPadding.normalAll(
                                  key: const Key('__totalAmountInfo__'),
                                  child: Row(
                                    children: [
                                      KoyalText.body2(
                                        L10nCappRepayment.of(context).repaymentTotalAmount,
                                        color: ColorTheme.of(context).secondaryTextColor,
                                      ),
                                      Expanded(
                                        child: KoyalText.subtitle1(
                                          key: const Key('__totalAmountValue__'),
                                          selectedAmount.formatCurrency(),
                                          color: ColorTheme.of(context).defaultTextColor,
                                          textAlign: TextAlign.end,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        color: ColorTheme.of(context).backgroundColor,
                        child: HorizontalButtonsLayout(
                          primaryButton: PrimaryButton(
                            key: const Key('__confirmPaymentButton__'),
                            text: L10nCappRepayment.of(context).paymentSummaryButtonConfirm,
                            onPressed: () {
                              context.get<CappRepaymentTrackingService>().trackOwnOnlinePaymentSummaryClickConfirm();

                              final selectedPaymentMethod = state.selectedPaymentMethod;
                              if (selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.mobileBanking.getId()) {
                                final selectedBank = state.selectedBank;
                                final virtualAccount = state.virtualAccount;

                                if (selectedBank != null && virtualAccount != null) {
                                  final deeplink = _bloc.getBaoKimDeeplink(
                                    settings: _bloc.settings.baoKimSettings,
                                    bank: selectedBank,
                                    virtualAccount: virtualAccount,
                                  );
                                  if (deeplink.isNotEmpty) {
                                    GmaPlatform.launchUrl(deeplink, mode: LaunchMode.externalApplication);
                                  }
                                }
                              } else {
                                _bloc.add(const RepaymentPaymentSummaryEvent.confirm());
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleOnePayResult(RepaymentPaymentSummaryState state) {
    final onepayPaymentMethod = RepaymentPaymentMethod.onepay.getUserPaymentMethod(
      userPaymentMethods: state.paymentMethods,
      l10nCappRepayment: L10nCappRepayment.of(context),
    );

    final transaction = state.repaymentTransaction;
    state.failureOrSuccessOnePay.fold(() {}, (a) {
      a.fold((l) {
        _navigateToTransactionFailedScreen(
          errorType: TrackingUtils.getErrorType(errorInfo: ApiUtils.parseFromRepaymentFailureToErrorType(l).name),
        );
      }, (r) async {
        final contractNumber = state.contract?.contractNumber;
        if (null != transaction && null != state.contract) {
          final response = await context.navigator.pushTyped(
            package: CappRepayment,
            screen: OnePayScreen,
            arguments: OnePayRouteArgs(
              state.fullName,
              contractNumber ?? '',
              transaction.pcsTransactionId ?? '',
              transaction.finalAmount!.toDouble(),
              r.paymentUrl ?? '',
            ),
          ) as Either<Tuple2<String, String>, Map>?;
          // Delay a bit for screen_view event tracking from onePay -> summary to be tracked
          await Future.delayed(const Duration(milliseconds: 200));
          response?.fold(
            (l) {
              final errorCode = l.value1;
              final errorMessage = l.value2;

              _navigateToTransactionFailedScreen(
                message: errorMessage,
                errorType: TrackingUtils.getErrorType(
                  errorInfo: errorCode,
                  paymentMethod: onepayPaymentMethod,
                ),
              );
            },
            (r) {
              // In case payment with voucher  => get promotion info from BE transaction detail
              if (_bloc.isPaymentWithVoucherCode()) {
                _bloc.add(const RepaymentPaymentSummaryEvent.checkTransactionResult());
              } else {
                _navigateToTransactionSuccessScreen(
                  args: RepaymentTransactionSuccessRouteArgs(
                    isEWallet: false,
                    contractNo: r['contractNumber']?.toString() ?? '',
                    customerName: r['customerName']?.toString() ?? '',
                    phoneNumber: state.phoneNumber,
                    finalAmount: r['amount'] as double,
                    dateProcessed: r['date']?.toString() ?? '',
                    paymentOption: onepayPaymentMethod?.title ?? '',
                    paymentOptionId: onepayPaymentMethod?.gmaId ?? '',
                    transactionNo: r['transactionId']?.toString() ?? '',
                  ),
                );
              }
            },
          );
        }
      });
      return;
    });
  }

  Future<void> _initVnPay(BuildContext context, bool isProd) async {
    final vnPaySettings = _bloc.settings.vnPaySettings;
    final payInit = context.get<InitPayPluginInit>();
    await payInit(
      VnPaySettings(
        isSandBox: !isProd,
        appBackAlert: L10nCappRepayment.of(context).repaymentVnpayAppBackAlert,
        scheme: vnPaySettings.scheme,
        tmnCode: isProd ? vnPaySettings.prodTmnCode : vnPaySettings.tmnCode,
      ),
    );
  }

  Future<void> _handleVnPayResult(RepaymentPaymentSummaryState state) async {
    final vnpayPaymentMethod = RepaymentPaymentMethod.vnpay.getUserPaymentMethod(
      userPaymentMethods: state.paymentMethods,
      l10nCappRepayment: L10nCappRepayment.of(context),
    );

    state.failureOrSuccessVnPay.fold(() {}, (either) {
      either.fold((l) {
        _navigateToTransactionFailedScreen(
          errorType: TrackingUtils.getErrorType(errorInfo: ApiUtils.parseFromRepaymentFailureToErrorType(l).name),
        );
      }, (transaction) async {
        final paymentUrl = transaction.paymentUrl ?? '';
        if (paymentUrl.isEmpty) {
          _navigateToTransactionFailedScreen(
            errorType: TrackingUtils.getErrorType(errorInfo: RepaymentErrorType.unexpected.name),
          );
          return;
        }

        // Store transaction result to cache
        final transactionResult = transaction.transactionInfo;
        if (transactionResult != null) {
          _bloc.add(
            RepaymentPaymentSummaryEvent.storeTransactionResult(
              transactionResult,
            ),
          );
        }
        String? action;
        try {
          action = await VnpayPlugin.makePayment(
            paymentUrl,
          ) as String?;
        } on PlatformException catch (e) {
          final code = e.code;
          debugPrint('Error: RepaymentPaymentSummaryScreen: $code');

          _navigateToTransactionFailedScreen(
            errorType: TrackingUtils.getErrorType(errorInfo: RepaymentErrorType.unexpected.name),
          );
          return;
        }

        if (Constants.vnPayNotificationAppBackAction == action) {
          // User clicks back from sdk webview
          // Do nothing
        } else if (Constants.vnPayNotificationCallMobileBankingAppAction == action) {
          // User select payment via ewallet, banking app
          debugPrint('Select app payment');
          if (GmaPlatform.isAndroid) {
            _bloc.add(const RepaymentPaymentSummaryEvent.checkTransactionResult());
          } else {
            _bloc.add(
              const RepaymentPaymentSummaryEvent.setCalledVnPayMobileBankingApp(
                isCalledVnPayMobileBankingApp: true,
              ),
            );
          }
        } else if (Constants.vnPayNotificationWebBackAction == action) {
          // Trigger when redirect to URL:http://cancel.sdk.merchantbackapp
          // vnp_ResponseCode == 24 / Customer cancel payment
          debugPrint('Cancel payment from web');
          _bloc.add(const RepaymentPaymentSummaryEvent.checkTransactionResult());
        } else if (Constants.vnPayNotificationFailBackAction == action) {
          //Trigger when redirect to URL:http://fail.sdk.merchantbackapp
          // vnp_ResponseCode != 00 / Payment fail
          debugPrint('Payment fail');
          _navigateToTransactionFailedScreen(
            errorType: TrackingUtils.getErrorType(
              errorInfo: RepaymentVnpayErrorType.fail.name,
              paymentMethod: vnpayPaymentMethod,
            ),
          );
        } else if (Constants.vnPayNotificationSuccessBackAction == action) {
          //Trigger when redirect to URL: http://success.sdk.merchantbackapp
          //vnp_ResponseCode == 00) / Payment success
          debugPrint('Payment successful');
          _bloc.add(const RepaymentPaymentSummaryEvent.checkTransactionResult());
        }
      });
    });
  }

  void _handleMomoResult(RepaymentPaymentSummaryState state) {
    state.failureOrSuccessMomo.fold(() {}, (either) {
      either.fold((_) {
        _navigateToTransactionFailedScreen(
          errorType: TrackingUtils.getErrorType(errorInfo: RepaymentErrorType.unexpected.name),
        );
      }, (transaction) async {
        if (state.repaymentMomoTransaction != null && state.repaymentMomoTransaction?.paymentUrl != null) {
          // Store transaction result to cache
          final transactionResult = transaction.transactionInfo;
          if (transactionResult != null) {
            _bloc.add(
              RepaymentPaymentSummaryEvent.storeTransactionResult(transactionResult),
            );
          }

          // Open Momo deeplink
          final url = state.repaymentMomoTransaction!.paymentUrl!;
          final uri = Uri.encodeFull(url);
          await GmaPlatform.launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      });
    });
  }

  void _handleShopeePayResult(RepaymentPaymentSummaryState state) {
    state.failureOrSuccessShopeePay.fold(() {}, (either) {
      either.fold((_) {
        _navigateToTransactionFailedScreen(
          errorType: TrackingUtils.getErrorType(errorInfo: RepaymentErrorType.unexpected.name),
        );
      }, (transaction) async {
        if (state.repaymentShopeePayTransaction != null && state.repaymentShopeePayTransaction?.redirectUrl != null) {
          // Store transaction result to cache
          final transactionResult = transaction.transactionInfo;
          if (transactionResult != null) {
            _bloc.add(RepaymentPaymentSummaryEvent.storeTransactionResult(transactionResult));
          }

          // Open ShopeePay deeplink
          final url = state.repaymentShopeePayTransaction?.redirectUrl ?? '';
          await GmaPlatform.launchUrl(url, mode: LaunchMode.externalApplication);
        }
      });
    });
  }

  void _handleViettelMoneyResult(RepaymentPaymentSummaryState state) {
    final viettelPaymentMethod = RepaymentPaymentMethod.viettel.getUserPaymentMethod(
      userPaymentMethods: state.paymentMethods,
      l10nCappRepayment: L10nCappRepayment.of(context),
    );

    final transaction = state.repaymentTransaction;
    state.failureOrSuccessViettelMoney.fold(() {}, (a) {
      a.fold((l) {
        _navigateToTransactionFailedScreen(
          errorType: TrackingUtils.getErrorType(errorInfo: RepaymentErrorType.unexpected.name),
        );
      }, (r) async {
        final contractNumber = state.contract?.contractNumber;
        if (null != transaction && null != state.contract) {
          final response = await context.navigator.pushTyped(
            package: CappRepayment,
            screen: ViettelMoneyScreen,
            arguments: ViettelMoneyRouteArgs(
              state.fullName,
              contractNumber ?? '',
              transaction.pcsTransactionId ?? '',
              transaction.gmaTransactionId ?? '',
              transaction.finalAmount!.toDouble(),
              r.paymentUrl ?? '',
            ),
          ) as Either<Tuple2<String, String>, Map>?;
          // Delay a bit for screen_view event tracking from viettelMoney -> summary to be tracked
          await Future.delayed(const Duration(milliseconds: 200));
          response?.fold(
            (l) {
              final errorCode = l.value1;
              final errorMessage = l.value2;

              _navigateToTransactionFailedScreen(
                message: errorMessage,
                errorType: TrackingUtils.getErrorType(
                  errorInfo: errorCode,
                  paymentMethod: viettelPaymentMethod,
                ),
              );
            },
            (r) {
              if (_bloc.isPaymentWithVoucherCode()) {
                _bloc.add(const RepaymentPaymentSummaryEvent.checkTransactionResult());
              } else {
                _navigateToTransactionSuccessScreen(
                  args: RepaymentTransactionSuccessRouteArgs(
                    isEWallet: false,
                    contractNo: r['contractNumber']?.toString() ?? '',
                    customerName: r['customerName']?.toString() ?? '',
                    phoneNumber: state.phoneNumber,
                    finalAmount: r['amount'] as double,
                    dateProcessed: r['date']?.toString() ?? '',
                    paymentOption: viettelPaymentMethod?.title ?? '',
                    paymentOptionId: viettelPaymentMethod?.gmaId ?? '',
                    transactionNo: r['transactionId']?.toString() ?? '',
                  ),
                );
              }
            },
          );
        }
      });
    });
  }

  Future<void> _initZaloPay(bool isProd) async {
    final zaloPaySettings = _bloc.settings.zaloPaySettings;
    await ZalopayPlugin.initialize(
      ZaloPaySettings(isSandBox: !isProd, redirectUrl: zaloPaySettings.redirectUrl, appId: zaloPaySettings.appId),
    );
  }

  void _handleZaloPayResult(RepaymentPaymentSummaryState state) {
    state.failureOrSuccessZaloPay.fold(() {}, (either) {
      either.fold((_) {
        _navigateToTransactionFailedScreen(
          errorType: TrackingUtils.getErrorType(errorInfo: RepaymentErrorType.unexpected.name),
        );
      }, (transaction) async {
        // Store transaction result to cache
        final transactionResult = transaction.transactionInfo;
        if (transactionResult != null) {
          _bloc.add(RepaymentPaymentSummaryEvent.storeTransactionResult(transactionResult));
        }

        final result = await ZalopayPlugin.makePayment(
          '${transaction.zpTransToken}',
        ) as String?;
        debugPrint('Zalo payment result: $result');
        if (GmaPlatform.isIOS && result == ZaloPayResultCode.appNotInstall.code ||
            GmaPlatform.isAndroid && result == 'PAYMENT_APP_NOT_FOUND') {
          await ZalopayPlugin.navigateToZaloPayStore();
        }
      });
    });
  }

  void _navigateToTransactionFailedScreen({String? message, String? errorType}) {
    var fromOnlineMethod = true;
    if (_bloc.state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.vnpay.getId() ||
        _bloc.state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.onepay.getId()) {
      fromOnlineMethod = false;
    }
    context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentTransactionFailedScreen,
      arguments: RepaymentTransactionFailedRouteArgs(
        errorMessage: message ?? '',
        fromOnlineMethod: fromOnlineMethod,
        isShowFeedback: true,
        errorType: errorType,
      ),
    );
  }

  void _navigateToTransactionSuccessScreen({required RepaymentTransactionSuccessRouteArgs args}) {
    //Set transaction number custom dimension value to be send in Transaction Success Screen event tracking
    context.get<GlobalTrackingProperties>().transactionNumber = args.transactionNo;
    context.navigator.pushTyped(package: CappRepayment, screen: RepaymentTransactionSuccessScreen, arguments: args);
  }
}
