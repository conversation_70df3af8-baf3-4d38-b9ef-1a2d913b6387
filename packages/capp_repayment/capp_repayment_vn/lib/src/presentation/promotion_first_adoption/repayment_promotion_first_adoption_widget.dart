import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart' hide ChipTheme;
import 'package:koyal_shared_core/koyal_shared_core.dart';

class RepaymentPromotionFirstAdoptionWidget extends StatelessWidget {
  final String disCountVoucher;
  final String minRequiredAmount;
  final Decimal amount;
  final Decimal disCountAmount;
  final VoidCallback? onClaimTap;

  const RepaymentPromotionFirstAdoptionWidget({
    super.key,
    required this.disCountVoucher,
    required this.minRequiredAmount,
    required this.amount,
    required this.disCountAmount,
    this.onClaimTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        MainHeading(
          image: const AssetSvgImage(
            'assets/icons/ic_repayment_promotion_first_adoption.svg',
            package: 'capp_repayment',
            width: 214,
            height: 180,
          ),
          title: L10nCappRepayment.of(context).repaymentPromotionFirstAdoptionTitle(disCountVoucher),
          subtitle: L10nCappRepayment.of(context).repaymentPromotionFirstAdoptionSutitle(minRequiredAmount),
        ),
        KoyalPadding.normalHorizontal(
          child: Container(
            // padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: ColorTheme.of(context).backgroundColor,
              border: Border.all(
                color: ColorTheme.of(context).foreground5Color,
              ),
              borderRadius: BorderRadius.circular(KoyalRoundedCorner.cardCorner),
            ),
            child: Column(
              children: [
                KoyalPadding.normalAll(
                  child: Row(
                    children: [
                      KoyalText.body2(
                        L10nCappRepayment.of(context).repaymentPromotionFirstAdoptionPos,
                        color: ColorTheme.of(context).foreground60Color,
                      ),
                      const Spacer(),
                      KoyalText.body2(amount.formatCurrency(), color: ColorTheme.of(context).foreground90Color),
                    ],
                  ),
                ),
                Divider(
                  height: 1,
                  thickness: 1,
                  color: ColorTheme.of(context).dividerColor,
                ),
                Container(
                  color: HciColors.semanticOrange50,
                  child: KoyalPadding.normalAll(
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            KoyalText.body2(
                              L10nCappRepayment.of(context).repaymentPromotionFirstAdoptionApp,
                              color: ColorTheme.of(context).waringTextColor,
                            ),
                            const SizedBox(height: 8),
                            SimpleChip(
                              label:
                                  L10nCappRepayment.of(context).repaymentPromotionFirstAdoptionVoucher(disCountVoucher),
                              chipTheme: ChipTheme.success(),
                            ),
                          ],
                        ),
                        const Spacer(),
                        KoyalText.subtitle1(
                          (amount - disCountAmount).formatCurrency(),
                          color: ColorTheme.of(context).waringTextColor,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 32),
        VerticalButtonsLayout(
          primaryButton: PrimaryButton(
            text: L10nCappRepayment.of(context).repaymentPromotionFirstAdoptionButton,
            onPressed: () {
              onClaimTap?.call();
              Navigator.pop(context);
            },
          ),
        ),
      ],
    );
  }
}
