import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment.dart';

class RepaymentBankTransferScreen extends StatefulWidget with RouteWrapper {
  final RepaymentBankTransferRouteArgs arguments;

  const RepaymentBankTransferScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentBankTransferBloc>()
          ..add(RepaymentBankTransferEvent.initialize(arguments.selectedContract)),
        child: this,
      );

  @override
  State<RepaymentBankTransferScreen> createState() => _RepaymentBankTransferScreenState();
}

class _RepaymentBankTransferScreenState extends State<RepaymentBankTransferScreen> {
  final String accountNumber = '************';
  final String bankName = 'Bank for Industry and Trade of\nVietnam – CN1 (Vietinbank)';
  final String accountName = 'Home Credit Vietnam Finance\nCompany Limited';

  @override
  void initState() {
    context.get<CappRepaymentTrackingService>().trackOwnBankTransferScreenView();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__bankTransferScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__bankTransferAppBar__'),
        title: L10nCappRepayment.of(context).repaymentBankTransfer,
      ),
      backgroundColor: ColorTheme.of(context).backgroundColor,
      body: Column(
        children: [
          Expanded(
            child: BlocBuilder<RepaymentBankTransferBloc, RepaymentBankTransferState>(
              builder: (context, state) {
                if (state.loadingState == LoadingState.isCompleted) {
                  final contractNum = state.selectedContract?.contractNumber ?? '';
                  final totalAmount = state.totalAmount!.formatCurrency();
                  final dueDate = null != state.selectedContract?.dueDate
                      ? state.selectedContract!.dueDate!.toLocal().mediumDate()
                      : '';
                  return Column(
                    children: [
                      RepaymentContractInfo(accountNo: contractNum, totalAmount: totalAmount, dueDate: dueDate),
                      const SectionDivider(),
                      RepaymentBankTransferInfo(
                        accountNumber: accountNumber,
                        bankName: bankName,
                        accountName: accountName,
                        transferContent: '${L10nCappRepayment.of(context).repaymentForContractNumber} $contractNum',
                        onTapCopyAccountNum: () =>
                            context.get<CappRepaymentTrackingService>().trackOwnBankTransferClickCopyAccountNumber(),
                        onTapCopyTransferContent: () =>
                            context.get<CappRepaymentTrackingService>().trackOwnBankTransferClickCopyTransferContent(),
                      ),
                    ],
                  );
                }

                return Container();
              },
            ),
          ),
          VerticalButtonsLayout(
            primaryButton: PrimaryButton(
              key: const Key('__backToHomeButton__'),
              text: L10nCappRepayment.of(context).backToHomepage,
              onPressed: () {
                context.get<GlobalTrackingProperties>().repaymentAbTest = null;
                context.navigator.toMainScreen();
              },
            ),
          ),
        ],
      ),
    );
  }
}
