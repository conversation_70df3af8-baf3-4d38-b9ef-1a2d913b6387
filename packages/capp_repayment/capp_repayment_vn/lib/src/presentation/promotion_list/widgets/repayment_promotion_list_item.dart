import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../domain/models/promotion/repayment_voucher.dart';

class RepaymentPromotionListItem extends StatelessWidget {
  final RepaymentVoucher voucher;
  final RepaymentVoucher? selectedVoucher;
  final Function(RepaymentVoucher)? onTapViewDetails;
  final Function(RepaymentVoucher)? onChanged;
  const RepaymentPromotionListItem({
    super.key,
    required this.voucher,
    this.onTapViewDetails,
    this.onChanged,
    this.selectedVoucher,
  });

  @override
  Widget build(BuildContext context) {
    final title = voucher.name;
    final minRepaymentAmount = voucher.minRequiredAmount ?? Decimal.zero;
    final dueDate = voucher.endDate?.toLocal().mediumDate() ?? '';
    final iconUrl = voucher.icon ?? '';
    final voucherCode = voucher.code;
    final selectedVoucherCode = selectedVoucher?.code;
    final isValid = voucher.isValid ?? false;

    var subTitle = '';
    if (minRepaymentAmount > Decimal.zero) {
      subTitle = '${L10nCappRepayment.of(context).repaymentMinRepayment} ${minRepaymentAmount.formatCurrency()}';
    }
    if (dueDate.isNotEmpty) {
      if (subTitle.isNotEmpty) {
        subTitle += '\n';
      }
      subTitle += '${L10nCappRepayment.of(context).repaymentDueDateNormal} $dueDate';
    }

    return RepaymentSelectableContainerWithRadio(
      key: Key('__promotionListItemRadio-${voucherCode}__'),
      title: title,
      avatar: isValid
          ? _buildAvatarImage(iconUrl)
          : ColorFiltered(
              colorFilter: ImageUtils.getTintColorFilter(),
              child: _buildAvatarImage(iconUrl),
            ),
      description: subTitle,
      groupValue: selectedVoucherCode,
      onChanged: (value) => isValid ? onChanged?.call(voucher) : null,
      actionText: L10nCappRepayment.of(context).repaymentViewDetails,
      isValid: isValid,
      action: () {
        onTapViewDetails?.call(voucher);
      },
      value: voucherCode,
    );
  }

  Widget _buildAvatarImage(String iconUrl) {
    return iconUrl.isEmpty
        ? const AssetSvgImage(
            'assets/icons/ic_voucher_default.svg',
            fit: BoxFit.fitWidth,
            package: 'capp_repayment',
            width: 32,
          )
        : Image.network(
            iconUrl,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) {
                return child;
              } else {
                return KoyalShimmer(
                  child: Container(
                    decoration: BoxDecoration(
                      color: HciColors.black,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const SizedBox(
                      height: 32,
                      width: 32,
                    ),
                  ),
                );
              }
            },
            errorBuilder: (context, exception, stackTrace) {
              return const AssetSvgImage(
                'assets/icons/ic_voucher_default.svg',
                fit: BoxFit.fitWidth,
                package: 'capp_repayment',
                width: 32,
              );
            },
            width: 32,
          );
  }
}
