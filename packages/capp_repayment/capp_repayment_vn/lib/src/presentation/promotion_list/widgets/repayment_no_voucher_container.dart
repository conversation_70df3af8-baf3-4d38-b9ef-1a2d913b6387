import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/widgets.dart';

class RepaymentNoVoucherContainer extends StatelessWidget {
  final VoidCallback? onTapBack;
  const RepaymentNoVoucherContainer({super.key, this.onTapBack});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        children: [
          'assets/icons/ic_no_voucher.png'.asImage(
            fit: BoxFit.fitWidth,
            width: 110,
            imagePackage: 'capp_repayment',
          ),
          const SizedBox(
            height: KoyalPadding.paddingNormal,
          ),
          KoyalText.body2(L10nCappRepayment.of(context).repaymentSorryWeDontHaveAnyVoucher),
          const Spacer(),
          VerticalButtonsLayout(
            secondaryButton: SecondaryButton(
              key: const Key('__noVoucherButtonBack__'),
              text: L10nCappRepayment.of(context).repaymentPtpProcessBackButton,
              onPressed: () {
                onTapBack?.call();
              },
            ),
          ),
        ],
      ),
    );
  }
}
