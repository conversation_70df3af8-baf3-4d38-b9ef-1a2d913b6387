// ignore_for_file: use_build_context_synchronously

import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../capp_repayment.dart';

class RepaymentPromotionListPopup extends StatefulWidget {
  final RepaymentPromotionListRouteArgs arguments;
  final Function(RepaymentVoucher? selectedVoucher)? onSelectVoucher;

  const RepaymentPromotionListPopup({
    Key? key,
    required this.arguments,
    this.onSelectVoucher,
  }) : super(key: key);

  @override
  RepaymentPromotionListPopupState createState() => RepaymentPromotionListPopupState();
}

class RepaymentPromotionListPopupState extends State<RepaymentPromotionListPopup> with WidgetsBindingObserver {
  late RepaymentPromotionListBloc _bloc;
  final loadingItemsIterable = Iterable<int>.generate(3);

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    _bloc = context.get<RepaymentPromotionListBloc>()
      ..add(
        RepaymentPromotionListEvent.initialize(
          selectedAmount: widget.arguments.selectedAmount,
          selectedContract: widget.arguments.selectedContract,
          selectedPaymentMethod: widget.arguments.selectedPaymentMethod,
          appliedVoucher: widget.arguments.appliedVoucher,
        ),
      );

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepaymentPromotionListBloc, RepaymentPromotionListState>(
      bloc: _bloc,
      listener: (context, state) {
        if (state.loadingState == LoadingState.isCompleted) {
          state.failureOrSuccessFetchVouchers.fold(() => null, (either) {
            either.fold(
              (l) {
                context.get<CappRepaymentTrackingService>().trackDirectDiscountViewErrorFetchVoucherPopup();
                KoyalSnackBar.show(
                  context: context,
                  hasBottomButton: false,
                  duration: const Duration(seconds: 3),
                  actionLabel: L10nCappRepayment.of(context).repaymentRefresh,
                  action: () {
                    context.get<CappRepaymentTrackingService>().trackDirectDiscountClickErrorFetchVoucherRefresh();
                    ScaffoldMessenger.of(context).removeCurrentSnackBar();
                    if (_bloc.state.loadingState != LoadingState.isLoading) {
                      _bloc.add(const RepaymentPromotionListEvent.fetchVouchers());
                    }
                  },
                  message: L10nCappRepayment.of(context).repaymentSomethingWentWrongPleaseTryAgain,
                );
              },
              (vouchers) {
                if (vouchers.isEmpty) {
                  context.get<CappRepaymentTrackingService>().trackDirectDiscountViewNoVoucherPopup();
                } else {
                  context.get<CappRepaymentTrackingService>().trackDirectDiscountViewVoucherListPopup();
                }
              },
            );
          });
        }
      },
      listenWhen: (state1, state2) {
        return state1.loadingState != state2.loadingState;
      },
      builder: (context, state) {
        final listItems = <Widget>[];
        final selectedVoucher = state.selectedVoucher;
        final appliedVoucher = state.appliedVoucher;

        final vouchers = List<RepaymentVoucher>.from(state.vouchers);

        listItems.addAll(
          _buildPromotionSection(
            vouchers: vouchers,
            selectedVoucher: selectedVoucher,
            appliedVoucher: appliedVoucher,
          ),
        );

        return Container(
          height: MediaQuery.of(context).size.height * 0.89,
          padding: const EdgeInsets.only(top: 24, bottom: 3),
          decoration: BoxDecoration(
            color: ColorTheme.of(context).backgroundColor,
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
          ),
          child: Scaffold(
            body: Column(
              children: [
                Container(
                  width: 40,
                  height: 8,
                  decoration: BoxDecoration(
                    color: ColorTheme.of(context).foreground5Color,
                    borderRadius: BorderRadius.circular(100.0),
                  ),
                ),
                SectionHeading(
                  key: const Key('__promotionPopupHeader__'),
                  title: L10nCappRepayment.of(context).repaymentPromotion,
                ),
                Expanded(
                  child: Container(
                    child: (state.loadingState == LoadingState.isLoading)
                        ? KoyalShimmer(
                            child: Column(
                              children: loadingItemsIterable
                                  .map(
                                    (e) => Container(
                                      margin: const EdgeInsets.fromLTRB(
                                        KoyalPadding.paddingNormal,
                                        KoyalPadding.paddingNormal,
                                        KoyalPadding.paddingNormal,
                                        0,
                                      ),
                                      width: double.infinity,
                                      height: 116,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          )
                        : vouchers.isEmpty
                            ? RepaymentNoVoucherContainer(
                                key: const Key('__promotionPopupEmptyContainer__'),
                                onTapBack: () {
                                  context.get<CappRepaymentTrackingService>().trackDirectDiscountClickGoBackNoVoucher();
                                  context.navigator.pop();
                                },
                              )
                            : ListView.builder(
                                key: const Key('__promotionPopupList__'),
                                shrinkWrap: true,
                                padding: EdgeInsets.zero,
                                itemCount: listItems.length,
                                itemBuilder: (context, index) {
                                  return listItems.elementAt(index);
                                },
                              ),
                  ),
                ),
                if (vouchers.isNotEmpty)
                  KoyalPadding.normalAll(
                    child: PrimaryButton(
                      key: const Key('__applyVoucherButton__'),
                      text: L10nCappRepayment.of(context).repaymentApply,
                      onPressed: state.isValidInput
                          ? () {
                              context.get<CappRepaymentTrackingService>().trackDirectDiscountClickApplyVoucher();
                              if (selectedVoucher != null) {
                                if (selectedVoucher != appliedVoucher) {
                                  widget.onSelectVoucher?.call(selectedVoucher);
                                }
                                context.navigator.pop();
                              }
                            }
                          : null,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildPromotionSection({
    required List<RepaymentVoucher> vouchers,
    RepaymentVoucher? selectedVoucher,
    RepaymentVoucher? appliedVoucher,
  }) {
    // Promotions
    final listItems = <Widget>[];
    final voucherViews = vouchers.mapWithIndex((index, voucher) {
      return Padding(
        padding: const EdgeInsets.only(
          left: KoyalPadding.paddingNormal,
          right: KoyalPadding.paddingNormal,
          bottom: KoyalPadding.paddingSmall,
        ),
        child: RepaymentPromotionListItem(
          key: Key('__promotionListItem-${voucher.code}__'),
          voucher: voucher,
          selectedVoucher: selectedVoucher,
          onChanged: (selectedVoucher) {
            final isValid = selectedVoucher.isValid ?? false;
            if (isValid) {
              context.get<CappRepaymentTrackingService>().trackDirectDiscountClickValidVoucher();
            } else {
              context.get<CappRepaymentTrackingService>().trackDirectDiscountClickInvalidVoucher();
            }
            _bloc.add(RepaymentPromotionListEvent.selectVoucher(selectedVoucher: selectedVoucher));
          },
          onTapViewDetails: (voucher) async {
            final isValid = voucher.isValid ?? false;
            if (isValid) {
              context.get<CappRepaymentTrackingService>().trackDirectDiscountClickDetailValidVoucher();
            } else {
              context.get<CappRepaymentTrackingService>().trackDirectDiscountClickDetailInvalidVoucher();
            }
            final isApplyNow = await context.navigator.pushTyped(
              package: CappRepayment,
              screen: RepaymentPromotionDetailScreen,
              arguments: RepaymentPromotionDetailRouteArgs(
                selectedVoucher: voucher,
                isApplied: appliedVoucher?.code == voucher.code,
              ),
            );

            if (isApplyNow is bool) {
              // True for apply now
              if (isApplyNow) {
                if (voucher != appliedVoucher) {
                  widget.onSelectVoucher?.call(voucher);
                }

                context.navigator.pop();
              } else {
                widget.onSelectVoucher?.call(null);
                context.navigator.pop();
              }
            }
          },
        ),
      );
    }).toList();
    // Add voucher views
    if (vouchers.isNotEmpty) {
      listItems.addAll(voucherViews);
    }

    return listItems;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();

    // Do not show snack bar on lock screen
    if (state == AppLifecycleState.resumed && navigationHistoryObserver.top?.settings.name == 'lock_screen') {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }
}
