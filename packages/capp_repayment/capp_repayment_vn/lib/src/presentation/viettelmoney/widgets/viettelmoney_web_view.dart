import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../../../capp_repayment.dart';

class ViettelMoneyWebView extends StatefulWidget {
  final String url;
  final String returnUrl;
  final Function(String code, String transactionId) onResult;

  const ViettelMoneyWebView(
    this.url,
    this.returnUrl,
    this.onResult, {
    Key? key,
  }) : super(key: key);

  @override
  State createState() {
    return ViettelMoneyWebViewState();
  }
}

class ViettelMoneyWebViewState extends State<ViettelMoneyWebView> {
  late InAppWebViewSettings options;
  late Set<Factory<OneSequenceGestureRecognizer>>? _gestureRecognizers;
  String? redirectUrl;

  @override
  void initState() {
    options = InAppWebViewSettings(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      clearCache: true,
      cacheEnabled: false,
      allowsInlineMediaPlayback: Platform.isIOS,
    );
    _gestureRecognizers = <Factory<OneSequenceGestureRecognizer>>{}
      ..add(const Factory<VerticalDragGestureRecognizer>(VerticalDragGestureRecognizer.new));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return !Platform.isLinux
        ? InAppWebView(
            gestureRecognizers: _gestureRecognizers,
            initialUrlRequest: URLRequest(url: WebUri.uri(Uri.parse(widget.url))),
            initialSettings: options,
            onWebViewCreated: (controller) {},
            onLoadStart: (controller, url) {},
            onPermissionRequest: (controller, permissionRequest) async {
              return PermissionResponse(resources: permissionRequest.resources, action: PermissionResponseAction.GRANT);
            },
            shouldOverrideUrlLoading: (controller, navigationAction) async {
              final uri = navigationAction.request.url!;
              final errorCode = uri.queryParameters[Constants.viettelErrorCodeKey];
              final transactionIdWithoutPrefix = uri.queryParameters[Constants.viettelTransactionIdKey] ?? '';
              final transactionId = Constants.viettelTransactionIdPrefix + transactionIdWithoutPrefix;
              if (uri.host.isNotEmpty && widget.returnUrl.contains(uri.host) && errorCode != null) {
                // Prevent handling duplicate result
                if (redirectUrl != uri.rawValue) {
                  widget.onResult(errorCode, transactionId);
                  redirectUrl = uri.rawValue;
                }

                return NavigationActionPolicy.CANCEL;
              }
              return NavigationActionPolicy.ALLOW;
            },
            onLoadStop: (controller, url) async {},
            onReceivedError: (controller, request, error) {},
            onProgressChanged: (controller, progress) {},
            onUpdateVisitedHistory: (controller, url, androidIsReload) {},
            onConsoleMessage: (controller, consoleMessage) {
              if (kDebugMode) {
                print(consoleMessage);
              }
            },
          )
        : const SizedBox();
  }
}
