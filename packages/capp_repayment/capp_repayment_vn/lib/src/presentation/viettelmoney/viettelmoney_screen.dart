import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:dartz/dartz.dart' hide State;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class ViettelMoneyScreen extends StatefulWidget with RouteWrapper {
  final ViettelMoneyRouteArgs arguments;

  const ViettelMoneyScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  ViettelMoneyScreenState createState() => ViettelMoneyScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<ViettelMoneyBloc>()
          ..add(
            ViettelMoneyEvent.initialize(
              arguments.contractNumber,
              arguments.transactionId,
              arguments.uuid,
              arguments.amount,
              arguments.customerName,
              arguments.paymentUrl,
            ),
          ),
        child: this,
      );
}

class ViettelMoneyScreenState extends State<ViettelMoneyScreen> {
  late String _returnUrl;

  @override
  void initState() {
    super.initState();
    final settings = BlocProvider.of<ViettelMoneyBloc>(context).settings;
    _returnUrl = settings.viettelMoneySettings.returnUrl;
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      appBar: KoyalAppBar(
        title: L10nCappRepayment.of(context).repaymentContractRepayment,
      ),
      body: BlocBuilder<ViettelMoneyBloc, ViettelMoneyState>(
        builder: (ctx, state) {
          final viettelMoneyPortalUrl = state.viettelMoneyPortalUrl;
          if (viettelMoneyPortalUrl?.isNotEmpty ?? false) {
            return ViettelMoneyWebView(viettelMoneyPortalUrl!, _returnUrl, (code, transactionId) {
              if (code == Constants.viettelSuccessCode) {
                context.navigator.pop(
                  right<Tuple2<String, String>, Map>(<String, dynamic>{
                    'transactionId': transactionId,
                    'amount': state.amount,
                    'customerName': state.customerName,
                    'contractNumber': state.contractNumber,
                    'date': DateTime.now().toString(),
                    'provider': state.provider,
                  }),
                );
              } else {
                if (code == 'V06') {
                  context.get<ViettelMoneyBloc>().add(ViettelMoneyEvent.cancelTransaction(state.uuid ?? ''));
                }
                context.navigator.pop(left<Tuple2<String, String>, Map>(Tuple2(code, _getErrorMessage(context, code))));
              }
            });
          } else {
            return Container();
          }
        },
      ),
    );
  }

  String _getErrorMessage(BuildContext context, String code) {
    switch (code) {
      case '22':
      case 'V02':
        return L10nCappRepayment.of(context).viettelErrorMessageCode22;
      case 'V03':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeV03;
      case '21':
      case '685':
        return L10nCappRepayment.of(context).viettelErrorMessageCode21;
      case '16':
        return L10nCappRepayment.of(context).viettelErrorMessageCode16;
      case 'W04':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeW04;
      case 'V04':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeV04;
      case 'V05':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeV05;
      case 'V06':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeV06;
      case 'S_MAINTAIN':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeSMaintain;
      case '99':
        return L10nCappRepayment.of(context).viettelErrorMessageCode99;
      case 'M03':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeM03;
      case 'M04':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeM04;
      case '813':
        return L10nCappRepayment.of(context).viettelErrorMessageCode813;
      case 'V01':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeV01;
      case 'M01':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeM01;
      case 'M02':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeM02;
      case 'P48':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeP48;
      case '430':
        return L10nCappRepayment.of(context).viettelErrorMessageCode430;
      case '427':
        return L10nCappRepayment.of(context).viettelErrorMessageCode427;
      case '191':
        return L10nCappRepayment.of(context).viettelErrorMessageCode191;
      case 'P01':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeP01;
      case 'P03':
        return L10nCappRepayment.of(context).viettelErrorMessageCodeP03;
      case '32':
        return L10nCappRepayment.of(context).viettelErrorMessageCode32;
    }
    return '';
  }
}
