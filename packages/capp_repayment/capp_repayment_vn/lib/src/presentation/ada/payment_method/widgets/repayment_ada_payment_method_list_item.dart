import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart' hide ChipTheme;

import '../../../../../capp_repayment.dart';

class RepaymentAdaPaymentMethodListItem extends StatelessWidget {
  final String title;
  final String? description;
  final String? badge;
  final String iconUrl;
  final RepaymentUserPaymentMethod method;
  final Function()? onTap;

  final bool selected;

  const RepaymentAdaPaymentMethodListItem({
    Key? key,
    required this.title,
    this.description,
    required this.iconUrl,
    this.badge,
    required this.selected,
    required this.method,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalAll(
      top: false,
      left: false,
      right: false,
      child: SingleSelectableContainer(
        value: true,
        key: Key('__paymentMethodItem__${method.gmaId}'),
        simpleTag: (badge ?? '').isNotEmpty
            ? SimpleChip(
                key: Key('__paymentMethodItemBadge-${method.gmaId}__'),
                label: badge!,
                chipTheme: ChipTheme.success(),
              )
            : null,
        avatar: Container(
          padding: EdgeInsets.only(top: (description ?? '').isEmpty || title.isEmpty ? 0 : 6),
          child: iconUrl.asImage(
            fit: BoxFit.fitWidth,
            showShimmer: true,
            width: 32,
            imagePackage: 'capp_repayment',
          ),
        ),
        onChanged: (_) {
          onTap?.call();
        },
        showRadioIcon: false,
        groupValue: selected,
        title: title,
      ),
    );
  }
}
