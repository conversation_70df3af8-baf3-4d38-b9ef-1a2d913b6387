import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_repayment.dart';

class RepaymentAdaPaymentMethodPopup extends StatefulWidget {
  final RepaymentAdaPaymentMethodRouteArgs arguments;
  const RepaymentAdaPaymentMethodPopup({
    Key? key,
    required this.arguments,
  }) : super(key: key);

  @override
  RepaymentAdaPaymentMethodPopupState createState() => RepaymentAdaPaymentMethodPopupState();
}

class RepaymentAdaPaymentMethodPopupState extends State<RepaymentAdaPaymentMethodPopup> {
  late RepaymentAdaPaymentMethodBloc _bloc;

  @override
  void initState() {
    _bloc = context.get<RepaymentAdaPaymentMethodBloc>()
      ..add(
        RepaymentAdaPaymentMethodEvent.initialize(
          paymentMethods: widget.arguments.paymentMethods,
          selectedPaymentMethod: widget.arguments.selectedPaymentMethod,
          selectedContract: widget.arguments.selectedContract,
        ),
      );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RepaymentAdaPaymentMethodBloc, RepaymentAdaPaymentMethodState>(
      bloc: _bloc,
      builder: (context, state) {
        final listItems = <Widget>[];
        final selectedPaymentMethod = state.selectedPaymentMethod;

        // Payment methods
        final paymentMethods =
            List<RepaymentUserPaymentMethod>.from(state.paymentMethods ?? <RepaymentUserPaymentMethod>[]);
        if (paymentMethods.isNotEmpty) {
          listItems.addAll(
            _getPaymentSection(
              paymentMethods: paymentMethods,
              selectedPaymentMethod: selectedPaymentMethod,
              flow: widget.arguments.flow,
              contract: state.selectedContract,
            ),
          );
        }

        return SingleChildScrollView(
          key: const Key('__paymentMethodPopup__'),
          child: Container(
            padding: const EdgeInsets.only(top: KoyalPadding.paddingLarge, bottom: KoyalPadding.paddingXSmall),
            decoration: BoxDecoration(
              color: ColorTheme.of(context).backgroundColor,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Column(
              children: [
                KoyalPadding.normalAll(
                  top: false,
                  left: false,
                  right: false,
                  child: Column(
                    children: [
                      Container(
                        width: 40,
                        height: 8,
                        decoration: BoxDecoration(
                          color: ColorTheme.of(context).foreground5Color,
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                      SizedBox(
                        height: 368,
                        child: ListView.builder(
                          key: const Key('__paymentMethodList__'),
                          shrinkWrap: true,
                          itemCount: listItems.length,
                          itemBuilder: (context, index) => listItems.elementAt(index),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Widget> _getPaymentSection({
    required List<RepaymentUserPaymentMethod> paymentMethods,
    RepaymentUserPaymentMethod? selectedPaymentMethod,
    bool isShowSectionDivider = false,
    AdaFromFlow? flow,
    AdaContract? contract,
  }) {
    if (paymentMethods.isEmpty) {
      return [];
    }

    // Payment methods
    final listItems = <Widget>[];
    final methods = paymentMethods.mapWithIndex((index, method) {
      return KoyalPadding.normalHorizontal(
        child: RepaymentAdaPaymentMethodListItem(
          key: Key('__paymentMethodItem-${method.gmaId}__'),
          method: method,
          title: method.title ?? '',
          description: method.description ?? '',
          badge: LoanUtils.isShowAdBadge(contract: contract, paymentMethod: method) ? method.autoDebitBadge ?? '' : '',
          onTap: () async {
            if (selectedPaymentMethod != method) {
              _bloc.add(
                RepaymentAdaPaymentMethodEvent.selectPaymentMethod(method),
              );
            }
            if (flow == AdaFromFlow.loanJourney) {
              context.get<CappRepaymentTrackingService>().trackLoanOriginationAdaPaymentMethodClick();
            } else {
              if (method.gmaId == RepaymentAdaPaymentMethod.onepay.getId()) {
                context.get<CappRepaymentTrackingService>().trackAdaMainClickOnepayPayMethod();
              } else if (method.gmaId == RepaymentAdaPaymentMethod.zalo.getId()) {
                context.get<CappRepaymentTrackingService>().trackAdaMainClickZaloPayMethod();
              }
            }

            context.navigator.pop(method);
          },
          iconUrl: method.iconUrl ?? '',
          selected: method == selectedPaymentMethod,
        ),
      );
    }).toList();

    // Add header
    final header = L10nCappRepayment.of(context).repaymentPaymentMethods;

    listItems
      ..add(
        SectionHeading(
          key: const Key('__paymentMethodPopupHeader__'),
          title: header,
        ),
      )
      // Add payment methods
      ..addAll(methods);

    // Add section divider
    if (isShowSectionDivider) {
      listItems.add(const SectionDivider());
    }

    return listItems;
  }
}
