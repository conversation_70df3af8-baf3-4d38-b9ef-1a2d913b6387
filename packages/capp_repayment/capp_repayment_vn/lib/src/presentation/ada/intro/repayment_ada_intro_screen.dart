import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_repayment.dart';

class RepaymentAdaIntroScreen extends StatefulWidget with RouteWrapper {
  final RepaymentAdaIntroRouteArgs? arguments;

  const RepaymentAdaIntroScreen({
    super.key,
    this.arguments,
  });

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentAdaIntroBloc>()
          ..add(
            RepaymentAdaIntroEvent.initialize(
              initialContractNumber: arguments?.contractNumber,
              contractType: arguments?.contractType,
              ddmCode: arguments?.ddmCode,
              flow: arguments?.flow,
              flowInstanceId: arguments?.flowInstanceId,
              savedPaymentMethodId: arguments?.savedPaymentMethodId,
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentAdaIntroScreen> createState() => _RepaymentAdaIntroScreenState();
}

class _RepaymentAdaIntroScreenState extends State<RepaymentAdaIntroScreen> {
  @override
  void initState() {
    super.initState();
    if (widget.arguments?.flow == AdaFromFlow.loanJourney) {
      context.get<CappRepaymentTrackingService>()
        ..trackLoanOriginationAdaIntroScreenView()
        ..trackLoanOriginationAdaIntroView();
    }
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__adaIntroScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__adaIntroAppBar__'),
        title: L10nCappRepayment.of(context).introduction,
        leading: KoyalAppBarLeading.close,
        onClose: () => context.navigator.pop(),
      ),
      body: BlocBuilder<RepaymentAdaIntroBloc, RepaymentAdaIntroState>(
        builder: (context, state) {
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MainHeading(
                        title: L10nCappRepayment.of(context).repaymentAdaIntroEasyToRepay,
                        avatar: 'assets/icons/ic_calendar.png'.asImage(
                          fit: BoxFit.contain,
                          imagePackage: 'capp_repayment',
                        ),
                      ),
                      const SectionDivider(),
                      const SizedBox(
                        height: 16,
                      ),
                      KoyalPadding.normalHorizontal(
                        child: KoyalText.subtitle1(
                          L10nCappRepayment.of(context).introduction,
                          textAlign: TextAlign.left,
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      IconedInfoListItem(
                        title: L10nCappRepayment.of(context).repaymentAdaIntroFirstTitle,
                        bodyText: L10nCappRepayment.of(context).repaymentAdaIntroFirstSubtitle,
                        icon: 'assets/icons/ic_card_4_digits.svg'.asImage(
                          imagePackage: 'capp_repayment',
                        ),
                      ),
                      IconedInfoListItem(
                        title: L10nCappRepayment.of(context).repaymentAdaIntroSecondTitle,
                        bodyText: L10nCappRepayment.of(context).repaymentAdaIntroSecondSubtitle,
                        icon: 'assets/icons/ic_verified_calendar.svg'.asImage(
                          imagePackage: 'capp_repayment',
                        ),
                      ),
                      IconedInfoListItem(
                        title: L10nCappRepayment.of(context).repaymentAdaIntroThirdTitle,
                        bodyText: L10nCappRepayment.of(context).repaymentAdaIntroThirdSubtitle,
                        icon: 'assets/icons/ic_cash_loan.svg'.asImage(
                          imagePackage: 'capp_repayment',
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                    ],
                  ),
                ),
              ),
              VerticalButtonsLayout(
                primaryButton: PrimaryButton(
                  key: const Key('__adaIntroContinueButton__'),
                  text: L10nCappRepayment.of(context).continueWord,
                  onPressed: () async {
                    final flow = state.flow;
                    if (flow == AdaFromFlow.loanJourney) {
                      context.get<CappRepaymentTrackingService>().trackLoanOriginationAdaIntroContinueClick();
                      await context.navigator.pushTyped(
                        package: CappRepayment,
                        screen: LooAdaMainScreen,
                        arguments: LooAdaMainRouteArgs(
                          contractType: state.contractType ?? '',
                          flowInstanceId: state.flowInstanceId ?? '',
                          ddmCode: state.ddmCode,
                          savedPaymentMethodId: state.savedPaymentMethodId ?? '',
                          flow: state.flow,
                        ),
                      );
                    } else {
                      context.get<CappRepaymentTrackingService>().trackAdaIntroClickContinue();
                      await context.navigator.pushTyped(
                        package: CappRepayment,
                        screen: RepaymentAdaMainScreen,
                        arguments: RepaymentAdaMainRouteArgs(
                          contractNumber: state.initialContractNumber,
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
