import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentAdaAdditionalInfoPopup extends StatelessWidget {
  final Function()? onTapDetail;
  final String? title;
  final String? description;

  const RepaymentAdaAdditionalInfoPopup({
    Key? key,
    this.title,
    this.description,
    this.onTapDetail,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      key: const Key('__consentPopup__'),
      child: Container(
        decoration: BoxDecoration(
          color: ColorTheme.of(context).backgroundColor,
          borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                const SizedBox(height: KoyalPadding.paddingLarge),
                Container(
                  width: 40,
                  height: 8,
                  decoration: BoxDecoration(
                    color: HciColors.black85,
                    borderRadius: BorderRadius.circular(100.0),
                  ),
                ),
                SectionHeading(
                  title: title ?? '',
                ),
                KoyalPadding.normalHorizontal(
                  child: KoyalText.body2(
                    description ?? '',
                    color: ColorTheme.of(context).secondaryTextColor,
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
            TertiaryButton(
              text: L10nCappRepayment.of(context).detail,
              onPressed: () {
                onTapDetail?.call();
              },
            ),
            const SizedBox(
              height: KoyalPadding.paddingLarge,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> onPressedOk(BuildContext context) async {}
}
