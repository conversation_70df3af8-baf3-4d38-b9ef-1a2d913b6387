import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart' hide ChipTheme;

import '../../../../../capp_repayment.dart';

class RepaymentAdaPaymentMethodMain extends StatelessWidget {
  final RepaymentUserPaymentMethod? paymentMethod;
  final AdaContract? contract;
  final AdaEnrolledContract? enrolledContract;
  final bool isEnable;
  final Function(
    RepaymentUserPaymentMethod? paymentMethod,
  )? onTap;

  const RepaymentAdaPaymentMethodMain({
    Key? key,
    required this.paymentMethod,
    this.contract,
    this.enrolledContract,
    this.onTap,
    required this.isEnable,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var title = '';
    var badge = '';
    var imagePath = '';

    title = paymentMethod?.title ?? '';
    badge = paymentMethod?.autoDebitBadge ?? '';
    final isShowBadge =
        LoanUtils.isShowAdBadge(contract: contract, enrolledContract: enrolledContract, paymentMethod: paymentMethod);

    imagePath = paymentMethod?.iconUrl ?? '';

    return KoyalPadding.normalAll(
      top: false,
      child: SingleSelectableContainer(
        value: false,
        title: title,
        disabled: !isEnable,
        showRadioIcon: false,
        simpleTag: isShowBadge && badge.isNotEmpty
            ? SimpleChip(
                label: badge,
                chipTheme: ChipTheme.success(),
              )
            : null,
        onChanged: (_) {
          onTap?.call(
            paymentMethod,
          );
        },
        avatar: SizedBox(
          width: 32,
          height: 32,
          child: imagePath.isEmpty
              ? Center(
                  child: Icon(
                    size: 28,
                    KoyalIcons.information_circle_outline,
                    color: ColorTheme.of(context).backgroundColor,
                  ),
                )
              : imagePath.asImage(
                  fit: BoxFit.fill,
                  showShimmer: true,
                  height: 32,
                  width: 32,
                  imagePackage: 'capp_repayment',
                ),
        ),
      ),
    );
  }
}
