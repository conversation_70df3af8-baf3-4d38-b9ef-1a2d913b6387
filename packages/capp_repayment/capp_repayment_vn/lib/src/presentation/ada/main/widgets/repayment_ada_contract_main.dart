import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../../capp_repayment.dart';
import '../../management/widgets/repayment_ada_product_avatar.dart';

class RepaymentAdaContractMain extends StatelessWidget with RepaymentAdaMixin {
  final AdaContract contract;
  final bool isEnable;
  final Function(AdaContract)? onTap;

  const RepaymentAdaContractMain({
    Key? key,
    required this.contract,
    this.onTap,
    required this.isEnable,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var productName = '';
    var productImageUrl = '';
    var productType = '';

    var contractNumber = '';

    if (contract.contractType == RepaymentContractType.cel) {
      contractNumber = '${L10nCappRepayment.of(context).repaymentContractNumber} ${contract.contractNumber ?? ''}';
      final con = contract.celContract;
      productType = RepaymentGmaProductType.cel.id;
      productImageUrl = con?.productImageUrl ?? '';
      productName = con?.productName ?? '';
      if (productName.isEmpty) {
        productName = LoanUtils.getCelProductNameFromLoanType(
          loanType: con?.loanType,
          repaymentLocalization: L10nCappRepayment.of(context),
        );
      }
    } else {
      contractNumber = '${L10nCappRepayment.of(context).repaymentContractNumber} ${contract.contractNumber ?? ''}';
      final con = contract.relContract;
      productType = con?.gmaProductType ?? '';
      if (con != null) {
        productName = productType == RepaymentGmaProductType.bnpl.id
            ? LoanUtils.getHomePayLaterContractName(context)
            : L10nCappRepayment.of(context).repaymentProductCreditCard;
        productImageUrl = '';
      }
    }
    return KoyalPadding.normalAll(
      top: false,
      child: SingleSelectableContainer(
        onChanged: (_) {
          if (isEnable) {
            onTap?.call(contract);
          }
        },
        title: productName,
        bodyText: isEnable ? contractNumber : '',
        value: false,
        showRadioIcon: false,
        avatar: RepaymentAdaProductAvatar(
          productImageUrl: productImageUrl,
          loanType: contract.celContract?.loanType,
          productType: productType,
        ),
      ),
    );
  }
}
