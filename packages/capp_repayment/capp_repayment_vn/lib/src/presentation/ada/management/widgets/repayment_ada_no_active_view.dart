import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentAdaNoActiveView extends StatelessWidget {
  const RepaymentAdaNoActiveView({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        'assets/icons/ic_illu_no_search_results.svg'.asImage(
          fit: BoxFit.fitWidth,
          width: 120,
          imagePackage: 'capp_repayment',
        ),
        KoyalPadding.smallHorizontal(
          child: MainHeading(
            title: L10nCappRepayment.of(context).repaymentAdaNoActiveAdaTitle,
            subtitle: L10nCappRepayment.of(context).repaymentAdaNoActiveAdaSubtitle,
          ),
        ),
      ],
    );
  }
}
