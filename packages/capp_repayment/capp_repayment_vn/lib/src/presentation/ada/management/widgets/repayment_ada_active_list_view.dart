import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../../../../capp_repayment.dart';

class RepaymentAdaActiveListView extends StatelessWidget {
  final List<AdaEnrolledContract> enrolledAdaContracts;
  final List<RepaymentUserPaymentMethod> paymentMethods;
  final Function(AdaEnrolledContract)? onTap;

  const RepaymentAdaActiveListView({
    super.key,
    required this.enrolledAdaContracts,
    this.onTap,
    required this.paymentMethods,
  });

  @override
  Widget build(BuildContext context) {
    final items = _buildContractListItem(context, enrolledAdaContracts);
    return ListView.builder(
      shrinkWrap: true,
      itemCount: items.length,
      itemBuilder: (context, index) => items[index],
    );
  }

  List<Widget> _buildContractListItem(
    BuildContext context,
    List<AdaEnrolledContract> enrolledAdaContracts,
  ) {
    final contractWidgets = enrolledAdaContracts
        .map(
          (contract) {
            final paymentMethod = paymentMethods.firstWhereOrNull((e) => e.gmaId == contract.paymentPartner);

            return paymentMethod != null
                ? RepaymentAdaItem(
                    key: Key('__adaManagementAdaListItem-${contract.id}__'),
                    contract: contract,
                    paymentMethod: paymentMethod,
                    onTap: onTap,
                  )
                : null;
          },
        )
        .whereNotNull()
        .toList();

    return contractWidgets;
  }
}
