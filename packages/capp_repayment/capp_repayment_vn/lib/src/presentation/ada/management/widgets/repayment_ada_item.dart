import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../../capp_repayment.dart';
import 'repayment_ada_product_avatar.dart';

class RepaymentAdaItem extends StatelessWidget {
  final AdaEnrolledContract contract;
  final RepaymentUserPaymentMethod paymentMethod;
  final Function(AdaEnrolledContract)? onTap;

  const RepaymentAdaItem({super.key, required this.contract, this.onTap, required this.paymentMethod});

  @override
  Widget build(BuildContext context) {
    var productName = '';
    var productImageUrl = '';
    final gmaProductType = contract.productType;
    if (gmaProductType == RepaymentGmaProductType.cel.id) {
      productImageUrl = contract.productImageUrl ?? '';
      productName = contract.productName ?? '';
      if (productName.isEmpty) {
        productName = LoanUtils.getCelProductNameFromLoanType(
          loanType: contract.loanType,
          repaymentLocalization: L10nCappRepayment.of(context),
        );
      }
    } else if (gmaProductType == RepaymentGmaProductType.bnpl.id) {
      productName = LoanUtils.getHomePayLaterContractName(context);
    } else if (gmaProductType == RepaymentGmaProductType.cc.id) {
      productName = L10nCappRepayment.of(context).repaymentProductCreditCard;
    }

    final paymentMethodName = paymentMethod.title ?? '';
    return InkWell(
      onTap: () {
        onTap?.call(contract);
      },
      child: KoyalPadding.normalAll(
        child: Row(
          children: [
            RepaymentAdaProductAvatar(
              productImageUrl: productImageUrl,
              loanType: contract.loanType,
              productType: gmaProductType ?? '',
            ),
            const SizedBox(
              width: 16,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  KoyalText.body1(productName),
                  const SizedBox(
                    height: 4,
                  ),
                  KoyalText.caption2(
                    '${L10nCappRepayment.of(context).repaymentPaymentVia} $paymentMethodName',
                    color: ColorTheme.of(context).secondaryTextColor,
                  ),
                ],
              ),
            ),
            const SizedBox(
              width: 8,
            ),
            Icon(
              KoyalIcons.chevron_forward_outline,
              color: ColorTheme.of(context).secondaryTextColor,
            ),
          ],
        ),
      ),
    );
  }
}
