import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class RepaymentAdaProductAvatar extends StatelessWidget {
  final String productImageUrl;
  final String productType;
  final String? loanType;
  final double? avatarSize;
  const RepaymentAdaProductAvatar({
    super.key,
    required this.productImageUrl,
    required this.productType,
    this.avatarSize,
    this.loanType,
  });

  @override
  Widget build(BuildContext context) {
    var imageExtension = '';
    if (productImageUrl.isNotEmpty) {
      final parts = productImageUrl.split('.');
      if (parts.length > 1) {
        imageExtension = parts[parts.length - 1].toLowerCase();
      }
    }
    var imagePath = '';
    if (productType == RepaymentGmaProductType.cel.id) {
      if (loanType?.toLowerCase() == RepaymentLoanType.cash.id) {
        imagePath = 'assets/icons/ic_personal_loan_new.png';
      } else {
        imagePath = 'assets/icons/ic_generic_commodity_new.png';
      }
    } else if (productType == RepaymentGmaProductType.cc.id) {
      imagePath = 'assets/icons/ic_credit_card_black.png';
    } else if (productType == RepaymentGmaProductType.bnpl.id) {
      imagePath = 'assets/icons/ic_bnpl_new.png';
    }

    final size = avatarSize ?? 32.0;
    return productImageUrl.isNotEmpty
        ? imageExtension == 'svg'
            ? SvgPicture.network(
                productImageUrl,
                width: size,
                placeholderBuilder: (context) {
                  return Image.asset(
                    'assets/images/no_picture.jpg',
                    fit: BoxFit.fill,
                    width: size,
                    height: size,
                  );
                },
              )
            : Image.network(
                productImageUrl,
                errorBuilder: (context, exception, stackTrace) {
                  return Image.asset(
                    'assets/images/no_picture.jpg',
                    fit: BoxFit.fill,
                    width: size,
                    height: size,
                  );
                },
                fit: BoxFit.fitWidth,
                width: size,
              )
        : imagePath.asImage(
            fit: BoxFit.fill,
            height: size,
            width: size,
            imagePackage: imagePath == 'assets/images/no_picture.jpg' ? null : 'capp_repayment',
          );
  }
}
