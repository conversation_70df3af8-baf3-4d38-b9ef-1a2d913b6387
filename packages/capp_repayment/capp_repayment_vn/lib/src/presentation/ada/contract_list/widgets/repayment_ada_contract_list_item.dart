import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../capp_repayment.dart';

class RepaymentAdaContractListItem extends StatelessWidget {
  static const dueDateWidth = 128.0;
  final AdaContract contract;
  final String contractNumber;
  final String? productImageUrl;
  final String productName;
  final DateTime? dueDate;
  final int? dpd;
  final Decimal dueAmount;
  final Function(AdaContract)? onSelectContract;
  final bool selected;
  final bool isPayable;

  const RepaymentAdaContractListItem({
    Key? key,
    required this.contract,
    this.onSelectContract,
    required this.contractNumber,
    this.productImageUrl,
    required this.productName,
    required this.dueDate,
    this.dpd,
    required this.dueAmount,
    required this.selected,
    required this.isPayable,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var imageExtension = '';
    if (null != productImageUrl && productImageUrl!.isNotEmpty) {
      final parts = productImageUrl!.split('.');
      if (parts.length > 1) {
        imageExtension = parts[parts.length - 1].toLowerCase();
      }
    }
    var imagePath = '';
    if (contract.contractType == RepaymentContractType.cel) {
      if (contract.celContract?.loanType?.toLowerCase() == RepaymentLoanType.cash.id) {
        imagePath = 'assets/icons/ic_personal_loan_new.png';
      } else {
        imagePath = 'assets/icons/ic_generic_commodity_new.png';
      }
    } else if (contract.contractType == RepaymentContractType.rel) {
      if (contract.relContract?.gmaProductType == RepaymentGmaProductType.bnpl.id) {
        imagePath = 'assets/icons/ic_bnpl_new.png';
      } else {
        imagePath = 'assets/icons/ic_credit_card_black.png';
      }
    } else {
      imagePath = 'assets/images/no_picture.jpg';
    }

    return RepaymentSelectableNewContainer(
      title: productName,
      paymentStatus: (dpd ?? 0) > 0 ? RepaymentPaymentStatus.overdue : null,
      avatar: Container(
        width: 56,
        height: 56,
        padding: const EdgeInsets.all(8),
        child: null != productImageUrl && productImageUrl!.isNotEmpty
            ? imageExtension == 'svg'
                ? SvgPicture.network(
                    productImageUrl!,
                    width: 40,
                    placeholderBuilder: (context) {
                      return Image.asset(
                        'assets/images/no_picture.jpg',
                        fit: BoxFit.fill,
                        width: 40,
                        height: 40,
                      );
                    },
                  )
                : Image.network(
                    productImageUrl!,
                    errorBuilder: (context, exception, stackTrace) {
                      return Image.asset(
                        'assets/images/no_picture.jpg',
                        fit: BoxFit.fill,
                        width: 40,
                        height: 40,
                      );
                    },
                    fit: BoxFit.fitWidth,
                    width: 40,
                  )
            : imagePath.asImage(
                fit: BoxFit.fill,
                height: 40,
                width: 40,
                imagePackage: imagePath == 'assets/images/no_picture.jpg' ? null : 'capp_repayment',
              ),
      ),
      onTap: () {
        if (null != onSelectContract) {
          onSelectContract!(contract);
        }
      },
      isSelected: selected,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Flexible(
                child: KoyalText.body2(
                  '${L10nCappRepayment.of(context).repaymentContractNumber} $contractNumber',
                  color: ColorTheme.of(context).defaultTextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
