import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../capp_repayment.dart' as capp_repayment;
import '../../../../capp_repayment.dart';

class RepaymentAdaContractListPopup extends StatefulWidget {
  final RepaymentAdaContractListArgs arguments;
  final Function(AdaContract selectedContract)? onSelectContract;
  const RepaymentAdaContractListPopup({
    Key? key,
    required this.arguments,
    this.onSelectContract,
  }) : super(key: key);

  @override
  RepaymentAdaContractListPopupState createState() => RepaymentAdaContractListPopupState();
}

class RepaymentAdaContractListPopupState extends State<RepaymentAdaContractListPopup> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => context.get<capp_repayment.RepaymentAdaContractListBloc>()
        ..add(
          capp_repayment.RepaymentAdaContractListEvent.initialize(
            contracts: widget.arguments.contracts,
            selectedContract: widget.arguments.selectedContract,
          ),
        ),
      child: BlocConsumer<capp_repayment.RepaymentAdaContractListBloc, capp_repayment.RepaymentAdaContractListState>(
        listener: (context, state) {
          if (state.isNeedToDismiss) {
            final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
            navigationHistoryObserver.top?.navigator?.pop();
          }
        },
        builder: (context, state) {
          final contracts = state.contracts;
          final selectedContract = state.selectedContract;
          final items = _buildContractListItem(context, contracts, selectedContract);

          final isLoading = state.loadingState == LoadingState.isLoading;

          return Container(
            height: MediaQuery.of(context).size.height - 54,
            padding: const EdgeInsets.only(top: 20, bottom: 3),
            decoration: BoxDecoration(
              color: ColorTheme.of(context).backgroundColor,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 8,
                  decoration: BoxDecoration(
                    color: ColorTheme.of(context).foreground5Color,
                    borderRadius: BorderRadius.circular(100.0),
                  ),
                ),
                const SizedBox(
                  height: 6,
                ),
                Container(
                  key: const Key('__contractListPopupTitle__'),
                  padding: const EdgeInsets.all(KoyalPadding.paddingNormal),
                  width: double.infinity,
                  child: KoyalText.subtitle1(
                    L10nCappRepayment.of(context).repaymentSelectLoan,
                    color: ColorTheme.of(context).defaultTextColor,
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    key: const Key('__contractListPopup__'),
                    child: Column(
                      children: [
                        KoyalPadding.normalAll(
                          top: false,
                          left: false,
                          right: false,
                          child: Column(
                            children: [
                              if (isLoading == true)
                                const KoyalProgressIndicator.large()
                              else
                                ListView.separated(
                                  key: const Key('__adaContractListPopupContractList__'),
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  separatorBuilder: (context, index) => const SizedBox(
                                    height: 16,
                                  ),
                                  itemCount: items.length,
                                  itemBuilder: (context, index) => items.elementAt(index),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildContractListItem(
    BuildContext context,
    List<AdaContract> contracts,
    AdaContract? selectedContract,
  ) {
    if (contracts.isEmpty) {
      return [];
    }

    final selectedContractNumber = selectedContract?.contractNumber;

    // Contracts
    final contractWidgets = contracts.mapWithIndex((index, contract) {
      String? contractNum;
      DateTime? dueDate;
      String? productName;
      String? productImageUrl;
      Decimal? dueAmount;
      int? dpd;
      const isPayable = true;
      if (contract.contractType == RepaymentContractType.cel) {
        final con = contract.celContract;
        contractNum = con?.contractNumber;
        dueDate = con?.dueDate;
        productImageUrl = con?.productImageUrl;
        productName = con?.productName ?? '';
        if (productName.isEmpty) {
          productName = LoanUtils.getCelProductNameFromLoanType(
            loanType: con?.loanType,
            repaymentLocalization: L10nCappRepayment.of(context),
          );
        }
        dueAmount = con?.dueAmount;
      } else {
        final con = contract.relContract;
        final gmaProductType = con?.gmaProductType;
        dpd = con?.dpd;
        if (con != null) {
          contractNum = con.contractNumber;
          dueDate = con.dueDate;
          productName = gmaProductType == RepaymentGmaProductType.bnpl.id
              ? LoanUtils.getHomePayLaterContractName(context)
              : L10nCappRepayment.of(context).repaymentProductCreditCard;
          productImageUrl = '';

          // due amount is always minimumDueAmount for both CC and BNPL
          dueAmount = con.minimumDueAmount ?? Decimal.zero;
        }
      }
      return KoyalPadding.normalHorizontal(
        child: capp_repayment.RepaymentAdaContractListItem(
          key: Key('__adaContractListPopupContractListItem-${contractNum}__'),
          contract: contract,
          contractNumber: contractNum ?? '',
          productName: productName ?? '',
          dueDate: dueDate,
          dpd: dpd,
          productImageUrl: productImageUrl,
          dueAmount: dueAmount ?? Decimal.zero,
          selected: selectedContract != null && selectedContractNumber == contractNum,
          onSelectContract: (selectedContract) async {
            context.navigator.pop();
            if (selectedContractNumber != contractNum) {
              widget.onSelectContract?.call(selectedContract);
            }
          },
          isPayable: isPayable,
        ),
      );
    }).toList();

    return contractWidgets;
  }
}
