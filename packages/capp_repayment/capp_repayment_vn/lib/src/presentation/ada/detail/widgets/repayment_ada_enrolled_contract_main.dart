import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../../capp_repayment.dart';
import '../../management/widgets/repayment_ada_product_avatar.dart';

class RepaymentAdaEnrolledContractMain extends StatelessWidget with RepaymentAdaMixin {
  final AdaEnrolledContract contract;
  final bool isEnable;

  const RepaymentAdaEnrolledContractMain({
    Key? key,
    required this.contract,
    required this.isEnable,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var productName = '';
    var productImageUrl = '';

    final contractNumber = '${L10nCappRepayment.of(context).repaymentContractNumber} ${contract.contractNumber ?? ''}';
    final productType = contract.productType;
    if (productType == RepaymentGmaProductType.cel.id) {
      productImageUrl = contract.productImageUrl ?? '';
      productName = contract.productName ?? '';
      if (productName.isEmpty) {
        productName = LoanUtils.getCelProductNameFromLoanType(
          loanType: contract.loanType,
          repaymentLocalization: L10nCappRepayment.of(context),
        );
      }
    } else {
      productName = productType == RepaymentGmaProductType.bnpl.id
          ? LoanUtils.getHomePayLaterContractName(context)
          : L10nCappRepayment.of(context).repaymentProductCreditCard;
      productImageUrl = '';
    }

    return KoyalPadding.normalAll(
      top: false,
      child: SingleSelectableContainer(
        title: productName,
        bodyText: contractNumber,
        value: false,
        showRadioIcon: false,
        onChanged: (value) => {},
        avatar: RepaymentAdaProductAvatar(
          productImageUrl: productImageUrl,
          loanType: contract.loanType,
          productType: productType ?? '',
        ),
      ),
    );
  }
}
