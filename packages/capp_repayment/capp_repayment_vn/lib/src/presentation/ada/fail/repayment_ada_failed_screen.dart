import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../capp_repayment.dart';

class RepaymentAdaFailedScreen extends StatefulWidget {
  final RepaymentAdaFailedRouteArgs arguments;

  const RepaymentAdaFailedScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  State<RepaymentAdaFailedScreen> createState() => _RepaymentAdaFailedScreenState();
}

class _RepaymentAdaFailedScreenState extends State<RepaymentAdaFailedScreen> {
  @override
  Widget build(BuildContext context) {
    final contract = widget.arguments.contract;
    final draftContract = widget.arguments.draftContract;
    final contractType = contract?.contractType ?? draftContract?.contractType;

    var productName = '';
    var desc = '';

    if(contract != null) {
      if (contractType == RepaymentContractType.rel) {
        productName = LoanUtils.getRelProductNameFromProductType(
          context: context,
          gmaProductType: contract.relContract?.gmaProductType ?? '',
        );
        desc = LoanUtils.getRelRegisterAdaFailDescFromProductType(
          context: context,
          gmaProductType: contract.relContract?.gmaProductType ?? '',
        );
      } else if (contractType == RepaymentContractType.cel) {
        productName = contract.celContract?.productName ?? '';
        if (productName.isEmpty) {
          productName = LoanUtils.getCelProductNameFromLoanType(
            loanType: contract.celContract?.loanType,
            repaymentLocalization: L10nCappRepayment.of(context),
          );
        }
        desc = L10nCappRepayment.of(context).repaymentAdaCelFailedRegisterDesc;
      }
    } else {
      // Draft contract
      if (contractType == RepaymentContractType.rel) {
        desc = LoanUtils.getRelRegisterAdaFailDescFromProductType(
          context: context,
          gmaProductType: '',
        );
      } else if (contractType == RepaymentContractType.cel) {
        desc = L10nCappRepayment.of(context).repaymentAdaCelFailedRegisterDesc;
      }
    }

    return KoyalWillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        appBar: KoyalAppBar(
          leading: KoyalAppBarLeading.close,
          onClose: () => _onBack(isBackToHome: false),
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // Header section
                    MainHeading(
                      title: L10nCappRepayment.of(context).repaymentAdaFailedRegister,
                      subtitle: desc,
                      avatar: 'assets/icons/ic_fail.png'.asImage(
                        imagePackage: 'capp_repayment',
                      ),
                    ),
                    const SectionDivider(),
                    // Summary section
                    _transactionSection(context: context, productName: productName),
                  ],
                ),
              ),
            ),
            VerticalButtonsLayout(
              primaryButton: PrimaryButton(
                text: L10nCappRepayment.of(context).transactionFailedTryAgain,
                onPressed: () {
                  context.get<CappRepaymentTrackingService>().trackAdaFailClickTryAgain();
                  _onBack(isBackToHome: false);
                },
              ),
              tertiaryButton: TertiaryButton(
                text: L10nCappRepayment.of(context).repaymentBackLater,
                onPressed: () {
                  context.get<CappRepaymentTrackingService>().trackAdaFailClickLater();
                  _onBack(isBackToHome: true);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _transactionSection({required BuildContext context, required String productName}) {
    final paymentMethod = widget.arguments.paymentMethod;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeading(title: L10nCappRepayment.of(context).repaymentAda),
        if (productName.isNotEmpty) ...[
          ExtendedInfoListItem(
            title: L10nCappRepayment.of(context).repaymentAdaContractType,
            bodyText: productName,
          ),
          const ListDivider(),
        ],
        ExtendedInfoListItem(
          title: L10nCappRepayment.of(context).repaymentAdaPaymentMethod,
          bodyText: paymentMethod.title ?? '',
        ),
      ],
    );
  }

  void _onBack({required bool isBackToHome}) {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final navigationHistory = navigationHistoryObserver.history.map((h) => h.settings.name).toList();
    if(navigationHistory.contains('loo_ada_main_screen')) {
      context.navigator.popUntilFromPackage('CappRepayment', 'LooAdaMainScreen');
    } else {
      if(isBackToHome) {
        context.navigator.toMainScreen();
      } else {
        context.navigator.pop();
      }
    }
  }
}
