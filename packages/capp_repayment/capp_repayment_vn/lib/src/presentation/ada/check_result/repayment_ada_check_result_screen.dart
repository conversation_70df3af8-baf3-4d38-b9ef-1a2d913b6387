import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_repayment.dart';

class RepaymentAdaCheckResultScreen extends StatefulWidget with RouteWrapper {
  final ScreenArguments arguments;
  const RepaymentAdaCheckResultScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentAdaCheckResultBloc>()
          ..add(
            RepaymentAdaCheckResultEvent.initialize(
              deeplink: arguments.map!['deeplink']?.toString() ?? '',
              previousRouteName: arguments.map!['previousRouteName']?.toString() ?? '',
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentAdaCheckResultScreen> createState() => _RepaymentAdaCheckResultScreenState();
}

class _RepaymentAdaCheckResultScreenState extends State<RepaymentAdaCheckResultScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepaymentAdaCheckResultBloc, RepaymentAdaCheckResultState>(
      listenWhen: (p, c) => p.status != c.status && c.loadingState == LoadingState.isCompleted,
      listener: (ctx, state) {
        final adRegistrationStatus = state.status;
        final contract = state.contract;
        final draftContract = state.draftContract;
        final paymentMethod = state.paymentMethod;

        if ((contract != null || draftContract != null) && paymentMethod != null) {
          if (adRegistrationStatus == AdaRegistrationStatus.fail) {
            VnNavigationUtils.navigateToRepaymentAdaFailedScreen(
              context: context,
              contract: contract,
              draftContract: draftContract,
              paymentMethod: paymentMethod,
              isReplace: true,
            );
          } else if (adRegistrationStatus == AdaRegistrationStatus.success) {
            VnNavigationUtils.navigateToRepaymentAdaSuccessScreen(
              context: context,
              contract: contract,
              draftContract: draftContract,
              paymentMethod: paymentMethod,
              isReplace: true,
            );
          }
        }
        // In case cache is lost, pop check result screen
        else {
          context.navigator.pop();
        }
      },
      builder: (context, state) {
        return KoyalWillPopScope(
          onWillPop: () async => false,
          child: KoyalScaffold(
            body: Container(
              color: Colors.transparent,
              width: double.infinity,
              height: double.infinity,
              child: Center(
                child: state.loadingState == LoadingState.isLoading ? const KoyalProgressIndicator.large() : Container(),
              ),
            ),
          ),
        );
      },
    );
  }
}
