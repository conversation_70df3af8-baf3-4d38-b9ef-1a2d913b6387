import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../../../capp_repayment.dart';

class RepaymentAdaSuccessScreen extends StatefulWidget {
  final RepaymentAdaSuccessRouteArgs arguments;

  const RepaymentAdaSuccessScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  State<RepaymentAdaSuccessScreen> createState() => _RepaymentAdaSuccessScreenState();
}

class _RepaymentAdaSuccessScreenState extends State<RepaymentAdaSuccessScreen> {
  @override
  Widget build(BuildContext context) {
    return KoyalWillPopScope(
      onWillPop: () async => false,
      child: KoyalScaffold(
        appBar: KoyalAppBar(
          leading: KoyalAppBarLeading.close,
          onClose: _onBack,
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    MainHeading(
                      title: L10nCappRepayment.of(context).repaymentAdaEnrollSuccess,
                      subtitle: L10nCappRepayment.of(context).repaymentAdaEnrollSuccessDescription,
                      avatar: 'assets/icons/ic_approve.png'.asImage(
                        imagePackage: 'capp_repayment_core',
                      ),
                    ),
                    const SectionDivider(),
                    _transactionSection(context),
                  ],
                ),
              ),
            ),
            VerticalButtonsLayout(
              primaryButton: PrimaryButton(
                text: L10nCappRepayment.of(context).repaymentDone,
                onPressed: () {
                  context.get<CappRepaymentTrackingService>().trackAdaSuccessClickDone();
                  _onBack();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _transactionSection(BuildContext context) {
    var productName = '';
    var amountDisplay = '';
    var dueDay = 0;
    var repaymentDateTitle = '';
    var dueDateDisplay = '';
    var dueAmount = Decimal.zero;

    final paymentMethod = widget.arguments.paymentMethod;
    final contractType = getContractType();
    if (widget.arguments.contract != null) {
      final contract = widget.arguments.contract!;
      // ADA contract
      if (contractType == RepaymentContractType.rel) {
        productName = LoanUtils.getRelProductNameFromProductType(
          context: context,
          gmaProductType: contract.relContract?.gmaProductType ?? '',
        );
        amountDisplay = L10nCappRepayment.of(context).repaymentMinimumDueAmount;
        repaymentDateTitle = L10nCappRepayment.of(context).repaymentAdaDueDateRel;
        dueDateDisplay = L10nCappRepayment.of(context).repaymentAdaMainAutoDeductDateRelDesc;
      } else if (contractType == RepaymentContractType.cel) {
        dueAmount = contract.celContract?.dueAmount ?? Decimal.zero;
        productName = contract.celContract?.productName ?? '';
        if (productName.isEmpty) {
          productName = LoanUtils.getCelProductNameFromLoanType(
            loanType: contract.celContract?.loanType,
            repaymentLocalization: L10nCappRepayment.of(context),
          );
        }
        amountDisplay = dueAmount == Decimal.zero ? '' : dueAmount.formatCurrency();
        repaymentDateTitle = L10nCappRepayment.of(context).repaymentAdaDueDateCel;

        // Due day only apply for CEL
        dueDay = contract.celContract?.dueDay ?? 0;
        dueDateDisplay = dueDay.toString();

        // check ENG lang
        if (L10nCappRepayment.of(context).repaymentMonthly.contains('monthly')) {
          dueDateDisplay += DateTimeUtils.getOrdinalSuffix(dueDay);
        }
      }
    } else {
      // Draft contract
      if (widget.arguments.draftContract!.contractType == RepaymentContractType.rel) {
        amountDisplay = L10nCappRepayment.of(context).repaymentMinimumDueAmount;
        repaymentDateTitle = L10nCappRepayment.of(context).repaymentAdaDueDateRel;
      } else if (widget.arguments.draftContract!.contractType == RepaymentContractType.cel) {
        amountDisplay = L10nCappRepayment.of(context).repaymentDueAmount;
        repaymentDateTitle = L10nCappRepayment.of(context).repaymentAdaDueDateCel;
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeading(title: L10nCappRepayment.of(context).repaymentAda),
        if (productName.isNotEmpty) ...[
          ExtendedInfoListItem(
            title: L10nCappRepayment.of(context).repaymentAdaContractType,
            bodyText: productName,
          ),
          const ListDivider(),
        ],
        ExtendedInfoListItem(
          title: L10nCappRepayment.of(context).repaymentAdaPaymentMethod,
          bodyText: paymentMethod.title ?? '',
        ),
        const ListDivider(),
        if (contractType == RepaymentContractType.cel && dueDateDisplay.isNotEmpty) ...[
          ExtendedInfoListItem(
            title: L10nCappRepayment.of(context).repaymentDueDate,
            bodyText: dueDay < 1 ? '' : L10nCappRepayment.of(context).repaymentAdaMainDueDateCelDesc(dueDateDisplay),
          ),
          const ListDivider(),
        ],
        ExtendedInfoListItem(
          title: repaymentDateTitle,
          bodyText: L10nCappRepayment.of(context).repaymentAdaDueDateRelDesc,
        ),
        ExtendedInfoListItem(
          useBoldBodyText: contractType == RepaymentContractType.cel,
          title: L10nCappRepayment.of(context).repaymentAdaAmount,
          bodyText: amountDisplay,
        ),
        KoyalPadding.normalAll(
          top: false,
          bottom: false,
          child: RepaymentAlert(
            text: RichText(
              text: TextSpan(
                children: <TextSpan>[
                  TextSpan(
                    text: '${L10nCappRepayment.of(context).repaymentAdaAmountLimitWarning} ',
                    style: TextStyleTheme.of(context).caption1.copyWith(
                          color: AlertTheme.info().foregroundColor,
                        ),
                  ),
                  TextSpan(
                    text: L10nCappRepayment.of(context).detail,
                    style: TextStyleTheme.of(context).caption1.copyWith(
                          color: AlertTheme.info().foregroundColor,
                          decoration: TextDecoration.underline,
                        ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        context.get<CappRepaymentTrackingService>().trackAdaSuccessClickTncDetail();
                        NavigationUtils.openDeeplink(context: context, deeplink: Constants.adaBlogDeeplink);
                      },
                  ),
                ],
              ),
            ),
            theme: AlertTheme.info(),
          ),
        ),
      ],
    );
  }

  void _onBack() {
    final navigationHistoryObserver = context.get<NavigationHistoryObserver>();
    final navigationHistory = navigationHistoryObserver.history.map((h) => h.settings.name).toList();
    if(navigationHistory.contains('ada_processing_screen')) {
      context.navigator.popUntilFromPackage('CappLoanShared', 'AdaProcessingScreen');
    } else {
      context.navigator.toMainScreen();
    }
  }

  RepaymentContractType getContractType() {
    return widget.arguments.contract?.contractType ?? widget.arguments.draftContract!.contractType;
  }
}
