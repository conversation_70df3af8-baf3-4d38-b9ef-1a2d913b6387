import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_repayment.dart';

class AdaOnePayScreen extends StatefulWidget with RouteWrapper {
  final AdaOnePayRouteArgs arguments;

  const AdaOnePayScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  AdaOnePayScreenState createState() => AdaOnePayScreenState();

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<AdaOnePayBloc>()
          ..add(
            AdaOnePayEvent.initialize(
              arguments.redirectUrl,
            ),
          ),
        child: this,
      );
}

class AdaOnePayScreenState extends State<AdaOnePayScreen> {
  late String _returnUrl;

  @override
  void initState() {
    super.initState();
    final settings = BlocProvider.of<AdaOnePayBloc>(context).settings;
    _returnUrl = settings.onePaySettings.adRedirectUrl;

    // context.get<CappRepaymentTrackingService>().trackOnePayScreenView();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__adaOnepayScreen__'),
      resizeToAvoidBottomInset: !GmaPlatform.isIOS,
      safeAreaCustomSettings: SafeAreaCustomSettings(
        maintainBottomViewPadding: GmaPlatform.isIOS,
      ),
      appBar: KoyalAppBar(
        key: const Key('__adaOnepayAppBar__'),
        title: L10nCappRepayment.of(context).repaymentContractRepayment,
        onGoBack: () {
          if(widget.arguments.adaOnepayFlow == AdaOnepayFlow.register) {
            context.get<CappRepaymentTrackingService>().trackAdaOnepayRegisterClickBack();
          } else {
            context.get<CappRepaymentTrackingService>().trackAdaOnepayCancelOtpClickBack();
          }
          context.navigator.maybePop();
        },
      ),
      body: BlocBuilder<AdaOnePayBloc, AdaOnePayState>(
        builder: (ctx, state) {
          final onePayPortalUrl = state.onePayPortalUrl;
          if (onePayPortalUrl?.isNotEmpty ?? false) {
            return AdaOnePayPortalContainer(onePayPortalUrl!, _returnUrl, (responseCode) {
              if (responseCode == Constants.adaOnePayResponseCodeApproved) {
                context.navigator.pop(true);
              } else {
                context.navigator.pop(false);
              }
            });
          } else {
            return Container();
          }
        },
      ),
    );
  }
}
