// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'dart:async';

import 'package:capp_repayment_core/capp_repayment_core.dart' hide RepaymentContractInfo;
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';

import '../../../capp_repayment.dart';
import 'widgets/index.dart';

class RepaymentBankTransferQrScreen extends StatefulWidget with RouteWrapper {
  final RepaymentBankTransferRouteArgs arguments;

  const RepaymentBankTransferQrScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (_) => context.get<RepaymentBankTransferQrBloc>()
          ..add(
            RepaymentBankTransferQrEvent.initialize(
              arguments.selectedContract,
              arguments.selectedAmount,
              arguments.contractVirtualAccount,
            ),
          ),
        child: this,
      );

  @override
  State<RepaymentBankTransferQrScreen> createState() => _RepaymentBankTransferQrScreenState();
}

class _RepaymentBankTransferQrScreenState extends State<RepaymentBankTransferQrScreen>
    with SingleTickerProviderStateMixin {
  late RepaymentBankTransferQrBloc _bloc;
  late TabController _tabController;
  late int _activeIndex;

  @override
  void initState() {
    _activeIndex = 0;
    _tabController = TabController(
      length: 2,
      vsync: this,
    );
    _tabController.animation?.addListener(() {
      // This will catch a tab change by tapping on the tab bar
      if (_tabController.indexIsChanging) {
        if (_activeIndex != _tabController.index) {
          _activeIndex = _tabController.index;
          setState(() {});
        }
      } else {
        // This will catch a tab change by swipe
        final temp = _tabController.animation!.value.round();
        if (_activeIndex != temp) {
          _activeIndex = temp;
          setState(() {});
        }
      }
    });
    _bloc = BlocProvider.of<RepaymentBankTransferQrBloc>(context);
    context.get<CappRepaymentTrackingService>().trackOwnBankTransferScreenView();
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__bankTransferQrScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__bankTransferQrAppBar__'),
        title: L10nCappRepayment.of(context).repaymentBankTransferTitle,
      ),
      backgroundColor: ColorTheme.of(context).backgroundColor,
      body: KoyalWillPopScope(
        onWillPop: () async {
          context.get<CappRepaymentTrackingService>().trackDirectDiscountBankTransferClickBack();
          _handleCheckVoucherReservationOnBack(isBackToHome: false);
          return false;
        },
        child: MultiBlocListener(
          listeners: [
            BlocListener<RepaymentBankTransferQrBloc, RepaymentBankTransferQrState>(
              listenWhen: (previous, current) => !previous.isErrorFetchBankList && current.isErrorFetchBankList,
              listener: (context, state) {
                _showErrorDialog(context);
              },
            ),
            BlocListener<RepaymentBankTransferQrBloc, RepaymentBankTransferQrState>(
              listener: (context, state) async {
                final isDownloadSuccess = state.isDownloadSuccess;
                var currentSaveQrCodeToGalleryPermissionStatus = state.currentSaveQrCodeToGalleryPermissionStatus;
                if (isDownloadSuccess != null && state.downloadOrShareLoadingState != LoadingState.isLoading) {
                  if (isDownloadSuccess) {
                    final isDownloadBeforeQuit = state.isDownloadBeforeQuit;
                    final banks = state.banks ?? [];
                    final shouldOpenBankListAferDownloadSuccess = state.shouldOpenBankListAferDownloadSuccess;

                    if (shouldOpenBankListAferDownloadSuccess == true) {
                      await _showBankListPopup(
                        banks: banks,
                      );
                    } else {
                      await _showDownloadPopup(
                        title: L10nCappRepayment.of(context).repaymentDownloadSuccessfully,
                        isBack: isDownloadBeforeQuit,
                      );
                    }
                  } else {
                    if (currentSaveQrCodeToGalleryPermissionStatus != PermissionStatus.granted &&
                        currentSaveQrCodeToGalleryPermissionStatus != PermissionStatus.permanentlyDenied) {
                      if (GmaPlatform.isIOS) {
                        currentSaveQrCodeToGalleryPermissionStatus =
                            await PermissionUtils.getPermissionForSaveImageToPhotos()?.request();
                      }
                    } else if (currentSaveQrCodeToGalleryPermissionStatus == PermissionStatus.permanentlyDenied) {
                      _showOpenSettingForPermissionPopup();
                    } else {
                      await _showDownloadPopup(
                        title: L10nCappRepayment.of(context).repaymentDownloadFail,
                        isBack: false,
                      );
                      _bloc.add(
                        const RepaymentBankTransferQrEvent.clearDownloadStatus(),
                      );
                      return;
                    }

                    if (currentSaveQrCodeToGalleryPermissionStatus == PermissionStatus.granted) {
                      final qrUrl = state.contractVirtualAccount?.data?.qrCode ?? '';
                      _bloc
                        ..add(
                          const RepaymentBankTransferQrEvent.clearDownloadStatus(),
                        )
                        ..add(
                          RepaymentBankTransferQrEvent.saveQrCodeToGallery(
                            qrUrl: qrUrl,
                          ),
                        );
                      return;
                    }
                  }

                  _bloc.add(
                    const RepaymentBankTransferQrEvent.clearDownloadStatus(),
                  );
                }

                final isEligibleToShare = state.isEligibleToShare;
                final qrImageFilePath = state.qrImagePath ?? '';
                if (isEligibleToShare != null && state.downloadOrShareLoadingState != LoadingState.isLoading) {
                  if (qrImageFilePath.isNotEmpty) {
                    await Share.shareXFiles([XFile(qrImageFilePath)]);
                  } else {
                    await _showDownloadPopup(title: L10nCappRepayment.of(context).repaymentShareFail, isBack: false);
                  }
                  _bloc.add(
                    const RepaymentBankTransferQrEvent.clearShareStatus(),
                  );
                }
                if (state.loadingState == LoadingState.isCompleted) {
                  state.failureOrSuccessCheckVoucherReservation.fold(() => null, (either) {
                    either.fold(
                      (l) {
                        // Back when fail
                        _handleBack();
                      },
                      (r) async {
                        // back when status is not reserved
                        if (r.status?.toLowerCase() == RepaymentPromotionTransactionStatus.reserved.name) {
                          _bloc.add(const RepaymentBankTransferQrEvent.resetPromotionState());
                          final qrUrl = _bloc.state.contractVirtualAccount?.data?.qrCode ?? '';
                          if (qrUrl.isNotEmpty) {
                            _showQuitConfirmationPopup(qrUrl: qrUrl);
                          } else {
                            debugPrint('Currently, QR url is always not empty');
                          }
                        } else {
                          _handleBack();
                        }
                      },
                    );
                  });
                  // Cancel fail or success is still back
                  state.failureOrSuccessCancelVoucherReservation.fold(() => null, (either) {
                    either.fold(
                      (l) {
                        _handleBack();
                      },
                      (r) async {
                        _handleBack();
                      },
                    );
                  });
                }
              },
            ),
          ],
          child: BlocBuilder<RepaymentBankTransferQrBloc, RepaymentBankTransferQrState>(
            builder: (context, state) {
              final buttonTitle = _activeIndex == 0
                  ? L10nCappRepayment.of(context).repaymentBankTransferDownloadAndOpenBank
                  : L10nCappRepayment.of(context).repaymentBankTransferCopyAndOpenBank;
              return RepaymentProgressContainer(
                isEnableBack: true,
                isLoading: state.loadingState == LoadingState.isLoading ||
                    state.downloadOrShareLoadingState == LoadingState.isLoading,
                child: Stack(
                  children: [
                    _buildTabLayout(
                      context,
                      state,
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        decoration: BoxDecoration(
                          color: ColorTheme.of(context).backgroundColor,
                          boxShadow: [
                            BoxShadow(
                              color: HciColors.black.withOpacity(0.15),
                              blurRadius: 4,
                              offset: const Offset(0, -2), // changes position of shadow
                            ),
                          ],
                        ),
                        child: VerticalButtonsLayout(
                          primaryButton: PrimaryButton(
                            key: const Key('__openBankButton__'),
                            text: buttonTitle,
                            onPressed: () {
                              if (_activeIndex == 0) {
                                _handleDownloadAndOpenBank(context, state);
                              } else {
                                _handleCopyAndOpenBank(context, state);
                              }
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTabLayout(
    BuildContext context,
    RepaymentBankTransferQrState state,
  ) {
    Color labelColor(int index) {
      return index == _activeIndex
          ? ColorTheme.of(context).foreground90Color
          : ColorTheme.of(context).foreground60Color;
    }

    final qrUrl = state.contractVirtualAccount?.data?.qrCode ?? '';
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          indicatorSize: TabBarIndicatorSize.tab,
          indicatorPadding: const EdgeInsets.symmetric(
            horizontal: 8,
          ),
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  KoyalText.subtitle2(
                    L10nCappRepayment.of(context).repaymentBankTransferDownloadQr,
                    color: labelColor(0),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  KoyalText.subtitle2(
                    L10nCappRepayment.of(context).repaymentBankTransferSaveAccountNumber,
                    color: labelColor(1),
                  ),
                ],
              ),
            ),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              RepaymentBankTransferQrTab(
                onTapShare: () {
                  _onTapShare(qrUrl);
                },
                onTapDownload: () {
                  context.get<CappRepaymentTrackingService>().trackOwnBankTransferClickDownload();
                  _onTapDownload(qrUrl: qrUrl);
                },
              ),
              const RepaymentBankTransferAccountTab(),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _showDownloadPopup({required String title, bool? isBack}) {
    return showKoyalOverlay<void>(
      context,
      title: title,
      dismissible: false,
      primaryButtonBuilder: (c) => PrimaryButton(
        text: L10nCappRepayment.of(context).repaymentClose,
        onPressed: () {
          if (isBack ?? false) {
            _handleBack();
          } else {
            c.navigator.pop();
          }
        },
      ),
    );
  }

  Future<bool> _onTapShare(String qrUrl) async {
    context.get<CappRepaymentTrackingService>().trackOwnBankTransferClickShare();
    if (qrUrl.isEmpty) {
      await _showDownloadPopup(
        title: L10nCappRepayment.of(context).repaymentShareFail,
        isBack: false,
      );
      return false;
    } else {
      _bloc.add(
        RepaymentBankTransferQrEvent.shareQrCode(qrUrl: qrUrl),
      );
      return true;
    }
  }

  Future<bool> _onTapDownload({required String qrUrl, bool? shouldOpenBankListAferDownloadSuccess}) async {
    context.get<CappRepaymentTrackingService>().trackOwnBankTransferClickDownload();
    if (qrUrl.isEmpty) {
      await _showDownloadPopup(
        title: L10nCappRepayment.of(context).repaymentDownloadFail,
        isBack: false,
      );
      return false;
    } else {
      _bloc.add(
        RepaymentBankTransferQrEvent.saveQrCodeToGallery(
          qrUrl: qrUrl,
          isDownloadBeforeQuit: false,
          shouldOpenBankListAferDownloadSuccess: shouldOpenBankListAferDownloadSuccess ?? false,
        ),
      );
      return true;
    }
  }

  Future<void> _handleDownloadAndOpenBank(BuildContext context, RepaymentBankTransferQrState state) async {
    await _onTapDownload(
      qrUrl: state.contractVirtualAccount?.data?.qrCode ?? '',
      shouldOpenBankListAferDownloadSuccess: true,
    );
  }

  Future<void> _handleCopyAndOpenBank(BuildContext context, RepaymentBankTransferQrState state) async {
    await Clipboard.setData(ClipboardData(text: state.contractVirtualAccount?.data?.virtualAccountNumber ?? ''));

    final banks = state.banks ?? [];

    await _showBankListPopup(
      banks: banks,
    );
  }

  void _showOpenSettingForPermissionPopup() {
    showKoyalOverlay<String>(
      context,
      title: GmaPlatform.isAndroid
          ? L10nCappRepayment.of(context).repaymentToHavePermissionToAccessPhotoAndroid
          : L10nCappRepayment.of(context).repaymentToHavePermissionToAccessPhotoIos,
      primaryButtonBuilder: (ctx) => PrimaryButton(
        onPressed: () async {
          ctx.navigator.pop();
          await openAppSettings();
        },
        text: L10nCappRepayment.of(context).repaymentOpenSettings,
      ),
      tertiaryButtonBuilder: (ctx) => TertiaryButton(
        text: L10nCappRepayment.of(context).repaymentClose,
        onPressed: () {
          ctx.navigator.pop();
        },
      ),
    );
  }

  void _showQuitConfirmationPopup({required String qrUrl}) {
    context.get<CappRepaymentTrackingService>().trackDirectDiscountBankTransferViewCancelVoucherPopup();
    showKoyalOverlay<String>(
      key: const Key('__quitConfirmationPopup__'),
      context,
      title: L10nCappRepayment.of(context).repaymentDismissOffer,
      body: RichText(
        textAlign: TextAlign.center,
        text: KoyalTextSpan.noStyle(
          children: [
            KoyalTextSpan.subtitle2(
              context,
              text: L10nCappRepayment.of(context).repaymentSkipPromotionDownloadQr,
            ),
            KoyalTextSpan.body2(
              context,
              text: ' ${L10nCappRepayment.of(context).repaymentSkipPromotionAndPayment} ',
            ),
            KoyalTextSpan.subtitle2(
              context,
              text: L10nCappRepayment.of(context).repaymentSkipPromotion60Min,
            ),
            KoyalTextSpan.body2(
              context,
              text: ' ${L10nCappRepayment.of(context).repaymentSkipPromotionToApplyVoucherCancel}',
            ),
          ],
        ),
      ),
      primaryButtonBuilder: (ctx) => PrimaryButton(
        onPressed: () async {
          context.get<CappRepaymentTrackingService>().trackDirectDiscountBankTransferClickDownloadQr();
          if (qrUrl.isNotEmpty) {
            ctx.navigator.pop();
            _bloc.add(
              RepaymentBankTransferQrEvent.saveQrCodeToGallery(qrUrl: qrUrl, isDownloadBeforeQuit: true),
            );
          }
        },
        text: L10nCappRepayment.of(context).repaymentDownloadQrCode,
      ),
      tertiaryButtonBuilder: (ctx) => TertiaryButton(
        text: L10nCappRepayment.of(context).repaymentDismissPromo,
        onPressed: () {
          context.get<CappRepaymentTrackingService>().trackDirectDiscountBankTransferClickCancelVoucher();
          final promotionTransaction = _bloc.state.promotionTransaction;
          final promotionTransactionId = promotionTransaction?.promotionTransactionId ?? '';
          final refTransactionId = promotionTransaction?.refTransactionId ?? '';
          if (promotionTransactionId.isNotEmpty && refTransactionId.isNotEmpty) {
            final request = RepaymentVoucherCancellationRequest(
              promotionTransactionId: promotionTransactionId,
              refTransactionId: refTransactionId,
            );
            _bloc.add(RepaymentBankTransferQrEvent.cancelVoucherReservation(request: request));
          }
        },
      ),
    );
  }

  void _handleCheckVoucherReservationOnBack({required bool isBackToHome}) {
    final promotionTransactionId =
        _bloc.state.contractVirtualAccount?.data?.promotionData?.promotionTransactionId ?? '';
    if (promotionTransactionId.isNotEmpty) {
      _bloc.add(
        RepaymentBankTransferQrEvent.checkVoucherReservation(
          promotionTransactionId: promotionTransactionId,
          isBackToHome: isBackToHome,
        ),
      );
    }
    // In case no voucher selected, back without checking
    else {
      _handleBack(isBackHome: isBackToHome);
    }
  }

  void _handleBack({bool? isBackHome}) {
    // Try to get back to home in case not check voucher reservation first
    final isBackToHome = isBackHome ?? _bloc.state.isBackToHome ?? false;
    if (isBackToHome) {
      context.get<CappRepaymentTrackingService>().trackOwnBankTransferClickBackToHome();
      context.navigator.toMainScreen();
    } else {
      context.navigator.popUntilFromPackage('CappRepayment', 'RepaymentMainScreen');
    }
  }

  Future<dynamic> _showBankListPopup({
    required List<RepaymentQrBank> banks,
  }) async {
    if (banks.isNotEmpty) {
      FocusManager.instance.primaryFocus?.unfocus();
      final selectedBank = await _bloc.getSavedQrBank();
      return showModalBottomSheet<dynamic>(
        isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.0),
        ),
        context: context,
        builder: (c) => RepaymentBankListPopup(
          arguments: RepaymentBankBankListPopupRouteArgs(
            banks: banks,
            selectedBank: selectedBank,
          ),
        ),
      );
    } else {
      unawaited(_showErrorDialog(context));
    }
  }

  Future<void> _showErrorDialog(BuildContext context) async {
    await showKoyalOverlay<bool>(
      context,
      key: const Key('__qrBankTranferErrorPopup__'),
      title: L10nCappRepayment.of(context).somethingWentWrong,
      body: KoyalText.body2(
        L10nCappRepayment.of(context).repaymentPleaseTryAgain,
        color: ColorTheme.of(context).secondaryTextColor,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (buttonContext) => PrimaryButton(
        key: const Key('__selfiePreviewErrorPopupButton__'),
        text: L10nCappRepayment.of(context).repaymentOk,
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
      dismissible: false,
    );
  }
}
