import 'dart:async';

import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gma_platform/infrastructure/platform.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../capp_repayment.dart';

class RepaymentBankListPopup extends StatefulWidget {
  final RepaymentBankBankListPopupRouteArgs arguments;
  const RepaymentBankListPopup({
    Key? key,
    required this.arguments,
  }) : super(key: key);

  @override
  RepaymentBankListPopupState createState() => RepaymentBankListPopupState();
}

class RepaymentBankListPopupState extends State<RepaymentBankListPopup> {
  late RepaymentQrBankBloc _bloc;

  @override
  void initState() {
    _bloc = context.get<RepaymentQrBankBloc>()
      ..add(
        RepaymentQrBankEvent.initialize(
          banks: widget.arguments.banks,
          selectedBank: widget.arguments.selectedBank,
        ),
      );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RepaymentQrBankBloc, RepaymentQrBankState>(
      bloc: _bloc,
      builder: (context, state) {
        final selectedBank = state.selectedBank;
        final fixedPopupHeight = MediaQuery.sizeOf(context).height * 2.9 / 5;

        // Banks
        final banks = state.banks ?? [];
        final searchText = state.searchText ?? '';

        var filteredBanks = List<RepaymentQrBank>.from(banks);
        if (searchText.isNotEmpty) {
          filteredBanks = banks.where((e) => e.name.toLowerCase().contains(searchText.toLowerCase())).toList();
        }

        if (selectedBank != null && filteredBanks.contains(selectedBank)) {
          filteredBanks
            ..removeWhere((e) => e.id == selectedBank.id) // Remove selected item
            ..insert(0, selectedBank);
        }

        return SingleChildScrollView(
          key: const Key('__bankListPopup__'),
          child: Container(
            padding: const EdgeInsets.only(top: KoyalPadding.paddingLarge, bottom: KoyalPadding.paddingXSmall),
            decoration: BoxDecoration(
              color: ColorTheme.of(context).backgroundColor,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                KoyalPadding.normalAll(
                  top: false,
                  left: false,
                  right: false,
                  child: Column(
                    children: [
                      Container(
                        width: 40,
                        height: 8,
                        decoration: BoxDecoration(
                          color: ColorTheme.of(context).foreground5Color,
                          borderRadius: BorderRadius.circular(100.0),
                        ),
                      ),
                      SectionHeading(title: L10nCappRepayment.of(context).repaymentChooseBank),
                      KoyalPadding.normalAll(
                        child: NewSearchBar(
                          key: const Key('__bankListSearchbar__'),
                          hintText: L10nCappRepayment.of(context).repaymentSearchBankHint,
                          onResetSearch: () => _bloc.add(const RepaymentQrBankEvent.updateSearchText(text: '')),
                          onSearchChange: (value) {
                            if (value.isNotEmpty) {
                              if (state.searchText != value) {
                                _bloc.add(RepaymentQrBankEvent.updateSearchText(text: value));
                              }
                            } else {
                              _bloc.add(const RepaymentQrBankEvent.updateSearchText(text: ''));
                            }
                          },
                        ),
                      ),
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: searchText.isNotEmpty ? fixedPopupHeight : 0,
                          maxHeight: fixedPopupHeight,
                        ),
                        child: ListView.builder(
                          key: const Key('__bankList__'),
                          shrinkWrap: true,
                          itemCount: filteredBanks.length,
                          itemBuilder: (context, i) {
                            final bank = filteredBanks[i];
                            final bankName = bank.name;
                            final desc = bank.desc;
                            final iconUrl = bank.imageUrl;
                            final deeplink = bank.deepLink ?? '';
                            final item = DefaultListItemRow(
                              onTap: (ctx) async {
                                _bloc.add(RepaymentQrBankEvent.selectBank(bank: bank));

                                ctx.navigator.pop();

                                // Open bank deeplink
                                if (deeplink.isNotEmpty) {
                                  final uri = Uri.encodeFull(deeplink);

                                  if (await GmaPlatform.canLaunchUrl(uri)) {
                                    if (context.mounted) {
                                      unawaited(
                                        context.navigator.pushFromPackage(
                                          package: 'CappRepayment',
                                          screen: 'RepaymentBankTransferQrWaitingScreen',
                                        ),
                                      );
                                    }

                                    await GmaPlatform.launchUrl(uri, mode: LaunchMode.externalApplication);
                                  }
                                }
                              },
                              title: bankName,
                              caption: desc,
                              icon: iconUrl.asImage(
                                fit: BoxFit.fitWidth,
                                width: 32,
                                showShimmer: true,
                                imagePackage: 'capp_repayment',
                              ),
                            );

                            return item;
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
