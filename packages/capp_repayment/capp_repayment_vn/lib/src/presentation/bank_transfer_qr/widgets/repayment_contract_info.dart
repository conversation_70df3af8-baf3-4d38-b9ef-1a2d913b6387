import 'package:capp_repayment_core/capp_repayment_core.dart' hide RepaymentContractInfo;
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentContractInfo extends StatelessWidget {
  final String totalAmount;
  final String dueDate;
  final String accountNo;
  final String? minimumAmount;
  final String discountAmount;
  final String finalAmount;

  const RepaymentContractInfo({
    Key? key,
    required this.totalAmount,
    required this.dueDate,
    required this.accountNo,
    required this.discountAmount,
    required this.finalAmount,
    this.minimumAmount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        KoyalPadding.normalAll(
          key: const Key('__contractInfoTitle__'),
          child: KoyalText.subtitle1(
            L10nCappRepayment.of(context).repaymentContractInformationNormal,
            color: ColorTheme.of(context).defaultTextColor,
          ),
        ),
        if (minimumAmount != null) ...[
          ExtendedInfoListItem(
            key: const Key('__minimumAmountInfo__'),
            title: L10nCappRepayment.of(context).repaymentMinimumAmount,
            bodyText: minimumAmount!,
            useBoldBodyText: true,
          ),
          const ListDivider(),
        ],
        ExtendedInfoListItem(
          key: const Key('__totalAmountInfo__'),
          title: L10nCappRepayment.of(context).repaymentTotalAmountNormal,
          bodyText: totalAmount,
        ),
        const ListDivider(),
        ExtendedInfoListItem(
          key: const Key('__discountAmountInfo__'),
          title: L10nCappRepayment.of(context).repaymentQrDiscount,
          bodyText: discountAmount,
        ),
        const ListDivider(),
        ExtendedInfoListItem(
          key: const Key('__discountAmountInfo____discountAmountInfo__'),
          title: L10nCappRepayment.of(context).repaymentMainTotalPaymentAmount,
          bodyText: finalAmount,
          useBoldBodyText: true,
        ),
        const ListDivider(),
        ExtendedInfoListItem(
          key: const Key('__dueDateInfo__'),
          title: L10nCappRepayment.of(context).repaymentDueDateNormal,
          bodyText: dueDate,
        ),
        const ListDivider(),
        ExtendedInfoListItem(
          key: const Key('__contractNumberInfo__'),
          title: L10nCappRepayment.of(context).repaymentContractNo,
          bodyText: accountNo,
        ),
      ],
    );
  }
}
