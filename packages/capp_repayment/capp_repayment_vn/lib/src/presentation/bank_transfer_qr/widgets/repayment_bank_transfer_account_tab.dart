import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_repayment.dart';
import 'repayment_bank_transfer_summary_painter.dart';

class RepaymentBankTransferAccountTab extends StatelessWidget {
  const RepaymentBankTransferAccountTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RepaymentBankTransferQrBloc, RepaymentBankTransferQrState>(
      builder: (context, state) {
        final totalAmount = state.totalAmount!.formatCurrency();
        final promotionData = state.contractVirtualAccount?.data?.promotionData;
        final finalAmount = promotionData?.finalAmount.formatCurrency() ?? totalAmount;
        final contractVirtualAccount = state.contractVirtualAccount;
        final contractVirtualAccountData = contractVirtualAccount?.data;
        final virtualAccountNumber = contractVirtualAccountData?.virtualAccountNumber ?? '';

        return Container(
          color: ColorTheme.of(context).textDividerColor,
          child: SingleChildScrollView(
            child: Column(
              children: [
                SectionHeading(
                  title: L10nCappRepayment.of(context).repaymentBankTransferPaymentInfo,
                  bgColor: ColorTheme.of(context).textDividerColor,
                ),
                KoyalPadding.normalHorizontal(
                  child: CustomPaint(
                    painter: RepaymentBankTransferSummaryPainter(),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Alert(
                            key: const Key('__accountTabAlert__'),
                            text: L10nCappRepayment.of(context).repaymentBankTransferSaveAccountNumberAlert,
                            theme: AlertTheme.info(),
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          'assets/icons/ic_bidv_large.png'.asImage(
                            key: const Key('__accountTabHomeIcon__'),
                            imagePackage: 'capp_repayment',
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          KoyalText.subtitle1(
                            key: const Key('__accountNameInfo__'),
                            L10nCappRepayment.of(context).repaymentBankTransferHomeCreditName,
                          ),
                          const SizedBox(
                            height: 4,
                          ),
                          KoyalText.body1(
                            key: const Key('__bankNameInfo__'),
                            L10nCappRepayment.of(context).repaymentBankTransferHomeCreditBidvBank,
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          Row(
                            children: [
                              const Expanded(child: SizedBox.shrink()),
                              RichText(
                                text: KoyalTextSpan.noStyle(
                                  children: [
                                    KoyalTextSpan.body1(
                                      color: ColorTheme.of(context).defaultTextColor,
                                      context,
                                      text: L10nCappRepayment.of(context).repaymentBankTransferAccountNumber,
                                    ),
                                    KoyalTextSpan.body2(
                                      color: ColorTheme.of(context).defaultTextColor,
                                      context,
                                      text: virtualAccountNumber,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(
                                width: 8,
                              ),
                              Expanded(
                                child: CopyButton(
                                  key: const Key('__accountTabCopyButton__'),
                                  label: '',
                                  size: 21,
                                  copyText: virtualAccountNumber,
                                  onTap: () {
                                    context
                                        .get<CappRepaymentTrackingService>()
                                        .trackOwnBankTransferClickCopyAccountNumber();
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 36,
                          ),
                          KoyalPadding.small(
                            child: Row(
                              key: const Key('__accountTabAmountSection__'),
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                KoyalText.subtitle2(L10nCappRepayment.of(context).repaymentBankTransferTotalAmount),
                                KoyalText.subtitle1(finalAmount),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
