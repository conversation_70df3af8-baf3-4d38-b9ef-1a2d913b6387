import 'package:capp_ui_core/themes/colors/hci_colors.dart';
import 'package:flutter/material.dart';

class RepaymentBankTransferSummaryPainter extends CustomPainter {
  final double bottomHeight;
  final double borderRadiusValue;
  final double circleRadiusValue;
  final double dashWidth;
  final double spaceWidth;

  RepaymentBankTransferSummaryPainter(
      {this.bottomHeight = 60.0,
      this.borderRadiusValue = 16.0,
      this.circleRadiusValue = 10.0,
      this.dashWidth = 3.0,
      this.spaceWidth = 2.0,});

  @override
  void paint(Canvas canvas, Size size) {
    final dashedPaint = Paint()
      ..color = HciColors.supplementary100
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.square;

    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2
      ..style = PaintingStyle.fill
      ..strokeCap = StrokeCap.round;
    final height = size.height;
    final width = size.width;
    // The border radius
    final borderRadius = Radius.circular(borderRadiusValue);
    // The bottom circle radius
    final circleRadius = Radius.circular(circleRadiusValue);
    // Position at the top of break line
    const bottomHeight = 60;
    final path = Path()
      ..moveTo(borderRadiusValue, 0)
      // Left
      ..arcToPoint(Offset(0, borderRadiusValue), radius: borderRadius, clockwise: false)
      ..lineTo(0, height - (bottomHeight + circleRadiusValue))
      ..arcToPoint(Offset(circleRadiusValue, height - bottomHeight), radius: circleRadius)
      ..arcToPoint(Offset(0, height - bottomHeight + circleRadiusValue), radius: circleRadius)
      ..lineTo(0, height - borderRadiusValue)
      // Bottom
      ..arcToPoint(Offset(borderRadiusValue, height), radius: borderRadius, clockwise: false)
      ..lineTo(width - circleRadiusValue, height)
      ..arcToPoint(Offset(width, height - borderRadiusValue), radius: borderRadius, clockwise: false)
      // Right
      ..lineTo(width, height - bottomHeight + circleRadiusValue)
      ..arcToPoint(Offset(width - circleRadiusValue, height - bottomHeight), radius: circleRadius)
      ..arcToPoint(Offset(width, height - bottomHeight - circleRadiusValue), radius: circleRadius)
      // Top
      ..lineTo(width, borderRadiusValue)
      ..arcToPoint(Offset(width - borderRadiusValue, 0), radius: borderRadius, clockwise: false)
      ..lineTo(0, 0);

    canvas.drawPath(path, paint);
    final startX = circleRadiusValue;
    final y = height - bottomHeight;
    final centerX = width / 2;
    // Draw center line
    canvas
      ..drawLine(Offset(centerX, y), Offset(centerX - dashWidth / 2, y), dashedPaint)
      ..drawLine(Offset(centerX, y), Offset(centerX + dashWidth / 2, y), dashedPaint);
    var drawDash = false;
    for (var x = centerX - dashWidth / 2; x > startX + (dashWidth + spaceWidth); x = x - dashWidth - spaceWidth) {
      if (drawDash) {
        canvas
          ..drawLine(Offset(x, y), Offset(x - dashWidth, y), dashedPaint)
          ..drawLine(Offset((width - x), y), Offset((width - x) + dashWidth, y), dashedPaint);
      }
      drawDash = !drawDash;
    }
  }

  @override
  bool shouldRepaint(RepaymentBankTransferSummaryPainter oldDelegate) => false;

  @override
  bool shouldRebuildSemantics(RepaymentBankTransferSummaryPainter oldDelegate) => false;
}
