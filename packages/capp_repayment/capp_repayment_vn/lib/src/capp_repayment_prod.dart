import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:logger/logger.dart';
import 'package:vnpay/vnpay_plugin.dart';

import '../capp_repayment.dart';

class CappRepaymentProd extends CappRepayment {
  @override
  void registerServices(GetIt c) {
    super.registerServices(c);
    c.registerFactory<InitPayPluginInit>(() => (setting) async => VnpayPlugin.initialize(setting));
  }
}

class CappRepaymentTestProd extends CappRepaymentProd {
  @override
  void registerStorage(GetIt c) {
    c.registerLazySingleton(
      () => RepaymentStorage(
        storage: ReactiveInMemoryStorageProvider(logger: c<Logger>()),
      ),
    );
  }
}
