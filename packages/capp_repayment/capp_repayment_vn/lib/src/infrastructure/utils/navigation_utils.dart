import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment.dart';

class VnNavigationUtils {
  const VnNavigationUtils._();

  static Future<void> navigateToCheckResultScreen({
    required BuildContext context,
    required RepaymentUserPaymentMethod paymentMethod,
    required String uuid,
    required String phoneNumber,
    String? rootRouteName,
  }) async {
    await context.navigator.pushTyped(
      package: CappRepayment,
      screen: RepaymentCheckResultScreen,
      arguments: RepaymentCheckResultRouteArgs(
        paymentMethod: paymentMethod,
        uuid: uuid,
        phoneNumber: phoneNumber,
        rootRouteName: rootRouteName,
      ),
    );
  }

  static Future<void> navigateToRepaymentAdaSuccessScreen({
    required BuildContext context,
    AdaContract? contract,
    AdaDraftContract? draftContract,
    required RepaymentUserPaymentMethod paymentMethod,
    bool? isReplace,
  }) async {
    await (isReplace == true
        ? context.navigator.pushReplacementTyped(
            package: CappRepayment,
            screen: RepaymentAdaSuccessScreen,
            arguments: RepaymentAdaSuccessRouteArgs(
              contract: contract,
              draftContract: draftContract,
              paymentMethod: paymentMethod,
            ),
          )
        : context.navigator.pushTyped(
            package: CappRepayment,
            screen: RepaymentAdaSuccessScreen,
            arguments: RepaymentAdaSuccessRouteArgs(
              contract: contract,
              draftContract: draftContract,
              paymentMethod: paymentMethod,
            ),
          ));
  }

  static Future<void> navigateToRepaymentAdaFailedScreen({
    required BuildContext context,
    required RepaymentUserPaymentMethod paymentMethod,
    AdaContract? contract,
    AdaDraftContract? draftContract,
    bool? isReplace,
  }) async {
    await (isReplace == true
        ? context.navigator.pushReplacementTyped(
            package: CappRepayment,
            screen: RepaymentAdaFailedScreen,
            arguments: RepaymentAdaFailedRouteArgs(
                contract: contract, draftContract: draftContract, paymentMethod: paymentMethod,),
          )
        : context.navigator.pushTyped(
            package: CappRepayment,
            screen: RepaymentAdaFailedScreen,
            arguments: RepaymentAdaFailedRouteArgs(
                contract: contract, draftContract: draftContract, paymentMethod: paymentMethod,),
          ));
  }

  static Future<void> navigateToRepaymentLooAda({
    required BuildContext context,
    required String flowInstanceId,
    required String contractType,
    required String savedPaymentMethodId,
    String? ddmCode,
  }) async {
    await context.navigator.pushFromPackage(
      package: 'CappRepayment',
      screen: 'LooAdaMainScreen',
      arguments: LooAdaMainRouteArgs(
        contractType: contractType,
        flowInstanceId: flowInstanceId,
        ddmCode: ddmCode,
        savedPaymentMethodId: savedPaymentMethodId,
      ),
    );
  }
}
