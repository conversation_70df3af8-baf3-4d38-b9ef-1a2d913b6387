import '../../../capp_repayment.dart';

class TrackingUtils {
  const TrackingUtils._();

  // Error type format paymentMethod_bankShortName(if any)_errorInfo
  static String getErrorType({
    RepaymentUserPaymentMethod? paymentMethod,
    RepaymentBank? bank,
    required String errorInfo,
  }) {
    if (errorInfo.isEmpty) {
      return '';
    }

    var errorType = '';

    if (paymentMethod != null) {
      errorType += paymentMethod.gmaId;
    }
    if (bank != null) {
      errorType += '_${bank.shortName}';
    }
    if (errorType.isNotEmpty) {
      return '${errorType}_$errorInfo';
    } else {
      return errorInfo;
    }
  }
}
