import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentVoucherCancellationMapper on api_models.VNVoucherCancellationResponse {
  RepaymentVoucherCancellation toDomain() => RepaymentVoucherCancellation(
        message: message,
      );
}

extension RepaymentVoucherCancellationRequestMapper on api_models.VNVoucherCancellationRequest {
  static api_models.VNVoucherCancellationRequest fromDomain(RepaymentVoucherCancellationRequest request) =>
      api_models.VNVoucherCancellationRequest(
        promotionTransactionId: request.promotionTransactionId,
        refTransactionId: request.refTransactionId,
      );
}
