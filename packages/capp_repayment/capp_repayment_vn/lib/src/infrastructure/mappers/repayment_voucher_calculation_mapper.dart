import 'package:selfcareapi/model/models.dart' as api_models;

import '../../domain/models/promotion/index.dart';

extension RepaymentVoucherCalculationMapper on api_models.VNVoucherCalculationResponse {
  RepaymentVoucherCalculation toDomain() => RepaymentVoucherCalculation(
        calculation: RepaymentVoucherCalculationData(
          discountAmount: calculation?.discountAmount,
          finalAmount: calculation?.finalAmount,
          originalAmount: calculation?.originalAmount,
        ),
      );
}
