import 'package:selfcareapi/model/models.dart' as api_models;

import '../../domain/models/bank_transfer_qr/repayment_contract_virtual_account.dart';
import 'repayment_contract_virtual_account_data_mapper.dart';

extension RepaymentContractVirtualAccountMapper on api_models.RepaymentV2ContractVirtualAccountDto {
  RepaymentContractVirtualAccount toDomain() => RepaymentContractVirtualAccount(
        responseCode: responseCode,
        responseMessage: responseMessage,
        data: data?.toDomain(),
      );
}
