import 'package:decimal/decimal.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentPtpEvaluationMapper on api_models.PtpEvaluationResponse {
  RepaymentPtpEvaluation toDomain() => RepaymentPtpEvaluation(
        requestId: requestId,
        entityId: entityId,
        entityType: entityType,
        paymentMethod: paymentMethod,
        promiseAmount: Decimal.tryParse(promiseAmount?.toString() ?? ''),
        promiseDate: promiseDate,
        delinquencyReason: delinquencyReason,
        comment: comment,
        promiseEvaluationResult: promiseEvaluationResult,
        status: status,
      );
}
