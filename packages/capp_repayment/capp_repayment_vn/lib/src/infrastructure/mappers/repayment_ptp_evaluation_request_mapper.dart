import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentPtpEvaluationRequestMapper on api_models.PtpEvaluationRequest {
  static api_models.PtpEvaluationRequest fromDomain(RepaymentPtpEvaluationRequest request) =>
      api_models.PtpEvaluationRequest(
        entityId: request.entityId,
        promiseDate: request.promiseDate,
        promiseAmount: request.promiseAmount?.toDouble(),
      );
}
