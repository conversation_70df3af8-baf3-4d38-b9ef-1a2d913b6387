import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';
import 'repayment_transaction_create_mapper.dart';

extension RepaymentShopeePayTransactionRequestMapper on api_models.ShopeepayRepaymentTransactionCreateDto {
  static api_models.ShopeepayRepaymentTransactionCreateDto fromDomain(RepaymentShopeePayTransactionRequest request) =>
      api_models.ShopeepayRepaymentTransactionCreateDto(
        repaymentInfo: request.transactionCreate?.toDto(),
        returnUrl: request.returnUrl,
      );
}
