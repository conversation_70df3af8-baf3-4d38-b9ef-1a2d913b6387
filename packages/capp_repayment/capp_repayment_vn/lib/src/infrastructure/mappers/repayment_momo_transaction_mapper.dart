import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentMomoTransactionMapper on api_models.MomoInitResponseV2 {
  RepaymentMomoTransaction toDomain() => RepaymentMomoTransaction(
        transactionInfo: repaymentInfo?.toDomain(),
        paymentUrl: paymentUrl,
      );
}

extension RepaymentMomoTransactionMapperV1 on api_models.MomoInitResponse {
  RepaymentMomoTransaction toDomain() => RepaymentMomoTransaction(
        transactionInfo: repaymentInfo?.toDomain(),
        paymentUrl: target,
      );
}
