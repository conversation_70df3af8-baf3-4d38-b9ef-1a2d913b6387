import 'package:selfcareapi/model/models.dart' as api_models;

import '../../domain/models/promotion/index.dart';

extension RepaymentVoucherValidationMapper on api_models.VNVoucherValidationResponse {
  RepaymentVoucherValidation toDomain() => RepaymentVoucherValidation(
        isValid: isValid,
        calculation: RepaymentVoucherCalculationData(
          discountAmount: calculation?.discountAmount,
          finalAmount: calculation?.finalAmount,
          originalAmount: calculation?.originalAmount,
        ),
        violatedConditions: (violatedConditions ?? []).isNotEmpty
            ? violatedConditions!
                .map((e) => RepaymentVoucherViolatedCondition(reasonCode: e.reasonCode, reason: e.reason))
                .toList()
            : null,
      );
}
