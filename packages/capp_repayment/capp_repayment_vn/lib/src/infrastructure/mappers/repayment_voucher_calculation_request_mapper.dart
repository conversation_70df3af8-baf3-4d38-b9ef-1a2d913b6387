import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentVoucherCalculationRequestMapper on api_models.VNVoucherCalculationRequest {
  static api_models.VNVoucherCalculationRequest fromDomain(RepaymentVoucherCalculationRequest request) =>
      api_models.VNVoucherCalculationRequest(
        voucherCode: request.voucherCode,
        originalAmount: request.originalAmount,
        minRequiredAmount: request.minRequiredAmount,
      );
}