import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentContractVirtualAccountDataMapper on api_models.RepaymentV2ContractVirtualAccountData {
  RepaymentContractVirtualAccountData toDomain() => RepaymentContractVirtualAccountData(
        accountName: accountName,
        bankName: bankName,
        bankBranch: bankBranch,
        virtualAccountNumber: virtualAccountNumber,
        qrCode: qrCode,
        promotionData: promotion?.toDomain(),
      );
}
