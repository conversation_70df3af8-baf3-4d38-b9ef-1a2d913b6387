import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';
import 'repayment_transaction_create_mapper.dart';

extension RepaymentZaloPayTransactionRequestMapper on api_models.ZalopayRepaymentTransactionCreateDto {
  static api_models.ZalopayRepaymentTransactionCreateDto fromDomain(RepaymentZaloPayTransactionRequest request) =>
      api_models.ZalopayRepaymentTransactionCreateDto(
        repaymentInfo: request.transactionCreate?.toDto(),
      );
}