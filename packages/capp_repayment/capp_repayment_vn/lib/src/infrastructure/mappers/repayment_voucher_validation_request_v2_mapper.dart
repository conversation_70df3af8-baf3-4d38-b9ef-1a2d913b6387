import 'package:collection/collection.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentVoucherValidationRequestV2Mapper on api_models.VNVoucherValidationRequestV2 {
  static api_models.VNVoucherValidationRequestV2 fromDomain(RepaymentVoucherValidationRequest request) =>
      api_models.VNVoucherValidationRequestV2(
        voucherCode: request.voucherCode,
        contractNumber: request.contractNumber,
        originalAmount: request.originalAmount,
        paymentMethod: api_models.PromotionPaymentMethod.values.firstWhereOrNull((e) => e.toJson() == request.paymentMethod),
      );
}
