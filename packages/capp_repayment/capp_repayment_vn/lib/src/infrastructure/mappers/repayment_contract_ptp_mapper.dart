import 'package:decimal/decimal.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentPtpContractDataMapper on api_models.PtpEligibilityResponse {
  RepaymentPtpContract toDomain() => RepaymentPtpContract(
        entityId: entityId,
        entityType: entityType,
        ptpEligible: ptpEligible,
        minimumAmount: Decimal.tryParse(minimumAmount?.toString() ?? ''),
        totalAmount: Decimal.tryParse(totalAmount?.toString() ?? ''),
        minimumRate: int.tryParse(minimumRate?.toString() ?? ''),
        maximalLength: int.tryParse(maximalLength?.toString() ?? ''),
        dpd: int.tryParse(dpd?.toString() ?? ''),
        dueDate: dueDate,
        hasActivePTP: hasActivePTP,
        lastPTP: RepaymentPtpEvaluation(
          requestId: lastPTP?.requestId,
          entityType: lastPTP?.entityType,
          entityId: lastPTP?.entityId,
          comment: lastPTP?.comment,
          promiseAmount: Decimal.tryParse(lastPTP?.promiseAmount?.toString() ?? ''),
          delinquencyReason: lastPTP?.delinquencyReason,
          paymentMethod: lastPTP?.paymentMethod,
          promiseDate: lastPTP?.promiseDate,
          promiseEvaluationResult: lastPTP?.promiseEvaluationResult,
          status: lastPTP?.status,
        ),
      );
}
