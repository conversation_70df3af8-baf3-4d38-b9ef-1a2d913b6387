import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentUserPaymentMethodMapper on api_models.UserPaymentMethodDto {
  RepaymentUserPaymentMethod toDomain() {
    final repaymentProductCodess = segmentationBadge?.split(',').where((item) => item.trim().isNotEmpty).toList();

    final adProductCodes = segmentationAutoDebitBadge?.split(',').where((item) => item.trim().isNotEmpty).toList();

    return RepaymentUserPaymentMethod(
      id: id,
      gmaId: gmaId ?? '',
      badge: badge,
      description: description,
      iconUrl: iconUrl,
      title: title,
      autoDebitBadge: autoDebitBadge,
      repaymentProductCodess: repaymentProductCodess,
      adProductCodes: adProductCodes,
    );
  }
}
