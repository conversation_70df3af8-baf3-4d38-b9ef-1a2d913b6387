import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';
import 'repayment_transaction_create_mapper.dart';

extension RepaymentMomoTransactionRequestMapper on api_models.MomoRepaymentTransactionCreateDto {
  static api_models.MomoRepaymentTransactionCreateDto fromDomain(RepaymentMomoTransactionRequest request) =>
      api_models.MomoRepaymentTransactionCreateDto(
        repaymentTransaction: request.transactionCreate?.toDto(),
        callbackUrl: request.callbackUrl,
        client: request.client,
      );
}
