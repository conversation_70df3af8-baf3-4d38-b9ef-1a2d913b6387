import 'package:selfcareapi/model/models.dart' as api_models;

import '../../domain/index.dart';

extension RepaymentTransactionMapper on api_models.RepaymentTransactionDto {
  RepaymentTransaction toDomain() => RepaymentTransaction(
        gmaTransactionId: gmaTransactionId,
        cuid: cuid,
        pcsTransactionId: pcsTransactionId,
        partnerTransactionId: partnerTransactionId,
        contractNumber: contractNumber,
        finalAmount: amount,
        currencyCode: currencyCode,
        provider: provider,
        paymentStatus: paymentStatus,
        createdBy: createdBy,
        createdDate: createdDate,
        discountAmount: discountAmount,
        originalAmount: originalAmount,
        voucherCode: voucherCode,
      );
}
