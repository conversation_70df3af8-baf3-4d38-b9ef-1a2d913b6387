import 'package:selfcareapi/model/models.dart' as api_models;

import '../../domain/models/onepay/repayment_onepay_transaction.dart';
import 'repayment_transaction_mapper.dart';

extension RepaymentOnePayTransactionMapper on api_models.OnepayRepaymentTransactionDetailDto {
  RepaymentOnePayTransaction toDomain() =>
      RepaymentOnePayTransaction(paymentUrl: paymentUrl, transactionInfo: repaymentInfo?.toDomain());
}
