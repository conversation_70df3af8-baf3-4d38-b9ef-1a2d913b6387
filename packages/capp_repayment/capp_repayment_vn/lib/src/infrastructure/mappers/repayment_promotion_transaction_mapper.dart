import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentPromotionTransactionMapper on api_models.VNRepaymentPromotionTransaction {
  RepaymentPromotionTransaction toDomain() => RepaymentPromotionTransaction(
        discountAmount: discountAmount,
        finalAmount: finalAmount,
        originalAmount: originalAmount,
        promotionTransactionId: promotionTransactionId,
        refTransactionId: refTransactionId,
        actualAmount: actualAmount,
        contractNumber: contractNumber,
        status: status,
        voucherCode: voucherCode,
      );
}
