import 'package:selfcareapi/model/models.dart' as api_models;

import '../../domain/index.dart';
import 'repayment_transaction_create_mapper.dart';

extension RepaymentViettelMoneyTransactionRequestMapper on api_models.ViettelRepaymentTransactionCreateDto {
  static api_models.ViettelRepaymentTransactionCreateDto fromDomain(RepaymentViettelMoneyTransactionRequest request) =>
      api_models.ViettelRepaymentTransactionCreateDto(
        repaymentInfo: request.transactionCreate?.toDto(),
        returnUrl: request.returnUrl,
        cancelUrl: request.cancelUrl,
      );
}
