import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../../capp_repayment.dart';

extension AdaOnepayRegistrationMapper on api_models.OnepayAdaRegistrationResponseDTO {
  AdaOnepayRegistrationResponse toDomain() =>
      AdaOnepayRegistrationResponse(redirectLink: redirectLink, isUserEnrolled: isEnrolled);
}

extension AdaOnepayRegistrationRequestMapper on AdaOnepayRegistrationRequest {
  api_models.OnepayAdaRegistrationRequestDTO fromDomain() => api_models.OnepayAdaRegistrationRequestDTO(
        redirectLink: redirectLink,
        contractNumber: contractNumber,
        registerAdaAmount: registerAdaAmount,
        registerAdaDay: registerAdaDay,
        locale: locale,
      );
}

extension AdaDraftOnepayRegistrationResponseMapper on api_models.OnepayAdaDraftRegistrationResponseDTO {
  AdaDraftOnepayRegistrationResponse toDomain() => AdaDraftOnepayRegistrationResponse(
    id: id,
    ddmCode: ddmCode,
    redirectLink: redirectLink,
    isEnrolled: isEnrolled,
  );
}

extension AdaDraftOnepayRegistrationRequestMapper on AdaDraftOnepayRegistrationRequest {
  api_models.OnepayAdaDraftRegistrationRequestDTO fromDomain() => api_models.OnepayAdaDraftRegistrationRequestDTO(
    redirectLink: redirectLink,
    flowInstanceId: flowInstanceId,
    contractType: contractType,
    flow: flow,
    locale: locale,
  );
}
