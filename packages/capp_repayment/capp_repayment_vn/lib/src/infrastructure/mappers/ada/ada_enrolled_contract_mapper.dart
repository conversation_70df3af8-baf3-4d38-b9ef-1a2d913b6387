import 'package:decimal/decimal.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../../capp_repayment.dart';

extension AdaEnrolledContractMapper on api_models.AdaDTO {
  AdaEnrolledContract toDomain() => AdaEnrolledContract(
        contractNumber: contractNumber,
        registeredAdaAmount: Decimal.tryParse(registeredAdaAmount.toString()),
        registeredAdaDay: registeredAdaDay?.toInt(),
        loanType: loanType,
        productImageUrl: productImageUrl,
        productName: productName,
        id: id,
        paymentPartner: paymentPartner,
        productType: productType,
        status: status,
        productCode: productCode,
      );
}
