import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../../capp_repayment.dart';

extension AdaZaloRegistrationMapper on api_models.ZaloAdaRegistrationResponseDTO {
  AdaZaloRegistrationResponse toDomain() =>
      AdaZaloRegistrationResponse(deepLink: deepLink, bindingToken: bindingId, isUserEnrolled: isEnrolled);
}

extension AdaZaloRegistrationRequestMapper on AdaZaloRegistrationRequest {
  api_models.AdaRegistrationRequestDTO fromDomain() => api_models.AdaRegistrationRequestDTO(
        redirectLink: redirectLink,
        contractNumber: contractNumber,
        registerAdaAmount: registerAdaAmount,
        registerAdaDay: registerAdaDay,
      );
}

extension AdaDraftZaloRegistrationMapper on api_models.ZaloAdaDraftRegistrationResponseDTO {
  AdaDraftZaloRegistrationResponse toDomain() => AdaDraftZaloRegistrationResponse(
        id: id,
        deepLink: deepLink,
        bindingId: bindingId,
        isEnrolled: isEnrolled,
        ddmCode: ddmCode,
      );
}

extension AdaDraftZaloRegistrationRequestMapper on AdaDraftZaloRegistrationRequest {
  api_models.AdaDraftRegistrationRequestDTO fromDomain() => api_models.AdaDraftRegistrationRequestDTO(
        redirectLink: redirectLink,
        flowInstanceId: flowInstanceId,
        contractType: contractType,
        flow: flow,
      );
}
