import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../../capp_repayment.dart';

extension AdaOnepayCancelRequestMapper on AdaOnepayCancelRequest {
  api_models.OnepayAdaCancellationRequestDTO fromDomain() =>
      api_models.OnepayAdaCancellationRequestDTO(redirectLink: redirectLink, adaId: adaId, flowInstanceId: flowId,);
}

extension AdaOnepayCancelResponseMapper on api_models.OnepayAdaCancellationResponseDTO {
  AdaOnepayCancelResponse toDomain() =>
      AdaOnepayCancelResponse(redirectLink: redirectLink,);
}