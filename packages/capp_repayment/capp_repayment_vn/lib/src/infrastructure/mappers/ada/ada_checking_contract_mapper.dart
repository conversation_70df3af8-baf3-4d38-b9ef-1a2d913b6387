import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../../capp_repayment.dart';

extension AdaCheckingContractListResponseMapper on api_models.VNAdaContractEligibilityList {
  AdaContractListResponse toDomain() => AdaContractListResponse(
        celContracts: celEligibilityData?.map((e) => e.toDomain()).toList(),
        relContracts: relEligibilityData?.map((e) => e.toDomain()).toList(),
      );
}

extension AdaCelContractMapper on api_models.VNAdaCelEligibilityData {
  AdaCelContract toDomain() => AdaCelContract(
        contractNumber: contractNumber,
        dueDay: dueDay?.toInt(),
        dueAmount: dueAmount,
        totalOutstandingDebt: totalOutstandingDebt,
        isEligible: isEligible,
      );
}

extension AdaRelContractMapper on api_models.VNAdaRelEligibilityData {
  AdaRelContract toDomain() => AdaRelContract(
        contractNumber: contractNumber,
        gmaProductType: gmaProductType,
        billingDay: billingDay,
        isEligible: isEligible,
        currency: currency,
        minimumDueAmount: minimumDueAmount,
        totalAmountDue: totalAmountDue,
        dueDay: dueDay?.toInt(),
        outstandingBalance: outstandingBalance,
      );
}
