import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentVoucherValidationRequestMapper on api_models.VNVoucherValidationRequest {
  static api_models.VNVoucherValidationRequest fromDomain(RepaymentVoucherValidationRequest request) =>
      api_models.VNVoucherValidationRequest(
        voucherCode: request.voucherCode,
        contractNumber: request.contractNumber,
        originalAmount: request.originalAmount,
        paymentMethod: request.paymentMethod,
      );
}
