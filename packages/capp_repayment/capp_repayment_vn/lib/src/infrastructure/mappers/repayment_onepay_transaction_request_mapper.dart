import 'package:selfcareapi/model/models.dart' as api_models;

import '../../domain/index.dart';
import 'repayment_transaction_create_mapper.dart';

extension RepaymentOnePayTransactionRequestMapper on api_models.OnepayRepaymentTransactionCreateDto {
  static api_models.OnepayRepaymentTransactionCreateDto fromDomain(RepaymentOnePayTransactionRequest request) =>
      api_models.OnepayRepaymentTransactionCreateDto(
        repaymentTransaction: request.transactionCreate?.toDto(),
        returnUrl: request.returnUrl,
      );
}
