import 'package:collection/collection.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentVoucherMapper on api_models.VNRepaymentPromotionVoucherData {
  RepaymentVoucher toDomain() => RepaymentVoucher(
        code: code ?? '',
        name: name ?? '',
        icon: icon,
        status: status,
        description: description,
        flowApplied: flowApplied,
        minRequiredAmount: minRequiredAmount,
        discountType: discountType,
        discountPercentage: discountPercentage,
        maxDiscount: maxDiscount,
        discountAmount: discountAmount,
        startDate: startDate,
        endDate: endDate,
        isValid: isValid,
      );
}

extension RepaymentVoucherMapperV2 on api_models.VNRepaymentPromotionVoucherDataV2 {
  RepaymentVoucher toDomain() => RepaymentVoucher(
        code: code ?? '',
        name: name ?? '',
        icon: icon,
        status: status,
        description: description,
        flowApplied: flowApplied,
        minRequiredAmount: minRequiredAmount,
        discountType: discountType,
        discountPercentage: discountPercentage,
        maxDiscount: maxDiscount,
        discountAmount: discountAmount,
        startDate: startDate,
        endDate: endDate,
        isValid: isValid,
        appliedPaymentMethods: appliedPaymentMethods
            ?.map(
              (method) => RepaymentPaymentMethodHelper.byId(method.toJson()),
            )
            .whereNotNull()
            .toList(),
      );
}
