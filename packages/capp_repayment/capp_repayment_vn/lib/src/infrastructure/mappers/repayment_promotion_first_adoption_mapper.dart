import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment.dart';

extension RepaymentPromotionFirstAdoptionMapper on api_models.VNFirstRepaymentAdoptionData {
  RepaymentPromotionFirstAdoption toDomain() => RepaymentPromotionFirstAdoption(
        isDisplay: isDisplay,
        isReset: isReset,
        displayCount: displayCount,
      );
}

extension RepaymentPromotionVoucherV2Mapper on api_models.VNRepaymentPromotionVoucherV2 {
  RepaymentPromotionVoucher toDomain() => RepaymentPromotionVoucher(
        firstRepaymentAdoption: firstRepaymentAdoption?.toDomain(),
        availableVouchers: availableVouchers?.toDomain(),
      );
}

extension RepaymentPromotionVoucherDataV2ListMapper on List<api_models.VNRepaymentPromotionVoucherDataV2> {
  List<RepaymentVoucher> toDomain() => map((e) => e.toDomain()).toList();
}

extension RepaymentVoucherPromotionFirstAdoption on RepaymentVoucher {
  String get discountAmountKText => _toKText(discountAmount?.toDouble());

  String get minRequiredAmountKText => _toKText(minRequiredAmount?.toDouble());

  String _toKText(num? i) => i == null ? '' : '${i ~/ 1000}k';
}
