import '../../../capp_repayment.dart';

abstract class IAutoDebitStorage {
  // Contract
  Future<AdaContract?> getContract();
  Future<void> removeContract();
  Future<void> setContract(AdaContract contract);

  // Previous payment method
  Future<RepaymentUserPaymentMethod?> getPreviousPaymentMethod();
  Future<void> removePreviousPaymentMethod();
  Future<void> setPreviousPaymentMethod(RepaymentUserPaymentMethod previousPaymentMethod);

  Future<void> setAdaDraftResult(AdaDraftResult result);
  Future<void> removeAdaDraftResult();
  Future<AdaDraftResult?> getAdaDraftResult();

  // Intro screen is only displayed one time
  Future<bool?> shouldDisplayIntro();
  Future<void> setDidDisplayIntro();

  // Contract
  Future<AdaDraftContract?> getAdaDraftContract();
  Future<void> removeAdaDraftContract();
  Future<void> setAdaDraftContract(AdaDraftContract contract);
  Future<void> setAdaFromFlow(AdaFromFlow fromFlow);
  Future<String?> getAdaFromFlow();
}
