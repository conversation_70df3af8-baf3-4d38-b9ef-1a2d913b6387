import 'dart:convert';

import 'package:gma_storage/gma_storage.dart';

import '../../../capp_repayment.dart';

class RepaymentStorage {
  late final GmaStorageProvider storage;

  static const String transactionsResultKey = 'transactionsResultKey';
  static const String previousPaymentMethodKey = 'previousPaymentKey';
  static const String paymentMethodsKey = 'paymentMethodsKey';
  static const String previousMobileBankingAppKey = 'previousMobileBankingAppKey';
  static const String ptpIntroKey = 'ptpIntroKey';
  static const String promotionFirstAdoptionDisplayCountKey = 'promotionFirstAdoptionDisplayCountKey';
  static const String qrBankKey = 'qrBankKey';

  RepaymentStorage({required this.storage});

  // Transactions result
  Future<RepaymentTransactionResult?> getTransactionResult() async {
    try {
      final response = await storage.getMap(transactionsResultKey);
      return response != null ? RepaymentTransactionResult.fromJson(response) : null;
    } on Exception catch (_) {
      return null;
    }
  }

  Future<void> removeTransactionResult() async {
    await storage.delete(transactionsResultKey);
  }

  Future<void> setTransactionsResult(RepaymentTransactionResult transactionResult) async {
    return storage.insert<Map<String, dynamic>>(transactionsResultKey, transactionResult.toJson());
  }

  // Previous payment method
  Future<RepaymentUserPaymentMethod?> getPreviousPaymentMethod() async {
    try {
      final response = await storage.get<String>(previousPaymentMethodKey);
      return response != null ? RepaymentUserPaymentMethod.fromJson(response) : null;
    } on Exception catch (_) {
      return null;
    }
  }

  Future<void> removePreviousPaymentMethod() async {
    await storage.delete(previousPaymentMethodKey);
  }

  Future<void> setPreviousPaymentMethod(RepaymentUserPaymentMethod previousPaymentMethod) async {
    return storage.insert<String>(previousPaymentMethodKey, previousPaymentMethod.toJson());
  }

  // Payment methods
  Future<List<RepaymentUserPaymentMethod>> getPaymentMethods() async {
    try {
      final jsonString = await storage.get<String>(paymentMethodsKey) ?? '';
      if (jsonString.isNotEmpty) {
        final jsonList = jsonDecode(jsonString) as List<dynamic>;

        final paymentMethods = jsonList.map((json) {
          final method = RepaymentUserPaymentMethod.fromJson(json);
          return method;
        }).toList();

        return paymentMethods;
      }
    } on Exception catch (_) {
      return [];
    }
    return [];
  }

  Future<void> removePaymentMethods() async {
    await storage.delete(paymentMethodsKey);
  }

  Future<void> setPaymentMethods(List<RepaymentUserPaymentMethod> paymentMethods) async {
    final json = jsonEncode(paymentMethods.map((e) => e.toJson()).toList());
    if (json.isNotEmpty) {
      return storage.insert<String>(paymentMethodsKey, json);
    }
  }

  // Previous mobile banking app short name
  Future<String?> getPreviousMobileBankingAppShortName() async {
    try {
      return await storage.get<String>(previousMobileBankingAppKey);
    } on Exception catch (_) {
      return null;
    }
  }

  Future<void> removePreviousMobileBankingAppShortName() async {
    await storage.delete(previousMobileBankingAppKey);
  }

  Future<void> setPreviousMobileBankingAppShortName(String previousMobileBankingAppShortName) async {
    return storage.insert<String>(previousMobileBankingAppKey, previousMobileBankingAppShortName);
  }

  // Promise to pay
  Future<bool> getPromiseToPayIntroIsShown(String contractNumber) async {
    final introIsShownkey = '${ptpIntroKey}_$contractNumber';
    try {
      return await storage.get<bool>(introIsShownkey) ?? false;
    } on Exception catch (_) {
      return false;
    }
  }

  Future<void> setPromiseToPayIntroIsShown(String contractNumber) async {
    final introIsShownkey = '${ptpIntroKey}_$contractNumber';
    return storage.insert<bool>(introIsShownkey, true);
  }

  Future<int?> getPromotionFirstAdoptionDisplayCount(String key) async {
    final displayCountKey = '${promotionFirstAdoptionDisplayCountKey}_$key';
    try {
      return storage.get<int>(displayCountKey);
    } on Exception catch (_) {
      return null;
    }
  }

  Future<void> setPromotionFirstAdoptionDisplayCount(String key, int count) async {
    final displayCountKey = '${promotionFirstAdoptionDisplayCountKey}_$key';
    return storage.insert(displayCountKey, count);
  }

  // Qr bank
  Future<RepaymentQrBank?> getQrBank() async {
    try {
      final response = await storage.get<String>(qrBankKey);
      return response != null ? RepaymentQrBank.fromJson(response) : null;
    } on Exception catch (_) {
      return null;
    }
  }

  Future<void> removeQrBank() async {
    await storage.delete(qrBankKey);
  }

  Future<void> setQrBank(RepaymentQrBank bank) async {
    return storage.insert<String>(qrBankKey, bank.toJson());
  }
}

class RepaymentStorageOriginal {
  late final ReactiveStorage storage;

  static const String transactionsResultName = 'transactionsResultName';

  RepaymentStorageOriginal({required this.storage});

  Future<RepaymentTransactionResult?> getTransactionResult() async {
    try {
      final response = await storage.getMap(transactionsResultName);
      return response != null ? RepaymentTransactionResult.fromJson(response) : null;
    } on Exception catch (_) {
      return null;
    }
  }

  Future<void> removeTransactionResult() async {
    await storage.delete(transactionsResultName);
  }

  Future<void> setTransactionsResult(RepaymentTransactionResult transactionResult) async {
    return storage.insert<Map<String, dynamic>>(transactionsResultName, transactionResult.toJson());
  }
}
