import 'package:flutter/material.dart';
import 'package:gma_storage/gma_storage.dart';

import '../../../capp_repayment.dart';

class AutoDebitStorage implements IAutoDebitStorage {
  late final GmaStorageProvider storage;

  static const String adContractKey = 'adContractKey';
  static const String adPaymentMethodKey = 'adPaymentMethodKey';
  static const String adDidDisplayIntroKey = 'adDidDisplayIntroKey';
  static const String adADraftResultKey = 'adADraftResultKey';
  static const String adDraftContractKey = 'adDraftContractKey';
  static const String adFromFlowKey = 'adFromFlowKey';
  AutoDebitStorage({required this.storage});

  // Contract
  @override
  Future<AdaContract?> getContract() async {
    try {
      final response = await storage.getMap(adContractKey);
      return response != null ? AdaContract.fromJson(response) : null;
    } on Exception catch (e) {
      debugPrint('Error: AutoDebitStorage$e');
      return null;
    }
  }

  @override
  Future<void> removeContract() async {
    await storage.delete(adContractKey);
  }

  @override
  Future<void> setContract(AdaContract contract) async {
    final contractJson = contract.toJson();
    return storage.insert<Map<String, dynamic>>(adContractKey, contractJson);
  }

  // Previous payment method
  @override
  Future<RepaymentUserPaymentMethod?> getPreviousPaymentMethod() async {
    try {
      final response = await storage.get<String>(adPaymentMethodKey);
      return response != null ? RepaymentUserPaymentMethod.fromJson(response) : null;
    } on Exception catch (_) {
      return null;
    }
  }

  @override
  Future<void> removePreviousPaymentMethod() async {
    await storage.delete(adPaymentMethodKey);
  }

  @override
  Future<void> setPreviousPaymentMethod(RepaymentUserPaymentMethod previousPaymentMethod) async {
    return storage.insert<String>(adPaymentMethodKey, previousPaymentMethod.toJson());
  }

  // Intro screen is only displayed one time
  @override
  Future<bool?> shouldDisplayIntro() async {
    try {
      final didDisplayIntro = await storage.get<bool>(adDidDisplayIntroKey);
      return didDisplayIntro != true;
    } on Exception catch (_) {
      return null;
    }
  }

  @override
  Future<void> setDidDisplayIntro() async {
    return storage.insert<bool>(adDidDisplayIntroKey, true);
  }

  @override
  Future<void> removeAdaDraftResult() async {
    await storage.delete(adADraftResultKey);
  }

  @override
  Future<void> setAdaDraftResult(AdaDraftResult result) async {
    final contractJson = result.toJson();
    return storage.insert<Map<String, dynamic>>(adADraftResultKey, contractJson);
  }

  @override
  Future<AdaDraftResult?> getAdaDraftResult() async {
    try {
      final response = await storage.getMap(adADraftResultKey);
      return response != null ? AdaDraftResult.fromJson(response) : null;
    } on Exception catch (e) {
      debugPrint('Error: AutoDebitStorage$e');
      return null;
    }
  }

  @override
  Future<AdaDraftContract?> getAdaDraftContract() async {
    try {
      final response = await storage.getMap(adDraftContractKey);
      return response != null ? AdaDraftContract.fromJson(response) : null;
    } on Exception catch (e) {
      debugPrint('Error: AutoDebitStorage$e');
      return null;
    }
  }

  @override
  Future<void> removeAdaDraftContract() async {
    await storage.delete(adDraftContractKey);
  }

  @override
  Future<void> setAdaDraftContract(AdaDraftContract contract) async {
    final contractJson = contract.toJson();
    return storage.insert<Map<String, dynamic>>(adDraftContractKey, contractJson);
  }

  @override
  Future<void> setAdaFromFlow(AdaFromFlow fromFlow) {
    return  storage.insert(adFromFlowKey, fromFlow.name);
  }

  @override
  Future<String?> getAdaFromFlow() async {
    return storage.get(adFromFlowKey);
  }
}
