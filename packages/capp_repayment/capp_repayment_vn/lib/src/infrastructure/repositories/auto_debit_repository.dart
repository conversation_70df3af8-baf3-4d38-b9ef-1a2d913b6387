import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../../capp_repayment.dart';
import '../../index.dart';

class AutoDebitRepository implements IAutoDebitRepository {
  final VnAdaApi adaApi;
  final VnAdaContractApi adaContractApi;
  final IAutoDebitStorage autoDebitStorage;
  final Logger logger;
  final IFeatureFlagRepository featureFlagRepository;
  final ILocalizationRepository localizationRepository;
  final PaymentMethodsApi paymentMethodApi;

  AutoDebitRepository({
    required this.adaApi,
    required this.adaContractApi,
    required this.autoDebitStorage,
    required this.logger,
    required this.featureFlagRepository,
    required this.localizationRepository,
    required this.paymentMethodApi,
  });

  @override
  Future<Either<RepaymentFailure, AdaZaloRegistrationResponse>> createZaloBinding(
    AdaZaloRegistrationRequest request,
  ) async {
    try {
      final response = await adaApi.zaloAdaRegister(request.fromDomain());

      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, AdaOnepayRegistrationResponse>> registerOnepay(
    AdaOnepayRegistrationRequest request,
  ) async {
    try {
      final response = await adaApi.registerAdaOnepay(request.fromDomain());

      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<AdaContract?> getContractFromCache() async {
    return autoDebitStorage.getContract();
  }

  @override
  Future<bool> setContractToCache(AdaContract contract) async {
    try {
      await autoDebitStorage.setContract(contract);
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return false;
    }
  }

  // Payment methods
  @override
  Future<List<RepaymentUserPaymentMethod>> getBackupUserPaymentMethods({
    required L10nCappRepayment l10nCappRepayment,
  }) async {
    final methodMapList = <RepaymentUserPaymentMethod, String>{
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.atm.name,
        gmaId: RepaymentPaymentMethod.onepay.getId(),
        badge: '',
        iconUrl: RepaymentPaymentMethod.onepay.iconUrl,
        title: l10nCappRepayment.repaymentAdaOnepay,
      ): FeatureFlag.autoDebitOnepay,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.ewallet.name,
        gmaId: RepaymentPaymentMethod.zalo.getId(),
        description: RepaymentPaymentMethod.zalo.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.zalo.iconUrl,
        title: RepaymentPaymentMethod.zalo.getName(l10nCappRepayment),
      ): FeatureFlag.autoDebitZalopay,
    }..removeWhere((key, value) => !featureFlagRepository.isEnabledCached(value));
    final enabledPaymentMethods = methodMapList.keys.toList();
    return enabledPaymentMethods;
  }

  @override
  Future<RepaymentUserPaymentMethod?> getPreviousPaymentMethodFromCache() async {
    final previousPaymentMethod = await autoDebitStorage.getPreviousPaymentMethod();

    if (previousPaymentMethod?.gmaId == RepaymentAdaPaymentMethod.zalo.getId() &&
        featureFlagRepository.isEnabledCached(FeatureFlag.autoDebitZalopay)) {
      return previousPaymentMethod;
    } else if (previousPaymentMethod?.gmaId == RepaymentAdaPaymentMethod.onepay.getId() &&
        featureFlagRepository.isEnabledCached(FeatureFlag.autoDebitOnepay)) {
      return previousPaymentMethod;
    }

    return null;
  }

  @override
  Future<bool> setPaymentMethodToCache(RepaymentUserPaymentMethod? paymentMethod) async {
    try {
      if (paymentMethod == null) {
        await autoDebitStorage.removePreviousPaymentMethod();
      } else {
        await autoDebitStorage.setPreviousPaymentMethod(paymentMethod);
      }
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return false;
    }
  }

  @override
  Future<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>> fetchUserPaymentMethods() async {
    try {
      final currentLang = await _getSelectedLanguageCodeOrDefault();
      final response = await paymentMethodApi.paymentMethodsForUserGet(currentLang);

      final userPaymentMethods = response.items?.map((e) => e.toDomain()).toList() ?? [];
      // Currently only allow onepay and zalo
      final filteredUserPaymentMethods = userPaymentMethods
          .where(
            (e) => e.gmaId == RepaymentPaymentMethod.onepay.getId() || e.gmaId == RepaymentPaymentMethod.zalo.getId(),
          )
          .toList();

      final methodMapList = <RepaymentUserPaymentMethod, String>{};
      for (final method in filteredUserPaymentMethods) {
        methodMapList[method] = RepaymentUserPaymentMethod.getFeatureFlagById(gmaId: method.gmaId, isAdFlow: true);
      }
      methodMapList.removeWhere((key, value) => !featureFlagRepository.isEnabledCached(value));

      final enabledPaymentMethods = methodMapList.keys.toList();
      return right(enabledPaymentMethods);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  // Enrolled ADA contracts
  @override
  Future<Either<RepaymentFailure, List<AdaEnrolledContract>>> fetchAdaEnrolledContracts() async {
    try {
      final response = await adaApi.getAdaList();
      return right(response.adaList?.map((e) => e.toDomain()).toList() ?? []);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, bool>> cancelAdaZalo({String? adaId, String? flowId}) async {
    try {
      await adaApi.cancelZalo(
        ZaloAdaCancellationRequestDTO(
          adaId: adaId,
          flowInstanceId: flowId,
        ),
      );
      return right(true);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  // Intro screen
  @override
  Future<bool?> shouldDisplayIntro() async {
    return autoDebitStorage.shouldDisplayIntro();
  }

  @override
  Future<void> setDidDisplayIntro() async {
    await autoDebitStorage.setDidDisplayIntro();
  }

  @override
  Future<Either<RepaymentFailure, List<AdaContract>>> getAdaContracts() async {
    try {
      final response = await adaContractApi.adaContractList();
      return right(response.toDomain().parseToAdaContracts());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, List<AdaEligibleContract>>> getAdaEligibleContracts() async {
    try {
      final response = await adaContractApi.adaContractEligibility();
      return right(response.toDomain().parseToAdaEligibleContracts());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, AdaOnepayCancelResponse>> cancelAdaOnePay({
    String? adaId,
    String? flowId,
    required String redirectLink,
  }) async {
    try {
      final request = AdaOnepayCancelRequest(redirectLink: redirectLink, adaId: adaId, flowId: flowId,);
      final response = await adaApi.cancelAdaOnepay(request.fromDomain());
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  Future<String> _getSelectedLanguageCodeOrDefault() async {
    final currentLocale = await localizationRepository.loadSelectedLocale();
    final currentLang = currentLocale?.languageCode ?? 'vi';

    return currentLang;
  }

  @override
  Future<AdaDraftResult?> getAdaDraftResult() {
    return autoDebitStorage.getAdaDraftResult();
  }

  @override
  Future<void> setAdaDraftResult(AdaDraftResult result) {
    return autoDebitStorage.setAdaDraftResult(result);
  }

  @override
  Future<void> removeAdaDraftResult() {
    return autoDebitStorage.removeAdaDraftResult();
  }

  @override
  Future<Either<RepaymentFailure, AdaDraftZaloRegistrationResponse>> createDraftZaloBinding(AdaDraftZaloRegistrationRequest request) async {
    try {
      final response = await adaApi.zaloDraftRegistration(request.fromDomain());

      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<AdaDraftContract?> getAdaDraftContract() async {
    return autoDebitStorage.getAdaDraftContract();
  }

  @override
  Future<bool> setAdaDraftContractToCache(AdaDraftContract adaDraftContract) async {
    try {
      await autoDebitStorage.setAdaDraftContract(adaDraftContract);
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return false;
    }
  }

  @override
  Future<void> removeAdaDraftContract() {
    return autoDebitStorage.removeAdaDraftContract();
  }

  @override
  Future<AdaFromFlow?> getAdaFromFlow() async {
    final adaFromFlowValue = await autoDebitStorage.getAdaFromFlow();
    if(adaFromFlowValue?.isNotEmpty ?? false) {
      return enumValueFromString(adaFromFlowValue, AdaFromFlow.values);
    }
    return null;
  }

  @override
  Future<void> setAdaFromFlow(AdaFromFlow fromFlow) {
    return autoDebitStorage.setAdaFromFlow(fromFlow);
  }

  @override
  Future<Either<RepaymentFailure, AdaContractDetail>> getAdaContractDetail({required String id}) async {
    try {
      final response = await adaApi.getAdaById(id);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, AdaDraftOnepayRegistrationResponse>> registerDraftOnepay(
      AdaDraftOnepayRegistrationRequest request,) async {
    try {
      final response = await adaApi.onepayDraftRegistration(request.fromDomain());
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in auto debit repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }
}