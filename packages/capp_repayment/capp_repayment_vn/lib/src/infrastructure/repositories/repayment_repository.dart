import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../../../capp_repayment.dart';
import '../../domain/repositories/index.dart' as vn_repositories;
import '../../index.dart';
import '../mappers/repayment_contract_ptp_mapper.dart';

class RepaymentRepository implements vn_repositories.IRepaymentRepository {
  final VnRepaymentTxnApi txnApi;
  final RepaymentApi api;
  final RepaymentStorage repaymentStorage;
  final Logger logger;
  final VnRepaymentPtpApi ptpApi;
  final PaymentMethodsApi paymentMethodApi;
  final IFeatureFlagRepository featureFlagRepository;
  final ILocalizationRepository localizationRepository;

  RepaymentRepository({
    required this.txnApi,
    required this.api,
    required this.repaymentStorage,
    required this.logger,
    required this.ptpApi,
    required this.paymentMethodApi,
    required this.featureFlagRepository,
    required this.localizationRepository,
  });

  @override
  Future<Either<RepaymentFailure, RepaymentContractListResponse>> getContracts() async {
    final isContractSourceFromPcs =
        featureFlagRepository.isEnabledCached(FeatureFlag.repaymentContractSourceGetFromPcs);

    try {
      if (isContractSourceFromPcs) {
        final response = await api.getRepaymentContractsV2();

        return right(response.toDomain());
      } else {
        final response = await api.getRepaymentContracts();

        return right(response.toDomain());
      }
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>> fetchUserPaymentMethods() async {
    try {
      final currentLang = await _getSelectedLanguageCodeOrDefault();
      final response = await paymentMethodApi.paymentMethodsForUserGet(currentLang);

      final userPaymentMethods = response.items?.map((e) => e.toDomain()).toList() ?? [];

      final methodMapList = <RepaymentUserPaymentMethod, String>{};
      for (final method in userPaymentMethods) {
        methodMapList[method] = RepaymentUserPaymentMethod.getFeatureFlagById(gmaId: method.gmaId);
      }
      methodMapList.removeWhere((key, value) => !featureFlagRepository.isEnabledCached(value));

      final enabledPaymentMethods = methodMapList.keys.toList();
      return right(enabledPaymentMethods);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<List<RepaymentUserPaymentMethod>> getBackupUserPaymentMethods({
    required L10nCappRepayment l10nCappRepayment,
  }) async {
    final methodMapList = <RepaymentUserPaymentMethod, String>{
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.ewallet.name,
        gmaId: RepaymentPaymentMethod.momo.getId(),
        description: RepaymentPaymentMethod.momo.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.momo.iconUrl,
        title: RepaymentPaymentMethod.momo.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentMomo,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.ewallet.name,
        gmaId: RepaymentPaymentMethod.shopee.getId(),
        description: RepaymentPaymentMethod.shopee.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.shopee.iconUrl,
        title: RepaymentPaymentMethod.shopee.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentShopeepay,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.ewallet.name,
        gmaId: RepaymentPaymentMethod.viettel.getId(),
        description: RepaymentPaymentMethod.viettel.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.viettel.iconUrl,
        title: RepaymentPaymentMethod.viettel.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentViettelmoney,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.ewallet.name,
        gmaId: RepaymentPaymentMethod.zalo.getId(),
        description: RepaymentPaymentMethod.zalo.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.zalo.iconUrl,
        title: RepaymentPaymentMethod.zalo.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentZalopay,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.onlineBanking.name,
        gmaId: RepaymentPaymentMethod.vnpay.getId(),
        description: RepaymentPaymentMethod.vnpay.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.vnpay.iconUrl,
        title: RepaymentPaymentMethod.vnpay.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentVnpay,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.atm.name,
        gmaId: RepaymentPaymentMethod.onepay.getId(),
        description: RepaymentPaymentMethod.onepay.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.onepay.iconUrl,
        title: RepaymentPaymentMethod.onepay.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentOnepay,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.bankTransfer.name,
        gmaId: RepaymentPaymentMethod.bankTransferQr.getId(),
        description: RepaymentPaymentMethod.bankTransferQr.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.bankTransferQr.iconUrl,
        title: RepaymentPaymentMethod.bankTransferQr.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentBankTransferQr,
      RepaymentUserPaymentMethod(
        category: RepaymentPaymentCategory.bankTransfer.name,
        gmaId: RepaymentPaymentMethod.mobileBanking.getId(),
        description: RepaymentPaymentMethod.mobileBanking.getDescription(l10nCappRepayment),
        badge: '',
        iconUrl: RepaymentPaymentMethod.mobileBanking.iconUrl,
        title: RepaymentPaymentMethod.mobileBanking.getName(l10nCappRepayment),
      ): FeatureFlag.repaymentBankTransferApp2App,
    }..removeWhere((key, value) => !featureFlagRepository.isEnabledCached(value));
    final enabledPaymentMethods = methodMapList.keys.toList();
    return enabledPaymentMethods;
  }

  @override
  Future<Either<RepaymentFailure, RepaymentOnePayTransaction>> createOnePayTransaction(
    RepaymentOnePayTransactionRequest request,
  ) async {
    try {
      final response =
          await txnApi.createOnepayRepaymentTransaction(RepaymentOnePayTransactionRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentMomoTransaction>> createMomoTransactionV1(
    RepaymentMomoTransactionRequest request,
  ) async {
    try {
      final response =
          await txnApi.createMomoRepaymentTransaction(RepaymentMomoTransactionRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentMomoTransaction>> createMomoTransaction(
    RepaymentMomoTransactionRequest request,
  ) async {
    try {
      final response =
          await txnApi.createMomoRepaymentTransactionV2(RepaymentMomoTransactionRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentTransaction>> getTransactionResult(
    String transactionId,
  ) async {
    try {
      final response = await txnApi.getRepaymentTransaction(transactionId);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<RepaymentTransactionResult?> getTransactionResultFromCache() async {
    return repaymentStorage.getTransactionResult();
  }

  @override
  Future<bool> setTransactionResultToCache(RepaymentTransactionResult result) async {
    try {
      await repaymentStorage.setTransactionsResult(result);
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return false;
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentVnPayTransaction>> createVnPayTransaction(
    RepaymentVnPayTransactionRequest request,
  ) async {
    try {
      final response =
          await txnApi.createVnpayRepaymentTransaction(RepaymentVnPayTransactionRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentShopeePayTransaction>> createShopeePayTransaction(
    RepaymentShopeePayTransactionRequest request,
  ) async {
    try {
      final response = await txnApi
          .createShopeepayRepaymentTransaction(RepaymentShopeePayTransactionRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in shopeepay create transaction', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentZaloPayTransaction>> createZaloPayTransaction(
    RepaymentZaloPayTransactionRequest request,
  ) async {
    try {
      final response =
          await txnApi.createZalopayRepaymentTransaction(RepaymentZaloPayTransactionRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in zalopay create transaction', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentViettelMoneyTransaction>> createViettelMoneyTransaction(
    RepaymentViettelMoneyTransactionRequest request,
  ) async {
    try {
      final response = await txnApi
          .createViettelRepaymentTransaction(RepaymentViettelMoneyTransactionRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, bool>> cancelTransaction(String uuid) async {
    try {
      await txnApi.cancelTransaction(uuid);
      return right(true);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentContractVirtualAccount>> getVirtualAccount({
    required String contractNumber,
    required num selectedAmount,
    String? voucherCode,
  }) async {
    try {
      final response = await api.getVirtualAccountV2(contractNumber, selectedAmount, voucherCode: voucherCode);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  // Payment methods
  @override
  Future<RepaymentUserPaymentMethod?> getPreviousPaymentMethodFromCache() async {
    final previousPaymentMethod = await repaymentStorage.getPreviousPaymentMethod();
    final previousPaymentMethodId = previousPaymentMethod?.gmaId ?? '';
    if (previousPaymentMethodId.isNotEmpty) {
      final ffName = RepaymentUserPaymentMethod.getFeatureFlagById(gmaId: previousPaymentMethodId);
      if (featureFlagRepository.isEnabledCached(ffName)) {
        return previousPaymentMethod;
      }
    }

    return null;
  }

  @override
  Future<bool> setPaymentMethodToCache(RepaymentUserPaymentMethod? paymentMethod) async {
    try {
      if (paymentMethod == null) {
        await repaymentStorage.removePreviousPaymentMethod();
      } else {
        await repaymentStorage.setPreviousPaymentMethod(paymentMethod);
      }
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return false;
    }
  }

  @override
  Future<bool> setPaymentMethodsToCache({required List<RepaymentUserPaymentMethod> paymentMethods}) async {
    try {
      if (paymentMethods.isEmpty) {
        await repaymentStorage.removePreviousPaymentMethod();
      } else {
        await repaymentStorage.setPaymentMethods(paymentMethods);
      }
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return false;
    }
  }

  @override
  Future<List<RepaymentUserPaymentMethod>?> getPaymentMethodsFromCache() async {
    final paymentMethods = await repaymentStorage.getPaymentMethods();
    final methodMapList = <RepaymentUserPaymentMethod, String>{};

    for (final method in paymentMethods) {
      methodMapList[method] = RepaymentUserPaymentMethod.getFeatureFlagById(gmaId: method.gmaId);
    }

    methodMapList.removeWhere((key, value) => !featureFlagRepository.isEnabledCached(value));

    final enabledPaymentMethods = methodMapList.keys.toList();
    return enabledPaymentMethods;
  }

  @override
  Future<String?> getPreviousMobileBankingAppShortNameFromCache() async {
    return repaymentStorage.getPreviousMobileBankingAppShortName();
  }

  @override
  Future<bool> setMobileBankingAppShortNameToCache(String? bankShortName) async {
    try {
      if (bankShortName == null) {
        await repaymentStorage.removePreviousMobileBankingAppShortName();
      } else {
        await repaymentStorage.setPreviousMobileBankingAppShortName(bankShortName);
      }
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return false;
    }
  }

  @override
  Future<bool> getPromiseToPayIntroIsShown(String contractNumber) async {
    try {
      return await repaymentStorage.getPromiseToPayIntroIsShown(contractNumber);
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return false;
    }
  }

  @override
  Future setPromiseToPayIntroIsShown(String contractNumber) {
    return repaymentStorage.setPromiseToPayIntroIsShown(contractNumber);
  }

  @override
  Future<Either<RepaymentFailure, RepaymentPtpContract>> getPtpEligibility(String contractNumber) async {
    try {
      final response = await ptpApi.getPtpEligibility(contractNumber);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentPtpEvaluation>> submitPtpEvaluation(
    RepaymentPtpEvaluationRequest request,
  ) async {
    try {
      final response = await ptpApi.submitPtpEvaluation(RepaymentPtpEvaluationRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentPtpEvaluation>> getPtpEvaluation(
    String requestId,
  ) async {
    try {
      final response = await ptpApi.getPtpEvaluation(requestId);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  Future<String> _getSelectedLanguageCodeOrDefault() async {
    final currentLocale = await localizationRepository.loadSelectedLocale();
    final currentLang = currentLocale?.languageCode ?? 'vi';

    return currentLang;
  }

  @override
  Future<Either<RepaymentFailure, List<RepaymentQrBank>>> fetchQrBanks({required String virtualAccountNumber}) async {
    try {
      final response = await api.getBanks(virtualAccountNumber);

      final banks = response.data?.map((e) => e.toDomain()).toList() ?? [];

      return right(banks);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<RepaymentQrBank?> getQrBankFromCache() async {
    return repaymentStorage.getQrBank();
  }

  @override
  Future<bool> setQrBankToCache(RepaymentQrBank result) async {
    try {
      await repaymentStorage.setQrBank(result);
      return true;
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return false;
    }
  }
}
