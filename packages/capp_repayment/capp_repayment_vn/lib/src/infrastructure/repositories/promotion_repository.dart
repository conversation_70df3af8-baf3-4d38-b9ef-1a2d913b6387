import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:collection/collection.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as api_models;
import 'package:selfcareapi/selfcareapi.dart';

import '../../../capp_repayment.dart';
import '../../domain/repositories/index.dart' as vn_repositories;

class PromotionRepository implements vn_repositories.IPromotionRepository {
  final Logger logger;
  RepaymentPromotionApi promotionApi;

  PromotionRepository({
    required this.logger,
    required this.promotionApi,
  });

  @override
  Future<Either<RepaymentFailure, List<RepaymentVoucher>>> fetchVouchers(
    RepaymentVoucherRequest request,
  ) async {
    final contractNumber = request.contractNumber;
    final selectedAmount = request.selectedAmount.toDouble().toInt();
    final selectedPaymentMethodString = request.selectedPaymentMethod ?? '';
    try {
      final response = await promotionApi.getRepaymentPromotionVoucher(
        amount: selectedAmount,
        contractNumber: contractNumber,
        paymentMethod: selectedPaymentMethodString.isNotEmpty ? [selectedPaymentMethodString] : [],
      );
      final availableVouchers = response.availableVouchers ?? [];
      return right(availableVouchers.isNotEmpty ? availableVouchers.map((e) => e.toDomain()).toList() : []);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, List<RepaymentVoucher>>> fetchVouchersV2(
    RepaymentVoucherRequest request,
  ) async {
    final contractNumber = request.contractNumber;
    final selectedAmount = request.selectedAmount.toDouble().toInt();
    final selectedPaymentMethodString = request.selectedPaymentMethod ?? '';
    final paymentMethod =
        api_models.PromotionPaymentMethod.values.firstWhereOrNull((e) => e.toJson() == selectedPaymentMethodString);
    try {
      final response = await promotionApi.getRepaymentPromotionVoucherV2(
        amount: selectedAmount,
        contractNumber: contractNumber,
        paymentMethod: paymentMethod != null ? [api_models.PromotionPaymentMethodWrapper(paymentMethod)] : [],
      );
      final availableVouchers = response.availableVouchers ?? [];
      return right(availableVouchers.isNotEmpty ? availableVouchers.map((e) => e.toDomain()).toList() : []);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentVoucherValidation>> validatePromotionVoucher(
    RepaymentVoucherValidationRequest request,
  ) async {
    try {
      final response = await promotionApi
          .validateRepaymentPromotionVoucher(RepaymentVoucherValidationRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentVoucherValidation>> validatePromotionVoucherV2(
    RepaymentVoucherValidationRequest request,
  ) async {
    try {
      final response = await promotionApi
          .validateRepaymentPromotionVoucherV2(RepaymentVoucherValidationRequestV2Mapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentVoucherCalculation>> calculatePromotionVoucher({
    required String voucherCode,
    required Decimal originalAmount,
    Decimal? minRequiredAmount,
  }) async {
    try {
      final request = RepaymentVoucherCalculationRequest(
        voucherCode: voucherCode,
        minRequiredAmount: minRequiredAmount,
        originalAmount: originalAmount,
      );
      final response = await promotionApi
          .calculateRepaymentPromotionVoucher(RepaymentVoucherCalculationRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentPromotionTransaction>> getPromotionTransactionDetail({
    required String promotionTransactionId,
  }) async {
    try {
      final response = await promotionApi.getRepaymentPromotionTransactionDetails(promotionTransactionId);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentVoucherCancellation>> cancelVoucherReservation({
    required RepaymentVoucherCancellationRequest request,
  }) async {
    try {
      final response = await promotionApi
          .cancelRepaymentPromotionVoucher(RepaymentVoucherCancellationRequestMapper.fromDomain(request));
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }

  @override
  Future<Either<RepaymentFailure, RepaymentPromotionVoucher>> fetchFirstAdoptionVoucher(
    RepaymentVoucherRequest request,
  ) async {
    final contractNumber = request.contractNumber;
    final selectedAmount = request.selectedAmount.toDouble().toInt();
    try {
      final response = await promotionApi.getRepaymentPromotionVoucherV2(
        amount: selectedAmount,
        contractNumber: contractNumber,
        promotionType: 'FIRST_REPAYMENT_ADOPTION',
      );
      final r = response.toDomain();
      return right(r);
    } on DioError catch (e) {
      return left(ApiUtils.parseDioErrorToRepaymentFailure(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const RepaymentFailure.unexpected());
    }
  }
}
