// Prod prefix is used for production environment, the others are used for fake and insider environment

class CappOnePaySettings {
  final String returnUrl;
  final String adRedirectUrl;

  const CappOnePaySettings({
    required this.returnUrl,
    required this.adRedirectUrl,
  });
}

class CappVnPaySettings {
  final String scheme;
  final String tmnCode;
  final String prodTmnCode;

  const CappVnPaySettings({this.scheme = '', this.tmnCode = '', this.prodTmnCode = ''});
}

class CappShopeePaySettings {
  final String returnUrl;

  const CappShopeePaySettings({required this.returnUrl});
}

class CappZaloPaySettings {
  final String redirectUrl;
  final String adRedirectUrl;
  final int appId;

  const CappZaloPaySettings({this.redirectUrl = '', this.adRedirectUrl = '', this.appId = 0});
}

class CappViettelMoneySettings {
  final String returnUrl;

  const CappViettelMoneySettings({required this.returnUrl});
}

class CappBaoKimSettings {
  final String deeplink;

  const CappBaoKimSettings({required this.deeplink});
}

class CappRepaymentSettingsVn {
  final CappOnePaySettings onePaySettings;
  final CappVnPaySettings vnPaySettings;
  final CappShopeePaySettings shopeePaySettings;
  final CappZaloPaySettings zaloPaySettings;
  final CappViettelMoneySettings viettelMoneySettings;
  final CappBaoKimSettings baoKimSettings;

  const CappRepaymentSettingsVn({
    required this.onePaySettings,
    required this.vnPaySettings,
    required this.shopeePaySettings,
    required this.zaloPaySettings,
    required this.viettelMoneySettings,
    required this.baoKimSettings,
  });
}

class CappRepaymentSettingsProdVn extends CappRepaymentSettingsVn {
  const CappRepaymentSettingsProdVn()
      : super(
          // OnePay
          onePaySettings: const CappOnePaySettings(
            returnUrl: 'https://onepay_homecredit.vn',
            adRedirectUrl: 'hcvn://onepay/auto_debit/register',
          ),

          // VnPay
          vnPaySettings: const CappVnPaySettings(scheme: 'hcvnvnpay', tmnCode: 'HOMEGAPP', prodTmnCode: 'HOMEGAPP'),

          // ShopeePay
          shopeePaySettings: const CappShopeePaySettings(returnUrl: 'hcvn://repayment/result/shopeepay'),

          // ZaloPay
          zaloPaySettings: const CappZaloPaySettings(
            redirectUrl: 'hcvn://repayment/result/zalopay',
            adRedirectUrl: 'hcvn://auto_debit/result/zalopay',
            appId: 1559,
          ),

          // ViettelMoney
          viettelMoneySettings: const CappViettelMoneySettings(returnUrl: 'viettelmoney_homecredit.vn'),

          // Bao kim
          baoKimSettings: const CappBaoKimSettings(deeplink: 'https://devtest.baokim.vn:9229'),
        );
}

class CappRepaymentSettingsFakeVn extends CappRepaymentSettingsVn {
  const CappRepaymentSettingsFakeVn()
      : super(
          // OnePay
          onePaySettings: const CappOnePaySettings(
            returnUrl: 'https://onepay_homecredit.vn',
            adRedirectUrl: 'hcvn://onepay/auto_debit/register',
          ),

          // VnPay
          vnPaySettings: const CappVnPaySettings(scheme: 'hcvnvnpay', tmnCode: 'HOMEGAPP'),

          // ShopeePay
          shopeePaySettings: const CappShopeePaySettings(returnUrl: 'hcvn://repayment/result/shopeepay'),

          // ZaloPay
          zaloPaySettings: const CappZaloPaySettings(
            redirectUrl: 'hcvn://repayment/result/zalopay',
            adRedirectUrl: 'hcvn://auto_debit/result/zalopay',
            appId: 1559,
          ),

          // ViettelMoney
          viettelMoneySettings: const CappViettelMoneySettings(returnUrl: 'viettelmoney_homecredit.vn'),

          // Bao kim
          baoKimSettings: const CappBaoKimSettings(deeplink: 'https://devtest.baokim.vn:9229'),
        );
}
