// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_bank_transfer_qr_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentBankTransferQrEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBankTransferQrEventCopyWith<$Res> {
  factory $RepaymentBankTransferQrEventCopyWith(
          RepaymentBankTransferQrEvent value,
          $Res Function(RepaymentBankTransferQrEvent) then) =
      _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
          RepaymentBankTransferQrEvent>;
}

/// @nodoc
class _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        $Val extends RepaymentBankTransferQrEvent>
    implements $RepaymentBankTransferQrEventCopyWith<$Res> {
  _$RepaymentBankTransferQrEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {RepaymentContract selectedContract,
      Decimal? selectedAmount,
      RepaymentContractVirtualAccount contractVirtualAccount});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedContract = null,
    Object? selectedAmount = freezed,
    Object? contractVirtualAccount = null,
  }) {
    return _then(_$_Initialize(
      null == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract,
      freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      null == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      this.selectedContract, this.selectedAmount, this.contractVirtualAccount);

  @override
  final RepaymentContract selectedContract;
  @override
  final Decimal? selectedAmount;
  @override
  final RepaymentContractVirtualAccount contractVirtualAccount;

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.initialize(selectedContract: $selectedContract, selectedAmount: $selectedAmount, contractVirtualAccount: $contractVirtualAccount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.contractVirtualAccount, contractVirtualAccount) ||
                other.contractVirtualAccount == contractVirtualAccount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, selectedContract, selectedAmount, contractVirtualAccount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return initialize(selectedContract, selectedAmount, contractVirtualAccount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return initialize?.call(
        selectedContract, selectedAmount, contractVirtualAccount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(
          selectedContract, selectedAmount, contractVirtualAccount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentBankTransferQrEvent {
  const factory _Initialize(
          final RepaymentContract selectedContract,
          final Decimal? selectedAmount,
          final RepaymentContractVirtualAccount contractVirtualAccount) =
      _$_Initialize;

  RepaymentContract get selectedContract;
  Decimal? get selectedAmount;
  RepaymentContractVirtualAccount get contractVirtualAccount;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_UpdateQrImageFilePathCopyWith<$Res> {
  factory _$$_UpdateQrImageFilePathCopyWith(_$_UpdateQrImageFilePath value,
          $Res Function(_$_UpdateQrImageFilePath) then) =
      __$$_UpdateQrImageFilePathCopyWithImpl<$Res>;
  @useResult
  $Res call({String path});
}

/// @nodoc
class __$$_UpdateQrImageFilePathCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        _$_UpdateQrImageFilePath>
    implements _$$_UpdateQrImageFilePathCopyWith<$Res> {
  __$$_UpdateQrImageFilePathCopyWithImpl(_$_UpdateQrImageFilePath _value,
      $Res Function(_$_UpdateQrImageFilePath) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? path = null,
  }) {
    return _then(_$_UpdateQrImageFilePath(
      null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_UpdateQrImageFilePath implements _UpdateQrImageFilePath {
  const _$_UpdateQrImageFilePath(this.path);

  @override
  final String path;

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.updateQrImageFilePath(path: $path)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UpdateQrImageFilePath &&
            (identical(other.path, path) || other.path == path));
  }

  @override
  int get hashCode => Object.hash(runtimeType, path);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UpdateQrImageFilePathCopyWith<_$_UpdateQrImageFilePath> get copyWith =>
      __$$_UpdateQrImageFilePathCopyWithImpl<_$_UpdateQrImageFilePath>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return updateQrImageFilePath(path);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return updateQrImageFilePath?.call(path);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (updateQrImageFilePath != null) {
      return updateQrImageFilePath(path);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return updateQrImageFilePath(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return updateQrImageFilePath?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (updateQrImageFilePath != null) {
      return updateQrImageFilePath(this);
    }
    return orElse();
  }
}

abstract class _UpdateQrImageFilePath implements RepaymentBankTransferQrEvent {
  const factory _UpdateQrImageFilePath(final String path) =
      _$_UpdateQrImageFilePath;

  String get path;
  @JsonKey(ignore: true)
  _$$_UpdateQrImageFilePathCopyWith<_$_UpdateQrImageFilePath> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SaveQrCodeToGalleryCopyWith<$Res> {
  factory _$$_SaveQrCodeToGalleryCopyWith(_$_SaveQrCodeToGallery value,
          $Res Function(_$_SaveQrCodeToGallery) then) =
      __$$_SaveQrCodeToGalleryCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String qrUrl,
      bool? isDownloadBeforeQuit,
      bool? shouldOpenBankListAferDownloadSuccess});
}

/// @nodoc
class __$$_SaveQrCodeToGalleryCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        _$_SaveQrCodeToGallery>
    implements _$$_SaveQrCodeToGalleryCopyWith<$Res> {
  __$$_SaveQrCodeToGalleryCopyWithImpl(_$_SaveQrCodeToGallery _value,
      $Res Function(_$_SaveQrCodeToGallery) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? qrUrl = null,
    Object? isDownloadBeforeQuit = freezed,
    Object? shouldOpenBankListAferDownloadSuccess = freezed,
  }) {
    return _then(_$_SaveQrCodeToGallery(
      qrUrl: null == qrUrl
          ? _value.qrUrl
          : qrUrl // ignore: cast_nullable_to_non_nullable
              as String,
      isDownloadBeforeQuit: freezed == isDownloadBeforeQuit
          ? _value.isDownloadBeforeQuit
          : isDownloadBeforeQuit // ignore: cast_nullable_to_non_nullable
              as bool?,
      shouldOpenBankListAferDownloadSuccess: freezed ==
              shouldOpenBankListAferDownloadSuccess
          ? _value.shouldOpenBankListAferDownloadSuccess
          : shouldOpenBankListAferDownloadSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_SaveQrCodeToGallery implements _SaveQrCodeToGallery {
  const _$_SaveQrCodeToGallery(
      {required this.qrUrl,
      this.isDownloadBeforeQuit,
      this.shouldOpenBankListAferDownloadSuccess});

  @override
  final String qrUrl;
  @override
  final bool? isDownloadBeforeQuit;
  @override
  final bool? shouldOpenBankListAferDownloadSuccess;

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.saveQrCodeToGallery(qrUrl: $qrUrl, isDownloadBeforeQuit: $isDownloadBeforeQuit, shouldOpenBankListAferDownloadSuccess: $shouldOpenBankListAferDownloadSuccess)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SaveQrCodeToGallery &&
            (identical(other.qrUrl, qrUrl) || other.qrUrl == qrUrl) &&
            (identical(other.isDownloadBeforeQuit, isDownloadBeforeQuit) ||
                other.isDownloadBeforeQuit == isDownloadBeforeQuit) &&
            (identical(other.shouldOpenBankListAferDownloadSuccess,
                    shouldOpenBankListAferDownloadSuccess) ||
                other.shouldOpenBankListAferDownloadSuccess ==
                    shouldOpenBankListAferDownloadSuccess));
  }

  @override
  int get hashCode => Object.hash(runtimeType, qrUrl, isDownloadBeforeQuit,
      shouldOpenBankListAferDownloadSuccess);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SaveQrCodeToGalleryCopyWith<_$_SaveQrCodeToGallery> get copyWith =>
      __$$_SaveQrCodeToGalleryCopyWithImpl<_$_SaveQrCodeToGallery>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return saveQrCodeToGallery(
        qrUrl, isDownloadBeforeQuit, shouldOpenBankListAferDownloadSuccess);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return saveQrCodeToGallery?.call(
        qrUrl, isDownloadBeforeQuit, shouldOpenBankListAferDownloadSuccess);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (saveQrCodeToGallery != null) {
      return saveQrCodeToGallery(
          qrUrl, isDownloadBeforeQuit, shouldOpenBankListAferDownloadSuccess);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return saveQrCodeToGallery(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return saveQrCodeToGallery?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (saveQrCodeToGallery != null) {
      return saveQrCodeToGallery(this);
    }
    return orElse();
  }
}

abstract class _SaveQrCodeToGallery implements RepaymentBankTransferQrEvent {
  const factory _SaveQrCodeToGallery(
          {required final String qrUrl,
          final bool? isDownloadBeforeQuit,
          final bool? shouldOpenBankListAferDownloadSuccess}) =
      _$_SaveQrCodeToGallery;

  String get qrUrl;
  bool? get isDownloadBeforeQuit;
  bool? get shouldOpenBankListAferDownloadSuccess;
  @JsonKey(ignore: true)
  _$$_SaveQrCodeToGalleryCopyWith<_$_SaveQrCodeToGallery> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ShareQrCodeCopyWith<$Res> {
  factory _$$_ShareQrCodeCopyWith(
          _$_ShareQrCode value, $Res Function(_$_ShareQrCode) then) =
      __$$_ShareQrCodeCopyWithImpl<$Res>;
  @useResult
  $Res call({String qrUrl});
}

/// @nodoc
class __$$_ShareQrCodeCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res, _$_ShareQrCode>
    implements _$$_ShareQrCodeCopyWith<$Res> {
  __$$_ShareQrCodeCopyWithImpl(
      _$_ShareQrCode _value, $Res Function(_$_ShareQrCode) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? qrUrl = null,
  }) {
    return _then(_$_ShareQrCode(
      qrUrl: null == qrUrl
          ? _value.qrUrl
          : qrUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_ShareQrCode implements _ShareQrCode {
  const _$_ShareQrCode({required this.qrUrl});

  @override
  final String qrUrl;

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.shareQrCode(qrUrl: $qrUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShareQrCode &&
            (identical(other.qrUrl, qrUrl) || other.qrUrl == qrUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, qrUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShareQrCodeCopyWith<_$_ShareQrCode> get copyWith =>
      __$$_ShareQrCodeCopyWithImpl<_$_ShareQrCode>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return shareQrCode(qrUrl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return shareQrCode?.call(qrUrl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (shareQrCode != null) {
      return shareQrCode(qrUrl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return shareQrCode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return shareQrCode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (shareQrCode != null) {
      return shareQrCode(this);
    }
    return orElse();
  }
}

abstract class _ShareQrCode implements RepaymentBankTransferQrEvent {
  const factory _ShareQrCode({required final String qrUrl}) = _$_ShareQrCode;

  String get qrUrl;
  @JsonKey(ignore: true)
  _$$_ShareQrCodeCopyWith<_$_ShareQrCode> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ClearShareStatusCopyWith<$Res> {
  factory _$$_ClearShareStatusCopyWith(
          _$_ClearShareStatus value, $Res Function(_$_ClearShareStatus) then) =
      __$$_ClearShareStatusCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ClearShareStatusCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        _$_ClearShareStatus> implements _$$_ClearShareStatusCopyWith<$Res> {
  __$$_ClearShareStatusCopyWithImpl(
      _$_ClearShareStatus _value, $Res Function(_$_ClearShareStatus) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ClearShareStatus implements _ClearShareStatus {
  const _$_ClearShareStatus();

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.clearShareStatus()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ClearShareStatus);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return clearShareStatus();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return clearShareStatus?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (clearShareStatus != null) {
      return clearShareStatus();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return clearShareStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return clearShareStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (clearShareStatus != null) {
      return clearShareStatus(this);
    }
    return orElse();
  }
}

abstract class _ClearShareStatus implements RepaymentBankTransferQrEvent {
  const factory _ClearShareStatus() = _$_ClearShareStatus;
}

/// @nodoc
abstract class _$$_ClearDownloadStatusCopyWith<$Res> {
  factory _$$_ClearDownloadStatusCopyWith(_$_ClearDownloadStatus value,
          $Res Function(_$_ClearDownloadStatus) then) =
      __$$_ClearDownloadStatusCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ClearDownloadStatusCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        _$_ClearDownloadStatus>
    implements _$$_ClearDownloadStatusCopyWith<$Res> {
  __$$_ClearDownloadStatusCopyWithImpl(_$_ClearDownloadStatus _value,
      $Res Function(_$_ClearDownloadStatus) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ClearDownloadStatus implements _ClearDownloadStatus {
  const _$_ClearDownloadStatus();

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.clearDownloadStatus()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ClearDownloadStatus);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return clearDownloadStatus();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return clearDownloadStatus?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (clearDownloadStatus != null) {
      return clearDownloadStatus();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return clearDownloadStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return clearDownloadStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (clearDownloadStatus != null) {
      return clearDownloadStatus(this);
    }
    return orElse();
  }
}

abstract class _ClearDownloadStatus implements RepaymentBankTransferQrEvent {
  const factory _ClearDownloadStatus() = _$_ClearDownloadStatus;
}

/// @nodoc
abstract class _$$_CheckVoucherReservationCopyWith<$Res> {
  factory _$$_CheckVoucherReservationCopyWith(_$_CheckVoucherReservation value,
          $Res Function(_$_CheckVoucherReservation) then) =
      __$$_CheckVoucherReservationCopyWithImpl<$Res>;
  @useResult
  $Res call({String promotionTransactionId, bool? isBackToHome});
}

/// @nodoc
class __$$_CheckVoucherReservationCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        _$_CheckVoucherReservation>
    implements _$$_CheckVoucherReservationCopyWith<$Res> {
  __$$_CheckVoucherReservationCopyWithImpl(_$_CheckVoucherReservation _value,
      $Res Function(_$_CheckVoucherReservation) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? promotionTransactionId = null,
    Object? isBackToHome = freezed,
  }) {
    return _then(_$_CheckVoucherReservation(
      promotionTransactionId: null == promotionTransactionId
          ? _value.promotionTransactionId
          : promotionTransactionId // ignore: cast_nullable_to_non_nullable
              as String,
      isBackToHome: freezed == isBackToHome
          ? _value.isBackToHome
          : isBackToHome // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_CheckVoucherReservation implements _CheckVoucherReservation {
  const _$_CheckVoucherReservation(
      {required this.promotionTransactionId, this.isBackToHome});

  @override
  final String promotionTransactionId;
  @override
  final bool? isBackToHome;

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.checkVoucherReservation(promotionTransactionId: $promotionTransactionId, isBackToHome: $isBackToHome)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckVoucherReservation &&
            (identical(other.promotionTransactionId, promotionTransactionId) ||
                other.promotionTransactionId == promotionTransactionId) &&
            (identical(other.isBackToHome, isBackToHome) ||
                other.isBackToHome == isBackToHome));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, promotionTransactionId, isBackToHome);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CheckVoucherReservationCopyWith<_$_CheckVoucherReservation>
      get copyWith =>
          __$$_CheckVoucherReservationCopyWithImpl<_$_CheckVoucherReservation>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return checkVoucherReservation(promotionTransactionId, isBackToHome);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return checkVoucherReservation?.call(promotionTransactionId, isBackToHome);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (checkVoucherReservation != null) {
      return checkVoucherReservation(promotionTransactionId, isBackToHome);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return checkVoucherReservation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return checkVoucherReservation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (checkVoucherReservation != null) {
      return checkVoucherReservation(this);
    }
    return orElse();
  }
}

abstract class _CheckVoucherReservation
    implements RepaymentBankTransferQrEvent {
  const factory _CheckVoucherReservation(
      {required final String promotionTransactionId,
      final bool? isBackToHome}) = _$_CheckVoucherReservation;

  String get promotionTransactionId;
  bool? get isBackToHome;
  @JsonKey(ignore: true)
  _$$_CheckVoucherReservationCopyWith<_$_CheckVoucherReservation>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_CancelVoucherReservationCopyWith<$Res> {
  factory _$$_CancelVoucherReservationCopyWith(
          _$_CancelVoucherReservation value,
          $Res Function(_$_CancelVoucherReservation) then) =
      __$$_CancelVoucherReservationCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentVoucherCancellationRequest request});
}

/// @nodoc
class __$$_CancelVoucherReservationCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        _$_CancelVoucherReservation>
    implements _$$_CancelVoucherReservationCopyWith<$Res> {
  __$$_CancelVoucherReservationCopyWithImpl(_$_CancelVoucherReservation _value,
      $Res Function(_$_CancelVoucherReservation) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_CancelVoucherReservation(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherCancellationRequest,
    ));
  }
}

/// @nodoc

class _$_CancelVoucherReservation implements _CancelVoucherReservation {
  const _$_CancelVoucherReservation({required this.request});

  @override
  final RepaymentVoucherCancellationRequest request;

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.cancelVoucherReservation(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CancelVoucherReservation &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CancelVoucherReservationCopyWith<_$_CancelVoucherReservation>
      get copyWith => __$$_CancelVoucherReservationCopyWithImpl<
          _$_CancelVoucherReservation>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return cancelVoucherReservation(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return cancelVoucherReservation?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (cancelVoucherReservation != null) {
      return cancelVoucherReservation(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return cancelVoucherReservation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return cancelVoucherReservation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (cancelVoucherReservation != null) {
      return cancelVoucherReservation(this);
    }
    return orElse();
  }
}

abstract class _CancelVoucherReservation
    implements RepaymentBankTransferQrEvent {
  const factory _CancelVoucherReservation(
          {required final RepaymentVoucherCancellationRequest request}) =
      _$_CancelVoucherReservation;

  RepaymentVoucherCancellationRequest get request;
  @JsonKey(ignore: true)
  _$$_CancelVoucherReservationCopyWith<_$_CancelVoucherReservation>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetPromotionStateCopyWith<$Res> {
  factory _$$_ResetPromotionStateCopyWith(_$_ResetPromotionState value,
          $Res Function(_$_ResetPromotionState) then) =
      __$$_ResetPromotionStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetPromotionStateCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res,
        _$_ResetPromotionState>
    implements _$$_ResetPromotionStateCopyWith<$Res> {
  __$$_ResetPromotionStateCopyWithImpl(_$_ResetPromotionState _value,
      $Res Function(_$_ResetPromotionState) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetPromotionState implements _ResetPromotionState {
  const _$_ResetPromotionState();

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.resetPromotionState()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetPromotionState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return resetPromotionState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return resetPromotionState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (resetPromotionState != null) {
      return resetPromotionState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return resetPromotionState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return resetPromotionState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (resetPromotionState != null) {
      return resetPromotionState(this);
    }
    return orElse();
  }
}

abstract class _ResetPromotionState implements RepaymentBankTransferQrEvent {
  const factory _ResetPromotionState() = _$_ResetPromotionState;
}

/// @nodoc
abstract class _$$_FetchQrBanksCopyWith<$Res> {
  factory _$$_FetchQrBanksCopyWith(
          _$_FetchQrBanks value, $Res Function(_$_FetchQrBanks) then) =
      __$$_FetchQrBanksCopyWithImpl<$Res>;
  @useResult
  $Res call({String virtualAccountNumber});
}

/// @nodoc
class __$$_FetchQrBanksCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrEventCopyWithImpl<$Res, _$_FetchQrBanks>
    implements _$$_FetchQrBanksCopyWith<$Res> {
  __$$_FetchQrBanksCopyWithImpl(
      _$_FetchQrBanks _value, $Res Function(_$_FetchQrBanks) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? virtualAccountNumber = null,
  }) {
    return _then(_$_FetchQrBanks(
      virtualAccountNumber: null == virtualAccountNumber
          ? _value.virtualAccountNumber
          : virtualAccountNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_FetchQrBanks implements _FetchQrBanks {
  const _$_FetchQrBanks({required this.virtualAccountNumber});

  @override
  final String virtualAccountNumber;

  @override
  String toString() {
    return 'RepaymentBankTransferQrEvent.fetchQrBanks(virtualAccountNumber: $virtualAccountNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FetchQrBanks &&
            (identical(other.virtualAccountNumber, virtualAccountNumber) ||
                other.virtualAccountNumber == virtualAccountNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, virtualAccountNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FetchQrBanksCopyWith<_$_FetchQrBanks> get copyWith =>
      __$$_FetchQrBanksCopyWithImpl<_$_FetchQrBanks>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)
        initialize,
    required TResult Function(String path) updateQrImageFilePath,
    required TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)
        saveQrCodeToGallery,
    required TResult Function(String qrUrl) shareQrCode,
    required TResult Function() clearShareStatus,
    required TResult Function() clearDownloadStatus,
    required TResult Function(String promotionTransactionId, bool? isBackToHome)
        checkVoucherReservation,
    required TResult Function(RepaymentVoucherCancellationRequest request)
        cancelVoucherReservation,
    required TResult Function() resetPromotionState,
    required TResult Function(String virtualAccountNumber) fetchQrBanks,
  }) {
    return fetchQrBanks(virtualAccountNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult? Function(String path)? updateQrImageFilePath,
    TResult? Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult? Function(String qrUrl)? shareQrCode,
    TResult? Function()? clearShareStatus,
    TResult? Function()? clearDownloadStatus,
    TResult? Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult? Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult? Function()? resetPromotionState,
    TResult? Function(String virtualAccountNumber)? fetchQrBanks,
  }) {
    return fetchQrBanks?.call(virtualAccountNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract selectedContract,
            Decimal? selectedAmount,
            RepaymentContractVirtualAccount contractVirtualAccount)?
        initialize,
    TResult Function(String path)? updateQrImageFilePath,
    TResult Function(String qrUrl, bool? isDownloadBeforeQuit,
            bool? shouldOpenBankListAferDownloadSuccess)?
        saveQrCodeToGallery,
    TResult Function(String qrUrl)? shareQrCode,
    TResult Function()? clearShareStatus,
    TResult Function()? clearDownloadStatus,
    TResult Function(String promotionTransactionId, bool? isBackToHome)?
        checkVoucherReservation,
    TResult Function(RepaymentVoucherCancellationRequest request)?
        cancelVoucherReservation,
    TResult Function()? resetPromotionState,
    TResult Function(String virtualAccountNumber)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (fetchQrBanks != null) {
      return fetchQrBanks(virtualAccountNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_UpdateQrImageFilePath value)
        updateQrImageFilePath,
    required TResult Function(_SaveQrCodeToGallery value) saveQrCodeToGallery,
    required TResult Function(_ShareQrCode value) shareQrCode,
    required TResult Function(_ClearShareStatus value) clearShareStatus,
    required TResult Function(_ClearDownloadStatus value) clearDownloadStatus,
    required TResult Function(_CheckVoucherReservation value)
        checkVoucherReservation,
    required TResult Function(_CancelVoucherReservation value)
        cancelVoucherReservation,
    required TResult Function(_ResetPromotionState value) resetPromotionState,
    required TResult Function(_FetchQrBanks value) fetchQrBanks,
  }) {
    return fetchQrBanks(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult? Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult? Function(_ShareQrCode value)? shareQrCode,
    TResult? Function(_ClearShareStatus value)? clearShareStatus,
    TResult? Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult? Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult? Function(_CancelVoucherReservation value)?
        cancelVoucherReservation,
    TResult? Function(_ResetPromotionState value)? resetPromotionState,
    TResult? Function(_FetchQrBanks value)? fetchQrBanks,
  }) {
    return fetchQrBanks?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_UpdateQrImageFilePath value)? updateQrImageFilePath,
    TResult Function(_SaveQrCodeToGallery value)? saveQrCodeToGallery,
    TResult Function(_ShareQrCode value)? shareQrCode,
    TResult Function(_ClearShareStatus value)? clearShareStatus,
    TResult Function(_ClearDownloadStatus value)? clearDownloadStatus,
    TResult Function(_CheckVoucherReservation value)? checkVoucherReservation,
    TResult Function(_CancelVoucherReservation value)? cancelVoucherReservation,
    TResult Function(_ResetPromotionState value)? resetPromotionState,
    TResult Function(_FetchQrBanks value)? fetchQrBanks,
    required TResult orElse(),
  }) {
    if (fetchQrBanks != null) {
      return fetchQrBanks(this);
    }
    return orElse();
  }
}

abstract class _FetchQrBanks implements RepaymentBankTransferQrEvent {
  const factory _FetchQrBanks({required final String virtualAccountNumber}) =
      _$_FetchQrBanks;

  String get virtualAccountNumber;
  @JsonKey(ignore: true)
  _$$_FetchQrBanksCopyWith<_$_FetchQrBanks> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentBankTransferQrState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  Decimal? get totalAmount => throw _privateConstructorUsedError;
  RepaymentContract? get selectedContract => throw _privateConstructorUsedError;
  String? get qrImagePath => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  bool get isErrorFetchBankList => throw _privateConstructorUsedError;
  RepaymentContractVirtualAccount? get contractVirtualAccount =>
      throw _privateConstructorUsedError;
  PermissionStatus? get currentSaveQrCodeToGalleryPermissionStatus =>
      throw _privateConstructorUsedError;
  bool? get isDownloadSuccess => throw _privateConstructorUsedError;
  bool? get isEligibleToShare => throw _privateConstructorUsedError;
  LoadingState get downloadOrShareLoadingState =>
      throw _privateConstructorUsedError;
  bool get shouldOpenBankListAferDownloadSuccess =>
      throw _privateConstructorUsedError; // Promotion
  RepaymentPromotionTransaction? get promotionTransaction =>
      throw _privateConstructorUsedError;
  RepaymentVoucherCancellation? get voucherCancellation =>
      throw _privateConstructorUsedError;
  bool? get isDownloadBeforeQuit => throw _privateConstructorUsedError;
  bool? get isBackToHome => throw _privateConstructorUsedError; // Bank
  List<RepaymentQrBank>? get banks => throw _privateConstructorUsedError;
  RepaymentQrBank? get selectedBank => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentPromotionTransaction>>
      get failureOrSuccessCheckVoucherReservation =>
          throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentVoucherCancellation>>
      get failureOrSuccessCancelVoucherReservation =>
          throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<RepaymentQrBank>>>
      get failureOrSuccessFetchQrBank => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentBankTransferQrStateCopyWith<RepaymentBankTransferQrState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBankTransferQrStateCopyWith<$Res> {
  factory $RepaymentBankTransferQrStateCopyWith(
          RepaymentBankTransferQrState value,
          $Res Function(RepaymentBankTransferQrState) then) =
      _$RepaymentBankTransferQrStateCopyWithImpl<$Res,
          RepaymentBankTransferQrState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      Decimal? totalAmount,
      RepaymentContract? selectedContract,
      String? qrImagePath,
      bool isError,
      bool isErrorFetchBankList,
      RepaymentContractVirtualAccount? contractVirtualAccount,
      PermissionStatus? currentSaveQrCodeToGalleryPermissionStatus,
      bool? isDownloadSuccess,
      bool? isEligibleToShare,
      LoadingState downloadOrShareLoadingState,
      bool shouldOpenBankListAferDownloadSuccess,
      RepaymentPromotionTransaction? promotionTransaction,
      RepaymentVoucherCancellation? voucherCancellation,
      bool? isDownloadBeforeQuit,
      bool? isBackToHome,
      List<RepaymentQrBank>? banks,
      RepaymentQrBank? selectedBank,
      Option<Either<RepaymentFailure, RepaymentPromotionTransaction>>
          failureOrSuccessCheckVoucherReservation,
      Option<Either<RepaymentFailure, RepaymentVoucherCancellation>>
          failureOrSuccessCancelVoucherReservation,
      Option<Either<RepaymentFailure, List<RepaymentQrBank>>>
          failureOrSuccessFetchQrBank});
}

/// @nodoc
class _$RepaymentBankTransferQrStateCopyWithImpl<$Res,
        $Val extends RepaymentBankTransferQrState>
    implements $RepaymentBankTransferQrStateCopyWith<$Res> {
  _$RepaymentBankTransferQrStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? totalAmount = freezed,
    Object? selectedContract = freezed,
    Object? qrImagePath = freezed,
    Object? isError = null,
    Object? isErrorFetchBankList = null,
    Object? contractVirtualAccount = freezed,
    Object? currentSaveQrCodeToGalleryPermissionStatus = freezed,
    Object? isDownloadSuccess = freezed,
    Object? isEligibleToShare = freezed,
    Object? downloadOrShareLoadingState = null,
    Object? shouldOpenBankListAferDownloadSuccess = null,
    Object? promotionTransaction = freezed,
    Object? voucherCancellation = freezed,
    Object? isDownloadBeforeQuit = freezed,
    Object? isBackToHome = freezed,
    Object? banks = freezed,
    Object? selectedBank = freezed,
    Object? failureOrSuccessCheckVoucherReservation = null,
    Object? failureOrSuccessCancelVoucherReservation = null,
    Object? failureOrSuccessFetchQrBank = null,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      qrImagePath: freezed == qrImagePath
          ? _value.qrImagePath
          : qrImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isErrorFetchBankList: null == isErrorFetchBankList
          ? _value.isErrorFetchBankList
          : isErrorFetchBankList // ignore: cast_nullable_to_non_nullable
              as bool,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      currentSaveQrCodeToGalleryPermissionStatus: freezed ==
              currentSaveQrCodeToGalleryPermissionStatus
          ? _value.currentSaveQrCodeToGalleryPermissionStatus
          : currentSaveQrCodeToGalleryPermissionStatus // ignore: cast_nullable_to_non_nullable
              as PermissionStatus?,
      isDownloadSuccess: freezed == isDownloadSuccess
          ? _value.isDownloadSuccess
          : isDownloadSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEligibleToShare: freezed == isEligibleToShare
          ? _value.isEligibleToShare
          : isEligibleToShare // ignore: cast_nullable_to_non_nullable
              as bool?,
      downloadOrShareLoadingState: null == downloadOrShareLoadingState
          ? _value.downloadOrShareLoadingState
          : downloadOrShareLoadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      shouldOpenBankListAferDownloadSuccess: null ==
              shouldOpenBankListAferDownloadSuccess
          ? _value.shouldOpenBankListAferDownloadSuccess
          : shouldOpenBankListAferDownloadSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      promotionTransaction: freezed == promotionTransaction
          ? _value.promotionTransaction
          : promotionTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentPromotionTransaction?,
      voucherCancellation: freezed == voucherCancellation
          ? _value.voucherCancellation
          : voucherCancellation // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherCancellation?,
      isDownloadBeforeQuit: freezed == isDownloadBeforeQuit
          ? _value.isDownloadBeforeQuit
          : isDownloadBeforeQuit // ignore: cast_nullable_to_non_nullable
              as bool?,
      isBackToHome: freezed == isBackToHome
          ? _value.isBackToHome
          : isBackToHome // ignore: cast_nullable_to_non_nullable
              as bool?,
      banks: freezed == banks
          ? _value.banks
          : banks // ignore: cast_nullable_to_non_nullable
              as List<RepaymentQrBank>?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentQrBank?,
      failureOrSuccessCheckVoucherReservation: null ==
              failureOrSuccessCheckVoucherReservation
          ? _value.failureOrSuccessCheckVoucherReservation
          : failureOrSuccessCheckVoucherReservation // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentPromotionTransaction>>,
      failureOrSuccessCancelVoucherReservation: null ==
              failureOrSuccessCancelVoucherReservation
          ? _value.failureOrSuccessCancelVoucherReservation
          : failureOrSuccessCancelVoucherReservation // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentVoucherCancellation>>,
      failureOrSuccessFetchQrBank: null == failureOrSuccessFetchQrBank
          ? _value.failureOrSuccessFetchQrBank
          : failureOrSuccessFetchQrBank // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentQrBank>>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentBankTransferQrStateCopyWith<$Res>
    implements $RepaymentBankTransferQrStateCopyWith<$Res> {
  factory _$$_RepaymentBankTransferQrStateCopyWith(
          _$_RepaymentBankTransferQrState value,
          $Res Function(_$_RepaymentBankTransferQrState) then) =
      __$$_RepaymentBankTransferQrStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      Decimal? totalAmount,
      RepaymentContract? selectedContract,
      String? qrImagePath,
      bool isError,
      bool isErrorFetchBankList,
      RepaymentContractVirtualAccount? contractVirtualAccount,
      PermissionStatus? currentSaveQrCodeToGalleryPermissionStatus,
      bool? isDownloadSuccess,
      bool? isEligibleToShare,
      LoadingState downloadOrShareLoadingState,
      bool shouldOpenBankListAferDownloadSuccess,
      RepaymentPromotionTransaction? promotionTransaction,
      RepaymentVoucherCancellation? voucherCancellation,
      bool? isDownloadBeforeQuit,
      bool? isBackToHome,
      List<RepaymentQrBank>? banks,
      RepaymentQrBank? selectedBank,
      Option<Either<RepaymentFailure, RepaymentPromotionTransaction>>
          failureOrSuccessCheckVoucherReservation,
      Option<Either<RepaymentFailure, RepaymentVoucherCancellation>>
          failureOrSuccessCancelVoucherReservation,
      Option<Either<RepaymentFailure, List<RepaymentQrBank>>>
          failureOrSuccessFetchQrBank});
}

/// @nodoc
class __$$_RepaymentBankTransferQrStateCopyWithImpl<$Res>
    extends _$RepaymentBankTransferQrStateCopyWithImpl<$Res,
        _$_RepaymentBankTransferQrState>
    implements _$$_RepaymentBankTransferQrStateCopyWith<$Res> {
  __$$_RepaymentBankTransferQrStateCopyWithImpl(
      _$_RepaymentBankTransferQrState _value,
      $Res Function(_$_RepaymentBankTransferQrState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? totalAmount = freezed,
    Object? selectedContract = freezed,
    Object? qrImagePath = freezed,
    Object? isError = null,
    Object? isErrorFetchBankList = null,
    Object? contractVirtualAccount = freezed,
    Object? currentSaveQrCodeToGalleryPermissionStatus = freezed,
    Object? isDownloadSuccess = freezed,
    Object? isEligibleToShare = freezed,
    Object? downloadOrShareLoadingState = null,
    Object? shouldOpenBankListAferDownloadSuccess = null,
    Object? promotionTransaction = freezed,
    Object? voucherCancellation = freezed,
    Object? isDownloadBeforeQuit = freezed,
    Object? isBackToHome = freezed,
    Object? banks = freezed,
    Object? selectedBank = freezed,
    Object? failureOrSuccessCheckVoucherReservation = null,
    Object? failureOrSuccessCancelVoucherReservation = null,
    Object? failureOrSuccessFetchQrBank = null,
  }) {
    return _then(_$_RepaymentBankTransferQrState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      qrImagePath: freezed == qrImagePath
          ? _value.qrImagePath
          : qrImagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      isErrorFetchBankList: null == isErrorFetchBankList
          ? _value.isErrorFetchBankList
          : isErrorFetchBankList // ignore: cast_nullable_to_non_nullable
              as bool,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      currentSaveQrCodeToGalleryPermissionStatus: freezed ==
              currentSaveQrCodeToGalleryPermissionStatus
          ? _value.currentSaveQrCodeToGalleryPermissionStatus
          : currentSaveQrCodeToGalleryPermissionStatus // ignore: cast_nullable_to_non_nullable
              as PermissionStatus?,
      isDownloadSuccess: freezed == isDownloadSuccess
          ? _value.isDownloadSuccess
          : isDownloadSuccess // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEligibleToShare: freezed == isEligibleToShare
          ? _value.isEligibleToShare
          : isEligibleToShare // ignore: cast_nullable_to_non_nullable
              as bool?,
      downloadOrShareLoadingState: null == downloadOrShareLoadingState
          ? _value.downloadOrShareLoadingState
          : downloadOrShareLoadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      shouldOpenBankListAferDownloadSuccess: null ==
              shouldOpenBankListAferDownloadSuccess
          ? _value.shouldOpenBankListAferDownloadSuccess
          : shouldOpenBankListAferDownloadSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      promotionTransaction: freezed == promotionTransaction
          ? _value.promotionTransaction
          : promotionTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentPromotionTransaction?,
      voucherCancellation: freezed == voucherCancellation
          ? _value.voucherCancellation
          : voucherCancellation // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherCancellation?,
      isDownloadBeforeQuit: freezed == isDownloadBeforeQuit
          ? _value.isDownloadBeforeQuit
          : isDownloadBeforeQuit // ignore: cast_nullable_to_non_nullable
              as bool?,
      isBackToHome: freezed == isBackToHome
          ? _value.isBackToHome
          : isBackToHome // ignore: cast_nullable_to_non_nullable
              as bool?,
      banks: freezed == banks
          ? _value._banks
          : banks // ignore: cast_nullable_to_non_nullable
              as List<RepaymentQrBank>?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentQrBank?,
      failureOrSuccessCheckVoucherReservation: null ==
              failureOrSuccessCheckVoucherReservation
          ? _value.failureOrSuccessCheckVoucherReservation
          : failureOrSuccessCheckVoucherReservation // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentPromotionTransaction>>,
      failureOrSuccessCancelVoucherReservation: null ==
              failureOrSuccessCancelVoucherReservation
          ? _value.failureOrSuccessCancelVoucherReservation
          : failureOrSuccessCancelVoucherReservation // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentVoucherCancellation>>,
      failureOrSuccessFetchQrBank: null == failureOrSuccessFetchQrBank
          ? _value.failureOrSuccessFetchQrBank
          : failureOrSuccessFetchQrBank // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentQrBank>>>,
    ));
  }
}

/// @nodoc

class _$_RepaymentBankTransferQrState implements _RepaymentBankTransferQrState {
  const _$_RepaymentBankTransferQrState(
      {required this.loadingState,
      required this.totalAmount,
      required this.selectedContract,
      this.qrImagePath,
      this.isError = false,
      this.isErrorFetchBankList = false,
      required this.contractVirtualAccount,
      this.currentSaveQrCodeToGalleryPermissionStatus,
      this.isDownloadSuccess,
      this.isEligibleToShare,
      required this.downloadOrShareLoadingState,
      this.shouldOpenBankListAferDownloadSuccess = false,
      this.promotionTransaction,
      this.voucherCancellation,
      this.isDownloadBeforeQuit,
      this.isBackToHome,
      final List<RepaymentQrBank>? banks,
      this.selectedBank,
      required this.failureOrSuccessCheckVoucherReservation,
      required this.failureOrSuccessCancelVoucherReservation,
      required this.failureOrSuccessFetchQrBank})
      : _banks = banks;

  @override
  final LoadingState loadingState;
  @override
  final Decimal? totalAmount;
  @override
  final RepaymentContract? selectedContract;
  @override
  final String? qrImagePath;
  @override
  @JsonKey()
  final bool isError;
  @override
  @JsonKey()
  final bool isErrorFetchBankList;
  @override
  final RepaymentContractVirtualAccount? contractVirtualAccount;
  @override
  final PermissionStatus? currentSaveQrCodeToGalleryPermissionStatus;
  @override
  final bool? isDownloadSuccess;
  @override
  final bool? isEligibleToShare;
  @override
  final LoadingState downloadOrShareLoadingState;
  @override
  @JsonKey()
  final bool shouldOpenBankListAferDownloadSuccess;
// Promotion
  @override
  final RepaymentPromotionTransaction? promotionTransaction;
  @override
  final RepaymentVoucherCancellation? voucherCancellation;
  @override
  final bool? isDownloadBeforeQuit;
  @override
  final bool? isBackToHome;
// Bank
  final List<RepaymentQrBank>? _banks;
// Bank
  @override
  List<RepaymentQrBank>? get banks {
    final value = _banks;
    if (value == null) return null;
    if (_banks is EqualUnmodifiableListView) return _banks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final RepaymentQrBank? selectedBank;
  @override
  final Option<Either<RepaymentFailure, RepaymentPromotionTransaction>>
      failureOrSuccessCheckVoucherReservation;
  @override
  final Option<Either<RepaymentFailure, RepaymentVoucherCancellation>>
      failureOrSuccessCancelVoucherReservation;
  @override
  final Option<Either<RepaymentFailure, List<RepaymentQrBank>>>
      failureOrSuccessFetchQrBank;

  @override
  String toString() {
    return 'RepaymentBankTransferQrState(loadingState: $loadingState, totalAmount: $totalAmount, selectedContract: $selectedContract, qrImagePath: $qrImagePath, isError: $isError, isErrorFetchBankList: $isErrorFetchBankList, contractVirtualAccount: $contractVirtualAccount, currentSaveQrCodeToGalleryPermissionStatus: $currentSaveQrCodeToGalleryPermissionStatus, isDownloadSuccess: $isDownloadSuccess, isEligibleToShare: $isEligibleToShare, downloadOrShareLoadingState: $downloadOrShareLoadingState, shouldOpenBankListAferDownloadSuccess: $shouldOpenBankListAferDownloadSuccess, promotionTransaction: $promotionTransaction, voucherCancellation: $voucherCancellation, isDownloadBeforeQuit: $isDownloadBeforeQuit, isBackToHome: $isBackToHome, banks: $banks, selectedBank: $selectedBank, failureOrSuccessCheckVoucherReservation: $failureOrSuccessCheckVoucherReservation, failureOrSuccessCancelVoucherReservation: $failureOrSuccessCancelVoucherReservation, failureOrSuccessFetchQrBank: $failureOrSuccessFetchQrBank)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentBankTransferQrState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.qrImagePath, qrImagePath) ||
                other.qrImagePath == qrImagePath) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.isErrorFetchBankList, isErrorFetchBankList) ||
                other.isErrorFetchBankList == isErrorFetchBankList) &&
            (identical(other.contractVirtualAccount, contractVirtualAccount) ||
                other.contractVirtualAccount == contractVirtualAccount) &&
            (identical(other.currentSaveQrCodeToGalleryPermissionStatus,
                    currentSaveQrCodeToGalleryPermissionStatus) ||
                other.currentSaveQrCodeToGalleryPermissionStatus ==
                    currentSaveQrCodeToGalleryPermissionStatus) &&
            (identical(other.isDownloadSuccess, isDownloadSuccess) ||
                other.isDownloadSuccess == isDownloadSuccess) &&
            (identical(other.isEligibleToShare, isEligibleToShare) ||
                other.isEligibleToShare == isEligibleToShare) &&
            (identical(other.downloadOrShareLoadingState, downloadOrShareLoadingState) ||
                other.downloadOrShareLoadingState ==
                    downloadOrShareLoadingState) &&
            (identical(other.shouldOpenBankListAferDownloadSuccess, shouldOpenBankListAferDownloadSuccess) ||
                other.shouldOpenBankListAferDownloadSuccess ==
                    shouldOpenBankListAferDownloadSuccess) &&
            (identical(other.promotionTransaction, promotionTransaction) ||
                other.promotionTransaction == promotionTransaction) &&
            (identical(other.voucherCancellation, voucherCancellation) ||
                other.voucherCancellation == voucherCancellation) &&
            (identical(other.isDownloadBeforeQuit, isDownloadBeforeQuit) ||
                other.isDownloadBeforeQuit == isDownloadBeforeQuit) &&
            (identical(other.isBackToHome, isBackToHome) ||
                other.isBackToHome == isBackToHome) &&
            const DeepCollectionEquality().equals(other._banks, _banks) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.failureOrSuccessCheckVoucherReservation, failureOrSuccessCheckVoucherReservation) ||
                other.failureOrSuccessCheckVoucherReservation ==
                    failureOrSuccessCheckVoucherReservation) &&
            (identical(other.failureOrSuccessCancelVoucherReservation,
                    failureOrSuccessCancelVoucherReservation) ||
                other.failureOrSuccessCancelVoucherReservation ==
                    failureOrSuccessCancelVoucherReservation) &&
            (identical(other.failureOrSuccessFetchQrBank, failureOrSuccessFetchQrBank) ||
                other.failureOrSuccessFetchQrBank == failureOrSuccessFetchQrBank));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        loadingState,
        totalAmount,
        selectedContract,
        qrImagePath,
        isError,
        isErrorFetchBankList,
        contractVirtualAccount,
        currentSaveQrCodeToGalleryPermissionStatus,
        isDownloadSuccess,
        isEligibleToShare,
        downloadOrShareLoadingState,
        shouldOpenBankListAferDownloadSuccess,
        promotionTransaction,
        voucherCancellation,
        isDownloadBeforeQuit,
        isBackToHome,
        const DeepCollectionEquality().hash(_banks),
        selectedBank,
        failureOrSuccessCheckVoucherReservation,
        failureOrSuccessCancelVoucherReservation,
        failureOrSuccessFetchQrBank
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentBankTransferQrStateCopyWith<_$_RepaymentBankTransferQrState>
      get copyWith => __$$_RepaymentBankTransferQrStateCopyWithImpl<
          _$_RepaymentBankTransferQrState>(this, _$identity);
}

abstract class _RepaymentBankTransferQrState
    implements RepaymentBankTransferQrState {
  const factory _RepaymentBankTransferQrState(
      {required final LoadingState loadingState,
      required final Decimal? totalAmount,
      required final RepaymentContract? selectedContract,
      final String? qrImagePath,
      final bool isError,
      final bool isErrorFetchBankList,
      required final RepaymentContractVirtualAccount? contractVirtualAccount,
      final PermissionStatus? currentSaveQrCodeToGalleryPermissionStatus,
      final bool? isDownloadSuccess,
      final bool? isEligibleToShare,
      required final LoadingState downloadOrShareLoadingState,
      final bool shouldOpenBankListAferDownloadSuccess,
      final RepaymentPromotionTransaction? promotionTransaction,
      final RepaymentVoucherCancellation? voucherCancellation,
      final bool? isDownloadBeforeQuit,
      final bool? isBackToHome,
      final List<RepaymentQrBank>? banks,
      final RepaymentQrBank? selectedBank,
      required final Option<
              Either<RepaymentFailure, RepaymentPromotionTransaction>>
          failureOrSuccessCheckVoucherReservation,
      required final Option<
              Either<RepaymentFailure, RepaymentVoucherCancellation>>
          failureOrSuccessCancelVoucherReservation,
      required final Option<Either<RepaymentFailure, List<RepaymentQrBank>>>
          failureOrSuccessFetchQrBank}) = _$_RepaymentBankTransferQrState;

  @override
  LoadingState get loadingState;
  @override
  Decimal? get totalAmount;
  @override
  RepaymentContract? get selectedContract;
  @override
  String? get qrImagePath;
  @override
  bool get isError;
  @override
  bool get isErrorFetchBankList;
  @override
  RepaymentContractVirtualAccount? get contractVirtualAccount;
  @override
  PermissionStatus? get currentSaveQrCodeToGalleryPermissionStatus;
  @override
  bool? get isDownloadSuccess;
  @override
  bool? get isEligibleToShare;
  @override
  LoadingState get downloadOrShareLoadingState;
  @override
  bool get shouldOpenBankListAferDownloadSuccess;
  @override // Promotion
  RepaymentPromotionTransaction? get promotionTransaction;
  @override
  RepaymentVoucherCancellation? get voucherCancellation;
  @override
  bool? get isDownloadBeforeQuit;
  @override
  bool? get isBackToHome;
  @override // Bank
  List<RepaymentQrBank>? get banks;
  @override
  RepaymentQrBank? get selectedBank;
  @override
  Option<Either<RepaymentFailure, RepaymentPromotionTransaction>>
      get failureOrSuccessCheckVoucherReservation;
  @override
  Option<Either<RepaymentFailure, RepaymentVoucherCancellation>>
      get failureOrSuccessCancelVoucherReservation;
  @override
  Option<Either<RepaymentFailure, List<RepaymentQrBank>>>
      get failureOrSuccessFetchQrBank;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentBankTransferQrStateCopyWith<_$_RepaymentBankTransferQrState>
      get copyWith => throw _privateConstructorUsedError;
}
