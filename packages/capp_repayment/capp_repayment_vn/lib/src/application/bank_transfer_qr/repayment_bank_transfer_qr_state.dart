part of 'repayment_bank_transfer_qr_bloc.dart';

@freezed
class RepaymentBankTransferQrState with _$RepaymentBankTransferQrState {
  const factory RepaymentBankTransferQrState({
    required LoadingState loadingState,
    required Decimal? totalAmount,
    required RepaymentContract? selectedContract,
    String? qrImagePath,
    @Default(false) bool isError,
    @Default(false) bool isErrorFetchBankList,
    required RepaymentContractVirtualAccount? contractVirtualAccount,
    PermissionStatus? currentSaveQrCodeToGalleryPermissionStatus,
    bool? isDownloadSuccess,
    bool? isEligibleToShare,
    required LoadingState downloadOrShareLoadingState,
    @Default(false) bool shouldOpenBankListAferDownloadSuccess,

    // Promotion
    RepaymentPromotionTransaction? promotionTransaction,
    RepaymentVoucherCancellation? voucherCancellation,
    bool? isDownloadBeforeQuit,
    bool? isBackToHome,

    // Bank
    List<RepaymentQrBank>? banks,
    RepaymentQrBank? selectedBank,
    required Option<Either<RepaymentFailure, RepaymentPromotionTransaction>> failureOrSuccessCheckVoucherReservation,
    required Option<Either<RepaymentFailure, RepaymentVoucherCancellation>> failureOrSuccessCancelVoucherReservation,
    required Option<Either<RepaymentFailure, List<RepaymentQrBank>>> failureOrSuccessFetchQrBank,
  }) = _RepaymentBankTransferQrState;

  factory RepaymentBankTransferQrState.initialize() => RepaymentBankTransferQrState(
        loadingState: LoadingState.isInitial,
        totalAmount: Decimal.zero,
        selectedContract: null,
        contractVirtualAccount: null,
        downloadOrShareLoadingState: LoadingState.isInitial,
        failureOrSuccessCheckVoucherReservation: none(),
        failureOrSuccessCancelVoucherReservation: none(),
        failureOrSuccessFetchQrBank: none(),
      );
}
