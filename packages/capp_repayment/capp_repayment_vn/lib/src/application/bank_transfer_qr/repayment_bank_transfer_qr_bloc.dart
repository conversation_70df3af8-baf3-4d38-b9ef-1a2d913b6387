import 'dart:io';
import 'dart:typed_data';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../capp_repayment.dart';

part 'repayment_bank_transfer_qr_bloc.freezed.dart';
part 'repayment_bank_transfer_qr_event.dart';
part 'repayment_bank_transfer_qr_state.dart';

class RepaymentBankTransferQrBloc extends Bloc<RepaymentBankTransferQrEvent, RepaymentBankTransferQrState> {
  final Logger logger;
  final IPromotionRepository promotionRepository;
  final IDownloadService downloadService;
  final IRepaymentRepository repaymentRepository;

  RepaymentBankTransferQrBloc({
    required this.logger,
    required this.promotionRepository,
    required this.downloadService,
    required this.repaymentRepository,
  }) : super(RepaymentBankTransferQrState.initialize()) {
    on<RepaymentBankTransferQrEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          updateQrImageFilePath: (e) => _updateQrImageFilePath(e, emit),
          saveQrCodeToGallery: (e) => _saveQrCodeToGallery(e, emit),
          shareQrCode: (e) => _shareQrCode(e, emit),
          clearShareStatus: (e) => _clearShareStatus(e, emit),
          clearDownloadStatus: (e) => _clearDownloadStatus(e, emit),
          checkVoucherReservation: (e) => _checkVoucherReservation(e, emit),
          cancelVoucherReservation: (e) => _cancelVoucherReservation(e, emit),
          resetPromotionState: (e) => _resetPromotionState(e, emit),
          fetchQrBanks: (e) => _fetchQrBanks(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    String? contractNumber;
    Decimal? totalAmount;

    totalAmount = e.selectedAmount;
    if (e.selectedContract.contractType == RepaymentContractType.cel) {
      contractNumber = e.selectedContract.loanContract?.contractNumber;
    } else {
      contractNumber = e.selectedContract.relContract?.accountNumber;
    }
    final contractVirtualAccount = e.contractVirtualAccount;
    final selectedQrBank = await getSavedQrBank();
    if ((contractNumber ?? '').isEmpty) {
      emit(state.copyWith(loadingState: LoadingState.isCompleted, isError: true));
    } else {
      emit(
        state.copyWith(
          totalAmount: totalAmount,
          selectedContract: e.selectedContract,
          contractVirtualAccount: contractVirtualAccount,
          selectedBank: selectedQrBank,
        ),
      );

      final virtualAccountNumber = contractVirtualAccount.data?.virtualAccountNumber ?? '';
      if (virtualAccountNumber.isNotEmpty) {
        add(RepaymentBankTransferQrEvent.fetchQrBanks(virtualAccountNumber: virtualAccountNumber));
      }
    }
  }

  Future<void> _updateQrImageFilePath(
    _UpdateQrImageFilePath e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    emit(state.copyWith(qrImagePath: e.path));
  }

  Future<void> _saveQrCodeToGallery(
    _SaveQrCodeToGallery e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    final currentSaveQrCodeToGalleryPermissionStatus =
        await PermissionUtils.getCurrentPermissionStatusForSaveImageToPhotos();
    final isDownloadBeforeQuit = e.isDownloadBeforeQuit ?? state.isDownloadBeforeQuit;

    if (currentSaveQrCodeToGalleryPermissionStatus == PermissionStatus.granted ||
        currentSaveQrCodeToGalleryPermissionStatus == PermissionStatus.limited) {
      try {
        // Download image
        emit(
          state.copyWith(
            downloadOrShareLoadingState: LoadingState.isLoading,
            shouldOpenBankListAferDownloadSuccess: e.shouldOpenBankListAferDownloadSuccess ?? false,
          ),
        );
        final isSuccess = await _saveNetworkImageToGallery(url: e.qrUrl);
        emit(
          state.copyWith(
            isDownloadSuccess: isSuccess,
            isDownloadBeforeQuit: isDownloadBeforeQuit,
            downloadOrShareLoadingState: LoadingState.isCompleted,
            currentSaveQrCodeToGalleryPermissionStatus: currentSaveQrCodeToGalleryPermissionStatus,
          ),
        );
      } catch (_) {
        emit(
          state.copyWith(
            isDownloadSuccess: false,
            isDownloadBeforeQuit: isDownloadBeforeQuit,
            downloadOrShareLoadingState: LoadingState.isCompleted,
          ),
        );
      }
    } else {
      emit(
        state.copyWith(
          currentSaveQrCodeToGalleryPermissionStatus: currentSaveQrCodeToGalleryPermissionStatus,
          isDownloadBeforeQuit: isDownloadBeforeQuit,
          isDownloadSuccess: false,
          shouldOpenBankListAferDownloadSuccess: e.shouldOpenBankListAferDownloadSuccess ?? false,
        ),
      );
    }
  }

  Future<void> _shareQrCode(
    _ShareQrCode e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    final qrImageFilePath = state.qrImagePath;
    emit(state.copyWith(downloadOrShareLoadingState: LoadingState.isLoading));
    // If user share image before, share instead of downloading again
    try {
      if (qrImageFilePath != null && qrImageFilePath.isNotEmpty) {
        if (File(qrImageFilePath).existsSync()) {
          emit(state.copyWith(isEligibleToShare: true, downloadOrShareLoadingState: LoadingState.isCompleted));
        } else {
          emit(
            state.copyWith(
              isEligibleToShare: false,
              qrImagePath: '',
              downloadOrShareLoadingState: LoadingState.isCompleted,
            ),
          );
        }
      } else {
        final imageFile = await _saveNetworkImageToFile(url: e.qrUrl, isUseTemporaryFolder: true);
        if (imageFile == null) {
          emit(state.copyWith(isEligibleToShare: false, downloadOrShareLoadingState: LoadingState.isCompleted));
          return;
        }
        final imageFilePath = imageFile.path;
        if (imageFilePath.isNotEmpty) {
          emit(
            state.copyWith(
              isEligibleToShare: true,
              qrImagePath: imageFilePath,
              downloadOrShareLoadingState: LoadingState.isCompleted,
            ),
          );
        } else {
          emit(state.copyWith(isEligibleToShare: false, downloadOrShareLoadingState: LoadingState.isCompleted));
        }
      }
    } catch (e) {
      logger.wtf('Share image fail: $e');
      emit(state.copyWith(isEligibleToShare: false, downloadOrShareLoadingState: LoadingState.isCompleted));
    }
  }

  Future<void> _clearDownloadStatus(
    _ClearDownloadStatus e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    emit(state.copyWith(isDownloadSuccess: null, downloadOrShareLoadingState: LoadingState.isInitial));
  }

  Future<void> _clearShareStatus(
    _ClearShareStatus e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    emit(state.copyWith(isEligibleToShare: null, downloadOrShareLoadingState: LoadingState.isInitial));
  }

  Future<void> _checkVoucherReservation(
    _CheckVoucherReservation e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    final promotionTransactionId = e.promotionTransactionId;
    final isBackToHome = e.isBackToHome;

    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false, isBackToHome: isBackToHome));

    final response = await promotionRepository.getPromotionTransactionDetail(
      promotionTransactionId: promotionTransactionId,
    );
    logger.d('getPromotionTransactionDetail response$response');

    emit(
      response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: true,
          failureOrSuccessCheckVoucherReservation: optionOf(response),
        );
      }, (r) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessCheckVoucherReservation: optionOf(response),
          isError: false,
          promotionTransaction: r,
        );
      }),
    );
  }

  Future<void> _cancelVoucherReservation(
    _CancelVoucherReservation e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    final request = e.request;

    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false));

    final response = await promotionRepository.cancelVoucherReservation(
      request: request,
    );
    logger.d('cancelVoucherReservation response$response');

    emit(
      response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: true,
          failureOrSuccessCancelVoucherReservation: optionOf(response),
        );
      }, (r) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessCancelVoucherReservation: optionOf(response),
          isError: false,
          voucherCancellation: r,
        );
      }),
    );
  }

  Future<void> _resetPromotionState(
    _ResetPromotionState e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    emit(
      state.copyWith(
        isError: false,
        loadingState: LoadingState.isInitial,
        failureOrSuccessCancelVoucherReservation: none(),
        failureOrSuccessCheckVoucherReservation: none(),
      ),
    );
  }

  Future<void> _fetchQrBanks(
    _FetchQrBanks e,
    Emitter<RepaymentBankTransferQrState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, isErrorFetchBankList: false));

    final response = await repaymentRepository.fetchQrBanks(virtualAccountNumber: e.virtualAccountNumber);
    logger.d('fetchQrBanks response$response');

    emit(
      await response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isErrorFetchBankList: true,
          failureOrSuccessFetchQrBank: optionOf(response),
        );
      }, (r) async {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessFetchQrBank: optionOf(response),
          isErrorFetchBankList: false,
          banks: r,
        );
      }),
    );
  }

  Future<File?> _saveNetworkImageToFile({required String url, required bool isUseTemporaryFolder}) async {
    try {
      final fileName = 'hc_${DateTime.now().millisecondsSinceEpoch}';
      final fileExtension = FileUtils.getFileExtension(url);
      final directory = await FileUtils.getDownloadDirectory(getTemporaryFolder: isUseTemporaryFolder);
      final directoryPath = directory?.path ?? '';

      var fullPath = '';
      if (directoryPath.isNotEmpty) {
        fullPath = '$directoryPath/$fileName$fileExtension';
      }

      final file = await downloadService.downloadFile(url: url, fullPath: fullPath);
      return file;
    } catch (e) {
      logger.wtf('Failed to download QR code', e.toString());
      return null;
    }
  }

  Future<bool> _saveNetworkImageToGallery({required String url}) async {
    final fileName = 'hc_${DateTime.now().millisecondsSinceEpoch}';
    final flleData = await downloadService.downloadFileAsBytes(url: url);
    var saveImageResult = false;

    // Parse network image to bytes successfully
    if (flleData != null) {
      // Handle save image to gallery for Android 9 and below
      if (GmaPlatform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        final sdkInt = androidInfo.version.sdkInt;
        // For android 9 and below
        if (sdkInt < 29) {
          return FileUtils.saveFileAs(
            bytes: Uint8List.fromList(flleData),
            fileName: fileName,
            ext: CoreConstants.downloadImageJpgExtension,
            customMimeType: CoreConstants.downloadImageJpgMimeType,
          );
        }
      }

      // Handle save image for iOS, Android 10 and above
      final result = await ImageGallerySaver.saveImage(Uint8List.fromList(flleData), quality: 100, name: fileName)
          as Map<dynamic, dynamic>;
      var contentUriPath = '';
      if (result.containsKey('filePath')) {
        contentUriPath = result['filePath'] as String;
      }
      if (result.containsKey('isSuccess')) {
        saveImageResult = result['isSuccess'] as bool;
      }
      logger.d('QR content uri path: $contentUriPath');
      return saveImageResult;
    }

    return saveImageResult;
  }

  Future<RepaymentQrBank?> getSavedQrBank() async {
    final selectedQrBank = await repaymentRepository.getQrBankFromCache();

    return selectedQrBank;
  }
}
