part of 'repayment_bank_transfer_qr_bloc.dart';

@freezed
class RepaymentBankTransferQrEvent with _$RepaymentBankTransferQrEvent {
  const factory RepaymentBankTransferQrEvent.initialize(
    RepaymentContract selectedContract,
    Decimal? selectedAmount,
    RepaymentContractVirtualAccount contractVirtualAccount,
  ) = _Initialize;
  const factory RepaymentBankTransferQrEvent.updateQrImageFilePath(String path) = _UpdateQrImageFilePath;
  const factory RepaymentBankTransferQrEvent.saveQrCodeToGallery({
    required String qrUrl,
    bool? isDownloadBeforeQuit,
    bool? shouldOpenBankListAferDownloadSuccess,
  }) = _SaveQrCodeToGallery;
  const factory RepaymentBankTransferQrEvent.shareQrCode({required String qrUrl}) = _ShareQrCode;
  const factory RepaymentBankTransferQrEvent.clearShareStatus() = _ClearShareStatus;
  const factory RepaymentBankTransferQrEvent.clearDownloadStatus() = _ClearDownloadStatus;
  const factory RepaymentBankTransferQrEvent.checkVoucherReservation({
    required String promotionTransactionId,
    bool? isBackToHome,
  }) = _CheckVoucherReservation;
  const factory RepaymentBankTransferQrEvent.cancelVoucherReservation({
    required RepaymentVoucherCancellationRequest request,
  }) = _CancelVoucherReservation;
  const factory RepaymentBankTransferQrEvent.resetPromotionState() = _ResetPromotionState;
  const factory RepaymentBankTransferQrEvent.fetchQrBanks({required String virtualAccountNumber}) = _FetchQrBanks;
}
