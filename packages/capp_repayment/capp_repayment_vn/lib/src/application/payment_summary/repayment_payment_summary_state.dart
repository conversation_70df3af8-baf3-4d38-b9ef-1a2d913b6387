part of 'repayment_payment_summary_bloc.dart';

@freezed
class RepaymentPaymentSummaryState with _$RepaymentPaymentSummaryState {
  const factory RepaymentPaymentSummaryState({
    required LoadingState loadingState,
    required Decimal selectedAmount,
    required RepaymentContract? contract,
    required String fullName,
    required String phoneNumber,
    required bool isPartnerProdSetting,
    List<RepaymentUserPaymentMethod>? paymentMethods,
    required RepaymentUserPaymentMethod? selectedPaymentMethod,
    RepaymentBank? selectedBank,
    RepaymentContractVirtualAccount? virtualAccount,
    @Default(false) bool isEnableMomoV2,
    required Option<Either<RepaymentFailure, RepaymentOnePayTransaction>> failureOrSuccessOnePay,
    required Option<Either<RepaymentFailure, RepaymentMomoTransaction>> failureOrSuccessMomo,
    required Option<Either<RepaymentFailure, RepaymentVnPayTransaction>> failureOrSuccessVnPay,
    required Option<Either<RepaymentFailure, RepaymentShopeePayTransaction>> failureOrSuccessShopeePay,
    required Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>> failureOrSuccessZaloPay,
    required Option<Either<RepaymentFailure, RepaymentViettelMoneyTransaction>> failureOrSuccessViettelMoney,
    required bool isCalledVnPayMobileBankingApp,
    required Option<Either<RepaymentFailure, RepaymentTransaction>> failureOrSuccessTransactionResult,
    required bool fromRepaymentNew,
    required bool isEnableDirectDiscount,
    bool? isCheckingTransactionResult,
    RepaymentMomoTransaction? repaymentMomoTransaction,
    RepaymentVnPayTransaction? repaymentVnPayTransaction,
    RepaymentShopeePayTransaction? repaymentShopeePayTransaction,
    RepaymentZaloPayTransaction? repaymentZaloPayTransaction,
    RepaymentTransaction? repaymentTransaction,
    String? gmaTransactionId,
    RepaymentVoucherCalculationData? voucherCalculationData,
    String? voucherCode,
  }) = _RepaymentPaymentSummaryState;

  factory RepaymentPaymentSummaryState.initialize() => RepaymentPaymentSummaryState(
        loadingState: LoadingState.isInitial,
        selectedAmount: Decimal.parse('0'),
        contract: null,
        fullName: '',
        phoneNumber: '',
        isPartnerProdSetting: true,
        selectedPaymentMethod: null,
        isCalledVnPayMobileBankingApp: false,
        isCheckingTransactionResult: false,
        failureOrSuccessMomo: none(),
        failureOrSuccessOnePay: none(),
        failureOrSuccessVnPay: none(),
        failureOrSuccessShopeePay: none(),
        failureOrSuccessZaloPay: none(),
        failureOrSuccessViettelMoney: none(),
        failureOrSuccessTransactionResult: none(),
        fromRepaymentNew: false,
        isEnableDirectDiscount: false,
      );
}
