import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:intl/intl.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_repayment.dart';
import '../../domain/repositories/index.dart' as vn_repositories;

part 'repayment_payment_summary_bloc.freezed.dart';
part 'repayment_payment_summary_event.dart';
part 'repayment_payment_summary_state.dart';

class RepaymentPaymentSummaryBloc extends Bloc<RepaymentPaymentSummaryEvent, RepaymentPaymentSummaryState> {
  final Logger logger;
  final vn_repositories.IRepaymentRepository repaymentRepository;
  final LockStatusBloc? lockStatusBloc;
  final currencyCode = 'VND';
  final CappRepaymentSettingsVn settings;
  final IUserRepository userRepository;

  RepaymentPaymentSummaryBloc({
    required this.logger,
    required this.repaymentRepository,
    required this.userRepository,
    required this.settings,
    required this.lockStatusBloc,
  }) : super(RepaymentPaymentSummaryState.initialize()) {
    on<RepaymentPaymentSummaryEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          makeOnePayTransaction: (e) => _makeOnePayTransaction(e, emit),
          makeMomoTransaction: (e) => _makeMomoTransaction(e, emit),
          makeVnPayTransaction: (e) => _makeVnPayTransaction(e, emit),
          makeShopeePayTransaction: (e) => _makeShopeePayTransaction(e, emit),
          makeZaloPayTransaction: (e) => _makeZaloPayTransaction(e, emit),
          makeViettelMoneyTransaction: (e) => _makeViettelMoneyTransaction(e, emit),
          confirm: (e) => _confirm(e, emit),
          checkTransactionResult: (e) => _checkTransactionResult(e, emit),
          storeTransactionResult: (e) => _storeTransactionResult(e, emit),
          setCalledVnPayMobileBankingApp: (e) => _setCalledVnPayMobileBankingApp(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    var isProdSetting = true;
    var phoneNumber = '';
    (await userRepository.currentUser()).fold((l) {}, (user) {
      isProdSetting =
          user.accessLevel?.toLowerCase() == CoreConstants.publicAccessLevel.toLowerCase() && user.isInsider == false;
      phoneNumber = user.phoneNumber ?? '';
    });

    var paymentMethods = await repaymentRepository.getPaymentMethodsFromCache() ?? [];

    // In case cache is lost, get from backup
    if (paymentMethods.isEmpty) {
      paymentMethods =
          await repaymentRepository.getBackupUserPaymentMethods(l10nCappRepayment: e.repaymentLocalization);
    }
    emit(
      state.copyWith(
        loadingState: LoadingState.isInitial,
        selectedAmount: e.selectedAmount,
        contract: e.contract,
        fullName: e.fullName,
        phoneNumber: phoneNumber,
        isPartnerProdSetting: isProdSetting,
        selectedPaymentMethod: e.selectedPaymentMethod,
        paymentMethods: paymentMethods,
        selectedBank: e.selectedBank,
        virtualAccount: e.virtualAccount,
        fromRepaymentNew: e.fromRepaymentNew,
        isEnableDirectDiscount: e.isEnableDirectDiscount,
        voucherCalculationData: e.voucherCalculationData,
        voucherCode: e.voucherCode,
        isEnableMomoV2: e.isEnableMomoV2 ?? false,
      ),
    );
  }

  Future<void> _makeOnePayTransaction(
    _MakeOnePayTransaction e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, failureOrSuccessOnePay: none()));
    await Future<dynamic>.delayed(const Duration(seconds: 3));
    final response = await repaymentRepository.createOnePayTransaction(e.request);
    logger.d('createOnePayTransaction response$response');
    emit(
      response.fold((l) {
        return state.copyWith(loadingState: LoadingState.isCompleted, failureOrSuccessOnePay: optionOf(response));
      }, (r) {
        final transactionInfo = r.transactionInfo;
        final gmaTransactionId = transactionInfo?.gmaTransactionId;
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessOnePay: optionOf(response),
          repaymentTransaction: transactionInfo,
          gmaTransactionId: gmaTransactionId,
        );
      }),
    );
  }

  Future<void> _makeMomoTransaction(
    _MakeMomoTransaction e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    final isEnableMomoV2 = state.isEnableMomoV2;
    emit(state.copyWith(loadingState: LoadingState.isLoading, failureOrSuccessMomo: none()));
    await Future<dynamic>.delayed(const Duration(seconds: 3));
    final response = await (isEnableMomoV2
        ? repaymentRepository.createMomoTransaction(e.request)
        : repaymentRepository.createMomoTransactionV1(e.request));
    logger.d('createMomoTransaction response$response');
    emit(
      response.fold((l) {
        return state.copyWith(loadingState: LoadingState.isCompleted, failureOrSuccessMomo: optionOf(response));
      }, (r) {
        lockStatusBloc?.add(const LockStatusEvent.setEnabled(enabled: false));
        final transactionInfo = r.transactionInfo;
        final gmaTransactionId = transactionInfo?.gmaTransactionId;

        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessMomo: optionOf(response),
          repaymentMomoTransaction: r,
          gmaTransactionId: gmaTransactionId,
        );
      }),
    );
  }

  Future<void> _makeVnPayTransaction(
    _MakeVnPayTransaction e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, failureOrSuccessVnPay: none()));
    await Future<dynamic>.delayed(const Duration(seconds: 3));
    final response = await repaymentRepository.createVnPayTransaction(e.request);
    logger.d('createVnPayTransaction response$response');
    emit(
      response.fold((l) {
        return state.copyWith(loadingState: LoadingState.isCompleted, failureOrSuccessVnPay: optionOf(response));
      }, (r) {
        lockStatusBloc?.add(const LockStatusEvent.setEnabled(enabled: false));

        final transactionInfo = r.transactionInfo;
        final gmaTransactionId = transactionInfo?.gmaTransactionId;

        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessVnPay: optionOf(response),
          repaymentVnPayTransaction: r,
          gmaTransactionId: gmaTransactionId,
        );
      }),
    );
  }

  Future<void> _makeShopeePayTransaction(
    _MakeShopeePayTransaction e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, failureOrSuccessShopeePay: none()));
    await Future<dynamic>.delayed(const Duration(seconds: 3));
    final response = await repaymentRepository.createShopeePayTransaction(e.request);
    logger.d('createShopeePayTransaction response$response');
    emit(
      response.fold((l) {
        return state.copyWith(loadingState: LoadingState.isCompleted, failureOrSuccessShopeePay: optionOf(response));
      }, (r) {
        lockStatusBloc?.add(const LockStatusEvent.setEnabled(enabled: false));

        final transactionInfo = r.transactionInfo;
        final gmaTransactionId = transactionInfo?.gmaTransactionId;

        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessShopeePay: optionOf(response),
          repaymentShopeePayTransaction: r,
          gmaTransactionId: gmaTransactionId,
        );
      }),
    );
  }

  Future<void> _makeZaloPayTransaction(
    _MakeZaloPayTransaction e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, failureOrSuccessZaloPay: none()));
    await Future<dynamic>.delayed(const Duration(seconds: 3));

    final response = await repaymentRepository.createZaloPayTransaction(e.request);
    logger.d('createZaloPayTransaction response$response');
    emit(
      response.fold((l) {
        return state.copyWith(loadingState: LoadingState.isCompleted, failureOrSuccessZaloPay: optionOf(response));
      }, (r) {
        lockStatusBloc?.add(const LockStatusEvent.setEnabled(enabled: false));

        final transactionInfo = r.transactionInfo;
        final gmaTransactionId = transactionInfo?.gmaTransactionId;

        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessZaloPay: optionOf(response),
          repaymentZaloPayTransaction: r,
          gmaTransactionId: gmaTransactionId,
        );
      }),
    );
  }

  Future<void> _makeViettelMoneyTransaction(
    _MakeViettelMoneyTransaction e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, failureOrSuccessViettelMoney: none()));
    await Future<dynamic>.delayed(const Duration(seconds: 3));
    final response = await repaymentRepository.createViettelMoneyTransaction(e.request);
    logger.d('createViettelMoneyTransaction response$response');
    emit(
      response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessViettelMoney: optionOf(response),
        );
      }, (r) {
        final transactionInfo = r.transactionInfo;
        final gmaTransactionId = transactionInfo?.gmaTransactionId;

        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessViettelMoney: optionOf(response),
          repaymentTransaction: transactionInfo,
          gmaTransactionId: gmaTransactionId,
        );
      }),
    );
  }

  Future<void> _confirm(
    _Confirm e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    if (state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.onepay.getId()) {
      final request = RepaymentOnePayTransactionRequest(
        transactionCreate: RepaymentTransactionCreate(
          amount: state.selectedAmount,
          contractNumber: state.contract!.contractNumber,
          currencyCode: currencyCode,
          voucherCode: state.voucherCode,
        ),
        returnUrl: Uri.encodeComponent(settings.onePaySettings.returnUrl),
      );
      add(RepaymentPaymentSummaryEvent.makeOnePayTransaction(request));
    } else if (state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.momo.getId()) {
      var client = '';
      if (GmaPlatform.isAndroid) {
        client = 'android_app';
      }
      if (GmaPlatform.isIOS) {
        client = 'ios_app';
      }
      final request = RepaymentMomoTransactionRequest(
        transactionCreate: RepaymentTransactionCreate(
          amount: state.selectedAmount,
          contractNumber: state.contract!.contractNumber,
          currencyCode: currencyCode,
          voucherCode: state.voucherCode,
        ),
        client: client,
        callbackUrl: 'hcvn://repayment/result/momo',
        provider: RepaymentPaymentMethod.momo.name,
      );
      add(RepaymentPaymentSummaryEvent.makeMomoTransaction(request));
    } else if (state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.vnpay.getId()) {
      final request = RepaymentVnPayTransactionRequest(
        transactionCreate: RepaymentTransactionCreate(
          amount: state.selectedAmount,
          contractNumber: state.contract!.contractNumber,
          currencyCode: currencyCode,
          voucherCode: state.voucherCode,
        ),
      );
      add(RepaymentPaymentSummaryEvent.makeVnPayTransaction(request));
    } else if (state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.shopee.getId()) {
      final request = RepaymentShopeePayTransactionRequest(
        transactionCreate: RepaymentTransactionCreate(
          amount: state.selectedAmount,
          contractNumber: state.contract!.contractNumber,
          provider: RepaymentPaymentMethod.shopee.name,
          currencyCode: currencyCode,
          voucherCode: state.voucherCode,
        ),
        returnUrl: settings.shopeePaySettings.returnUrl,
      );
      add(RepaymentPaymentSummaryEvent.makeShopeePayTransaction(request));
    } else if (state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.zalo.getId()) {
      final request = RepaymentZaloPayTransactionRequest(
        transactionCreate: RepaymentTransactionCreate(
          amount: state.selectedAmount,
          contractNumber: state.contract!.contractNumber,
          provider: RepaymentPaymentMethod.zalo.name,
          currencyCode: currencyCode,
          voucherCode: state.voucherCode,
        ),
        redirectUrl: '',
      );
      add(RepaymentPaymentSummaryEvent.makeZaloPayTransaction(request));
    } else if (state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.viettel.getId()) {
      final redirectUrl = Uri.encodeComponent(settings.viettelMoneySettings.returnUrl);
      final request = RepaymentViettelMoneyTransactionRequest(
        transactionCreate: RepaymentTransactionCreate(
          amount: state.selectedAmount,
          contractNumber: state.contract!.contractNumber,
          currencyCode: currencyCode,
          voucherCode: state.voucherCode,
        ),
        returnUrl: redirectUrl,
        cancelUrl: redirectUrl,
      );
      add(RepaymentPaymentSummaryEvent.makeViettelMoneyTransaction(request));
    }
  }

  Future<void> _checkTransactionResult(
    _CheckTransactionResult e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    final gmaTransactionId = state.gmaTransactionId ?? '';
    Either<RepaymentFailure, RepaymentTransaction>? response;

    if (gmaTransactionId.isNotEmpty) {
      emit(
        state.copyWith(
          loadingState: LoadingState.isLoading,
          isCheckingTransactionResult: true,
          failureOrSuccessTransactionResult: none(),
        ),
      );
      response = await repaymentRepository.getTransactionResult(gmaTransactionId);
      logger.d('getTransactionResult response$response');
    } else {
      response = const Left(RepaymentFailure.unexpected());
    }
    emit(
      state.copyWith(
        loadingState: LoadingState.isCompleted,
        isCheckingTransactionResult: false,
        failureOrSuccessTransactionResult: optionOf(response),
      ),
    );
  }

  Future<void> _storeTransactionResult(
    _StoreTransactionResult e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    final transactionResult = RepaymentTransactionResult(
      contractNo: e.transactionResult.contractNumber ?? '',
      customerName: state.fullName,
      dateProcessed: DateFormat(RepaymentFormatConfig.storageDateTimeFormat).format(DateTime.now()),
      isEWallet: false,
      paymentOption: state.selectedPaymentMethod?.title ?? '',
      paymentOptionId: state.selectedPaymentMethod?.gmaId ?? '',
      finalAmount: e.transactionResult.finalAmount!.toDouble(),
      discountAmount: e.transactionResult.discountAmount?.toDouble(),
      originalAmount: e.transactionResult.originalAmount?.toDouble(),
      voucherCode: e.transactionResult.voucherCode,
      transactionId: e.transactionResult.gmaTransactionId ?? '',
      transactionNo: '',
    );
    await repaymentRepository.setTransactionResultToCache(transactionResult);
  }

  Future<void> _setCalledVnPayMobileBankingApp(
    _SetCalledVnPayMobileBankingApp e,
    Emitter<RepaymentPaymentSummaryState> emit,
  ) async {
    emit(state.copyWith(isCalledVnPayMobileBankingApp: e.isCalledVnPayMobileBankingApp));
  }

  String getBaoKimDeeplink({
    required CappBaoKimSettings settings,
    required RepaymentBank bank,
    required RepaymentContractVirtualAccount virtualAccount,
  }) {
    final account = virtualAccount.data?.virtualAccountNumber ?? '';
    final bankShortName = bank.shortName;
    return '${settings.deeplink}/payment?account=$account&bank=$bankShortName&fill=1';
  }

  bool isPaymentWithVoucherCode() {
    final voucherCode = state.voucherCode ?? '';
    return voucherCode.isNotEmpty;
  }
}
