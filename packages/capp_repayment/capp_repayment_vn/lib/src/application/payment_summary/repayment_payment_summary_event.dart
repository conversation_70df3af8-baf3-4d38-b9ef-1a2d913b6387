part of 'repayment_payment_summary_bloc.dart';

@freezed
class RepaymentPaymentSummaryEvent with _$RepaymentPaymentSummaryEvent {
  const factory RepaymentPaymentSummaryEvent.initialize({
    required Decimal selectedAmount,
    required RepaymentContract contract,
    required String fullName,
    required RepaymentUserPaymentMethod selectedPaymentMethod,
    required bool fromRepaymentNew,
    required bool isEnableDirectDiscount,
    bool? isEnableMomoV2,
    RepaymentBank? selectedBank,
    RepaymentContractVirtualAccount? virtualAccount,
    RepaymentVoucherCalculationData? voucherCalculationData,
    String? voucherCode,
    required L10nCappRepayment repaymentLocalization,
  }) = _Initialize;
  const factory RepaymentPaymentSummaryEvent.makeOnePayTransaction(RepaymentOnePayTransactionRequest request) =
      _MakeOnePayTransaction;
  const factory RepaymentPaymentSummaryEvent.makeMomoTransaction(RepaymentMomoTransactionRequest request) =
      _MakeMomoTransaction;
  const factory RepaymentPaymentSummaryEvent.makeVnPayTransaction(RepaymentVnPayTransactionRequest request) =
      _MakeVnPayTransaction;
  const factory RepaymentPaymentSummaryEvent.makeShopeePayTransaction(RepaymentShopeePayTransactionRequest request) =
      _MakeShopeePayTransaction;
  const factory RepaymentPaymentSummaryEvent.makeZaloPayTransaction(RepaymentZaloPayTransactionRequest request) =
      _MakeZaloPayTransaction;
  const factory RepaymentPaymentSummaryEvent.makeViettelMoneyTransaction(
    RepaymentViettelMoneyTransactionRequest request,
  ) = _MakeViettelMoneyTransaction;
  const factory RepaymentPaymentSummaryEvent.confirm() = _Confirm;
  const factory RepaymentPaymentSummaryEvent.checkTransactionResult() = _CheckTransactionResult;
  const factory RepaymentPaymentSummaryEvent.storeTransactionResult(RepaymentTransaction transactionResult) =
      _StoreTransactionResult;
  const factory RepaymentPaymentSummaryEvent.setCalledVnPayMobileBankingApp({
    required bool isCalledVnPayMobileBankingApp,
  }) = _SetCalledVnPayMobileBankingApp;
}
