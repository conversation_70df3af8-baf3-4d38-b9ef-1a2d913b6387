// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_payment_summary_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPaymentSummaryEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPaymentSummaryEventCopyWith<$Res> {
  factory $RepaymentPaymentSummaryEventCopyWith(
          RepaymentPaymentSummaryEvent value,
          $Res Function(RepaymentPaymentSummaryEvent) then) =
      _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
          RepaymentPaymentSummaryEvent>;
}

/// @nodoc
class _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        $Val extends RepaymentPaymentSummaryEvent>
    implements $RepaymentPaymentSummaryEventCopyWith<$Res> {
  _$RepaymentPaymentSummaryEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Decimal selectedAmount,
      RepaymentContract contract,
      String fullName,
      RepaymentUserPaymentMethod selectedPaymentMethod,
      bool fromRepaymentNew,
      bool isEnableDirectDiscount,
      bool? isEnableMomoV2,
      RepaymentBank? selectedBank,
      RepaymentContractVirtualAccount? virtualAccount,
      RepaymentVoucherCalculationData? voucherCalculationData,
      String? voucherCode,
      L10nCappRepayment repaymentLocalization});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedAmount = null,
    Object? contract = null,
    Object? fullName = null,
    Object? selectedPaymentMethod = null,
    Object? fromRepaymentNew = null,
    Object? isEnableDirectDiscount = null,
    Object? isEnableMomoV2 = freezed,
    Object? selectedBank = freezed,
    Object? virtualAccount = freezed,
    Object? voucherCalculationData = freezed,
    Object? voucherCode = freezed,
    Object? repaymentLocalization = null,
  }) {
    return _then(_$_Initialize(
      selectedAmount: null == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      contract: null == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      selectedPaymentMethod: null == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod,
      fromRepaymentNew: null == fromRepaymentNew
          ? _value.fromRepaymentNew
          : fromRepaymentNew // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableDirectDiscount: null == isEnableDirectDiscount
          ? _value.isEnableDirectDiscount
          : isEnableDirectDiscount // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableMomoV2: freezed == isEnableMomoV2
          ? _value.isEnableMomoV2
          : isEnableMomoV2 // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      virtualAccount: freezed == virtualAccount
          ? _value.virtualAccount
          : virtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      voucherCalculationData: freezed == voucherCalculationData
          ? _value.voucherCalculationData
          : voucherCalculationData // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherCalculationData?,
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
      repaymentLocalization: null == repaymentLocalization
          ? _value.repaymentLocalization
          : repaymentLocalization // ignore: cast_nullable_to_non_nullable
              as L10nCappRepayment,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required this.selectedAmount,
      required this.contract,
      required this.fullName,
      required this.selectedPaymentMethod,
      required this.fromRepaymentNew,
      required this.isEnableDirectDiscount,
      this.isEnableMomoV2,
      this.selectedBank,
      this.virtualAccount,
      this.voucherCalculationData,
      this.voucherCode,
      required this.repaymentLocalization});

  @override
  final Decimal selectedAmount;
  @override
  final RepaymentContract contract;
  @override
  final String fullName;
  @override
  final RepaymentUserPaymentMethod selectedPaymentMethod;
  @override
  final bool fromRepaymentNew;
  @override
  final bool isEnableDirectDiscount;
  @override
  final bool? isEnableMomoV2;
  @override
  final RepaymentBank? selectedBank;
  @override
  final RepaymentContractVirtualAccount? virtualAccount;
  @override
  final RepaymentVoucherCalculationData? voucherCalculationData;
  @override
  final String? voucherCode;
  @override
  final L10nCappRepayment repaymentLocalization;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.initialize(selectedAmount: $selectedAmount, contract: $contract, fullName: $fullName, selectedPaymentMethod: $selectedPaymentMethod, fromRepaymentNew: $fromRepaymentNew, isEnableDirectDiscount: $isEnableDirectDiscount, isEnableMomoV2: $isEnableMomoV2, selectedBank: $selectedBank, virtualAccount: $virtualAccount, voucherCalculationData: $voucherCalculationData, voucherCode: $voucherCode, repaymentLocalization: $repaymentLocalization)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.contract, contract) ||
                other.contract == contract) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.selectedPaymentMethod, selectedPaymentMethod) ||
                other.selectedPaymentMethod == selectedPaymentMethod) &&
            (identical(other.fromRepaymentNew, fromRepaymentNew) ||
                other.fromRepaymentNew == fromRepaymentNew) &&
            (identical(other.isEnableDirectDiscount, isEnableDirectDiscount) ||
                other.isEnableDirectDiscount == isEnableDirectDiscount) &&
            (identical(other.isEnableMomoV2, isEnableMomoV2) ||
                other.isEnableMomoV2 == isEnableMomoV2) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.virtualAccount, virtualAccount) ||
                other.virtualAccount == virtualAccount) &&
            (identical(other.voucherCalculationData, voucherCalculationData) ||
                other.voucherCalculationData == voucherCalculationData) &&
            (identical(other.voucherCode, voucherCode) ||
                other.voucherCode == voucherCode) &&
            (identical(other.repaymentLocalization, repaymentLocalization) ||
                other.repaymentLocalization == repaymentLocalization));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      selectedAmount,
      contract,
      fullName,
      selectedPaymentMethod,
      fromRepaymentNew,
      isEnableDirectDiscount,
      isEnableMomoV2,
      selectedBank,
      virtualAccount,
      voucherCalculationData,
      voucherCode,
      repaymentLocalization);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return initialize(
        selectedAmount,
        contract,
        fullName,
        selectedPaymentMethod,
        fromRepaymentNew,
        isEnableDirectDiscount,
        isEnableMomoV2,
        selectedBank,
        virtualAccount,
        voucherCalculationData,
        voucherCode,
        repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return initialize?.call(
        selectedAmount,
        contract,
        fullName,
        selectedPaymentMethod,
        fromRepaymentNew,
        isEnableDirectDiscount,
        isEnableMomoV2,
        selectedBank,
        virtualAccount,
        voucherCalculationData,
        voucherCode,
        repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(
          selectedAmount,
          contract,
          fullName,
          selectedPaymentMethod,
          fromRepaymentNew,
          isEnableDirectDiscount,
          isEnableMomoV2,
          selectedBank,
          virtualAccount,
          voucherCalculationData,
          voucherCode,
          repaymentLocalization);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPaymentSummaryEvent {
  const factory _Initialize(
      {required final Decimal selectedAmount,
      required final RepaymentContract contract,
      required final String fullName,
      required final RepaymentUserPaymentMethod selectedPaymentMethod,
      required final bool fromRepaymentNew,
      required final bool isEnableDirectDiscount,
      final bool? isEnableMomoV2,
      final RepaymentBank? selectedBank,
      final RepaymentContractVirtualAccount? virtualAccount,
      final RepaymentVoucherCalculationData? voucherCalculationData,
      final String? voucherCode,
      required final L10nCappRepayment repaymentLocalization}) = _$_Initialize;

  Decimal get selectedAmount;
  RepaymentContract get contract;
  String get fullName;
  RepaymentUserPaymentMethod get selectedPaymentMethod;
  bool get fromRepaymentNew;
  bool get isEnableDirectDiscount;
  bool? get isEnableMomoV2;
  RepaymentBank? get selectedBank;
  RepaymentContractVirtualAccount? get virtualAccount;
  RepaymentVoucherCalculationData? get voucherCalculationData;
  String? get voucherCode;
  L10nCappRepayment get repaymentLocalization;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_MakeOnePayTransactionCopyWith<$Res> {
  factory _$$_MakeOnePayTransactionCopyWith(_$_MakeOnePayTransaction value,
          $Res Function(_$_MakeOnePayTransaction) then) =
      __$$_MakeOnePayTransactionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentOnePayTransactionRequest request});

  $RepaymentOnePayTransactionRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$_MakeOnePayTransactionCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_MakeOnePayTransaction>
    implements _$$_MakeOnePayTransactionCopyWith<$Res> {
  __$$_MakeOnePayTransactionCopyWithImpl(_$_MakeOnePayTransaction _value,
      $Res Function(_$_MakeOnePayTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_MakeOnePayTransaction(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentOnePayTransactionRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentOnePayTransactionRequestCopyWith<$Res> get request {
    return $RepaymentOnePayTransactionRequestCopyWith<$Res>(_value.request,
        (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$_MakeOnePayTransaction implements _MakeOnePayTransaction {
  const _$_MakeOnePayTransaction(this.request);

  @override
  final RepaymentOnePayTransactionRequest request;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.makeOnePayTransaction(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MakeOnePayTransaction &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MakeOnePayTransactionCopyWith<_$_MakeOnePayTransaction> get copyWith =>
      __$$_MakeOnePayTransactionCopyWithImpl<_$_MakeOnePayTransaction>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeOnePayTransaction(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeOnePayTransaction?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeOnePayTransaction != null) {
      return makeOnePayTransaction(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeOnePayTransaction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeOnePayTransaction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeOnePayTransaction != null) {
      return makeOnePayTransaction(this);
    }
    return orElse();
  }
}

abstract class _MakeOnePayTransaction implements RepaymentPaymentSummaryEvent {
  const factory _MakeOnePayTransaction(
          final RepaymentOnePayTransactionRequest request) =
      _$_MakeOnePayTransaction;

  RepaymentOnePayTransactionRequest get request;
  @JsonKey(ignore: true)
  _$$_MakeOnePayTransactionCopyWith<_$_MakeOnePayTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_MakeMomoTransactionCopyWith<$Res> {
  factory _$$_MakeMomoTransactionCopyWith(_$_MakeMomoTransaction value,
          $Res Function(_$_MakeMomoTransaction) then) =
      __$$_MakeMomoTransactionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentMomoTransactionRequest request});

  $RepaymentMomoTransactionRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$_MakeMomoTransactionCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_MakeMomoTransaction>
    implements _$$_MakeMomoTransactionCopyWith<$Res> {
  __$$_MakeMomoTransactionCopyWithImpl(_$_MakeMomoTransaction _value,
      $Res Function(_$_MakeMomoTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_MakeMomoTransaction(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentMomoTransactionRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentMomoTransactionRequestCopyWith<$Res> get request {
    return $RepaymentMomoTransactionRequestCopyWith<$Res>(_value.request,
        (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$_MakeMomoTransaction implements _MakeMomoTransaction {
  const _$_MakeMomoTransaction(this.request);

  @override
  final RepaymentMomoTransactionRequest request;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.makeMomoTransaction(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MakeMomoTransaction &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MakeMomoTransactionCopyWith<_$_MakeMomoTransaction> get copyWith =>
      __$$_MakeMomoTransactionCopyWithImpl<_$_MakeMomoTransaction>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeMomoTransaction(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeMomoTransaction?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeMomoTransaction != null) {
      return makeMomoTransaction(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeMomoTransaction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeMomoTransaction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeMomoTransaction != null) {
      return makeMomoTransaction(this);
    }
    return orElse();
  }
}

abstract class _MakeMomoTransaction implements RepaymentPaymentSummaryEvent {
  const factory _MakeMomoTransaction(
      final RepaymentMomoTransactionRequest request) = _$_MakeMomoTransaction;

  RepaymentMomoTransactionRequest get request;
  @JsonKey(ignore: true)
  _$$_MakeMomoTransactionCopyWith<_$_MakeMomoTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_MakeVnPayTransactionCopyWith<$Res> {
  factory _$$_MakeVnPayTransactionCopyWith(_$_MakeVnPayTransaction value,
          $Res Function(_$_MakeVnPayTransaction) then) =
      __$$_MakeVnPayTransactionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentVnPayTransactionRequest request});

  $RepaymentVnPayTransactionRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$_MakeVnPayTransactionCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_MakeVnPayTransaction>
    implements _$$_MakeVnPayTransactionCopyWith<$Res> {
  __$$_MakeVnPayTransactionCopyWithImpl(_$_MakeVnPayTransaction _value,
      $Res Function(_$_MakeVnPayTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_MakeVnPayTransaction(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentVnPayTransactionRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentVnPayTransactionRequestCopyWith<$Res> get request {
    return $RepaymentVnPayTransactionRequestCopyWith<$Res>(_value.request,
        (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$_MakeVnPayTransaction implements _MakeVnPayTransaction {
  const _$_MakeVnPayTransaction(this.request);

  @override
  final RepaymentVnPayTransactionRequest request;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.makeVnPayTransaction(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MakeVnPayTransaction &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MakeVnPayTransactionCopyWith<_$_MakeVnPayTransaction> get copyWith =>
      __$$_MakeVnPayTransactionCopyWithImpl<_$_MakeVnPayTransaction>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeVnPayTransaction(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeVnPayTransaction?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeVnPayTransaction != null) {
      return makeVnPayTransaction(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeVnPayTransaction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeVnPayTransaction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeVnPayTransaction != null) {
      return makeVnPayTransaction(this);
    }
    return orElse();
  }
}

abstract class _MakeVnPayTransaction implements RepaymentPaymentSummaryEvent {
  const factory _MakeVnPayTransaction(
      final RepaymentVnPayTransactionRequest request) = _$_MakeVnPayTransaction;

  RepaymentVnPayTransactionRequest get request;
  @JsonKey(ignore: true)
  _$$_MakeVnPayTransactionCopyWith<_$_MakeVnPayTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_MakeShopeePayTransactionCopyWith<$Res> {
  factory _$$_MakeShopeePayTransactionCopyWith(
          _$_MakeShopeePayTransaction value,
          $Res Function(_$_MakeShopeePayTransaction) then) =
      __$$_MakeShopeePayTransactionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentShopeePayTransactionRequest request});

  $RepaymentShopeePayTransactionRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$_MakeShopeePayTransactionCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_MakeShopeePayTransaction>
    implements _$$_MakeShopeePayTransactionCopyWith<$Res> {
  __$$_MakeShopeePayTransactionCopyWithImpl(_$_MakeShopeePayTransaction _value,
      $Res Function(_$_MakeShopeePayTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_MakeShopeePayTransaction(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentShopeePayTransactionRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentShopeePayTransactionRequestCopyWith<$Res> get request {
    return $RepaymentShopeePayTransactionRequestCopyWith<$Res>(_value.request,
        (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$_MakeShopeePayTransaction implements _MakeShopeePayTransaction {
  const _$_MakeShopeePayTransaction(this.request);

  @override
  final RepaymentShopeePayTransactionRequest request;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.makeShopeePayTransaction(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MakeShopeePayTransaction &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MakeShopeePayTransactionCopyWith<_$_MakeShopeePayTransaction>
      get copyWith => __$$_MakeShopeePayTransactionCopyWithImpl<
          _$_MakeShopeePayTransaction>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeShopeePayTransaction(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeShopeePayTransaction?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeShopeePayTransaction != null) {
      return makeShopeePayTransaction(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeShopeePayTransaction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeShopeePayTransaction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeShopeePayTransaction != null) {
      return makeShopeePayTransaction(this);
    }
    return orElse();
  }
}

abstract class _MakeShopeePayTransaction
    implements RepaymentPaymentSummaryEvent {
  const factory _MakeShopeePayTransaction(
          final RepaymentShopeePayTransactionRequest request) =
      _$_MakeShopeePayTransaction;

  RepaymentShopeePayTransactionRequest get request;
  @JsonKey(ignore: true)
  _$$_MakeShopeePayTransactionCopyWith<_$_MakeShopeePayTransaction>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_MakeZaloPayTransactionCopyWith<$Res> {
  factory _$$_MakeZaloPayTransactionCopyWith(_$_MakeZaloPayTransaction value,
          $Res Function(_$_MakeZaloPayTransaction) then) =
      __$$_MakeZaloPayTransactionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentZaloPayTransactionRequest request});

  $RepaymentZaloPayTransactionRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$_MakeZaloPayTransactionCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_MakeZaloPayTransaction>
    implements _$$_MakeZaloPayTransactionCopyWith<$Res> {
  __$$_MakeZaloPayTransactionCopyWithImpl(_$_MakeZaloPayTransaction _value,
      $Res Function(_$_MakeZaloPayTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_MakeZaloPayTransaction(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentZaloPayTransactionRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentZaloPayTransactionRequestCopyWith<$Res> get request {
    return $RepaymentZaloPayTransactionRequestCopyWith<$Res>(_value.request,
        (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$_MakeZaloPayTransaction implements _MakeZaloPayTransaction {
  const _$_MakeZaloPayTransaction(this.request);

  @override
  final RepaymentZaloPayTransactionRequest request;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.makeZaloPayTransaction(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MakeZaloPayTransaction &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MakeZaloPayTransactionCopyWith<_$_MakeZaloPayTransaction> get copyWith =>
      __$$_MakeZaloPayTransactionCopyWithImpl<_$_MakeZaloPayTransaction>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeZaloPayTransaction(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeZaloPayTransaction?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeZaloPayTransaction != null) {
      return makeZaloPayTransaction(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeZaloPayTransaction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeZaloPayTransaction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeZaloPayTransaction != null) {
      return makeZaloPayTransaction(this);
    }
    return orElse();
  }
}

abstract class _MakeZaloPayTransaction implements RepaymentPaymentSummaryEvent {
  const factory _MakeZaloPayTransaction(
          final RepaymentZaloPayTransactionRequest request) =
      _$_MakeZaloPayTransaction;

  RepaymentZaloPayTransactionRequest get request;
  @JsonKey(ignore: true)
  _$$_MakeZaloPayTransactionCopyWith<_$_MakeZaloPayTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_MakeViettelMoneyTransactionCopyWith<$Res> {
  factory _$$_MakeViettelMoneyTransactionCopyWith(
          _$_MakeViettelMoneyTransaction value,
          $Res Function(_$_MakeViettelMoneyTransaction) then) =
      __$$_MakeViettelMoneyTransactionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentViettelMoneyTransactionRequest request});

  $RepaymentViettelMoneyTransactionRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$_MakeViettelMoneyTransactionCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_MakeViettelMoneyTransaction>
    implements _$$_MakeViettelMoneyTransactionCopyWith<$Res> {
  __$$_MakeViettelMoneyTransactionCopyWithImpl(
      _$_MakeViettelMoneyTransaction _value,
      $Res Function(_$_MakeViettelMoneyTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_MakeViettelMoneyTransaction(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentViettelMoneyTransactionRequest,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentViettelMoneyTransactionRequestCopyWith<$Res> get request {
    return $RepaymentViettelMoneyTransactionRequestCopyWith<$Res>(
        _value.request, (value) {
      return _then(_value.copyWith(request: value));
    });
  }
}

/// @nodoc

class _$_MakeViettelMoneyTransaction implements _MakeViettelMoneyTransaction {
  const _$_MakeViettelMoneyTransaction(this.request);

  @override
  final RepaymentViettelMoneyTransactionRequest request;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.makeViettelMoneyTransaction(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MakeViettelMoneyTransaction &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MakeViettelMoneyTransactionCopyWith<_$_MakeViettelMoneyTransaction>
      get copyWith => __$$_MakeViettelMoneyTransactionCopyWithImpl<
          _$_MakeViettelMoneyTransaction>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeViettelMoneyTransaction(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeViettelMoneyTransaction?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeViettelMoneyTransaction != null) {
      return makeViettelMoneyTransaction(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return makeViettelMoneyTransaction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return makeViettelMoneyTransaction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (makeViettelMoneyTransaction != null) {
      return makeViettelMoneyTransaction(this);
    }
    return orElse();
  }
}

abstract class _MakeViettelMoneyTransaction
    implements RepaymentPaymentSummaryEvent {
  const factory _MakeViettelMoneyTransaction(
          final RepaymentViettelMoneyTransactionRequest request) =
      _$_MakeViettelMoneyTransaction;

  RepaymentViettelMoneyTransactionRequest get request;
  @JsonKey(ignore: true)
  _$$_MakeViettelMoneyTransactionCopyWith<_$_MakeViettelMoneyTransaction>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ConfirmCopyWith<$Res> {
  factory _$$_ConfirmCopyWith(
          _$_Confirm value, $Res Function(_$_Confirm) then) =
      __$$_ConfirmCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ConfirmCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res, _$_Confirm>
    implements _$$_ConfirmCopyWith<$Res> {
  __$$_ConfirmCopyWithImpl(_$_Confirm _value, $Res Function(_$_Confirm) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Confirm implements _Confirm {
  const _$_Confirm();

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.confirm()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Confirm);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return confirm();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return confirm?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (confirm != null) {
      return confirm();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return confirm(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return confirm?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (confirm != null) {
      return confirm(this);
    }
    return orElse();
  }
}

abstract class _Confirm implements RepaymentPaymentSummaryEvent {
  const factory _Confirm() = _$_Confirm;
}

/// @nodoc
abstract class _$$_CheckTransactionResultCopyWith<$Res> {
  factory _$$_CheckTransactionResultCopyWith(_$_CheckTransactionResult value,
          $Res Function(_$_CheckTransactionResult) then) =
      __$$_CheckTransactionResultCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_CheckTransactionResultCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_CheckTransactionResult>
    implements _$$_CheckTransactionResultCopyWith<$Res> {
  __$$_CheckTransactionResultCopyWithImpl(_$_CheckTransactionResult _value,
      $Res Function(_$_CheckTransactionResult) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_CheckTransactionResult implements _CheckTransactionResult {
  const _$_CheckTransactionResult();

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.checkTransactionResult()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckTransactionResult);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return checkTransactionResult();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return checkTransactionResult?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (checkTransactionResult != null) {
      return checkTransactionResult();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return checkTransactionResult(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return checkTransactionResult?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (checkTransactionResult != null) {
      return checkTransactionResult(this);
    }
    return orElse();
  }
}

abstract class _CheckTransactionResult implements RepaymentPaymentSummaryEvent {
  const factory _CheckTransactionResult() = _$_CheckTransactionResult;
}

/// @nodoc
abstract class _$$_StoreTransactionResultCopyWith<$Res> {
  factory _$$_StoreTransactionResultCopyWith(_$_StoreTransactionResult value,
          $Res Function(_$_StoreTransactionResult) then) =
      __$$_StoreTransactionResultCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentTransaction transactionResult});

  $RepaymentTransactionCopyWith<$Res> get transactionResult;
}

/// @nodoc
class __$$_StoreTransactionResultCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_StoreTransactionResult>
    implements _$$_StoreTransactionResultCopyWith<$Res> {
  __$$_StoreTransactionResultCopyWithImpl(_$_StoreTransactionResult _value,
      $Res Function(_$_StoreTransactionResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionResult = null,
  }) {
    return _then(_$_StoreTransactionResult(
      null == transactionResult
          ? _value.transactionResult
          : transactionResult // ignore: cast_nullable_to_non_nullable
              as RepaymentTransaction,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentTransactionCopyWith<$Res> get transactionResult {
    return $RepaymentTransactionCopyWith<$Res>(_value.transactionResult,
        (value) {
      return _then(_value.copyWith(transactionResult: value));
    });
  }
}

/// @nodoc

class _$_StoreTransactionResult implements _StoreTransactionResult {
  const _$_StoreTransactionResult(this.transactionResult);

  @override
  final RepaymentTransaction transactionResult;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.storeTransactionResult(transactionResult: $transactionResult)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StoreTransactionResult &&
            (identical(other.transactionResult, transactionResult) ||
                other.transactionResult == transactionResult));
  }

  @override
  int get hashCode => Object.hash(runtimeType, transactionResult);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_StoreTransactionResultCopyWith<_$_StoreTransactionResult> get copyWith =>
      __$$_StoreTransactionResultCopyWithImpl<_$_StoreTransactionResult>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return storeTransactionResult(transactionResult);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return storeTransactionResult?.call(transactionResult);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (storeTransactionResult != null) {
      return storeTransactionResult(transactionResult);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return storeTransactionResult(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return storeTransactionResult?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (storeTransactionResult != null) {
      return storeTransactionResult(this);
    }
    return orElse();
  }
}

abstract class _StoreTransactionResult implements RepaymentPaymentSummaryEvent {
  const factory _StoreTransactionResult(
      final RepaymentTransaction transactionResult) = _$_StoreTransactionResult;

  RepaymentTransaction get transactionResult;
  @JsonKey(ignore: true)
  _$$_StoreTransactionResultCopyWith<_$_StoreTransactionResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetCalledVnPayMobileBankingAppCopyWith<$Res> {
  factory _$$_SetCalledVnPayMobileBankingAppCopyWith(
          _$_SetCalledVnPayMobileBankingApp value,
          $Res Function(_$_SetCalledVnPayMobileBankingApp) then) =
      __$$_SetCalledVnPayMobileBankingAppCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isCalledVnPayMobileBankingApp});
}

/// @nodoc
class __$$_SetCalledVnPayMobileBankingAppCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryEventCopyWithImpl<$Res,
        _$_SetCalledVnPayMobileBankingApp>
    implements _$$_SetCalledVnPayMobileBankingAppCopyWith<$Res> {
  __$$_SetCalledVnPayMobileBankingAppCopyWithImpl(
      _$_SetCalledVnPayMobileBankingApp _value,
      $Res Function(_$_SetCalledVnPayMobileBankingApp) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCalledVnPayMobileBankingApp = null,
  }) {
    return _then(_$_SetCalledVnPayMobileBankingApp(
      isCalledVnPayMobileBankingApp: null == isCalledVnPayMobileBankingApp
          ? _value.isCalledVnPayMobileBankingApp
          : isCalledVnPayMobileBankingApp // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_SetCalledVnPayMobileBankingApp
    implements _SetCalledVnPayMobileBankingApp {
  const _$_SetCalledVnPayMobileBankingApp(
      {required this.isCalledVnPayMobileBankingApp});

  @override
  final bool isCalledVnPayMobileBankingApp;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryEvent.setCalledVnPayMobileBankingApp(isCalledVnPayMobileBankingApp: $isCalledVnPayMobileBankingApp)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetCalledVnPayMobileBankingApp &&
            (identical(other.isCalledVnPayMobileBankingApp,
                    isCalledVnPayMobileBankingApp) ||
                other.isCalledVnPayMobileBankingApp ==
                    isCalledVnPayMobileBankingApp));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isCalledVnPayMobileBankingApp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetCalledVnPayMobileBankingAppCopyWith<_$_SetCalledVnPayMobileBankingApp>
      get copyWith => __$$_SetCalledVnPayMobileBankingAppCopyWithImpl<
          _$_SetCalledVnPayMobileBankingApp>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentOnePayTransactionRequest request)
        makeOnePayTransaction,
    required TResult Function(RepaymentMomoTransactionRequest request)
        makeMomoTransaction,
    required TResult Function(RepaymentVnPayTransactionRequest request)
        makeVnPayTransaction,
    required TResult Function(RepaymentShopeePayTransactionRequest request)
        makeShopeePayTransaction,
    required TResult Function(RepaymentZaloPayTransactionRequest request)
        makeZaloPayTransaction,
    required TResult Function(RepaymentViettelMoneyTransactionRequest request)
        makeViettelMoneyTransaction,
    required TResult Function() confirm,
    required TResult Function() checkTransactionResult,
    required TResult Function(RepaymentTransaction transactionResult)
        storeTransactionResult,
    required TResult Function(bool isCalledVnPayMobileBankingApp)
        setCalledVnPayMobileBankingApp,
  }) {
    return setCalledVnPayMobileBankingApp(isCalledVnPayMobileBankingApp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult? Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult? Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult? Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult? Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult? Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult? Function()? confirm,
    TResult? Function()? checkTransactionResult,
    TResult? Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult? Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
  }) {
    return setCalledVnPayMobileBankingApp?.call(isCalledVnPayMobileBankingApp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            Decimal selectedAmount,
            RepaymentContract contract,
            String fullName,
            RepaymentUserPaymentMethod selectedPaymentMethod,
            bool fromRepaymentNew,
            bool isEnableDirectDiscount,
            bool? isEnableMomoV2,
            RepaymentBank? selectedBank,
            RepaymentContractVirtualAccount? virtualAccount,
            RepaymentVoucherCalculationData? voucherCalculationData,
            String? voucherCode,
            L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentOnePayTransactionRequest request)?
        makeOnePayTransaction,
    TResult Function(RepaymentMomoTransactionRequest request)?
        makeMomoTransaction,
    TResult Function(RepaymentVnPayTransactionRequest request)?
        makeVnPayTransaction,
    TResult Function(RepaymentShopeePayTransactionRequest request)?
        makeShopeePayTransaction,
    TResult Function(RepaymentZaloPayTransactionRequest request)?
        makeZaloPayTransaction,
    TResult Function(RepaymentViettelMoneyTransactionRequest request)?
        makeViettelMoneyTransaction,
    TResult Function()? confirm,
    TResult Function()? checkTransactionResult,
    TResult Function(RepaymentTransaction transactionResult)?
        storeTransactionResult,
    TResult Function(bool isCalledVnPayMobileBankingApp)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (setCalledVnPayMobileBankingApp != null) {
      return setCalledVnPayMobileBankingApp(isCalledVnPayMobileBankingApp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_MakeOnePayTransaction value)
        makeOnePayTransaction,
    required TResult Function(_MakeMomoTransaction value) makeMomoTransaction,
    required TResult Function(_MakeVnPayTransaction value) makeVnPayTransaction,
    required TResult Function(_MakeShopeePayTransaction value)
        makeShopeePayTransaction,
    required TResult Function(_MakeZaloPayTransaction value)
        makeZaloPayTransaction,
    required TResult Function(_MakeViettelMoneyTransaction value)
        makeViettelMoneyTransaction,
    required TResult Function(_Confirm value) confirm,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_StoreTransactionResult value)
        storeTransactionResult,
    required TResult Function(_SetCalledVnPayMobileBankingApp value)
        setCalledVnPayMobileBankingApp,
  }) {
    return setCalledVnPayMobileBankingApp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult? Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult? Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult? Function(_MakeShopeePayTransaction value)?
        makeShopeePayTransaction,
    TResult? Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult? Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult? Function(_Confirm value)? confirm,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult? Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
  }) {
    return setCalledVnPayMobileBankingApp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_MakeOnePayTransaction value)? makeOnePayTransaction,
    TResult Function(_MakeMomoTransaction value)? makeMomoTransaction,
    TResult Function(_MakeVnPayTransaction value)? makeVnPayTransaction,
    TResult Function(_MakeShopeePayTransaction value)? makeShopeePayTransaction,
    TResult Function(_MakeZaloPayTransaction value)? makeZaloPayTransaction,
    TResult Function(_MakeViettelMoneyTransaction value)?
        makeViettelMoneyTransaction,
    TResult Function(_Confirm value)? confirm,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_StoreTransactionResult value)? storeTransactionResult,
    TResult Function(_SetCalledVnPayMobileBankingApp value)?
        setCalledVnPayMobileBankingApp,
    required TResult orElse(),
  }) {
    if (setCalledVnPayMobileBankingApp != null) {
      return setCalledVnPayMobileBankingApp(this);
    }
    return orElse();
  }
}

abstract class _SetCalledVnPayMobileBankingApp
    implements RepaymentPaymentSummaryEvent {
  const factory _SetCalledVnPayMobileBankingApp(
          {required final bool isCalledVnPayMobileBankingApp}) =
      _$_SetCalledVnPayMobileBankingApp;

  bool get isCalledVnPayMobileBankingApp;
  @JsonKey(ignore: true)
  _$$_SetCalledVnPayMobileBankingAppCopyWith<_$_SetCalledVnPayMobileBankingApp>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentPaymentSummaryState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  Decimal get selectedAmount => throw _privateConstructorUsedError;
  RepaymentContract? get contract => throw _privateConstructorUsedError;
  String get fullName => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  bool get isPartnerProdSetting => throw _privateConstructorUsedError;
  List<RepaymentUserPaymentMethod>? get paymentMethods =>
      throw _privateConstructorUsedError;
  RepaymentUserPaymentMethod? get selectedPaymentMethod =>
      throw _privateConstructorUsedError;
  RepaymentBank? get selectedBank => throw _privateConstructorUsedError;
  RepaymentContractVirtualAccount? get virtualAccount =>
      throw _privateConstructorUsedError;
  bool get isEnableMomoV2 => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentOnePayTransaction>>
      get failureOrSuccessOnePay => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentMomoTransaction>>
      get failureOrSuccessMomo => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>
      get failureOrSuccessVnPay => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentShopeePayTransaction>>
      get failureOrSuccessShopeePay => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>>
      get failureOrSuccessZaloPay => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>
      get failureOrSuccessViettelMoney => throw _privateConstructorUsedError;
  bool get isCalledVnPayMobileBankingApp => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentTransaction>>
      get failureOrSuccessTransactionResult =>
          throw _privateConstructorUsedError;
  bool get fromRepaymentNew => throw _privateConstructorUsedError;
  bool get isEnableDirectDiscount => throw _privateConstructorUsedError;
  bool? get isCheckingTransactionResult => throw _privateConstructorUsedError;
  RepaymentMomoTransaction? get repaymentMomoTransaction =>
      throw _privateConstructorUsedError;
  RepaymentVnPayTransaction? get repaymentVnPayTransaction =>
      throw _privateConstructorUsedError;
  RepaymentShopeePayTransaction? get repaymentShopeePayTransaction =>
      throw _privateConstructorUsedError;
  RepaymentZaloPayTransaction? get repaymentZaloPayTransaction =>
      throw _privateConstructorUsedError;
  RepaymentTransaction? get repaymentTransaction =>
      throw _privateConstructorUsedError;
  String? get gmaTransactionId => throw _privateConstructorUsedError;
  RepaymentVoucherCalculationData? get voucherCalculationData =>
      throw _privateConstructorUsedError;
  String? get voucherCode => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPaymentSummaryStateCopyWith<RepaymentPaymentSummaryState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPaymentSummaryStateCopyWith<$Res> {
  factory $RepaymentPaymentSummaryStateCopyWith(
          RepaymentPaymentSummaryState value,
          $Res Function(RepaymentPaymentSummaryState) then) =
      _$RepaymentPaymentSummaryStateCopyWithImpl<$Res,
          RepaymentPaymentSummaryState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      Decimal selectedAmount,
      RepaymentContract? contract,
      String fullName,
      String phoneNumber,
      bool isPartnerProdSetting,
      List<RepaymentUserPaymentMethod>? paymentMethods,
      RepaymentUserPaymentMethod? selectedPaymentMethod,
      RepaymentBank? selectedBank,
      RepaymentContractVirtualAccount? virtualAccount,
      bool isEnableMomoV2,
      Option<Either<RepaymentFailure, RepaymentOnePayTransaction>>
          failureOrSuccessOnePay,
      Option<Either<RepaymentFailure, RepaymentMomoTransaction>>
          failureOrSuccessMomo,
      Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>
          failureOrSuccessVnPay,
      Option<Either<RepaymentFailure, RepaymentShopeePayTransaction>>
          failureOrSuccessShopeePay,
      Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>>
          failureOrSuccessZaloPay,
      Option<Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>
          failureOrSuccessViettelMoney,
      bool isCalledVnPayMobileBankingApp,
      Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      bool fromRepaymentNew,
      bool isEnableDirectDiscount,
      bool? isCheckingTransactionResult,
      RepaymentMomoTransaction? repaymentMomoTransaction,
      RepaymentVnPayTransaction? repaymentVnPayTransaction,
      RepaymentShopeePayTransaction? repaymentShopeePayTransaction,
      RepaymentZaloPayTransaction? repaymentZaloPayTransaction,
      RepaymentTransaction? repaymentTransaction,
      String? gmaTransactionId,
      RepaymentVoucherCalculationData? voucherCalculationData,
      String? voucherCode});

  $RepaymentMomoTransactionCopyWith<$Res>? get repaymentMomoTransaction;
  $RepaymentVnPayTransactionCopyWith<$Res>? get repaymentVnPayTransaction;
  $RepaymentShopeePayTransactionCopyWith<$Res>?
      get repaymentShopeePayTransaction;
  $RepaymentZaloPayTransactionCopyWith<$Res>? get repaymentZaloPayTransaction;
  $RepaymentTransactionCopyWith<$Res>? get repaymentTransaction;
}

/// @nodoc
class _$RepaymentPaymentSummaryStateCopyWithImpl<$Res,
        $Val extends RepaymentPaymentSummaryState>
    implements $RepaymentPaymentSummaryStateCopyWith<$Res> {
  _$RepaymentPaymentSummaryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? selectedAmount = null,
    Object? contract = freezed,
    Object? fullName = null,
    Object? phoneNumber = null,
    Object? isPartnerProdSetting = null,
    Object? paymentMethods = freezed,
    Object? selectedPaymentMethod = freezed,
    Object? selectedBank = freezed,
    Object? virtualAccount = freezed,
    Object? isEnableMomoV2 = null,
    Object? failureOrSuccessOnePay = null,
    Object? failureOrSuccessMomo = null,
    Object? failureOrSuccessVnPay = null,
    Object? failureOrSuccessShopeePay = null,
    Object? failureOrSuccessZaloPay = null,
    Object? failureOrSuccessViettelMoney = null,
    Object? isCalledVnPayMobileBankingApp = null,
    Object? failureOrSuccessTransactionResult = null,
    Object? fromRepaymentNew = null,
    Object? isEnableDirectDiscount = null,
    Object? isCheckingTransactionResult = freezed,
    Object? repaymentMomoTransaction = freezed,
    Object? repaymentVnPayTransaction = freezed,
    Object? repaymentShopeePayTransaction = freezed,
    Object? repaymentZaloPayTransaction = freezed,
    Object? repaymentTransaction = freezed,
    Object? gmaTransactionId = freezed,
    Object? voucherCalculationData = freezed,
    Object? voucherCode = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      selectedAmount: null == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      contract: freezed == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      isPartnerProdSetting: null == isPartnerProdSetting
          ? _value.isPartnerProdSetting
          : isPartnerProdSetting // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentMethods: freezed == paymentMethods
          ? _value.paymentMethods
          : paymentMethods // ignore: cast_nullable_to_non_nullable
              as List<RepaymentUserPaymentMethod>?,
      selectedPaymentMethod: freezed == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      virtualAccount: freezed == virtualAccount
          ? _value.virtualAccount
          : virtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      isEnableMomoV2: null == isEnableMomoV2
          ? _value.isEnableMomoV2
          : isEnableMomoV2 // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOnePay: null == failureOrSuccessOnePay
          ? _value.failureOrSuccessOnePay
          : failureOrSuccessOnePay // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentOnePayTransaction>>,
      failureOrSuccessMomo: null == failureOrSuccessMomo
          ? _value.failureOrSuccessMomo
          : failureOrSuccessMomo // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentMomoTransaction>>,
      failureOrSuccessVnPay: null == failureOrSuccessVnPay
          ? _value.failureOrSuccessVnPay
          : failureOrSuccessVnPay // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>,
      failureOrSuccessShopeePay: null == failureOrSuccessShopeePay
          ? _value.failureOrSuccessShopeePay
          : failureOrSuccessShopeePay // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentShopeePayTransaction>>,
      failureOrSuccessZaloPay: null == failureOrSuccessZaloPay
          ? _value.failureOrSuccessZaloPay
          : failureOrSuccessZaloPay // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>>,
      failureOrSuccessViettelMoney: null == failureOrSuccessViettelMoney
          ? _value.failureOrSuccessViettelMoney
          : failureOrSuccessViettelMoney // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>,
      isCalledVnPayMobileBankingApp: null == isCalledVnPayMobileBankingApp
          ? _value.isCalledVnPayMobileBankingApp
          : isCalledVnPayMobileBankingApp // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessTransactionResult: null ==
              failureOrSuccessTransactionResult
          ? _value.failureOrSuccessTransactionResult
          : failureOrSuccessTransactionResult // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentTransaction>>,
      fromRepaymentNew: null == fromRepaymentNew
          ? _value.fromRepaymentNew
          : fromRepaymentNew // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableDirectDiscount: null == isEnableDirectDiscount
          ? _value.isEnableDirectDiscount
          : isEnableDirectDiscount // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckingTransactionResult: freezed == isCheckingTransactionResult
          ? _value.isCheckingTransactionResult
          : isCheckingTransactionResult // ignore: cast_nullable_to_non_nullable
              as bool?,
      repaymentMomoTransaction: freezed == repaymentMomoTransaction
          ? _value.repaymentMomoTransaction
          : repaymentMomoTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentMomoTransaction?,
      repaymentVnPayTransaction: freezed == repaymentVnPayTransaction
          ? _value.repaymentVnPayTransaction
          : repaymentVnPayTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentVnPayTransaction?,
      repaymentShopeePayTransaction: freezed == repaymentShopeePayTransaction
          ? _value.repaymentShopeePayTransaction
          : repaymentShopeePayTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentShopeePayTransaction?,
      repaymentZaloPayTransaction: freezed == repaymentZaloPayTransaction
          ? _value.repaymentZaloPayTransaction
          : repaymentZaloPayTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentZaloPayTransaction?,
      repaymentTransaction: freezed == repaymentTransaction
          ? _value.repaymentTransaction
          : repaymentTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentTransaction?,
      gmaTransactionId: freezed == gmaTransactionId
          ? _value.gmaTransactionId
          : gmaTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      voucherCalculationData: freezed == voucherCalculationData
          ? _value.voucherCalculationData
          : voucherCalculationData // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherCalculationData?,
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentMomoTransactionCopyWith<$Res>? get repaymentMomoTransaction {
    if (_value.repaymentMomoTransaction == null) {
      return null;
    }

    return $RepaymentMomoTransactionCopyWith<$Res>(
        _value.repaymentMomoTransaction!, (value) {
      return _then(_value.copyWith(repaymentMomoTransaction: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentVnPayTransactionCopyWith<$Res>? get repaymentVnPayTransaction {
    if (_value.repaymentVnPayTransaction == null) {
      return null;
    }

    return $RepaymentVnPayTransactionCopyWith<$Res>(
        _value.repaymentVnPayTransaction!, (value) {
      return _then(_value.copyWith(repaymentVnPayTransaction: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentShopeePayTransactionCopyWith<$Res>?
      get repaymentShopeePayTransaction {
    if (_value.repaymentShopeePayTransaction == null) {
      return null;
    }

    return $RepaymentShopeePayTransactionCopyWith<$Res>(
        _value.repaymentShopeePayTransaction!, (value) {
      return _then(
          _value.copyWith(repaymentShopeePayTransaction: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentZaloPayTransactionCopyWith<$Res>? get repaymentZaloPayTransaction {
    if (_value.repaymentZaloPayTransaction == null) {
      return null;
    }

    return $RepaymentZaloPayTransactionCopyWith<$Res>(
        _value.repaymentZaloPayTransaction!, (value) {
      return _then(_value.copyWith(repaymentZaloPayTransaction: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RepaymentTransactionCopyWith<$Res>? get repaymentTransaction {
    if (_value.repaymentTransaction == null) {
      return null;
    }

    return $RepaymentTransactionCopyWith<$Res>(_value.repaymentTransaction!,
        (value) {
      return _then(_value.copyWith(repaymentTransaction: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_RepaymentPaymentSummaryStateCopyWith<$Res>
    implements $RepaymentPaymentSummaryStateCopyWith<$Res> {
  factory _$$_RepaymentPaymentSummaryStateCopyWith(
          _$_RepaymentPaymentSummaryState value,
          $Res Function(_$_RepaymentPaymentSummaryState) then) =
      __$$_RepaymentPaymentSummaryStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      Decimal selectedAmount,
      RepaymentContract? contract,
      String fullName,
      String phoneNumber,
      bool isPartnerProdSetting,
      List<RepaymentUserPaymentMethod>? paymentMethods,
      RepaymentUserPaymentMethod? selectedPaymentMethod,
      RepaymentBank? selectedBank,
      RepaymentContractVirtualAccount? virtualAccount,
      bool isEnableMomoV2,
      Option<Either<RepaymentFailure, RepaymentOnePayTransaction>>
          failureOrSuccessOnePay,
      Option<Either<RepaymentFailure, RepaymentMomoTransaction>>
          failureOrSuccessMomo,
      Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>
          failureOrSuccessVnPay,
      Option<Either<RepaymentFailure, RepaymentShopeePayTransaction>>
          failureOrSuccessShopeePay,
      Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>>
          failureOrSuccessZaloPay,
      Option<Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>
          failureOrSuccessViettelMoney,
      bool isCalledVnPayMobileBankingApp,
      Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      bool fromRepaymentNew,
      bool isEnableDirectDiscount,
      bool? isCheckingTransactionResult,
      RepaymentMomoTransaction? repaymentMomoTransaction,
      RepaymentVnPayTransaction? repaymentVnPayTransaction,
      RepaymentShopeePayTransaction? repaymentShopeePayTransaction,
      RepaymentZaloPayTransaction? repaymentZaloPayTransaction,
      RepaymentTransaction? repaymentTransaction,
      String? gmaTransactionId,
      RepaymentVoucherCalculationData? voucherCalculationData,
      String? voucherCode});

  @override
  $RepaymentMomoTransactionCopyWith<$Res>? get repaymentMomoTransaction;
  @override
  $RepaymentVnPayTransactionCopyWith<$Res>? get repaymentVnPayTransaction;
  @override
  $RepaymentShopeePayTransactionCopyWith<$Res>?
      get repaymentShopeePayTransaction;
  @override
  $RepaymentZaloPayTransactionCopyWith<$Res>? get repaymentZaloPayTransaction;
  @override
  $RepaymentTransactionCopyWith<$Res>? get repaymentTransaction;
}

/// @nodoc
class __$$_RepaymentPaymentSummaryStateCopyWithImpl<$Res>
    extends _$RepaymentPaymentSummaryStateCopyWithImpl<$Res,
        _$_RepaymentPaymentSummaryState>
    implements _$$_RepaymentPaymentSummaryStateCopyWith<$Res> {
  __$$_RepaymentPaymentSummaryStateCopyWithImpl(
      _$_RepaymentPaymentSummaryState _value,
      $Res Function(_$_RepaymentPaymentSummaryState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? selectedAmount = null,
    Object? contract = freezed,
    Object? fullName = null,
    Object? phoneNumber = null,
    Object? isPartnerProdSetting = null,
    Object? paymentMethods = freezed,
    Object? selectedPaymentMethod = freezed,
    Object? selectedBank = freezed,
    Object? virtualAccount = freezed,
    Object? isEnableMomoV2 = null,
    Object? failureOrSuccessOnePay = null,
    Object? failureOrSuccessMomo = null,
    Object? failureOrSuccessVnPay = null,
    Object? failureOrSuccessShopeePay = null,
    Object? failureOrSuccessZaloPay = null,
    Object? failureOrSuccessViettelMoney = null,
    Object? isCalledVnPayMobileBankingApp = null,
    Object? failureOrSuccessTransactionResult = null,
    Object? fromRepaymentNew = null,
    Object? isEnableDirectDiscount = null,
    Object? isCheckingTransactionResult = freezed,
    Object? repaymentMomoTransaction = freezed,
    Object? repaymentVnPayTransaction = freezed,
    Object? repaymentShopeePayTransaction = freezed,
    Object? repaymentZaloPayTransaction = freezed,
    Object? repaymentTransaction = freezed,
    Object? gmaTransactionId = freezed,
    Object? voucherCalculationData = freezed,
    Object? voucherCode = freezed,
  }) {
    return _then(_$_RepaymentPaymentSummaryState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      selectedAmount: null == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      contract: freezed == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      isPartnerProdSetting: null == isPartnerProdSetting
          ? _value.isPartnerProdSetting
          : isPartnerProdSetting // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentMethods: freezed == paymentMethods
          ? _value._paymentMethods
          : paymentMethods // ignore: cast_nullable_to_non_nullable
              as List<RepaymentUserPaymentMethod>?,
      selectedPaymentMethod: freezed == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      virtualAccount: freezed == virtualAccount
          ? _value.virtualAccount
          : virtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      isEnableMomoV2: null == isEnableMomoV2
          ? _value.isEnableMomoV2
          : isEnableMomoV2 // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOnePay: null == failureOrSuccessOnePay
          ? _value.failureOrSuccessOnePay
          : failureOrSuccessOnePay // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentOnePayTransaction>>,
      failureOrSuccessMomo: null == failureOrSuccessMomo
          ? _value.failureOrSuccessMomo
          : failureOrSuccessMomo // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentMomoTransaction>>,
      failureOrSuccessVnPay: null == failureOrSuccessVnPay
          ? _value.failureOrSuccessVnPay
          : failureOrSuccessVnPay // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>,
      failureOrSuccessShopeePay: null == failureOrSuccessShopeePay
          ? _value.failureOrSuccessShopeePay
          : failureOrSuccessShopeePay // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentShopeePayTransaction>>,
      failureOrSuccessZaloPay: null == failureOrSuccessZaloPay
          ? _value.failureOrSuccessZaloPay
          : failureOrSuccessZaloPay // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>>,
      failureOrSuccessViettelMoney: null == failureOrSuccessViettelMoney
          ? _value.failureOrSuccessViettelMoney
          : failureOrSuccessViettelMoney // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>,
      isCalledVnPayMobileBankingApp: null == isCalledVnPayMobileBankingApp
          ? _value.isCalledVnPayMobileBankingApp
          : isCalledVnPayMobileBankingApp // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessTransactionResult: null ==
              failureOrSuccessTransactionResult
          ? _value.failureOrSuccessTransactionResult
          : failureOrSuccessTransactionResult // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentTransaction>>,
      fromRepaymentNew: null == fromRepaymentNew
          ? _value.fromRepaymentNew
          : fromRepaymentNew // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnableDirectDiscount: null == isEnableDirectDiscount
          ? _value.isEnableDirectDiscount
          : isEnableDirectDiscount // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckingTransactionResult: freezed == isCheckingTransactionResult
          ? _value.isCheckingTransactionResult
          : isCheckingTransactionResult // ignore: cast_nullable_to_non_nullable
              as bool?,
      repaymentMomoTransaction: freezed == repaymentMomoTransaction
          ? _value.repaymentMomoTransaction
          : repaymentMomoTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentMomoTransaction?,
      repaymentVnPayTransaction: freezed == repaymentVnPayTransaction
          ? _value.repaymentVnPayTransaction
          : repaymentVnPayTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentVnPayTransaction?,
      repaymentShopeePayTransaction: freezed == repaymentShopeePayTransaction
          ? _value.repaymentShopeePayTransaction
          : repaymentShopeePayTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentShopeePayTransaction?,
      repaymentZaloPayTransaction: freezed == repaymentZaloPayTransaction
          ? _value.repaymentZaloPayTransaction
          : repaymentZaloPayTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentZaloPayTransaction?,
      repaymentTransaction: freezed == repaymentTransaction
          ? _value.repaymentTransaction
          : repaymentTransaction // ignore: cast_nullable_to_non_nullable
              as RepaymentTransaction?,
      gmaTransactionId: freezed == gmaTransactionId
          ? _value.gmaTransactionId
          : gmaTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      voucherCalculationData: freezed == voucherCalculationData
          ? _value.voucherCalculationData
          : voucherCalculationData // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherCalculationData?,
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_RepaymentPaymentSummaryState implements _RepaymentPaymentSummaryState {
  const _$_RepaymentPaymentSummaryState(
      {required this.loadingState,
      required this.selectedAmount,
      required this.contract,
      required this.fullName,
      required this.phoneNumber,
      required this.isPartnerProdSetting,
      final List<RepaymentUserPaymentMethod>? paymentMethods,
      required this.selectedPaymentMethod,
      this.selectedBank,
      this.virtualAccount,
      this.isEnableMomoV2 = false,
      required this.failureOrSuccessOnePay,
      required this.failureOrSuccessMomo,
      required this.failureOrSuccessVnPay,
      required this.failureOrSuccessShopeePay,
      required this.failureOrSuccessZaloPay,
      required this.failureOrSuccessViettelMoney,
      required this.isCalledVnPayMobileBankingApp,
      required this.failureOrSuccessTransactionResult,
      required this.fromRepaymentNew,
      required this.isEnableDirectDiscount,
      this.isCheckingTransactionResult,
      this.repaymentMomoTransaction,
      this.repaymentVnPayTransaction,
      this.repaymentShopeePayTransaction,
      this.repaymentZaloPayTransaction,
      this.repaymentTransaction,
      this.gmaTransactionId,
      this.voucherCalculationData,
      this.voucherCode})
      : _paymentMethods = paymentMethods;

  @override
  final LoadingState loadingState;
  @override
  final Decimal selectedAmount;
  @override
  final RepaymentContract? contract;
  @override
  final String fullName;
  @override
  final String phoneNumber;
  @override
  final bool isPartnerProdSetting;
  final List<RepaymentUserPaymentMethod>? _paymentMethods;
  @override
  List<RepaymentUserPaymentMethod>? get paymentMethods {
    final value = _paymentMethods;
    if (value == null) return null;
    if (_paymentMethods is EqualUnmodifiableListView) return _paymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  @override
  final RepaymentBank? selectedBank;
  @override
  final RepaymentContractVirtualAccount? virtualAccount;
  @override
  @JsonKey()
  final bool isEnableMomoV2;
  @override
  final Option<Either<RepaymentFailure, RepaymentOnePayTransaction>>
      failureOrSuccessOnePay;
  @override
  final Option<Either<RepaymentFailure, RepaymentMomoTransaction>>
      failureOrSuccessMomo;
  @override
  final Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>
      failureOrSuccessVnPay;
  @override
  final Option<Either<RepaymentFailure, RepaymentShopeePayTransaction>>
      failureOrSuccessShopeePay;
  @override
  final Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>>
      failureOrSuccessZaloPay;
  @override
  final Option<Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>
      failureOrSuccessViettelMoney;
  @override
  final bool isCalledVnPayMobileBankingApp;
  @override
  final Option<Either<RepaymentFailure, RepaymentTransaction>>
      failureOrSuccessTransactionResult;
  @override
  final bool fromRepaymentNew;
  @override
  final bool isEnableDirectDiscount;
  @override
  final bool? isCheckingTransactionResult;
  @override
  final RepaymentMomoTransaction? repaymentMomoTransaction;
  @override
  final RepaymentVnPayTransaction? repaymentVnPayTransaction;
  @override
  final RepaymentShopeePayTransaction? repaymentShopeePayTransaction;
  @override
  final RepaymentZaloPayTransaction? repaymentZaloPayTransaction;
  @override
  final RepaymentTransaction? repaymentTransaction;
  @override
  final String? gmaTransactionId;
  @override
  final RepaymentVoucherCalculationData? voucherCalculationData;
  @override
  final String? voucherCode;

  @override
  String toString() {
    return 'RepaymentPaymentSummaryState(loadingState: $loadingState, selectedAmount: $selectedAmount, contract: $contract, fullName: $fullName, phoneNumber: $phoneNumber, isPartnerProdSetting: $isPartnerProdSetting, paymentMethods: $paymentMethods, selectedPaymentMethod: $selectedPaymentMethod, selectedBank: $selectedBank, virtualAccount: $virtualAccount, isEnableMomoV2: $isEnableMomoV2, failureOrSuccessOnePay: $failureOrSuccessOnePay, failureOrSuccessMomo: $failureOrSuccessMomo, failureOrSuccessVnPay: $failureOrSuccessVnPay, failureOrSuccessShopeePay: $failureOrSuccessShopeePay, failureOrSuccessZaloPay: $failureOrSuccessZaloPay, failureOrSuccessViettelMoney: $failureOrSuccessViettelMoney, isCalledVnPayMobileBankingApp: $isCalledVnPayMobileBankingApp, failureOrSuccessTransactionResult: $failureOrSuccessTransactionResult, fromRepaymentNew: $fromRepaymentNew, isEnableDirectDiscount: $isEnableDirectDiscount, isCheckingTransactionResult: $isCheckingTransactionResult, repaymentMomoTransaction: $repaymentMomoTransaction, repaymentVnPayTransaction: $repaymentVnPayTransaction, repaymentShopeePayTransaction: $repaymentShopeePayTransaction, repaymentZaloPayTransaction: $repaymentZaloPayTransaction, repaymentTransaction: $repaymentTransaction, gmaTransactionId: $gmaTransactionId, voucherCalculationData: $voucherCalculationData, voucherCode: $voucherCode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPaymentSummaryState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.contract, contract) ||
                other.contract == contract) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.isPartnerProdSetting, isPartnerProdSetting) ||
                other.isPartnerProdSetting == isPartnerProdSetting) &&
            const DeepCollectionEquality()
                .equals(other._paymentMethods, _paymentMethods) &&
            (identical(other.selectedPaymentMethod, selectedPaymentMethod) ||
                other.selectedPaymentMethod == selectedPaymentMethod) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.virtualAccount, virtualAccount) ||
                other.virtualAccount == virtualAccount) &&
            (identical(other.isEnableMomoV2, isEnableMomoV2) ||
                other.isEnableMomoV2 == isEnableMomoV2) &&
            (identical(other.failureOrSuccessOnePay, failureOrSuccessOnePay) ||
                other.failureOrSuccessOnePay == failureOrSuccessOnePay) &&
            (identical(other.failureOrSuccessMomo, failureOrSuccessMomo) ||
                other.failureOrSuccessMomo == failureOrSuccessMomo) &&
            (identical(other.failureOrSuccessVnPay, failureOrSuccessVnPay) ||
                other.failureOrSuccessVnPay == failureOrSuccessVnPay) &&
            (identical(other.failureOrSuccessShopeePay, failureOrSuccessShopeePay) ||
                other.failureOrSuccessShopeePay == failureOrSuccessShopeePay) &&
            (identical(other.failureOrSuccessZaloPay, failureOrSuccessZaloPay) ||
                other.failureOrSuccessZaloPay == failureOrSuccessZaloPay) &&
            (identical(other.failureOrSuccessViettelMoney, failureOrSuccessViettelMoney) ||
                other.failureOrSuccessViettelMoney ==
                    failureOrSuccessViettelMoney) &&
            (identical(other.isCalledVnPayMobileBankingApp, isCalledVnPayMobileBankingApp) ||
                other.isCalledVnPayMobileBankingApp ==
                    isCalledVnPayMobileBankingApp) &&
            (identical(other.failureOrSuccessTransactionResult, failureOrSuccessTransactionResult) ||
                other.failureOrSuccessTransactionResult ==
                    failureOrSuccessTransactionResult) &&
            (identical(other.fromRepaymentNew, fromRepaymentNew) ||
                other.fromRepaymentNew == fromRepaymentNew) &&
            (identical(other.isEnableDirectDiscount, isEnableDirectDiscount) ||
                other.isEnableDirectDiscount == isEnableDirectDiscount) &&
            (identical(other.isCheckingTransactionResult, isCheckingTransactionResult) ||
                other.isCheckingTransactionResult ==
                    isCheckingTransactionResult) &&
            (identical(other.repaymentMomoTransaction, repaymentMomoTransaction) ||
                other.repaymentMomoTransaction == repaymentMomoTransaction) &&
            (identical(other.repaymentVnPayTransaction, repaymentVnPayTransaction) ||
                other.repaymentVnPayTransaction == repaymentVnPayTransaction) &&
            (identical(other.repaymentShopeePayTransaction, repaymentShopeePayTransaction) ||
                other.repaymentShopeePayTransaction ==
                    repaymentShopeePayTransaction) &&
            (identical(other.repaymentZaloPayTransaction, repaymentZaloPayTransaction) ||
                other.repaymentZaloPayTransaction == repaymentZaloPayTransaction) &&
            (identical(other.repaymentTransaction, repaymentTransaction) || other.repaymentTransaction == repaymentTransaction) &&
            (identical(other.gmaTransactionId, gmaTransactionId) || other.gmaTransactionId == gmaTransactionId) &&
            (identical(other.voucherCalculationData, voucherCalculationData) || other.voucherCalculationData == voucherCalculationData) &&
            (identical(other.voucherCode, voucherCode) || other.voucherCode == voucherCode));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        loadingState,
        selectedAmount,
        contract,
        fullName,
        phoneNumber,
        isPartnerProdSetting,
        const DeepCollectionEquality().hash(_paymentMethods),
        selectedPaymentMethod,
        selectedBank,
        virtualAccount,
        isEnableMomoV2,
        failureOrSuccessOnePay,
        failureOrSuccessMomo,
        failureOrSuccessVnPay,
        failureOrSuccessShopeePay,
        failureOrSuccessZaloPay,
        failureOrSuccessViettelMoney,
        isCalledVnPayMobileBankingApp,
        failureOrSuccessTransactionResult,
        fromRepaymentNew,
        isEnableDirectDiscount,
        isCheckingTransactionResult,
        repaymentMomoTransaction,
        repaymentVnPayTransaction,
        repaymentShopeePayTransaction,
        repaymentZaloPayTransaction,
        repaymentTransaction,
        gmaTransactionId,
        voucherCalculationData,
        voucherCode
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPaymentSummaryStateCopyWith<_$_RepaymentPaymentSummaryState>
      get copyWith => __$$_RepaymentPaymentSummaryStateCopyWithImpl<
          _$_RepaymentPaymentSummaryState>(this, _$identity);
}

abstract class _RepaymentPaymentSummaryState
    implements RepaymentPaymentSummaryState {
  const factory _RepaymentPaymentSummaryState(
      {required final LoadingState loadingState,
      required final Decimal selectedAmount,
      required final RepaymentContract? contract,
      required final String fullName,
      required final String phoneNumber,
      required final bool isPartnerProdSetting,
      final List<RepaymentUserPaymentMethod>? paymentMethods,
      required final RepaymentUserPaymentMethod? selectedPaymentMethod,
      final RepaymentBank? selectedBank,
      final RepaymentContractVirtualAccount? virtualAccount,
      final bool isEnableMomoV2,
      required final Option<
              Either<RepaymentFailure, RepaymentOnePayTransaction>>
          failureOrSuccessOnePay,
      required final Option<Either<RepaymentFailure, RepaymentMomoTransaction>>
          failureOrSuccessMomo,
      required final Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>
          failureOrSuccessVnPay,
      required final Option<
              Either<RepaymentFailure, RepaymentShopeePayTransaction>>
          failureOrSuccessShopeePay,
      required final Option<
              Either<RepaymentFailure, RepaymentZaloPayTransaction>>
          failureOrSuccessZaloPay,
      required final Option<
              Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>
          failureOrSuccessViettelMoney,
      required final bool isCalledVnPayMobileBankingApp,
      required final Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      required final bool fromRepaymentNew,
      required final bool isEnableDirectDiscount,
      final bool? isCheckingTransactionResult,
      final RepaymentMomoTransaction? repaymentMomoTransaction,
      final RepaymentVnPayTransaction? repaymentVnPayTransaction,
      final RepaymentShopeePayTransaction? repaymentShopeePayTransaction,
      final RepaymentZaloPayTransaction? repaymentZaloPayTransaction,
      final RepaymentTransaction? repaymentTransaction,
      final String? gmaTransactionId,
      final RepaymentVoucherCalculationData? voucherCalculationData,
      final String? voucherCode}) = _$_RepaymentPaymentSummaryState;

  @override
  LoadingState get loadingState;
  @override
  Decimal get selectedAmount;
  @override
  RepaymentContract? get contract;
  @override
  String get fullName;
  @override
  String get phoneNumber;
  @override
  bool get isPartnerProdSetting;
  @override
  List<RepaymentUserPaymentMethod>? get paymentMethods;
  @override
  RepaymentUserPaymentMethod? get selectedPaymentMethod;
  @override
  RepaymentBank? get selectedBank;
  @override
  RepaymentContractVirtualAccount? get virtualAccount;
  @override
  bool get isEnableMomoV2;
  @override
  Option<Either<RepaymentFailure, RepaymentOnePayTransaction>>
      get failureOrSuccessOnePay;
  @override
  Option<Either<RepaymentFailure, RepaymentMomoTransaction>>
      get failureOrSuccessMomo;
  @override
  Option<Either<RepaymentFailure, RepaymentVnPayTransaction>>
      get failureOrSuccessVnPay;
  @override
  Option<Either<RepaymentFailure, RepaymentShopeePayTransaction>>
      get failureOrSuccessShopeePay;
  @override
  Option<Either<RepaymentFailure, RepaymentZaloPayTransaction>>
      get failureOrSuccessZaloPay;
  @override
  Option<Either<RepaymentFailure, RepaymentViettelMoneyTransaction>>
      get failureOrSuccessViettelMoney;
  @override
  bool get isCalledVnPayMobileBankingApp;
  @override
  Option<Either<RepaymentFailure, RepaymentTransaction>>
      get failureOrSuccessTransactionResult;
  @override
  bool get fromRepaymentNew;
  @override
  bool get isEnableDirectDiscount;
  @override
  bool? get isCheckingTransactionResult;
  @override
  RepaymentMomoTransaction? get repaymentMomoTransaction;
  @override
  RepaymentVnPayTransaction? get repaymentVnPayTransaction;
  @override
  RepaymentShopeePayTransaction? get repaymentShopeePayTransaction;
  @override
  RepaymentZaloPayTransaction? get repaymentZaloPayTransaction;
  @override
  RepaymentTransaction? get repaymentTransaction;
  @override
  String? get gmaTransactionId;
  @override
  RepaymentVoucherCalculationData? get voucherCalculationData;
  @override
  String? get voucherCode;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPaymentSummaryStateCopyWith<_$_RepaymentPaymentSummaryState>
      get copyWith => throw _privateConstructorUsedError;
}
