import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../domain/index.dart';

part 'repayment_qr_bank_bloc.freezed.dart';
part 'repayment_qr_bank_event.dart';
part 'repayment_qr_bank_state.dart';

class RepaymentQrBankBloc extends Bloc<RepaymentQrBankEvent, RepaymentQrBankState> {
  final Logger logger;
  final IRepaymentRepository repaymentRepository;
  RepaymentQrBankBloc({
    required this.repaymentRepository,
    required this.logger,
  }) : super(RepaymentQrBankState.initialize()) {
    on<_Initialize>((e, emit) async {
      final banks = e.banks;
      final selectedBank = e.selectedBank;

      emit(
        state.copyWith(
          loadingState: LoadingState.isInitial,
          banks: banks,
          selectedBank: selectedBank,
        ),
      );
    });
    on<_SelectBank>((e, emit) async {
      final bank = e.bank;
      await repaymentRepository.setQrBankToCache(bank);

      emit(
        state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: false,
          selectedBank: bank,
        ),
      );
    });
    on<_UpdateSearchText>((e, emit) async {
      emit(
        state.copyWith(
          searchText: e.text,
        ),
      );
    });
  }
}
