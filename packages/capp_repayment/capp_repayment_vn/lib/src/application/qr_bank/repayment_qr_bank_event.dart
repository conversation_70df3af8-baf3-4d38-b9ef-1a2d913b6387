part of 'repayment_qr_bank_bloc.dart';

@freezed
class RepaymentQrBankEvent with _$RepaymentQrBankEvent {
  const factory RepaymentQrBankEvent.initialize({
    required List<RepaymentQrBank> banks,
    RepaymentQrBank? selectedBank,
  }) = _Initialize;
  const factory RepaymentQrBankEvent.selectBank({required RepaymentQrBank bank}) = _SelectBank;
  const factory RepaymentQrBankEvent.updateSearchText({required String text}) = _UpdateSearchText;
}
