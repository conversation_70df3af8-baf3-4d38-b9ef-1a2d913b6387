// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_contract_list_new_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentContractListNewEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)
        initialize,
    required TResult Function(RepaymentContract contract) selectContract,
    required TResult Function() refreshContract,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult? Function(RepaymentContract contract)? selectContract,
    TResult? Function()? refreshContract,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult Function(RepaymentContract contract)? selectContract,
    TResult Function()? refreshContract,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentContractListNewEventCopyWith<$Res> {
  factory $RepaymentContractListNewEventCopyWith(
          RepaymentContractListNewEvent value,
          $Res Function(RepaymentContractListNewEvent) then) =
      _$RepaymentContractListNewEventCopyWithImpl<$Res,
          RepaymentContractListNewEvent>;
}

/// @nodoc
class _$RepaymentContractListNewEventCopyWithImpl<$Res,
        $Val extends RepaymentContractListNewEvent>
    implements $RepaymentContractListNewEventCopyWith<$Res> {
  _$RepaymentContractListNewEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<RepaymentContract> contracts, RepaymentContract? selectedContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentContractListNewEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contracts = null,
    Object? selectedContract = freezed,
  }) {
    return _then(_$_Initialize(
      contracts: null == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required final List<RepaymentContract> contracts, this.selectedContract})
      : _contracts = contracts;

  final List<RepaymentContract> _contracts;
  @override
  List<RepaymentContract> get contracts {
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contracts);
  }

  @override
  final RepaymentContract? selectedContract;

  @override
  String toString() {
    return 'RepaymentContractListNewEvent.initialize(contracts: $contracts, selectedContract: $selectedContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            const DeepCollectionEquality()
                .equals(other._contracts, _contracts) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_contracts), selectedContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)
        initialize,
    required TResult Function(RepaymentContract contract) selectContract,
    required TResult Function() refreshContract,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
  }) {
    return initialize(contracts, selectedContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult? Function(RepaymentContract contract)? selectContract,
    TResult? Function()? refreshContract,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
  }) {
    return initialize?.call(contracts, selectedContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult Function(RepaymentContract contract)? selectContract,
    TResult Function()? refreshContract,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(contracts, selectedContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentContractListNewEvent {
  const factory _Initialize(
      {required final List<RepaymentContract> contracts,
      final RepaymentContract? selectedContract}) = _$_Initialize;

  List<RepaymentContract> get contracts;
  RepaymentContract? get selectedContract;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectContractCopyWith<$Res> {
  factory _$$_SelectContractCopyWith(
          _$_SelectContract value, $Res Function(_$_SelectContract) then) =
      __$$_SelectContractCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentContract contract});
}

/// @nodoc
class __$$_SelectContractCopyWithImpl<$Res>
    extends _$RepaymentContractListNewEventCopyWithImpl<$Res, _$_SelectContract>
    implements _$$_SelectContractCopyWith<$Res> {
  __$$_SelectContractCopyWithImpl(
      _$_SelectContract _value, $Res Function(_$_SelectContract) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contract = null,
  }) {
    return _then(_$_SelectContract(
      contract: null == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract,
    ));
  }
}

/// @nodoc

class _$_SelectContract implements _SelectContract {
  const _$_SelectContract({required this.contract});

  @override
  final RepaymentContract contract;

  @override
  String toString() {
    return 'RepaymentContractListNewEvent.selectContract(contract: $contract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectContract &&
            (identical(other.contract, contract) ||
                other.contract == contract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectContractCopyWith<_$_SelectContract> get copyWith =>
      __$$_SelectContractCopyWithImpl<_$_SelectContract>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)
        initialize,
    required TResult Function(RepaymentContract contract) selectContract,
    required TResult Function() refreshContract,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
  }) {
    return selectContract(contract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult? Function(RepaymentContract contract)? selectContract,
    TResult? Function()? refreshContract,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
  }) {
    return selectContract?.call(contract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult Function(RepaymentContract contract)? selectContract,
    TResult Function()? refreshContract,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (selectContract != null) {
      return selectContract(contract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
  }) {
    return selectContract(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
  }) {
    return selectContract?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (selectContract != null) {
      return selectContract(this);
    }
    return orElse();
  }
}

abstract class _SelectContract implements RepaymentContractListNewEvent {
  const factory _SelectContract({required final RepaymentContract contract}) =
      _$_SelectContract;

  RepaymentContract get contract;
  @JsonKey(ignore: true)
  _$$_SelectContractCopyWith<_$_SelectContract> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_RefreshContractCopyWith<$Res> {
  factory _$$_RefreshContractCopyWith(
          _$_RefreshContract value, $Res Function(_$_RefreshContract) then) =
      __$$_RefreshContractCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_RefreshContractCopyWithImpl<$Res>
    extends _$RepaymentContractListNewEventCopyWithImpl<$Res,
        _$_RefreshContract> implements _$$_RefreshContractCopyWith<$Res> {
  __$$_RefreshContractCopyWithImpl(
      _$_RefreshContract _value, $Res Function(_$_RefreshContract) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_RefreshContract implements _RefreshContract {
  const _$_RefreshContract();

  @override
  String toString() {
    return 'RepaymentContractListNewEvent.refreshContract()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_RefreshContract);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)
        initialize,
    required TResult Function(RepaymentContract contract) selectContract,
    required TResult Function() refreshContract,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
  }) {
    return refreshContract();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult? Function(RepaymentContract contract)? selectContract,
    TResult? Function()? refreshContract,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
  }) {
    return refreshContract?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult Function(RepaymentContract contract)? selectContract,
    TResult Function()? refreshContract,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (refreshContract != null) {
      return refreshContract();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
  }) {
    return refreshContract(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
  }) {
    return refreshContract?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (refreshContract != null) {
      return refreshContract(this);
    }
    return orElse();
  }
}

abstract class _RefreshContract implements RepaymentContractListNewEvent {
  const factory _RefreshContract() = _$_RefreshContract;
}

/// @nodoc
abstract class _$$_ProccessKoyalDataMessageCopyWith<$Res> {
  factory _$$_ProccessKoyalDataMessageCopyWith(
          _$_ProccessKoyalDataMessage value,
          $Res Function(_$_ProccessKoyalDataMessage) then) =
      __$$_ProccessKoyalDataMessageCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationMessage? notification});

  $NotificationMessageCopyWith<$Res>? get notification;
}

/// @nodoc
class __$$_ProccessKoyalDataMessageCopyWithImpl<$Res>
    extends _$RepaymentContractListNewEventCopyWithImpl<$Res,
        _$_ProccessKoyalDataMessage>
    implements _$$_ProccessKoyalDataMessageCopyWith<$Res> {
  __$$_ProccessKoyalDataMessageCopyWithImpl(_$_ProccessKoyalDataMessage _value,
      $Res Function(_$_ProccessKoyalDataMessage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = freezed,
  }) {
    return _then(_$_ProccessKoyalDataMessage(
      freezed == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationMessage?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<$Res>? get notification {
    if (_value.notification == null) {
      return null;
    }

    return $NotificationMessageCopyWith<$Res>(_value.notification!, (value) {
      return _then(_value.copyWith(notification: value));
    });
  }
}

/// @nodoc

class _$_ProccessKoyalDataMessage implements _ProccessKoyalDataMessage {
  const _$_ProccessKoyalDataMessage(this.notification);

  @override
  final NotificationMessage? notification;

  @override
  String toString() {
    return 'RepaymentContractListNewEvent.proccessKoyalDataMessage(notification: $notification)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProccessKoyalDataMessage &&
            (identical(other.notification, notification) ||
                other.notification == notification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notification);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProccessKoyalDataMessageCopyWith<_$_ProccessKoyalDataMessage>
      get copyWith => __$$_ProccessKoyalDataMessageCopyWithImpl<
          _$_ProccessKoyalDataMessage>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)
        initialize,
    required TResult Function(RepaymentContract contract) selectContract,
    required TResult Function() refreshContract,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
  }) {
    return proccessKoyalDataMessage(notification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult? Function(RepaymentContract contract)? selectContract,
    TResult? Function()? refreshContract,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
  }) {
    return proccessKoyalDataMessage?.call(notification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentContract> contracts,
            RepaymentContract? selectedContract)?
        initialize,
    TResult Function(RepaymentContract contract)? selectContract,
    TResult Function()? refreshContract,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (proccessKoyalDataMessage != null) {
      return proccessKoyalDataMessage(notification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
  }) {
    return proccessKoyalDataMessage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
  }) {
    return proccessKoyalDataMessage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    required TResult orElse(),
  }) {
    if (proccessKoyalDataMessage != null) {
      return proccessKoyalDataMessage(this);
    }
    return orElse();
  }
}

abstract class _ProccessKoyalDataMessage
    implements RepaymentContractListNewEvent {
  const factory _ProccessKoyalDataMessage(
      final NotificationMessage? notification) = _$_ProccessKoyalDataMessage;

  NotificationMessage? get notification;
  @JsonKey(ignore: true)
  _$$_ProccessKoyalDataMessageCopyWith<_$_ProccessKoyalDataMessage>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentContractListNewState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  RepaymentContract? get selectedContract => throw _privateConstructorUsedError;
  List<RepaymentContract> get contracts => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccessFetchContracts => throw _privateConstructorUsedError;
  bool get isNeedToDismiss => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentContractListNewStateCopyWith<RepaymentContractListNewState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentContractListNewStateCopyWith<$Res> {
  factory $RepaymentContractListNewStateCopyWith(
          RepaymentContractListNewState value,
          $Res Function(RepaymentContractListNewState) then) =
      _$RepaymentContractListNewStateCopyWithImpl<$Res,
          RepaymentContractListNewState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      RepaymentContract? selectedContract,
      List<RepaymentContract> contracts,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessFetchContracts,
      bool isNeedToDismiss});
}

/// @nodoc
class _$RepaymentContractListNewStateCopyWithImpl<$Res,
        $Val extends RepaymentContractListNewState>
    implements $RepaymentContractListNewStateCopyWith<$Res> {
  _$RepaymentContractListNewStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedContract = freezed,
    Object? contracts = null,
    Object? failureOrSuccessFetchContracts = null,
    Object? isNeedToDismiss = null,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      contracts: null == contracts
          ? _value.contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>,
      failureOrSuccessFetchContracts: null == failureOrSuccessFetchContracts
          ? _value.failureOrSuccessFetchContracts
          : failureOrSuccessFetchContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      isNeedToDismiss: null == isNeedToDismiss
          ? _value.isNeedToDismiss
          : isNeedToDismiss // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentContractListNewStateCopyWith<$Res>
    implements $RepaymentContractListNewStateCopyWith<$Res> {
  factory _$$_RepaymentContractListNewStateCopyWith(
          _$_RepaymentContractListNewState value,
          $Res Function(_$_RepaymentContractListNewState) then) =
      __$$_RepaymentContractListNewStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      RepaymentContract? selectedContract,
      List<RepaymentContract> contracts,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessFetchContracts,
      bool isNeedToDismiss});
}

/// @nodoc
class __$$_RepaymentContractListNewStateCopyWithImpl<$Res>
    extends _$RepaymentContractListNewStateCopyWithImpl<$Res,
        _$_RepaymentContractListNewState>
    implements _$$_RepaymentContractListNewStateCopyWith<$Res> {
  __$$_RepaymentContractListNewStateCopyWithImpl(
      _$_RepaymentContractListNewState _value,
      $Res Function(_$_RepaymentContractListNewState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedContract = freezed,
    Object? contracts = null,
    Object? failureOrSuccessFetchContracts = null,
    Object? isNeedToDismiss = null,
  }) {
    return _then(_$_RepaymentContractListNewState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      contracts: null == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>,
      failureOrSuccessFetchContracts: null == failureOrSuccessFetchContracts
          ? _value.failureOrSuccessFetchContracts
          : failureOrSuccessFetchContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      isNeedToDismiss: null == isNeedToDismiss
          ? _value.isNeedToDismiss
          : isNeedToDismiss // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_RepaymentContractListNewState
    implements _RepaymentContractListNewState {
  const _$_RepaymentContractListNewState(
      {required this.loadingState,
      this.isError,
      this.selectedContract,
      final List<RepaymentContract> contracts = const <RepaymentContract>[],
      required this.failureOrSuccessFetchContracts,
      this.isNeedToDismiss = false})
      : _contracts = contracts;

  @override
  final LoadingState loadingState;
  @override
  final bool? isError;
  @override
  final RepaymentContract? selectedContract;
  final List<RepaymentContract> _contracts;
  @override
  @JsonKey()
  List<RepaymentContract> get contracts {
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contracts);
  }

  @override
  final Option<Either<RepaymentFailure, List<RepaymentContract>>>
      failureOrSuccessFetchContracts;
  @override
  @JsonKey()
  final bool isNeedToDismiss;

  @override
  String toString() {
    return 'RepaymentContractListNewState(loadingState: $loadingState, isError: $isError, selectedContract: $selectedContract, contracts: $contracts, failureOrSuccessFetchContracts: $failureOrSuccessFetchContracts, isNeedToDismiss: $isNeedToDismiss)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentContractListNewState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            const DeepCollectionEquality()
                .equals(other._contracts, _contracts) &&
            (identical(other.failureOrSuccessFetchContracts,
                    failureOrSuccessFetchContracts) ||
                other.failureOrSuccessFetchContracts ==
                    failureOrSuccessFetchContracts) &&
            (identical(other.isNeedToDismiss, isNeedToDismiss) ||
                other.isNeedToDismiss == isNeedToDismiss));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      isError,
      selectedContract,
      const DeepCollectionEquality().hash(_contracts),
      failureOrSuccessFetchContracts,
      isNeedToDismiss);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentContractListNewStateCopyWith<_$_RepaymentContractListNewState>
      get copyWith => __$$_RepaymentContractListNewStateCopyWithImpl<
          _$_RepaymentContractListNewState>(this, _$identity);
}

abstract class _RepaymentContractListNewState
    implements RepaymentContractListNewState {
  const factory _RepaymentContractListNewState(
      {required final LoadingState loadingState,
      final bool? isError,
      final RepaymentContract? selectedContract,
      final List<RepaymentContract> contracts,
      required final Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessFetchContracts,
      final bool isNeedToDismiss}) = _$_RepaymentContractListNewState;

  @override
  LoadingState get loadingState;
  @override
  bool? get isError;
  @override
  RepaymentContract? get selectedContract;
  @override
  List<RepaymentContract> get contracts;
  @override
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccessFetchContracts;
  @override
  bool get isNeedToDismiss;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentContractListNewStateCopyWith<_$_RepaymentContractListNewState>
      get copyWith => throw _privateConstructorUsedError;
}
