import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../../capp_repayment.dart';

part 'repayment_contract_list_new_bloc.freezed.dart';
part 'repayment_contract_list_new_event.dart';
part 'repayment_contract_list_new_state.dart';

class RepaymentContractListNewBloc extends Bloc<RepaymentContractListNewEvent, RepaymentContractListNewState> {
  final IRepaymentRepository repaymentRepository;
  final Logger logger;

  RepaymentContractListNewBloc({
    required this.repaymentRepository,
    required this.logger,
  }) : super(RepaymentContractListNewState.initialize()) {
    on<RepaymentContractListNewEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          selectContract: (e) => _selectContract(e, emit),
          refreshContract: (e) => _refreshContract(e, emit),
          proccessKoyalDataMessage: (e) => _proccessKoyalDataMessage(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentContractListNewState> emit,
  ) async {
    emit(
      state.copyWith(
        contracts: e.contracts,
        selectedContract: e.selectedContract,
      ),
    );
  }

  Future<void> _selectContract(
    _SelectContract e,
    Emitter<RepaymentContractListNewState> emit,
  ) async {
    emit(
      state.copyWith(
        selectedContract: e.contract,
      ),
    );
  }

  Future<void> _refreshContract(
    _RefreshContract e,
    Emitter<RepaymentContractListNewState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false));

    final apiResponse = await repaymentRepository.getContracts();
    logger.d('getContracts response$apiResponse');
    final response = apiResponse.map((r) => r.parseToRepaymentContract());

    emit(
      await response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: true,
          failureOrSuccessFetchContracts: optionOf(response),
        );
      }, (r) async {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessFetchContracts: optionOf(response),
          isError: false,
          contracts: r,
          isNeedToDismiss: checkIsNeedToDismiss(r, state.selectedContract),
        );
      }),
    );
  }

  Future<void> _proccessKoyalDataMessage(
    _ProccessKoyalDataMessage e,
    Emitter<RepaymentContractListNewState> emit,
  ) async {
    if (e.notification?.data?.notificationType == MessageType.contractsAccountBalanceChanged) {
      add(const RepaymentContractListNewEvent.refreshContract());
    }
  }

  bool isCelContractPayable(RepaymentLoanContract contract) {
    return contract.totalOutstandingDebt != Decimal.zero;
  }

  bool checkIsNeedToDismiss(List<RepaymentContract> contracts, RepaymentContract? selectedContract) {
    final isExist =
        contracts.where((contract) => contract.contractNumber == selectedContract?.contractNumber).toList().isNotEmpty;

    if (contracts.length == 1 || !isExist) {
      return true;
    }
    return false;
  }
}
