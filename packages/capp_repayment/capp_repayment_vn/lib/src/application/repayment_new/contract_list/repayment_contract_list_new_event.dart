part of 'repayment_contract_list_new_bloc.dart';

@freezed
class RepaymentContractListNewEvent with _$RepaymentContractListNewEvent {
  const factory RepaymentContractListNewEvent.initialize({
    required List<RepaymentContract> contracts,
    RepaymentContract? selectedContract,
  }) = _Initialize;
  const factory RepaymentContractListNewEvent.selectContract({
    required RepaymentContract contract,
  }) = _SelectContract;

  const factory RepaymentContractListNewEvent.refreshContract() = _RefreshContract;

  const factory RepaymentContractListNewEvent.proccessKoyalDataMessage(NotificationMessage? notification) =
      _ProccessKoyalDataMessage;
}
