// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_banking_app_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentBankingAppEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<RepaymentBank> banks, RepaymentBank? selectedBank)
        initialize,
    required TResult Function(RepaymentBank bank) selectBank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentBank> banks, RepaymentBank? selectedBank)?
        initialize,
    TResult? Function(RepaymentBank bank)? selectBank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentBank> banks, RepaymentBank? selectedBank)?
        initialize,
    TResult Function(RepaymentBank bank)? selectBank,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectBank value) selectBank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectBank value)? selectBank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectBank value)? selectBank,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBankingAppEventCopyWith<$Res> {
  factory $RepaymentBankingAppEventCopyWith(RepaymentBankingAppEvent value,
          $Res Function(RepaymentBankingAppEvent) then) =
      _$RepaymentBankingAppEventCopyWithImpl<$Res, RepaymentBankingAppEvent>;
}

/// @nodoc
class _$RepaymentBankingAppEventCopyWithImpl<$Res,
        $Val extends RepaymentBankingAppEvent>
    implements $RepaymentBankingAppEventCopyWith<$Res> {
  _$RepaymentBankingAppEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({List<RepaymentBank> banks, RepaymentBank? selectedBank});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentBankingAppEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? banks = null,
    Object? selectedBank = freezed,
  }) {
    return _then(_$_Initialize(
      banks: null == banks
          ? _value._banks
          : banks // ignore: cast_nullable_to_non_nullable
              as List<RepaymentBank>,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required final List<RepaymentBank> banks, this.selectedBank})
      : _banks = banks;

  final List<RepaymentBank> _banks;
  @override
  List<RepaymentBank> get banks {
    if (_banks is EqualUnmodifiableListView) return _banks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_banks);
  }

  @override
  final RepaymentBank? selectedBank;

  @override
  String toString() {
    return 'RepaymentBankingAppEvent.initialize(banks: $banks, selectedBank: $selectedBank)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            const DeepCollectionEquality().equals(other._banks, _banks) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_banks), selectedBank);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<RepaymentBank> banks, RepaymentBank? selectedBank)
        initialize,
    required TResult Function(RepaymentBank bank) selectBank,
  }) {
    return initialize(banks, selectedBank);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentBank> banks, RepaymentBank? selectedBank)?
        initialize,
    TResult? Function(RepaymentBank bank)? selectBank,
  }) {
    return initialize?.call(banks, selectedBank);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentBank> banks, RepaymentBank? selectedBank)?
        initialize,
    TResult Function(RepaymentBank bank)? selectBank,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(banks, selectedBank);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectBank value) selectBank,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectBank value)? selectBank,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectBank value)? selectBank,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentBankingAppEvent {
  const factory _Initialize(
      {required final List<RepaymentBank> banks,
      final RepaymentBank? selectedBank}) = _$_Initialize;

  List<RepaymentBank> get banks;
  RepaymentBank? get selectedBank;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectBankCopyWith<$Res> {
  factory _$$_SelectBankCopyWith(
          _$_SelectBank value, $Res Function(_$_SelectBank) then) =
      __$$_SelectBankCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentBank bank});
}

/// @nodoc
class __$$_SelectBankCopyWithImpl<$Res>
    extends _$RepaymentBankingAppEventCopyWithImpl<$Res, _$_SelectBank>
    implements _$$_SelectBankCopyWith<$Res> {
  __$$_SelectBankCopyWithImpl(
      _$_SelectBank _value, $Res Function(_$_SelectBank) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bank = null,
  }) {
    return _then(_$_SelectBank(
      bank: null == bank
          ? _value.bank
          : bank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank,
    ));
  }
}

/// @nodoc

class _$_SelectBank implements _SelectBank {
  const _$_SelectBank({required this.bank});

  @override
  final RepaymentBank bank;

  @override
  String toString() {
    return 'RepaymentBankingAppEvent.selectBank(bank: $bank)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectBank &&
            (identical(other.bank, bank) || other.bank == bank));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bank);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectBankCopyWith<_$_SelectBank> get copyWith =>
      __$$_SelectBankCopyWithImpl<_$_SelectBank>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<RepaymentBank> banks, RepaymentBank? selectedBank)
        initialize,
    required TResult Function(RepaymentBank bank) selectBank,
  }) {
    return selectBank(bank);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<RepaymentBank> banks, RepaymentBank? selectedBank)?
        initialize,
    TResult? Function(RepaymentBank bank)? selectBank,
  }) {
    return selectBank?.call(bank);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<RepaymentBank> banks, RepaymentBank? selectedBank)?
        initialize,
    TResult Function(RepaymentBank bank)? selectBank,
    required TResult orElse(),
  }) {
    if (selectBank != null) {
      return selectBank(bank);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectBank value) selectBank,
  }) {
    return selectBank(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectBank value)? selectBank,
  }) {
    return selectBank?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectBank value)? selectBank,
    required TResult orElse(),
  }) {
    if (selectBank != null) {
      return selectBank(this);
    }
    return orElse();
  }
}

abstract class _SelectBank implements RepaymentBankingAppEvent {
  const factory _SelectBank({required final RepaymentBank bank}) =
      _$_SelectBank;

  RepaymentBank get bank;
  @JsonKey(ignore: true)
  _$$_SelectBankCopyWith<_$_SelectBank> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentBankingAppState {
  RepaymentBank? get selectedBank => throw _privateConstructorUsedError;
  List<RepaymentBank> get banks => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentBankingAppStateCopyWith<RepaymentBankingAppState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBankingAppStateCopyWith<$Res> {
  factory $RepaymentBankingAppStateCopyWith(RepaymentBankingAppState value,
          $Res Function(RepaymentBankingAppState) then) =
      _$RepaymentBankingAppStateCopyWithImpl<$Res, RepaymentBankingAppState>;
  @useResult
  $Res call({RepaymentBank? selectedBank, List<RepaymentBank> banks});
}

/// @nodoc
class _$RepaymentBankingAppStateCopyWithImpl<$Res,
        $Val extends RepaymentBankingAppState>
    implements $RepaymentBankingAppStateCopyWith<$Res> {
  _$RepaymentBankingAppStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedBank = freezed,
    Object? banks = null,
  }) {
    return _then(_value.copyWith(
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      banks: null == banks
          ? _value.banks
          : banks // ignore: cast_nullable_to_non_nullable
              as List<RepaymentBank>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentBankingAppStateCopyWith<$Res>
    implements $RepaymentBankingAppStateCopyWith<$Res> {
  factory _$$_RepaymentBankingAppStateCopyWith(
          _$_RepaymentBankingAppState value,
          $Res Function(_$_RepaymentBankingAppState) then) =
      __$$_RepaymentBankingAppStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({RepaymentBank? selectedBank, List<RepaymentBank> banks});
}

/// @nodoc
class __$$_RepaymentBankingAppStateCopyWithImpl<$Res>
    extends _$RepaymentBankingAppStateCopyWithImpl<$Res,
        _$_RepaymentBankingAppState>
    implements _$$_RepaymentBankingAppStateCopyWith<$Res> {
  __$$_RepaymentBankingAppStateCopyWithImpl(_$_RepaymentBankingAppState _value,
      $Res Function(_$_RepaymentBankingAppState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedBank = freezed,
    Object? banks = null,
  }) {
    return _then(_$_RepaymentBankingAppState(
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      banks: null == banks
          ? _value._banks
          : banks // ignore: cast_nullable_to_non_nullable
              as List<RepaymentBank>,
    ));
  }
}

/// @nodoc

class _$_RepaymentBankingAppState implements _RepaymentBankingAppState {
  const _$_RepaymentBankingAppState(
      {this.selectedBank,
      final List<RepaymentBank> banks = const <RepaymentBank>[]})
      : _banks = banks;

  @override
  final RepaymentBank? selectedBank;
  final List<RepaymentBank> _banks;
  @override
  @JsonKey()
  List<RepaymentBank> get banks {
    if (_banks is EqualUnmodifiableListView) return _banks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_banks);
  }

  @override
  String toString() {
    return 'RepaymentBankingAppState(selectedBank: $selectedBank, banks: $banks)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentBankingAppState &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            const DeepCollectionEquality().equals(other._banks, _banks));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, selectedBank, const DeepCollectionEquality().hash(_banks));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentBankingAppStateCopyWith<_$_RepaymentBankingAppState>
      get copyWith => __$$_RepaymentBankingAppStateCopyWithImpl<
          _$_RepaymentBankingAppState>(this, _$identity);
}

abstract class _RepaymentBankingAppState implements RepaymentBankingAppState {
  const factory _RepaymentBankingAppState(
      {final RepaymentBank? selectedBank,
      final List<RepaymentBank> banks}) = _$_RepaymentBankingAppState;

  @override
  RepaymentBank? get selectedBank;
  @override
  List<RepaymentBank> get banks;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentBankingAppStateCopyWith<_$_RepaymentBankingAppState>
      get copyWith => throw _privateConstructorUsedError;
}
