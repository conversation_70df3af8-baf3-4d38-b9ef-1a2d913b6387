import 'package:collection/collection.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:logger/logger.dart';

import '../../../../capp_repayment.dart';

part 'repayment_banking_app_bloc.freezed.dart';
part 'repayment_banking_app_event.dart';
part 'repayment_banking_app_state.dart';

class RepaymentBankingAppBloc extends Bloc<RepaymentBankingAppEvent, RepaymentBankingAppState> {
  final Logger logger;
  final IRepaymentRepository repaymentRepository;

  RepaymentBankingAppBloc({
    required this.logger,
    required this.repaymentRepository,
  }) : super(RepaymentBankingAppState.initialize()) {
    on<_Initialize>(_initialize);
    on<_SelectBank>(_selectBank);
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentBankingAppState> emit,
  ) async {
    final banks = e.banks;
    final selectedBank = e.selectedBank;
    final previousMobileBankingAppShortName = await repaymentRepository.getPreviousMobileBankingAppShortNameFromCache();

    final previousMobileBankingApp = banks.firstWhereOrNull((e) => e.shortName == previousMobileBankingAppShortName);

    emit(
      state.copyWith(
        banks: e.banks,
        selectedBank: selectedBank ?? previousMobileBankingApp,
      ),
    );
  }

  Future<void> _selectBank(
    _SelectBank e,
    Emitter<RepaymentBankingAppState> emit,
  ) async {
    emit(
      state.copyWith(
        selectedBank: e.bank,
      ),
    );
  }

  static List<RepaymentBank> getBaoKiemBanks() {
    final banks = <RepaymentBank>[
      const RepaymentBank(
        shortName: 'VIETCOMBANK',
        name: 'Ngân hàng TMCP Ngoại thương Việt Nam',
        iconName: 'ic_repayment_vcb.png',
        thumbnail: 'ic_repayment_thumbnail_vcb.png',
      ),
      const RepaymentBank(
        shortName: 'BIDV',
        name: 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam',
        iconName: 'ic_repayment_bidv.png',
        thumbnail: 'ic_repayment_thumbnail_bidv.png',
      ),
      const RepaymentBank(
        shortName: 'VIETINBANK',
        name: 'Ngân hàng TMCP Công Thương Việt Nam',
        iconName: 'ic_repayment_vietin.png',
        thumbnail: 'ic_repayment_thumbnail_vietin.png',
      ),
      const RepaymentBank(
        shortName: 'AGRIBANK',
        name: 'Ngân hàng Nông Nghiệp và Phát triển Nông Thôn Việt Nam',
        iconName: 'ic_repayment_agri.png',
        thumbnail: 'ic_repayment_thumbnail_agri.png',
      ),
      const RepaymentBank(
        shortName: 'TECHCOMBANK',
        name: 'Ngân hàng TMCP Kỹ thương Việt Nam',
        iconName: 'ic_repayment_techcom.png',
        thumbnail: 'ic_repayment_thumbnail_techcom.png',
      ),
      const RepaymentBank(
        shortName: 'VPBANK',
        name: 'Ngân hàng TMCP Việt Nam Thịnh Vương',
        iconName: 'ic_repayment_vp.png',
        thumbnail: 'ic_repayment_thumbnail_vp.png',
      ),
      const RepaymentBank(
        shortName: 'MBBANK',
        name: 'Ngân hàng TMCP Quân Đội',
        iconName: 'ic_repayment_mb.png',
        thumbnail: 'ic_repayment_thumbnail_mb.png',
      ),
      const RepaymentBank(
        shortName: 'ACB',
        name: 'Ngân hàng TMCP Á Châu',
        iconName: 'ic_repayment_acb.png',
        thumbnail: 'ic_repayment_thumbnail_acb.png',
      ),
      const RepaymentBank(
        shortName: 'SHB',
        name: 'Ngân hàng TMCP Sài Gòn - Hà Nội',
        iconName: 'ic_repayment_shb.png',
        thumbnail: 'ic_repayment_thumbnail_shb.png',
      ),
      const RepaymentBank(
        shortName: 'TPBANK',
        name: 'Ngân hàng TMCP Tiên Phong',
        iconName: 'ic_repayment_tp.png',
        thumbnail: 'ic_repayment_thumbnail_tp.png',
      ),
      const RepaymentBank(
        shortName: 'SEABANK',
        name: 'Ngân hàng TMCP Đông Nam Á',
        iconName: 'ic_repayment_sea.png',
        thumbnail: 'ic_repayment_thumbnail_sea.png',
      ),
      const RepaymentBank(
        shortName: 'VIB',
        name: 'Ngân hàng TMCP Quốc Tế',
        iconName: 'ic_repayment_vib.png',
        thumbnail: 'ic_repayment_thumbnail_vib.png',
      ),
      const RepaymentBank(
        shortName: 'ABBANK',
        name: 'Ngân hàng TMCP An Bình',
        iconName: 'ic_repayment_abb.png',
        thumbnail: 'ic_repayment_thumbnail_abb.png',
      ),
      const RepaymentBank(
        shortName: 'SCB',
        name: 'Ngân hàng TMCP Sài Gòn',
        iconName: 'ic_repayment_scb.png',
        thumbnail: 'ic_repayment_thumbnail_scb.png',
      ),
      const RepaymentBank(
        shortName: 'VIETABANK',
        name: 'Ngân hàng TMCP Việt Á',
        iconName: 'ic_repayment_vieta.png',
        thumbnail: 'ic_repayment_thumbnail_vieta.png',
      ),
      const RepaymentBank(
        shortName: 'HDBANK',
        name: 'Ngân hàng TMCP Phát Triển Thành Phố Hồ Chí Minh',
        iconName: 'ic_repayment_hd.png',
        thumbnail: 'ic_repayment_thumbnail_hd.png',
      ),
      const RepaymentBank(
        shortName: 'EXIMBANK',
        name: 'Ngân hàng TMCP Xuất nhập khẩu Việt Nam',
        iconName: 'ic_repayment_exim.png',
        thumbnail: 'ic_repayment_thumbnail_exim.png',
      ),
      const RepaymentBank(
        shortName: 'NCB',
        name: 'Ngân hàng TMCP Quốc Dân',
        iconName: 'ic_repayment_ncb.png',
        thumbnail: 'ic_repayment_thumbnail_ncb.png',
      ),
      const RepaymentBank(
        shortName: 'SHINHANBANK',
        name: 'Ngân hàng TNHH MTV Shinhan Việt Nam',
        iconName: 'ic_repayment_shinhan.png',
        thumbnail: 'ic_repayment_thumbnail_shinhan.png',
      ),
      const RepaymentBank(
        shortName: 'KIENLONGBANK',
        name: 'Ngân hàng TMCP Kiên Long',
        iconName: 'ic_repayment_kienlong.png',
        thumbnail: 'ic_repayment_thumbnail_kienlong.png',
      ),
      const RepaymentBank(
        shortName: 'WOORIBANK',
        name: 'TNHH MTV Woori Việt Nam',
        iconName: 'ic_repayment_woori.png',
        thumbnail: 'ic_repayment_thumbnail_woori.png',
      ),
      const RepaymentBank(
        shortName: 'PVCOMBANK',
        name: 'Ngân hàng TMCP Đại Chúng Việt Nam',
        iconName: 'ic_repayment_pv.png',
        thumbnail: 'ic_repayment_thumbnail_pv.png',
      ),
      const RepaymentBank(
        shortName: 'PUBLICBANK',
        name: 'Ngân hàng TNHH MTV Public Việt Nam',
        iconName: 'ic_repayment_public.png',
        thumbnail: 'ic_repayment_thumbnail_public.png',
      ),
      const RepaymentBank(
        shortName: 'LIENVIETPOSTBANK',
        name: 'Ngân hàng TMCP Bưu Điện Liên Việt',
        iconName: 'ic_repayment_lienviet.png',
        thumbnail: 'ic_repayment_thumbnail_lienviet.png',
      ),
      const RepaymentBank(
        shortName: 'BAOVIET',
        name: 'Ngân hàng TMCP Bảo Việt',
        iconName: 'ic_repayment_baoviet.png',
        thumbnail: 'ic_repayment_thumbnail_baoviet.png',
      ),
      const RepaymentBank(
        shortName: 'NAMABANK',
        name: 'Ngân hàng TMCP Nam Á',
        iconName: 'ic_repayment_nama.png',
        thumbnail: 'ic_repayment_thumbnail_nama.png',
      ),
      const RepaymentBank(
        shortName: 'VIETBANK',
        name: 'Ngân hàng TMCP Việt Nam Thương Tín',
        iconName: 'ic_repayment_viet.png',
        thumbnail: 'ic_repayment_thumbnail_viet.png',
      ),
      const RepaymentBank(
        shortName: 'OCEANBANK',
        name: 'Ngân hàng TMCP Đại Dương',
        iconName: 'ic_repayment_ocean.png',
        thumbnail: 'ic_repayment_thumbnail_ocean.png',
      ),
      const RepaymentBank(
        shortName: 'SAIGONBANK',
        name: 'Ngân hàng TMCP Sài Gòn Công Thương',
        iconName: 'ic_repayment_saigon.png',
        thumbnail: 'ic_repayment_thumbnail_saigon.png',
      ),
      const RepaymentBank(
        shortName: 'CAKE',
        name: 'Ngân hàng số CAKE by VPBank',
        iconName: 'ic_repayment_cake.png',
        thumbnail: 'ic_repayment_thumbnail_cake.png',
      ),
      const RepaymentBank(
        shortName: 'COOPBANK',
        name: 'Ngân hàng Co-op Bank',
        iconName: 'ic_repayment_coop.png',
        thumbnail: 'ic_repayment_thumbnail_coop.png',
      ),
      const RepaymentBank(
        shortName: 'CIMB',
        name: 'Ngân hàng TNHH MTV CIMB',
        iconName: 'ic_repayment_cimb.png',
        thumbnail: 'ic_repayment_thumbnail_cimb.png',
      ),
    ];
    return banks;
  }
}
