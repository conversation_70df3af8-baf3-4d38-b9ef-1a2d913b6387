// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_main_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentMainEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentMainEventCopyWith<$Res> {
  factory $RepaymentMainEventCopyWith(
          RepaymentMainEvent value, $Res Function(RepaymentMainEvent) then) =
      _$RepaymentMainEventCopyWithImpl<$Res, RepaymentMainEvent>;
}

/// @nodoc
class _$RepaymentMainEventCopyWithImpl<$Res, $Val extends RepaymentMainEvent>
    implements $RepaymentMainEventCopyWith<$Res> {
  _$RepaymentMainEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {RepaymentContract? selectedContract,
      bool? isFromPayNow,
      bool enableDirectDiscount,
      L10nCappRepayment repaymentLocalization});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedContract = freezed,
    Object? isFromPayNow = freezed,
    Object? enableDirectDiscount = null,
    Object? repaymentLocalization = null,
  }) {
    return _then(_$_Initialize(
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      isFromPayNow: freezed == isFromPayNow
          ? _value.isFromPayNow
          : isFromPayNow // ignore: cast_nullable_to_non_nullable
              as bool?,
      enableDirectDiscount: null == enableDirectDiscount
          ? _value.enableDirectDiscount
          : enableDirectDiscount // ignore: cast_nullable_to_non_nullable
              as bool,
      repaymentLocalization: null == repaymentLocalization
          ? _value.repaymentLocalization
          : repaymentLocalization // ignore: cast_nullable_to_non_nullable
              as L10nCappRepayment,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required this.selectedContract,
      this.isFromPayNow,
      required this.enableDirectDiscount,
      required this.repaymentLocalization});

  @override
  final RepaymentContract? selectedContract;
  @override
  final bool? isFromPayNow;
  @override
  final bool enableDirectDiscount;
  @override
  final L10nCappRepayment repaymentLocalization;

  @override
  String toString() {
    return 'RepaymentMainEvent.initialize(selectedContract: $selectedContract, isFromPayNow: $isFromPayNow, enableDirectDiscount: $enableDirectDiscount, repaymentLocalization: $repaymentLocalization)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.isFromPayNow, isFromPayNow) ||
                other.isFromPayNow == isFromPayNow) &&
            (identical(other.enableDirectDiscount, enableDirectDiscount) ||
                other.enableDirectDiscount == enableDirectDiscount) &&
            (identical(other.repaymentLocalization, repaymentLocalization) ||
                other.repaymentLocalization == repaymentLocalization));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedContract, isFromPayNow,
      enableDirectDiscount, repaymentLocalization);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return initialize(selectedContract, isFromPayNow, enableDirectDiscount,
        repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return initialize?.call(selectedContract, isFromPayNow,
        enableDirectDiscount, repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(selectedContract, isFromPayNow, enableDirectDiscount,
          repaymentLocalization);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentMainEvent {
  const factory _Initialize(
      {required final RepaymentContract? selectedContract,
      final bool? isFromPayNow,
      required final bool enableDirectDiscount,
      required final L10nCappRepayment repaymentLocalization}) = _$_Initialize;

  RepaymentContract? get selectedContract;
  bool? get isFromPayNow;
  bool get enableDirectDiscount;
  L10nCappRepayment get repaymentLocalization;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectAmountOptionCopyWith<$Res> {
  factory _$$_SelectAmountOptionCopyWith(_$_SelectAmountOption value,
          $Res Function(_$_SelectAmountOption) then) =
      __$$_SelectAmountOptionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentAmountOptionType selectAmountOptionType});
}

/// @nodoc
class __$$_SelectAmountOptionCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_SelectAmountOption>
    implements _$$_SelectAmountOptionCopyWith<$Res> {
  __$$_SelectAmountOptionCopyWithImpl(
      _$_SelectAmountOption _value, $Res Function(_$_SelectAmountOption) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectAmountOptionType = null,
  }) {
    return _then(_$_SelectAmountOption(
      selectAmountOptionType: null == selectAmountOptionType
          ? _value.selectAmountOptionType
          : selectAmountOptionType // ignore: cast_nullable_to_non_nullable
              as RepaymentAmountOptionType,
    ));
  }
}

/// @nodoc

class _$_SelectAmountOption implements _SelectAmountOption {
  const _$_SelectAmountOption({required this.selectAmountOptionType});

  @override
  final RepaymentAmountOptionType selectAmountOptionType;

  @override
  String toString() {
    return 'RepaymentMainEvent.selectAmountOption(selectAmountOptionType: $selectAmountOptionType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectAmountOption &&
            (identical(other.selectAmountOptionType, selectAmountOptionType) ||
                other.selectAmountOptionType == selectAmountOptionType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectAmountOptionType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectAmountOptionCopyWith<_$_SelectAmountOption> get copyWith =>
      __$$_SelectAmountOptionCopyWithImpl<_$_SelectAmountOption>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return selectAmountOption(selectAmountOptionType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return selectAmountOption?.call(selectAmountOptionType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (selectAmountOption != null) {
      return selectAmountOption(selectAmountOptionType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return selectAmountOption(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return selectAmountOption?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (selectAmountOption != null) {
      return selectAmountOption(this);
    }
    return orElse();
  }
}

abstract class _SelectAmountOption implements RepaymentMainEvent {
  const factory _SelectAmountOption(
          {required final RepaymentAmountOptionType selectAmountOptionType}) =
      _$_SelectAmountOption;

  RepaymentAmountOptionType get selectAmountOptionType;
  @JsonKey(ignore: true)
  _$$_SelectAmountOptionCopyWith<_$_SelectAmountOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetAmountCopyWith<$Res> {
  factory _$$_SetAmountCopyWith(
          _$_SetAmount value, $Res Function(_$_SetAmount) then) =
      __$$_SetAmountCopyWithImpl<$Res>;
  @useResult
  $Res call({Decimal? amount});
}

/// @nodoc
class __$$_SetAmountCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_SetAmount>
    implements _$$_SetAmountCopyWith<$Res> {
  __$$_SetAmountCopyWithImpl(
      _$_SetAmount _value, $Res Function(_$_SetAmount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
  }) {
    return _then(_$_SetAmount(
      freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
    ));
  }
}

/// @nodoc

class _$_SetAmount implements _SetAmount {
  const _$_SetAmount(this.amount);

  @override
  final Decimal? amount;

  @override
  String toString() {
    return 'RepaymentMainEvent.setAmount(amount: $amount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetAmount &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      __$$_SetAmountCopyWithImpl<_$_SetAmount>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return setAmount(amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return setAmount?.call(amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return setAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return setAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(this);
    }
    return orElse();
  }
}

abstract class _SetAmount implements RepaymentMainEvent {
  const factory _SetAmount(final Decimal? amount) = _$_SetAmount;

  Decimal? get amount;
  @JsonKey(ignore: true)
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FetchVirtualAccountCopyWith<$Res> {
  factory _$$_FetchVirtualAccountCopyWith(_$_FetchVirtualAccount value,
          $Res Function(_$_FetchVirtualAccount) then) =
      __$$_FetchVirtualAccountCopyWithImpl<$Res>;
  @useResult
  $Res call({String? voucherCode});
}

/// @nodoc
class __$$_FetchVirtualAccountCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_FetchVirtualAccount>
    implements _$$_FetchVirtualAccountCopyWith<$Res> {
  __$$_FetchVirtualAccountCopyWithImpl(_$_FetchVirtualAccount _value,
      $Res Function(_$_FetchVirtualAccount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voucherCode = freezed,
  }) {
    return _then(_$_FetchVirtualAccount(
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_FetchVirtualAccount implements _FetchVirtualAccount {
  const _$_FetchVirtualAccount({this.voucherCode});

  @override
  final String? voucherCode;

  @override
  String toString() {
    return 'RepaymentMainEvent.fetchVirtualAccount(voucherCode: $voucherCode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FetchVirtualAccount &&
            (identical(other.voucherCode, voucherCode) ||
                other.voucherCode == voucherCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, voucherCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FetchVirtualAccountCopyWith<_$_FetchVirtualAccount> get copyWith =>
      __$$_FetchVirtualAccountCopyWithImpl<_$_FetchVirtualAccount>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return fetchVirtualAccount(voucherCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return fetchVirtualAccount?.call(voucherCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchVirtualAccount != null) {
      return fetchVirtualAccount(voucherCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return fetchVirtualAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return fetchVirtualAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchVirtualAccount != null) {
      return fetchVirtualAccount(this);
    }
    return orElse();
  }
}

abstract class _FetchVirtualAccount implements RepaymentMainEvent {
  const factory _FetchVirtualAccount({final String? voucherCode}) =
      _$_FetchVirtualAccount;

  String? get voucherCode;
  @JsonKey(ignore: true)
  _$$_FetchVirtualAccountCopyWith<_$_FetchVirtualAccount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetVirtualAccountCopyWith<$Res> {
  factory _$$_ResetVirtualAccountCopyWith(_$_ResetVirtualAccount value,
          $Res Function(_$_ResetVirtualAccount) then) =
      __$$_ResetVirtualAccountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetVirtualAccountCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ResetVirtualAccount>
    implements _$$_ResetVirtualAccountCopyWith<$Res> {
  __$$_ResetVirtualAccountCopyWithImpl(_$_ResetVirtualAccount _value,
      $Res Function(_$_ResetVirtualAccount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetVirtualAccount implements _ResetVirtualAccount {
  const _$_ResetVirtualAccount();

  @override
  String toString() {
    return 'RepaymentMainEvent.resetVirtualAccount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetVirtualAccount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return resetVirtualAccount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return resetVirtualAccount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetVirtualAccount != null) {
      return resetVirtualAccount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return resetVirtualAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return resetVirtualAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetVirtualAccount != null) {
      return resetVirtualAccount(this);
    }
    return orElse();
  }
}

abstract class _ResetVirtualAccount implements RepaymentMainEvent {
  const factory _ResetVirtualAccount() = _$_ResetVirtualAccount;
}

/// @nodoc
abstract class _$$_UpdateContractCopyWith<$Res> {
  factory _$$_UpdateContractCopyWith(
          _$_UpdateContract value, $Res Function(_$_UpdateContract) then) =
      __$$_UpdateContractCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentContract? contract});
}

/// @nodoc
class __$$_UpdateContractCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_UpdateContract>
    implements _$$_UpdateContractCopyWith<$Res> {
  __$$_UpdateContractCopyWithImpl(
      _$_UpdateContract _value, $Res Function(_$_UpdateContract) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contract = freezed,
  }) {
    return _then(_$_UpdateContract(
      freezed == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
    ));
  }
}

/// @nodoc

class _$_UpdateContract implements _UpdateContract {
  const _$_UpdateContract(this.contract);

  @override
  final RepaymentContract? contract;

  @override
  String toString() {
    return 'RepaymentMainEvent.updateContract(contract: $contract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UpdateContract &&
            (identical(other.contract, contract) ||
                other.contract == contract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UpdateContractCopyWith<_$_UpdateContract> get copyWith =>
      __$$_UpdateContractCopyWithImpl<_$_UpdateContract>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return updateContract(contract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return updateContract?.call(contract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (updateContract != null) {
      return updateContract(contract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return updateContract(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return updateContract?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (updateContract != null) {
      return updateContract(this);
    }
    return orElse();
  }
}

abstract class _UpdateContract implements RepaymentMainEvent {
  const factory _UpdateContract(final RepaymentContract? contract) =
      _$_UpdateContract;

  RepaymentContract? get contract;
  @JsonKey(ignore: true)
  _$$_UpdateContractCopyWith<_$_UpdateContract> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FetchContractsCopyWith<$Res> {
  factory _$$_FetchContractsCopyWith(
          _$_FetchContracts value, $Res Function(_$_FetchContracts) then) =
      __$$_FetchContractsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_FetchContractsCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_FetchContracts>
    implements _$$_FetchContractsCopyWith<$Res> {
  __$$_FetchContractsCopyWithImpl(
      _$_FetchContracts _value, $Res Function(_$_FetchContracts) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_FetchContracts implements _FetchContracts {
  const _$_FetchContracts();

  @override
  String toString() {
    return 'RepaymentMainEvent.fetchContracts()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_FetchContracts);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return fetchContracts();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return fetchContracts?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchContracts != null) {
      return fetchContracts();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return fetchContracts(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return fetchContracts?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchContracts != null) {
      return fetchContracts(this);
    }
    return orElse();
  }
}

abstract class _FetchContracts implements RepaymentMainEvent {
  const factory _FetchContracts() = _$_FetchContracts;
}

/// @nodoc
abstract class _$$_ResetFetchContractsCopyWith<$Res> {
  factory _$$_ResetFetchContractsCopyWith(_$_ResetFetchContracts value,
          $Res Function(_$_ResetFetchContracts) then) =
      __$$_ResetFetchContractsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetFetchContractsCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ResetFetchContracts>
    implements _$$_ResetFetchContractsCopyWith<$Res> {
  __$$_ResetFetchContractsCopyWithImpl(_$_ResetFetchContracts _value,
      $Res Function(_$_ResetFetchContracts) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetFetchContracts implements _ResetFetchContracts {
  const _$_ResetFetchContracts();

  @override
  String toString() {
    return 'RepaymentMainEvent.resetFetchContracts()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetFetchContracts);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return resetFetchContracts();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return resetFetchContracts?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetFetchContracts != null) {
      return resetFetchContracts();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return resetFetchContracts(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return resetFetchContracts?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetFetchContracts != null) {
      return resetFetchContracts(this);
    }
    return orElse();
  }
}

abstract class _ResetFetchContracts implements RepaymentMainEvent {
  const factory _ResetFetchContracts() = _$_ResetFetchContracts;
}

/// @nodoc
abstract class _$$_FetchUserPaymentMethodsCopyWith<$Res> {
  factory _$$_FetchUserPaymentMethodsCopyWith(_$_FetchUserPaymentMethods value,
          $Res Function(_$_FetchUserPaymentMethods) then) =
      __$$_FetchUserPaymentMethodsCopyWithImpl<$Res>;
  @useResult
  $Res call({L10nCappRepayment repaymentLocalization});
}

/// @nodoc
class __$$_FetchUserPaymentMethodsCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_FetchUserPaymentMethods>
    implements _$$_FetchUserPaymentMethodsCopyWith<$Res> {
  __$$_FetchUserPaymentMethodsCopyWithImpl(_$_FetchUserPaymentMethods _value,
      $Res Function(_$_FetchUserPaymentMethods) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repaymentLocalization = null,
  }) {
    return _then(_$_FetchUserPaymentMethods(
      repaymentLocalization: null == repaymentLocalization
          ? _value.repaymentLocalization
          : repaymentLocalization // ignore: cast_nullable_to_non_nullable
              as L10nCappRepayment,
    ));
  }
}

/// @nodoc

class _$_FetchUserPaymentMethods implements _FetchUserPaymentMethods {
  const _$_FetchUserPaymentMethods({required this.repaymentLocalization});

  @override
  final L10nCappRepayment repaymentLocalization;

  @override
  String toString() {
    return 'RepaymentMainEvent.fetchUserPaymentMethods(repaymentLocalization: $repaymentLocalization)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FetchUserPaymentMethods &&
            (identical(other.repaymentLocalization, repaymentLocalization) ||
                other.repaymentLocalization == repaymentLocalization));
  }

  @override
  int get hashCode => Object.hash(runtimeType, repaymentLocalization);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FetchUserPaymentMethodsCopyWith<_$_FetchUserPaymentMethods>
      get copyWith =>
          __$$_FetchUserPaymentMethodsCopyWithImpl<_$_FetchUserPaymentMethods>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return fetchUserPaymentMethods(repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return fetchUserPaymentMethods?.call(repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchUserPaymentMethods != null) {
      return fetchUserPaymentMethods(repaymentLocalization);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return fetchUserPaymentMethods(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return fetchUserPaymentMethods?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchUserPaymentMethods != null) {
      return fetchUserPaymentMethods(this);
    }
    return orElse();
  }
}

abstract class _FetchUserPaymentMethods implements RepaymentMainEvent {
  const factory _FetchUserPaymentMethods(
          {required final L10nCappRepayment repaymentLocalization}) =
      _$_FetchUserPaymentMethods;

  L10nCappRepayment get repaymentLocalization;
  @JsonKey(ignore: true)
  _$$_FetchUserPaymentMethodsCopyWith<_$_FetchUserPaymentMethods>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetPaymentMethodCopyWith<$Res> {
  factory _$$_SetPaymentMethodCopyWith(
          _$_SetPaymentMethod value, $Res Function(_$_SetPaymentMethod) then) =
      __$$_SetPaymentMethodCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank});
}

/// @nodoc
class __$$_SetPaymentMethodCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_SetPaymentMethod>
    implements _$$_SetPaymentMethodCopyWith<$Res> {
  __$$_SetPaymentMethodCopyWithImpl(
      _$_SetPaymentMethod _value, $Res Function(_$_SetPaymentMethod) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentMethod = freezed,
    Object? bank = freezed,
  }) {
    return _then(_$_SetPaymentMethod(
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      bank: freezed == bank
          ? _value.bank
          : bank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
    ));
  }
}

/// @nodoc

class _$_SetPaymentMethod implements _SetPaymentMethod {
  const _$_SetPaymentMethod({this.paymentMethod, this.bank});

  @override
  final RepaymentUserPaymentMethod? paymentMethod;
  @override
  final RepaymentBank? bank;

  @override
  String toString() {
    return 'RepaymentMainEvent.setPaymentMethod(paymentMethod: $paymentMethod, bank: $bank)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetPaymentMethod &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.bank, bank) || other.bank == bank));
  }

  @override
  int get hashCode => Object.hash(runtimeType, paymentMethod, bank);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetPaymentMethodCopyWith<_$_SetPaymentMethod> get copyWith =>
      __$$_SetPaymentMethodCopyWithImpl<_$_SetPaymentMethod>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return setPaymentMethod(paymentMethod, bank);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return setPaymentMethod?.call(paymentMethod, bank);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (setPaymentMethod != null) {
      return setPaymentMethod(paymentMethod, bank);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return setPaymentMethod(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return setPaymentMethod?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (setPaymentMethod != null) {
      return setPaymentMethod(this);
    }
    return orElse();
  }
}

abstract class _SetPaymentMethod implements RepaymentMainEvent {
  const factory _SetPaymentMethod(
      {final RepaymentUserPaymentMethod? paymentMethod,
      final RepaymentBank? bank}) = _$_SetPaymentMethod;

  RepaymentUserPaymentMethod? get paymentMethod;
  RepaymentBank? get bank;
  @JsonKey(ignore: true)
  _$$_SetPaymentMethodCopyWith<_$_SetPaymentMethod> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SavePaymentMethodCopyWith<$Res> {
  factory _$$_SavePaymentMethodCopyWith(_$_SavePaymentMethod value,
          $Res Function(_$_SavePaymentMethod) then) =
      __$$_SavePaymentMethodCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank});
}

/// @nodoc
class __$$_SavePaymentMethodCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_SavePaymentMethod>
    implements _$$_SavePaymentMethodCopyWith<$Res> {
  __$$_SavePaymentMethodCopyWithImpl(
      _$_SavePaymentMethod _value, $Res Function(_$_SavePaymentMethod) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentMethod = freezed,
    Object? bank = freezed,
  }) {
    return _then(_$_SavePaymentMethod(
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      bank: freezed == bank
          ? _value.bank
          : bank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
    ));
  }
}

/// @nodoc

class _$_SavePaymentMethod implements _SavePaymentMethod {
  const _$_SavePaymentMethod({this.paymentMethod, this.bank});

  @override
  final RepaymentUserPaymentMethod? paymentMethod;
  @override
  final RepaymentBank? bank;

  @override
  String toString() {
    return 'RepaymentMainEvent.savePaymentMethod(paymentMethod: $paymentMethod, bank: $bank)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SavePaymentMethod &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.bank, bank) || other.bank == bank));
  }

  @override
  int get hashCode => Object.hash(runtimeType, paymentMethod, bank);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SavePaymentMethodCopyWith<_$_SavePaymentMethod> get copyWith =>
      __$$_SavePaymentMethodCopyWithImpl<_$_SavePaymentMethod>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return savePaymentMethod(paymentMethod, bank);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return savePaymentMethod?.call(paymentMethod, bank);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (savePaymentMethod != null) {
      return savePaymentMethod(paymentMethod, bank);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return savePaymentMethod(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return savePaymentMethod?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (savePaymentMethod != null) {
      return savePaymentMethod(this);
    }
    return orElse();
  }
}

abstract class _SavePaymentMethod implements RepaymentMainEvent {
  const factory _SavePaymentMethod(
      {final RepaymentUserPaymentMethod? paymentMethod,
      final RepaymentBank? bank}) = _$_SavePaymentMethod;

  RepaymentUserPaymentMethod? get paymentMethod;
  RepaymentBank? get bank;
  @JsonKey(ignore: true)
  _$$_SavePaymentMethodCopyWith<_$_SavePaymentMethod> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ProccessKoyalDataMessageCopyWith<$Res> {
  factory _$$_ProccessKoyalDataMessageCopyWith(
          _$_ProccessKoyalDataMessage value,
          $Res Function(_$_ProccessKoyalDataMessage) then) =
      __$$_ProccessKoyalDataMessageCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationMessage? notification});

  $NotificationMessageCopyWith<$Res>? get notification;
}

/// @nodoc
class __$$_ProccessKoyalDataMessageCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ProccessKoyalDataMessage>
    implements _$$_ProccessKoyalDataMessageCopyWith<$Res> {
  __$$_ProccessKoyalDataMessageCopyWithImpl(_$_ProccessKoyalDataMessage _value,
      $Res Function(_$_ProccessKoyalDataMessage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = freezed,
  }) {
    return _then(_$_ProccessKoyalDataMessage(
      freezed == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationMessage?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $NotificationMessageCopyWith<$Res>? get notification {
    if (_value.notification == null) {
      return null;
    }

    return $NotificationMessageCopyWith<$Res>(_value.notification!, (value) {
      return _then(_value.copyWith(notification: value));
    });
  }
}

/// @nodoc

class _$_ProccessKoyalDataMessage implements _ProccessKoyalDataMessage {
  const _$_ProccessKoyalDataMessage(this.notification);

  @override
  final NotificationMessage? notification;

  @override
  String toString() {
    return 'RepaymentMainEvent.proccessKoyalDataMessage(notification: $notification)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ProccessKoyalDataMessage &&
            (identical(other.notification, notification) ||
                other.notification == notification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notification);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ProccessKoyalDataMessageCopyWith<_$_ProccessKoyalDataMessage>
      get copyWith => __$$_ProccessKoyalDataMessageCopyWithImpl<
          _$_ProccessKoyalDataMessage>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return proccessKoyalDataMessage(notification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return proccessKoyalDataMessage?.call(notification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (proccessKoyalDataMessage != null) {
      return proccessKoyalDataMessage(notification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return proccessKoyalDataMessage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return proccessKoyalDataMessage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (proccessKoyalDataMessage != null) {
      return proccessKoyalDataMessage(this);
    }
    return orElse();
  }
}

abstract class _ProccessKoyalDataMessage implements RepaymentMainEvent {
  const factory _ProccessKoyalDataMessage(
      final NotificationMessage? notification) = _$_ProccessKoyalDataMessage;

  NotificationMessage? get notification;
  @JsonKey(ignore: true)
  _$$_ProccessKoyalDataMessageCopyWith<_$_ProccessKoyalDataMessage>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetBalanceChangeStateCopyWith<$Res> {
  factory _$$_ResetBalanceChangeStateCopyWith(_$_ResetBalanceChangeState value,
          $Res Function(_$_ResetBalanceChangeState) then) =
      __$$_ResetBalanceChangeStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetBalanceChangeStateCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ResetBalanceChangeState>
    implements _$$_ResetBalanceChangeStateCopyWith<$Res> {
  __$$_ResetBalanceChangeStateCopyWithImpl(_$_ResetBalanceChangeState _value,
      $Res Function(_$_ResetBalanceChangeState) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetBalanceChangeState implements _ResetBalanceChangeState {
  const _$_ResetBalanceChangeState();

  @override
  String toString() {
    return 'RepaymentMainEvent.resetBalanceChangeState()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ResetBalanceChangeState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return resetBalanceChangeState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return resetBalanceChangeState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetBalanceChangeState != null) {
      return resetBalanceChangeState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return resetBalanceChangeState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return resetBalanceChangeState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetBalanceChangeState != null) {
      return resetBalanceChangeState(this);
    }
    return orElse();
  }
}

abstract class _ResetBalanceChangeState implements RepaymentMainEvent {
  const factory _ResetBalanceChangeState() = _$_ResetBalanceChangeState;
}

/// @nodoc
abstract class _$$_ResetFocusCustomAmountCopyWith<$Res> {
  factory _$$_ResetFocusCustomAmountCopyWith(_$_ResetFocusCustomAmount value,
          $Res Function(_$_ResetFocusCustomAmount) then) =
      __$$_ResetFocusCustomAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetFocusCustomAmountCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ResetFocusCustomAmount>
    implements _$$_ResetFocusCustomAmountCopyWith<$Res> {
  __$$_ResetFocusCustomAmountCopyWithImpl(_$_ResetFocusCustomAmount _value,
      $Res Function(_$_ResetFocusCustomAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetFocusCustomAmount implements _ResetFocusCustomAmount {
  const _$_ResetFocusCustomAmount();

  @override
  String toString() {
    return 'RepaymentMainEvent.resetFocusCustomAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ResetFocusCustomAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return resetFocusCustomAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return resetFocusCustomAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetFocusCustomAmount != null) {
      return resetFocusCustomAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return resetFocusCustomAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return resetFocusCustomAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetFocusCustomAmount != null) {
      return resetFocusCustomAmount(this);
    }
    return orElse();
  }
}

abstract class _ResetFocusCustomAmount implements RepaymentMainEvent {
  const factory _ResetFocusCustomAmount() = _$_ResetFocusCustomAmount;
}

/// @nodoc
abstract class _$$_SelectVoucherCopyWith<$Res> {
  factory _$$_SelectVoucherCopyWith(
          _$_SelectVoucher value, $Res Function(_$_SelectVoucher) then) =
      __$$_SelectVoucherCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentVoucher voucher});
}

/// @nodoc
class __$$_SelectVoucherCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_SelectVoucher>
    implements _$$_SelectVoucherCopyWith<$Res> {
  __$$_SelectVoucherCopyWithImpl(
      _$_SelectVoucher _value, $Res Function(_$_SelectVoucher) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voucher = null,
  }) {
    return _then(_$_SelectVoucher(
      null == voucher
          ? _value.voucher
          : voucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher,
    ));
  }
}

/// @nodoc

class _$_SelectVoucher implements _SelectVoucher {
  const _$_SelectVoucher(this.voucher);

  @override
  final RepaymentVoucher voucher;

  @override
  String toString() {
    return 'RepaymentMainEvent.selectVoucher(voucher: $voucher)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectVoucher &&
            (identical(other.voucher, voucher) || other.voucher == voucher));
  }

  @override
  int get hashCode => Object.hash(runtimeType, voucher);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectVoucherCopyWith<_$_SelectVoucher> get copyWith =>
      __$$_SelectVoucherCopyWithImpl<_$_SelectVoucher>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return selectVoucher(voucher);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return selectVoucher?.call(voucher);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (selectVoucher != null) {
      return selectVoucher(voucher);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return selectVoucher(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return selectVoucher?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (selectVoucher != null) {
      return selectVoucher(this);
    }
    return orElse();
  }
}

abstract class _SelectVoucher implements RepaymentMainEvent {
  const factory _SelectVoucher(final RepaymentVoucher voucher) =
      _$_SelectVoucher;

  RepaymentVoucher get voucher;
  @JsonKey(ignore: true)
  _$$_SelectVoucherCopyWith<_$_SelectVoucher> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_RemoveVoucherCopyWith<$Res> {
  factory _$$_RemoveVoucherCopyWith(
          _$_RemoveVoucher value, $Res Function(_$_RemoveVoucher) then) =
      __$$_RemoveVoucherCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_RemoveVoucherCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_RemoveVoucher>
    implements _$$_RemoveVoucherCopyWith<$Res> {
  __$$_RemoveVoucherCopyWithImpl(
      _$_RemoveVoucher _value, $Res Function(_$_RemoveVoucher) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_RemoveVoucher implements _RemoveVoucher {
  const _$_RemoveVoucher();

  @override
  String toString() {
    return 'RepaymentMainEvent.removeVoucher()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_RemoveVoucher);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return removeVoucher();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return removeVoucher?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (removeVoucher != null) {
      return removeVoucher();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return removeVoucher(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return removeVoucher?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (removeVoucher != null) {
      return removeVoucher(this);
    }
    return orElse();
  }
}

abstract class _RemoveVoucher implements RepaymentMainEvent {
  const factory _RemoveVoucher() = _$_RemoveVoucher;
}

/// @nodoc
abstract class _$$_CheckVoucherApplicableCopyWith<$Res> {
  factory _$$_CheckVoucherApplicableCopyWith(_$_CheckVoucherApplicable value,
          $Res Function(_$_CheckVoucherApplicable) then) =
      __$$_CheckVoucherApplicableCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentVoucher voucher});
}

/// @nodoc
class __$$_CheckVoucherApplicableCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_CheckVoucherApplicable>
    implements _$$_CheckVoucherApplicableCopyWith<$Res> {
  __$$_CheckVoucherApplicableCopyWithImpl(_$_CheckVoucherApplicable _value,
      $Res Function(_$_CheckVoucherApplicable) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voucher = null,
  }) {
    return _then(_$_CheckVoucherApplicable(
      null == voucher
          ? _value.voucher
          : voucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher,
    ));
  }
}

/// @nodoc

class _$_CheckVoucherApplicable implements _CheckVoucherApplicable {
  const _$_CheckVoucherApplicable(this.voucher);

  @override
  final RepaymentVoucher voucher;

  @override
  String toString() {
    return 'RepaymentMainEvent.checkVoucherApplicable(voucher: $voucher)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckVoucherApplicable &&
            (identical(other.voucher, voucher) || other.voucher == voucher));
  }

  @override
  int get hashCode => Object.hash(runtimeType, voucher);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CheckVoucherApplicableCopyWith<_$_CheckVoucherApplicable> get copyWith =>
      __$$_CheckVoucherApplicableCopyWithImpl<_$_CheckVoucherApplicable>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return checkVoucherApplicable(voucher);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return checkVoucherApplicable?.call(voucher);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (checkVoucherApplicable != null) {
      return checkVoucherApplicable(voucher);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return checkVoucherApplicable(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return checkVoucherApplicable?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (checkVoucherApplicable != null) {
      return checkVoucherApplicable(this);
    }
    return orElse();
  }
}

abstract class _CheckVoucherApplicable implements RepaymentMainEvent {
  const factory _CheckVoucherApplicable(final RepaymentVoucher voucher) =
      _$_CheckVoucherApplicable;

  RepaymentVoucher get voucher;
  @JsonKey(ignore: true)
  _$$_CheckVoucherApplicableCopyWith<_$_CheckVoucherApplicable> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ValidatePromotionVoucherCopyWith<$Res> {
  factory _$$_ValidatePromotionVoucherCopyWith(
          _$_ValidatePromotionVoucher value,
          $Res Function(_$_ValidatePromotionVoucher) then) =
      __$$_ValidatePromotionVoucherCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentVoucherValidationRequest request});
}

/// @nodoc
class __$$_ValidatePromotionVoucherCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ValidatePromotionVoucher>
    implements _$$_ValidatePromotionVoucherCopyWith<$Res> {
  __$$_ValidatePromotionVoucherCopyWithImpl(_$_ValidatePromotionVoucher _value,
      $Res Function(_$_ValidatePromotionVoucher) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_ValidatePromotionVoucher(
      null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherValidationRequest,
    ));
  }
}

/// @nodoc

class _$_ValidatePromotionVoucher implements _ValidatePromotionVoucher {
  const _$_ValidatePromotionVoucher(this.request);

  @override
  final RepaymentVoucherValidationRequest request;

  @override
  String toString() {
    return 'RepaymentMainEvent.validatePromotionVoucher(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ValidatePromotionVoucher &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ValidatePromotionVoucherCopyWith<_$_ValidatePromotionVoucher>
      get copyWith => __$$_ValidatePromotionVoucherCopyWithImpl<
          _$_ValidatePromotionVoucher>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return validatePromotionVoucher(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return validatePromotionVoucher?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (validatePromotionVoucher != null) {
      return validatePromotionVoucher(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return validatePromotionVoucher(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return validatePromotionVoucher?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (validatePromotionVoucher != null) {
      return validatePromotionVoucher(this);
    }
    return orElse();
  }
}

abstract class _ValidatePromotionVoucher implements RepaymentMainEvent {
  const factory _ValidatePromotionVoucher(
          final RepaymentVoucherValidationRequest request) =
      _$_ValidatePromotionVoucher;

  RepaymentVoucherValidationRequest get request;
  @JsonKey(ignore: true)
  _$$_ValidatePromotionVoucherCopyWith<_$_ValidatePromotionVoucher>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ApplyVoucherCalculationCopyWith<$Res> {
  factory _$$_ApplyVoucherCalculationCopyWith(_$_ApplyVoucherCalculation value,
          $Res Function(_$_ApplyVoucherCalculation) then) =
      __$$_ApplyVoucherCalculationCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentVoucher voucher});
}

/// @nodoc
class __$$_ApplyVoucherCalculationCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ApplyVoucherCalculation>
    implements _$$_ApplyVoucherCalculationCopyWith<$Res> {
  __$$_ApplyVoucherCalculationCopyWithImpl(_$_ApplyVoucherCalculation _value,
      $Res Function(_$_ApplyVoucherCalculation) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? voucher = null,
  }) {
    return _then(_$_ApplyVoucherCalculation(
      null == voucher
          ? _value.voucher
          : voucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher,
    ));
  }
}

/// @nodoc

class _$_ApplyVoucherCalculation implements _ApplyVoucherCalculation {
  const _$_ApplyVoucherCalculation(this.voucher);

  @override
  final RepaymentVoucher voucher;

  @override
  String toString() {
    return 'RepaymentMainEvent.applyVoucherCalculation(voucher: $voucher)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ApplyVoucherCalculation &&
            (identical(other.voucher, voucher) || other.voucher == voucher));
  }

  @override
  int get hashCode => Object.hash(runtimeType, voucher);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ApplyVoucherCalculationCopyWith<_$_ApplyVoucherCalculation>
      get copyWith =>
          __$$_ApplyVoucherCalculationCopyWithImpl<_$_ApplyVoucherCalculation>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return applyVoucherCalculation(voucher);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return applyVoucherCalculation?.call(voucher);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (applyVoucherCalculation != null) {
      return applyVoucherCalculation(voucher);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return applyVoucherCalculation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return applyVoucherCalculation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (applyVoucherCalculation != null) {
      return applyVoucherCalculation(this);
    }
    return orElse();
  }
}

abstract class _ApplyVoucherCalculation implements RepaymentMainEvent {
  const factory _ApplyVoucherCalculation(final RepaymentVoucher voucher) =
      _$_ApplyVoucherCalculation;

  RepaymentVoucher get voucher;
  @JsonKey(ignore: true)
  _$$_ApplyVoucherCalculationCopyWith<_$_ApplyVoucherCalculation>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetStateCopyWith<$Res> {
  factory _$$_ResetStateCopyWith(
          _$_ResetState value, $Res Function(_$_ResetState) then) =
      __$$_ResetStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetStateCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_ResetState>
    implements _$$_ResetStateCopyWith<$Res> {
  __$$_ResetStateCopyWithImpl(
      _$_ResetState _value, $Res Function(_$_ResetState) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetState implements _ResetState {
  const _$_ResetState();

  @override
  String toString() {
    return 'RepaymentMainEvent.resetState()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetState implements RepaymentMainEvent {
  const factory _ResetState() = _$_ResetState;
}

/// @nodoc
abstract class _$$_InitRepaymentBannerCopyWith<$Res> {
  factory _$$_InitRepaymentBannerCopyWith(_$_InitRepaymentBanner value,
          $Res Function(_$_InitRepaymentBanner) then) =
      __$$_InitRepaymentBannerCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitRepaymentBannerCopyWithImpl<$Res>
    extends _$RepaymentMainEventCopyWithImpl<$Res, _$_InitRepaymentBanner>
    implements _$$_InitRepaymentBannerCopyWith<$Res> {
  __$$_InitRepaymentBannerCopyWithImpl(_$_InitRepaymentBanner _value,
      $Res Function(_$_InitRepaymentBanner) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_InitRepaymentBanner implements _InitRepaymentBanner {
  const _$_InitRepaymentBanner();

  @override
  String toString() {
    return 'RepaymentMainEvent.initRepaymentBanner()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_InitRepaymentBanner);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            bool? isFromPayNow,
            bool enableDirectDiscount,
            L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(RepaymentAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String? voucherCode) fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
    required TResult Function(RepaymentContract? contract) updateContract,
    required TResult Function() fetchContracts,
    required TResult Function() resetFetchContracts,
    required TResult Function(L10nCappRepayment repaymentLocalization)
        fetchUserPaymentMethods,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        setPaymentMethod,
    required TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)
        savePaymentMethod,
    required TResult Function(NotificationMessage? notification)
        proccessKoyalDataMessage,
    required TResult Function() resetBalanceChangeState,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(RepaymentVoucher voucher) selectVoucher,
    required TResult Function() removeVoucher,
    required TResult Function(RepaymentVoucher voucher) checkVoucherApplicable,
    required TResult Function(RepaymentVoucherValidationRequest request)
        validatePromotionVoucher,
    required TResult Function(RepaymentVoucher voucher) applyVoucherCalculation,
    required TResult Function() resetState,
    required TResult Function() initRepaymentBanner,
  }) {
    return initRepaymentBanner();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String? voucherCode)? fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
    TResult? Function(RepaymentContract? contract)? updateContract,
    TResult? Function()? fetchContracts,
    TResult? Function()? resetFetchContracts,
    TResult? Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult? Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult? Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult? Function()? resetBalanceChangeState,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(RepaymentVoucher voucher)? selectVoucher,
    TResult? Function()? removeVoucher,
    TResult? Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult? Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult? Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult? Function()? resetState,
    TResult? Function()? initRepaymentBanner,
  }) {
    return initRepaymentBanner?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract? selectedContract, bool? isFromPayNow,
            bool enableDirectDiscount, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(RepaymentAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String? voucherCode)? fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    TResult Function(RepaymentContract? contract)? updateContract,
    TResult Function()? fetchContracts,
    TResult Function()? resetFetchContracts,
    TResult Function(L10nCappRepayment repaymentLocalization)?
        fetchUserPaymentMethods,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        setPaymentMethod,
    TResult Function(
            RepaymentUserPaymentMethod? paymentMethod, RepaymentBank? bank)?
        savePaymentMethod,
    TResult Function(NotificationMessage? notification)?
        proccessKoyalDataMessage,
    TResult Function()? resetBalanceChangeState,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(RepaymentVoucher voucher)? selectVoucher,
    TResult Function()? removeVoucher,
    TResult Function(RepaymentVoucher voucher)? checkVoucherApplicable,
    TResult Function(RepaymentVoucherValidationRequest request)?
        validatePromotionVoucher,
    TResult Function(RepaymentVoucher voucher)? applyVoucherCalculation,
    TResult Function()? resetState,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initRepaymentBanner != null) {
      return initRepaymentBanner();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
    required TResult Function(_UpdateContract value) updateContract,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_ResetFetchContracts value) resetFetchContracts,
    required TResult Function(_FetchUserPaymentMethods value)
        fetchUserPaymentMethods,
    required TResult Function(_SetPaymentMethod value) setPaymentMethod,
    required TResult Function(_SavePaymentMethod value) savePaymentMethod,
    required TResult Function(_ProccessKoyalDataMessage value)
        proccessKoyalDataMessage,
    required TResult Function(_ResetBalanceChangeState value)
        resetBalanceChangeState,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_RemoveVoucher value) removeVoucher,
    required TResult Function(_CheckVoucherApplicable value)
        checkVoucherApplicable,
    required TResult Function(_ValidatePromotionVoucher value)
        validatePromotionVoucher,
    required TResult Function(_ApplyVoucherCalculation value)
        applyVoucherCalculation,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return initRepaymentBanner(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult? Function(_UpdateContract value)? updateContract,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult? Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult? Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult? Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult? Function(_ProccessKoyalDataMessage value)?
        proccessKoyalDataMessage,
    TResult? Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_RemoveVoucher value)? removeVoucher,
    TResult? Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult? Function(_ValidatePromotionVoucher value)?
        validatePromotionVoucher,
    TResult? Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return initRepaymentBanner?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    TResult Function(_UpdateContract value)? updateContract,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_ResetFetchContracts value)? resetFetchContracts,
    TResult Function(_FetchUserPaymentMethods value)? fetchUserPaymentMethods,
    TResult Function(_SetPaymentMethod value)? setPaymentMethod,
    TResult Function(_SavePaymentMethod value)? savePaymentMethod,
    TResult Function(_ProccessKoyalDataMessage value)? proccessKoyalDataMessage,
    TResult Function(_ResetBalanceChangeState value)? resetBalanceChangeState,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_RemoveVoucher value)? removeVoucher,
    TResult Function(_CheckVoucherApplicable value)? checkVoucherApplicable,
    TResult Function(_ValidatePromotionVoucher value)? validatePromotionVoucher,
    TResult Function(_ApplyVoucherCalculation value)? applyVoucherCalculation,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initRepaymentBanner != null) {
      return initRepaymentBanner(this);
    }
    return orElse();
  }
}

abstract class _InitRepaymentBanner implements RepaymentMainEvent {
  const factory _InitRepaymentBanner() = _$_InitRepaymentBanner;
}

/// @nodoc
mixin _$RepaymentMainState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  LoadingState get fetchPaymentMethodsloadingState =>
      throw _privateConstructorUsedError;
  LoadingState get virtualAccountLoadingState =>
      throw _privateConstructorUsedError;
  LoadingState get validateVoucherLoadingState =>
      throw _privateConstructorUsedError;
  bool get shouldFocusCustomAmount => throw _privateConstructorUsedError;
  Decimal get minimumAmount => throw _privateConstructorUsedError;
  Decimal? get selectedAmount => throw _privateConstructorUsedError;
  RepaymentContract? get selectedContract => throw _privateConstructorUsedError;
  RepaymentUserPaymentMethod? get selectedPaymentMethod =>
      throw _privateConstructorUsedError;
  RepaymentBank? get selectedBank => throw _privateConstructorUsedError;
  bool get isFromPayNow => throw _privateConstructorUsedError;
  RepaymentAmountOptionType? get selectedAmountOption =>
      throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      get failureOrSuccessVirtualAccount => throw _privateConstructorUsedError;
  bool get canProcessPayment => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  bool? get isVirtualAccountError => throw _privateConstructorUsedError;
  bool? get isUpdatingBalanceChange => throw _privateConstructorUsedError;
  RepaymentContractVirtualAccount? get contractVirtualAccount =>
      throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccessFetchContracts => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>
      get failureOrSuccessFetchUserPaymentMethods =>
          throw _privateConstructorUsedError;
  bool? get isShowNoLoan => throw _privateConstructorUsedError;
  List<RepaymentContract>? get contracts => throw _privateConstructorUsedError;
  List<RepaymentUserPaymentMethod>? get paymentMethods =>
      throw _privateConstructorUsedError;
  bool get enableDirectDiscount =>
      throw _privateConstructorUsedError; // To detect the voucher is applicable using FE logic
  bool get isVoucherApplicable => throw _privateConstructorUsedError;
  RepaymentVoucher? get selectedVoucher => throw _privateConstructorUsedError;
  Decimal? get afterDiscountAmount => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentVoucherValidation>>
      get failureOrSuccessValidatePromotionVoucher =>
          throw _privateConstructorUsedError;
  RepaymentVoucherValidation? get voucherValidation =>
      throw _privateConstructorUsedError;
  String? get bannerId => throw _privateConstructorUsedError;
  bool? get isCalculatingPromotionVoucher => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentMainStateCopyWith<RepaymentMainState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentMainStateCopyWith<$Res> {
  factory $RepaymentMainStateCopyWith(
          RepaymentMainState value, $Res Function(RepaymentMainState) then) =
      _$RepaymentMainStateCopyWithImpl<$Res, RepaymentMainState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      LoadingState fetchPaymentMethodsloadingState,
      LoadingState virtualAccountLoadingState,
      LoadingState validateVoucherLoadingState,
      bool shouldFocusCustomAmount,
      Decimal minimumAmount,
      Decimal? selectedAmount,
      RepaymentContract? selectedContract,
      RepaymentUserPaymentMethod? selectedPaymentMethod,
      RepaymentBank? selectedBank,
      bool isFromPayNow,
      RepaymentAmountOptionType? selectedAmountOption,
      Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount,
      bool canProcessPayment,
      bool? isError,
      bool? isVirtualAccountError,
      bool? isUpdatingBalanceChange,
      RepaymentContractVirtualAccount? contractVirtualAccount,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessFetchContracts,
      Option<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>
          failureOrSuccessFetchUserPaymentMethods,
      bool? isShowNoLoan,
      List<RepaymentContract>? contracts,
      List<RepaymentUserPaymentMethod>? paymentMethods,
      bool enableDirectDiscount,
      bool isVoucherApplicable,
      RepaymentVoucher? selectedVoucher,
      Decimal? afterDiscountAmount,
      Option<Either<RepaymentFailure, RepaymentVoucherValidation>>
          failureOrSuccessValidatePromotionVoucher,
      RepaymentVoucherValidation? voucherValidation,
      String? bannerId,
      bool? isCalculatingPromotionVoucher});
}

/// @nodoc
class _$RepaymentMainStateCopyWithImpl<$Res, $Val extends RepaymentMainState>
    implements $RepaymentMainStateCopyWith<$Res> {
  _$RepaymentMainStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? fetchPaymentMethodsloadingState = null,
    Object? virtualAccountLoadingState = null,
    Object? validateVoucherLoadingState = null,
    Object? shouldFocusCustomAmount = null,
    Object? minimumAmount = null,
    Object? selectedAmount = freezed,
    Object? selectedContract = freezed,
    Object? selectedPaymentMethod = freezed,
    Object? selectedBank = freezed,
    Object? isFromPayNow = null,
    Object? selectedAmountOption = freezed,
    Object? failureOrSuccessVirtualAccount = null,
    Object? canProcessPayment = null,
    Object? isError = freezed,
    Object? isVirtualAccountError = freezed,
    Object? isUpdatingBalanceChange = freezed,
    Object? contractVirtualAccount = freezed,
    Object? failureOrSuccessFetchContracts = null,
    Object? failureOrSuccessFetchUserPaymentMethods = null,
    Object? isShowNoLoan = freezed,
    Object? contracts = freezed,
    Object? paymentMethods = freezed,
    Object? enableDirectDiscount = null,
    Object? isVoucherApplicable = null,
    Object? selectedVoucher = freezed,
    Object? afterDiscountAmount = freezed,
    Object? failureOrSuccessValidatePromotionVoucher = null,
    Object? voucherValidation = freezed,
    Object? bannerId = freezed,
    Object? isCalculatingPromotionVoucher = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      fetchPaymentMethodsloadingState: null == fetchPaymentMethodsloadingState
          ? _value.fetchPaymentMethodsloadingState
          : fetchPaymentMethodsloadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      virtualAccountLoadingState: null == virtualAccountLoadingState
          ? _value.virtualAccountLoadingState
          : virtualAccountLoadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      validateVoucherLoadingState: null == validateVoucherLoadingState
          ? _value.validateVoucherLoadingState
          : validateVoucherLoadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      shouldFocusCustomAmount: null == shouldFocusCustomAmount
          ? _value.shouldFocusCustomAmount
          : shouldFocusCustomAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      minimumAmount: null == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      selectedPaymentMethod: freezed == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      isFromPayNow: null == isFromPayNow
          ? _value.isFromPayNow
          : isFromPayNow // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedAmountOption: freezed == selectedAmountOption
          ? _value.selectedAmountOption
          : selectedAmountOption // ignore: cast_nullable_to_non_nullable
              as RepaymentAmountOptionType?,
      failureOrSuccessVirtualAccount: null == failureOrSuccessVirtualAccount
          ? _value.failureOrSuccessVirtualAccount
          : failureOrSuccessVirtualAccount // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentContractVirtualAccount>>,
      canProcessPayment: null == canProcessPayment
          ? _value.canProcessPayment
          : canProcessPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isVirtualAccountError: freezed == isVirtualAccountError
          ? _value.isVirtualAccountError
          : isVirtualAccountError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isUpdatingBalanceChange: freezed == isUpdatingBalanceChange
          ? _value.isUpdatingBalanceChange
          : isUpdatingBalanceChange // ignore: cast_nullable_to_non_nullable
              as bool?,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      failureOrSuccessFetchContracts: null == failureOrSuccessFetchContracts
          ? _value.failureOrSuccessFetchContracts
          : failureOrSuccessFetchContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      failureOrSuccessFetchUserPaymentMethods: null ==
              failureOrSuccessFetchUserPaymentMethods
          ? _value.failureOrSuccessFetchUserPaymentMethods
          : failureOrSuccessFetchUserPaymentMethods // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>,
      isShowNoLoan: freezed == isShowNoLoan
          ? _value.isShowNoLoan
          : isShowNoLoan // ignore: cast_nullable_to_non_nullable
              as bool?,
      contracts: freezed == contracts
          ? _value.contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>?,
      paymentMethods: freezed == paymentMethods
          ? _value.paymentMethods
          : paymentMethods // ignore: cast_nullable_to_non_nullable
              as List<RepaymentUserPaymentMethod>?,
      enableDirectDiscount: null == enableDirectDiscount
          ? _value.enableDirectDiscount
          : enableDirectDiscount // ignore: cast_nullable_to_non_nullable
              as bool,
      isVoucherApplicable: null == isVoucherApplicable
          ? _value.isVoucherApplicable
          : isVoucherApplicable // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      afterDiscountAmount: freezed == afterDiscountAmount
          ? _value.afterDiscountAmount
          : afterDiscountAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      failureOrSuccessValidatePromotionVoucher: null ==
              failureOrSuccessValidatePromotionVoucher
          ? _value.failureOrSuccessValidatePromotionVoucher
          : failureOrSuccessValidatePromotionVoucher // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentVoucherValidation>>,
      voucherValidation: freezed == voucherValidation
          ? _value.voucherValidation
          : voucherValidation // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherValidation?,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      isCalculatingPromotionVoucher: freezed == isCalculatingPromotionVoucher
          ? _value.isCalculatingPromotionVoucher
          : isCalculatingPromotionVoucher // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentMainStateCopyWith<$Res>
    implements $RepaymentMainStateCopyWith<$Res> {
  factory _$$_RepaymentMainStateCopyWith(_$_RepaymentMainState value,
          $Res Function(_$_RepaymentMainState) then) =
      __$$_RepaymentMainStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      LoadingState fetchPaymentMethodsloadingState,
      LoadingState virtualAccountLoadingState,
      LoadingState validateVoucherLoadingState,
      bool shouldFocusCustomAmount,
      Decimal minimumAmount,
      Decimal? selectedAmount,
      RepaymentContract? selectedContract,
      RepaymentUserPaymentMethod? selectedPaymentMethod,
      RepaymentBank? selectedBank,
      bool isFromPayNow,
      RepaymentAmountOptionType? selectedAmountOption,
      Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount,
      bool canProcessPayment,
      bool? isError,
      bool? isVirtualAccountError,
      bool? isUpdatingBalanceChange,
      RepaymentContractVirtualAccount? contractVirtualAccount,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessFetchContracts,
      Option<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>
          failureOrSuccessFetchUserPaymentMethods,
      bool? isShowNoLoan,
      List<RepaymentContract>? contracts,
      List<RepaymentUserPaymentMethod>? paymentMethods,
      bool enableDirectDiscount,
      bool isVoucherApplicable,
      RepaymentVoucher? selectedVoucher,
      Decimal? afterDiscountAmount,
      Option<Either<RepaymentFailure, RepaymentVoucherValidation>>
          failureOrSuccessValidatePromotionVoucher,
      RepaymentVoucherValidation? voucherValidation,
      String? bannerId,
      bool? isCalculatingPromotionVoucher});
}

/// @nodoc
class __$$_RepaymentMainStateCopyWithImpl<$Res>
    extends _$RepaymentMainStateCopyWithImpl<$Res, _$_RepaymentMainState>
    implements _$$_RepaymentMainStateCopyWith<$Res> {
  __$$_RepaymentMainStateCopyWithImpl(
      _$_RepaymentMainState _value, $Res Function(_$_RepaymentMainState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? fetchPaymentMethodsloadingState = null,
    Object? virtualAccountLoadingState = null,
    Object? validateVoucherLoadingState = null,
    Object? shouldFocusCustomAmount = null,
    Object? minimumAmount = null,
    Object? selectedAmount = freezed,
    Object? selectedContract = freezed,
    Object? selectedPaymentMethod = freezed,
    Object? selectedBank = freezed,
    Object? isFromPayNow = null,
    Object? selectedAmountOption = freezed,
    Object? failureOrSuccessVirtualAccount = null,
    Object? canProcessPayment = null,
    Object? isError = freezed,
    Object? isVirtualAccountError = freezed,
    Object? isUpdatingBalanceChange = freezed,
    Object? contractVirtualAccount = freezed,
    Object? failureOrSuccessFetchContracts = null,
    Object? failureOrSuccessFetchUserPaymentMethods = null,
    Object? isShowNoLoan = freezed,
    Object? contracts = freezed,
    Object? paymentMethods = freezed,
    Object? enableDirectDiscount = null,
    Object? isVoucherApplicable = null,
    Object? selectedVoucher = freezed,
    Object? afterDiscountAmount = freezed,
    Object? failureOrSuccessValidatePromotionVoucher = null,
    Object? voucherValidation = freezed,
    Object? bannerId = freezed,
    Object? isCalculatingPromotionVoucher = freezed,
  }) {
    return _then(_$_RepaymentMainState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      fetchPaymentMethodsloadingState: null == fetchPaymentMethodsloadingState
          ? _value.fetchPaymentMethodsloadingState
          : fetchPaymentMethodsloadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      virtualAccountLoadingState: null == virtualAccountLoadingState
          ? _value.virtualAccountLoadingState
          : virtualAccountLoadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      validateVoucherLoadingState: null == validateVoucherLoadingState
          ? _value.validateVoucherLoadingState
          : validateVoucherLoadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      shouldFocusCustomAmount: null == shouldFocusCustomAmount
          ? _value.shouldFocusCustomAmount
          : shouldFocusCustomAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      minimumAmount: null == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      selectedPaymentMethod: freezed == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      isFromPayNow: null == isFromPayNow
          ? _value.isFromPayNow
          : isFromPayNow // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedAmountOption: freezed == selectedAmountOption
          ? _value.selectedAmountOption
          : selectedAmountOption // ignore: cast_nullable_to_non_nullable
              as RepaymentAmountOptionType?,
      failureOrSuccessVirtualAccount: null == failureOrSuccessVirtualAccount
          ? _value.failureOrSuccessVirtualAccount
          : failureOrSuccessVirtualAccount // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentContractVirtualAccount>>,
      canProcessPayment: null == canProcessPayment
          ? _value.canProcessPayment
          : canProcessPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isVirtualAccountError: freezed == isVirtualAccountError
          ? _value.isVirtualAccountError
          : isVirtualAccountError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isUpdatingBalanceChange: freezed == isUpdatingBalanceChange
          ? _value.isUpdatingBalanceChange
          : isUpdatingBalanceChange // ignore: cast_nullable_to_non_nullable
              as bool?,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      failureOrSuccessFetchContracts: null == failureOrSuccessFetchContracts
          ? _value.failureOrSuccessFetchContracts
          : failureOrSuccessFetchContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      failureOrSuccessFetchUserPaymentMethods: null ==
              failureOrSuccessFetchUserPaymentMethods
          ? _value.failureOrSuccessFetchUserPaymentMethods
          : failureOrSuccessFetchUserPaymentMethods // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>,
      isShowNoLoan: freezed == isShowNoLoan
          ? _value.isShowNoLoan
          : isShowNoLoan // ignore: cast_nullable_to_non_nullable
              as bool?,
      contracts: freezed == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>?,
      paymentMethods: freezed == paymentMethods
          ? _value._paymentMethods
          : paymentMethods // ignore: cast_nullable_to_non_nullable
              as List<RepaymentUserPaymentMethod>?,
      enableDirectDiscount: null == enableDirectDiscount
          ? _value.enableDirectDiscount
          : enableDirectDiscount // ignore: cast_nullable_to_non_nullable
              as bool,
      isVoucherApplicable: null == isVoucherApplicable
          ? _value.isVoucherApplicable
          : isVoucherApplicable // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      afterDiscountAmount: freezed == afterDiscountAmount
          ? _value.afterDiscountAmount
          : afterDiscountAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      failureOrSuccessValidatePromotionVoucher: null ==
              failureOrSuccessValidatePromotionVoucher
          ? _value.failureOrSuccessValidatePromotionVoucher
          : failureOrSuccessValidatePromotionVoucher // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentVoucherValidation>>,
      voucherValidation: freezed == voucherValidation
          ? _value.voucherValidation
          : voucherValidation // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucherValidation?,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      isCalculatingPromotionVoucher: freezed == isCalculatingPromotionVoucher
          ? _value.isCalculatingPromotionVoucher
          : isCalculatingPromotionVoucher // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_RepaymentMainState implements _RepaymentMainState {
  const _$_RepaymentMainState(
      {required this.loadingState,
      required this.fetchPaymentMethodsloadingState,
      required this.virtualAccountLoadingState,
      required this.validateVoucherLoadingState,
      required this.shouldFocusCustomAmount,
      required this.minimumAmount,
      required this.selectedAmount,
      required this.selectedContract,
      this.selectedPaymentMethod,
      this.selectedBank,
      required this.isFromPayNow,
      required this.selectedAmountOption,
      required this.failureOrSuccessVirtualAccount,
      required this.canProcessPayment,
      this.isError,
      this.isVirtualAccountError,
      this.isUpdatingBalanceChange,
      this.contractVirtualAccount,
      required this.failureOrSuccessFetchContracts,
      required this.failureOrSuccessFetchUserPaymentMethods,
      this.isShowNoLoan,
      final List<RepaymentContract>? contracts,
      final List<RepaymentUserPaymentMethod>? paymentMethods,
      required this.enableDirectDiscount,
      required this.isVoucherApplicable,
      this.selectedVoucher,
      this.afterDiscountAmount,
      required this.failureOrSuccessValidatePromotionVoucher,
      this.voucherValidation,
      this.bannerId,
      this.isCalculatingPromotionVoucher})
      : _contracts = contracts,
        _paymentMethods = paymentMethods;

  @override
  final LoadingState loadingState;
  @override
  final LoadingState fetchPaymentMethodsloadingState;
  @override
  final LoadingState virtualAccountLoadingState;
  @override
  final LoadingState validateVoucherLoadingState;
  @override
  final bool shouldFocusCustomAmount;
  @override
  final Decimal minimumAmount;
  @override
  final Decimal? selectedAmount;
  @override
  final RepaymentContract? selectedContract;
  @override
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  @override
  final RepaymentBank? selectedBank;
  @override
  final bool isFromPayNow;
  @override
  final RepaymentAmountOptionType? selectedAmountOption;
  @override
  final Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      failureOrSuccessVirtualAccount;
  @override
  final bool canProcessPayment;
  @override
  final bool? isError;
  @override
  final bool? isVirtualAccountError;
  @override
  final bool? isUpdatingBalanceChange;
  @override
  final RepaymentContractVirtualAccount? contractVirtualAccount;
  @override
  final Option<Either<RepaymentFailure, List<RepaymentContract>>>
      failureOrSuccessFetchContracts;
  @override
  final Option<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>
      failureOrSuccessFetchUserPaymentMethods;
  @override
  final bool? isShowNoLoan;
  final List<RepaymentContract>? _contracts;
  @override
  List<RepaymentContract>? get contracts {
    final value = _contracts;
    if (value == null) return null;
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<RepaymentUserPaymentMethod>? _paymentMethods;
  @override
  List<RepaymentUserPaymentMethod>? get paymentMethods {
    final value = _paymentMethods;
    if (value == null) return null;
    if (_paymentMethods is EqualUnmodifiableListView) return _paymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool enableDirectDiscount;
// To detect the voucher is applicable using FE logic
  @override
  final bool isVoucherApplicable;
  @override
  final RepaymentVoucher? selectedVoucher;
  @override
  final Decimal? afterDiscountAmount;
  @override
  final Option<Either<RepaymentFailure, RepaymentVoucherValidation>>
      failureOrSuccessValidatePromotionVoucher;
  @override
  final RepaymentVoucherValidation? voucherValidation;
  @override
  final String? bannerId;
  @override
  final bool? isCalculatingPromotionVoucher;

  @override
  String toString() {
    return 'RepaymentMainState(loadingState: $loadingState, fetchPaymentMethodsloadingState: $fetchPaymentMethodsloadingState, virtualAccountLoadingState: $virtualAccountLoadingState, validateVoucherLoadingState: $validateVoucherLoadingState, shouldFocusCustomAmount: $shouldFocusCustomAmount, minimumAmount: $minimumAmount, selectedAmount: $selectedAmount, selectedContract: $selectedContract, selectedPaymentMethod: $selectedPaymentMethod, selectedBank: $selectedBank, isFromPayNow: $isFromPayNow, selectedAmountOption: $selectedAmountOption, failureOrSuccessVirtualAccount: $failureOrSuccessVirtualAccount, canProcessPayment: $canProcessPayment, isError: $isError, isVirtualAccountError: $isVirtualAccountError, isUpdatingBalanceChange: $isUpdatingBalanceChange, contractVirtualAccount: $contractVirtualAccount, failureOrSuccessFetchContracts: $failureOrSuccessFetchContracts, failureOrSuccessFetchUserPaymentMethods: $failureOrSuccessFetchUserPaymentMethods, isShowNoLoan: $isShowNoLoan, contracts: $contracts, paymentMethods: $paymentMethods, enableDirectDiscount: $enableDirectDiscount, isVoucherApplicable: $isVoucherApplicable, selectedVoucher: $selectedVoucher, afterDiscountAmount: $afterDiscountAmount, failureOrSuccessValidatePromotionVoucher: $failureOrSuccessValidatePromotionVoucher, voucherValidation: $voucherValidation, bannerId: $bannerId, isCalculatingPromotionVoucher: $isCalculatingPromotionVoucher)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentMainState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.fetchPaymentMethodsloadingState, fetchPaymentMethodsloadingState) ||
                other.fetchPaymentMethodsloadingState ==
                    fetchPaymentMethodsloadingState) &&
            (identical(other.virtualAccountLoadingState, virtualAccountLoadingState) ||
                other.virtualAccountLoadingState ==
                    virtualAccountLoadingState) &&
            (identical(other.validateVoucherLoadingState, validateVoucherLoadingState) ||
                other.validateVoucherLoadingState ==
                    validateVoucherLoadingState) &&
            (identical(other.shouldFocusCustomAmount, shouldFocusCustomAmount) ||
                other.shouldFocusCustomAmount == shouldFocusCustomAmount) &&
            (identical(other.minimumAmount, minimumAmount) ||
                other.minimumAmount == minimumAmount) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.selectedPaymentMethod, selectedPaymentMethod) ||
                other.selectedPaymentMethod == selectedPaymentMethod) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.isFromPayNow, isFromPayNow) ||
                other.isFromPayNow == isFromPayNow) &&
            (identical(other.selectedAmountOption, selectedAmountOption) ||
                other.selectedAmountOption == selectedAmountOption) &&
            (identical(other.failureOrSuccessVirtualAccount, failureOrSuccessVirtualAccount) ||
                other.failureOrSuccessVirtualAccount ==
                    failureOrSuccessVirtualAccount) &&
            (identical(other.canProcessPayment, canProcessPayment) ||
                other.canProcessPayment == canProcessPayment) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.isVirtualAccountError, isVirtualAccountError) ||
                other.isVirtualAccountError == isVirtualAccountError) &&
            (identical(other.isUpdatingBalanceChange, isUpdatingBalanceChange) ||
                other.isUpdatingBalanceChange == isUpdatingBalanceChange) &&
            (identical(other.contractVirtualAccount, contractVirtualAccount) ||
                other.contractVirtualAccount == contractVirtualAccount) &&
            (identical(other.failureOrSuccessFetchContracts, failureOrSuccessFetchContracts) ||
                other.failureOrSuccessFetchContracts == failureOrSuccessFetchContracts) &&
            (identical(other.failureOrSuccessFetchUserPaymentMethods, failureOrSuccessFetchUserPaymentMethods) || other.failureOrSuccessFetchUserPaymentMethods == failureOrSuccessFetchUserPaymentMethods) &&
            (identical(other.isShowNoLoan, isShowNoLoan) || other.isShowNoLoan == isShowNoLoan) &&
            const DeepCollectionEquality().equals(other._contracts, _contracts) &&
            const DeepCollectionEquality().equals(other._paymentMethods, _paymentMethods) &&
            (identical(other.enableDirectDiscount, enableDirectDiscount) || other.enableDirectDiscount == enableDirectDiscount) &&
            (identical(other.isVoucherApplicable, isVoucherApplicable) || other.isVoucherApplicable == isVoucherApplicable) &&
            (identical(other.selectedVoucher, selectedVoucher) || other.selectedVoucher == selectedVoucher) &&
            (identical(other.afterDiscountAmount, afterDiscountAmount) || other.afterDiscountAmount == afterDiscountAmount) &&
            (identical(other.failureOrSuccessValidatePromotionVoucher, failureOrSuccessValidatePromotionVoucher) || other.failureOrSuccessValidatePromotionVoucher == failureOrSuccessValidatePromotionVoucher) &&
            (identical(other.voucherValidation, voucherValidation) || other.voucherValidation == voucherValidation) &&
            (identical(other.bannerId, bannerId) || other.bannerId == bannerId) &&
            (identical(other.isCalculatingPromotionVoucher, isCalculatingPromotionVoucher) || other.isCalculatingPromotionVoucher == isCalculatingPromotionVoucher));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        loadingState,
        fetchPaymentMethodsloadingState,
        virtualAccountLoadingState,
        validateVoucherLoadingState,
        shouldFocusCustomAmount,
        minimumAmount,
        selectedAmount,
        selectedContract,
        selectedPaymentMethod,
        selectedBank,
        isFromPayNow,
        selectedAmountOption,
        failureOrSuccessVirtualAccount,
        canProcessPayment,
        isError,
        isVirtualAccountError,
        isUpdatingBalanceChange,
        contractVirtualAccount,
        failureOrSuccessFetchContracts,
        failureOrSuccessFetchUserPaymentMethods,
        isShowNoLoan,
        const DeepCollectionEquality().hash(_contracts),
        const DeepCollectionEquality().hash(_paymentMethods),
        enableDirectDiscount,
        isVoucherApplicable,
        selectedVoucher,
        afterDiscountAmount,
        failureOrSuccessValidatePromotionVoucher,
        voucherValidation,
        bannerId,
        isCalculatingPromotionVoucher
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentMainStateCopyWith<_$_RepaymentMainState> get copyWith =>
      __$$_RepaymentMainStateCopyWithImpl<_$_RepaymentMainState>(
          this, _$identity);
}

abstract class _RepaymentMainState implements RepaymentMainState {
  const factory _RepaymentMainState(
      {required final LoadingState loadingState,
      required final LoadingState fetchPaymentMethodsloadingState,
      required final LoadingState virtualAccountLoadingState,
      required final LoadingState validateVoucherLoadingState,
      required final bool shouldFocusCustomAmount,
      required final Decimal minimumAmount,
      required final Decimal? selectedAmount,
      required final RepaymentContract? selectedContract,
      final RepaymentUserPaymentMethod? selectedPaymentMethod,
      final RepaymentBank? selectedBank,
      required final bool isFromPayNow,
      required final RepaymentAmountOptionType? selectedAmountOption,
      required final Option<
              Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount,
      required final bool canProcessPayment,
      final bool? isError,
      final bool? isVirtualAccountError,
      final bool? isUpdatingBalanceChange,
      final RepaymentContractVirtualAccount? contractVirtualAccount,
      required final Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessFetchContracts,
      required final Option<
              Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>
          failureOrSuccessFetchUserPaymentMethods,
      final bool? isShowNoLoan,
      final List<RepaymentContract>? contracts,
      final List<RepaymentUserPaymentMethod>? paymentMethods,
      required final bool enableDirectDiscount,
      required final bool isVoucherApplicable,
      final RepaymentVoucher? selectedVoucher,
      final Decimal? afterDiscountAmount,
      required final Option<
              Either<RepaymentFailure, RepaymentVoucherValidation>>
          failureOrSuccessValidatePromotionVoucher,
      final RepaymentVoucherValidation? voucherValidation,
      final String? bannerId,
      final bool? isCalculatingPromotionVoucher}) = _$_RepaymentMainState;

  @override
  LoadingState get loadingState;
  @override
  LoadingState get fetchPaymentMethodsloadingState;
  @override
  LoadingState get virtualAccountLoadingState;
  @override
  LoadingState get validateVoucherLoadingState;
  @override
  bool get shouldFocusCustomAmount;
  @override
  Decimal get minimumAmount;
  @override
  Decimal? get selectedAmount;
  @override
  RepaymentContract? get selectedContract;
  @override
  RepaymentUserPaymentMethod? get selectedPaymentMethod;
  @override
  RepaymentBank? get selectedBank;
  @override
  bool get isFromPayNow;
  @override
  RepaymentAmountOptionType? get selectedAmountOption;
  @override
  Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      get failureOrSuccessVirtualAccount;
  @override
  bool get canProcessPayment;
  @override
  bool? get isError;
  @override
  bool? get isVirtualAccountError;
  @override
  bool? get isUpdatingBalanceChange;
  @override
  RepaymentContractVirtualAccount? get contractVirtualAccount;
  @override
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccessFetchContracts;
  @override
  Option<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>>
      get failureOrSuccessFetchUserPaymentMethods;
  @override
  bool? get isShowNoLoan;
  @override
  List<RepaymentContract>? get contracts;
  @override
  List<RepaymentUserPaymentMethod>? get paymentMethods;
  @override
  bool get enableDirectDiscount;
  @override // To detect the voucher is applicable using FE logic
  bool get isVoucherApplicable;
  @override
  RepaymentVoucher? get selectedVoucher;
  @override
  Decimal? get afterDiscountAmount;
  @override
  Option<Either<RepaymentFailure, RepaymentVoucherValidation>>
      get failureOrSuccessValidatePromotionVoucher;
  @override
  RepaymentVoucherValidation? get voucherValidation;
  @override
  String? get bannerId;
  @override
  bool? get isCalculatingPromotionVoucher;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentMainStateCopyWith<_$_RepaymentMainState> get copyWith =>
      throw _privateConstructorUsedError;
}
