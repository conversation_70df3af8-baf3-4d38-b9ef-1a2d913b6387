import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:collection/collection.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_messaging/koyal_messaging.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:stream_transform/stream_transform.dart';

import '../../../../capp_repayment.dart';

part 'repayment_main_bloc.freezed.dart';
part 'repayment_main_event.dart';
part 'repayment_main_state.dart';

class RepaymentMainBloc extends Bloc<RepaymentMainEvent, RepaymentMainState> {
  final IRepaymentRepository repaymentRepository;
  final IFeatureFlagRepository featureFlagRepository;
  final IPromotionRepository promotionRepository;
  final IUserRepository userRepository;
  final ImageServiceBase imageService;
  final Logger logger;
  final flowApplied = 'REPAYMENT';
  final debounceTimeInMilliseconds = 1500;

  RepaymentMainBloc({
    required this.repaymentRepository,
    required this.userRepository,
    required this.featureFlagRepository,
    required this.promotionRepository,
    required this.imageService,
    required this.logger,
  }) : super(RepaymentMainState.initialize()) {
    on<_Initialize>(_initialize);
    on<_InitRepaymentBanner>(_initRepaymentBanner);
    on<_FetchContracts>(_fetchContracts);
    on<_FetchUserPaymentMethods>(_fetchUserPaymentMethods);
    on<_ProccessKoyalDataMessage>(_proccessKoyalDataMessage);
    on<_ResetBalanceChangeState>(_resetBalanceChangeState);
    on<_SelectAmountOption>(_selectAmountOption);
    on<_SetAmount>(_setAmount);
    on<_FetchVirtualAccount>(_fetchVirtualAccount);
    on<_ResetVirtualAccount>(_resetVirtualAccount);
    on<_UpdateContract>(_updateContract);
    on<_ResetFetchContracts>(_resetFetchContracts);
    on<_SetPaymentMethod>(_setPaymentMethod);
    on<_SavePaymentMethod>(_savePaymentMethod);
    on<_ResetFocusCustomAmount>(_resetFocusCustomAmount);
    on<_SelectVoucher>(_selectVoucher);
    on<_RemoveVoucher>(_removeVoucher);
    on<_CheckVoucherApplicable>(_checkVoucherApplicable);
    on<_ApplyVoucherCalculation>(
      _applyVoucherCalculation,
      transformer: debounce(duration: Duration(milliseconds: debounceTimeInMilliseconds)),
    );
    on<_ResetState>(_resetState);
    on<_ValidatePromotionVoucher>(_validatePromotionVoucher);
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentMainState> emit,
  ) async {
    final user = (await userRepository.currentUser()).fold((l) => null, (r) => r);
    final signOnState = user?.signOnState;
    if (user?.accessLevel?.toLowerCase() == CoreConstants.publicAccessLevel.toLowerCase() ||
        user?.isInsider == true ||
        signOnState == SignOnStatus.client) {
      final isExistingUser = await userRepository.isExistingUser();
      if (!isExistingUser && signOnState != SignOnStatus.client) {
        emit(state.copyWith(isShowNoLoan: true));
        return;
      } else {
        // Get bank from cache if any
        final previousMobileBankingAppShortName =
            await repaymentRepository.getPreviousMobileBankingAppShortNameFromCache();
        final baoKimBanks = RepaymentBankingAppBloc.getBaoKiemBanks();
        final previousMobileBankingApp =
            baoKimBanks.firstWhereOrNull((e) => e.shortName == previousMobileBankingAppShortName);

        emit(
          state.copyWith(
            isFromPayNow: e.isFromPayNow ?? false,
            selectedBank: previousMobileBankingApp,
            enableDirectDiscount: e.enableDirectDiscount,
          ),
        );

        add(const RepaymentMainEvent.fetchContracts());
        add(RepaymentMainEvent.fetchUserPaymentMethods(repaymentLocalization: e.repaymentLocalization));
        add(const RepaymentMainEvent.initRepaymentBanner());
      }
    } else {
      emit(state.copyWith(isShowNoLoan: true));
    }
  }

  Future<void> _initRepaymentBanner(
    _InitRepaymentBanner e,
    Emitter<RepaymentMainState> emit,
  ) async {
    if (featureFlagRepository.isEnabledCached(FeatureFlag.repaymentMainScreenBanner)) {
      final bannerId = (await featureFlagRepository.getFeatureFlagValue(FeatureFlag.repaymentMainScreenBanner))
          .fold((_) => null, (r) => r);

      if (bannerId != null) {
        emit(
          state.copyWith(
            bannerId: bannerId,
          ),
        );
      }
    }
  }

  Future<void> _fetchContracts(
    _FetchContracts e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false));

    final apiResponse = await repaymentRepository.getContracts();
    logger.d('getContracts response$apiResponse');
    final response = apiResponse.map((r) => r.parseToRepaymentContract());

    emit(
      await response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: true,
          failureOrSuccessFetchContracts: optionOf(response),
        );
      }, (r) async {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessFetchContracts: optionOf(response),
          isError: false,
          contracts: r,
          isShowNoLoan: r.isEmpty,
        );
      }),
    );
  }

  Future<void> _fetchUserPaymentMethods(
    _FetchUserPaymentMethods e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(
      state.copyWith(
        fetchPaymentMethodsloadingState: LoadingState.isLoading,
        isError: false,
        failureOrSuccessFetchUserPaymentMethods: none(),
      ),
    );
    // Get saved payment method from cache
    final previousPaymentMethod = await repaymentRepository.getPreviousPaymentMethodFromCache();

    // Previous saved payment method
    RepaymentUserPaymentMethod? newPaymentMethod;

    // Check to get from Bapp or local
    final shouldGetFromBapp = featureFlagRepository.isEnabledCached(FeatureFlag.repaymentBappPaymentMethod);
    if (!shouldGetFromBapp) {
      final backupUserPaymentMethods =
          await repaymentRepository.getBackupUserPaymentMethods(l10nCappRepayment: e.repaymentLocalization);
      // Check to update latest payment method info if necessary
      if (previousPaymentMethod != null) {
        newPaymentMethod = backupUserPaymentMethods.firstWhereOrNull((e) => e.gmaId == previousPaymentMethod.gmaId);
      }

      final selectedPaymentMethod = newPaymentMethod ?? previousPaymentMethod;
      final canProcessPayment = _getCanProcessPaymentStateBySelectedPaymentMethod(selectedPaymentMethod);

      emit(
        state.copyWith(
          fetchPaymentMethodsloadingState: LoadingState.isCompleted,
          isError: false,
          paymentMethods: backupUserPaymentMethods,
          selectedPaymentMethod: selectedPaymentMethod,
          canProcessPayment: canProcessPayment,
        ),
      );

      return;
    }

    final response = await repaymentRepository.fetchUserPaymentMethods();
    logger.d('fetchUserPaymentMethods response$response');

    emit(
      await response.fold((l) async {
        // In case get payment methods fail, hardcode from FE
        final backupUserPaymentMethods =
            await repaymentRepository.getBackupUserPaymentMethods(l10nCappRepayment: e.repaymentLocalization);

        // Check to update latest payment method info if necessary
        if (previousPaymentMethod != null) {
          newPaymentMethod = backupUserPaymentMethods.firstWhereOrNull((e) => e.gmaId == previousPaymentMethod.gmaId);
        }

        final selectedPaymentMethod = newPaymentMethod ?? previousPaymentMethod;
        final canProcessPayment = _getCanProcessPaymentStateBySelectedPaymentMethod(selectedPaymentMethod);

        return state.copyWith(
          fetchPaymentMethodsloadingState: LoadingState.isCompleted,
          isError: false,
          paymentMethods: backupUserPaymentMethods,
          failureOrSuccessFetchUserPaymentMethods: optionOf(response),
          selectedPaymentMethod: selectedPaymentMethod,
          canProcessPayment: canProcessPayment,
        );
      }, (paymentMethods) async {
        // Store payment methods to cache for using in other screens
        if (paymentMethods.isNotEmpty) {
          await repaymentRepository.setPaymentMethodsToCache(paymentMethods: paymentMethods);

          // Check to update latest payment method info if necessary
          if (previousPaymentMethod != null) {
            newPaymentMethod = paymentMethods.firstWhereOrNull((e) => e.gmaId == previousPaymentMethod.gmaId);
          }
        }

        final selectedPaymentMethod = newPaymentMethod ?? previousPaymentMethod;
        final canProcessPayment = _getCanProcessPaymentStateBySelectedPaymentMethod(selectedPaymentMethod);

        return state.copyWith(
          fetchPaymentMethodsloadingState: LoadingState.isCompleted,
          failureOrSuccessFetchUserPaymentMethods: optionOf(response),
          isError: false,
          paymentMethods: paymentMethods,
          selectedPaymentMethod: selectedPaymentMethod,
          canProcessPayment: canProcessPayment,
        );
      }),
    );
  }

  Future<void> _proccessKoyalDataMessage(
    _ProccessKoyalDataMessage e,
    Emitter<RepaymentMainState> emit,
  ) async {
    if (e.notification?.data?.notificationType == MessageType.contractsAccountBalanceChanged) {
      // Handle balance changed
      emit(state.copyWith(isUpdatingBalanceChange: true));
      add(const RepaymentMainEvent.fetchContracts());
    }
  }

  Future<void> _resetBalanceChangeState(
    _ResetBalanceChangeState e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(state.copyWith(isUpdatingBalanceChange: false));
  }

  Future<void> _selectAmountOption(
    _SelectAmountOption e,
    Emitter<RepaymentMainState> emit,
  ) async {
    Decimal? selectedAmount;
    var shouldFocusCustomAmount = false;
    switch (e.selectAmountOptionType) {
      case RepaymentAmountOptionType.celDueAmount:
        {
          selectedAmount = state.selectedContract!.loanContract!.dueAmount;
          break;
        }
      case RepaymentAmountOptionType.relMinimumDueAmount:
        {
          selectedAmount = state.selectedContract!.relContract!.minimumDueAmount;
          break;
        }
      case RepaymentAmountOptionType.relTotalDueAmount:
        {
          selectedAmount = state.selectedContract!.relContract!.totalAmountDue;
          break;
        }
      case RepaymentAmountOptionType.customAmount:
        {
          selectedAmount = null;
          shouldFocusCustomAmount = true;
          break;
        }
      default:
        break;
    }
    final canProcessPayment = _canProcessPaymentState(
      selectedAmount: selectedAmount,
      selectedContract: state.selectedContract,
      selectedAmountOption: e.selectAmountOptionType,
      selectedRepaymentMethod: state.selectedPaymentMethod,
    );
    emit(
      state.copyWith(
        selectedAmount: selectedAmount,
        selectedAmountOption: e.selectAmountOptionType,
        canProcessPayment: canProcessPayment,
        shouldFocusCustomAmount: shouldFocusCustomAmount,
      ),
    );
    if (state.enableDirectDiscount && state.selectedVoucher != null) {
      add(RepaymentMainEvent.checkVoucherApplicable(state.selectedVoucher!));
    }
  }

  Future<void> _setAmount(
    _SetAmount e,
    Emitter<RepaymentMainState> emit,
  ) async {
    if (e.amount != null) {
      var selectedAmount = e.amount ?? Decimal.zero;
      final celContract = state.selectedContract?.loanContract;
      final relContract = state.selectedContract?.relContract;
      if (celContract != null) {
        // CEL contract
        final totalOutstandingDebt = celContract.totalOutstandingDebt ?? Decimal.zero;
        final overTotalDebt = selectedAmount >= totalOutstandingDebt;
        if (overTotalDebt) {
          selectedAmount = totalOutstandingDebt;
        }
        final canProcessPayment = _canProcessPaymentState(
          selectedAmount: selectedAmount,
          selectedContract: state.selectedContract,
          selectedAmountOption: state.selectedAmountOption,
          selectedRepaymentMethod: state.selectedPaymentMethod,
        );
        emit(
          state.copyWith(
            selectedAmount: selectedAmount,
            canProcessPayment: canProcessPayment,
          ),
        );
      } else if (relContract != null) {
        // REL contract
        final canProcessPayment = _canProcessPaymentState(
          selectedAmount: selectedAmount,
          selectedContract: state.selectedContract,
          selectedAmountOption: state.selectedAmountOption,
          selectedRepaymentMethod: state.selectedPaymentMethod,
        );
        emit(
          state.copyWith(
            selectedAmount: selectedAmount,
            canProcessPayment: canProcessPayment,
          ),
        );
      }
    } else {
      final canProcessPayment = _canProcessPaymentState(
        selectedAmount: null,
        selectedContract: state.selectedContract,
        selectedAmountOption: state.selectedAmountOption,
        selectedRepaymentMethod: state.selectedPaymentMethod,
      );
      emit(
        state.copyWith(
          selectedAmount: null,
          canProcessPayment: canProcessPayment,
        ),
      );
    }
    if (state.enableDirectDiscount && state.selectedVoucher != null) {
      add(RepaymentMainEvent.checkVoucherApplicable(state.selectedVoucher!));
    }
  }

  Future<void> _fetchVirtualAccount(
    _FetchVirtualAccount e,
    Emitter<RepaymentMainState> emit,
  ) async {
    final selectedAmount = state.selectedAmount;
    final contractNumber = state.selectedContract?.contractNumber ?? '';
    final selectedVoucherCode = e.voucherCode;
    emit(state.copyWith(virtualAccountLoadingState: LoadingState.isLoading, isError: false));

    final response = await repaymentRepository.getVirtualAccount(
      contractNumber: contractNumber,
      selectedAmount: selectedAmount!.toBigInt().toInt(),
      voucherCode: selectedVoucherCode,
    );
    logger.d('getVirtualAccount response$response');

    emit(
      response.fold((l) {
        return state.copyWith(
          virtualAccountLoadingState: LoadingState.isCompleted,
          isVirtualAccountError: true,
          failureOrSuccessVirtualAccount: optionOf(response),
        );
      }, (r) {
        return state.copyWith(
          virtualAccountLoadingState: LoadingState.isCompleted,
          failureOrSuccessVirtualAccount: optionOf(response),
          isVirtualAccountError: false,
          contractVirtualAccount: r,
        );
      }),
    );
  }

  Future<void> _resetVirtualAccount(
    _ResetVirtualAccount e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(
      state.copyWith(
        virtualAccountLoadingState: LoadingState.isInitial,
        failureOrSuccessVirtualAccount: none(),
        isVirtualAccountError: false,
        contractVirtualAccount: null,
      ),
    );
  }

  Future<void> _updateContract(
    _UpdateContract e,
    Emitter<RepaymentMainState> emit,
  ) async {
    add(const RepaymentMainEvent.removeVoucher());
    final contracts = state.contracts ?? [];
    if (contracts.isNotEmpty) {
      final validContract = _getValidContract(contracts: contracts, selectedContract: e.contract);
      if (validContract == null) {
        emit(state.copyWith(isShowNoLoan: true));
      } else {
        await emit.forEach(
          _updateContractState(validContract),
          onData: (state) {
            return state;
          },
        ).catchError((dynamic error) {
          logger.d(error.toString());
        });
      }
    }
  }

  Future<void> _resetFetchContracts(
    _ResetFetchContracts e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(
      state.copyWith(
        loadingState: LoadingState.isInitial,
        failureOrSuccessFetchContracts: none(),
        isError: false,
      ),
    );
  }

  Future<void> _setPaymentMethod(
    _SetPaymentMethod e,
    Emitter<RepaymentMainState> emit,
  ) async {
    final canProcessPayment = _canProcessPaymentState(
      selectedAmount: state.selectedAmount,
      selectedContract: state.selectedContract,
      selectedAmountOption: state.selectedAmountOption,
      selectedRepaymentMethod: e.paymentMethod,
    );
    emit(
      state.copyWith(
        selectedPaymentMethod: e.paymentMethod,
        selectedBank: e.bank,
        canProcessPayment: canProcessPayment,
      ),
    );
    // Check voucher applicable in case select app to app payment method
    if (state.enableDirectDiscount && state.selectedVoucher != null) {
      add(RepaymentMainEvent.checkVoucherApplicable(state.selectedVoucher!));
    }
  }

  Future<void> _savePaymentMethod(
    _SavePaymentMethod e,
    Emitter<RepaymentMainState> emit,
  ) async {
    final paymentMethod = e.paymentMethod;
    if (paymentMethod != null) {
      await repaymentRepository.setPaymentMethodToCache(paymentMethod);
      // Clear saved banking app if choose != mobile banking payment method
      if (paymentMethod.gmaId != RepaymentPaymentMethod.mobileBanking.getId()) {
        await repaymentRepository.setMobileBankingAppShortNameToCache(null);
      }
    }
    final bank = e.bank;
    if (bank != null) {
      await repaymentRepository.setMobileBankingAppShortNameToCache(bank.shortName);
    }
  }

  Future<void> _resetFocusCustomAmount(
    _ResetFocusCustomAmount e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(
      state.copyWith(
        shouldFocusCustomAmount: false,
      ),
    );
  }

  Future<void> _selectVoucher(
    _SelectVoucher e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(state.copyWith(selectedVoucher: e.voucher));
    add(RepaymentMainEvent.checkVoucherApplicable(e.voucher));
  }

  Future<void> _removeVoucher(
    _RemoveVoucher e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(
      state.copyWith(
        selectedVoucher: null,
        afterDiscountAmount: null,
        isVoucherApplicable: false,
        voucherValidation: null,
      ),
    );
  }

  Future<void> _checkVoucherApplicable(
    _CheckVoucherApplicable e,
    Emitter<RepaymentMainState> emit,
  ) async {
    // Clear previous voucher validation data
    emit(
      state.copyWith(
        failureOrSuccessValidatePromotionVoucher: none(),
        voucherValidation: null,
      ),
    );
    final isValidVoucherBaseOnPaymentMethod = state.selectedPaymentMethod == null ||
        ((e.voucher.appliedPaymentMethods?.isNotEmpty ?? false) &&
            e.voucher.appliedPaymentMethods!.map((e) => e.getId()).contains(state.selectedPaymentMethod?.gmaId));
    final isMobileBanking = state.selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.mobileBanking.getId();
    if (state.selectedAmount != null &&
        (state.selectedAmount! >= e.voucher.minRequiredAmount!) &&
        isValidVoucherBaseOnPaymentMethod &&
        !isMobileBanking) {
      emit(state.copyWith(isVoucherApplicable: true));
      emit(state.copyWith(isCalculatingPromotionVoucher: true));
      add(RepaymentMainEvent.applyVoucherCalculation(e.voucher));
    } else {
      emit(state.copyWith(isVoucherApplicable: false, afterDiscountAmount: null));
    }
  }

  Future<void> _applyVoucherCalculation(
    _ApplyVoucherCalculation e,
    Emitter<RepaymentMainState> emit,
  ) async {
    final voucherCode = e.voucher.code;
    final minRequiredAmount = e.voucher.minRequiredAmount;
    final originalAmount = state.selectedAmount ?? Decimal.zero;
    // Check amount condition after debounce to prevent calling API when the amount is less than min required amount
    // in latest event (because current default debounce implementation is always emit the latest event)
    if (originalAmount < minRequiredAmount!) {
      emit(
        state.copyWith(
          isCalculatingPromotionVoucher: false,
          afterDiscountAmount: null,
        ),
      );
      return;
    }
    final response = await promotionRepository.calculatePromotionVoucher(
      voucherCode: voucherCode,
      minRequiredAmount: minRequiredAmount,
      originalAmount: originalAmount,
    );
    logger.d('calculate promotion voucher response$response');

    emit(
      await response.fold((l) {
        return state.copyWith(
          isCalculatingPromotionVoucher: false,
          afterDiscountAmount: null,
        );
      }, (r) async {
        return state.copyWith(
          isCalculatingPromotionVoucher: false,
          afterDiscountAmount: r.calculation?.finalAmount,
        );
      }),
    );
  }

  Future<void> _resetState(
    _ResetState e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(
      state.copyWith(
        loadingState: LoadingState.isInitial,
        fetchPaymentMethodsloadingState: LoadingState.isInitial,
        failureOrSuccessFetchContracts: none(),
        failureOrSuccessValidatePromotionVoucher: none(),
        failureOrSuccessVirtualAccount: none(),
      ),
    );
  }

  Future<void> _validatePromotionVoucher(
    _ValidatePromotionVoucher e,
    Emitter<RepaymentMainState> emit,
  ) async {
    emit(state.copyWith(validateVoucherLoadingState: LoadingState.isLoading, isError: false));

    final response = await promotionRepository.validatePromotionVoucherV2(
      e.request,
    );
    logger.d('validatePromotionVoucher response$response');

    emit(
      response.fold((l) {
        return state.copyWith(
          validateVoucherLoadingState: LoadingState.isCompleted,
          failureOrSuccessValidatePromotionVoucher: optionOf(response),
          voucherValidation: null,
        );
      }, (r) {
        return state.copyWith(
          validateVoucherLoadingState: LoadingState.isCompleted,
          failureOrSuccessValidatePromotionVoucher: optionOf(response),
          voucherValidation: r,
          // Update final amount to cover the case when calculation failed due to server error/no internet
          afterDiscountAmount: r.calculation?.finalAmount,
        );
      }),
    );
  }

  Stream<RepaymentMainState> _updateContractState(RepaymentContract repaymentContract) async* {
    // CEL contract
    if (repaymentContract.loanContract != null) {
      final loanContract = repaymentContract.loanContract!;
      final minimumAmount = loanContract.totalOutstandingDebt! < Constants.defaultMinThresholdAmount
          ? loanContract.totalOutstandingDebt!
          : Constants.defaultMinThresholdAmount;
      final dueAmount = loanContract.dueAmount!;
      final isDueAmountLessThanMinimumAmount = dueAmount < minimumAmount;
      yield state.copyWith(
        selectedContract: repaymentContract,
        minimumAmount: minimumAmount,
        selectedAmountOption: null,
        selectedAmount: null,
      );
      // Wait for amount selection option to be reset
      await Future<void>.delayed(const Duration(milliseconds: 100));
      if (isDueAmountLessThanMinimumAmount) {
        add(
          const RepaymentMainEvent.selectAmountOption(
            selectAmountOptionType: RepaymentAmountOptionType.customAmount,
          ),
        );
      } else {
        add(
          const RepaymentMainEvent.selectAmountOption(
            selectAmountOptionType: RepaymentAmountOptionType.celDueAmount,
          ),
        );
      }
    } else if (repaymentContract.relContract != null) {
      // REL contract
      final relContract = repaymentContract.relContract!;
      final totalDueAmount = relContract.totalAmountDue!;
      final minimumDueAmount = relContract.minimumDueAmount!;
      final isPayOff = relContract.totalAmountDue! == Decimal.zero;
      final isBnplProduct = (relContract.gmaProductType ?? '') == RepaymentGmaProductType.bnpl.id;
      Decimal minimumAmount;
      if (totalDueAmount < Constants.defaultMinThresholdAmount && !isPayOff) {
        minimumAmount = totalDueAmount;
      } else {
        minimumAmount = Constants.defaultMinThresholdAmount;
      }
      final isMinimumDueAmountLessThanMinimumAmount = minimumDueAmount < minimumAmount;
      yield state.copyWith(
        selectedContract: repaymentContract,
        minimumAmount: minimumAmount,
        selectedAmountOption: null,
        selectedAmount: null,
      );
      // Wait for amount selection option to be reset
      await Future<void>.delayed(const Duration(milliseconds: 100));
      if (isBnplProduct) {
        // BNPL product
        if (!isPayOff) {
          add(
            const RepaymentMainEvent.selectAmountOption(
              selectAmountOptionType: RepaymentAmountOptionType.relTotalDueAmount,
            ),
          );
        } else {
          add(
            const RepaymentMainEvent.selectAmountOption(
              selectAmountOptionType: RepaymentAmountOptionType.customAmount,
            ),
          );
        }
      } else {
        // Credit Card product
        if (!isPayOff) {
          if (isMinimumDueAmountLessThanMinimumAmount) {
            add(
              const RepaymentMainEvent.selectAmountOption(
                selectAmountOptionType: RepaymentAmountOptionType.relTotalDueAmount,
              ),
            );
          } else {
            add(
              const RepaymentMainEvent.selectAmountOption(
                selectAmountOptionType: RepaymentAmountOptionType.relMinimumDueAmount,
              ),
            );
          }
        } else {
          add(
            const RepaymentMainEvent.selectAmountOption(
              selectAmountOptionType: RepaymentAmountOptionType.customAmount,
            ),
          );
        }
      }
    }
  }

  bool _canProcessPaymentState({
    required Decimal? selectedAmount,
    required RepaymentContract? selectedContract,
    required RepaymentAmountOptionType? selectedAmountOption,
    required RepaymentUserPaymentMethod? selectedRepaymentMethod,
  }) {
    final canProcessPayment = (selectedContract?.loanContract != null || selectedContract?.relContract != null) &&
        selectedAmountOption != null &&
        selectedAmount != null &&
        (selectedAmount >= state.minimumAmount) &&
        selectedRepaymentMethod != null;
    return canProcessPayment;
  }

  bool _getCanProcessPaymentStateBySelectedPaymentMethod(RepaymentUserPaymentMethod? selectedPaymentMethod,) {
    return _canProcessPaymentState(
      selectedAmount: state.selectedAmount,
      selectedContract: state.selectedContract,
      selectedAmountOption: state.selectedAmountOption,
      selectedRepaymentMethod: selectedPaymentMethod,
    );
  }

  bool isCelContractPayable(RepaymentLoanContract contract) {
    return contract.totalOutstandingDebt != null && contract.totalOutstandingDebt != Decimal.zero;
  }

  RepaymentContract? _getValidContract({
    required List<RepaymentContract> contracts,
    RepaymentContract? selectedContract,
  }) {
    final validContract = contracts.firstWhereOrNull(
      (e) {
        return (selectedContract == null || selectedContract.contractNumber == e.contractNumber) &&
            ((e.contractType == RepaymentContractType.cel && isCelContractPayable(e.loanContract!)) ||
                e.contractType == RepaymentContractType.rel);
      },
    );
    return validContract;
  }

  EventTransformer<Event> debounce<Event>({
    required Duration duration,
  }) {
    return (events, mapper) => events.debounce(duration).switchMap(mapper);
  }

  bool isShowConfirmationQuitDialog() {
    return featureFlagRepository.isEnabledCached(FeatureFlag.repaymentPopupPleaseStay);
  }

  bool isShowSappiAlert({required Decimal? sappiInstallmentAmount}) {
    final isEnableSappiAlert = featureFlagRepository.isEnabledCached(FeatureFlag.repaymentSappiAlertBar);
    return isEnableSappiAlert && sappiInstallmentAmount != null;
  }
}
