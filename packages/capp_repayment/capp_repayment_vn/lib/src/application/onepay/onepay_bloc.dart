import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_localizations/koyal_localizations.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment.dart';

part 'onepay_bloc.freezed.dart';
part 'onepay_event.dart';
part 'onepay_state.dart';

class OnePayBloc extends Bloc<OnePayEvent, OnePayState> {
  final CappRepaymentSettingsVn settings;
  final LocalizationBloc localizationBloc;

  OnePayBloc({required this.settings, required this.localizationBloc}) : super(OnePayState.initialize()) {
    on<_Initialize>((e, emit) async {
      emit(
        state.copyWith(
          loadingState: LoadingState.isLoading,
          contractNumber: e.contractNumber,
          transactionId: e.transactionId,
          amount: e.amount,
          customerName: e.customerName,
          onePayPortalUrl: e.paymentUrl,
        ),
      );
    });
  }
}
