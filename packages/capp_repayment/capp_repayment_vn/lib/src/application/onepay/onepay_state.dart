part of 'onepay_bloc.dart';

@freezed
class OnePayState with _$OnePayState {
  const factory OnePayState({
    required LoadingState loadingState,
    required bool isError,
    required String? contractNumber,
    required String? transactionId,
    required double? amount,
    required String? customerName,
    required String provider,
    required String? onePayPortalUrl,
  }) = _OnePayState;

  factory OnePayState.initialize() => const OnePayState(
        loadingState: LoadingState.isInitial,
        isError: false,
        contractNumber: null,
        transactionId: null,
        amount: null,
        customerName: null,
        provider: 'OnePay',
        onePayPortalUrl: null,
      );
}
