// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onepay_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$OnePayEvent {
  String get contractNumber => throw _privateConstructorUsedError;
  String get transactionId => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  String get customerName => throw _privateConstructorUsedError;
  String get paymentUrl => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber, String transactionId,
            double amount, String customerName, String paymentUrl)
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber, String transactionId,
            double amount, String customerName, String paymentUrl)?
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber, String transactionId, double amount,
            String customerName, String paymentUrl)?
        initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OnePayEventCopyWith<OnePayEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnePayEventCopyWith<$Res> {
  factory $OnePayEventCopyWith(
          OnePayEvent value, $Res Function(OnePayEvent) then) =
      _$OnePayEventCopyWithImpl<$Res, OnePayEvent>;
  @useResult
  $Res call(
      {String contractNumber,
      String transactionId,
      double amount,
      String customerName,
      String paymentUrl});
}

/// @nodoc
class _$OnePayEventCopyWithImpl<$Res, $Val extends OnePayEvent>
    implements $OnePayEventCopyWith<$Res> {
  _$OnePayEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
    Object? transactionId = null,
    Object? amount = null,
    Object? customerName = null,
    Object? paymentUrl = null,
  }) {
    return _then(_value.copyWith(
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      customerName: null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      paymentUrl: null == paymentUrl
          ? _value.paymentUrl
          : paymentUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res>
    implements $OnePayEventCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String contractNumber,
      String transactionId,
      double amount,
      String customerName,
      String paymentUrl});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$OnePayEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
    Object? transactionId = null,
    Object? amount = null,
    Object? customerName = null,
    Object? paymentUrl = null,
  }) {
    return _then(_$_Initialize(
      null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
      null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      null == paymentUrl
          ? _value.paymentUrl
          : paymentUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(this.contractNumber, this.transactionId, this.amount,
      this.customerName, this.paymentUrl);

  @override
  final String contractNumber;
  @override
  final String transactionId;
  @override
  final double amount;
  @override
  final String customerName;
  @override
  final String paymentUrl;

  @override
  String toString() {
    return 'OnePayEvent.initialize(contractNumber: $contractNumber, transactionId: $transactionId, amount: $amount, customerName: $customerName, paymentUrl: $paymentUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.paymentUrl, paymentUrl) ||
                other.paymentUrl == paymentUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractNumber, transactionId,
      amount, customerName, paymentUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber, String transactionId,
            double amount, String customerName, String paymentUrl)
        initialize,
  }) {
    return initialize(
        contractNumber, transactionId, amount, customerName, paymentUrl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber, String transactionId,
            double amount, String customerName, String paymentUrl)?
        initialize,
  }) {
    return initialize?.call(
        contractNumber, transactionId, amount, customerName, paymentUrl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber, String transactionId, double amount,
            String customerName, String paymentUrl)?
        initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(
          contractNumber, transactionId, amount, customerName, paymentUrl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements OnePayEvent {
  const factory _Initialize(
      final String contractNumber,
      final String transactionId,
      final double amount,
      final String customerName,
      final String paymentUrl) = _$_Initialize;

  @override
  String get contractNumber;
  @override
  String get transactionId;
  @override
  double get amount;
  @override
  String get customerName;
  @override
  String get paymentUrl;
  @override
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$OnePayState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  String? get transactionId => throw _privateConstructorUsedError;
  double? get amount => throw _privateConstructorUsedError;
  String? get customerName => throw _privateConstructorUsedError;
  String get provider => throw _privateConstructorUsedError;
  String? get onePayPortalUrl => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OnePayStateCopyWith<OnePayState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnePayStateCopyWith<$Res> {
  factory $OnePayStateCopyWith(
          OnePayState value, $Res Function(OnePayState) then) =
      _$OnePayStateCopyWithImpl<$Res, OnePayState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool isError,
      String? contractNumber,
      String? transactionId,
      double? amount,
      String? customerName,
      String provider,
      String? onePayPortalUrl});
}

/// @nodoc
class _$OnePayStateCopyWithImpl<$Res, $Val extends OnePayState>
    implements $OnePayStateCopyWith<$Res> {
  _$OnePayStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = null,
    Object? contractNumber = freezed,
    Object? transactionId = freezed,
    Object? amount = freezed,
    Object? customerName = freezed,
    Object? provider = null,
    Object? onePayPortalUrl = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      onePayPortalUrl: freezed == onePayPortalUrl
          ? _value.onePayPortalUrl
          : onePayPortalUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_OnePayStateCopyWith<$Res>
    implements $OnePayStateCopyWith<$Res> {
  factory _$$_OnePayStateCopyWith(
          _$_OnePayState value, $Res Function(_$_OnePayState) then) =
      __$$_OnePayStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool isError,
      String? contractNumber,
      String? transactionId,
      double? amount,
      String? customerName,
      String provider,
      String? onePayPortalUrl});
}

/// @nodoc
class __$$_OnePayStateCopyWithImpl<$Res>
    extends _$OnePayStateCopyWithImpl<$Res, _$_OnePayState>
    implements _$$_OnePayStateCopyWith<$Res> {
  __$$_OnePayStateCopyWithImpl(
      _$_OnePayState _value, $Res Function(_$_OnePayState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = null,
    Object? contractNumber = freezed,
    Object? transactionId = freezed,
    Object? amount = freezed,
    Object? customerName = freezed,
    Object? provider = null,
    Object? onePayPortalUrl = freezed,
  }) {
    return _then(_$_OnePayState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      onePayPortalUrl: freezed == onePayPortalUrl
          ? _value.onePayPortalUrl
          : onePayPortalUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_OnePayState implements _OnePayState {
  const _$_OnePayState(
      {required this.loadingState,
      required this.isError,
      required this.contractNumber,
      required this.transactionId,
      required this.amount,
      required this.customerName,
      required this.provider,
      required this.onePayPortalUrl});

  @override
  final LoadingState loadingState;
  @override
  final bool isError;
  @override
  final String? contractNumber;
  @override
  final String? transactionId;
  @override
  final double? amount;
  @override
  final String? customerName;
  @override
  final String provider;
  @override
  final String? onePayPortalUrl;

  @override
  String toString() {
    return 'OnePayState(loadingState: $loadingState, isError: $isError, contractNumber: $contractNumber, transactionId: $transactionId, amount: $amount, customerName: $customerName, provider: $provider, onePayPortalUrl: $onePayPortalUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_OnePayState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.onePayPortalUrl, onePayPortalUrl) ||
                other.onePayPortalUrl == onePayPortalUrl));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      isError,
      contractNumber,
      transactionId,
      amount,
      customerName,
      provider,
      onePayPortalUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_OnePayStateCopyWith<_$_OnePayState> get copyWith =>
      __$$_OnePayStateCopyWithImpl<_$_OnePayState>(this, _$identity);
}

abstract class _OnePayState implements OnePayState {
  const factory _OnePayState(
      {required final LoadingState loadingState,
      required final bool isError,
      required final String? contractNumber,
      required final String? transactionId,
      required final double? amount,
      required final String? customerName,
      required final String provider,
      required final String? onePayPortalUrl}) = _$_OnePayState;

  @override
  LoadingState get loadingState;
  @override
  bool get isError;
  @override
  String? get contractNumber;
  @override
  String? get transactionId;
  @override
  double? get amount;
  @override
  String? get customerName;
  @override
  String get provider;
  @override
  String? get onePayPortalUrl;
  @override
  @JsonKey(ignore: true)
  _$$_OnePayStateCopyWith<_$_OnePayState> get copyWith =>
      throw _privateConstructorUsedError;
}
