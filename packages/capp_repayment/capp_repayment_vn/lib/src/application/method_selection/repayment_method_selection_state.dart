part of 'repayment_method_selection_bloc.dart';

@freezed
class RepaymentMethodSelectionState with _$RepaymentMethodSelectionState {
  const factory RepaymentMethodSelectionState({
    required LoadingState loadingState,
    bool? isError,
    required Option<Either<RepaymentFailure, List<RepaymentContract>>> failureOrSuccessContracts,
    bool? isFromPayNow,
    RepaymentContract? selectedContract,
    String? bannerId,
    List<RepaymentContract>? contracts,
  }) = _RepaymentMethodSelectionState;

  factory RepaymentMethodSelectionState.initialize() => RepaymentMethodSelectionState(
        loadingState: LoadingState.isInitial,
        isError: false,
        failureOrSuccessContracts: none(),
        isFromPayNow: false,
      );
}
