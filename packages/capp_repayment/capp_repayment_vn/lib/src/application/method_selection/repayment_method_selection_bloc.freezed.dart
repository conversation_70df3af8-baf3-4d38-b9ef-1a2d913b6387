// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_method_selection_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentMethodSelectionEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract, bool? isFromPayNow)
        initialize,
    required TResult Function() fetchContracts,
    required TResult Function() initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult Function()? fetchContracts,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentMethodSelectionEventCopyWith<$Res> {
  factory $RepaymentMethodSelectionEventCopyWith(
          RepaymentMethodSelectionEvent value,
          $Res Function(RepaymentMethodSelectionEvent) then) =
      _$RepaymentMethodSelectionEventCopyWithImpl<$Res,
          RepaymentMethodSelectionEvent>;
}

/// @nodoc
class _$RepaymentMethodSelectionEventCopyWithImpl<$Res,
        $Val extends RepaymentMethodSelectionEvent>
    implements $RepaymentMethodSelectionEventCopyWith<$Res> {
  _$RepaymentMethodSelectionEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentContract selectedContract, bool? isFromPayNow});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentMethodSelectionEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedContract = null,
    Object? isFromPayNow = freezed,
  }) {
    return _then(_$_Initialize(
      selectedContract: null == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract,
      isFromPayNow: freezed == isFromPayNow
          ? _value.isFromPayNow
          : isFromPayNow // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.selectedContract, this.isFromPayNow});

  @override
  final RepaymentContract selectedContract;
  @override
  final bool? isFromPayNow;

  @override
  String toString() {
    return 'RepaymentMethodSelectionEvent.initialize(selectedContract: $selectedContract, isFromPayNow: $isFromPayNow)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.isFromPayNow, isFromPayNow) ||
                other.isFromPayNow == isFromPayNow));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedContract, isFromPayNow);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract, bool? isFromPayNow)
        initialize,
    required TResult Function() fetchContracts,
    required TResult Function() initRepaymentBanner,
  }) {
    return initialize(selectedContract, isFromPayNow);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? initRepaymentBanner,
  }) {
    return initialize?.call(selectedContract, isFromPayNow);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult Function()? fetchContracts,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(selectedContract, isFromPayNow);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentMethodSelectionEvent {
  const factory _Initialize(
      {required final RepaymentContract selectedContract,
      final bool? isFromPayNow}) = _$_Initialize;

  RepaymentContract get selectedContract;
  bool? get isFromPayNow;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FetchContractsCopyWith<$Res> {
  factory _$$_FetchContractsCopyWith(
          _$_FetchContracts value, $Res Function(_$_FetchContracts) then) =
      __$$_FetchContractsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_FetchContractsCopyWithImpl<$Res>
    extends _$RepaymentMethodSelectionEventCopyWithImpl<$Res, _$_FetchContracts>
    implements _$$_FetchContractsCopyWith<$Res> {
  __$$_FetchContractsCopyWithImpl(
      _$_FetchContracts _value, $Res Function(_$_FetchContracts) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_FetchContracts implements _FetchContracts {
  const _$_FetchContracts();

  @override
  String toString() {
    return 'RepaymentMethodSelectionEvent.fetchContracts()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_FetchContracts);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract, bool? isFromPayNow)
        initialize,
    required TResult Function() fetchContracts,
    required TResult Function() initRepaymentBanner,
  }) {
    return fetchContracts();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? initRepaymentBanner,
  }) {
    return fetchContracts?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult Function()? fetchContracts,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchContracts != null) {
      return fetchContracts();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return fetchContracts(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return fetchContracts?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (fetchContracts != null) {
      return fetchContracts(this);
    }
    return orElse();
  }
}

abstract class _FetchContracts implements RepaymentMethodSelectionEvent {
  const factory _FetchContracts() = _$_FetchContracts;
}

/// @nodoc
abstract class _$$_InitRepaymentBannerCopyWith<$Res> {
  factory _$$_InitRepaymentBannerCopyWith(_$_InitRepaymentBanner value,
          $Res Function(_$_InitRepaymentBanner) then) =
      __$$_InitRepaymentBannerCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitRepaymentBannerCopyWithImpl<$Res>
    extends _$RepaymentMethodSelectionEventCopyWithImpl<$Res,
        _$_InitRepaymentBanner>
    implements _$$_InitRepaymentBannerCopyWith<$Res> {
  __$$_InitRepaymentBannerCopyWithImpl(_$_InitRepaymentBanner _value,
      $Res Function(_$_InitRepaymentBanner) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_InitRepaymentBanner implements _InitRepaymentBanner {
  const _$_InitRepaymentBanner();

  @override
  String toString() {
    return 'RepaymentMethodSelectionEvent.initRepaymentBanner()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_InitRepaymentBanner);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract selectedContract, bool? isFromPayNow)
        initialize,
    required TResult Function() fetchContracts,
    required TResult Function() initRepaymentBanner,
  }) {
    return initRepaymentBanner();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? initRepaymentBanner,
  }) {
    return initRepaymentBanner?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract selectedContract, bool? isFromPayNow)?
        initialize,
    TResult Function()? fetchContracts,
    TResult Function()? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initRepaymentBanner != null) {
      return initRepaymentBanner();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_InitRepaymentBanner value) initRepaymentBanner,
  }) {
    return initRepaymentBanner(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_InitRepaymentBanner value)? initRepaymentBanner,
  }) {
    return initRepaymentBanner?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_InitRepaymentBanner value)? initRepaymentBanner,
    required TResult orElse(),
  }) {
    if (initRepaymentBanner != null) {
      return initRepaymentBanner(this);
    }
    return orElse();
  }
}

abstract class _InitRepaymentBanner implements RepaymentMethodSelectionEvent {
  const factory _InitRepaymentBanner() = _$_InitRepaymentBanner;
}

/// @nodoc
mixin _$RepaymentMethodSelectionState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccessContracts => throw _privateConstructorUsedError;
  bool? get isFromPayNow => throw _privateConstructorUsedError;
  RepaymentContract? get selectedContract => throw _privateConstructorUsedError;
  String? get bannerId => throw _privateConstructorUsedError;
  List<RepaymentContract>? get contracts => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentMethodSelectionStateCopyWith<RepaymentMethodSelectionState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentMethodSelectionStateCopyWith<$Res> {
  factory $RepaymentMethodSelectionStateCopyWith(
          RepaymentMethodSelectionState value,
          $Res Function(RepaymentMethodSelectionState) then) =
      _$RepaymentMethodSelectionStateCopyWithImpl<$Res,
          RepaymentMethodSelectionState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessContracts,
      bool? isFromPayNow,
      RepaymentContract? selectedContract,
      String? bannerId,
      List<RepaymentContract>? contracts});
}

/// @nodoc
class _$RepaymentMethodSelectionStateCopyWithImpl<$Res,
        $Val extends RepaymentMethodSelectionState>
    implements $RepaymentMethodSelectionStateCopyWith<$Res> {
  _$RepaymentMethodSelectionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? failureOrSuccessContracts = null,
    Object? isFromPayNow = freezed,
    Object? selectedContract = freezed,
    Object? bannerId = freezed,
    Object? contracts = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      failureOrSuccessContracts: null == failureOrSuccessContracts
          ? _value.failureOrSuccessContracts
          : failureOrSuccessContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      isFromPayNow: freezed == isFromPayNow
          ? _value.isFromPayNow
          : isFromPayNow // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      contracts: freezed == contracts
          ? _value.contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentMethodSelectionStateCopyWith<$Res>
    implements $RepaymentMethodSelectionStateCopyWith<$Res> {
  factory _$$_RepaymentMethodSelectionStateCopyWith(
          _$_RepaymentMethodSelectionState value,
          $Res Function(_$_RepaymentMethodSelectionState) then) =
      __$$_RepaymentMethodSelectionStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessContracts,
      bool? isFromPayNow,
      RepaymentContract? selectedContract,
      String? bannerId,
      List<RepaymentContract>? contracts});
}

/// @nodoc
class __$$_RepaymentMethodSelectionStateCopyWithImpl<$Res>
    extends _$RepaymentMethodSelectionStateCopyWithImpl<$Res,
        _$_RepaymentMethodSelectionState>
    implements _$$_RepaymentMethodSelectionStateCopyWith<$Res> {
  __$$_RepaymentMethodSelectionStateCopyWithImpl(
      _$_RepaymentMethodSelectionState _value,
      $Res Function(_$_RepaymentMethodSelectionState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? failureOrSuccessContracts = null,
    Object? isFromPayNow = freezed,
    Object? selectedContract = freezed,
    Object? bannerId = freezed,
    Object? contracts = freezed,
  }) {
    return _then(_$_RepaymentMethodSelectionState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      failureOrSuccessContracts: null == failureOrSuccessContracts
          ? _value.failureOrSuccessContracts
          : failureOrSuccessContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      isFromPayNow: freezed == isFromPayNow
          ? _value.isFromPayNow
          : isFromPayNow // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      contracts: freezed == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>?,
    ));
  }
}

/// @nodoc

class _$_RepaymentMethodSelectionState
    implements _RepaymentMethodSelectionState {
  const _$_RepaymentMethodSelectionState(
      {required this.loadingState,
      this.isError,
      required this.failureOrSuccessContracts,
      this.isFromPayNow,
      this.selectedContract,
      this.bannerId,
      final List<RepaymentContract>? contracts})
      : _contracts = contracts;

  @override
  final LoadingState loadingState;
  @override
  final bool? isError;
  @override
  final Option<Either<RepaymentFailure, List<RepaymentContract>>>
      failureOrSuccessContracts;
  @override
  final bool? isFromPayNow;
  @override
  final RepaymentContract? selectedContract;
  @override
  final String? bannerId;
  final List<RepaymentContract>? _contracts;
  @override
  List<RepaymentContract>? get contracts {
    final value = _contracts;
    if (value == null) return null;
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RepaymentMethodSelectionState(loadingState: $loadingState, isError: $isError, failureOrSuccessContracts: $failureOrSuccessContracts, isFromPayNow: $isFromPayNow, selectedContract: $selectedContract, bannerId: $bannerId, contracts: $contracts)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentMethodSelectionState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.failureOrSuccessContracts,
                    failureOrSuccessContracts) ||
                other.failureOrSuccessContracts == failureOrSuccessContracts) &&
            (identical(other.isFromPayNow, isFromPayNow) ||
                other.isFromPayNow == isFromPayNow) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.bannerId, bannerId) ||
                other.bannerId == bannerId) &&
            const DeepCollectionEquality()
                .equals(other._contracts, _contracts));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      isError,
      failureOrSuccessContracts,
      isFromPayNow,
      selectedContract,
      bannerId,
      const DeepCollectionEquality().hash(_contracts));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentMethodSelectionStateCopyWith<_$_RepaymentMethodSelectionState>
      get copyWith => __$$_RepaymentMethodSelectionStateCopyWithImpl<
          _$_RepaymentMethodSelectionState>(this, _$identity);
}

abstract class _RepaymentMethodSelectionState
    implements RepaymentMethodSelectionState {
  const factory _RepaymentMethodSelectionState(
      {required final LoadingState loadingState,
      final bool? isError,
      required final Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccessContracts,
      final bool? isFromPayNow,
      final RepaymentContract? selectedContract,
      final String? bannerId,
      final List<RepaymentContract>?
          contracts}) = _$_RepaymentMethodSelectionState;

  @override
  LoadingState get loadingState;
  @override
  bool? get isError;
  @override
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccessContracts;
  @override
  bool? get isFromPayNow;
  @override
  RepaymentContract? get selectedContract;
  @override
  String? get bannerId;
  @override
  List<RepaymentContract>? get contracts;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentMethodSelectionStateCopyWith<_$_RepaymentMethodSelectionState>
      get copyWith => throw _privateConstructorUsedError;
}
