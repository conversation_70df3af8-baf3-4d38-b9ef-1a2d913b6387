part of 'repayment_method_selection_bloc.dart';

@freezed
class RepaymentMethodSelectionEvent with _$RepaymentMethodSelectionEvent {
  const factory RepaymentMethodSelectionEvent.initialize({
    required RepaymentContract selectedContract,
    bool? isFromPayNow,
  }) = _Initialize;
  const factory RepaymentMethodSelectionEvent.fetchContracts() = _FetchContracts;
  const factory RepaymentMethodSelectionEvent.initRepaymentBanner() = _InitRepaymentBanner;
}
