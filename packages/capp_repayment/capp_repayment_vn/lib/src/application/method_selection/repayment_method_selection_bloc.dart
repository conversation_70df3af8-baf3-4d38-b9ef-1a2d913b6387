import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:collection/collection.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_repayment.dart';

part 'repayment_method_selection_bloc.freezed.dart';
part 'repayment_method_selection_event.dart';
part 'repayment_method_selection_state.dart';

class RepaymentMethodSelectionBloc extends Bloc<RepaymentMethodSelectionEvent, RepaymentMethodSelectionState> {
  final IRepaymentRepository repaymentRepository;
  final IFeatureFlagRepository featureFlagRepository;
  final ImageServiceBase imageService;
  final Logger logger;
  final Decimal minimumAmount = Decimal.fromInt(50000);

  RepaymentMethodSelectionBloc({
    required this.featureFlagRepository,
    required this.imageService,
    required this.repaymentRepository,
    required this.logger,
  }) : super(RepaymentMethodSelectionState.initialize()) {
    on<_Initialize>(_initialize);
    on<_InitRepaymentBanner>(_initRepaymentBanner);
    on<_FetchContracts>(_fetchContracts);
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentMethodSelectionState> emit,
  ) async {
    final isFromPayNow = e.isFromPayNow ?? false;
    emit(
      state.copyWith(
        loadingState: LoadingState.isInitial,
        selectedContract: e.selectedContract,
        isFromPayNow: isFromPayNow,
      ),
    );

    if (isFromPayNow) {
      add(const RepaymentMethodSelectionEvent.fetchContracts());
    }
    add(const RepaymentMethodSelectionEvent.initRepaymentBanner());
  }

  Future<void> _initRepaymentBanner(
    _InitRepaymentBanner e,
    Emitter<RepaymentMethodSelectionState> emit,
  ) async {
    if (featureFlagRepository.isEnabledCached(FeatureFlag.repaymentMainScreenBanner)) {
      final bannerId = (await featureFlagRepository.getFeatureFlagValue(FeatureFlag.repaymentMainScreenBanner))
              .fold((_) => null, (r) => r) ??
          '';

      if (bannerId.isNotEmpty) {
        emit(
          state.copyWith(
            bannerId: bannerId,
          ),
        );
      }
    }
  }

  Future<void> _fetchContracts(
    _FetchContracts e,
    Emitter<RepaymentMethodSelectionState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading));

    final apiResponse = await repaymentRepository.getContracts();
    logger.d('getContracts response$apiResponse');
    final response = apiResponse.map(_handleContractsDataResponse);

    emit(
      await response.fold((l) {
        return state.copyWith(loadingState: LoadingState.isCompleted, failureOrSuccessContracts: optionOf(response));
      }, (r) async {
        final selectedContract = state.selectedContract;
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessContracts: optionOf(response),
          selectedContract: selectedContract != null
              ? getFullRepaymentContract(contracts: r, selectedContract: selectedContract)
              : null,
          contracts: r,
        );
      }),
    );
  }

  List<RepaymentContract> _handleContractsDataResponse(RepaymentContractListResponse r) {
    final contracts = <RepaymentContract>[];
    if (r.loanContracts != null && r.loanContracts!.isNotEmpty) {
      contracts.addAll(
        r.loanContracts!.map((e) {
          return RepaymentContract(
            contractType: RepaymentContractTypeHelper.parse(e.contractType),
            loanContract: e,
          );
        }).toList(),
      );
    }
    if (r.relContracts != null && r.relContracts!.isNotEmpty) {
      contracts.addAll(
        r.relContracts!.map((e) {
          return RepaymentContract(
            contractType: RepaymentContractTypeHelper.parse(e.contractType),
            relContract: e,
          );
        }).toList(),
      );
    }
    contracts.sort((a, b) {
      if (a.dueDate != null && b.dueDate != null) {
        return a.dueDate!.compareTo(b.dueDate!);
      }

      return 0;
    });

    return contracts;
  }

  bool isRelContractPayable(RepaymentRelContract contract) {
    return (contract.minimumDueAmount ?? Decimal.zero) >= minimumAmount ||
        (contract.totalAmountDue ?? Decimal.zero) >= minimumAmount;
  }

  RepaymentContract? getFullRepaymentContract({
    required RepaymentContract selectedContract,
    required List<RepaymentContract> contracts,
  }) {
    return contracts.firstWhereOrNull((e) => e.contractNumber == selectedContract.contractNumber);
  }
}
