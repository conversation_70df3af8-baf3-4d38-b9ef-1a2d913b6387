part of 'repayment_ptp_payment_plan_bloc.dart';

@freezed
class RepaymentPtpPaymentPlanState with _$RepaymentPtpPaymentPlanState {
  const factory RepaymentPtpPaymentPlanState({
    @Default(false) bool isError,
    required LoadingState loadingState,
    Decimal? dueAmount,
    Decimal? minimumAmount,
    @Default(false) bool shouldFocusCustomAmount,
    @Default(false) bool isInputValid,
    Decimal? selectedAmount,
    @Default(null) RepaymentPtpAmountOptionType? selectedAmountOption,
    required RepaymentPtpContract? ptpContract,
    DateTime? dueDate,
    DateTime? extensionDate,
    @Default(<int>[]) List<int> extensionDayNumbers,
    int? selectedExtensionDayNumber,
    required Option<Either<RepaymentFailure, RepaymentPtpEvaluation>> failureOrSuccessSubmitPtpEvaluation,
    RepaymentPtpEvaluation? submitPtpEvaluationResult,
  }) = _RepaymentPtpPaymentPlanState;

  factory RepaymentPtpPaymentPlanState.initialize() => RepaymentPtpPaymentPlanState(
        loadingState: LoadingState.isInitial,
        minimumAmount: Decimal.fromInt(0),
        ptpContract: null,
        failureOrSuccessSubmitPtpEvaluation: none(),
      );
}
