part of 'repayment_ptp_payment_plan_bloc.dart';

@freezed
class RepaymentPtpPaymentPlanEvent with _$RepaymentPtpPaymentPlanEvent {
  const factory RepaymentPtpPaymentPlanEvent.initialize({
    required RepaymentPtpContract ptpContract,
  }) = _Initialize;
  const factory RepaymentPtpPaymentPlanEvent.selectAmountOption({
    required RepaymentPtpAmountOptionType selectAmountOptionType,
  }) = _SelectAmountOption;

  // Enter amount
  const factory RepaymentPtpPaymentPlanEvent.setAmount(Decimal? amount) = _SetAmount;
  const factory RepaymentPtpPaymentPlanEvent.resetFocusCustomAmount() = _ResetFocusCustomAmount;

  // Select date
  const factory RepaymentPtpPaymentPlanEvent.selectExtensionDayNumber({required int? number}) =
      _SelectExtensionDayNumber;

  // Submit
  const factory RepaymentPtpPaymentPlanEvent.resetState() = _ResetState;
  const factory RepaymentPtpPaymentPlanEvent.submitPtpEvaluation({required RepaymentPtpEvaluationRequest request}) =
      _SubmitPtpEvaluation;
}
