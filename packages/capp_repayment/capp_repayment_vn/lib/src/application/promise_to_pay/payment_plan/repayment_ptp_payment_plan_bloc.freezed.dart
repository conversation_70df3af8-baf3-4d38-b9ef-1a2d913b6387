// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_ptp_payment_plan_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPtpPaymentPlanEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpPaymentPlanEventCopyWith<$Res> {
  factory $RepaymentPtpPaymentPlanEventCopyWith(
          RepaymentPtpPaymentPlanEvent value,
          $Res Function(RepaymentPtpPaymentPlanEvent) then) =
      _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res,
          RepaymentPtpPaymentPlanEvent>;
}

/// @nodoc
class _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res,
        $Val extends RepaymentPtpPaymentPlanEvent>
    implements $RepaymentPtpPaymentPlanEventCopyWith<$Res> {
  _$RepaymentPtpPaymentPlanEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentPtpContract ptpContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ptpContract = null,
  }) {
    return _then(_$_Initialize(
      ptpContract: null == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.ptpContract});

  @override
  final RepaymentPtpContract ptpContract;

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanEvent.initialize(ptpContract: $ptpContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ptpContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) {
    return initialize(ptpContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) {
    return initialize?.call(ptpContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(ptpContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPtpPaymentPlanEvent {
  const factory _Initialize({required final RepaymentPtpContract ptpContract}) =
      _$_Initialize;

  RepaymentPtpContract get ptpContract;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectAmountOptionCopyWith<$Res> {
  factory _$$_SelectAmountOptionCopyWith(_$_SelectAmountOption value,
          $Res Function(_$_SelectAmountOption) then) =
      __$$_SelectAmountOptionCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentPtpAmountOptionType selectAmountOptionType});
}

/// @nodoc
class __$$_SelectAmountOptionCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res,
        _$_SelectAmountOption> implements _$$_SelectAmountOptionCopyWith<$Res> {
  __$$_SelectAmountOptionCopyWithImpl(
      _$_SelectAmountOption _value, $Res Function(_$_SelectAmountOption) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectAmountOptionType = null,
  }) {
    return _then(_$_SelectAmountOption(
      selectAmountOptionType: null == selectAmountOptionType
          ? _value.selectAmountOptionType
          : selectAmountOptionType // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpAmountOptionType,
    ));
  }
}

/// @nodoc

class _$_SelectAmountOption implements _SelectAmountOption {
  const _$_SelectAmountOption({required this.selectAmountOptionType});

  @override
  final RepaymentPtpAmountOptionType selectAmountOptionType;

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanEvent.selectAmountOption(selectAmountOptionType: $selectAmountOptionType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectAmountOption &&
            (identical(other.selectAmountOptionType, selectAmountOptionType) ||
                other.selectAmountOptionType == selectAmountOptionType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectAmountOptionType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectAmountOptionCopyWith<_$_SelectAmountOption> get copyWith =>
      __$$_SelectAmountOptionCopyWithImpl<_$_SelectAmountOption>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) {
    return selectAmountOption(selectAmountOptionType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) {
    return selectAmountOption?.call(selectAmountOptionType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (selectAmountOption != null) {
      return selectAmountOption(selectAmountOptionType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) {
    return selectAmountOption(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) {
    return selectAmountOption?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (selectAmountOption != null) {
      return selectAmountOption(this);
    }
    return orElse();
  }
}

abstract class _SelectAmountOption implements RepaymentPtpPaymentPlanEvent {
  const factory _SelectAmountOption(
      {required final RepaymentPtpAmountOptionType
          selectAmountOptionType}) = _$_SelectAmountOption;

  RepaymentPtpAmountOptionType get selectAmountOptionType;
  @JsonKey(ignore: true)
  _$$_SelectAmountOptionCopyWith<_$_SelectAmountOption> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetAmountCopyWith<$Res> {
  factory _$$_SetAmountCopyWith(
          _$_SetAmount value, $Res Function(_$_SetAmount) then) =
      __$$_SetAmountCopyWithImpl<$Res>;
  @useResult
  $Res call({Decimal? amount});
}

/// @nodoc
class __$$_SetAmountCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res, _$_SetAmount>
    implements _$$_SetAmountCopyWith<$Res> {
  __$$_SetAmountCopyWithImpl(
      _$_SetAmount _value, $Res Function(_$_SetAmount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
  }) {
    return _then(_$_SetAmount(
      freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
    ));
  }
}

/// @nodoc

class _$_SetAmount implements _SetAmount {
  const _$_SetAmount(this.amount);

  @override
  final Decimal? amount;

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanEvent.setAmount(amount: $amount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetAmount &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      __$$_SetAmountCopyWithImpl<_$_SetAmount>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) {
    return setAmount(amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) {
    return setAmount?.call(amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) {
    return setAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) {
    return setAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(this);
    }
    return orElse();
  }
}

abstract class _SetAmount implements RepaymentPtpPaymentPlanEvent {
  const factory _SetAmount(final Decimal? amount) = _$_SetAmount;

  Decimal? get amount;
  @JsonKey(ignore: true)
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetFocusCustomAmountCopyWith<$Res> {
  factory _$$_ResetFocusCustomAmountCopyWith(_$_ResetFocusCustomAmount value,
          $Res Function(_$_ResetFocusCustomAmount) then) =
      __$$_ResetFocusCustomAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetFocusCustomAmountCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res,
        _$_ResetFocusCustomAmount>
    implements _$$_ResetFocusCustomAmountCopyWith<$Res> {
  __$$_ResetFocusCustomAmountCopyWithImpl(_$_ResetFocusCustomAmount _value,
      $Res Function(_$_ResetFocusCustomAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetFocusCustomAmount implements _ResetFocusCustomAmount {
  const _$_ResetFocusCustomAmount();

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanEvent.resetFocusCustomAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ResetFocusCustomAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) {
    return resetFocusCustomAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) {
    return resetFocusCustomAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (resetFocusCustomAmount != null) {
      return resetFocusCustomAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) {
    return resetFocusCustomAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) {
    return resetFocusCustomAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (resetFocusCustomAmount != null) {
      return resetFocusCustomAmount(this);
    }
    return orElse();
  }
}

abstract class _ResetFocusCustomAmount implements RepaymentPtpPaymentPlanEvent {
  const factory _ResetFocusCustomAmount() = _$_ResetFocusCustomAmount;
}

/// @nodoc
abstract class _$$_SelectExtensionDayNumberCopyWith<$Res> {
  factory _$$_SelectExtensionDayNumberCopyWith(
          _$_SelectExtensionDayNumber value,
          $Res Function(_$_SelectExtensionDayNumber) then) =
      __$$_SelectExtensionDayNumberCopyWithImpl<$Res>;
  @useResult
  $Res call({int? number});
}

/// @nodoc
class __$$_SelectExtensionDayNumberCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res,
        _$_SelectExtensionDayNumber>
    implements _$$_SelectExtensionDayNumberCopyWith<$Res> {
  __$$_SelectExtensionDayNumberCopyWithImpl(_$_SelectExtensionDayNumber _value,
      $Res Function(_$_SelectExtensionDayNumber) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? number = freezed,
  }) {
    return _then(_$_SelectExtensionDayNumber(
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$_SelectExtensionDayNumber implements _SelectExtensionDayNumber {
  const _$_SelectExtensionDayNumber({required this.number});

  @override
  final int? number;

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanEvent.selectExtensionDayNumber(number: $number)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectExtensionDayNumber &&
            (identical(other.number, number) || other.number == number));
  }

  @override
  int get hashCode => Object.hash(runtimeType, number);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectExtensionDayNumberCopyWith<_$_SelectExtensionDayNumber>
      get copyWith => __$$_SelectExtensionDayNumberCopyWithImpl<
          _$_SelectExtensionDayNumber>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) {
    return selectExtensionDayNumber(number);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) {
    return selectExtensionDayNumber?.call(number);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (selectExtensionDayNumber != null) {
      return selectExtensionDayNumber(number);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) {
    return selectExtensionDayNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) {
    return selectExtensionDayNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (selectExtensionDayNumber != null) {
      return selectExtensionDayNumber(this);
    }
    return orElse();
  }
}

abstract class _SelectExtensionDayNumber
    implements RepaymentPtpPaymentPlanEvent {
  const factory _SelectExtensionDayNumber({required final int? number}) =
      _$_SelectExtensionDayNumber;

  int? get number;
  @JsonKey(ignore: true)
  _$$_SelectExtensionDayNumberCopyWith<_$_SelectExtensionDayNumber>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetStateCopyWith<$Res> {
  factory _$$_ResetStateCopyWith(
          _$_ResetState value, $Res Function(_$_ResetState) then) =
      __$$_ResetStateCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetStateCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res, _$_ResetState>
    implements _$$_ResetStateCopyWith<$Res> {
  __$$_ResetStateCopyWithImpl(
      _$_ResetState _value, $Res Function(_$_ResetState) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetState implements _ResetState {
  const _$_ResetState();

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanEvent.resetState()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) {
    return resetState();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) {
    return resetState?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) {
    return resetState(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) {
    return resetState?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (resetState != null) {
      return resetState(this);
    }
    return orElse();
  }
}

abstract class _ResetState implements RepaymentPtpPaymentPlanEvent {
  const factory _ResetState() = _$_ResetState;
}

/// @nodoc
abstract class _$$_SubmitPtpEvaluationCopyWith<$Res> {
  factory _$$_SubmitPtpEvaluationCopyWith(_$_SubmitPtpEvaluation value,
          $Res Function(_$_SubmitPtpEvaluation) then) =
      __$$_SubmitPtpEvaluationCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentPtpEvaluationRequest request});
}

/// @nodoc
class __$$_SubmitPtpEvaluationCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanEventCopyWithImpl<$Res,
        _$_SubmitPtpEvaluation>
    implements _$$_SubmitPtpEvaluationCopyWith<$Res> {
  __$$_SubmitPtpEvaluationCopyWithImpl(_$_SubmitPtpEvaluation _value,
      $Res Function(_$_SubmitPtpEvaluation) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$_SubmitPtpEvaluation(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEvaluationRequest,
    ));
  }
}

/// @nodoc

class _$_SubmitPtpEvaluation implements _SubmitPtpEvaluation {
  const _$_SubmitPtpEvaluation({required this.request});

  @override
  final RepaymentPtpEvaluationRequest request;

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanEvent.submitPtpEvaluation(request: $request)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubmitPtpEvaluation &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubmitPtpEvaluationCopyWith<_$_SubmitPtpEvaluation> get copyWith =>
      __$$_SubmitPtpEvaluationCopyWithImpl<_$_SubmitPtpEvaluation>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
    required TResult Function(
            RepaymentPtpAmountOptionType selectAmountOptionType)
        selectAmountOption,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function() resetFocusCustomAmount,
    required TResult Function(int? number) selectExtensionDayNumber,
    required TResult Function() resetState,
    required TResult Function(RepaymentPtpEvaluationRequest request)
        submitPtpEvaluation,
  }) {
    return submitPtpEvaluation(request);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
    TResult? Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function()? resetFocusCustomAmount,
    TResult? Function(int? number)? selectExtensionDayNumber,
    TResult? Function()? resetState,
    TResult? Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
  }) {
    return submitPtpEvaluation?.call(request);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    TResult Function(RepaymentPtpAmountOptionType selectAmountOptionType)?
        selectAmountOption,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function()? resetFocusCustomAmount,
    TResult Function(int? number)? selectExtensionDayNumber,
    TResult Function()? resetState,
    TResult Function(RepaymentPtpEvaluationRequest request)?
        submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (submitPtpEvaluation != null) {
      return submitPtpEvaluation(request);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectAmountOption value) selectAmountOption,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_ResetFocusCustomAmount value)
        resetFocusCustomAmount,
    required TResult Function(_SelectExtensionDayNumber value)
        selectExtensionDayNumber,
    required TResult Function(_ResetState value) resetState,
    required TResult Function(_SubmitPtpEvaluation value) submitPtpEvaluation,
  }) {
    return submitPtpEvaluation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectAmountOption value)? selectAmountOption,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult? Function(_SelectExtensionDayNumber value)?
        selectExtensionDayNumber,
    TResult? Function(_ResetState value)? resetState,
    TResult? Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
  }) {
    return submitPtpEvaluation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectAmountOption value)? selectAmountOption,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_ResetFocusCustomAmount value)? resetFocusCustomAmount,
    TResult Function(_SelectExtensionDayNumber value)? selectExtensionDayNumber,
    TResult Function(_ResetState value)? resetState,
    TResult Function(_SubmitPtpEvaluation value)? submitPtpEvaluation,
    required TResult orElse(),
  }) {
    if (submitPtpEvaluation != null) {
      return submitPtpEvaluation(this);
    }
    return orElse();
  }
}

abstract class _SubmitPtpEvaluation implements RepaymentPtpPaymentPlanEvent {
  const factory _SubmitPtpEvaluation(
          {required final RepaymentPtpEvaluationRequest request}) =
      _$_SubmitPtpEvaluation;

  RepaymentPtpEvaluationRequest get request;
  @JsonKey(ignore: true)
  _$$_SubmitPtpEvaluationCopyWith<_$_SubmitPtpEvaluation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentPtpPaymentPlanState {
  bool get isError => throw _privateConstructorUsedError;
  LoadingState get loadingState => throw _privateConstructorUsedError;
  Decimal? get dueAmount => throw _privateConstructorUsedError;
  Decimal? get minimumAmount => throw _privateConstructorUsedError;
  bool get shouldFocusCustomAmount => throw _privateConstructorUsedError;
  bool get isInputValid => throw _privateConstructorUsedError;
  Decimal? get selectedAmount => throw _privateConstructorUsedError;
  RepaymentPtpAmountOptionType? get selectedAmountOption =>
      throw _privateConstructorUsedError;
  RepaymentPtpContract? get ptpContract => throw _privateConstructorUsedError;
  DateTime? get dueDate => throw _privateConstructorUsedError;
  DateTime? get extensionDate => throw _privateConstructorUsedError;
  List<int> get extensionDayNumbers => throw _privateConstructorUsedError;
  int? get selectedExtensionDayNumber => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
      get failureOrSuccessSubmitPtpEvaluation =>
          throw _privateConstructorUsedError;
  RepaymentPtpEvaluation? get submitPtpEvaluationResult =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPtpPaymentPlanStateCopyWith<RepaymentPtpPaymentPlanState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpPaymentPlanStateCopyWith<$Res> {
  factory $RepaymentPtpPaymentPlanStateCopyWith(
          RepaymentPtpPaymentPlanState value,
          $Res Function(RepaymentPtpPaymentPlanState) then) =
      _$RepaymentPtpPaymentPlanStateCopyWithImpl<$Res,
          RepaymentPtpPaymentPlanState>;
  @useResult
  $Res call(
      {bool isError,
      LoadingState loadingState,
      Decimal? dueAmount,
      Decimal? minimumAmount,
      bool shouldFocusCustomAmount,
      bool isInputValid,
      Decimal? selectedAmount,
      RepaymentPtpAmountOptionType? selectedAmountOption,
      RepaymentPtpContract? ptpContract,
      DateTime? dueDate,
      DateTime? extensionDate,
      List<int> extensionDayNumbers,
      int? selectedExtensionDayNumber,
      Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
          failureOrSuccessSubmitPtpEvaluation,
      RepaymentPtpEvaluation? submitPtpEvaluationResult});
}

/// @nodoc
class _$RepaymentPtpPaymentPlanStateCopyWithImpl<$Res,
        $Val extends RepaymentPtpPaymentPlanState>
    implements $RepaymentPtpPaymentPlanStateCopyWith<$Res> {
  _$RepaymentPtpPaymentPlanStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isError = null,
    Object? loadingState = null,
    Object? dueAmount = freezed,
    Object? minimumAmount = freezed,
    Object? shouldFocusCustomAmount = null,
    Object? isInputValid = null,
    Object? selectedAmount = freezed,
    Object? selectedAmountOption = freezed,
    Object? ptpContract = freezed,
    Object? dueDate = freezed,
    Object? extensionDate = freezed,
    Object? extensionDayNumbers = null,
    Object? selectedExtensionDayNumber = freezed,
    Object? failureOrSuccessSubmitPtpEvaluation = null,
    Object? submitPtpEvaluationResult = freezed,
  }) {
    return _then(_value.copyWith(
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      minimumAmount: freezed == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      shouldFocusCustomAmount: null == shouldFocusCustomAmount
          ? _value.shouldFocusCustomAmount
          : shouldFocusCustomAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isInputValid: null == isInputValid
          ? _value.isInputValid
          : isInputValid // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedAmountOption: freezed == selectedAmountOption
          ? _value.selectedAmountOption
          : selectedAmountOption // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpAmountOptionType?,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      extensionDate: freezed == extensionDate
          ? _value.extensionDate
          : extensionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      extensionDayNumbers: null == extensionDayNumbers
          ? _value.extensionDayNumbers
          : extensionDayNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>,
      selectedExtensionDayNumber: freezed == selectedExtensionDayNumber
          ? _value.selectedExtensionDayNumber
          : selectedExtensionDayNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      failureOrSuccessSubmitPtpEvaluation: null ==
              failureOrSuccessSubmitPtpEvaluation
          ? _value.failureOrSuccessSubmitPtpEvaluation
          : failureOrSuccessSubmitPtpEvaluation // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>,
      submitPtpEvaluationResult: freezed == submitPtpEvaluationResult
          ? _value.submitPtpEvaluationResult
          : submitPtpEvaluationResult // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEvaluation?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPtpPaymentPlanStateCopyWith<$Res>
    implements $RepaymentPtpPaymentPlanStateCopyWith<$Res> {
  factory _$$_RepaymentPtpPaymentPlanStateCopyWith(
          _$_RepaymentPtpPaymentPlanState value,
          $Res Function(_$_RepaymentPtpPaymentPlanState) then) =
      __$$_RepaymentPtpPaymentPlanStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isError,
      LoadingState loadingState,
      Decimal? dueAmount,
      Decimal? minimumAmount,
      bool shouldFocusCustomAmount,
      bool isInputValid,
      Decimal? selectedAmount,
      RepaymentPtpAmountOptionType? selectedAmountOption,
      RepaymentPtpContract? ptpContract,
      DateTime? dueDate,
      DateTime? extensionDate,
      List<int> extensionDayNumbers,
      int? selectedExtensionDayNumber,
      Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
          failureOrSuccessSubmitPtpEvaluation,
      RepaymentPtpEvaluation? submitPtpEvaluationResult});
}

/// @nodoc
class __$$_RepaymentPtpPaymentPlanStateCopyWithImpl<$Res>
    extends _$RepaymentPtpPaymentPlanStateCopyWithImpl<$Res,
        _$_RepaymentPtpPaymentPlanState>
    implements _$$_RepaymentPtpPaymentPlanStateCopyWith<$Res> {
  __$$_RepaymentPtpPaymentPlanStateCopyWithImpl(
      _$_RepaymentPtpPaymentPlanState _value,
      $Res Function(_$_RepaymentPtpPaymentPlanState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isError = null,
    Object? loadingState = null,
    Object? dueAmount = freezed,
    Object? minimumAmount = freezed,
    Object? shouldFocusCustomAmount = null,
    Object? isInputValid = null,
    Object? selectedAmount = freezed,
    Object? selectedAmountOption = freezed,
    Object? ptpContract = freezed,
    Object? dueDate = freezed,
    Object? extensionDate = freezed,
    Object? extensionDayNumbers = null,
    Object? selectedExtensionDayNumber = freezed,
    Object? failureOrSuccessSubmitPtpEvaluation = null,
    Object? submitPtpEvaluationResult = freezed,
  }) {
    return _then(_$_RepaymentPtpPaymentPlanState(
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      minimumAmount: freezed == minimumAmount
          ? _value.minimumAmount
          : minimumAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      shouldFocusCustomAmount: null == shouldFocusCustomAmount
          ? _value.shouldFocusCustomAmount
          : shouldFocusCustomAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isInputValid: null == isInputValid
          ? _value.isInputValid
          : isInputValid // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedAmountOption: freezed == selectedAmountOption
          ? _value.selectedAmountOption
          : selectedAmountOption // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpAmountOptionType?,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      dueDate: freezed == dueDate
          ? _value.dueDate
          : dueDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      extensionDate: freezed == extensionDate
          ? _value.extensionDate
          : extensionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      extensionDayNumbers: null == extensionDayNumbers
          ? _value._extensionDayNumbers
          : extensionDayNumbers // ignore: cast_nullable_to_non_nullable
              as List<int>,
      selectedExtensionDayNumber: freezed == selectedExtensionDayNumber
          ? _value.selectedExtensionDayNumber
          : selectedExtensionDayNumber // ignore: cast_nullable_to_non_nullable
              as int?,
      failureOrSuccessSubmitPtpEvaluation: null ==
              failureOrSuccessSubmitPtpEvaluation
          ? _value.failureOrSuccessSubmitPtpEvaluation
          : failureOrSuccessSubmitPtpEvaluation // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>,
      submitPtpEvaluationResult: freezed == submitPtpEvaluationResult
          ? _value.submitPtpEvaluationResult
          : submitPtpEvaluationResult // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEvaluation?,
    ));
  }
}

/// @nodoc

class _$_RepaymentPtpPaymentPlanState implements _RepaymentPtpPaymentPlanState {
  const _$_RepaymentPtpPaymentPlanState(
      {this.isError = false,
      required this.loadingState,
      this.dueAmount,
      this.minimumAmount,
      this.shouldFocusCustomAmount = false,
      this.isInputValid = false,
      this.selectedAmount,
      this.selectedAmountOption = null,
      required this.ptpContract,
      this.dueDate,
      this.extensionDate,
      final List<int> extensionDayNumbers = const <int>[],
      this.selectedExtensionDayNumber,
      required this.failureOrSuccessSubmitPtpEvaluation,
      this.submitPtpEvaluationResult})
      : _extensionDayNumbers = extensionDayNumbers;

  @override
  @JsonKey()
  final bool isError;
  @override
  final LoadingState loadingState;
  @override
  final Decimal? dueAmount;
  @override
  final Decimal? minimumAmount;
  @override
  @JsonKey()
  final bool shouldFocusCustomAmount;
  @override
  @JsonKey()
  final bool isInputValid;
  @override
  final Decimal? selectedAmount;
  @override
  @JsonKey()
  final RepaymentPtpAmountOptionType? selectedAmountOption;
  @override
  final RepaymentPtpContract? ptpContract;
  @override
  final DateTime? dueDate;
  @override
  final DateTime? extensionDate;
  final List<int> _extensionDayNumbers;
  @override
  @JsonKey()
  List<int> get extensionDayNumbers {
    if (_extensionDayNumbers is EqualUnmodifiableListView)
      return _extensionDayNumbers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_extensionDayNumbers);
  }

  @override
  final int? selectedExtensionDayNumber;
  @override
  final Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
      failureOrSuccessSubmitPtpEvaluation;
  @override
  final RepaymentPtpEvaluation? submitPtpEvaluationResult;

  @override
  String toString() {
    return 'RepaymentPtpPaymentPlanState(isError: $isError, loadingState: $loadingState, dueAmount: $dueAmount, minimumAmount: $minimumAmount, shouldFocusCustomAmount: $shouldFocusCustomAmount, isInputValid: $isInputValid, selectedAmount: $selectedAmount, selectedAmountOption: $selectedAmountOption, ptpContract: $ptpContract, dueDate: $dueDate, extensionDate: $extensionDate, extensionDayNumbers: $extensionDayNumbers, selectedExtensionDayNumber: $selectedExtensionDayNumber, failureOrSuccessSubmitPtpEvaluation: $failureOrSuccessSubmitPtpEvaluation, submitPtpEvaluationResult: $submitPtpEvaluationResult)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPtpPaymentPlanState &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.dueAmount, dueAmount) ||
                other.dueAmount == dueAmount) &&
            (identical(other.minimumAmount, minimumAmount) ||
                other.minimumAmount == minimumAmount) &&
            (identical(
                    other.shouldFocusCustomAmount, shouldFocusCustomAmount) ||
                other.shouldFocusCustomAmount == shouldFocusCustomAmount) &&
            (identical(other.isInputValid, isInputValid) ||
                other.isInputValid == isInputValid) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.selectedAmountOption, selectedAmountOption) ||
                other.selectedAmountOption == selectedAmountOption) &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract) &&
            (identical(other.dueDate, dueDate) || other.dueDate == dueDate) &&
            (identical(other.extensionDate, extensionDate) ||
                other.extensionDate == extensionDate) &&
            const DeepCollectionEquality()
                .equals(other._extensionDayNumbers, _extensionDayNumbers) &&
            (identical(other.selectedExtensionDayNumber,
                    selectedExtensionDayNumber) ||
                other.selectedExtensionDayNumber ==
                    selectedExtensionDayNumber) &&
            (identical(other.failureOrSuccessSubmitPtpEvaluation,
                    failureOrSuccessSubmitPtpEvaluation) ||
                other.failureOrSuccessSubmitPtpEvaluation ==
                    failureOrSuccessSubmitPtpEvaluation) &&
            (identical(other.submitPtpEvaluationResult,
                    submitPtpEvaluationResult) ||
                other.submitPtpEvaluationResult == submitPtpEvaluationResult));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isError,
      loadingState,
      dueAmount,
      minimumAmount,
      shouldFocusCustomAmount,
      isInputValid,
      selectedAmount,
      selectedAmountOption,
      ptpContract,
      dueDate,
      extensionDate,
      const DeepCollectionEquality().hash(_extensionDayNumbers),
      selectedExtensionDayNumber,
      failureOrSuccessSubmitPtpEvaluation,
      submitPtpEvaluationResult);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPtpPaymentPlanStateCopyWith<_$_RepaymentPtpPaymentPlanState>
      get copyWith => __$$_RepaymentPtpPaymentPlanStateCopyWithImpl<
          _$_RepaymentPtpPaymentPlanState>(this, _$identity);
}

abstract class _RepaymentPtpPaymentPlanState
    implements RepaymentPtpPaymentPlanState {
  const factory _RepaymentPtpPaymentPlanState(
      {final bool isError,
      required final LoadingState loadingState,
      final Decimal? dueAmount,
      final Decimal? minimumAmount,
      final bool shouldFocusCustomAmount,
      final bool isInputValid,
      final Decimal? selectedAmount,
      final RepaymentPtpAmountOptionType? selectedAmountOption,
      required final RepaymentPtpContract? ptpContract,
      final DateTime? dueDate,
      final DateTime? extensionDate,
      final List<int> extensionDayNumbers,
      final int? selectedExtensionDayNumber,
      required final Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
          failureOrSuccessSubmitPtpEvaluation,
      final RepaymentPtpEvaluation?
          submitPtpEvaluationResult}) = _$_RepaymentPtpPaymentPlanState;

  @override
  bool get isError;
  @override
  LoadingState get loadingState;
  @override
  Decimal? get dueAmount;
  @override
  Decimal? get minimumAmount;
  @override
  bool get shouldFocusCustomAmount;
  @override
  bool get isInputValid;
  @override
  Decimal? get selectedAmount;
  @override
  RepaymentPtpAmountOptionType? get selectedAmountOption;
  @override
  RepaymentPtpContract? get ptpContract;
  @override
  DateTime? get dueDate;
  @override
  DateTime? get extensionDate;
  @override
  List<int> get extensionDayNumbers;
  @override
  int? get selectedExtensionDayNumber;
  @override
  Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
      get failureOrSuccessSubmitPtpEvaluation;
  @override
  RepaymentPtpEvaluation? get submitPtpEvaluationResult;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPtpPaymentPlanStateCopyWith<_$_RepaymentPtpPaymentPlanState>
      get copyWith => throw _privateConstructorUsedError;
}
