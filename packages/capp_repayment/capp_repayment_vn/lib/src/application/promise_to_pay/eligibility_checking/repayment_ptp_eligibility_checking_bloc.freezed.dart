// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_ptp_eligibility_checking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPtpEligibilityCheckingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber) initialize,
    required TResult Function() checkPtpEligibility,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber)? initialize,
    TResult? Function()? checkPtpEligibility,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber)? initialize,
    TResult Function()? checkPtpEligibility,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckPtpEligibility value) checkPtpEligibility,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckPtpEligibility value)? checkPtpEligibility,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckPtpEligibility value)? checkPtpEligibility,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpEligibilityCheckingEventCopyWith<$Res> {
  factory $RepaymentPtpEligibilityCheckingEventCopyWith(
          RepaymentPtpEligibilityCheckingEvent value,
          $Res Function(RepaymentPtpEligibilityCheckingEvent) then) =
      _$RepaymentPtpEligibilityCheckingEventCopyWithImpl<$Res,
          RepaymentPtpEligibilityCheckingEvent>;
}

/// @nodoc
class _$RepaymentPtpEligibilityCheckingEventCopyWithImpl<$Res,
        $Val extends RepaymentPtpEligibilityCheckingEvent>
    implements $RepaymentPtpEligibilityCheckingEventCopyWith<$Res> {
  _$RepaymentPtpEligibilityCheckingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({String contractNumber});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPtpEligibilityCheckingEventCopyWithImpl<$Res,
        _$_Initialize> implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
  }) {
    return _then(_$_Initialize(
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.contractNumber});

  @override
  final String contractNumber;

  @override
  String toString() {
    return 'RepaymentPtpEligibilityCheckingEvent.initialize(contractNumber: $contractNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber) initialize,
    required TResult Function() checkPtpEligibility,
  }) {
    return initialize(contractNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber)? initialize,
    TResult? Function()? checkPtpEligibility,
  }) {
    return initialize?.call(contractNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber)? initialize,
    TResult Function()? checkPtpEligibility,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(contractNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckPtpEligibility value) checkPtpEligibility,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckPtpEligibility value)? checkPtpEligibility,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckPtpEligibility value)? checkPtpEligibility,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPtpEligibilityCheckingEvent {
  const factory _Initialize({required final String contractNumber}) =
      _$_Initialize;

  String get contractNumber;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_CheckPtpEligibilityCopyWith<$Res> {
  factory _$$_CheckPtpEligibilityCopyWith(_$_CheckPtpEligibility value,
          $Res Function(_$_CheckPtpEligibility) then) =
      __$$_CheckPtpEligibilityCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_CheckPtpEligibilityCopyWithImpl<$Res>
    extends _$RepaymentPtpEligibilityCheckingEventCopyWithImpl<$Res,
        _$_CheckPtpEligibility>
    implements _$$_CheckPtpEligibilityCopyWith<$Res> {
  __$$_CheckPtpEligibilityCopyWithImpl(_$_CheckPtpEligibility _value,
      $Res Function(_$_CheckPtpEligibility) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_CheckPtpEligibility implements _CheckPtpEligibility {
  const _$_CheckPtpEligibility();

  @override
  String toString() {
    return 'RepaymentPtpEligibilityCheckingEvent.checkPtpEligibility()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_CheckPtpEligibility);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber) initialize,
    required TResult Function() checkPtpEligibility,
  }) {
    return checkPtpEligibility();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber)? initialize,
    TResult? Function()? checkPtpEligibility,
  }) {
    return checkPtpEligibility?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber)? initialize,
    TResult Function()? checkPtpEligibility,
    required TResult orElse(),
  }) {
    if (checkPtpEligibility != null) {
      return checkPtpEligibility();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckPtpEligibility value) checkPtpEligibility,
  }) {
    return checkPtpEligibility(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckPtpEligibility value)? checkPtpEligibility,
  }) {
    return checkPtpEligibility?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckPtpEligibility value)? checkPtpEligibility,
    required TResult orElse(),
  }) {
    if (checkPtpEligibility != null) {
      return checkPtpEligibility(this);
    }
    return orElse();
  }
}

abstract class _CheckPtpEligibility
    implements RepaymentPtpEligibilityCheckingEvent {
  const factory _CheckPtpEligibility() = _$_CheckPtpEligibility;
}

/// @nodoc
mixin _$RepaymentPtpEligibilityCheckingState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentPtpContract>> get failureOrSuccess =>
      throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  RepaymentPtpContract? get ptpContract => throw _privateConstructorUsedError;
  RepaymentPtpEligibilityCheckingRedirect? get redirect =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPtpEligibilityCheckingStateCopyWith<
          RepaymentPtpEligibilityCheckingState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpEligibilityCheckingStateCopyWith<$Res> {
  factory $RepaymentPtpEligibilityCheckingStateCopyWith(
          RepaymentPtpEligibilityCheckingState value,
          $Res Function(RepaymentPtpEligibilityCheckingState) then) =
      _$RepaymentPtpEligibilityCheckingStateCopyWithImpl<$Res,
          RepaymentPtpEligibilityCheckingState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      Option<Either<RepaymentFailure, RepaymentPtpContract>> failureOrSuccess,
      bool? isError,
      String? contractNumber,
      RepaymentPtpContract? ptpContract,
      RepaymentPtpEligibilityCheckingRedirect? redirect});
}

/// @nodoc
class _$RepaymentPtpEligibilityCheckingStateCopyWithImpl<$Res,
        $Val extends RepaymentPtpEligibilityCheckingState>
    implements $RepaymentPtpEligibilityCheckingStateCopyWith<$Res> {
  _$RepaymentPtpEligibilityCheckingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? failureOrSuccess = null,
    Object? isError = freezed,
    Object? contractNumber = freezed,
    Object? ptpContract = freezed,
    Object? redirect = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentPtpContract>>,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      redirect: freezed == redirect
          ? _value.redirect
          : redirect // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEligibilityCheckingRedirect?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPtpEligibilityCheckingStateCopyWith<$Res>
    implements $RepaymentPtpEligibilityCheckingStateCopyWith<$Res> {
  factory _$$_RepaymentPtpEligibilityCheckingStateCopyWith(
          _$_RepaymentPtpEligibilityCheckingState value,
          $Res Function(_$_RepaymentPtpEligibilityCheckingState) then) =
      __$$_RepaymentPtpEligibilityCheckingStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      Option<Either<RepaymentFailure, RepaymentPtpContract>> failureOrSuccess,
      bool? isError,
      String? contractNumber,
      RepaymentPtpContract? ptpContract,
      RepaymentPtpEligibilityCheckingRedirect? redirect});
}

/// @nodoc
class __$$_RepaymentPtpEligibilityCheckingStateCopyWithImpl<$Res>
    extends _$RepaymentPtpEligibilityCheckingStateCopyWithImpl<$Res,
        _$_RepaymentPtpEligibilityCheckingState>
    implements _$$_RepaymentPtpEligibilityCheckingStateCopyWith<$Res> {
  __$$_RepaymentPtpEligibilityCheckingStateCopyWithImpl(
      _$_RepaymentPtpEligibilityCheckingState _value,
      $Res Function(_$_RepaymentPtpEligibilityCheckingState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? failureOrSuccess = null,
    Object? isError = freezed,
    Object? contractNumber = freezed,
    Object? ptpContract = freezed,
    Object? redirect = freezed,
  }) {
    return _then(_$_RepaymentPtpEligibilityCheckingState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentPtpContract>>,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      redirect: freezed == redirect
          ? _value.redirect
          : redirect // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEligibilityCheckingRedirect?,
    ));
  }
}

/// @nodoc

class _$_RepaymentPtpEligibilityCheckingState
    implements _RepaymentPtpEligibilityCheckingState {
  const _$_RepaymentPtpEligibilityCheckingState(
      {required this.loadingState,
      required this.failureOrSuccess,
      this.isError,
      this.contractNumber,
      this.ptpContract,
      this.redirect});

  @override
  final LoadingState loadingState;
  @override
  final Option<Either<RepaymentFailure, RepaymentPtpContract>> failureOrSuccess;
  @override
  final bool? isError;
  @override
  final String? contractNumber;
  @override
  final RepaymentPtpContract? ptpContract;
  @override
  final RepaymentPtpEligibilityCheckingRedirect? redirect;

  @override
  String toString() {
    return 'RepaymentPtpEligibilityCheckingState(loadingState: $loadingState, failureOrSuccess: $failureOrSuccess, isError: $isError, contractNumber: $contractNumber, ptpContract: $ptpContract, redirect: $redirect)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPtpEligibilityCheckingState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.failureOrSuccess, failureOrSuccess) ||
                other.failureOrSuccess == failureOrSuccess) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract) &&
            (identical(other.redirect, redirect) ||
                other.redirect == redirect));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loadingState, failureOrSuccess,
      isError, contractNumber, ptpContract, redirect);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPtpEligibilityCheckingStateCopyWith<
          _$_RepaymentPtpEligibilityCheckingState>
      get copyWith => __$$_RepaymentPtpEligibilityCheckingStateCopyWithImpl<
          _$_RepaymentPtpEligibilityCheckingState>(this, _$identity);
}

abstract class _RepaymentPtpEligibilityCheckingState
    implements RepaymentPtpEligibilityCheckingState {
  const factory _RepaymentPtpEligibilityCheckingState(
          {required final LoadingState loadingState,
          required final Option<Either<RepaymentFailure, RepaymentPtpContract>>
              failureOrSuccess,
          final bool? isError,
          final String? contractNumber,
          final RepaymentPtpContract? ptpContract,
          final RepaymentPtpEligibilityCheckingRedirect? redirect}) =
      _$_RepaymentPtpEligibilityCheckingState;

  @override
  LoadingState get loadingState;
  @override
  Option<Either<RepaymentFailure, RepaymentPtpContract>> get failureOrSuccess;
  @override
  bool? get isError;
  @override
  String? get contractNumber;
  @override
  RepaymentPtpContract? get ptpContract;
  @override
  RepaymentPtpEligibilityCheckingRedirect? get redirect;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPtpEligibilityCheckingStateCopyWith<
          _$_RepaymentPtpEligibilityCheckingState>
      get copyWith => throw _privateConstructorUsedError;
}
