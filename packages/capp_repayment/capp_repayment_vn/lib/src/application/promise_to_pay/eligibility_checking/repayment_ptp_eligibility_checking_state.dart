part of 'repayment_ptp_eligibility_checking_bloc.dart';

@freezed
class RepaymentPtpEligibilityCheckingState with _$RepaymentPtpEligibilityCheckingState {
  const factory RepaymentPtpEligibilityCheckingState({
    required LoadingState loadingState,
    required Option<Either<RepaymentFailure, RepaymentPtpContract>> failureOrSuccess,
    bool? isError,
    String? contractNumber,
    RepaymentPtpContract? ptpContract,
    RepaymentPtpEligibilityCheckingRedirect? redirect,
  }) = _RepaymentPtpEligibilityCheckingState;

  factory RepaymentPtpEligibilityCheckingState.initialize() => RepaymentPtpEligibilityCheckingState(
        loadingState: LoadingState.isInitial,
        failureOrSuccess: none(),
        isError: false,
      );
}
