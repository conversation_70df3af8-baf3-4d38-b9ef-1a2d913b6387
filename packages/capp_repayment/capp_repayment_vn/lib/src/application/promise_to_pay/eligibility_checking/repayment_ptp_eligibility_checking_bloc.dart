import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../../capp_repayment.dart';

part 'repayment_ptp_eligibility_checking_bloc.freezed.dart';
part 'repayment_ptp_eligibility_checking_event.dart';
part 'repayment_ptp_eligibility_checking_state.dart';

class RepaymentPtpEligibilityCheckingBloc
    extends Bloc<RepaymentPtpEligibilityCheckingEvent, RepaymentPtpEligibilityCheckingState> {
  final IRepaymentRepository repaymentRepository;
  final IIdentityRepository identityRepository;
  final Logger logger;

  RepaymentPtpEligibilityCheckingBloc({
    required this.repaymentRepository,
    required this.identityRepository,
    required this.logger,
  }) : super(RepaymentPtpEligibilityCheckingState.initialize()) {
    on<RepaymentPtpEligibilityCheckingEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          checkPtpEligibility: (e) => _checkPtpEligibility(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentPtpEligibilityCheckingState> emit,
  ) async {
    emit(state.copyWith(contractNumber: e.contractNumber));
    add(const RepaymentPtpEligibilityCheckingEvent.checkPtpEligibility());
  }

  Future<void> _checkPtpEligibility(
    _CheckPtpEligibility e,
    Emitter<RepaymentPtpEligibilityCheckingState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false));

    final contractNumber = state.contractNumber ?? '';
    final response = await repaymentRepository.getPtpEligibility(contractNumber);

    emit(
      response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: true,
          failureOrSuccess: optionOf(response),
        );
      }, (r) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccess: optionOf(response),
          isError: false,
          ptpContract: r,
        );
      }),
    );
    if (state.ptpContract != null) {
      final ptpContract = state.ptpContract!;
      final isPtpIntroductionShow = !(await isPtpIntroductionShownCache(contractNumber));
      final redirect = await handleRedirect(
        ptpContract: ptpContract,
        contractNumber: contractNumber,
        isPtpIntroductionShow: isPtpIntroductionShow,
      );
      emit(
        state.copyWith(
          redirect: redirect,
        ),
      );
    }
  }

  Future<RepaymentPtpEligibilityCheckingRedirect> handleRedirect({
    required RepaymentPtpContract ptpContract,
    required String contractNumber,
    required bool isPtpIntroductionShow,
  }) async {
    final ptpEligible = ptpContract.ptpEligible ?? false;

    final hasActivePTP = ptpContract.hasActivePTP ?? false;

    final lastPtpRequestStatus = ptpContract.lastPTP?.status?.toLowerCase() ?? '';
    if (ptpEligible) {
      if (isPtpIntroductionShow) {
        return RepaymentPtpEligibilityCheckingRedirect.intro;
      } else {
        return RepaymentPtpEligibilityCheckingRedirect.option;
      }
    } else {
      if (!hasActivePTP) {
        return RepaymentPtpEligibilityCheckingRedirect.failPopup;
      } else {
        if (lastPtpRequestStatus == RepaymentPtpStatus.active.name) {
          return RepaymentPtpEligibilityCheckingRedirect.success;
        } else if (lastPtpRequestStatus == RepaymentPtpStatus.processing.name) {
          return RepaymentPtpEligibilityCheckingRedirect.processing;
        } else if (lastPtpRequestStatus == RepaymentPtpStatus.rejected.name) {
          return RepaymentPtpEligibilityCheckingRedirect.failPopup;
        } else {
          if (isPtpIntroductionShow) {
            return RepaymentPtpEligibilityCheckingRedirect.intro;
          } else {
            return RepaymentPtpEligibilityCheckingRedirect.option;
          }
        }
      }
    }
  }

  Future<bool> isPtpIntroductionShownCache(String contractNumber) {
    return repaymentRepository.getPromiseToPayIntroIsShown(contractNumber);
  }
}

enum RepaymentPtpEligibilityCheckingRedirect { none, intro, option, processing, success, failPopup }
