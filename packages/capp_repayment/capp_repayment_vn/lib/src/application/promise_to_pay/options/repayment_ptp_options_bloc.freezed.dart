// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_ptp_options_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPtpOptionsEvent {
  RepaymentPtpContract get ptpContract => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPtpOptionsEventCopyWith<RepaymentPtpOptionsEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpOptionsEventCopyWith<$Res> {
  factory $RepaymentPtpOptionsEventCopyWith(RepaymentPtpOptionsEvent value,
          $Res Function(RepaymentPtpOptionsEvent) then) =
      _$RepaymentPtpOptionsEventCopyWithImpl<$Res, RepaymentPtpOptionsEvent>;
  @useResult
  $Res call({RepaymentPtpContract ptpContract});
}

/// @nodoc
class _$RepaymentPtpOptionsEventCopyWithImpl<$Res,
        $Val extends RepaymentPtpOptionsEvent>
    implements $RepaymentPtpOptionsEventCopyWith<$Res> {
  _$RepaymentPtpOptionsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ptpContract = null,
  }) {
    return _then(_value.copyWith(
      ptpContract: null == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res>
    implements $RepaymentPtpOptionsEventCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({RepaymentPtpContract ptpContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPtpOptionsEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ptpContract = null,
  }) {
    return _then(_$_Initialize(
      ptpContract: null == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.ptpContract});

  @override
  final RepaymentPtpContract ptpContract;

  @override
  String toString() {
    return 'RepaymentPtpOptionsEvent.initialize(ptpContract: $ptpContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ptpContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract ptpContract) initialize,
  }) {
    return initialize(ptpContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract ptpContract)? initialize,
  }) {
    return initialize?.call(ptpContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract ptpContract)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(ptpContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPtpOptionsEvent {
  const factory _Initialize({required final RepaymentPtpContract ptpContract}) =
      _$_Initialize;

  @override
  RepaymentPtpContract get ptpContract;
  @override
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentPtpOptionsState {
  RepaymentPtpContract? get ptpContract => throw _privateConstructorUsedError;
  RepaymentPtpType get type => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPtpOptionsStateCopyWith<RepaymentPtpOptionsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpOptionsStateCopyWith<$Res> {
  factory $RepaymentPtpOptionsStateCopyWith(RepaymentPtpOptionsState value,
          $Res Function(RepaymentPtpOptionsState) then) =
      _$RepaymentPtpOptionsStateCopyWithImpl<$Res, RepaymentPtpOptionsState>;
  @useResult
  $Res call({RepaymentPtpContract? ptpContract, RepaymentPtpType type});
}

/// @nodoc
class _$RepaymentPtpOptionsStateCopyWithImpl<$Res,
        $Val extends RepaymentPtpOptionsState>
    implements $RepaymentPtpOptionsStateCopyWith<$Res> {
  _$RepaymentPtpOptionsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ptpContract = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPtpOptionsStateCopyWith<$Res>
    implements $RepaymentPtpOptionsStateCopyWith<$Res> {
  factory _$$_RepaymentPtpOptionsStateCopyWith(
          _$_RepaymentPtpOptionsState value,
          $Res Function(_$_RepaymentPtpOptionsState) then) =
      __$$_RepaymentPtpOptionsStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({RepaymentPtpContract? ptpContract, RepaymentPtpType type});
}

/// @nodoc
class __$$_RepaymentPtpOptionsStateCopyWithImpl<$Res>
    extends _$RepaymentPtpOptionsStateCopyWithImpl<$Res,
        _$_RepaymentPtpOptionsState>
    implements _$$_RepaymentPtpOptionsStateCopyWith<$Res> {
  __$$_RepaymentPtpOptionsStateCopyWithImpl(_$_RepaymentPtpOptionsState _value,
      $Res Function(_$_RepaymentPtpOptionsState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ptpContract = freezed,
    Object? type = null,
  }) {
    return _then(_$_RepaymentPtpOptionsState(
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpType,
    ));
  }
}

/// @nodoc

class _$_RepaymentPtpOptionsState implements _RepaymentPtpOptionsState {
  const _$_RepaymentPtpOptionsState(
      {required this.ptpContract, required this.type});

  @override
  final RepaymentPtpContract? ptpContract;
  @override
  final RepaymentPtpType type;

  @override
  String toString() {
    return 'RepaymentPtpOptionsState(ptpContract: $ptpContract, type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPtpOptionsState &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract) &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ptpContract, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPtpOptionsStateCopyWith<_$_RepaymentPtpOptionsState>
      get copyWith => __$$_RepaymentPtpOptionsStateCopyWithImpl<
          _$_RepaymentPtpOptionsState>(this, _$identity);
}

abstract class _RepaymentPtpOptionsState implements RepaymentPtpOptionsState {
  const factory _RepaymentPtpOptionsState(
      {required final RepaymentPtpContract? ptpContract,
      required final RepaymentPtpType type}) = _$_RepaymentPtpOptionsState;

  @override
  RepaymentPtpContract? get ptpContract;
  @override
  RepaymentPtpType get type;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPtpOptionsStateCopyWith<_$_RepaymentPtpOptionsState>
      get copyWith => throw _privateConstructorUsedError;
}
