part of 'repayment_ptp_options_bloc.dart';

@freezed
class RepaymentPtpOptionsState with _$RepaymentPtpOptionsState {
  const factory RepaymentPtpOptionsState({
    required RepaymentPtpContract? ptpContract,
    required RepaymentPtpType type,
  }) = _RepaymentPtpOptionsState;

  factory RepaymentPtpOptionsState.initialize() => const RepaymentPtpOptionsState(
        ptpContract: null,
        type: RepaymentPtpType.none,
      );
}
