part of 'repayment_ptp_introduction_bloc.dart';

@freezed
class RepaymentPtpIntroductionState with _$RepaymentPtpIntroductionState {
  factory RepaymentPtpIntroductionState({
    required bool isDialog,
    required RepaymentPtpContract? ptpContract,
  }) = _RepaymentPtpIntroductionState;

  factory RepaymentPtpIntroductionState.initialize() => RepaymentPtpIntroductionState(
        isDialog: false,
        ptpContract: null,
      );

  @override
  String toString() {
    final contractNumber = ptpContract?.entityId ?? '';
    return 'RepaymentPtpIntroductionState{ isDialog: $isDialog, contractNumber: $contractNumber';
  }
}
