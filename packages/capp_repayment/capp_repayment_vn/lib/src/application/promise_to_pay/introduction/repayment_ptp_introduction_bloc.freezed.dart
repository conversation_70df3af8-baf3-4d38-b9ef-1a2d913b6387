// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_ptp_introduction_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPtpIntroductionEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isDialog, RepaymentPtpContract ptpContract)
        initialize,
    required TResult Function() savePromiseToPayIsShown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isDialog, RepaymentPtpContract ptpContract)?
        initialize,
    TResult? Function()? savePromiseToPayIsShown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isDialog, RepaymentPtpContract ptpContract)?
        initialize,
    TResult Function()? savePromiseToPayIsShown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SavePromiseToPayIsShown value)
        savePromiseToPayIsShown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SavePromiseToPayIsShown value)? savePromiseToPayIsShown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SavePromiseToPayIsShown value)? savePromiseToPayIsShown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpIntroductionEventCopyWith<$Res> {
  factory $RepaymentPtpIntroductionEventCopyWith(
          RepaymentPtpIntroductionEvent value,
          $Res Function(RepaymentPtpIntroductionEvent) then) =
      _$RepaymentPtpIntroductionEventCopyWithImpl<$Res,
          RepaymentPtpIntroductionEvent>;
}

/// @nodoc
class _$RepaymentPtpIntroductionEventCopyWithImpl<$Res,
        $Val extends RepaymentPtpIntroductionEvent>
    implements $RepaymentPtpIntroductionEventCopyWith<$Res> {
  _$RepaymentPtpIntroductionEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isDialog, RepaymentPtpContract ptpContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPtpIntroductionEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDialog = null,
    Object? ptpContract = null,
  }) {
    return _then(_$_Initialize(
      isDialog: null == isDialog
          ? _value.isDialog
          : isDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      ptpContract: null == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.isDialog, required this.ptpContract});

  @override
  final bool isDialog;
  @override
  final RepaymentPtpContract ptpContract;

  @override
  String toString() {
    return 'RepaymentPtpIntroductionEvent.initialize(isDialog: $isDialog, ptpContract: $ptpContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.isDialog, isDialog) ||
                other.isDialog == isDialog) &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isDialog, ptpContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isDialog, RepaymentPtpContract ptpContract)
        initialize,
    required TResult Function() savePromiseToPayIsShown,
  }) {
    return initialize(isDialog, ptpContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isDialog, RepaymentPtpContract ptpContract)?
        initialize,
    TResult? Function()? savePromiseToPayIsShown,
  }) {
    return initialize?.call(isDialog, ptpContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isDialog, RepaymentPtpContract ptpContract)?
        initialize,
    TResult Function()? savePromiseToPayIsShown,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(isDialog, ptpContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SavePromiseToPayIsShown value)
        savePromiseToPayIsShown,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SavePromiseToPayIsShown value)? savePromiseToPayIsShown,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SavePromiseToPayIsShown value)? savePromiseToPayIsShown,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPtpIntroductionEvent {
  const factory _Initialize(
      {required final bool isDialog,
      required final RepaymentPtpContract ptpContract}) = _$_Initialize;

  bool get isDialog;
  RepaymentPtpContract get ptpContract;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SavePromiseToPayIsShownCopyWith<$Res> {
  factory _$$_SavePromiseToPayIsShownCopyWith(_$_SavePromiseToPayIsShown value,
          $Res Function(_$_SavePromiseToPayIsShown) then) =
      __$$_SavePromiseToPayIsShownCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SavePromiseToPayIsShownCopyWithImpl<$Res>
    extends _$RepaymentPtpIntroductionEventCopyWithImpl<$Res,
        _$_SavePromiseToPayIsShown>
    implements _$$_SavePromiseToPayIsShownCopyWith<$Res> {
  __$$_SavePromiseToPayIsShownCopyWithImpl(_$_SavePromiseToPayIsShown _value,
      $Res Function(_$_SavePromiseToPayIsShown) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SavePromiseToPayIsShown implements _SavePromiseToPayIsShown {
  const _$_SavePromiseToPayIsShown();

  @override
  String toString() {
    return 'RepaymentPtpIntroductionEvent.savePromiseToPayIsShown()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SavePromiseToPayIsShown);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isDialog, RepaymentPtpContract ptpContract)
        initialize,
    required TResult Function() savePromiseToPayIsShown,
  }) {
    return savePromiseToPayIsShown();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isDialog, RepaymentPtpContract ptpContract)?
        initialize,
    TResult? Function()? savePromiseToPayIsShown,
  }) {
    return savePromiseToPayIsShown?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isDialog, RepaymentPtpContract ptpContract)?
        initialize,
    TResult Function()? savePromiseToPayIsShown,
    required TResult orElse(),
  }) {
    if (savePromiseToPayIsShown != null) {
      return savePromiseToPayIsShown();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SavePromiseToPayIsShown value)
        savePromiseToPayIsShown,
  }) {
    return savePromiseToPayIsShown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SavePromiseToPayIsShown value)? savePromiseToPayIsShown,
  }) {
    return savePromiseToPayIsShown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SavePromiseToPayIsShown value)? savePromiseToPayIsShown,
    required TResult orElse(),
  }) {
    if (savePromiseToPayIsShown != null) {
      return savePromiseToPayIsShown(this);
    }
    return orElse();
  }
}

abstract class _SavePromiseToPayIsShown
    implements RepaymentPtpIntroductionEvent {
  const factory _SavePromiseToPayIsShown() = _$_SavePromiseToPayIsShown;
}

/// @nodoc
mixin _$RepaymentPtpIntroductionState {
  bool get isDialog => throw _privateConstructorUsedError;
  RepaymentPtpContract? get ptpContract => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPtpIntroductionStateCopyWith<RepaymentPtpIntroductionState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpIntroductionStateCopyWith<$Res> {
  factory $RepaymentPtpIntroductionStateCopyWith(
          RepaymentPtpIntroductionState value,
          $Res Function(RepaymentPtpIntroductionState) then) =
      _$RepaymentPtpIntroductionStateCopyWithImpl<$Res,
          RepaymentPtpIntroductionState>;
  @useResult
  $Res call({bool isDialog, RepaymentPtpContract? ptpContract});
}

/// @nodoc
class _$RepaymentPtpIntroductionStateCopyWithImpl<$Res,
        $Val extends RepaymentPtpIntroductionState>
    implements $RepaymentPtpIntroductionStateCopyWith<$Res> {
  _$RepaymentPtpIntroductionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDialog = null,
    Object? ptpContract = freezed,
  }) {
    return _then(_value.copyWith(
      isDialog: null == isDialog
          ? _value.isDialog
          : isDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPtpIntroductionStateCopyWith<$Res>
    implements $RepaymentPtpIntroductionStateCopyWith<$Res> {
  factory _$$_RepaymentPtpIntroductionStateCopyWith(
          _$_RepaymentPtpIntroductionState value,
          $Res Function(_$_RepaymentPtpIntroductionState) then) =
      __$$_RepaymentPtpIntroductionStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isDialog, RepaymentPtpContract? ptpContract});
}

/// @nodoc
class __$$_RepaymentPtpIntroductionStateCopyWithImpl<$Res>
    extends _$RepaymentPtpIntroductionStateCopyWithImpl<$Res,
        _$_RepaymentPtpIntroductionState>
    implements _$$_RepaymentPtpIntroductionStateCopyWith<$Res> {
  __$$_RepaymentPtpIntroductionStateCopyWithImpl(
      _$_RepaymentPtpIntroductionState _value,
      $Res Function(_$_RepaymentPtpIntroductionState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDialog = null,
    Object? ptpContract = freezed,
  }) {
    return _then(_$_RepaymentPtpIntroductionState(
      isDialog: null == isDialog
          ? _value.isDialog
          : isDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
    ));
  }
}

/// @nodoc

class _$_RepaymentPtpIntroductionState
    implements _RepaymentPtpIntroductionState {
  _$_RepaymentPtpIntroductionState(
      {required this.isDialog, required this.ptpContract});

  @override
  final bool isDialog;
  @override
  final RepaymentPtpContract? ptpContract;

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPtpIntroductionState &&
            (identical(other.isDialog, isDialog) ||
                other.isDialog == isDialog) &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isDialog, ptpContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPtpIntroductionStateCopyWith<_$_RepaymentPtpIntroductionState>
      get copyWith => __$$_RepaymentPtpIntroductionStateCopyWithImpl<
          _$_RepaymentPtpIntroductionState>(this, _$identity);
}

abstract class _RepaymentPtpIntroductionState
    implements RepaymentPtpIntroductionState {
  factory _RepaymentPtpIntroductionState(
          {required final bool isDialog,
          required final RepaymentPtpContract? ptpContract}) =
      _$_RepaymentPtpIntroductionState;

  @override
  bool get isDialog;
  @override
  RepaymentPtpContract? get ptpContract;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPtpIntroductionStateCopyWith<_$_RepaymentPtpIntroductionState>
      get copyWith => throw _privateConstructorUsedError;
}
