part of 'repayment_ptp_rejected_bloc.dart';

@freezed
class RepaymentPtpRejectedState with _$RepaymentPtpRejectedState {
  const factory RepaymentPtpRejectedState({
    required LoadingState loadingState,
    bool? isError,
    RepaymentPtpContract? ptpContract,
    RepaymentPtpEvaluation? ptpEvaluation,
  }) = _RepaymentPtpRejectedState;

  factory RepaymentPtpRejectedState.initialize() => const RepaymentPtpRejectedState(
        loadingState: LoadingState.isInitial,
        isError: false,
      );
}
