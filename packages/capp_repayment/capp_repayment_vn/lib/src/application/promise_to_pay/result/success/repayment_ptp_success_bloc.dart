import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../../capp_repayment.dart';

part 'repayment_ptp_success_bloc.freezed.dart';
part 'repayment_ptp_success_event.dart';
part 'repayment_ptp_success_state.dart';

class RepaymentPtpSuccessBloc extends Bloc<RepaymentPtpSuccessEvent, RepaymentPtpSuccessState> {
  final IRepaymentRepository repaymentRepository;

  RepaymentPtpSuccessBloc({
    required this.repaymentRepository,
  }) : super(RepaymentPtpSuccessState.initialize()) {
    on<_Initialize>(_initialize);
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentPtpSuccessState> emit,
  ) async {
    emit(state.copyWith(ptpContract: e.ptpContract, ptpEvaluation: e.ptpEvaluation));
  }
}
