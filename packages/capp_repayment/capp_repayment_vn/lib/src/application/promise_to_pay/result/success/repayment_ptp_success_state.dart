part of 'repayment_ptp_success_bloc.dart';

@freezed
class RepaymentPtpSuccessState with _$RepaymentPtpSuccessState {
  const factory RepaymentPtpSuccessState({
    required LoadingState loadingState,
    bool? isError,
    RepaymentPtpContract? ptpContract,
    RepaymentPtpEvaluation? ptpEvaluation,
  }) = _RepaymentPtpSuccessState;

  factory RepaymentPtpSuccessState.initialize() => const RepaymentPtpSuccessState(
        loadingState: LoadingState.isInitial,
        isError: false,
      );
}
