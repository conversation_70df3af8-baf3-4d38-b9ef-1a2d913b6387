// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_ptp_processing_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPtpProcessingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)
        initialize,
    required TResult Function() fetchPtpEvaluation,
    required TResult Function(RepaymentPtpEvaluation ptpEvaluation)
        handleStatus,
    required TResult Function() showButtonBack,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult? Function()? fetchPtpEvaluation,
    TResult? Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult? Function()? showButtonBack,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult Function()? fetchPtpEvaluation,
    TResult Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult Function()? showButtonBack,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPtpEvaluation value) fetchPtpEvaluation,
    required TResult Function(_HandleStatus value) handleStatus,
    required TResult Function(_ShowButtonBack value) showButtonBack,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult? Function(_HandleStatus value)? handleStatus,
    TResult? Function(_ShowButtonBack value)? showButtonBack,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult Function(_HandleStatus value)? handleStatus,
    TResult Function(_ShowButtonBack value)? showButtonBack,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpProcessingEventCopyWith<$Res> {
  factory $RepaymentPtpProcessingEventCopyWith(
          RepaymentPtpProcessingEvent value,
          $Res Function(RepaymentPtpProcessingEvent) then) =
      _$RepaymentPtpProcessingEventCopyWithImpl<$Res,
          RepaymentPtpProcessingEvent>;
}

/// @nodoc
class _$RepaymentPtpProcessingEventCopyWithImpl<$Res,
        $Val extends RepaymentPtpProcessingEvent>
    implements $RepaymentPtpProcessingEventCopyWith<$Res> {
  _$RepaymentPtpProcessingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {RepaymentPtpContract? ptpContract,
      RepaymentPtpEvaluation? ptpEvaluation});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPtpProcessingEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ptpContract = freezed,
    Object? ptpEvaluation = freezed,
  }) {
    return _then(_$_Initialize(
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      ptpEvaluation: freezed == ptpEvaluation
          ? _value.ptpEvaluation
          : ptpEvaluation // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEvaluation?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.ptpContract, required this.ptpEvaluation});

  @override
  final RepaymentPtpContract? ptpContract;
  @override
  final RepaymentPtpEvaluation? ptpEvaluation;

  @override
  String toString() {
    return 'RepaymentPtpProcessingEvent.initialize(ptpContract: $ptpContract, ptpEvaluation: $ptpEvaluation)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract) &&
            (identical(other.ptpEvaluation, ptpEvaluation) ||
                other.ptpEvaluation == ptpEvaluation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ptpContract, ptpEvaluation);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)
        initialize,
    required TResult Function() fetchPtpEvaluation,
    required TResult Function(RepaymentPtpEvaluation ptpEvaluation)
        handleStatus,
    required TResult Function() showButtonBack,
  }) {
    return initialize(ptpContract, ptpEvaluation);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult? Function()? fetchPtpEvaluation,
    TResult? Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult? Function()? showButtonBack,
  }) {
    return initialize?.call(ptpContract, ptpEvaluation);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult Function()? fetchPtpEvaluation,
    TResult Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult Function()? showButtonBack,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(ptpContract, ptpEvaluation);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPtpEvaluation value) fetchPtpEvaluation,
    required TResult Function(_HandleStatus value) handleStatus,
    required TResult Function(_ShowButtonBack value) showButtonBack,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult? Function(_HandleStatus value)? handleStatus,
    TResult? Function(_ShowButtonBack value)? showButtonBack,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult Function(_HandleStatus value)? handleStatus,
    TResult Function(_ShowButtonBack value)? showButtonBack,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPtpProcessingEvent {
  const factory _Initialize(
      {required final RepaymentPtpContract? ptpContract,
      required final RepaymentPtpEvaluation? ptpEvaluation}) = _$_Initialize;

  RepaymentPtpContract? get ptpContract;
  RepaymentPtpEvaluation? get ptpEvaluation;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FetchPtpEvaluationCopyWith<$Res> {
  factory _$$_FetchPtpEvaluationCopyWith(_$_FetchPtpEvaluation value,
          $Res Function(_$_FetchPtpEvaluation) then) =
      __$$_FetchPtpEvaluationCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_FetchPtpEvaluationCopyWithImpl<$Res>
    extends _$RepaymentPtpProcessingEventCopyWithImpl<$Res,
        _$_FetchPtpEvaluation> implements _$$_FetchPtpEvaluationCopyWith<$Res> {
  __$$_FetchPtpEvaluationCopyWithImpl(
      _$_FetchPtpEvaluation _value, $Res Function(_$_FetchPtpEvaluation) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_FetchPtpEvaluation implements _FetchPtpEvaluation {
  const _$_FetchPtpEvaluation();

  @override
  String toString() {
    return 'RepaymentPtpProcessingEvent.fetchPtpEvaluation()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_FetchPtpEvaluation);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)
        initialize,
    required TResult Function() fetchPtpEvaluation,
    required TResult Function(RepaymentPtpEvaluation ptpEvaluation)
        handleStatus,
    required TResult Function() showButtonBack,
  }) {
    return fetchPtpEvaluation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult? Function()? fetchPtpEvaluation,
    TResult? Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult? Function()? showButtonBack,
  }) {
    return fetchPtpEvaluation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult Function()? fetchPtpEvaluation,
    TResult Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult Function()? showButtonBack,
    required TResult orElse(),
  }) {
    if (fetchPtpEvaluation != null) {
      return fetchPtpEvaluation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPtpEvaluation value) fetchPtpEvaluation,
    required TResult Function(_HandleStatus value) handleStatus,
    required TResult Function(_ShowButtonBack value) showButtonBack,
  }) {
    return fetchPtpEvaluation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult? Function(_HandleStatus value)? handleStatus,
    TResult? Function(_ShowButtonBack value)? showButtonBack,
  }) {
    return fetchPtpEvaluation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult Function(_HandleStatus value)? handleStatus,
    TResult Function(_ShowButtonBack value)? showButtonBack,
    required TResult orElse(),
  }) {
    if (fetchPtpEvaluation != null) {
      return fetchPtpEvaluation(this);
    }
    return orElse();
  }
}

abstract class _FetchPtpEvaluation implements RepaymentPtpProcessingEvent {
  const factory _FetchPtpEvaluation() = _$_FetchPtpEvaluation;
}

/// @nodoc
abstract class _$$_HandleStatusCopyWith<$Res> {
  factory _$$_HandleStatusCopyWith(
          _$_HandleStatus value, $Res Function(_$_HandleStatus) then) =
      __$$_HandleStatusCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentPtpEvaluation ptpEvaluation});
}

/// @nodoc
class __$$_HandleStatusCopyWithImpl<$Res>
    extends _$RepaymentPtpProcessingEventCopyWithImpl<$Res, _$_HandleStatus>
    implements _$$_HandleStatusCopyWith<$Res> {
  __$$_HandleStatusCopyWithImpl(
      _$_HandleStatus _value, $Res Function(_$_HandleStatus) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ptpEvaluation = null,
  }) {
    return _then(_$_HandleStatus(
      ptpEvaluation: null == ptpEvaluation
          ? _value.ptpEvaluation
          : ptpEvaluation // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEvaluation,
    ));
  }
}

/// @nodoc

class _$_HandleStatus implements _HandleStatus {
  const _$_HandleStatus({required this.ptpEvaluation});

  @override
  final RepaymentPtpEvaluation ptpEvaluation;

  @override
  String toString() {
    return 'RepaymentPtpProcessingEvent.handleStatus(ptpEvaluation: $ptpEvaluation)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_HandleStatus &&
            (identical(other.ptpEvaluation, ptpEvaluation) ||
                other.ptpEvaluation == ptpEvaluation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ptpEvaluation);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_HandleStatusCopyWith<_$_HandleStatus> get copyWith =>
      __$$_HandleStatusCopyWithImpl<_$_HandleStatus>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)
        initialize,
    required TResult Function() fetchPtpEvaluation,
    required TResult Function(RepaymentPtpEvaluation ptpEvaluation)
        handleStatus,
    required TResult Function() showButtonBack,
  }) {
    return handleStatus(ptpEvaluation);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult? Function()? fetchPtpEvaluation,
    TResult? Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult? Function()? showButtonBack,
  }) {
    return handleStatus?.call(ptpEvaluation);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult Function()? fetchPtpEvaluation,
    TResult Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult Function()? showButtonBack,
    required TResult orElse(),
  }) {
    if (handleStatus != null) {
      return handleStatus(ptpEvaluation);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPtpEvaluation value) fetchPtpEvaluation,
    required TResult Function(_HandleStatus value) handleStatus,
    required TResult Function(_ShowButtonBack value) showButtonBack,
  }) {
    return handleStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult? Function(_HandleStatus value)? handleStatus,
    TResult? Function(_ShowButtonBack value)? showButtonBack,
  }) {
    return handleStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult Function(_HandleStatus value)? handleStatus,
    TResult Function(_ShowButtonBack value)? showButtonBack,
    required TResult orElse(),
  }) {
    if (handleStatus != null) {
      return handleStatus(this);
    }
    return orElse();
  }
}

abstract class _HandleStatus implements RepaymentPtpProcessingEvent {
  const factory _HandleStatus(
      {required final RepaymentPtpEvaluation ptpEvaluation}) = _$_HandleStatus;

  RepaymentPtpEvaluation get ptpEvaluation;
  @JsonKey(ignore: true)
  _$$_HandleStatusCopyWith<_$_HandleStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ShowButtonBackCopyWith<$Res> {
  factory _$$_ShowButtonBackCopyWith(
          _$_ShowButtonBack value, $Res Function(_$_ShowButtonBack) then) =
      __$$_ShowButtonBackCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ShowButtonBackCopyWithImpl<$Res>
    extends _$RepaymentPtpProcessingEventCopyWithImpl<$Res, _$_ShowButtonBack>
    implements _$$_ShowButtonBackCopyWith<$Res> {
  __$$_ShowButtonBackCopyWithImpl(
      _$_ShowButtonBack _value, $Res Function(_$_ShowButtonBack) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ShowButtonBack implements _ShowButtonBack {
  const _$_ShowButtonBack();

  @override
  String toString() {
    return 'RepaymentPtpProcessingEvent.showButtonBack()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ShowButtonBack);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)
        initialize,
    required TResult Function() fetchPtpEvaluation,
    required TResult Function(RepaymentPtpEvaluation ptpEvaluation)
        handleStatus,
    required TResult Function() showButtonBack,
  }) {
    return showButtonBack();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult? Function()? fetchPtpEvaluation,
    TResult? Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult? Function()? showButtonBack,
  }) {
    return showButtonBack?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentPtpContract? ptpContract,
            RepaymentPtpEvaluation? ptpEvaluation)?
        initialize,
    TResult Function()? fetchPtpEvaluation,
    TResult Function(RepaymentPtpEvaluation ptpEvaluation)? handleStatus,
    TResult Function()? showButtonBack,
    required TResult orElse(),
  }) {
    if (showButtonBack != null) {
      return showButtonBack();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPtpEvaluation value) fetchPtpEvaluation,
    required TResult Function(_HandleStatus value) handleStatus,
    required TResult Function(_ShowButtonBack value) showButtonBack,
  }) {
    return showButtonBack(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult? Function(_HandleStatus value)? handleStatus,
    TResult? Function(_ShowButtonBack value)? showButtonBack,
  }) {
    return showButtonBack?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPtpEvaluation value)? fetchPtpEvaluation,
    TResult Function(_HandleStatus value)? handleStatus,
    TResult Function(_ShowButtonBack value)? showButtonBack,
    required TResult orElse(),
  }) {
    if (showButtonBack != null) {
      return showButtonBack(this);
    }
    return orElse();
  }
}

abstract class _ShowButtonBack implements RepaymentPtpProcessingEvent {
  const factory _ShowButtonBack() = _$_ShowButtonBack;
}

/// @nodoc
mixin _$RepaymentPtpProcessingState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
      get failureOrSuccess => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  bool? get isShowBackButton => throw _privateConstructorUsedError;
  RepaymentPtpContract? get ptpContract => throw _privateConstructorUsedError;
  RepaymentPtpEvaluation? get ptpEvaluation =>
      throw _privateConstructorUsedError;
  RepaymentPtpProcessingRedirect? get redirect =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPtpProcessingStateCopyWith<RepaymentPtpProcessingState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPtpProcessingStateCopyWith<$Res> {
  factory $RepaymentPtpProcessingStateCopyWith(
          RepaymentPtpProcessingState value,
          $Res Function(RepaymentPtpProcessingState) then) =
      _$RepaymentPtpProcessingStateCopyWithImpl<$Res,
          RepaymentPtpProcessingState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      Option<Either<RepaymentFailure, RepaymentPtpEvaluation>> failureOrSuccess,
      bool? isError,
      bool? isShowBackButton,
      RepaymentPtpContract? ptpContract,
      RepaymentPtpEvaluation? ptpEvaluation,
      RepaymentPtpProcessingRedirect? redirect});
}

/// @nodoc
class _$RepaymentPtpProcessingStateCopyWithImpl<$Res,
        $Val extends RepaymentPtpProcessingState>
    implements $RepaymentPtpProcessingStateCopyWith<$Res> {
  _$RepaymentPtpProcessingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? failureOrSuccess = null,
    Object? isError = freezed,
    Object? isShowBackButton = freezed,
    Object? ptpContract = freezed,
    Object? ptpEvaluation = freezed,
    Object? redirect = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isShowBackButton: freezed == isShowBackButton
          ? _value.isShowBackButton
          : isShowBackButton // ignore: cast_nullable_to_non_nullable
              as bool?,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      ptpEvaluation: freezed == ptpEvaluation
          ? _value.ptpEvaluation
          : ptpEvaluation // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEvaluation?,
      redirect: freezed == redirect
          ? _value.redirect
          : redirect // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpProcessingRedirect?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPtpProcessingStateCopyWith<$Res>
    implements $RepaymentPtpProcessingStateCopyWith<$Res> {
  factory _$$_RepaymentPtpProcessingStateCopyWith(
          _$_RepaymentPtpProcessingState value,
          $Res Function(_$_RepaymentPtpProcessingState) then) =
      __$$_RepaymentPtpProcessingStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      Option<Either<RepaymentFailure, RepaymentPtpEvaluation>> failureOrSuccess,
      bool? isError,
      bool? isShowBackButton,
      RepaymentPtpContract? ptpContract,
      RepaymentPtpEvaluation? ptpEvaluation,
      RepaymentPtpProcessingRedirect? redirect});
}

/// @nodoc
class __$$_RepaymentPtpProcessingStateCopyWithImpl<$Res>
    extends _$RepaymentPtpProcessingStateCopyWithImpl<$Res,
        _$_RepaymentPtpProcessingState>
    implements _$$_RepaymentPtpProcessingStateCopyWith<$Res> {
  __$$_RepaymentPtpProcessingStateCopyWithImpl(
      _$_RepaymentPtpProcessingState _value,
      $Res Function(_$_RepaymentPtpProcessingState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? failureOrSuccess = null,
    Object? isError = freezed,
    Object? isShowBackButton = freezed,
    Object? ptpContract = freezed,
    Object? ptpEvaluation = freezed,
    Object? redirect = freezed,
  }) {
    return _then(_$_RepaymentPtpProcessingState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isShowBackButton: freezed == isShowBackButton
          ? _value.isShowBackButton
          : isShowBackButton // ignore: cast_nullable_to_non_nullable
              as bool?,
      ptpContract: freezed == ptpContract
          ? _value.ptpContract
          : ptpContract // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpContract?,
      ptpEvaluation: freezed == ptpEvaluation
          ? _value.ptpEvaluation
          : ptpEvaluation // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpEvaluation?,
      redirect: freezed == redirect
          ? _value.redirect
          : redirect // ignore: cast_nullable_to_non_nullable
              as RepaymentPtpProcessingRedirect?,
    ));
  }
}

/// @nodoc

class _$_RepaymentPtpProcessingState implements _RepaymentPtpProcessingState {
  const _$_RepaymentPtpProcessingState(
      {required this.loadingState,
      required this.failureOrSuccess,
      this.isError,
      this.isShowBackButton,
      this.ptpContract,
      this.ptpEvaluation,
      this.redirect});

  @override
  final LoadingState loadingState;
  @override
  final Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
      failureOrSuccess;
  @override
  final bool? isError;
  @override
  final bool? isShowBackButton;
  @override
  final RepaymentPtpContract? ptpContract;
  @override
  final RepaymentPtpEvaluation? ptpEvaluation;
  @override
  final RepaymentPtpProcessingRedirect? redirect;

  @override
  String toString() {
    return 'RepaymentPtpProcessingState(loadingState: $loadingState, failureOrSuccess: $failureOrSuccess, isError: $isError, isShowBackButton: $isShowBackButton, ptpContract: $ptpContract, ptpEvaluation: $ptpEvaluation, redirect: $redirect)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPtpProcessingState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.failureOrSuccess, failureOrSuccess) ||
                other.failureOrSuccess == failureOrSuccess) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.isShowBackButton, isShowBackButton) ||
                other.isShowBackButton == isShowBackButton) &&
            (identical(other.ptpContract, ptpContract) ||
                other.ptpContract == ptpContract) &&
            (identical(other.ptpEvaluation, ptpEvaluation) ||
                other.ptpEvaluation == ptpEvaluation) &&
            (identical(other.redirect, redirect) ||
                other.redirect == redirect));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loadingState, failureOrSuccess,
      isError, isShowBackButton, ptpContract, ptpEvaluation, redirect);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPtpProcessingStateCopyWith<_$_RepaymentPtpProcessingState>
      get copyWith => __$$_RepaymentPtpProcessingStateCopyWithImpl<
          _$_RepaymentPtpProcessingState>(this, _$identity);
}

abstract class _RepaymentPtpProcessingState
    implements RepaymentPtpProcessingState {
  const factory _RepaymentPtpProcessingState(
      {required final LoadingState loadingState,
      required final Option<Either<RepaymentFailure, RepaymentPtpEvaluation>>
          failureOrSuccess,
      final bool? isError,
      final bool? isShowBackButton,
      final RepaymentPtpContract? ptpContract,
      final RepaymentPtpEvaluation? ptpEvaluation,
      final RepaymentPtpProcessingRedirect?
          redirect}) = _$_RepaymentPtpProcessingState;

  @override
  LoadingState get loadingState;
  @override
  Option<Either<RepaymentFailure, RepaymentPtpEvaluation>> get failureOrSuccess;
  @override
  bool? get isError;
  @override
  bool? get isShowBackButton;
  @override
  RepaymentPtpContract? get ptpContract;
  @override
  RepaymentPtpEvaluation? get ptpEvaluation;
  @override
  RepaymentPtpProcessingRedirect? get redirect;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPtpProcessingStateCopyWith<_$_RepaymentPtpProcessingState>
      get copyWith => throw _privateConstructorUsedError;
}
