part of 'repayment_ptp_processing_bloc.dart';

@freezed
class RepaymentPtpProcessingState with _$RepaymentPtpProcessingState {
  const factory RepaymentPtpProcessingState({
    required LoadingState loadingState,
    required Option<Either<RepaymentFailure, RepaymentPtpEvaluation>> failureOrSuccess,
    bool? isError,
    bool? isShowBackButton,
    RepaymentPtpContract? ptpContract,
    RepaymentPtpEvaluation? ptpEvaluation,
    RepaymentPtpProcessingRedirect? redirect,
  }) = _RepaymentPtpProcessingState;

  factory RepaymentPtpProcessingState.initialize() => RepaymentPtpProcessingState(
        loadingState: LoadingState.isInitial,
        failureOrSuccess: none(),
        isError: false,
        isShowBackButton: false,
        redirect: RepaymentPtpProcessingRedirect.none,
      );
}
