part of 'repayment_payment_result_bloc.dart';

@freezed
class RepaymentPaymentResultState with _$RepaymentPaymentResultState {
  const factory RepaymentPaymentResultState({
    required LoadingState loadingState,
    required String deeplink,
    required String phoneNumber,
    String? previousRouteName,
    required Option<Either<String, RepaymentTransactionSuccessRouteArgs>> failureOrSuccess,
    required Option<Either<RepaymentFailure, RepaymentTransaction>> failureOrSuccessTransactionResult,
    RepaymentUserPaymentMethod? paymentMethod,
    List<RepaymentUserPaymentMethod>? paymentMethods,
    String? transactionId,
    String? voucherCode,
  }) = _RepaymentPaymentResultState;

  factory RepaymentPaymentResultState.initialize() => RepaymentPaymentResultState(
        loadingState: LoadingState.isInitial,
        deeplink: '',
        phoneNumber: '',
        failureOrSuccess: none(),
        failureOrSuccessTransactionResult: none(),
      );
}
