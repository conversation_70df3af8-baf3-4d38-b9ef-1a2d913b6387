import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:collection/collection.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment.dart';

part 'repayment_transaction_success_bloc.freezed.dart';
part 'repayment_transaction_success_event.dart';
part 'repayment_transaction_success_state.dart';

class RepaymentTransactionSuccessBloc extends Bloc<RepaymentTransactionSuccessEvent, RepaymentTransactionSuccessState> {
  final IFeatureFlagRepository featureFlagRepository;
  final IAutoDebitRepository autoDebitRepository;

  RepaymentTransactionSuccessBloc({
    required this.featureFlagRepository,
    required this.autoDebitRepository,
  }) : super(RepaymentTransactionSuccessState.initialize()) {
    on<_Initialize>(
      (e, emit) async {
        // TODO(RET-C): Comment this code until the ADA is ready to release
        final response = await autoDebitRepository.getAdaEligibleContracts();
        emit(
          response.fold((l) {
            return state.copyWith(
              loadingState: LoadingState.isCompleted,
              isAdaEligible: false,
            );
          }, (r) {
            final contractNumber = e.contractNumber;
            final currentContract = r.firstWhereOrNull((contract) => contract.contractNumber == contractNumber);
            return state.copyWith(
              loadingState: LoadingState.isCompleted,
              isAdaEligible: currentContract?.isEligible ?? false,
              contractNumber: contractNumber,
            );
          }),
        );
        // TODO(RET-C): Uncomment this code until the ADA is ready to release (to be removed)
        // emit(
        //   state.copyWith(
        //     isAdaEligible: true,
        //     loadingState: LoadingState.isCompleted,
        //   ),
        // );
      },
    );
  }

  bool isShowTransactionSuccessBanner() {
    return featureFlagRepository.isEnabledCached(FeatureFlag.repaymentResultBanner);
  }
}
