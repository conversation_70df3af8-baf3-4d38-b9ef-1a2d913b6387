import 'dart:convert';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_repayment.dart';

part 'repayment_payment_result_bloc.freezed.dart';
part 'repayment_payment_result_event.dart';
part 'repayment_payment_result_state.dart';

class RepaymentPaymentResultBloc extends Bloc<RepaymentPaymentResultEvent, RepaymentPaymentResultState> {
  final LockStatusBloc? lockStatusBloc;
  final IUserRepository userRepository;
  final IRepaymentRepository repaymentRepository;
  final Logger logger;

  RepaymentPaymentResultBloc({
    required this.lockStatusBloc,
    required this.repaymentRepository,
    required this.userRepository,
    required this.logger,
  }) : super(RepaymentPaymentResultState.initialize()) {
    on<RepaymentPaymentResultEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          checkTransactionResult: (e) => _checkTransactionResult(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentPaymentResultState> emit,
  ) async {
    // Get payment methods from cache
    var paymentMethods = await repaymentRepository.getPaymentMethodsFromCache() ?? [];

    // In case cache is lost, get from backup
    if (paymentMethods.isEmpty) {
      paymentMethods =
          await repaymentRepository.getBackupUserPaymentMethods(l10nCappRepayment: e.repaymentLocalization);
    }

    emit(
      state.copyWith(
        paymentMethods: paymentMethods,
      ),
    );

    lockStatusBloc?.add(const LockStatusEvent.setEnabled(enabled: true));
    final isEnableMomoV2 = e.isEnableMomoV2 ?? false;
    if (e.deeplink.contains('momo')) {
      await emit.forEach(
        isEnableMomoV2
            ? _handleMomoResult(e.deeplink, e.previousRouteName)
            : _handleMomoResultV1(e.deeplink, e.previousRouteName),
        onData: (state) {
          return state;
        },
      ).catchError((dynamic error) {
        logger.d(error.toString());
      });
    } else if (e.deeplink.contains('vnpay')) {
      await emit.forEach(
        _handleVnpayResult(e.deeplink, e.previousRouteName),
        onData: (state) {
          return state;
        },
      ).catchError((dynamic error) {
        logger.d(error.toString());
      });
    } else if (e.deeplink.contains('shopeepay')) {
      await emit.forEach(
        _handleShopeePayResult(e.deeplink, e.previousRouteName),
        onData: (state) {
          return state;
        },
      ).catchError((dynamic error) {
        logger.d(error.toString());
      });
    } else if (e.deeplink.contains('zalopay')) {
      await emit.forEach(
        _handleZaloPayResult(e.deeplink, e.previousRouteName),
        onData: (state) {
          return state;
        },
      ).catchError((dynamic error) {
        logger.d(error.toString());
      });
    } else {
      await emit.forEach(
        _handleFailedScreen(deeplink: e.deeplink, previousRouteName: e.previousRouteName),
        onData: (state) {
          return state;
        },
      ).catchError((dynamic error) {
        logger.d(error.toString());
      });
    }
  }

  Future<void> _checkTransactionResult(
    _CheckTransactionResult e,
    Emitter<RepaymentPaymentResultState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading));
    final transactionResult = await repaymentRepository.getTransactionResult(e.transactionId);
    final phoneNumber = await _fetchPhoneNumber();
    emit(
      state.copyWith(
        failureOrSuccessTransactionResult: optionOf(transactionResult),
        transactionId: e.transactionId,
        phoneNumber: phoneNumber,
        loadingState: LoadingState.isCompleted,
      ),
    );
  }

  Stream<RepaymentPaymentResultState> _handleMomoResultV1(String deeplink, String previousRouteName) async* {
    try {
      final momoPaymentMethod =
          RepaymentPaymentMethod.momo.getUserPaymentMethod(userPaymentMethods: state.paymentMethods);

      final uri = Uri.tryParse(Uri.decodeFull(deeplink));
      final data = uri?.queryParameters.containsKey('data') ?? false ? uri?.queryParameters['data'] ?? '' : '';
      final phoneNumber = await _fetchPhoneNumber();
      final map = jsonDecode(utf8.decode(base64.decode(data))) as Map<String, dynamic>;
      if (data.isNotEmpty && map['resultCode'] == 0) {
        final transactionResultCache = await repaymentRepository.getTransactionResultFromCache();
        if (transactionResultCache != null) {
          final transactionId = transactionResultCache.transactionId;
          final voucherCode = transactionResultCache.voucherCode;
          yield state.copyWith(
            transactionId: transactionId,
            phoneNumber: phoneNumber,
            voucherCode: voucherCode,
            deeplink: deeplink,
            paymentMethod: momoPaymentMethod,
            failureOrSuccess: optionOf(
              right<String, RepaymentTransactionSuccessRouteArgs>(
                RepaymentTransactionSuccessRouteArgs(
                  isEWallet: false,
                  contractNo: map['reference1']?.toString() ?? '-',
                  finalAmount: double.parse((map['amount'] ?? '0').toString()),
                  customerName: '',
                  phoneNumber: phoneNumber,
                  paymentOption: RepaymentPaymentMethod.momo
                          .getUserPaymentMethod(userPaymentMethods: state.paymentMethods)
                          ?.title ??
                      '',
                  paymentOptionId: momoPaymentMethod?.gmaId ?? '',
                  dateProcessed: '',
                  transactionNo: map['paymentId'] != null ? 'MM${map['paymentId']}' : '-',
                ),
              ),
            ),
          );
        }
      } else {
        yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
      }
    } on Exception catch (_) {
      yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
    }
  }

  Stream<RepaymentPaymentResultState> _handleMomoResult(String deeplink, String previousRouteName) async* {
    try {
      final momoPaymentMethod =
          RepaymentPaymentMethod.momo.getUserPaymentMethod(userPaymentMethods: state.paymentMethods);

      final uri = Uri.tryParse(deeplink);
      String? resultCode;
      if (uri?.queryParameters.containsKey('resultCode') ?? false) {
        resultCode = uri?.queryParameters['resultCode'];
      }
      if (resultCode == '0') {
        final phoneNumber = await _fetchPhoneNumber();
        final transactionResultCache = await repaymentRepository.getTransactionResultFromCache();
        final transactionId = transactionResultCache?.transactionId ?? '';
        if (transactionId.isNotEmpty) {
          yield state.copyWith(
            deeplink: deeplink,
            phoneNumber: phoneNumber,
            paymentMethod: momoPaymentMethod,
            previousRouteName: previousRouteName,
          );

          add(RepaymentPaymentResultEvent.checkTransactionResult(transactionId));
        } else {
          yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
        }
      } else {
        yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
      }
    } on Exception catch (_) {
      yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
    }
  }

  Stream<RepaymentPaymentResultState> _handleVnpayResult(String deeplink, String previousRouteName) async* {
    if (GmaPlatform.isIOS && previousRouteName == 'repayment_payment_summary_screen') {
      return;
    }
    final vnpayPaymentMethod =
        RepaymentPaymentMethod.vnpay.getUserPaymentMethod(userPaymentMethods: state.paymentMethods);
    try {
      final uri = Uri.tryParse(Uri.decodeFull(deeplink));
      if (uri == null) {
        return;
      }
      final queryParameters = uri.queryParameters;
      final responseCode =
          queryParameters.containsKey('vnp_ResponseCode') ? queryParameters['vnp_ResponseCode'] ?? '' : '';
      final contractNum = queryParameters.containsKey('vnp_OrderInfo') ? queryParameters['vnp_OrderInfo'] ?? '' : '';
      final amount = queryParameters.containsKey('vnp_Amount') ? queryParameters['vnp_Amount'] ?? '' : '';
      final transactionNo =
          queryParameters.containsKey('vnp_TransactionNo') ? queryParameters['vnp_TransactionNo'] ?? '' : '';
      final phoneNumber = await _fetchPhoneNumber();
      if (responseCode.isNotEmpty && responseCode == '00') {
        final transactionResultCache = await repaymentRepository.getTransactionResultFromCache();
        if (transactionResultCache != null) {
          final transactionId = transactionResultCache.transactionId;
          final voucherCode = transactionResultCache.voucherCode;
          yield state.copyWith(
            transactionId: transactionId,
            phoneNumber: phoneNumber,
            voucherCode: voucherCode,
            deeplink: deeplink,
            paymentMethod: vnpayPaymentMethod,
            failureOrSuccess: optionOf(
              right<String, RepaymentTransactionSuccessRouteArgs>(
                RepaymentTransactionSuccessRouteArgs(
                  isEWallet: false,
                  contractNo: contractNum,
                  finalAmount: double.parse(amount),
                  customerName: '',
                  phoneNumber: phoneNumber,
                  paymentOption: vnpayPaymentMethod?.title ?? '',
                  paymentOptionId: vnpayPaymentMethod?.gmaId ?? '',
                  dateProcessed: '',
                  transactionNo: transactionNo,
                ),
              ),
            ),
          );
        }
      } else if (responseCode.isEmpty) {
        //Payment successfully without data, return scheme only
        final transactionResultCache = await repaymentRepository.getTransactionResultFromCache();
        if (transactionResultCache != null) {
          final transactionId = transactionResultCache.transactionId;
          add(RepaymentPaymentResultEvent.checkTransactionResult(transactionId));
        } else {
          yield state.copyWith(
            failureOrSuccessTransactionResult: optionOf(
              left<RepaymentFailure, RepaymentTransaction>(
                const RepaymentFailure.unexpected(),
              ),
            ),
          );
        }
      } else {
        yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
      }
    } on Exception catch (_) {
      yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
    }
  }

  Stream<RepaymentPaymentResultState> _handleShopeePayResult(String deeplink, String previousRouteName) async* {
    try {
      final shopeePaymentMethod =
          RepaymentPaymentMethod.shopee.getUserPaymentMethod(userPaymentMethods: state.paymentMethods);

      final uri = Uri.tryParse(Uri.decodeFull(deeplink));
      final resultCode =
          uri?.queryParameters.containsKey('result_code') ?? false ? uri?.queryParameters['result_code'] ?? '' : '';
      final phoneNumber = await _fetchPhoneNumber();
      if (resultCode == ShopeePayResultCode.success.code) {
        final transactionResultCache = await repaymentRepository.getTransactionResultFromCache();
        if (transactionResultCache != null) {
          final partnerTransactionId = uri?.queryParameters.containsKey('transaction_sn') ?? false
              ? uri?.queryParameters['transaction_sn'] ?? ''
              : '';
          final transactionId = transactionResultCache.transactionId;
          final voucherCode = transactionResultCache.voucherCode;
          yield state.copyWith(
            transactionId: transactionId,
            deeplink: deeplink,
            phoneNumber: phoneNumber,
            voucherCode: voucherCode,
            paymentMethod: shopeePaymentMethod,
            failureOrSuccess: optionOf(
              right<String, RepaymentTransactionSuccessRouteArgs>(
                RepaymentTransactionSuccessRouteArgs(
                  isEWallet: false,
                  contractNo: transactionResultCache.contractNo,
                  voucherCode: voucherCode,
                  finalAmount: transactionResultCache.finalAmount,
                  discountAmount: transactionResultCache.discountAmount,
                  originalAmount: transactionResultCache.originalAmount,
                  customerName: '',
                  phoneNumber: phoneNumber,
                  paymentOption: transactionResultCache.paymentOption,
                  paymentOptionId: transactionResultCache.paymentOptionId,
                  dateProcessed: '',
                  transactionNo: partnerTransactionId,
                ),
              ),
            ),
          );
        }
      } else if (resultCode == ShopeePayResultCode.cancel.code ||
          resultCode == ShopeePayResultCode.fail.code ||
          resultCode == ShopeePayResultCode.expired.code) {
        yield* _handleFailedScreen(
          deeplink: deeplink,
          paymentMethod: shopeePaymentMethod,
          previousRouteName: previousRouteName,
          message: resultCode,
        );
      } else if (resultCode == ShopeePayResultCode.inprogress.code) {
        final transactionResultCache = await repaymentRepository.getTransactionResultFromCache();
        if (transactionResultCache != null) {
          final transactionId = transactionResultCache.transactionId;
          yield state.copyWith(
            deeplink: deeplink,
            phoneNumber: phoneNumber,
            paymentMethod: shopeePaymentMethod,
            previousRouteName: previousRouteName,
          );

          add(RepaymentPaymentResultEvent.checkTransactionResult(transactionId));
        } else {
          yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
        }
      } else {
        yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
      }
    } on Exception catch (_) {
      yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
    }
  }

  Stream<RepaymentPaymentResultState> _handleZaloPayResult(String deeplink, String previousRouteName) async* {
    try {
      final zaloPaymentMethod =
          RepaymentPaymentMethod.zalo.getUserPaymentMethod(userPaymentMethods: state.paymentMethods);

      final uri = Uri.tryParse(GmaPlatform.isAndroid ? deeplink : Uri.decodeFull(deeplink));
      final code = uri?.queryParameters.containsKey('code') ?? false ? uri?.queryParameters['code'] ?? '' : '';
      final phoneNumber = await _fetchPhoneNumber();
      if (code == ZaloPayResultCode.success.code) {
        final transactionResultCache = await repaymentRepository.getTransactionResultFromCache();
        if (transactionResultCache != null) {
          var partnerTransactionId = '';
          if (uri?.queryParameters.containsKey('apptransid') ?? false) {
            partnerTransactionId = uri!.queryParameters['apptransid']!;
          } else if (uri?.queryParameters.containsKey('appTransID') ?? false) {
            partnerTransactionId = uri!.queryParameters['appTransID']!;
          }

          final transactionId = transactionResultCache.transactionId;
          final voucherCode = transactionResultCache.voucherCode;
          yield state.copyWith(
            transactionId: transactionId,
            deeplink: deeplink,
            phoneNumber: phoneNumber,
            voucherCode: voucherCode,
            paymentMethod: zaloPaymentMethod,
            failureOrSuccess: optionOf(
              right<String, RepaymentTransactionSuccessRouteArgs>(
                RepaymentTransactionSuccessRouteArgs(
                  isEWallet: false,
                  contractNo: transactionResultCache.contractNo,
                  finalAmount: transactionResultCache.finalAmount,
                  discountAmount: transactionResultCache.discountAmount,
                  originalAmount: transactionResultCache.originalAmount,
                  voucherCode: transactionResultCache.voucherCode,
                  customerName: '',
                  paymentOptionId: transactionResultCache.paymentOptionId,
                  phoneNumber: phoneNumber,
                  paymentOption: transactionResultCache.paymentOption,
                  dateProcessed: '',
                  transactionNo: partnerTransactionId,
                ),
              ),
            ),
          );
        }
      } else if (code == ZaloPayResultCode.invalidResponse.code ||
          code == ZaloPayResultCode.invalidOrder.code ||
          code == ZaloPayResultCode.cancel.code ||
          code == ZaloPayResultCode.cancelIos.code ||
          code == ZaloPayResultCode.fail.code) {
        yield* _handleFailedScreen(
          deeplink: deeplink,
          paymentMethod: zaloPaymentMethod,
          previousRouteName: previousRouteName,
          message: code,
        );
      } else {
        yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
      }
    } on Exception catch (_) {
      yield* _handleFailedScreen(deeplink: deeplink, previousRouteName: previousRouteName);
    }
  }

  Stream<RepaymentPaymentResultState> _handleFailedScreen({
    required String deeplink,
    String? message,
    required String previousRouteName,
    RepaymentUserPaymentMethod? paymentMethod,
  }) async* {
    yield state.copyWith(
      deeplink: deeplink,
      paymentMethod: paymentMethod,
      previousRouteName: previousRouteName,
      failureOrSuccess: optionOf(left<String, RepaymentTransactionSuccessRouteArgs>(message ?? '')),
    );
  }

  Future<String> _fetchPhoneNumber() async {
    return (await userRepository.currentUser()).fold((l) => '', (r) => r.phoneNumber ?? '');
  }
}
