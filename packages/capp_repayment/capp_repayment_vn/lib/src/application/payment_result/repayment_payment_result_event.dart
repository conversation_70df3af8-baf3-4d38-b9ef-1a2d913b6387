part of 'repayment_payment_result_bloc.dart';

@freezed
class RepaymentPaymentResultEvent with _$RepaymentPaymentResultEvent {
  const factory RepaymentPaymentResultEvent.initialize({
    required String deeplink,
    required String previousRouteName,
    bool? isEnableMomoV2,
    required L10nCappRepayment repaymentLocalization,
  }) = _Initialize;
  const factory RepaymentPaymentResultEvent.checkTransactionResult(String transactionId) = _CheckTransactionResult;
}
