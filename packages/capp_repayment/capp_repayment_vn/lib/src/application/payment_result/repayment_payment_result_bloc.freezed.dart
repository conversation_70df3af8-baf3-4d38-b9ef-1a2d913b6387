// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_payment_result_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPaymentResultEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPaymentResultEventCopyWith<$Res> {
  factory $RepaymentPaymentResultEventCopyWith(
          RepaymentPaymentResultEvent value,
          $Res Function(RepaymentPaymentResultEvent) then) =
      _$RepaymentPaymentResultEventCopyWithImpl<$Res,
          RepaymentPaymentResultEvent>;
}

/// @nodoc
class _$RepaymentPaymentResultEventCopyWithImpl<$Res,
        $Val extends RepaymentPaymentResultEvent>
    implements $RepaymentPaymentResultEventCopyWith<$Res> {
  _$RepaymentPaymentResultEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String deeplink,
      String previousRouteName,
      bool? isEnableMomoV2,
      L10nCappRepayment repaymentLocalization});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPaymentResultEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deeplink = null,
    Object? previousRouteName = null,
    Object? isEnableMomoV2 = freezed,
    Object? repaymentLocalization = null,
  }) {
    return _then(_$_Initialize(
      deeplink: null == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as String,
      previousRouteName: null == previousRouteName
          ? _value.previousRouteName
          : previousRouteName // ignore: cast_nullable_to_non_nullable
              as String,
      isEnableMomoV2: freezed == isEnableMomoV2
          ? _value.isEnableMomoV2
          : isEnableMomoV2 // ignore: cast_nullable_to_non_nullable
              as bool?,
      repaymentLocalization: null == repaymentLocalization
          ? _value.repaymentLocalization
          : repaymentLocalization // ignore: cast_nullable_to_non_nullable
              as L10nCappRepayment,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required this.deeplink,
      required this.previousRouteName,
      this.isEnableMomoV2,
      required this.repaymentLocalization});

  @override
  final String deeplink;
  @override
  final String previousRouteName;
  @override
  final bool? isEnableMomoV2;
  @override
  final L10nCappRepayment repaymentLocalization;

  @override
  String toString() {
    return 'RepaymentPaymentResultEvent.initialize(deeplink: $deeplink, previousRouteName: $previousRouteName, isEnableMomoV2: $isEnableMomoV2, repaymentLocalization: $repaymentLocalization)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.deeplink, deeplink) ||
                other.deeplink == deeplink) &&
            (identical(other.previousRouteName, previousRouteName) ||
                other.previousRouteName == previousRouteName) &&
            (identical(other.isEnableMomoV2, isEnableMomoV2) ||
                other.isEnableMomoV2 == isEnableMomoV2) &&
            (identical(other.repaymentLocalization, repaymentLocalization) ||
                other.repaymentLocalization == repaymentLocalization));
  }

  @override
  int get hashCode => Object.hash(runtimeType, deeplink, previousRouteName,
      isEnableMomoV2, repaymentLocalization);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
  }) {
    return initialize(
        deeplink, previousRouteName, isEnableMomoV2, repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
  }) {
    return initialize?.call(
        deeplink, previousRouteName, isEnableMomoV2, repaymentLocalization);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(
          deeplink, previousRouteName, isEnableMomoV2, repaymentLocalization);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPaymentResultEvent {
  const factory _Initialize(
      {required final String deeplink,
      required final String previousRouteName,
      final bool? isEnableMomoV2,
      required final L10nCappRepayment repaymentLocalization}) = _$_Initialize;

  String get deeplink;
  String get previousRouteName;
  bool? get isEnableMomoV2;
  L10nCappRepayment get repaymentLocalization;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_CheckTransactionResultCopyWith<$Res> {
  factory _$$_CheckTransactionResultCopyWith(_$_CheckTransactionResult value,
          $Res Function(_$_CheckTransactionResult) then) =
      __$$_CheckTransactionResultCopyWithImpl<$Res>;
  @useResult
  $Res call({String transactionId});
}

/// @nodoc
class __$$_CheckTransactionResultCopyWithImpl<$Res>
    extends _$RepaymentPaymentResultEventCopyWithImpl<$Res,
        _$_CheckTransactionResult>
    implements _$$_CheckTransactionResultCopyWith<$Res> {
  __$$_CheckTransactionResultCopyWithImpl(_$_CheckTransactionResult _value,
      $Res Function(_$_CheckTransactionResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
  }) {
    return _then(_$_CheckTransactionResult(
      null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_CheckTransactionResult implements _CheckTransactionResult {
  const _$_CheckTransactionResult(this.transactionId);

  @override
  final String transactionId;

  @override
  String toString() {
    return 'RepaymentPaymentResultEvent.checkTransactionResult(transactionId: $transactionId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckTransactionResult &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, transactionId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CheckTransactionResultCopyWith<_$_CheckTransactionResult> get copyWith =>
      __$$_CheckTransactionResultCopyWithImpl<_$_CheckTransactionResult>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
  }) {
    return checkTransactionResult(transactionId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
  }) {
    return checkTransactionResult?.call(transactionId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String deeplink, String previousRouteName,
            bool? isEnableMomoV2, L10nCappRepayment repaymentLocalization)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    required TResult orElse(),
  }) {
    if (checkTransactionResult != null) {
      return checkTransactionResult(transactionId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
  }) {
    return checkTransactionResult(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
  }) {
    return checkTransactionResult?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    required TResult orElse(),
  }) {
    if (checkTransactionResult != null) {
      return checkTransactionResult(this);
    }
    return orElse();
  }
}

abstract class _CheckTransactionResult implements RepaymentPaymentResultEvent {
  const factory _CheckTransactionResult(final String transactionId) =
      _$_CheckTransactionResult;

  String get transactionId;
  @JsonKey(ignore: true)
  _$$_CheckTransactionResultCopyWith<_$_CheckTransactionResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentPaymentResultState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  String get deeplink => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String? get previousRouteName => throw _privateConstructorUsedError;
  Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
      get failureOrSuccess => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentTransaction>>
      get failureOrSuccessTransactionResult =>
          throw _privateConstructorUsedError;
  RepaymentUserPaymentMethod? get paymentMethod =>
      throw _privateConstructorUsedError;
  List<RepaymentUserPaymentMethod>? get paymentMethods =>
      throw _privateConstructorUsedError;
  String? get transactionId => throw _privateConstructorUsedError;
  String? get voucherCode => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPaymentResultStateCopyWith<RepaymentPaymentResultState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPaymentResultStateCopyWith<$Res> {
  factory $RepaymentPaymentResultStateCopyWith(
          RepaymentPaymentResultState value,
          $Res Function(RepaymentPaymentResultState) then) =
      _$RepaymentPaymentResultStateCopyWithImpl<$Res,
          RepaymentPaymentResultState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      String deeplink,
      String phoneNumber,
      String? previousRouteName,
      Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
          failureOrSuccess,
      Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      RepaymentUserPaymentMethod? paymentMethod,
      List<RepaymentUserPaymentMethod>? paymentMethods,
      String? transactionId,
      String? voucherCode});
}

/// @nodoc
class _$RepaymentPaymentResultStateCopyWithImpl<$Res,
        $Val extends RepaymentPaymentResultState>
    implements $RepaymentPaymentResultStateCopyWith<$Res> {
  _$RepaymentPaymentResultStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? deeplink = null,
    Object? phoneNumber = null,
    Object? previousRouteName = freezed,
    Object? failureOrSuccess = null,
    Object? failureOrSuccessTransactionResult = null,
    Object? paymentMethod = freezed,
    Object? paymentMethods = freezed,
    Object? transactionId = freezed,
    Object? voucherCode = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      deeplink: null == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      previousRouteName: freezed == previousRouteName
          ? _value.previousRouteName
          : previousRouteName // ignore: cast_nullable_to_non_nullable
              as String?,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<String, RepaymentTransactionSuccessRouteArgs>>,
      failureOrSuccessTransactionResult: null ==
              failureOrSuccessTransactionResult
          ? _value.failureOrSuccessTransactionResult
          : failureOrSuccessTransactionResult // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentTransaction>>,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      paymentMethods: freezed == paymentMethods
          ? _value.paymentMethods
          : paymentMethods // ignore: cast_nullable_to_non_nullable
              as List<RepaymentUserPaymentMethod>?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPaymentResultStateCopyWith<$Res>
    implements $RepaymentPaymentResultStateCopyWith<$Res> {
  factory _$$_RepaymentPaymentResultStateCopyWith(
          _$_RepaymentPaymentResultState value,
          $Res Function(_$_RepaymentPaymentResultState) then) =
      __$$_RepaymentPaymentResultStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      String deeplink,
      String phoneNumber,
      String? previousRouteName,
      Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
          failureOrSuccess,
      Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      RepaymentUserPaymentMethod? paymentMethod,
      List<RepaymentUserPaymentMethod>? paymentMethods,
      String? transactionId,
      String? voucherCode});
}

/// @nodoc
class __$$_RepaymentPaymentResultStateCopyWithImpl<$Res>
    extends _$RepaymentPaymentResultStateCopyWithImpl<$Res,
        _$_RepaymentPaymentResultState>
    implements _$$_RepaymentPaymentResultStateCopyWith<$Res> {
  __$$_RepaymentPaymentResultStateCopyWithImpl(
      _$_RepaymentPaymentResultState _value,
      $Res Function(_$_RepaymentPaymentResultState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? deeplink = null,
    Object? phoneNumber = null,
    Object? previousRouteName = freezed,
    Object? failureOrSuccess = null,
    Object? failureOrSuccessTransactionResult = null,
    Object? paymentMethod = freezed,
    Object? paymentMethods = freezed,
    Object? transactionId = freezed,
    Object? voucherCode = freezed,
  }) {
    return _then(_$_RepaymentPaymentResultState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      deeplink: null == deeplink
          ? _value.deeplink
          : deeplink // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      previousRouteName: freezed == previousRouteName
          ? _value.previousRouteName
          : previousRouteName // ignore: cast_nullable_to_non_nullable
              as String?,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<String, RepaymentTransactionSuccessRouteArgs>>,
      failureOrSuccessTransactionResult: null ==
              failureOrSuccessTransactionResult
          ? _value.failureOrSuccessTransactionResult
          : failureOrSuccessTransactionResult // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentTransaction>>,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      paymentMethods: freezed == paymentMethods
          ? _value._paymentMethods
          : paymentMethods // ignore: cast_nullable_to_non_nullable
              as List<RepaymentUserPaymentMethod>?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      voucherCode: freezed == voucherCode
          ? _value.voucherCode
          : voucherCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_RepaymentPaymentResultState implements _RepaymentPaymentResultState {
  const _$_RepaymentPaymentResultState(
      {required this.loadingState,
      required this.deeplink,
      required this.phoneNumber,
      this.previousRouteName,
      required this.failureOrSuccess,
      required this.failureOrSuccessTransactionResult,
      this.paymentMethod,
      final List<RepaymentUserPaymentMethod>? paymentMethods,
      this.transactionId,
      this.voucherCode})
      : _paymentMethods = paymentMethods;

  @override
  final LoadingState loadingState;
  @override
  final String deeplink;
  @override
  final String phoneNumber;
  @override
  final String? previousRouteName;
  @override
  final Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
      failureOrSuccess;
  @override
  final Option<Either<RepaymentFailure, RepaymentTransaction>>
      failureOrSuccessTransactionResult;
  @override
  final RepaymentUserPaymentMethod? paymentMethod;
  final List<RepaymentUserPaymentMethod>? _paymentMethods;
  @override
  List<RepaymentUserPaymentMethod>? get paymentMethods {
    final value = _paymentMethods;
    if (value == null) return null;
    if (_paymentMethods is EqualUnmodifiableListView) return _paymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? transactionId;
  @override
  final String? voucherCode;

  @override
  String toString() {
    return 'RepaymentPaymentResultState(loadingState: $loadingState, deeplink: $deeplink, phoneNumber: $phoneNumber, previousRouteName: $previousRouteName, failureOrSuccess: $failureOrSuccess, failureOrSuccessTransactionResult: $failureOrSuccessTransactionResult, paymentMethod: $paymentMethod, paymentMethods: $paymentMethods, transactionId: $transactionId, voucherCode: $voucherCode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPaymentResultState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.deeplink, deeplink) ||
                other.deeplink == deeplink) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.previousRouteName, previousRouteName) ||
                other.previousRouteName == previousRouteName) &&
            (identical(other.failureOrSuccess, failureOrSuccess) ||
                other.failureOrSuccess == failureOrSuccess) &&
            (identical(other.failureOrSuccessTransactionResult,
                    failureOrSuccessTransactionResult) ||
                other.failureOrSuccessTransactionResult ==
                    failureOrSuccessTransactionResult) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            const DeepCollectionEquality()
                .equals(other._paymentMethods, _paymentMethods) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.voucherCode, voucherCode) ||
                other.voucherCode == voucherCode));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      deeplink,
      phoneNumber,
      previousRouteName,
      failureOrSuccess,
      failureOrSuccessTransactionResult,
      paymentMethod,
      const DeepCollectionEquality().hash(_paymentMethods),
      transactionId,
      voucherCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPaymentResultStateCopyWith<_$_RepaymentPaymentResultState>
      get copyWith => __$$_RepaymentPaymentResultStateCopyWithImpl<
          _$_RepaymentPaymentResultState>(this, _$identity);
}

abstract class _RepaymentPaymentResultState
    implements RepaymentPaymentResultState {
  const factory _RepaymentPaymentResultState(
      {required final LoadingState loadingState,
      required final String deeplink,
      required final String phoneNumber,
      final String? previousRouteName,
      required final Option<
              Either<String, RepaymentTransactionSuccessRouteArgs>>
          failureOrSuccess,
      required final Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      final RepaymentUserPaymentMethod? paymentMethod,
      final List<RepaymentUserPaymentMethod>? paymentMethods,
      final String? transactionId,
      final String? voucherCode}) = _$_RepaymentPaymentResultState;

  @override
  LoadingState get loadingState;
  @override
  String get deeplink;
  @override
  String get phoneNumber;
  @override
  String? get previousRouteName;
  @override
  Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
      get failureOrSuccess;
  @override
  Option<Either<RepaymentFailure, RepaymentTransaction>>
      get failureOrSuccessTransactionResult;
  @override
  RepaymentUserPaymentMethod? get paymentMethod;
  @override
  List<RepaymentUserPaymentMethod>? get paymentMethods;
  @override
  String? get transactionId;
  @override
  String? get voucherCode;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPaymentResultStateCopyWith<_$_RepaymentPaymentResultState>
      get copyWith => throw _privateConstructorUsedError;
}
