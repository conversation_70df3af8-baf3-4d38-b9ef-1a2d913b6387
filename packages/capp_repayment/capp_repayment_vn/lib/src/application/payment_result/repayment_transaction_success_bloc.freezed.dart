// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_transaction_success_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentTransactionSuccessEvent {
  CountryFlavor get countryFlavor => throw _privateConstructorUsedError;
  String get contractNumber => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CountryFlavor countryFlavor, String contractNumber)
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CountryFlavor countryFlavor, String contractNumber)?
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CountryFlavor countryFlavor, String contractNumber)?
        initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentTransactionSuccessEventCopyWith<RepaymentTransactionSuccessEvent>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentTransactionSuccessEventCopyWith<$Res> {
  factory $RepaymentTransactionSuccessEventCopyWith(
          RepaymentTransactionSuccessEvent value,
          $Res Function(RepaymentTransactionSuccessEvent) then) =
      _$RepaymentTransactionSuccessEventCopyWithImpl<$Res,
          RepaymentTransactionSuccessEvent>;
  @useResult
  $Res call({CountryFlavor countryFlavor, String contractNumber});
}

/// @nodoc
class _$RepaymentTransactionSuccessEventCopyWithImpl<$Res,
        $Val extends RepaymentTransactionSuccessEvent>
    implements $RepaymentTransactionSuccessEventCopyWith<$Res> {
  _$RepaymentTransactionSuccessEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryFlavor = null,
    Object? contractNumber = null,
  }) {
    return _then(_value.copyWith(
      countryFlavor: null == countryFlavor
          ? _value.countryFlavor
          : countryFlavor // ignore: cast_nullable_to_non_nullable
              as CountryFlavor,
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res>
    implements $RepaymentTransactionSuccessEventCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({CountryFlavor countryFlavor, String contractNumber});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentTransactionSuccessEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryFlavor = null,
    Object? contractNumber = null,
  }) {
    return _then(_$_Initialize(
      countryFlavor: null == countryFlavor
          ? _value.countryFlavor
          : countryFlavor // ignore: cast_nullable_to_non_nullable
              as CountryFlavor,
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required this.countryFlavor, required this.contractNumber});

  @override
  final CountryFlavor countryFlavor;
  @override
  final String contractNumber;

  @override
  String toString() {
    return 'RepaymentTransactionSuccessEvent.initialize(countryFlavor: $countryFlavor, contractNumber: $contractNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.countryFlavor, countryFlavor) ||
                other.countryFlavor == countryFlavor) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, countryFlavor, contractNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            CountryFlavor countryFlavor, String contractNumber)
        initialize,
  }) {
    return initialize(countryFlavor, contractNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CountryFlavor countryFlavor, String contractNumber)?
        initialize,
  }) {
    return initialize?.call(countryFlavor, contractNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CountryFlavor countryFlavor, String contractNumber)?
        initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(countryFlavor, contractNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentTransactionSuccessEvent {
  const factory _Initialize(
      {required final CountryFlavor countryFlavor,
      required final String contractNumber}) = _$_Initialize;

  @override
  CountryFlavor get countryFlavor;
  @override
  String get contractNumber;
  @override
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentTransactionSuccessState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool get isAdaEligible => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentTransactionSuccessStateCopyWith<RepaymentTransactionSuccessState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentTransactionSuccessStateCopyWith<$Res> {
  factory $RepaymentTransactionSuccessStateCopyWith(
          RepaymentTransactionSuccessState value,
          $Res Function(RepaymentTransactionSuccessState) then) =
      _$RepaymentTransactionSuccessStateCopyWithImpl<$Res,
          RepaymentTransactionSuccessState>;
  @useResult
  $Res call(
      {LoadingState loadingState, bool isAdaEligible, String? contractNumber});
}

/// @nodoc
class _$RepaymentTransactionSuccessStateCopyWithImpl<$Res,
        $Val extends RepaymentTransactionSuccessState>
    implements $RepaymentTransactionSuccessStateCopyWith<$Res> {
  _$RepaymentTransactionSuccessStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isAdaEligible = null,
    Object? contractNumber = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isAdaEligible: null == isAdaEligible
          ? _value.isAdaEligible
          : isAdaEligible // ignore: cast_nullable_to_non_nullable
              as bool,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentTransactionSuccessStateCopyWith<$Res>
    implements $RepaymentTransactionSuccessStateCopyWith<$Res> {
  factory _$$_RepaymentTransactionSuccessStateCopyWith(
          _$_RepaymentTransactionSuccessState value,
          $Res Function(_$_RepaymentTransactionSuccessState) then) =
      __$$_RepaymentTransactionSuccessStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState, bool isAdaEligible, String? contractNumber});
}

/// @nodoc
class __$$_RepaymentTransactionSuccessStateCopyWithImpl<$Res>
    extends _$RepaymentTransactionSuccessStateCopyWithImpl<$Res,
        _$_RepaymentTransactionSuccessState>
    implements _$$_RepaymentTransactionSuccessStateCopyWith<$Res> {
  __$$_RepaymentTransactionSuccessStateCopyWithImpl(
      _$_RepaymentTransactionSuccessState _value,
      $Res Function(_$_RepaymentTransactionSuccessState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isAdaEligible = null,
    Object? contractNumber = freezed,
  }) {
    return _then(_$_RepaymentTransactionSuccessState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isAdaEligible: null == isAdaEligible
          ? _value.isAdaEligible
          : isAdaEligible // ignore: cast_nullable_to_non_nullable
              as bool,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_RepaymentTransactionSuccessState
    implements _RepaymentTransactionSuccessState {
  const _$_RepaymentTransactionSuccessState(
      {this.loadingState = LoadingState.isInitial,
      this.isAdaEligible = false,
      this.contractNumber});

  @override
  @JsonKey()
  final LoadingState loadingState;
  @override
  @JsonKey()
  final bool isAdaEligible;
  @override
  final String? contractNumber;

  @override
  String toString() {
    return 'RepaymentTransactionSuccessState(loadingState: $loadingState, isAdaEligible: $isAdaEligible, contractNumber: $contractNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentTransactionSuccessState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isAdaEligible, isAdaEligible) ||
                other.isAdaEligible == isAdaEligible) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, loadingState, isAdaEligible, contractNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentTransactionSuccessStateCopyWith<
          _$_RepaymentTransactionSuccessState>
      get copyWith => __$$_RepaymentTransactionSuccessStateCopyWithImpl<
          _$_RepaymentTransactionSuccessState>(this, _$identity);
}

abstract class _RepaymentTransactionSuccessState
    implements RepaymentTransactionSuccessState {
  const factory _RepaymentTransactionSuccessState(
      {final LoadingState loadingState,
      final bool isAdaEligible,
      final String? contractNumber}) = _$_RepaymentTransactionSuccessState;

  @override
  LoadingState get loadingState;
  @override
  bool get isAdaEligible;
  @override
  String? get contractNumber;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentTransactionSuccessStateCopyWith<
          _$_RepaymentTransactionSuccessState>
      get copyWith => throw _privateConstructorUsedError;
}
