// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_bank_transfer_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentBankTransferEvent {
  RepaymentContract get selectedContract => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentContract selectedContract) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract selectedContract)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract selectedContract)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentBankTransferEventCopyWith<RepaymentBankTransferEvent>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBankTransferEventCopyWith<$Res> {
  factory $RepaymentBankTransferEventCopyWith(RepaymentBankTransferEvent value,
          $Res Function(RepaymentBankTransferEvent) then) =
      _$RepaymentBankTransferEventCopyWithImpl<$Res,
          RepaymentBankTransferEvent>;
  @useResult
  $Res call({RepaymentContract selectedContract});
}

/// @nodoc
class _$RepaymentBankTransferEventCopyWithImpl<$Res,
        $Val extends RepaymentBankTransferEvent>
    implements $RepaymentBankTransferEventCopyWith<$Res> {
  _$RepaymentBankTransferEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedContract = null,
  }) {
    return _then(_value.copyWith(
      selectedContract: null == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res>
    implements $RepaymentBankTransferEventCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({RepaymentContract selectedContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentBankTransferEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedContract = null,
  }) {
    return _then(_$_Initialize(
      null == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(this.selectedContract);

  @override
  final RepaymentContract selectedContract;

  @override
  String toString() {
    return 'RepaymentBankTransferEvent.initialize(selectedContract: $selectedContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentContract selectedContract) initialize,
  }) {
    return initialize(selectedContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentContract selectedContract)? initialize,
  }) {
    return initialize?.call(selectedContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentContract selectedContract)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(selectedContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentBankTransferEvent {
  const factory _Initialize(final RepaymentContract selectedContract) =
      _$_Initialize;

  @override
  RepaymentContract get selectedContract;
  @override
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentBankTransferState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  Decimal? get totalAmount => throw _privateConstructorUsedError;
  RepaymentContract? get selectedContract => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentBankTransferStateCopyWith<RepaymentBankTransferState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBankTransferStateCopyWith<$Res> {
  factory $RepaymentBankTransferStateCopyWith(RepaymentBankTransferState value,
          $Res Function(RepaymentBankTransferState) then) =
      _$RepaymentBankTransferStateCopyWithImpl<$Res,
          RepaymentBankTransferState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      Decimal? totalAmount,
      RepaymentContract? selectedContract});
}

/// @nodoc
class _$RepaymentBankTransferStateCopyWithImpl<$Res,
        $Val extends RepaymentBankTransferState>
    implements $RepaymentBankTransferStateCopyWith<$Res> {
  _$RepaymentBankTransferStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? totalAmount = freezed,
    Object? selectedContract = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentBankTransferStateCopyWith<$Res>
    implements $RepaymentBankTransferStateCopyWith<$Res> {
  factory _$$_RepaymentBankTransferStateCopyWith(
          _$_RepaymentBankTransferState value,
          $Res Function(_$_RepaymentBankTransferState) then) =
      __$$_RepaymentBankTransferStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      Decimal? totalAmount,
      RepaymentContract? selectedContract});
}

/// @nodoc
class __$$_RepaymentBankTransferStateCopyWithImpl<$Res>
    extends _$RepaymentBankTransferStateCopyWithImpl<$Res,
        _$_RepaymentBankTransferState>
    implements _$$_RepaymentBankTransferStateCopyWith<$Res> {
  __$$_RepaymentBankTransferStateCopyWithImpl(
      _$_RepaymentBankTransferState _value,
      $Res Function(_$_RepaymentBankTransferState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? totalAmount = freezed,
    Object? selectedContract = freezed,
  }) {
    return _then(_$_RepaymentBankTransferState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
    ));
  }
}

/// @nodoc

class _$_RepaymentBankTransferState implements _RepaymentBankTransferState {
  const _$_RepaymentBankTransferState(
      {required this.loadingState,
      required this.totalAmount,
      required this.selectedContract});

  @override
  final LoadingState loadingState;
  @override
  final Decimal? totalAmount;
  @override
  final RepaymentContract? selectedContract;

  @override
  String toString() {
    return 'RepaymentBankTransferState(loadingState: $loadingState, totalAmount: $totalAmount, selectedContract: $selectedContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentBankTransferState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, loadingState, totalAmount, selectedContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentBankTransferStateCopyWith<_$_RepaymentBankTransferState>
      get copyWith => __$$_RepaymentBankTransferStateCopyWithImpl<
          _$_RepaymentBankTransferState>(this, _$identity);
}

abstract class _RepaymentBankTransferState
    implements RepaymentBankTransferState {
  const factory _RepaymentBankTransferState(
          {required final LoadingState loadingState,
          required final Decimal? totalAmount,
          required final RepaymentContract? selectedContract}) =
      _$_RepaymentBankTransferState;

  @override
  LoadingState get loadingState;
  @override
  Decimal? get totalAmount;
  @override
  RepaymentContract? get selectedContract;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentBankTransferStateCopyWith<_$_RepaymentBankTransferState>
      get copyWith => throw _privateConstructorUsedError;
}
