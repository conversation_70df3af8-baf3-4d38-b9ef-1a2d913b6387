part of 'repayment_bank_transfer_bloc.dart';

@freezed
class RepaymentBankTransferState with _$RepaymentBankTransferState {
  const factory RepaymentBankTransferState({
    required LoadingState loadingState,
    required Decimal? totalAmount,
    required RepaymentContract? selectedContract,
  }) = _RepaymentBankTransferState;

  factory RepaymentBankTransferState.initialize() => RepaymentBankTransferState(
        loadingState: LoadingState.isInitial,
        totalAmount: Decimal.zero,
        selectedContract: null,
      );
}
