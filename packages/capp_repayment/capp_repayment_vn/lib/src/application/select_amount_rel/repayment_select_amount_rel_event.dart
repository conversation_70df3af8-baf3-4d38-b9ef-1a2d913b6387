part of 'repayment_select_amount_rel_bloc.dart';

@freezed
class RepaymentSelectAmountRelEvent with _$RepaymentSelectAmountRelEvent {
  const factory RepaymentSelectAmountRelEvent.initialize({
    required RepaymentRelContract repaymentRelContract,
  }) = _Initialize;

  const factory RepaymentSelectAmountRelEvent.selectTotalDueAmount() = _SelectTotalDueAmount;

  const factory RepaymentSelectAmountRelEvent.selectMinimumDueAmount() = _SelectMinimumDueAmount;

  const factory RepaymentSelectAmountRelEvent.selectCustomAmount() = _SelectCustomAmount;

  const factory RepaymentSelectAmountRelEvent.setAmount(Decimal? amount) = _SetAmount;

  const factory RepaymentSelectAmountRelEvent.fetchVirtualAccount({
    required String contractNumber,
    required Decimal selectedAmount,
  }) = _FetchVirtualAccount;

  const factory RepaymentSelectAmountRelEvent.resetVirtualAccount() = _ResetVirtualAccount;
}
