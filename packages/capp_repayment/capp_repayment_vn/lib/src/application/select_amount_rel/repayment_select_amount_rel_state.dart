part of 'repayment_select_amount_rel_bloc.dart';

@freezed
class RepaymentSelectAmountRelState with _$RepaymentSelectAmountRelState {
  const factory RepaymentSelectAmountRelState({
    bool? isError,
    required LoadingState loadingState,
    required RepaymentRelContract? repaymentRelContract,
    required Decimal minimumThresholdAmount,
    required Decimal? selectedAmount,
    required bool isTotalDueAmountSelected,
    required bool isMinimumDueAmountSelected,
    required bool isCustomAmountSelected,
    required bool isLessThanMinimumDueAmount,
    required bool isLessThanMinimumThresholdAmount,
    required bool isMinimumDueAmountLessThanThresholdAmount,
    required bool isPayOff,
    required bool isShowMinimumDueAmountOption,
    RepaymentContractVirtualAccount? contractVirtualAccount,
    required Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>> failureOrSuccessVirtualAccount,
  }) = _RepaymentSelectAmountRelState;

  factory RepaymentSelectAmountRelState.initialize() => RepaymentSelectAmountRelState(
        isError: false,
        loadingState: LoadingState.isInitial,
        repaymentRelContract: null,
        minimumThresholdAmount: Decimal.fromInt(0),
        selectedAmount: null,
        isTotalDueAmountSelected: false,
        isMinimumDueAmountSelected: false,
        isCustomAmountSelected: false,
        isLessThanMinimumDueAmount: false,
        isLessThanMinimumThresholdAmount: false,
        isMinimumDueAmountLessThanThresholdAmount: false,
        isPayOff: false,
        isShowMinimumDueAmountOption: true,
        failureOrSuccessVirtualAccount: none(),
      );
}
