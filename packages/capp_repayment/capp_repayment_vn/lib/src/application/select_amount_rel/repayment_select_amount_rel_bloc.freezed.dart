// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_select_amount_rel_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentSelectAmountRelEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentSelectAmountRelEventCopyWith<$Res> {
  factory $RepaymentSelectAmountRelEventCopyWith(
          RepaymentSelectAmountRelEvent value,
          $Res Function(RepaymentSelectAmountRelEvent) then) =
      _$RepaymentSelectAmountRelEventCopyWithImpl<$Res,
          RepaymentSelectAmountRelEvent>;
}

/// @nodoc
class _$RepaymentSelectAmountRelEventCopyWithImpl<$Res,
        $Val extends RepaymentSelectAmountRelEvent>
    implements $RepaymentSelectAmountRelEventCopyWith<$Res> {
  _$RepaymentSelectAmountRelEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentRelContract repaymentRelContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repaymentRelContract = null,
  }) {
    return _then(_$_Initialize(
      repaymentRelContract: null == repaymentRelContract
          ? _value.repaymentRelContract
          : repaymentRelContract // ignore: cast_nullable_to_non_nullable
              as RepaymentRelContract,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.repaymentRelContract});

  @override
  final RepaymentRelContract repaymentRelContract;

  @override
  String toString() {
    return 'RepaymentSelectAmountRelEvent.initialize(repaymentRelContract: $repaymentRelContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.repaymentRelContract, repaymentRelContract) ||
                other.repaymentRelContract == repaymentRelContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, repaymentRelContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return initialize(repaymentRelContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return initialize?.call(repaymentRelContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(repaymentRelContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentSelectAmountRelEvent {
  const factory _Initialize(
          {required final RepaymentRelContract repaymentRelContract}) =
      _$_Initialize;

  RepaymentRelContract get repaymentRelContract;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectTotalDueAmountCopyWith<$Res> {
  factory _$$_SelectTotalDueAmountCopyWith(_$_SelectTotalDueAmount value,
          $Res Function(_$_SelectTotalDueAmount) then) =
      __$$_SelectTotalDueAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SelectTotalDueAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelEventCopyWithImpl<$Res,
        _$_SelectTotalDueAmount>
    implements _$$_SelectTotalDueAmountCopyWith<$Res> {
  __$$_SelectTotalDueAmountCopyWithImpl(_$_SelectTotalDueAmount _value,
      $Res Function(_$_SelectTotalDueAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SelectTotalDueAmount implements _SelectTotalDueAmount {
  const _$_SelectTotalDueAmount();

  @override
  String toString() {
    return 'RepaymentSelectAmountRelEvent.selectTotalDueAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SelectTotalDueAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return selectTotalDueAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return selectTotalDueAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectTotalDueAmount != null) {
      return selectTotalDueAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return selectTotalDueAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return selectTotalDueAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectTotalDueAmount != null) {
      return selectTotalDueAmount(this);
    }
    return orElse();
  }
}

abstract class _SelectTotalDueAmount implements RepaymentSelectAmountRelEvent {
  const factory _SelectTotalDueAmount() = _$_SelectTotalDueAmount;
}

/// @nodoc
abstract class _$$_SelectMinimumDueAmountCopyWith<$Res> {
  factory _$$_SelectMinimumDueAmountCopyWith(_$_SelectMinimumDueAmount value,
          $Res Function(_$_SelectMinimumDueAmount) then) =
      __$$_SelectMinimumDueAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SelectMinimumDueAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelEventCopyWithImpl<$Res,
        _$_SelectMinimumDueAmount>
    implements _$$_SelectMinimumDueAmountCopyWith<$Res> {
  __$$_SelectMinimumDueAmountCopyWithImpl(_$_SelectMinimumDueAmount _value,
      $Res Function(_$_SelectMinimumDueAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SelectMinimumDueAmount implements _SelectMinimumDueAmount {
  const _$_SelectMinimumDueAmount();

  @override
  String toString() {
    return 'RepaymentSelectAmountRelEvent.selectMinimumDueAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectMinimumDueAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return selectMinimumDueAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return selectMinimumDueAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectMinimumDueAmount != null) {
      return selectMinimumDueAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return selectMinimumDueAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return selectMinimumDueAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectMinimumDueAmount != null) {
      return selectMinimumDueAmount(this);
    }
    return orElse();
  }
}

abstract class _SelectMinimumDueAmount
    implements RepaymentSelectAmountRelEvent {
  const factory _SelectMinimumDueAmount() = _$_SelectMinimumDueAmount;
}

/// @nodoc
abstract class _$$_SelectCustomAmountCopyWith<$Res> {
  factory _$$_SelectCustomAmountCopyWith(_$_SelectCustomAmount value,
          $Res Function(_$_SelectCustomAmount) then) =
      __$$_SelectCustomAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SelectCustomAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelEventCopyWithImpl<$Res,
        _$_SelectCustomAmount> implements _$$_SelectCustomAmountCopyWith<$Res> {
  __$$_SelectCustomAmountCopyWithImpl(
      _$_SelectCustomAmount _value, $Res Function(_$_SelectCustomAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SelectCustomAmount implements _SelectCustomAmount {
  const _$_SelectCustomAmount();

  @override
  String toString() {
    return 'RepaymentSelectAmountRelEvent.selectCustomAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SelectCustomAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return selectCustomAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return selectCustomAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectCustomAmount != null) {
      return selectCustomAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return selectCustomAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return selectCustomAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectCustomAmount != null) {
      return selectCustomAmount(this);
    }
    return orElse();
  }
}

abstract class _SelectCustomAmount implements RepaymentSelectAmountRelEvent {
  const factory _SelectCustomAmount() = _$_SelectCustomAmount;
}

/// @nodoc
abstract class _$$_SetAmountCopyWith<$Res> {
  factory _$$_SetAmountCopyWith(
          _$_SetAmount value, $Res Function(_$_SetAmount) then) =
      __$$_SetAmountCopyWithImpl<$Res>;
  @useResult
  $Res call({Decimal? amount});
}

/// @nodoc
class __$$_SetAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelEventCopyWithImpl<$Res, _$_SetAmount>
    implements _$$_SetAmountCopyWith<$Res> {
  __$$_SetAmountCopyWithImpl(
      _$_SetAmount _value, $Res Function(_$_SetAmount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
  }) {
    return _then(_$_SetAmount(
      freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
    ));
  }
}

/// @nodoc

class _$_SetAmount implements _SetAmount {
  const _$_SetAmount(this.amount);

  @override
  final Decimal? amount;

  @override
  String toString() {
    return 'RepaymentSelectAmountRelEvent.setAmount(amount: $amount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetAmount &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      __$$_SetAmountCopyWithImpl<_$_SetAmount>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return setAmount(amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return setAmount?.call(amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return setAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return setAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(this);
    }
    return orElse();
  }
}

abstract class _SetAmount implements RepaymentSelectAmountRelEvent {
  const factory _SetAmount(final Decimal? amount) = _$_SetAmount;

  Decimal? get amount;
  @JsonKey(ignore: true)
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FetchVirtualAccountCopyWith<$Res> {
  factory _$$_FetchVirtualAccountCopyWith(_$_FetchVirtualAccount value,
          $Res Function(_$_FetchVirtualAccount) then) =
      __$$_FetchVirtualAccountCopyWithImpl<$Res>;
  @useResult
  $Res call({String contractNumber, Decimal selectedAmount});
}

/// @nodoc
class __$$_FetchVirtualAccountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelEventCopyWithImpl<$Res,
        _$_FetchVirtualAccount>
    implements _$$_FetchVirtualAccountCopyWith<$Res> {
  __$$_FetchVirtualAccountCopyWithImpl(_$_FetchVirtualAccount _value,
      $Res Function(_$_FetchVirtualAccount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
    Object? selectedAmount = null,
  }) {
    return _then(_$_FetchVirtualAccount(
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
      selectedAmount: null == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
    ));
  }
}

/// @nodoc

class _$_FetchVirtualAccount implements _FetchVirtualAccount {
  const _$_FetchVirtualAccount(
      {required this.contractNumber, required this.selectedAmount});

  @override
  final String contractNumber;
  @override
  final Decimal selectedAmount;

  @override
  String toString() {
    return 'RepaymentSelectAmountRelEvent.fetchVirtualAccount(contractNumber: $contractNumber, selectedAmount: $selectedAmount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FetchVirtualAccount &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractNumber, selectedAmount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FetchVirtualAccountCopyWith<_$_FetchVirtualAccount> get copyWith =>
      __$$_FetchVirtualAccountCopyWithImpl<_$_FetchVirtualAccount>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return fetchVirtualAccount(contractNumber, selectedAmount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return fetchVirtualAccount?.call(contractNumber, selectedAmount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (fetchVirtualAccount != null) {
      return fetchVirtualAccount(contractNumber, selectedAmount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return fetchVirtualAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return fetchVirtualAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (fetchVirtualAccount != null) {
      return fetchVirtualAccount(this);
    }
    return orElse();
  }
}

abstract class _FetchVirtualAccount implements RepaymentSelectAmountRelEvent {
  const factory _FetchVirtualAccount(
      {required final String contractNumber,
      required final Decimal selectedAmount}) = _$_FetchVirtualAccount;

  String get contractNumber;
  Decimal get selectedAmount;
  @JsonKey(ignore: true)
  _$$_FetchVirtualAccountCopyWith<_$_FetchVirtualAccount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetVirtualAccountCopyWith<$Res> {
  factory _$$_ResetVirtualAccountCopyWith(_$_ResetVirtualAccount value,
          $Res Function(_$_ResetVirtualAccount) then) =
      __$$_ResetVirtualAccountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetVirtualAccountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelEventCopyWithImpl<$Res,
        _$_ResetVirtualAccount>
    implements _$$_ResetVirtualAccountCopyWith<$Res> {
  __$$_ResetVirtualAccountCopyWithImpl(_$_ResetVirtualAccount _value,
      $Res Function(_$_ResetVirtualAccount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetVirtualAccount implements _ResetVirtualAccount {
  const _$_ResetVirtualAccount();

  @override
  String toString() {
    return 'RepaymentSelectAmountRelEvent.resetVirtualAccount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetVirtualAccount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentRelContract repaymentRelContract)
        initialize,
    required TResult Function() selectTotalDueAmount,
    required TResult Function() selectMinimumDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return resetVirtualAccount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult? Function()? selectTotalDueAmount,
    TResult? Function()? selectMinimumDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return resetVirtualAccount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentRelContract repaymentRelContract)? initialize,
    TResult Function()? selectTotalDueAmount,
    TResult Function()? selectMinimumDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (resetVirtualAccount != null) {
      return resetVirtualAccount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectTotalDueAmount value) selectTotalDueAmount,
    required TResult Function(_SelectMinimumDueAmount value)
        selectMinimumDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return resetVirtualAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult? Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return resetVirtualAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectTotalDueAmount value)? selectTotalDueAmount,
    TResult Function(_SelectMinimumDueAmount value)? selectMinimumDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (resetVirtualAccount != null) {
      return resetVirtualAccount(this);
    }
    return orElse();
  }
}

abstract class _ResetVirtualAccount implements RepaymentSelectAmountRelEvent {
  const factory _ResetVirtualAccount() = _$_ResetVirtualAccount;
}

/// @nodoc
mixin _$RepaymentSelectAmountRelState {
  bool? get isError => throw _privateConstructorUsedError;
  LoadingState get loadingState => throw _privateConstructorUsedError;
  RepaymentRelContract? get repaymentRelContract =>
      throw _privateConstructorUsedError;
  Decimal get minimumThresholdAmount => throw _privateConstructorUsedError;
  Decimal? get selectedAmount => throw _privateConstructorUsedError;
  bool get isTotalDueAmountSelected => throw _privateConstructorUsedError;
  bool get isMinimumDueAmountSelected => throw _privateConstructorUsedError;
  bool get isCustomAmountSelected => throw _privateConstructorUsedError;
  bool get isLessThanMinimumDueAmount => throw _privateConstructorUsedError;
  bool get isLessThanMinimumThresholdAmount =>
      throw _privateConstructorUsedError;
  bool get isMinimumDueAmountLessThanThresholdAmount =>
      throw _privateConstructorUsedError;
  bool get isPayOff => throw _privateConstructorUsedError;
  bool get isShowMinimumDueAmountOption => throw _privateConstructorUsedError;
  RepaymentContractVirtualAccount? get contractVirtualAccount =>
      throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      get failureOrSuccessVirtualAccount => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentSelectAmountRelStateCopyWith<RepaymentSelectAmountRelState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentSelectAmountRelStateCopyWith<$Res> {
  factory $RepaymentSelectAmountRelStateCopyWith(
          RepaymentSelectAmountRelState value,
          $Res Function(RepaymentSelectAmountRelState) then) =
      _$RepaymentSelectAmountRelStateCopyWithImpl<$Res,
          RepaymentSelectAmountRelState>;
  @useResult
  $Res call(
      {bool? isError,
      LoadingState loadingState,
      RepaymentRelContract? repaymentRelContract,
      Decimal minimumThresholdAmount,
      Decimal? selectedAmount,
      bool isTotalDueAmountSelected,
      bool isMinimumDueAmountSelected,
      bool isCustomAmountSelected,
      bool isLessThanMinimumDueAmount,
      bool isLessThanMinimumThresholdAmount,
      bool isMinimumDueAmountLessThanThresholdAmount,
      bool isPayOff,
      bool isShowMinimumDueAmountOption,
      RepaymentContractVirtualAccount? contractVirtualAccount,
      Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount});
}

/// @nodoc
class _$RepaymentSelectAmountRelStateCopyWithImpl<$Res,
        $Val extends RepaymentSelectAmountRelState>
    implements $RepaymentSelectAmountRelStateCopyWith<$Res> {
  _$RepaymentSelectAmountRelStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isError = freezed,
    Object? loadingState = null,
    Object? repaymentRelContract = freezed,
    Object? minimumThresholdAmount = null,
    Object? selectedAmount = freezed,
    Object? isTotalDueAmountSelected = null,
    Object? isMinimumDueAmountSelected = null,
    Object? isCustomAmountSelected = null,
    Object? isLessThanMinimumDueAmount = null,
    Object? isLessThanMinimumThresholdAmount = null,
    Object? isMinimumDueAmountLessThanThresholdAmount = null,
    Object? isPayOff = null,
    Object? isShowMinimumDueAmountOption = null,
    Object? contractVirtualAccount = freezed,
    Object? failureOrSuccessVirtualAccount = null,
  }) {
    return _then(_value.copyWith(
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      repaymentRelContract: freezed == repaymentRelContract
          ? _value.repaymentRelContract
          : repaymentRelContract // ignore: cast_nullable_to_non_nullable
              as RepaymentRelContract?,
      minimumThresholdAmount: null == minimumThresholdAmount
          ? _value.minimumThresholdAmount
          : minimumThresholdAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      isTotalDueAmountSelected: null == isTotalDueAmountSelected
          ? _value.isTotalDueAmountSelected
          : isTotalDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isMinimumDueAmountSelected: null == isMinimumDueAmountSelected
          ? _value.isMinimumDueAmountSelected
          : isMinimumDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountSelected: null == isCustomAmountSelected
          ? _value.isCustomAmountSelected
          : isCustomAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanMinimumDueAmount: null == isLessThanMinimumDueAmount
          ? _value.isLessThanMinimumDueAmount
          : isLessThanMinimumDueAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanMinimumThresholdAmount: null == isLessThanMinimumThresholdAmount
          ? _value.isLessThanMinimumThresholdAmount
          : isLessThanMinimumThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isMinimumDueAmountLessThanThresholdAmount: null ==
              isMinimumDueAmountLessThanThresholdAmount
          ? _value.isMinimumDueAmountLessThanThresholdAmount
          : isMinimumDueAmountLessThanThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isPayOff: null == isPayOff
          ? _value.isPayOff
          : isPayOff // ignore: cast_nullable_to_non_nullable
              as bool,
      isShowMinimumDueAmountOption: null == isShowMinimumDueAmountOption
          ? _value.isShowMinimumDueAmountOption
          : isShowMinimumDueAmountOption // ignore: cast_nullable_to_non_nullable
              as bool,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      failureOrSuccessVirtualAccount: null == failureOrSuccessVirtualAccount
          ? _value.failureOrSuccessVirtualAccount
          : failureOrSuccessVirtualAccount // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentContractVirtualAccount>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentSelectAmountRelStateCopyWith<$Res>
    implements $RepaymentSelectAmountRelStateCopyWith<$Res> {
  factory _$$_RepaymentSelectAmountRelStateCopyWith(
          _$_RepaymentSelectAmountRelState value,
          $Res Function(_$_RepaymentSelectAmountRelState) then) =
      __$$_RepaymentSelectAmountRelStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isError,
      LoadingState loadingState,
      RepaymentRelContract? repaymentRelContract,
      Decimal minimumThresholdAmount,
      Decimal? selectedAmount,
      bool isTotalDueAmountSelected,
      bool isMinimumDueAmountSelected,
      bool isCustomAmountSelected,
      bool isLessThanMinimumDueAmount,
      bool isLessThanMinimumThresholdAmount,
      bool isMinimumDueAmountLessThanThresholdAmount,
      bool isPayOff,
      bool isShowMinimumDueAmountOption,
      RepaymentContractVirtualAccount? contractVirtualAccount,
      Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount});
}

/// @nodoc
class __$$_RepaymentSelectAmountRelStateCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountRelStateCopyWithImpl<$Res,
        _$_RepaymentSelectAmountRelState>
    implements _$$_RepaymentSelectAmountRelStateCopyWith<$Res> {
  __$$_RepaymentSelectAmountRelStateCopyWithImpl(
      _$_RepaymentSelectAmountRelState _value,
      $Res Function(_$_RepaymentSelectAmountRelState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isError = freezed,
    Object? loadingState = null,
    Object? repaymentRelContract = freezed,
    Object? minimumThresholdAmount = null,
    Object? selectedAmount = freezed,
    Object? isTotalDueAmountSelected = null,
    Object? isMinimumDueAmountSelected = null,
    Object? isCustomAmountSelected = null,
    Object? isLessThanMinimumDueAmount = null,
    Object? isLessThanMinimumThresholdAmount = null,
    Object? isMinimumDueAmountLessThanThresholdAmount = null,
    Object? isPayOff = null,
    Object? isShowMinimumDueAmountOption = null,
    Object? contractVirtualAccount = freezed,
    Object? failureOrSuccessVirtualAccount = null,
  }) {
    return _then(_$_RepaymentSelectAmountRelState(
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      repaymentRelContract: freezed == repaymentRelContract
          ? _value.repaymentRelContract
          : repaymentRelContract // ignore: cast_nullable_to_non_nullable
              as RepaymentRelContract?,
      minimumThresholdAmount: null == minimumThresholdAmount
          ? _value.minimumThresholdAmount
          : minimumThresholdAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      isTotalDueAmountSelected: null == isTotalDueAmountSelected
          ? _value.isTotalDueAmountSelected
          : isTotalDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isMinimumDueAmountSelected: null == isMinimumDueAmountSelected
          ? _value.isMinimumDueAmountSelected
          : isMinimumDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountSelected: null == isCustomAmountSelected
          ? _value.isCustomAmountSelected
          : isCustomAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanMinimumDueAmount: null == isLessThanMinimumDueAmount
          ? _value.isLessThanMinimumDueAmount
          : isLessThanMinimumDueAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanMinimumThresholdAmount: null == isLessThanMinimumThresholdAmount
          ? _value.isLessThanMinimumThresholdAmount
          : isLessThanMinimumThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isMinimumDueAmountLessThanThresholdAmount: null ==
              isMinimumDueAmountLessThanThresholdAmount
          ? _value.isMinimumDueAmountLessThanThresholdAmount
          : isMinimumDueAmountLessThanThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isPayOff: null == isPayOff
          ? _value.isPayOff
          : isPayOff // ignore: cast_nullable_to_non_nullable
              as bool,
      isShowMinimumDueAmountOption: null == isShowMinimumDueAmountOption
          ? _value.isShowMinimumDueAmountOption
          : isShowMinimumDueAmountOption // ignore: cast_nullable_to_non_nullable
              as bool,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
      failureOrSuccessVirtualAccount: null == failureOrSuccessVirtualAccount
          ? _value.failureOrSuccessVirtualAccount
          : failureOrSuccessVirtualAccount // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentContractVirtualAccount>>,
    ));
  }
}

/// @nodoc

class _$_RepaymentSelectAmountRelState
    implements _RepaymentSelectAmountRelState {
  const _$_RepaymentSelectAmountRelState(
      {this.isError,
      required this.loadingState,
      required this.repaymentRelContract,
      required this.minimumThresholdAmount,
      required this.selectedAmount,
      required this.isTotalDueAmountSelected,
      required this.isMinimumDueAmountSelected,
      required this.isCustomAmountSelected,
      required this.isLessThanMinimumDueAmount,
      required this.isLessThanMinimumThresholdAmount,
      required this.isMinimumDueAmountLessThanThresholdAmount,
      required this.isPayOff,
      required this.isShowMinimumDueAmountOption,
      this.contractVirtualAccount,
      required this.failureOrSuccessVirtualAccount});

  @override
  final bool? isError;
  @override
  final LoadingState loadingState;
  @override
  final RepaymentRelContract? repaymentRelContract;
  @override
  final Decimal minimumThresholdAmount;
  @override
  final Decimal? selectedAmount;
  @override
  final bool isTotalDueAmountSelected;
  @override
  final bool isMinimumDueAmountSelected;
  @override
  final bool isCustomAmountSelected;
  @override
  final bool isLessThanMinimumDueAmount;
  @override
  final bool isLessThanMinimumThresholdAmount;
  @override
  final bool isMinimumDueAmountLessThanThresholdAmount;
  @override
  final bool isPayOff;
  @override
  final bool isShowMinimumDueAmountOption;
  @override
  final RepaymentContractVirtualAccount? contractVirtualAccount;
  @override
  final Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      failureOrSuccessVirtualAccount;

  @override
  String toString() {
    return 'RepaymentSelectAmountRelState(isError: $isError, loadingState: $loadingState, repaymentRelContract: $repaymentRelContract, minimumThresholdAmount: $minimumThresholdAmount, selectedAmount: $selectedAmount, isTotalDueAmountSelected: $isTotalDueAmountSelected, isMinimumDueAmountSelected: $isMinimumDueAmountSelected, isCustomAmountSelected: $isCustomAmountSelected, isLessThanMinimumDueAmount: $isLessThanMinimumDueAmount, isLessThanMinimumThresholdAmount: $isLessThanMinimumThresholdAmount, isMinimumDueAmountLessThanThresholdAmount: $isMinimumDueAmountLessThanThresholdAmount, isPayOff: $isPayOff, isShowMinimumDueAmountOption: $isShowMinimumDueAmountOption, contractVirtualAccount: $contractVirtualAccount, failureOrSuccessVirtualAccount: $failureOrSuccessVirtualAccount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentSelectAmountRelState &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.repaymentRelContract, repaymentRelContract) ||
                other.repaymentRelContract == repaymentRelContract) &&
            (identical(other.minimumThresholdAmount, minimumThresholdAmount) ||
                other.minimumThresholdAmount == minimumThresholdAmount) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.isTotalDueAmountSelected, isTotalDueAmountSelected) ||
                other.isTotalDueAmountSelected == isTotalDueAmountSelected) &&
            (identical(other.isMinimumDueAmountSelected, isMinimumDueAmountSelected) ||
                other.isMinimumDueAmountSelected ==
                    isMinimumDueAmountSelected) &&
            (identical(other.isCustomAmountSelected, isCustomAmountSelected) ||
                other.isCustomAmountSelected == isCustomAmountSelected) &&
            (identical(other.isLessThanMinimumDueAmount, isLessThanMinimumDueAmount) ||
                other.isLessThanMinimumDueAmount ==
                    isLessThanMinimumDueAmount) &&
            (identical(other.isLessThanMinimumThresholdAmount,
                    isLessThanMinimumThresholdAmount) ||
                other.isLessThanMinimumThresholdAmount ==
                    isLessThanMinimumThresholdAmount) &&
            (identical(other.isMinimumDueAmountLessThanThresholdAmount,
                    isMinimumDueAmountLessThanThresholdAmount) ||
                other.isMinimumDueAmountLessThanThresholdAmount ==
                    isMinimumDueAmountLessThanThresholdAmount) &&
            (identical(other.isPayOff, isPayOff) ||
                other.isPayOff == isPayOff) &&
            (identical(other.isShowMinimumDueAmountOption, isShowMinimumDueAmountOption) ||
                other.isShowMinimumDueAmountOption ==
                    isShowMinimumDueAmountOption) &&
            (identical(other.contractVirtualAccount, contractVirtualAccount) ||
                other.contractVirtualAccount == contractVirtualAccount) &&
            (identical(other.failureOrSuccessVirtualAccount, failureOrSuccessVirtualAccount) ||
                other.failureOrSuccessVirtualAccount ==
                    failureOrSuccessVirtualAccount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isError,
      loadingState,
      repaymentRelContract,
      minimumThresholdAmount,
      selectedAmount,
      isTotalDueAmountSelected,
      isMinimumDueAmountSelected,
      isCustomAmountSelected,
      isLessThanMinimumDueAmount,
      isLessThanMinimumThresholdAmount,
      isMinimumDueAmountLessThanThresholdAmount,
      isPayOff,
      isShowMinimumDueAmountOption,
      contractVirtualAccount,
      failureOrSuccessVirtualAccount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentSelectAmountRelStateCopyWith<_$_RepaymentSelectAmountRelState>
      get copyWith => __$$_RepaymentSelectAmountRelStateCopyWithImpl<
          _$_RepaymentSelectAmountRelState>(this, _$identity);
}

abstract class _RepaymentSelectAmountRelState
    implements RepaymentSelectAmountRelState {
  const factory _RepaymentSelectAmountRelState(
      {final bool? isError,
      required final LoadingState loadingState,
      required final RepaymentRelContract? repaymentRelContract,
      required final Decimal minimumThresholdAmount,
      required final Decimal? selectedAmount,
      required final bool isTotalDueAmountSelected,
      required final bool isMinimumDueAmountSelected,
      required final bool isCustomAmountSelected,
      required final bool isLessThanMinimumDueAmount,
      required final bool isLessThanMinimumThresholdAmount,
      required final bool isMinimumDueAmountLessThanThresholdAmount,
      required final bool isPayOff,
      required final bool isShowMinimumDueAmountOption,
      final RepaymentContractVirtualAccount? contractVirtualAccount,
      required final Option<
              Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount}) = _$_RepaymentSelectAmountRelState;

  @override
  bool? get isError;
  @override
  LoadingState get loadingState;
  @override
  RepaymentRelContract? get repaymentRelContract;
  @override
  Decimal get minimumThresholdAmount;
  @override
  Decimal? get selectedAmount;
  @override
  bool get isTotalDueAmountSelected;
  @override
  bool get isMinimumDueAmountSelected;
  @override
  bool get isCustomAmountSelected;
  @override
  bool get isLessThanMinimumDueAmount;
  @override
  bool get isLessThanMinimumThresholdAmount;
  @override
  bool get isMinimumDueAmountLessThanThresholdAmount;
  @override
  bool get isPayOff;
  @override
  bool get isShowMinimumDueAmountOption;
  @override
  RepaymentContractVirtualAccount? get contractVirtualAccount;
  @override
  Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      get failureOrSuccessVirtualAccount;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentSelectAmountRelStateCopyWith<_$_RepaymentSelectAmountRelState>
      get copyWith => throw _privateConstructorUsedError;
}
