import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_repayment.dart';

part 'repayment_select_amount_rel_bloc.freezed.dart';
part 'repayment_select_amount_rel_event.dart';
part 'repayment_select_amount_rel_state.dart';

class RepaymentSelectAmountRelBloc extends Bloc<RepaymentSelectAmountRelEvent, RepaymentSelectAmountRelState> {
  final IRepaymentRepository repaymentRepository;
  final Logger logger;
  final defaultMinThresholdAmount = Decimal.parse('50000');

  RepaymentSelectAmountRelBloc({required this.repaymentRepository, required this.logger})
      : super(RepaymentSelectAmountRelState.initialize()) {
    on<RepaymentSelectAmountRelEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          selectTotalDueAmount: (e) => _selectTotalDueAmount(e, emit),
          selectMinimumDueAmount: (e) => _selectMinimumDueAmount(e, emit),
          selectCustomAmount: (e) => _selectCustomAmount(e, emit),
          setAmount: (e) => _setAmount(e, emit),
          fetchVirtualAccount: (e) => _fetchVirtualAccount(e, emit),
          resetVirtualAccount: (e) => _resetVirtualAccount(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentSelectAmountRelState> emit,
  ) async {
    final isPayOff = e.repaymentRelContract.totalAmountDue! == Decimal.zero;
    final isShowMinimumDueAmountOption =
        !((e.repaymentRelContract.gmaProductType ?? '') == RepaymentGmaProductType.bnpl.id);
    Decimal minimumThresholdAmount;
    if (e.repaymentRelContract.totalAmountDue! < defaultMinThresholdAmount && !isPayOff) {
      minimumThresholdAmount = e.repaymentRelContract.totalAmountDue!;
    } else {
      minimumThresholdAmount = defaultMinThresholdAmount;
    }
    final isMinimumDueAmountLessThanThresholdAmount = e.repaymentRelContract.minimumDueAmount! < minimumThresholdAmount;
    emit(
      state.copyWith(
        repaymentRelContract: e.repaymentRelContract,
        minimumThresholdAmount: minimumThresholdAmount,
        selectedAmount: null,
        isTotalDueAmountSelected: false,
        isMinimumDueAmountSelected: false,
        isCustomAmountSelected: false,
        isLessThanMinimumThresholdAmount: false,
        isLessThanMinimumDueAmount: false,
        isMinimumDueAmountLessThanThresholdAmount: isMinimumDueAmountLessThanThresholdAmount,
        isPayOff: isPayOff,
        isShowMinimumDueAmountOption: isShowMinimumDueAmountOption,
      ),
    );
    if (isShowMinimumDueAmountOption) {
      if (!isPayOff) {
        if (isMinimumDueAmountLessThanThresholdAmount) {
          add(const RepaymentSelectAmountRelEvent.selectTotalDueAmount());
        } else {
          add(const RepaymentSelectAmountRelEvent.selectMinimumDueAmount());
        }
      } else {
        add(const RepaymentSelectAmountRelEvent.selectCustomAmount());
      }
    } else {
      if (!isPayOff) {
        add(const RepaymentSelectAmountRelEvent.selectTotalDueAmount());
      } else {
        add(const RepaymentSelectAmountRelEvent.selectCustomAmount());
      }
    }
  }

  Future<void> _selectTotalDueAmount(
    _SelectTotalDueAmount e,
    Emitter<RepaymentSelectAmountRelState> emit,
  ) async {
    emit(
      state.copyWith(
        isTotalDueAmountSelected: true,
        isCustomAmountSelected: false,
        isMinimumDueAmountSelected: false,
        isLessThanMinimumThresholdAmount: false,
        isLessThanMinimumDueAmount: false,
        selectedAmount: state.repaymentRelContract!.totalAmountDue,
      ),
    );
  }

  Future<void> _selectMinimumDueAmount(
    _SelectMinimumDueAmount e,
    Emitter<RepaymentSelectAmountRelState> emit,
  ) async {
    emit(
      state.copyWith(
        isTotalDueAmountSelected: false,
        isCustomAmountSelected: false,
        isMinimumDueAmountSelected: true,
        isLessThanMinimumThresholdAmount: false,
        isLessThanMinimumDueAmount: false,
        selectedAmount: state.repaymentRelContract!.minimumDueAmount,
      ),
    );
  }

  Future<void> _selectCustomAmount(
    _SelectCustomAmount e,
    Emitter<RepaymentSelectAmountRelState> emit,
  ) async {
    emit(
      state.copyWith(
        isTotalDueAmountSelected: false,
        isCustomAmountSelected: true,
        isMinimumDueAmountSelected: false,
        isLessThanMinimumThresholdAmount: false,
        isLessThanMinimumDueAmount: false,
        selectedAmount: null,
      ),
    );
  }

  Future<void> _setAmount(
    _SetAmount e,
    Emitter<RepaymentSelectAmountRelState> emit,
  ) async {
    if (e.amount != null) {
      final isLessThanMinimumThresholdAmount = e.amount! < state.minimumThresholdAmount;
      final isLessThanMinimumDueAmount = e.amount! < state.repaymentRelContract!.minimumDueAmount!;
      final selectedAmount = e.amount;
      emit(
        state.copyWith(
          selectedAmount: selectedAmount,
          isLessThanMinimumThresholdAmount: isLessThanMinimumThresholdAmount,
          isLessThanMinimumDueAmount: isLessThanMinimumDueAmount,
        ),
      );
    } else {
      emit(
        state.copyWith(
          selectedAmount: null,
          isLessThanMinimumThresholdAmount: false,
          isLessThanMinimumDueAmount: false,
        ),
      );
    }
  }

  Future<void> _fetchVirtualAccount(
    _FetchVirtualAccount e,
    Emitter<RepaymentSelectAmountRelState> emit,
  ) async {
    final selectedAmount = e.selectedAmount;
    final contractNumber = e.contractNumber;
    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false));

    final response = await repaymentRepository.getVirtualAccount(
      contractNumber: contractNumber,
      selectedAmount: selectedAmount.toBigInt().toInt(),
    );
    logger.d('getVirtualAccount response$response');

    emit(
      await response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessVirtualAccount: optionOf(response),
          isError: true,
        );
      }, (r) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessVirtualAccount: optionOf(response),
          contractVirtualAccount: r,
          isError: false,
        );
      }),
    );
  }

  Future<void> _resetVirtualAccount(
    _ResetVirtualAccount e,
    Emitter<RepaymentSelectAmountRelState> emit,
  ) async {
    emit(
      state.copyWith(
        loadingState: LoadingState.isInitial,
        failureOrSuccessVirtualAccount: none(),
        isError: false,
        contractVirtualAccount: null,
      ),
    );
  }
}
