part of 'repayment_ada_main_bloc.dart';

@freezed
class RepaymentAdaMainState with _$RepaymentAdaMainState {
  const factory RepaymentAdaMainState({
    required LoadingState loadingState,
    required LoadingState fetchPaymentMethodsloadingState,
    required LoadingState fetchContractsloadingState,
    String? initialContractNumber,
    required AdaContract? selectedContract,
    RepaymentUserPaymentMethod? selectedPaymentMethod,
    bool? isError, // Use for show error when fetch contract fail only
    required Option<Either<RepaymentFailure, List<AdaContract>>> failureOrSuccessFetchContracts,
    required Option<Either<RepaymentFailure, AdaZaloRegistrationResponse>> failureOrSuccessZaloCreateBinding,
    required Option<Either<RepaymentFailure, AdaOnepayRegistrationResponse>> failureOrSuccessRegisterOnepay,
    required Option<Either<RepaymentFailure, List<RepaymentUserPaymentMethod>>> failureOrSuccessFetchUserPaymentMethods,
    bool? isShowNoLoan,
    bool? isShowIntro,
    List<AdaContract>? contracts,
    List<RepaymentUserPaymentMethod>? paymentMethods,
    String? zaloRegisterAdUrl,
    String? zaloBindingToken,
    String? zaloRedirectUrl,
    bool? isUserEnrolled,
    @Default(true) bool isAcceptPaymentMethod,

    // Onepay
    String? onepayRedirectUrl, // Get from setting
    String? onepayRedirectLink, // Use to open onepay webview
    @Default(false) bool isProdSetting,
    @Default(true) bool canChangeContract, // In case user must choose pre-select contract to register ADA
  }) = _RepaymentAdaMainState;

  factory RepaymentAdaMainState.initialize() => RepaymentAdaMainState(
        isError: false,
        loadingState: LoadingState.isInitial,
        fetchPaymentMethodsloadingState: LoadingState.isInitial,
        fetchContractsloadingState: LoadingState.isInitial,
        failureOrSuccessFetchContracts: none(),
        failureOrSuccessZaloCreateBinding: none(),
        failureOrSuccessRegisterOnepay: none(),
        failureOrSuccessFetchUserPaymentMethods: none(),
        selectedContract: null,
      );
}
