part of 'repayment_ada_management_bloc.dart';

@freezed
class RepaymentAdaManagementEvent with _$RepaymentAdaManagementEvent {
  const factory RepaymentAdaManagementEvent.initialize({
    required L10nCappRepayment repaymentLocalization,
    Map<String, Object?>? dataMap,
  }) = _Initialize;
  const factory RepaymentAdaManagementEvent.fetchData({
    bool? shouldDelay,
    required L10nCappRepayment repaymentLocalization,
  }) = _FetchData;
}
