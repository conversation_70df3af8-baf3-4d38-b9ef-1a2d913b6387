part of 'repayment_ada_payment_method_bloc.dart';

@freezed
class RepaymentAdaPaymentMethodEvent with _$RepaymentAdaPaymentMethodEvent {
  const factory RepaymentAdaPaymentMethodEvent.initialize({
    required List<RepaymentUserPaymentMethod> paymentMethods,
    RepaymentUserPaymentMethod? selectedPaymentMethod,
    AdaContract? selectedContract,
  }) = _Initialize;
  const factory RepaymentAdaPaymentMethodEvent.selectPaymentMethod(RepaymentUserPaymentMethod method) =
      _SelectPaymentMethod;
}
