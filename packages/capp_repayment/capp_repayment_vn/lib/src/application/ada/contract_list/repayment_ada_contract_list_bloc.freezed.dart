// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_ada_contract_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentAdaContractListEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)
        initialize,
    required TResult Function(AdaContract contract) selectContract,
    required TResult Function() refreshContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult? Function(AdaContract contract)? selectContract,
    TResult? Function()? refreshContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult Function(AdaContract contract)? selectContract,
    TResult Function()? refreshContract,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentAdaContractListEventCopyWith<$Res> {
  factory $RepaymentAdaContractListEventCopyWith(
          RepaymentAdaContractListEvent value,
          $Res Function(RepaymentAdaContractListEvent) then) =
      _$RepaymentAdaContractListEventCopyWithImpl<$Res,
          RepaymentAdaContractListEvent>;
}

/// @nodoc
class _$RepaymentAdaContractListEventCopyWithImpl<$Res,
        $Val extends RepaymentAdaContractListEvent>
    implements $RepaymentAdaContractListEventCopyWith<$Res> {
  _$RepaymentAdaContractListEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({List<AdaContract> contracts, AdaContract? selectedContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentAdaContractListEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contracts = null,
    Object? selectedContract = freezed,
  }) {
    return _then(_$_Initialize(
      contracts: null == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<AdaContract>,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as AdaContract?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required final List<AdaContract> contracts, this.selectedContract})
      : _contracts = contracts;

  final List<AdaContract> _contracts;
  @override
  List<AdaContract> get contracts {
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contracts);
  }

  @override
  final AdaContract? selectedContract;

  @override
  String toString() {
    return 'RepaymentAdaContractListEvent.initialize(contracts: $contracts, selectedContract: $selectedContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            const DeepCollectionEquality()
                .equals(other._contracts, _contracts) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_contracts), selectedContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)
        initialize,
    required TResult Function(AdaContract contract) selectContract,
    required TResult Function() refreshContract,
  }) {
    return initialize(contracts, selectedContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult? Function(AdaContract contract)? selectContract,
    TResult? Function()? refreshContract,
  }) {
    return initialize?.call(contracts, selectedContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult Function(AdaContract contract)? selectContract,
    TResult Function()? refreshContract,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(contracts, selectedContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentAdaContractListEvent {
  const factory _Initialize(
      {required final List<AdaContract> contracts,
      final AdaContract? selectedContract}) = _$_Initialize;

  List<AdaContract> get contracts;
  AdaContract? get selectedContract;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectContractCopyWith<$Res> {
  factory _$$_SelectContractCopyWith(
          _$_SelectContract value, $Res Function(_$_SelectContract) then) =
      __$$_SelectContractCopyWithImpl<$Res>;
  @useResult
  $Res call({AdaContract contract});
}

/// @nodoc
class __$$_SelectContractCopyWithImpl<$Res>
    extends _$RepaymentAdaContractListEventCopyWithImpl<$Res, _$_SelectContract>
    implements _$$_SelectContractCopyWith<$Res> {
  __$$_SelectContractCopyWithImpl(
      _$_SelectContract _value, $Res Function(_$_SelectContract) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contract = null,
  }) {
    return _then(_$_SelectContract(
      contract: null == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as AdaContract,
    ));
  }
}

/// @nodoc

class _$_SelectContract implements _SelectContract {
  const _$_SelectContract({required this.contract});

  @override
  final AdaContract contract;

  @override
  String toString() {
    return 'RepaymentAdaContractListEvent.selectContract(contract: $contract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectContract &&
            (identical(other.contract, contract) ||
                other.contract == contract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectContractCopyWith<_$_SelectContract> get copyWith =>
      __$$_SelectContractCopyWithImpl<_$_SelectContract>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)
        initialize,
    required TResult Function(AdaContract contract) selectContract,
    required TResult Function() refreshContract,
  }) {
    return selectContract(contract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult? Function(AdaContract contract)? selectContract,
    TResult? Function()? refreshContract,
  }) {
    return selectContract?.call(contract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult Function(AdaContract contract)? selectContract,
    TResult Function()? refreshContract,
    required TResult orElse(),
  }) {
    if (selectContract != null) {
      return selectContract(contract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
  }) {
    return selectContract(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
  }) {
    return selectContract?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    required TResult orElse(),
  }) {
    if (selectContract != null) {
      return selectContract(this);
    }
    return orElse();
  }
}

abstract class _SelectContract implements RepaymentAdaContractListEvent {
  const factory _SelectContract({required final AdaContract contract}) =
      _$_SelectContract;

  AdaContract get contract;
  @JsonKey(ignore: true)
  _$$_SelectContractCopyWith<_$_SelectContract> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_RefreshContractCopyWith<$Res> {
  factory _$$_RefreshContractCopyWith(
          _$_RefreshContract value, $Res Function(_$_RefreshContract) then) =
      __$$_RefreshContractCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_RefreshContractCopyWithImpl<$Res>
    extends _$RepaymentAdaContractListEventCopyWithImpl<$Res,
        _$_RefreshContract> implements _$$_RefreshContractCopyWith<$Res> {
  __$$_RefreshContractCopyWithImpl(
      _$_RefreshContract _value, $Res Function(_$_RefreshContract) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_RefreshContract implements _RefreshContract {
  const _$_RefreshContract();

  @override
  String toString() {
    return 'RepaymentAdaContractListEvent.refreshContract()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_RefreshContract);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)
        initialize,
    required TResult Function(AdaContract contract) selectContract,
    required TResult Function() refreshContract,
  }) {
    return refreshContract();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult? Function(AdaContract contract)? selectContract,
    TResult? Function()? refreshContract,
  }) {
    return refreshContract?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            List<AdaContract> contracts, AdaContract? selectedContract)?
        initialize,
    TResult Function(AdaContract contract)? selectContract,
    TResult Function()? refreshContract,
    required TResult orElse(),
  }) {
    if (refreshContract != null) {
      return refreshContract();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectContract value) selectContract,
    required TResult Function(_RefreshContract value) refreshContract,
  }) {
    return refreshContract(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectContract value)? selectContract,
    TResult? Function(_RefreshContract value)? refreshContract,
  }) {
    return refreshContract?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectContract value)? selectContract,
    TResult Function(_RefreshContract value)? refreshContract,
    required TResult orElse(),
  }) {
    if (refreshContract != null) {
      return refreshContract(this);
    }
    return orElse();
  }
}

abstract class _RefreshContract implements RepaymentAdaContractListEvent {
  const factory _RefreshContract() = _$_RefreshContract;
}

/// @nodoc
mixin _$RepaymentAdaContractListState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  AdaContract? get selectedContract => throw _privateConstructorUsedError;
  List<AdaContract> get contracts => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<AdaContract>>>
      get failureOrSuccessFetchContracts => throw _privateConstructorUsedError;
  bool get isNeedToDismiss => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentAdaContractListStateCopyWith<RepaymentAdaContractListState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentAdaContractListStateCopyWith<$Res> {
  factory $RepaymentAdaContractListStateCopyWith(
          RepaymentAdaContractListState value,
          $Res Function(RepaymentAdaContractListState) then) =
      _$RepaymentAdaContractListStateCopyWithImpl<$Res,
          RepaymentAdaContractListState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      AdaContract? selectedContract,
      List<AdaContract> contracts,
      Option<Either<RepaymentFailure, List<AdaContract>>>
          failureOrSuccessFetchContracts,
      bool isNeedToDismiss});
}

/// @nodoc
class _$RepaymentAdaContractListStateCopyWithImpl<$Res,
        $Val extends RepaymentAdaContractListState>
    implements $RepaymentAdaContractListStateCopyWith<$Res> {
  _$RepaymentAdaContractListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedContract = freezed,
    Object? contracts = null,
    Object? failureOrSuccessFetchContracts = null,
    Object? isNeedToDismiss = null,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as AdaContract?,
      contracts: null == contracts
          ? _value.contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<AdaContract>,
      failureOrSuccessFetchContracts: null == failureOrSuccessFetchContracts
          ? _value.failureOrSuccessFetchContracts
          : failureOrSuccessFetchContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<AdaContract>>>,
      isNeedToDismiss: null == isNeedToDismiss
          ? _value.isNeedToDismiss
          : isNeedToDismiss // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentAdaContractListStateCopyWith<$Res>
    implements $RepaymentAdaContractListStateCopyWith<$Res> {
  factory _$$_RepaymentAdaContractListStateCopyWith(
          _$_RepaymentAdaContractListState value,
          $Res Function(_$_RepaymentAdaContractListState) then) =
      __$$_RepaymentAdaContractListStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      AdaContract? selectedContract,
      List<AdaContract> contracts,
      Option<Either<RepaymentFailure, List<AdaContract>>>
          failureOrSuccessFetchContracts,
      bool isNeedToDismiss});
}

/// @nodoc
class __$$_RepaymentAdaContractListStateCopyWithImpl<$Res>
    extends _$RepaymentAdaContractListStateCopyWithImpl<$Res,
        _$_RepaymentAdaContractListState>
    implements _$$_RepaymentAdaContractListStateCopyWith<$Res> {
  __$$_RepaymentAdaContractListStateCopyWithImpl(
      _$_RepaymentAdaContractListState _value,
      $Res Function(_$_RepaymentAdaContractListState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedContract = freezed,
    Object? contracts = null,
    Object? failureOrSuccessFetchContracts = null,
    Object? isNeedToDismiss = null,
  }) {
    return _then(_$_RepaymentAdaContractListState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as AdaContract?,
      contracts: null == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<AdaContract>,
      failureOrSuccessFetchContracts: null == failureOrSuccessFetchContracts
          ? _value.failureOrSuccessFetchContracts
          : failureOrSuccessFetchContracts // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<AdaContract>>>,
      isNeedToDismiss: null == isNeedToDismiss
          ? _value.isNeedToDismiss
          : isNeedToDismiss // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_RepaymentAdaContractListState
    implements _RepaymentAdaContractListState {
  const _$_RepaymentAdaContractListState(
      {required this.loadingState,
      this.isError,
      this.selectedContract,
      final List<AdaContract> contracts = const <AdaContract>[],
      required this.failureOrSuccessFetchContracts,
      this.isNeedToDismiss = false})
      : _contracts = contracts;

  @override
  final LoadingState loadingState;
  @override
  final bool? isError;
  @override
  final AdaContract? selectedContract;
  final List<AdaContract> _contracts;
  @override
  @JsonKey()
  List<AdaContract> get contracts {
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contracts);
  }

  @override
  final Option<Either<RepaymentFailure, List<AdaContract>>>
      failureOrSuccessFetchContracts;
  @override
  @JsonKey()
  final bool isNeedToDismiss;

  @override
  String toString() {
    return 'RepaymentAdaContractListState(loadingState: $loadingState, isError: $isError, selectedContract: $selectedContract, contracts: $contracts, failureOrSuccessFetchContracts: $failureOrSuccessFetchContracts, isNeedToDismiss: $isNeedToDismiss)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentAdaContractListState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            const DeepCollectionEquality()
                .equals(other._contracts, _contracts) &&
            (identical(other.failureOrSuccessFetchContracts,
                    failureOrSuccessFetchContracts) ||
                other.failureOrSuccessFetchContracts ==
                    failureOrSuccessFetchContracts) &&
            (identical(other.isNeedToDismiss, isNeedToDismiss) ||
                other.isNeedToDismiss == isNeedToDismiss));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      isError,
      selectedContract,
      const DeepCollectionEquality().hash(_contracts),
      failureOrSuccessFetchContracts,
      isNeedToDismiss);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentAdaContractListStateCopyWith<_$_RepaymentAdaContractListState>
      get copyWith => __$$_RepaymentAdaContractListStateCopyWithImpl<
          _$_RepaymentAdaContractListState>(this, _$identity);
}

abstract class _RepaymentAdaContractListState
    implements RepaymentAdaContractListState {
  const factory _RepaymentAdaContractListState(
      {required final LoadingState loadingState,
      final bool? isError,
      final AdaContract? selectedContract,
      final List<AdaContract> contracts,
      required final Option<Either<RepaymentFailure, List<AdaContract>>>
          failureOrSuccessFetchContracts,
      final bool isNeedToDismiss}) = _$_RepaymentAdaContractListState;

  @override
  LoadingState get loadingState;
  @override
  bool? get isError;
  @override
  AdaContract? get selectedContract;
  @override
  List<AdaContract> get contracts;
  @override
  Option<Either<RepaymentFailure, List<AdaContract>>>
      get failureOrSuccessFetchContracts;
  @override
  bool get isNeedToDismiss;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentAdaContractListStateCopyWith<_$_RepaymentAdaContractListState>
      get copyWith => throw _privateConstructorUsedError;
}
