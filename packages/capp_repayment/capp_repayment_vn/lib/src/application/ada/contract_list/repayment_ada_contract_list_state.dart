part of 'repayment_ada_contract_list_bloc.dart';

@freezed
class RepaymentAdaContractListState with _$RepaymentAdaContractListState {
  const factory RepaymentAdaContractListState({
    required LoadingState loadingState,
    bool? isError,
    AdaContract? selectedContract,
    @Default(<AdaContract>[]) List<AdaContract> contracts,
    required Option<Either<RepaymentFailure, List<AdaContract>>> failureOrSuccessFetchContracts,
    @Default(false) bool isNeedToDismiss,
  }) = _RepaymentAdaContractListState;

  factory RepaymentAdaContractListState.initialize() => RepaymentAdaContractListState(
        loadingState: LoadingState.isInitial,
        isError: false,
        contracts: [],
        failureOrSuccessFetchContracts: none(),
      );
}
