// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_ada_intro_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentAdaIntroEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)
        initialize,
    required TResult Function() setDidDisplayIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)?
        initialize,
    TResult? Function()? setDidDisplayIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)?
        initialize,
    TResult Function()? setDidDisplayIntro,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SetDidDisplayIntro value) setDidDisplayIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SetDidDisplayIntro value)? setDidDisplayIntro,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SetDidDisplayIntro value)? setDidDisplayIntro,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentAdaIntroEventCopyWith<$Res> {
  factory $RepaymentAdaIntroEventCopyWith(RepaymentAdaIntroEvent value,
          $Res Function(RepaymentAdaIntroEvent) then) =
      _$RepaymentAdaIntroEventCopyWithImpl<$Res, RepaymentAdaIntroEvent>;
}

/// @nodoc
class _$RepaymentAdaIntroEventCopyWithImpl<$Res,
        $Val extends RepaymentAdaIntroEvent>
    implements $RepaymentAdaIntroEventCopyWith<$Res> {
  _$RepaymentAdaIntroEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String? initialContractNumber,
      AdaFromFlow? flow,
      String? flowInstanceId,
      String? contractType,
      String? ddmCode,
      String? savedPaymentMethodId});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentAdaIntroEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? initialContractNumber = freezed,
    Object? flow = freezed,
    Object? flowInstanceId = freezed,
    Object? contractType = freezed,
    Object? ddmCode = freezed,
    Object? savedPaymentMethodId = freezed,
  }) {
    return _then(_$_Initialize(
      initialContractNumber: freezed == initialContractNumber
          ? _value.initialContractNumber
          : initialContractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      flow: freezed == flow
          ? _value.flow
          : flow // ignore: cast_nullable_to_non_nullable
              as AdaFromFlow?,
      flowInstanceId: freezed == flowInstanceId
          ? _value.flowInstanceId
          : flowInstanceId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractType: freezed == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as String?,
      ddmCode: freezed == ddmCode
          ? _value.ddmCode
          : ddmCode // ignore: cast_nullable_to_non_nullable
              as String?,
      savedPaymentMethodId: freezed == savedPaymentMethodId
          ? _value.savedPaymentMethodId
          : savedPaymentMethodId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {this.initialContractNumber,
      this.flow,
      this.flowInstanceId,
      this.contractType,
      this.ddmCode,
      this.savedPaymentMethodId});

  @override
  final String? initialContractNumber;
  @override
  final AdaFromFlow? flow;
  @override
  final String? flowInstanceId;
  @override
  final String? contractType;
// CEL, REL
  @override
  final String? ddmCode;
  @override
  final String? savedPaymentMethodId;

  @override
  String toString() {
    return 'RepaymentAdaIntroEvent.initialize(initialContractNumber: $initialContractNumber, flow: $flow, flowInstanceId: $flowInstanceId, contractType: $contractType, ddmCode: $ddmCode, savedPaymentMethodId: $savedPaymentMethodId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.initialContractNumber, initialContractNumber) ||
                other.initialContractNumber == initialContractNumber) &&
            (identical(other.flow, flow) || other.flow == flow) &&
            (identical(other.flowInstanceId, flowInstanceId) ||
                other.flowInstanceId == flowInstanceId) &&
            (identical(other.contractType, contractType) ||
                other.contractType == contractType) &&
            (identical(other.ddmCode, ddmCode) || other.ddmCode == ddmCode) &&
            (identical(other.savedPaymentMethodId, savedPaymentMethodId) ||
                other.savedPaymentMethodId == savedPaymentMethodId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, initialContractNumber, flow,
      flowInstanceId, contractType, ddmCode, savedPaymentMethodId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)
        initialize,
    required TResult Function() setDidDisplayIntro,
  }) {
    return initialize(initialContractNumber, flow, flowInstanceId, contractType,
        ddmCode, savedPaymentMethodId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)?
        initialize,
    TResult? Function()? setDidDisplayIntro,
  }) {
    return initialize?.call(initialContractNumber, flow, flowInstanceId,
        contractType, ddmCode, savedPaymentMethodId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)?
        initialize,
    TResult Function()? setDidDisplayIntro,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(initialContractNumber, flow, flowInstanceId,
          contractType, ddmCode, savedPaymentMethodId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SetDidDisplayIntro value) setDidDisplayIntro,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SetDidDisplayIntro value)? setDidDisplayIntro,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SetDidDisplayIntro value)? setDidDisplayIntro,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentAdaIntroEvent {
  const factory _Initialize(
      {final String? initialContractNumber,
      final AdaFromFlow? flow,
      final String? flowInstanceId,
      final String? contractType,
      final String? ddmCode,
      final String? savedPaymentMethodId}) = _$_Initialize;

  String? get initialContractNumber;
  AdaFromFlow? get flow;
  String? get flowInstanceId;
  String? get contractType; // CEL, REL
  String? get ddmCode;
  String? get savedPaymentMethodId;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SetDidDisplayIntroCopyWith<$Res> {
  factory _$$_SetDidDisplayIntroCopyWith(_$_SetDidDisplayIntro value,
          $Res Function(_$_SetDidDisplayIntro) then) =
      __$$_SetDidDisplayIntroCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SetDidDisplayIntroCopyWithImpl<$Res>
    extends _$RepaymentAdaIntroEventCopyWithImpl<$Res, _$_SetDidDisplayIntro>
    implements _$$_SetDidDisplayIntroCopyWith<$Res> {
  __$$_SetDidDisplayIntroCopyWithImpl(
      _$_SetDidDisplayIntro _value, $Res Function(_$_SetDidDisplayIntro) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SetDidDisplayIntro implements _SetDidDisplayIntro {
  const _$_SetDidDisplayIntro();

  @override
  String toString() {
    return 'RepaymentAdaIntroEvent.setDidDisplayIntro()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SetDidDisplayIntro);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)
        initialize,
    required TResult Function() setDidDisplayIntro,
  }) {
    return setDidDisplayIntro();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)?
        initialize,
    TResult? Function()? setDidDisplayIntro,
  }) {
    return setDidDisplayIntro?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? initialContractNumber,
            AdaFromFlow? flow,
            String? flowInstanceId,
            String? contractType,
            String? ddmCode,
            String? savedPaymentMethodId)?
        initialize,
    TResult Function()? setDidDisplayIntro,
    required TResult orElse(),
  }) {
    if (setDidDisplayIntro != null) {
      return setDidDisplayIntro();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SetDidDisplayIntro value) setDidDisplayIntro,
  }) {
    return setDidDisplayIntro(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SetDidDisplayIntro value)? setDidDisplayIntro,
  }) {
    return setDidDisplayIntro?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SetDidDisplayIntro value)? setDidDisplayIntro,
    required TResult orElse(),
  }) {
    if (setDidDisplayIntro != null) {
      return setDidDisplayIntro(this);
    }
    return orElse();
  }
}

abstract class _SetDidDisplayIntro implements RepaymentAdaIntroEvent {
  const factory _SetDidDisplayIntro() = _$_SetDidDisplayIntro;
}

/// @nodoc
mixin _$RepaymentAdaIntroState {
  AdaFromFlow? get flow =>
      throw _privateConstructorUsedError; // Loan journey flow
  String? get flowInstanceId => throw _privateConstructorUsedError;
  String? get contractType => throw _privateConstructorUsedError; // CEL, REL
  String? get ddmCode => throw _privateConstructorUsedError;
  String? get savedPaymentMethodId =>
      throw _privateConstructorUsedError; // Gma id
// Other flows
  String? get initialContractNumber => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentAdaIntroStateCopyWith<RepaymentAdaIntroState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentAdaIntroStateCopyWith<$Res> {
  factory $RepaymentAdaIntroStateCopyWith(RepaymentAdaIntroState value,
          $Res Function(RepaymentAdaIntroState) then) =
      _$RepaymentAdaIntroStateCopyWithImpl<$Res, RepaymentAdaIntroState>;
  @useResult
  $Res call(
      {AdaFromFlow? flow,
      String? flowInstanceId,
      String? contractType,
      String? ddmCode,
      String? savedPaymentMethodId,
      String? initialContractNumber});
}

/// @nodoc
class _$RepaymentAdaIntroStateCopyWithImpl<$Res,
        $Val extends RepaymentAdaIntroState>
    implements $RepaymentAdaIntroStateCopyWith<$Res> {
  _$RepaymentAdaIntroStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flow = freezed,
    Object? flowInstanceId = freezed,
    Object? contractType = freezed,
    Object? ddmCode = freezed,
    Object? savedPaymentMethodId = freezed,
    Object? initialContractNumber = freezed,
  }) {
    return _then(_value.copyWith(
      flow: freezed == flow
          ? _value.flow
          : flow // ignore: cast_nullable_to_non_nullable
              as AdaFromFlow?,
      flowInstanceId: freezed == flowInstanceId
          ? _value.flowInstanceId
          : flowInstanceId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractType: freezed == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as String?,
      ddmCode: freezed == ddmCode
          ? _value.ddmCode
          : ddmCode // ignore: cast_nullable_to_non_nullable
              as String?,
      savedPaymentMethodId: freezed == savedPaymentMethodId
          ? _value.savedPaymentMethodId
          : savedPaymentMethodId // ignore: cast_nullable_to_non_nullable
              as String?,
      initialContractNumber: freezed == initialContractNumber
          ? _value.initialContractNumber
          : initialContractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentAdaIntroStateCopyWith<$Res>
    implements $RepaymentAdaIntroStateCopyWith<$Res> {
  factory _$$_RepaymentAdaIntroStateCopyWith(_$_RepaymentAdaIntroState value,
          $Res Function(_$_RepaymentAdaIntroState) then) =
      __$$_RepaymentAdaIntroStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AdaFromFlow? flow,
      String? flowInstanceId,
      String? contractType,
      String? ddmCode,
      String? savedPaymentMethodId,
      String? initialContractNumber});
}

/// @nodoc
class __$$_RepaymentAdaIntroStateCopyWithImpl<$Res>
    extends _$RepaymentAdaIntroStateCopyWithImpl<$Res,
        _$_RepaymentAdaIntroState>
    implements _$$_RepaymentAdaIntroStateCopyWith<$Res> {
  __$$_RepaymentAdaIntroStateCopyWithImpl(_$_RepaymentAdaIntroState _value,
      $Res Function(_$_RepaymentAdaIntroState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flow = freezed,
    Object? flowInstanceId = freezed,
    Object? contractType = freezed,
    Object? ddmCode = freezed,
    Object? savedPaymentMethodId = freezed,
    Object? initialContractNumber = freezed,
  }) {
    return _then(_$_RepaymentAdaIntroState(
      flow: freezed == flow
          ? _value.flow
          : flow // ignore: cast_nullable_to_non_nullable
              as AdaFromFlow?,
      flowInstanceId: freezed == flowInstanceId
          ? _value.flowInstanceId
          : flowInstanceId // ignore: cast_nullable_to_non_nullable
              as String?,
      contractType: freezed == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as String?,
      ddmCode: freezed == ddmCode
          ? _value.ddmCode
          : ddmCode // ignore: cast_nullable_to_non_nullable
              as String?,
      savedPaymentMethodId: freezed == savedPaymentMethodId
          ? _value.savedPaymentMethodId
          : savedPaymentMethodId // ignore: cast_nullable_to_non_nullable
              as String?,
      initialContractNumber: freezed == initialContractNumber
          ? _value.initialContractNumber
          : initialContractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_RepaymentAdaIntroState implements _RepaymentAdaIntroState {
  const _$_RepaymentAdaIntroState(
      {this.flow,
      this.flowInstanceId,
      this.contractType,
      this.ddmCode,
      this.savedPaymentMethodId,
      this.initialContractNumber});

  @override
  final AdaFromFlow? flow;
// Loan journey flow
  @override
  final String? flowInstanceId;
  @override
  final String? contractType;
// CEL, REL
  @override
  final String? ddmCode;
  @override
  final String? savedPaymentMethodId;
// Gma id
// Other flows
  @override
  final String? initialContractNumber;

  @override
  String toString() {
    return 'RepaymentAdaIntroState(flow: $flow, flowInstanceId: $flowInstanceId, contractType: $contractType, ddmCode: $ddmCode, savedPaymentMethodId: $savedPaymentMethodId, initialContractNumber: $initialContractNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentAdaIntroState &&
            (identical(other.flow, flow) || other.flow == flow) &&
            (identical(other.flowInstanceId, flowInstanceId) ||
                other.flowInstanceId == flowInstanceId) &&
            (identical(other.contractType, contractType) ||
                other.contractType == contractType) &&
            (identical(other.ddmCode, ddmCode) || other.ddmCode == ddmCode) &&
            (identical(other.savedPaymentMethodId, savedPaymentMethodId) ||
                other.savedPaymentMethodId == savedPaymentMethodId) &&
            (identical(other.initialContractNumber, initialContractNumber) ||
                other.initialContractNumber == initialContractNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, flow, flowInstanceId,
      contractType, ddmCode, savedPaymentMethodId, initialContractNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentAdaIntroStateCopyWith<_$_RepaymentAdaIntroState> get copyWith =>
      __$$_RepaymentAdaIntroStateCopyWithImpl<_$_RepaymentAdaIntroState>(
          this, _$identity);
}

abstract class _RepaymentAdaIntroState implements RepaymentAdaIntroState {
  const factory _RepaymentAdaIntroState(
      {final AdaFromFlow? flow,
      final String? flowInstanceId,
      final String? contractType,
      final String? ddmCode,
      final String? savedPaymentMethodId,
      final String? initialContractNumber}) = _$_RepaymentAdaIntroState;

  @override
  AdaFromFlow? get flow;
  @override // Loan journey flow
  String? get flowInstanceId;
  @override
  String? get contractType;
  @override // CEL, REL
  String? get ddmCode;
  @override
  String? get savedPaymentMethodId;
  @override // Gma id
// Other flows
  String? get initialContractNumber;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentAdaIntroStateCopyWith<_$_RepaymentAdaIntroState> get copyWith =>
      throw _privateConstructorUsedError;
}
