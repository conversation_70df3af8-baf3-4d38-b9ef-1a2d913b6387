part of 'viettelmoney_bloc.dart';

@freezed
class ViettelMoneyState with _$ViettelMoneyState {
  const factory ViettelMoneyState({
    required LoadingState loadingState,
    required bool isError,
    required String? contractNumber,
    required String? transactionId,
    required String? uuid,
    required double? amount,
    required String? customerName,
    required String provider,
    required String? viettelMoneyPortalUrl,
  }) = _ViettelMoneyState;

  factory ViettelMoneyState.initialize() => ViettelMoneyState(
        loadingState: LoadingState.isInitial,
        isError: false,
        contractNumber: null,
        transactionId: null,
        uuid: null,
        amount: null,
        customerName: null,
        provider: RepaymentPaymentMethod.viettel.provider,
        viettelMoneyPortalUrl: null,
      );
}
