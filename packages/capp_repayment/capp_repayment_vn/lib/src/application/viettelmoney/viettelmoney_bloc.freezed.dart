// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'viettelmoney_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ViettelMoneyEvent {
  String get uuid => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber, String transactionId,
            String uuid, double amount, String customerName, String paymentUrl)
        initialize,
    required TResult Function(String uuid) cancelTransaction,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber, String transactionId, String uuid,
            double amount, String customerName, String paymentUrl)?
        initialize,
    TResult? Function(String uuid)? cancelTransaction,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber, String transactionId, String uuid,
            double amount, String customerName, String paymentUrl)?
        initialize,
    TResult Function(String uuid)? cancelTransaction,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CancelTransaction value) cancelTransaction,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CancelTransaction value)? cancelTransaction,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CancelTransaction value)? cancelTransaction,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ViettelMoneyEventCopyWith<ViettelMoneyEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ViettelMoneyEventCopyWith<$Res> {
  factory $ViettelMoneyEventCopyWith(
          ViettelMoneyEvent value, $Res Function(ViettelMoneyEvent) then) =
      _$ViettelMoneyEventCopyWithImpl<$Res, ViettelMoneyEvent>;
  @useResult
  $Res call({String uuid});
}

/// @nodoc
class _$ViettelMoneyEventCopyWithImpl<$Res, $Val extends ViettelMoneyEvent>
    implements $ViettelMoneyEventCopyWith<$Res> {
  _$ViettelMoneyEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
  }) {
    return _then(_value.copyWith(
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res>
    implements $ViettelMoneyEventCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String contractNumber,
      String transactionId,
      String uuid,
      double amount,
      String customerName,
      String paymentUrl});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$ViettelMoneyEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
    Object? transactionId = null,
    Object? uuid = null,
    Object? amount = null,
    Object? customerName = null,
    Object? paymentUrl = null,
  }) {
    return _then(_$_Initialize(
      null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
      null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      null == paymentUrl
          ? _value.paymentUrl
          : paymentUrl // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(this.contractNumber, this.transactionId, this.uuid,
      this.amount, this.customerName, this.paymentUrl);

  @override
  final String contractNumber;
  @override
  final String transactionId;
  @override
  final String uuid;
  @override
  final double amount;
  @override
  final String customerName;
  @override
  final String paymentUrl;

  @override
  String toString() {
    return 'ViettelMoneyEvent.initialize(contractNumber: $contractNumber, transactionId: $transactionId, uuid: $uuid, amount: $amount, customerName: $customerName, paymentUrl: $paymentUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.uuid, uuid) || other.uuid == uuid) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.paymentUrl, paymentUrl) ||
                other.paymentUrl == paymentUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractNumber, transactionId,
      uuid, amount, customerName, paymentUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber, String transactionId,
            String uuid, double amount, String customerName, String paymentUrl)
        initialize,
    required TResult Function(String uuid) cancelTransaction,
  }) {
    return initialize(
        contractNumber, transactionId, uuid, amount, customerName, paymentUrl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber, String transactionId, String uuid,
            double amount, String customerName, String paymentUrl)?
        initialize,
    TResult? Function(String uuid)? cancelTransaction,
  }) {
    return initialize?.call(
        contractNumber, transactionId, uuid, amount, customerName, paymentUrl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber, String transactionId, String uuid,
            double amount, String customerName, String paymentUrl)?
        initialize,
    TResult Function(String uuid)? cancelTransaction,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(contractNumber, transactionId, uuid, amount,
          customerName, paymentUrl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CancelTransaction value) cancelTransaction,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CancelTransaction value)? cancelTransaction,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CancelTransaction value)? cancelTransaction,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements ViettelMoneyEvent {
  const factory _Initialize(
      final String contractNumber,
      final String transactionId,
      final String uuid,
      final double amount,
      final String customerName,
      final String paymentUrl) = _$_Initialize;

  String get contractNumber;
  String get transactionId;
  @override
  String get uuid;
  double get amount;
  String get customerName;
  String get paymentUrl;
  @override
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_CancelTransactionCopyWith<$Res>
    implements $ViettelMoneyEventCopyWith<$Res> {
  factory _$$_CancelTransactionCopyWith(_$_CancelTransaction value,
          $Res Function(_$_CancelTransaction) then) =
      __$$_CancelTransactionCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String uuid});
}

/// @nodoc
class __$$_CancelTransactionCopyWithImpl<$Res>
    extends _$ViettelMoneyEventCopyWithImpl<$Res, _$_CancelTransaction>
    implements _$$_CancelTransactionCopyWith<$Res> {
  __$$_CancelTransactionCopyWithImpl(
      _$_CancelTransaction _value, $Res Function(_$_CancelTransaction) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uuid = null,
  }) {
    return _then(_$_CancelTransaction(
      null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_CancelTransaction implements _CancelTransaction {
  const _$_CancelTransaction(this.uuid);

  @override
  final String uuid;

  @override
  String toString() {
    return 'ViettelMoneyEvent.cancelTransaction(uuid: $uuid)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CancelTransaction &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @override
  int get hashCode => Object.hash(runtimeType, uuid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CancelTransactionCopyWith<_$_CancelTransaction> get copyWith =>
      __$$_CancelTransactionCopyWithImpl<_$_CancelTransaction>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String contractNumber, String transactionId,
            String uuid, double amount, String customerName, String paymentUrl)
        initialize,
    required TResult Function(String uuid) cancelTransaction,
  }) {
    return cancelTransaction(uuid);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String contractNumber, String transactionId, String uuid,
            double amount, String customerName, String paymentUrl)?
        initialize,
    TResult? Function(String uuid)? cancelTransaction,
  }) {
    return cancelTransaction?.call(uuid);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String contractNumber, String transactionId, String uuid,
            double amount, String customerName, String paymentUrl)?
        initialize,
    TResult Function(String uuid)? cancelTransaction,
    required TResult orElse(),
  }) {
    if (cancelTransaction != null) {
      return cancelTransaction(uuid);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CancelTransaction value) cancelTransaction,
  }) {
    return cancelTransaction(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CancelTransaction value)? cancelTransaction,
  }) {
    return cancelTransaction?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CancelTransaction value)? cancelTransaction,
    required TResult orElse(),
  }) {
    if (cancelTransaction != null) {
      return cancelTransaction(this);
    }
    return orElse();
  }
}

abstract class _CancelTransaction implements ViettelMoneyEvent {
  const factory _CancelTransaction(final String uuid) = _$_CancelTransaction;

  @override
  String get uuid;
  @override
  @JsonKey(ignore: true)
  _$$_CancelTransactionCopyWith<_$_CancelTransaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ViettelMoneyState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  String? get contractNumber => throw _privateConstructorUsedError;
  String? get transactionId => throw _privateConstructorUsedError;
  String? get uuid => throw _privateConstructorUsedError;
  double? get amount => throw _privateConstructorUsedError;
  String? get customerName => throw _privateConstructorUsedError;
  String get provider => throw _privateConstructorUsedError;
  String? get viettelMoneyPortalUrl => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ViettelMoneyStateCopyWith<ViettelMoneyState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ViettelMoneyStateCopyWith<$Res> {
  factory $ViettelMoneyStateCopyWith(
          ViettelMoneyState value, $Res Function(ViettelMoneyState) then) =
      _$ViettelMoneyStateCopyWithImpl<$Res, ViettelMoneyState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool isError,
      String? contractNumber,
      String? transactionId,
      String? uuid,
      double? amount,
      String? customerName,
      String provider,
      String? viettelMoneyPortalUrl});
}

/// @nodoc
class _$ViettelMoneyStateCopyWithImpl<$Res, $Val extends ViettelMoneyState>
    implements $ViettelMoneyStateCopyWith<$Res> {
  _$ViettelMoneyStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = null,
    Object? contractNumber = freezed,
    Object? transactionId = freezed,
    Object? uuid = freezed,
    Object? amount = freezed,
    Object? customerName = freezed,
    Object? provider = null,
    Object? viettelMoneyPortalUrl = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      uuid: freezed == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      viettelMoneyPortalUrl: freezed == viettelMoneyPortalUrl
          ? _value.viettelMoneyPortalUrl
          : viettelMoneyPortalUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ViettelMoneyStateCopyWith<$Res>
    implements $ViettelMoneyStateCopyWith<$Res> {
  factory _$$_ViettelMoneyStateCopyWith(_$_ViettelMoneyState value,
          $Res Function(_$_ViettelMoneyState) then) =
      __$$_ViettelMoneyStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool isError,
      String? contractNumber,
      String? transactionId,
      String? uuid,
      double? amount,
      String? customerName,
      String provider,
      String? viettelMoneyPortalUrl});
}

/// @nodoc
class __$$_ViettelMoneyStateCopyWithImpl<$Res>
    extends _$ViettelMoneyStateCopyWithImpl<$Res, _$_ViettelMoneyState>
    implements _$$_ViettelMoneyStateCopyWith<$Res> {
  __$$_ViettelMoneyStateCopyWithImpl(
      _$_ViettelMoneyState _value, $Res Function(_$_ViettelMoneyState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = null,
    Object? contractNumber = freezed,
    Object? transactionId = freezed,
    Object? uuid = freezed,
    Object? amount = freezed,
    Object? customerName = freezed,
    Object? provider = null,
    Object? viettelMoneyPortalUrl = freezed,
  }) {
    return _then(_$_ViettelMoneyState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionId: freezed == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      uuid: freezed == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      provider: null == provider
          ? _value.provider
          : provider // ignore: cast_nullable_to_non_nullable
              as String,
      viettelMoneyPortalUrl: freezed == viettelMoneyPortalUrl
          ? _value.viettelMoneyPortalUrl
          : viettelMoneyPortalUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_ViettelMoneyState implements _ViettelMoneyState {
  const _$_ViettelMoneyState(
      {required this.loadingState,
      required this.isError,
      required this.contractNumber,
      required this.transactionId,
      required this.uuid,
      required this.amount,
      required this.customerName,
      required this.provider,
      required this.viettelMoneyPortalUrl});

  @override
  final LoadingState loadingState;
  @override
  final bool isError;
  @override
  final String? contractNumber;
  @override
  final String? transactionId;
  @override
  final String? uuid;
  @override
  final double? amount;
  @override
  final String? customerName;
  @override
  final String provider;
  @override
  final String? viettelMoneyPortalUrl;

  @override
  String toString() {
    return 'ViettelMoneyState(loadingState: $loadingState, isError: $isError, contractNumber: $contractNumber, transactionId: $transactionId, uuid: $uuid, amount: $amount, customerName: $customerName, provider: $provider, viettelMoneyPortalUrl: $viettelMoneyPortalUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ViettelMoneyState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.uuid, uuid) || other.uuid == uuid) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.viettelMoneyPortalUrl, viettelMoneyPortalUrl) ||
                other.viettelMoneyPortalUrl == viettelMoneyPortalUrl));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      isError,
      contractNumber,
      transactionId,
      uuid,
      amount,
      customerName,
      provider,
      viettelMoneyPortalUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ViettelMoneyStateCopyWith<_$_ViettelMoneyState> get copyWith =>
      __$$_ViettelMoneyStateCopyWithImpl<_$_ViettelMoneyState>(
          this, _$identity);
}

abstract class _ViettelMoneyState implements ViettelMoneyState {
  const factory _ViettelMoneyState(
      {required final LoadingState loadingState,
      required final bool isError,
      required final String? contractNumber,
      required final String? transactionId,
      required final String? uuid,
      required final double? amount,
      required final String? customerName,
      required final String provider,
      required final String? viettelMoneyPortalUrl}) = _$_ViettelMoneyState;

  @override
  LoadingState get loadingState;
  @override
  bool get isError;
  @override
  String? get contractNumber;
  @override
  String? get transactionId;
  @override
  String? get uuid;
  @override
  double? get amount;
  @override
  String? get customerName;
  @override
  String get provider;
  @override
  String? get viettelMoneyPortalUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ViettelMoneyStateCopyWith<_$_ViettelMoneyState> get copyWith =>
      throw _privateConstructorUsedError;
}
