// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_select_amount_cel_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentSelectAmountCelEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentSelectAmountCelEventCopyWith<$Res> {
  factory $RepaymentSelectAmountCelEventCopyWith(
          RepaymentSelectAmountCelEvent value,
          $Res Function(RepaymentSelectAmountCelEvent) then) =
      _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
          RepaymentSelectAmountCelEvent>;
}

/// @nodoc
class _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        $Val extends RepaymentSelectAmountCelEvent>
    implements $RepaymentSelectAmountCelEventCopyWith<$Res> {
  _$RepaymentSelectAmountCelEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentLoanContract repaymentLoanContract});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repaymentLoanContract = null,
  }) {
    return _then(_$_Initialize(
      repaymentLoanContract: null == repaymentLoanContract
          ? _value.repaymentLoanContract
          : repaymentLoanContract // ignore: cast_nullable_to_non_nullable
              as RepaymentLoanContract,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.repaymentLoanContract});

  @override
  final RepaymentLoanContract repaymentLoanContract;

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.initialize(repaymentLoanContract: $repaymentLoanContract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.repaymentLoanContract, repaymentLoanContract) ||
                other.repaymentLoanContract == repaymentLoanContract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, repaymentLoanContract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return initialize(repaymentLoanContract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return initialize?.call(repaymentLoanContract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(repaymentLoanContract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentSelectAmountCelEvent {
  const factory _Initialize(
          {required final RepaymentLoanContract repaymentLoanContract}) =
      _$_Initialize;

  RepaymentLoanContract get repaymentLoanContract;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectDueAmountCopyWith<$Res> {
  factory _$$_SelectDueAmountCopyWith(
          _$_SelectDueAmount value, $Res Function(_$_SelectDueAmount) then) =
      __$$_SelectDueAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SelectDueAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        _$_SelectDueAmount> implements _$$_SelectDueAmountCopyWith<$Res> {
  __$$_SelectDueAmountCopyWithImpl(
      _$_SelectDueAmount _value, $Res Function(_$_SelectDueAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SelectDueAmount implements _SelectDueAmount {
  const _$_SelectDueAmount();

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.selectDueAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SelectDueAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return selectDueAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return selectDueAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectDueAmount != null) {
      return selectDueAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return selectDueAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return selectDueAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectDueAmount != null) {
      return selectDueAmount(this);
    }
    return orElse();
  }
}

abstract class _SelectDueAmount implements RepaymentSelectAmountCelEvent {
  const factory _SelectDueAmount() = _$_SelectDueAmount;
}

/// @nodoc
abstract class _$$_SelectCustomAmountCopyWith<$Res> {
  factory _$$_SelectCustomAmountCopyWith(_$_SelectCustomAmount value,
          $Res Function(_$_SelectCustomAmount) then) =
      __$$_SelectCustomAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SelectCustomAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        _$_SelectCustomAmount> implements _$$_SelectCustomAmountCopyWith<$Res> {
  __$$_SelectCustomAmountCopyWithImpl(
      _$_SelectCustomAmount _value, $Res Function(_$_SelectCustomAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SelectCustomAmount implements _SelectCustomAmount {
  const _$_SelectCustomAmount();

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.selectCustomAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SelectCustomAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return selectCustomAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return selectCustomAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectCustomAmount != null) {
      return selectCustomAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return selectCustomAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return selectCustomAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (selectCustomAmount != null) {
      return selectCustomAmount(this);
    }
    return orElse();
  }
}

abstract class _SelectCustomAmount implements RepaymentSelectAmountCelEvent {
  const factory _SelectCustomAmount() = _$_SelectCustomAmount;
}

/// @nodoc
abstract class _$$_SetAmountCopyWith<$Res> {
  factory _$$_SetAmountCopyWith(
          _$_SetAmount value, $Res Function(_$_SetAmount) then) =
      __$$_SetAmountCopyWithImpl<$Res>;
  @useResult
  $Res call({Decimal? amount});
}

/// @nodoc
class __$$_SetAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res, _$_SetAmount>
    implements _$$_SetAmountCopyWith<$Res> {
  __$$_SetAmountCopyWithImpl(
      _$_SetAmount _value, $Res Function(_$_SetAmount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
  }) {
    return _then(_$_SetAmount(
      freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
    ));
  }
}

/// @nodoc

class _$_SetAmount implements _SetAmount {
  const _$_SetAmount(this.amount);

  @override
  final Decimal? amount;

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.setAmount(amount: $amount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetAmount &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      __$$_SetAmountCopyWithImpl<_$_SetAmount>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return setAmount(amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return setAmount?.call(amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return setAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return setAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(this);
    }
    return orElse();
  }
}

abstract class _SetAmount implements RepaymentSelectAmountCelEvent {
  const factory _SetAmount(final Decimal? amount) = _$_SetAmount;

  Decimal? get amount;
  @JsonKey(ignore: true)
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FetchVirtualAccountCopyWith<$Res> {
  factory _$$_FetchVirtualAccountCopyWith(_$_FetchVirtualAccount value,
          $Res Function(_$_FetchVirtualAccount) then) =
      __$$_FetchVirtualAccountCopyWithImpl<$Res>;
  @useResult
  $Res call({String contractNumber, Decimal selectedAmount});
}

/// @nodoc
class __$$_FetchVirtualAccountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        _$_FetchVirtualAccount>
    implements _$$_FetchVirtualAccountCopyWith<$Res> {
  __$$_FetchVirtualAccountCopyWithImpl(_$_FetchVirtualAccount _value,
      $Res Function(_$_FetchVirtualAccount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = null,
    Object? selectedAmount = null,
  }) {
    return _then(_$_FetchVirtualAccount(
      contractNumber: null == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String,
      selectedAmount: null == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
    ));
  }
}

/// @nodoc

class _$_FetchVirtualAccount implements _FetchVirtualAccount {
  const _$_FetchVirtualAccount(
      {required this.contractNumber, required this.selectedAmount});

  @override
  final String contractNumber;
  @override
  final Decimal selectedAmount;

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.fetchVirtualAccount(contractNumber: $contractNumber, selectedAmount: $selectedAmount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FetchVirtualAccount &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractNumber, selectedAmount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FetchVirtualAccountCopyWith<_$_FetchVirtualAccount> get copyWith =>
      __$$_FetchVirtualAccountCopyWithImpl<_$_FetchVirtualAccount>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return fetchVirtualAccount(contractNumber, selectedAmount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return fetchVirtualAccount?.call(contractNumber, selectedAmount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (fetchVirtualAccount != null) {
      return fetchVirtualAccount(contractNumber, selectedAmount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return fetchVirtualAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return fetchVirtualAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (fetchVirtualAccount != null) {
      return fetchVirtualAccount(this);
    }
    return orElse();
  }
}

abstract class _FetchVirtualAccount implements RepaymentSelectAmountCelEvent {
  const factory _FetchVirtualAccount(
      {required final String contractNumber,
      required final Decimal selectedAmount}) = _$_FetchVirtualAccount;

  String get contractNumber;
  Decimal get selectedAmount;
  @JsonKey(ignore: true)
  _$$_FetchVirtualAccountCopyWith<_$_FetchVirtualAccount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_ResetVirtualAccountCopyWith<$Res> {
  factory _$$_ResetVirtualAccountCopyWith(_$_ResetVirtualAccount value,
          $Res Function(_$_ResetVirtualAccount) then) =
      __$$_ResetVirtualAccountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ResetVirtualAccountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        _$_ResetVirtualAccount>
    implements _$$_ResetVirtualAccountCopyWith<$Res> {
  __$$_ResetVirtualAccountCopyWithImpl(_$_ResetVirtualAccount _value,
      $Res Function(_$_ResetVirtualAccount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_ResetVirtualAccount implements _ResetVirtualAccount {
  const _$_ResetVirtualAccount();

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.resetVirtualAccount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_ResetVirtualAccount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
    required TResult Function(String contractNumber, Decimal selectedAmount)
        fetchVirtualAccount,
    required TResult Function() resetVirtualAccount,
  }) {
    return resetVirtualAccount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
    TResult? Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult? Function()? resetVirtualAccount,
  }) {
    return resetVirtualAccount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract)? initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    TResult Function(String contractNumber, Decimal selectedAmount)?
        fetchVirtualAccount,
    TResult Function()? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (resetVirtualAccount != null) {
      return resetVirtualAccount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
    required TResult Function(_FetchVirtualAccount value) fetchVirtualAccount,
    required TResult Function(_ResetVirtualAccount value) resetVirtualAccount,
  }) {
    return resetVirtualAccount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
    TResult? Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult? Function(_ResetVirtualAccount value)? resetVirtualAccount,
  }) {
    return resetVirtualAccount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    TResult Function(_FetchVirtualAccount value)? fetchVirtualAccount,
    TResult Function(_ResetVirtualAccount value)? resetVirtualAccount,
    required TResult orElse(),
  }) {
    if (resetVirtualAccount != null) {
      return resetVirtualAccount(this);
    }
    return orElse();
  }
}

abstract class _ResetVirtualAccount implements RepaymentSelectAmountCelEvent {
  const factory _ResetVirtualAccount() = _$_ResetVirtualAccount;
}

/// @nodoc
mixin _$RepaymentSelectAmountCelState {
  bool? get isError => throw _privateConstructorUsedError;
  LoadingState get loadingState => throw _privateConstructorUsedError;
  RepaymentLoanContract? get repaymentLoanContract =>
      throw _privateConstructorUsedError;
  Decimal get dueAmount => throw _privateConstructorUsedError;
  Decimal get thresholdAmount => throw _privateConstructorUsedError;
  Decimal? get selectedAmount => throw _privateConstructorUsedError;
  bool get isDueAmountSelected => throw _privateConstructorUsedError;
  bool get isCustomAmountSelected => throw _privateConstructorUsedError;
  bool get isCustomAmountLessThanDueAmount =>
      throw _privateConstructorUsedError;
  bool get isCustomAmountLessThanThresholdAmount =>
      throw _privateConstructorUsedError;
  bool get isDueAmountLessThanThresholdAmount =>
      throw _privateConstructorUsedError;
  bool get isOverTotalDebt => throw _privateConstructorUsedError;
  bool get preventOverPayment => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      get failureOrSuccessVirtualAccount => throw _privateConstructorUsedError;
  RepaymentContractVirtualAccount? get contractVirtualAccount =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentSelectAmountCelStateCopyWith<RepaymentSelectAmountCelState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentSelectAmountCelStateCopyWith<$Res> {
  factory $RepaymentSelectAmountCelStateCopyWith(
          RepaymentSelectAmountCelState value,
          $Res Function(RepaymentSelectAmountCelState) then) =
      _$RepaymentSelectAmountCelStateCopyWithImpl<$Res,
          RepaymentSelectAmountCelState>;
  @useResult
  $Res call(
      {bool? isError,
      LoadingState loadingState,
      RepaymentLoanContract? repaymentLoanContract,
      Decimal dueAmount,
      Decimal thresholdAmount,
      Decimal? selectedAmount,
      bool isDueAmountSelected,
      bool isCustomAmountSelected,
      bool isCustomAmountLessThanDueAmount,
      bool isCustomAmountLessThanThresholdAmount,
      bool isDueAmountLessThanThresholdAmount,
      bool isOverTotalDebt,
      bool preventOverPayment,
      Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount,
      RepaymentContractVirtualAccount? contractVirtualAccount});
}

/// @nodoc
class _$RepaymentSelectAmountCelStateCopyWithImpl<$Res,
        $Val extends RepaymentSelectAmountCelState>
    implements $RepaymentSelectAmountCelStateCopyWith<$Res> {
  _$RepaymentSelectAmountCelStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isError = freezed,
    Object? loadingState = null,
    Object? repaymentLoanContract = freezed,
    Object? dueAmount = null,
    Object? thresholdAmount = null,
    Object? selectedAmount = freezed,
    Object? isDueAmountSelected = null,
    Object? isCustomAmountSelected = null,
    Object? isCustomAmountLessThanDueAmount = null,
    Object? isCustomAmountLessThanThresholdAmount = null,
    Object? isDueAmountLessThanThresholdAmount = null,
    Object? isOverTotalDebt = null,
    Object? preventOverPayment = null,
    Object? failureOrSuccessVirtualAccount = null,
    Object? contractVirtualAccount = freezed,
  }) {
    return _then(_value.copyWith(
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      repaymentLoanContract: freezed == repaymentLoanContract
          ? _value.repaymentLoanContract
          : repaymentLoanContract // ignore: cast_nullable_to_non_nullable
              as RepaymentLoanContract?,
      dueAmount: null == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      thresholdAmount: null == thresholdAmount
          ? _value.thresholdAmount
          : thresholdAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      isDueAmountSelected: null == isDueAmountSelected
          ? _value.isDueAmountSelected
          : isDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountSelected: null == isCustomAmountSelected
          ? _value.isCustomAmountSelected
          : isCustomAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountLessThanDueAmount: null == isCustomAmountLessThanDueAmount
          ? _value.isCustomAmountLessThanDueAmount
          : isCustomAmountLessThanDueAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountLessThanThresholdAmount: null ==
              isCustomAmountLessThanThresholdAmount
          ? _value.isCustomAmountLessThanThresholdAmount
          : isCustomAmountLessThanThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isDueAmountLessThanThresholdAmount: null ==
              isDueAmountLessThanThresholdAmount
          ? _value.isDueAmountLessThanThresholdAmount
          : isDueAmountLessThanThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverTotalDebt: null == isOverTotalDebt
          ? _value.isOverTotalDebt
          : isOverTotalDebt // ignore: cast_nullable_to_non_nullable
              as bool,
      preventOverPayment: null == preventOverPayment
          ? _value.preventOverPayment
          : preventOverPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessVirtualAccount: null == failureOrSuccessVirtualAccount
          ? _value.failureOrSuccessVirtualAccount
          : failureOrSuccessVirtualAccount // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentContractVirtualAccount>>,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentSelectAmountCelStateCopyWith<$Res>
    implements $RepaymentSelectAmountCelStateCopyWith<$Res> {
  factory _$$_RepaymentSelectAmountCelStateCopyWith(
          _$_RepaymentSelectAmountCelState value,
          $Res Function(_$_RepaymentSelectAmountCelState) then) =
      __$$_RepaymentSelectAmountCelStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? isError,
      LoadingState loadingState,
      RepaymentLoanContract? repaymentLoanContract,
      Decimal dueAmount,
      Decimal thresholdAmount,
      Decimal? selectedAmount,
      bool isDueAmountSelected,
      bool isCustomAmountSelected,
      bool isCustomAmountLessThanDueAmount,
      bool isCustomAmountLessThanThresholdAmount,
      bool isDueAmountLessThanThresholdAmount,
      bool isOverTotalDebt,
      bool preventOverPayment,
      Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
          failureOrSuccessVirtualAccount,
      RepaymentContractVirtualAccount? contractVirtualAccount});
}

/// @nodoc
class __$$_RepaymentSelectAmountCelStateCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelStateCopyWithImpl<$Res,
        _$_RepaymentSelectAmountCelState>
    implements _$$_RepaymentSelectAmountCelStateCopyWith<$Res> {
  __$$_RepaymentSelectAmountCelStateCopyWithImpl(
      _$_RepaymentSelectAmountCelState _value,
      $Res Function(_$_RepaymentSelectAmountCelState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isError = freezed,
    Object? loadingState = null,
    Object? repaymentLoanContract = freezed,
    Object? dueAmount = null,
    Object? thresholdAmount = null,
    Object? selectedAmount = freezed,
    Object? isDueAmountSelected = null,
    Object? isCustomAmountSelected = null,
    Object? isCustomAmountLessThanDueAmount = null,
    Object? isCustomAmountLessThanThresholdAmount = null,
    Object? isDueAmountLessThanThresholdAmount = null,
    Object? isOverTotalDebt = null,
    Object? preventOverPayment = null,
    Object? failureOrSuccessVirtualAccount = null,
    Object? contractVirtualAccount = freezed,
  }) {
    return _then(_$_RepaymentSelectAmountCelState(
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      repaymentLoanContract: freezed == repaymentLoanContract
          ? _value.repaymentLoanContract
          : repaymentLoanContract // ignore: cast_nullable_to_non_nullable
              as RepaymentLoanContract?,
      dueAmount: null == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      thresholdAmount: null == thresholdAmount
          ? _value.thresholdAmount
          : thresholdAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      isDueAmountSelected: null == isDueAmountSelected
          ? _value.isDueAmountSelected
          : isDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountSelected: null == isCustomAmountSelected
          ? _value.isCustomAmountSelected
          : isCustomAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountLessThanDueAmount: null == isCustomAmountLessThanDueAmount
          ? _value.isCustomAmountLessThanDueAmount
          : isCustomAmountLessThanDueAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountLessThanThresholdAmount: null ==
              isCustomAmountLessThanThresholdAmount
          ? _value.isCustomAmountLessThanThresholdAmount
          : isCustomAmountLessThanThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isDueAmountLessThanThresholdAmount: null ==
              isDueAmountLessThanThresholdAmount
          ? _value.isDueAmountLessThanThresholdAmount
          : isDueAmountLessThanThresholdAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverTotalDebt: null == isOverTotalDebt
          ? _value.isOverTotalDebt
          : isOverTotalDebt // ignore: cast_nullable_to_non_nullable
              as bool,
      preventOverPayment: null == preventOverPayment
          ? _value.preventOverPayment
          : preventOverPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessVirtualAccount: null == failureOrSuccessVirtualAccount
          ? _value.failureOrSuccessVirtualAccount
          : failureOrSuccessVirtualAccount // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<RepaymentFailure, RepaymentContractVirtualAccount>>,
      contractVirtualAccount: freezed == contractVirtualAccount
          ? _value.contractVirtualAccount
          : contractVirtualAccount // ignore: cast_nullable_to_non_nullable
              as RepaymentContractVirtualAccount?,
    ));
  }
}

/// @nodoc

class _$_RepaymentSelectAmountCelState
    implements _RepaymentSelectAmountCelState {
  const _$_RepaymentSelectAmountCelState(
      {this.isError,
      required this.loadingState,
      required this.repaymentLoanContract,
      required this.dueAmount,
      required this.thresholdAmount,
      required this.selectedAmount,
      required this.isDueAmountSelected,
      required this.isCustomAmountSelected,
      required this.isCustomAmountLessThanDueAmount,
      required this.isCustomAmountLessThanThresholdAmount,
      required this.isDueAmountLessThanThresholdAmount,
      required this.isOverTotalDebt,
      required this.preventOverPayment,
      required this.failureOrSuccessVirtualAccount,
      this.contractVirtualAccount});

  @override
  final bool? isError;
  @override
  final LoadingState loadingState;
  @override
  final RepaymentLoanContract? repaymentLoanContract;
  @override
  final Decimal dueAmount;
  @override
  final Decimal thresholdAmount;
  @override
  final Decimal? selectedAmount;
  @override
  final bool isDueAmountSelected;
  @override
  final bool isCustomAmountSelected;
  @override
  final bool isCustomAmountLessThanDueAmount;
  @override
  final bool isCustomAmountLessThanThresholdAmount;
  @override
  final bool isDueAmountLessThanThresholdAmount;
  @override
  final bool isOverTotalDebt;
  @override
  final bool preventOverPayment;
  @override
  final Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      failureOrSuccessVirtualAccount;
  @override
  final RepaymentContractVirtualAccount? contractVirtualAccount;

  @override
  String toString() {
    return 'RepaymentSelectAmountCelState(isError: $isError, loadingState: $loadingState, repaymentLoanContract: $repaymentLoanContract, dueAmount: $dueAmount, thresholdAmount: $thresholdAmount, selectedAmount: $selectedAmount, isDueAmountSelected: $isDueAmountSelected, isCustomAmountSelected: $isCustomAmountSelected, isCustomAmountLessThanDueAmount: $isCustomAmountLessThanDueAmount, isCustomAmountLessThanThresholdAmount: $isCustomAmountLessThanThresholdAmount, isDueAmountLessThanThresholdAmount: $isDueAmountLessThanThresholdAmount, isOverTotalDebt: $isOverTotalDebt, preventOverPayment: $preventOverPayment, failureOrSuccessVirtualAccount: $failureOrSuccessVirtualAccount, contractVirtualAccount: $contractVirtualAccount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentSelectAmountCelState &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.repaymentLoanContract, repaymentLoanContract) ||
                other.repaymentLoanContract == repaymentLoanContract) &&
            (identical(other.dueAmount, dueAmount) ||
                other.dueAmount == dueAmount) &&
            (identical(other.thresholdAmount, thresholdAmount) ||
                other.thresholdAmount == thresholdAmount) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.isDueAmountSelected, isDueAmountSelected) ||
                other.isDueAmountSelected == isDueAmountSelected) &&
            (identical(other.isCustomAmountSelected, isCustomAmountSelected) ||
                other.isCustomAmountSelected == isCustomAmountSelected) &&
            (identical(other.isCustomAmountLessThanDueAmount,
                    isCustomAmountLessThanDueAmount) ||
                other.isCustomAmountLessThanDueAmount ==
                    isCustomAmountLessThanDueAmount) &&
            (identical(other.isCustomAmountLessThanThresholdAmount,
                    isCustomAmountLessThanThresholdAmount) ||
                other.isCustomAmountLessThanThresholdAmount ==
                    isCustomAmountLessThanThresholdAmount) &&
            (identical(other.isDueAmountLessThanThresholdAmount,
                    isDueAmountLessThanThresholdAmount) ||
                other.isDueAmountLessThanThresholdAmount ==
                    isDueAmountLessThanThresholdAmount) &&
            (identical(other.isOverTotalDebt, isOverTotalDebt) ||
                other.isOverTotalDebt == isOverTotalDebt) &&
            (identical(other.preventOverPayment, preventOverPayment) ||
                other.preventOverPayment == preventOverPayment) &&
            (identical(other.failureOrSuccessVirtualAccount,
                    failureOrSuccessVirtualAccount) ||
                other.failureOrSuccessVirtualAccount ==
                    failureOrSuccessVirtualAccount) &&
            (identical(other.contractVirtualAccount, contractVirtualAccount) ||
                other.contractVirtualAccount == contractVirtualAccount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isError,
      loadingState,
      repaymentLoanContract,
      dueAmount,
      thresholdAmount,
      selectedAmount,
      isDueAmountSelected,
      isCustomAmountSelected,
      isCustomAmountLessThanDueAmount,
      isCustomAmountLessThanThresholdAmount,
      isDueAmountLessThanThresholdAmount,
      isOverTotalDebt,
      preventOverPayment,
      failureOrSuccessVirtualAccount,
      contractVirtualAccount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentSelectAmountCelStateCopyWith<_$_RepaymentSelectAmountCelState>
      get copyWith => __$$_RepaymentSelectAmountCelStateCopyWithImpl<
          _$_RepaymentSelectAmountCelState>(this, _$identity);
}

abstract class _RepaymentSelectAmountCelState
    implements RepaymentSelectAmountCelState {
  const factory _RepaymentSelectAmountCelState(
          {final bool? isError,
          required final LoadingState loadingState,
          required final RepaymentLoanContract? repaymentLoanContract,
          required final Decimal dueAmount,
          required final Decimal thresholdAmount,
          required final Decimal? selectedAmount,
          required final bool isDueAmountSelected,
          required final bool isCustomAmountSelected,
          required final bool isCustomAmountLessThanDueAmount,
          required final bool isCustomAmountLessThanThresholdAmount,
          required final bool isDueAmountLessThanThresholdAmount,
          required final bool isOverTotalDebt,
          required final bool preventOverPayment,
          required final Option<
                  Either<RepaymentFailure, RepaymentContractVirtualAccount>>
              failureOrSuccessVirtualAccount,
          final RepaymentContractVirtualAccount? contractVirtualAccount}) =
      _$_RepaymentSelectAmountCelState;

  @override
  bool? get isError;
  @override
  LoadingState get loadingState;
  @override
  RepaymentLoanContract? get repaymentLoanContract;
  @override
  Decimal get dueAmount;
  @override
  Decimal get thresholdAmount;
  @override
  Decimal? get selectedAmount;
  @override
  bool get isDueAmountSelected;
  @override
  bool get isCustomAmountSelected;
  @override
  bool get isCustomAmountLessThanDueAmount;
  @override
  bool get isCustomAmountLessThanThresholdAmount;
  @override
  bool get isDueAmountLessThanThresholdAmount;
  @override
  bool get isOverTotalDebt;
  @override
  bool get preventOverPayment;
  @override
  Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>>
      get failureOrSuccessVirtualAccount;
  @override
  RepaymentContractVirtualAccount? get contractVirtualAccount;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentSelectAmountCelStateCopyWith<_$_RepaymentSelectAmountCelState>
      get copyWith => throw _privateConstructorUsedError;
}
