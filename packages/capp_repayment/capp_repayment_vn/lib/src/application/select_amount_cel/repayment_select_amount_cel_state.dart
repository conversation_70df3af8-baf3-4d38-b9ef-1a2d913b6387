part of 'repayment_select_amount_cel_bloc.dart';

@freezed
class RepaymentSelectAmountCelState with _$RepaymentSelectAmountCelState {
  const factory RepaymentSelectAmountCelState({
    bool? isError,
    required LoadingState loadingState,
    required RepaymentLoanContract? repaymentLoanContract,
    required Decimal dueAmount,
    required Decimal thresholdAmount,
    required Decimal? selectedAmount,
    required bool isDueAmountSelected,
    required bool isCustomAmountSelected,
    required bool isCustomAmountLessThanDueAmount,
    required bool isCustomAmountLessThanThresholdAmount,
    required bool isDueAmountLessThanThresholdAmount,
    required bool isOverTotalDebt,
    required bool preventOverPayment,
    required Option<Either<RepaymentFailure, RepaymentContractVirtualAccount>> failureOrSuccessVirtualAccount,
    RepaymentContractVirtualAccount? contractVirtualAccount,
  }) = _RepaymentSelectAmountCelState;

  factory RepaymentSelectAmountCelState.initialize() => RepaymentSelectAmountCelState(
        isError: false,
        loadingState: LoadingState.isInitial,
        repaymentLoanContract: null,
        dueAmount: Decimal.fromInt(0),
        thresholdAmount: Decimal.fromInt(0),
        selectedAmount: null,
        isDueAmountSelected: false,
        isCustomAmountSelected: false,
        isCustomAmountLessThanDueAmount: false,
        isCustomAmountLessThanThresholdAmount: false,
        isDueAmountLessThanThresholdAmount: false,
        isOverTotalDebt: false,
        preventOverPayment: false,
        failureOrSuccessVirtualAccount: none(),
      );
}
