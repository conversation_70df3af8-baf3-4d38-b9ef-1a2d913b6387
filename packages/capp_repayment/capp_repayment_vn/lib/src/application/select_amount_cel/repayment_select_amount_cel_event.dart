part of 'repayment_select_amount_cel_bloc.dart';

@freezed
class RepaymentSelectAmountCelEvent with _$RepaymentSelectAmountCelEvent {
  const factory RepaymentSelectAmountCelEvent.initialize({
    required RepaymentLoanContract repaymentLoanContract,
  }) = _Initialize;

  const factory RepaymentSelectAmountCelEvent.selectDueAmount() = _SelectDueAmount;

  const factory RepaymentSelectAmountCelEvent.selectCustomAmount() = _SelectCustomAmount;

  const factory RepaymentSelectAmountCelEvent.setAmount(Decimal? amount) = _SetAmount;

  const factory RepaymentSelectAmountCelEvent.fetchVirtualAccount({
    required String contractNumber,
    required Decimal selectedAmount,
  }) = _FetchVirtualAccount;

  const factory RepaymentSelectAmountCelEvent.resetVirtualAccount() = _ResetVirtualAccount;
}
