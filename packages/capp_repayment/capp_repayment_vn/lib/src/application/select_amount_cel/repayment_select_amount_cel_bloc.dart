import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_repayment.dart';

part 'repayment_select_amount_cel_bloc.freezed.dart';
part 'repayment_select_amount_cel_event.dart';
part 'repayment_select_amount_cel_state.dart';

class RepaymentSelectAmountCelBloc extends Bloc<RepaymentSelectAmountCelEvent, RepaymentSelectAmountCelState> {
  final IRepaymentRepository repaymentRepository;
  final Logger logger;

  RepaymentSelectAmountCelBloc({required this.repaymentRepository, required this.logger})
      : super(RepaymentSelectAmountCelState.initialize()) {
    on<RepaymentSelectAmountCelEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          selectDueAmount: (e) => _selectDueAmount(e, emit),
          selectCustomAmount: (e) => _selectCustomAmount(e, emit),
          setAmount: (e) => _setAmount(e, emit),
          fetchVirtualAccount: (e) => _fetchVirtualAccount(e, emit),
          resetVirtualAccount: (e) => _resetVirtualAccount(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentSelectAmountCelState> emit,
  ) async {
    final minimumThresholdAmount = e.repaymentLoanContract.totalOutstandingDebt! < defaultMinThresholdAmount
        ? e.repaymentLoanContract.totalOutstandingDebt!
        : defaultMinThresholdAmount;
    final dueAmount = e.repaymentLoanContract.dueAmount ?? state.dueAmount;
    final isDueAmountLessThanMinThresholdAmount = dueAmount < minimumThresholdAmount;
    emit(
      state.copyWith(
        repaymentLoanContract: e.repaymentLoanContract,
        dueAmount: dueAmount,
        thresholdAmount: minimumThresholdAmount,
        selectedAmount: null,
        isDueAmountSelected: false,
        isCustomAmountSelected: false,
        isDueAmountLessThanThresholdAmount: isDueAmountLessThanMinThresholdAmount,
        isCustomAmountLessThanDueAmount: false,
        isCustomAmountLessThanThresholdAmount: false,
        isOverTotalDebt: false,
        preventOverPayment: true,
      ),
    );
    if (isDueAmountLessThanMinThresholdAmount) {
      add(const RepaymentSelectAmountCelEvent.selectCustomAmount());
    } else {
      add(const RepaymentSelectAmountCelEvent.selectDueAmount());
    }
  }

  Future<void> _selectDueAmount(
    _SelectDueAmount e,
    Emitter<RepaymentSelectAmountCelState> emit,
  ) async {
    emit(
      state.copyWith(
        isDueAmountSelected: true,
        isCustomAmountSelected: false,
        isCustomAmountLessThanThresholdAmount: false,
        isCustomAmountLessThanDueAmount: false,
        isOverTotalDebt: false,
        selectedAmount: state.dueAmount,
      ),
    );
  }

  Future<void> _selectCustomAmount(
    _SelectCustomAmount e,
    Emitter<RepaymentSelectAmountCelState> emit,
  ) async {
    emit(
      state.copyWith(
        isDueAmountSelected: false,
        isCustomAmountSelected: true,
        isCustomAmountLessThanThresholdAmount: false,
        isCustomAmountLessThanDueAmount: false,
        isOverTotalDebt: false,
        selectedAmount: null,
      ),
    );
  }

  Future<void> _setAmount(
    _SetAmount e,
    Emitter<RepaymentSelectAmountCelState> emit,
  ) async {
    if (e.amount != null) {
      final isCustomAmountLessThanThresholdAmount = e.amount! < state.thresholdAmount;
      final isCustomAmountLessThanDueAmount = e.amount! < state.dueAmount;
      var selectedAmount = e.amount;
      final overTotalDebt =
          (e.amount ?? Decimal.fromInt(0)) >= (state.repaymentLoanContract?.totalOutstandingDebt ?? Decimal.fromInt(0));
      if (state.preventOverPayment && overTotalDebt) {
        selectedAmount = state.repaymentLoanContract?.totalOutstandingDebt;
      }
      emit(
        state.copyWith(
          selectedAmount: selectedAmount,
          isCustomAmountLessThanThresholdAmount: isCustomAmountLessThanThresholdAmount,
          isCustomAmountLessThanDueAmount: isCustomAmountLessThanDueAmount,
          isOverTotalDebt: overTotalDebt,
        ),
      );
    } else {
      emit(
        state.copyWith(
          selectedAmount: null,
          isCustomAmountLessThanThresholdAmount: false,
          isCustomAmountLessThanDueAmount: false,
          isOverTotalDebt: false,
        ),
      );
    }
  }

  Future<void> _fetchVirtualAccount(
    _FetchVirtualAccount e,
    Emitter<RepaymentSelectAmountCelState> emit,
  ) async {
    final selectedAmount = e.selectedAmount;
    final contractNumber = e.contractNumber;
    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false));

    final response = await repaymentRepository.getVirtualAccount(
      contractNumber: contractNumber,
      selectedAmount: selectedAmount.toBigInt().toInt(),
    );
    logger.d('getVirtualAccount response$response');

    emit(
      response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: true,
          failureOrSuccessVirtualAccount: optionOf(response),
        );
      }, (r) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessVirtualAccount: optionOf(response),
          contractVirtualAccount: r,
          isError: false,
        );
      }),
    );
  }

  Future<void> _resetVirtualAccount(
    _ResetVirtualAccount e,
    Emitter<RepaymentSelectAmountCelState> emit,
  ) async {
    emit(
      state.copyWith(
        loadingState: LoadingState.isInitial,
        failureOrSuccessVirtualAccount: none(),
        isError: false,
        contractVirtualAccount: null,
      ),
    );
  }

  final defaultMinThresholdAmount = Decimal.parse('50000');
}
