import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_repayment.dart';

part 'repayment_promotion_detail_bloc.freezed.dart';
part 'repayment_promotion_detail_event.dart';
part 'repayment_promotion_detail_state.dart';

class RepaymentPromotionDetailBloc extends Bloc<RepaymentPromotionDetailEvent, RepaymentPromotionDetailState> {
  final Logger logger;

  RepaymentPromotionDetailBloc({
    required this.logger,
  }) : super(RepaymentPromotionDetailState.initialize()) {
    on<_Initialize>(_initialize);
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentPromotionDetailState> emit,
  ) async {
    emit(
      state.copyWith(
        isApplied: e.isApplied ?? false,
        selectedVoucher: e.selectedVoucher,
      ),
    );
  }
}
