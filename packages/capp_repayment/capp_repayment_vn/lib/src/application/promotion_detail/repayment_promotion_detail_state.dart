part of 'repayment_promotion_detail_bloc.dart';

@freezed
class RepaymentPromotionDetailState with _$RepaymentPromotionDetailState {
  const factory RepaymentPromotionDetailState({
    required LoadingState loadingState,
    bool? isError,
    RepaymentVoucher? selectedVoucher,
    @Default(false) bool isApplied,
  }) = _RepaymentPromotionDetailState;

  factory RepaymentPromotionDetailState.initialize() => const RepaymentPromotionDetailState(
        loadingState: LoadingState.isInitial,
        isError: false,
      );
}
