// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_promotion_detail_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPromotionDetailEvent {
  RepaymentVoucher? get selectedVoucher => throw _privateConstructorUsedError;
  bool? get isApplied => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentVoucher? selectedVoucher, bool? isApplied)
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentVoucher? selectedVoucher, bool? isApplied)?
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentVoucher? selectedVoucher, bool? isApplied)?
        initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPromotionDetailEventCopyWith<RepaymentPromotionDetailEvent>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPromotionDetailEventCopyWith<$Res> {
  factory $RepaymentPromotionDetailEventCopyWith(
          RepaymentPromotionDetailEvent value,
          $Res Function(RepaymentPromotionDetailEvent) then) =
      _$RepaymentPromotionDetailEventCopyWithImpl<$Res,
          RepaymentPromotionDetailEvent>;
  @useResult
  $Res call({RepaymentVoucher? selectedVoucher, bool? isApplied});
}

/// @nodoc
class _$RepaymentPromotionDetailEventCopyWithImpl<$Res,
        $Val extends RepaymentPromotionDetailEvent>
    implements $RepaymentPromotionDetailEventCopyWith<$Res> {
  _$RepaymentPromotionDetailEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedVoucher = freezed,
    Object? isApplied = freezed,
  }) {
    return _then(_value.copyWith(
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      isApplied: freezed == isApplied
          ? _value.isApplied
          : isApplied // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res>
    implements $RepaymentPromotionDetailEventCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({RepaymentVoucher? selectedVoucher, bool? isApplied});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPromotionDetailEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedVoucher = freezed,
    Object? isApplied = freezed,
  }) {
    return _then(_$_Initialize(
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      isApplied: freezed == isApplied
          ? _value.isApplied
          : isApplied // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({this.selectedVoucher, this.isApplied});

  @override
  final RepaymentVoucher? selectedVoucher;
  @override
  final bool? isApplied;

  @override
  String toString() {
    return 'RepaymentPromotionDetailEvent.initialize(selectedVoucher: $selectedVoucher, isApplied: $isApplied)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.selectedVoucher, selectedVoucher) ||
                other.selectedVoucher == selectedVoucher) &&
            (identical(other.isApplied, isApplied) ||
                other.isApplied == isApplied));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedVoucher, isApplied);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentVoucher? selectedVoucher, bool? isApplied)
        initialize,
  }) {
    return initialize(selectedVoucher, isApplied);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentVoucher? selectedVoucher, bool? isApplied)?
        initialize,
  }) {
    return initialize?.call(selectedVoucher, isApplied);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentVoucher? selectedVoucher, bool? isApplied)?
        initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(selectedVoucher, isApplied);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPromotionDetailEvent {
  const factory _Initialize(
      {final RepaymentVoucher? selectedVoucher,
      final bool? isApplied}) = _$_Initialize;

  @override
  RepaymentVoucher? get selectedVoucher;
  @override
  bool? get isApplied;
  @override
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentPromotionDetailState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  RepaymentVoucher? get selectedVoucher => throw _privateConstructorUsedError;
  bool get isApplied => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPromotionDetailStateCopyWith<RepaymentPromotionDetailState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPromotionDetailStateCopyWith<$Res> {
  factory $RepaymentPromotionDetailStateCopyWith(
          RepaymentPromotionDetailState value,
          $Res Function(RepaymentPromotionDetailState) then) =
      _$RepaymentPromotionDetailStateCopyWithImpl<$Res,
          RepaymentPromotionDetailState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      RepaymentVoucher? selectedVoucher,
      bool isApplied});
}

/// @nodoc
class _$RepaymentPromotionDetailStateCopyWithImpl<$Res,
        $Val extends RepaymentPromotionDetailState>
    implements $RepaymentPromotionDetailStateCopyWith<$Res> {
  _$RepaymentPromotionDetailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedVoucher = freezed,
    Object? isApplied = null,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      isApplied: null == isApplied
          ? _value.isApplied
          : isApplied // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPromotionDetailStateCopyWith<$Res>
    implements $RepaymentPromotionDetailStateCopyWith<$Res> {
  factory _$$_RepaymentPromotionDetailStateCopyWith(
          _$_RepaymentPromotionDetailState value,
          $Res Function(_$_RepaymentPromotionDetailState) then) =
      __$$_RepaymentPromotionDetailStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      RepaymentVoucher? selectedVoucher,
      bool isApplied});
}

/// @nodoc
class __$$_RepaymentPromotionDetailStateCopyWithImpl<$Res>
    extends _$RepaymentPromotionDetailStateCopyWithImpl<$Res,
        _$_RepaymentPromotionDetailState>
    implements _$$_RepaymentPromotionDetailStateCopyWith<$Res> {
  __$$_RepaymentPromotionDetailStateCopyWithImpl(
      _$_RepaymentPromotionDetailState _value,
      $Res Function(_$_RepaymentPromotionDetailState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedVoucher = freezed,
    Object? isApplied = null,
  }) {
    return _then(_$_RepaymentPromotionDetailState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      isApplied: null == isApplied
          ? _value.isApplied
          : isApplied // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_RepaymentPromotionDetailState
    implements _RepaymentPromotionDetailState {
  const _$_RepaymentPromotionDetailState(
      {required this.loadingState,
      this.isError,
      this.selectedVoucher,
      this.isApplied = false});

  @override
  final LoadingState loadingState;
  @override
  final bool? isError;
  @override
  final RepaymentVoucher? selectedVoucher;
  @override
  @JsonKey()
  final bool isApplied;

  @override
  String toString() {
    return 'RepaymentPromotionDetailState(loadingState: $loadingState, isError: $isError, selectedVoucher: $selectedVoucher, isApplied: $isApplied)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPromotionDetailState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.selectedVoucher, selectedVoucher) ||
                other.selectedVoucher == selectedVoucher) &&
            (identical(other.isApplied, isApplied) ||
                other.isApplied == isApplied));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, loadingState, isError, selectedVoucher, isApplied);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPromotionDetailStateCopyWith<_$_RepaymentPromotionDetailState>
      get copyWith => __$$_RepaymentPromotionDetailStateCopyWithImpl<
          _$_RepaymentPromotionDetailState>(this, _$identity);
}

abstract class _RepaymentPromotionDetailState
    implements RepaymentPromotionDetailState {
  const factory _RepaymentPromotionDetailState(
      {required final LoadingState loadingState,
      final bool? isError,
      final RepaymentVoucher? selectedVoucher,
      final bool isApplied}) = _$_RepaymentPromotionDetailState;

  @override
  LoadingState get loadingState;
  @override
  bool? get isError;
  @override
  RepaymentVoucher? get selectedVoucher;
  @override
  bool get isApplied;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPromotionDetailStateCopyWith<_$_RepaymentPromotionDetailState>
      get copyWith => throw _privateConstructorUsedError;
}
