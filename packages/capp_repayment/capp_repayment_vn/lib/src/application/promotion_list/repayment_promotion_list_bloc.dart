import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../../capp_repayment.dart';

part 'repayment_promotion_list_bloc.freezed.dart';
part 'repayment_promotion_list_event.dart';
part 'repayment_promotion_list_state.dart';

class RepaymentPromotionListBloc extends Bloc<RepaymentPromotionListEvent, RepaymentPromotionListState> {
  final Logger logger;
  final IPromotionRepository promotionRepository;
  final IFeatureFlagRepository featureFlagRepository;

  RepaymentPromotionListBloc({
    required this.logger,
    required this.promotionRepository,
    required this.featureFlagRepository,
  }) : super(RepaymentPromotionListState.initialize()) {
    on<RepaymentPromotionListEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          selectVoucher: (e) => _selectVoucher(e, emit),
          fetchVouchers: (e) => _fetchVouchers(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(
    _Initialize e,
    Emitter<RepaymentPromotionListState> emit,
  ) async {
    emit(
      state.copyWith(
        selectedAmount: e.selectedAmount,
        selectedContract: e.selectedContract,
        selectedPaymentMethod: e.selectedPaymentMethod,
        selectedBank: e.selectedBank,
        appliedVoucher: e.appliedVoucher,
      ),
    );

    add(const RepaymentPromotionListEvent.fetchVouchers());
  }

  Future<void> _selectVoucher(
    _SelectVoucher e,
    Emitter<RepaymentPromotionListState> emit,
  ) async {
    emit(
      state.copyWith(
        selectedVoucher: e.selectedVoucher,
        isValidInput: true,
      ),
    );
  }

  Future<void> _fetchVouchers(
    _FetchVouchers e,
    Emitter<RepaymentPromotionListState> emit,
  ) async {
    emit(state.copyWith(loadingState: LoadingState.isLoading, isError: false));
    final contractNumber = state.selectedContract?.contractNumber ?? '';
    final selectedAmount = state.selectedAmount ?? Decimal.zero;
    final selectedPaymentMethod = state.selectedPaymentMethod;
    var selectedPaymentMethodString = selectedPaymentMethod?.gmaId ?? '';
    final selectedBank = state.selectedBank;
    if (selectedPaymentMethod?.gmaId == RepaymentPaymentMethod.mobileBanking.getId() && selectedBank != null) {
      selectedPaymentMethodString += '_${selectedBank.shortName}';
    }
    final isEntrustmentLending = state.selectedContract?.loanContract?.isEntrustmentLending ?? false;
    final allowDirectDiscountEL = featureFlagRepository.isEnabledCached(FeatureFlag.repaymentAllowDirectDiscountEL);

    final isSappi = state.selectedContract?.loanContract?.sappiInstallmentAmount != null;
    final isAllowSappiPromotion = featureFlagRepository.isEnabledCached(FeatureFlag.repaymentSappiPromotionAllowance);

    if ((isEntrustmentLending && !allowDirectDiscountEL) || (isSappi && !isAllowSappiPromotion)) {
      await Future<void>.delayed(const Duration(seconds: 1));
      // Return empty voucher list for EL contract or contract with SAPPI
      emit(
        state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccessFetchVouchers: optionOf(right([])),
          isError: false,
          selectedVoucher: null,
          isValidInput: false,
          vouchers: [],
        ),
      );
    } else {
      final response = await promotionRepository.fetchVouchersV2(
        RepaymentVoucherRequest(
          contractNumber: contractNumber,
          selectedAmount: selectedAmount,
          selectedPaymentMethod: selectedPaymentMethodString,
        ),
      );
      logger.d('fetch vouchers response$response');

      emit(
        await response.fold((l) {
          return state.copyWith(
            loadingState: LoadingState.isCompleted,
            isError: true,
            failureOrSuccessFetchVouchers: optionOf(response),
          );
        }, (r) async {
          final appliedVoucher = state.appliedVoucher;
          var isValidInput = false;
          final isVoucherValid = appliedVoucher?.isValid ?? false;

          if (r.map((e) => e.code).toList().contains(appliedVoucher?.code)) {
            if (isVoucherValid) {
              isValidInput = true;
            }
          }
          return state.copyWith(
            loadingState: LoadingState.isCompleted,
            failureOrSuccessFetchVouchers: optionOf(response),
            isError: false,
            selectedVoucher: isVoucherValid ? appliedVoucher : null,
            isValidInput: isValidInput,
            vouchers: r,
          );
        }),
      );
    }
  }
}
