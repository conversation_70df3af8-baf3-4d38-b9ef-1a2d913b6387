// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_promotion_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPromotionListEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)
        initialize,
    required TResult Function(RepaymentVoucher selectedVoucher) selectVoucher,
    required TResult Function() fetchVouchers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult? Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult? Function()? fetchVouchers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult Function()? fetchVouchers,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_FetchVouchers value) fetchVouchers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_FetchVouchers value)? fetchVouchers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_FetchVouchers value)? fetchVouchers,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPromotionListEventCopyWith<$Res> {
  factory $RepaymentPromotionListEventCopyWith(
          RepaymentPromotionListEvent value,
          $Res Function(RepaymentPromotionListEvent) then) =
      _$RepaymentPromotionListEventCopyWithImpl<$Res,
          RepaymentPromotionListEvent>;
}

/// @nodoc
class _$RepaymentPromotionListEventCopyWithImpl<$Res,
        $Val extends RepaymentPromotionListEvent>
    implements $RepaymentPromotionListEventCopyWith<$Res> {
  _$RepaymentPromotionListEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {RepaymentContract? selectedContract,
      Decimal? selectedAmount,
      RepaymentUserPaymentMethod? selectedPaymentMethod,
      RepaymentBank? selectedBank,
      RepaymentVoucher? appliedVoucher});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPromotionListEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedContract = freezed,
    Object? selectedAmount = freezed,
    Object? selectedPaymentMethod = freezed,
    Object? selectedBank = freezed,
    Object? appliedVoucher = freezed,
  }) {
    return _then(_$_Initialize(
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedPaymentMethod: freezed == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      appliedVoucher: freezed == appliedVoucher
          ? _value.appliedVoucher
          : appliedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {this.selectedContract,
      this.selectedAmount,
      this.selectedPaymentMethod,
      this.selectedBank,
      this.appliedVoucher});

  @override
  final RepaymentContract? selectedContract;
  @override
  final Decimal? selectedAmount;
  @override
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  @override
  final RepaymentBank? selectedBank;
  @override
  final RepaymentVoucher? appliedVoucher;

  @override
  String toString() {
    return 'RepaymentPromotionListEvent.initialize(selectedContract: $selectedContract, selectedAmount: $selectedAmount, selectedPaymentMethod: $selectedPaymentMethod, selectedBank: $selectedBank, appliedVoucher: $appliedVoucher)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.selectedPaymentMethod, selectedPaymentMethod) ||
                other.selectedPaymentMethod == selectedPaymentMethod) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            (identical(other.appliedVoucher, appliedVoucher) ||
                other.appliedVoucher == appliedVoucher));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedContract, selectedAmount,
      selectedPaymentMethod, selectedBank, appliedVoucher);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)
        initialize,
    required TResult Function(RepaymentVoucher selectedVoucher) selectVoucher,
    required TResult Function() fetchVouchers,
  }) {
    return initialize(selectedContract, selectedAmount, selectedPaymentMethod,
        selectedBank, appliedVoucher);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult? Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult? Function()? fetchVouchers,
  }) {
    return initialize?.call(selectedContract, selectedAmount,
        selectedPaymentMethod, selectedBank, appliedVoucher);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult Function()? fetchVouchers,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(selectedContract, selectedAmount, selectedPaymentMethod,
          selectedBank, appliedVoucher);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_FetchVouchers value) fetchVouchers,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_FetchVouchers value)? fetchVouchers,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_FetchVouchers value)? fetchVouchers,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPromotionListEvent {
  const factory _Initialize(
      {final RepaymentContract? selectedContract,
      final Decimal? selectedAmount,
      final RepaymentUserPaymentMethod? selectedPaymentMethod,
      final RepaymentBank? selectedBank,
      final RepaymentVoucher? appliedVoucher}) = _$_Initialize;

  RepaymentContract? get selectedContract;
  Decimal? get selectedAmount;
  RepaymentUserPaymentMethod? get selectedPaymentMethod;
  RepaymentBank? get selectedBank;
  RepaymentVoucher? get appliedVoucher;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectVoucherCopyWith<$Res> {
  factory _$$_SelectVoucherCopyWith(
          _$_SelectVoucher value, $Res Function(_$_SelectVoucher) then) =
      __$$_SelectVoucherCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentVoucher selectedVoucher});
}

/// @nodoc
class __$$_SelectVoucherCopyWithImpl<$Res>
    extends _$RepaymentPromotionListEventCopyWithImpl<$Res, _$_SelectVoucher>
    implements _$$_SelectVoucherCopyWith<$Res> {
  __$$_SelectVoucherCopyWithImpl(
      _$_SelectVoucher _value, $Res Function(_$_SelectVoucher) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedVoucher = null,
  }) {
    return _then(_$_SelectVoucher(
      selectedVoucher: null == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher,
    ));
  }
}

/// @nodoc

class _$_SelectVoucher implements _SelectVoucher {
  const _$_SelectVoucher({required this.selectedVoucher});

  @override
  final RepaymentVoucher selectedVoucher;

  @override
  String toString() {
    return 'RepaymentPromotionListEvent.selectVoucher(selectedVoucher: $selectedVoucher)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectVoucher &&
            (identical(other.selectedVoucher, selectedVoucher) ||
                other.selectedVoucher == selectedVoucher));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedVoucher);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectVoucherCopyWith<_$_SelectVoucher> get copyWith =>
      __$$_SelectVoucherCopyWithImpl<_$_SelectVoucher>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)
        initialize,
    required TResult Function(RepaymentVoucher selectedVoucher) selectVoucher,
    required TResult Function() fetchVouchers,
  }) {
    return selectVoucher(selectedVoucher);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult? Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult? Function()? fetchVouchers,
  }) {
    return selectVoucher?.call(selectedVoucher);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult Function()? fetchVouchers,
    required TResult orElse(),
  }) {
    if (selectVoucher != null) {
      return selectVoucher(selectedVoucher);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_FetchVouchers value) fetchVouchers,
  }) {
    return selectVoucher(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_FetchVouchers value)? fetchVouchers,
  }) {
    return selectVoucher?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_FetchVouchers value)? fetchVouchers,
    required TResult orElse(),
  }) {
    if (selectVoucher != null) {
      return selectVoucher(this);
    }
    return orElse();
  }
}

abstract class _SelectVoucher implements RepaymentPromotionListEvent {
  const factory _SelectVoucher(
      {required final RepaymentVoucher selectedVoucher}) = _$_SelectVoucher;

  RepaymentVoucher get selectedVoucher;
  @JsonKey(ignore: true)
  _$$_SelectVoucherCopyWith<_$_SelectVoucher> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_FetchVouchersCopyWith<$Res> {
  factory _$$_FetchVouchersCopyWith(
          _$_FetchVouchers value, $Res Function(_$_FetchVouchers) then) =
      __$$_FetchVouchersCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_FetchVouchersCopyWithImpl<$Res>
    extends _$RepaymentPromotionListEventCopyWithImpl<$Res, _$_FetchVouchers>
    implements _$$_FetchVouchersCopyWith<$Res> {
  __$$_FetchVouchersCopyWithImpl(
      _$_FetchVouchers _value, $Res Function(_$_FetchVouchers) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_FetchVouchers implements _FetchVouchers {
  const _$_FetchVouchers();

  @override
  String toString() {
    return 'RepaymentPromotionListEvent.fetchVouchers()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_FetchVouchers);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)
        initialize,
    required TResult Function(RepaymentVoucher selectedVoucher) selectVoucher,
    required TResult Function() fetchVouchers,
  }) {
    return fetchVouchers();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult? Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult? Function()? fetchVouchers,
  }) {
    return fetchVouchers?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            RepaymentContract? selectedContract,
            Decimal? selectedAmount,
            RepaymentUserPaymentMethod? selectedPaymentMethod,
            RepaymentBank? selectedBank,
            RepaymentVoucher? appliedVoucher)?
        initialize,
    TResult Function(RepaymentVoucher selectedVoucher)? selectVoucher,
    TResult Function()? fetchVouchers,
    required TResult orElse(),
  }) {
    if (fetchVouchers != null) {
      return fetchVouchers();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectVoucher value) selectVoucher,
    required TResult Function(_FetchVouchers value) fetchVouchers,
  }) {
    return fetchVouchers(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectVoucher value)? selectVoucher,
    TResult? Function(_FetchVouchers value)? fetchVouchers,
  }) {
    return fetchVouchers?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectVoucher value)? selectVoucher,
    TResult Function(_FetchVouchers value)? fetchVouchers,
    required TResult orElse(),
  }) {
    if (fetchVouchers != null) {
      return fetchVouchers(this);
    }
    return orElse();
  }
}

abstract class _FetchVouchers implements RepaymentPromotionListEvent {
  const factory _FetchVouchers() = _$_FetchVouchers;
}

/// @nodoc
mixin _$RepaymentPromotionListState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  RepaymentVoucher? get selectedVoucher => throw _privateConstructorUsedError;
  RepaymentVoucher? get appliedVoucher => throw _privateConstructorUsedError;
  Decimal? get selectedAmount => throw _privateConstructorUsedError;
  RepaymentContract? get selectedContract => throw _privateConstructorUsedError;
  RepaymentUserPaymentMethod? get selectedPaymentMethod =>
      throw _privateConstructorUsedError;
  RepaymentBank? get selectedBank => throw _privateConstructorUsedError;
  List<RepaymentVoucher> get vouchers => throw _privateConstructorUsedError;
  bool get isValidInput => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<RepaymentVoucher>>>
      get failureOrSuccessFetchVouchers => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPromotionListStateCopyWith<RepaymentPromotionListState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPromotionListStateCopyWith<$Res> {
  factory $RepaymentPromotionListStateCopyWith(
          RepaymentPromotionListState value,
          $Res Function(RepaymentPromotionListState) then) =
      _$RepaymentPromotionListStateCopyWithImpl<$Res,
          RepaymentPromotionListState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      RepaymentVoucher? selectedVoucher,
      RepaymentVoucher? appliedVoucher,
      Decimal? selectedAmount,
      RepaymentContract? selectedContract,
      RepaymentUserPaymentMethod? selectedPaymentMethod,
      RepaymentBank? selectedBank,
      List<RepaymentVoucher> vouchers,
      bool isValidInput,
      Option<Either<RepaymentFailure, List<RepaymentVoucher>>>
          failureOrSuccessFetchVouchers});
}

/// @nodoc
class _$RepaymentPromotionListStateCopyWithImpl<$Res,
        $Val extends RepaymentPromotionListState>
    implements $RepaymentPromotionListStateCopyWith<$Res> {
  _$RepaymentPromotionListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedVoucher = freezed,
    Object? appliedVoucher = freezed,
    Object? selectedAmount = freezed,
    Object? selectedContract = freezed,
    Object? selectedPaymentMethod = freezed,
    Object? selectedBank = freezed,
    Object? vouchers = null,
    Object? isValidInput = null,
    Object? failureOrSuccessFetchVouchers = null,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      appliedVoucher: freezed == appliedVoucher
          ? _value.appliedVoucher
          : appliedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      selectedPaymentMethod: freezed == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      vouchers: null == vouchers
          ? _value.vouchers
          : vouchers // ignore: cast_nullable_to_non_nullable
              as List<RepaymentVoucher>,
      isValidInput: null == isValidInput
          ? _value.isValidInput
          : isValidInput // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessFetchVouchers: null == failureOrSuccessFetchVouchers
          ? _value.failureOrSuccessFetchVouchers
          : failureOrSuccessFetchVouchers // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentVoucher>>>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPromotionListStateCopyWith<$Res>
    implements $RepaymentPromotionListStateCopyWith<$Res> {
  factory _$$_RepaymentPromotionListStateCopyWith(
          _$_RepaymentPromotionListState value,
          $Res Function(_$_RepaymentPromotionListState) then) =
      __$$_RepaymentPromotionListStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool? isError,
      RepaymentVoucher? selectedVoucher,
      RepaymentVoucher? appliedVoucher,
      Decimal? selectedAmount,
      RepaymentContract? selectedContract,
      RepaymentUserPaymentMethod? selectedPaymentMethod,
      RepaymentBank? selectedBank,
      List<RepaymentVoucher> vouchers,
      bool isValidInput,
      Option<Either<RepaymentFailure, List<RepaymentVoucher>>>
          failureOrSuccessFetchVouchers});
}

/// @nodoc
class __$$_RepaymentPromotionListStateCopyWithImpl<$Res>
    extends _$RepaymentPromotionListStateCopyWithImpl<$Res,
        _$_RepaymentPromotionListState>
    implements _$$_RepaymentPromotionListStateCopyWith<$Res> {
  __$$_RepaymentPromotionListStateCopyWithImpl(
      _$_RepaymentPromotionListState _value,
      $Res Function(_$_RepaymentPromotionListState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = freezed,
    Object? selectedVoucher = freezed,
    Object? appliedVoucher = freezed,
    Object? selectedAmount = freezed,
    Object? selectedContract = freezed,
    Object? selectedPaymentMethod = freezed,
    Object? selectedBank = freezed,
    Object? vouchers = null,
    Object? isValidInput = null,
    Object? failureOrSuccessFetchVouchers = null,
  }) {
    return _then(_$_RepaymentPromotionListState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedVoucher: freezed == selectedVoucher
          ? _value.selectedVoucher
          : selectedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      appliedVoucher: freezed == appliedVoucher
          ? _value.appliedVoucher
          : appliedVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentVoucher?,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      selectedPaymentMethod: freezed == selectedPaymentMethod
          ? _value.selectedPaymentMethod
          : selectedPaymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      selectedBank: freezed == selectedBank
          ? _value.selectedBank
          : selectedBank // ignore: cast_nullable_to_non_nullable
              as RepaymentBank?,
      vouchers: null == vouchers
          ? _value._vouchers
          : vouchers // ignore: cast_nullable_to_non_nullable
              as List<RepaymentVoucher>,
      isValidInput: null == isValidInput
          ? _value.isValidInput
          : isValidInput // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessFetchVouchers: null == failureOrSuccessFetchVouchers
          ? _value.failureOrSuccessFetchVouchers
          : failureOrSuccessFetchVouchers // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentVoucher>>>,
    ));
  }
}

/// @nodoc

class _$_RepaymentPromotionListState implements _RepaymentPromotionListState {
  const _$_RepaymentPromotionListState(
      {required this.loadingState,
      this.isError,
      this.selectedVoucher,
      this.appliedVoucher,
      this.selectedAmount,
      required this.selectedContract,
      this.selectedPaymentMethod,
      this.selectedBank,
      final List<RepaymentVoucher> vouchers = const <RepaymentVoucher>[],
      this.isValidInput = false,
      required this.failureOrSuccessFetchVouchers})
      : _vouchers = vouchers;

  @override
  final LoadingState loadingState;
  @override
  final bool? isError;
  @override
  final RepaymentVoucher? selectedVoucher;
  @override
  final RepaymentVoucher? appliedVoucher;
  @override
  final Decimal? selectedAmount;
  @override
  final RepaymentContract? selectedContract;
  @override
  final RepaymentUserPaymentMethod? selectedPaymentMethod;
  @override
  final RepaymentBank? selectedBank;
  final List<RepaymentVoucher> _vouchers;
  @override
  @JsonKey()
  List<RepaymentVoucher> get vouchers {
    if (_vouchers is EqualUnmodifiableListView) return _vouchers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vouchers);
  }

  @override
  @JsonKey()
  final bool isValidInput;
  @override
  final Option<Either<RepaymentFailure, List<RepaymentVoucher>>>
      failureOrSuccessFetchVouchers;

  @override
  String toString() {
    return 'RepaymentPromotionListState(loadingState: $loadingState, isError: $isError, selectedVoucher: $selectedVoucher, appliedVoucher: $appliedVoucher, selectedAmount: $selectedAmount, selectedContract: $selectedContract, selectedPaymentMethod: $selectedPaymentMethod, selectedBank: $selectedBank, vouchers: $vouchers, isValidInput: $isValidInput, failureOrSuccessFetchVouchers: $failureOrSuccessFetchVouchers)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPromotionListState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.selectedVoucher, selectedVoucher) ||
                other.selectedVoucher == selectedVoucher) &&
            (identical(other.appliedVoucher, appliedVoucher) ||
                other.appliedVoucher == appliedVoucher) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            (identical(other.selectedPaymentMethod, selectedPaymentMethod) ||
                other.selectedPaymentMethod == selectedPaymentMethod) &&
            (identical(other.selectedBank, selectedBank) ||
                other.selectedBank == selectedBank) &&
            const DeepCollectionEquality().equals(other._vouchers, _vouchers) &&
            (identical(other.isValidInput, isValidInput) ||
                other.isValidInput == isValidInput) &&
            (identical(other.failureOrSuccessFetchVouchers,
                    failureOrSuccessFetchVouchers) ||
                other.failureOrSuccessFetchVouchers ==
                    failureOrSuccessFetchVouchers));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      isError,
      selectedVoucher,
      appliedVoucher,
      selectedAmount,
      selectedContract,
      selectedPaymentMethod,
      selectedBank,
      const DeepCollectionEquality().hash(_vouchers),
      isValidInput,
      failureOrSuccessFetchVouchers);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPromotionListStateCopyWith<_$_RepaymentPromotionListState>
      get copyWith => __$$_RepaymentPromotionListStateCopyWithImpl<
          _$_RepaymentPromotionListState>(this, _$identity);
}

abstract class _RepaymentPromotionListState
    implements RepaymentPromotionListState {
  const factory _RepaymentPromotionListState(
      {required final LoadingState loadingState,
      final bool? isError,
      final RepaymentVoucher? selectedVoucher,
      final RepaymentVoucher? appliedVoucher,
      final Decimal? selectedAmount,
      required final RepaymentContract? selectedContract,
      final RepaymentUserPaymentMethod? selectedPaymentMethod,
      final RepaymentBank? selectedBank,
      final List<RepaymentVoucher> vouchers,
      final bool isValidInput,
      required final Option<Either<RepaymentFailure, List<RepaymentVoucher>>>
          failureOrSuccessFetchVouchers}) = _$_RepaymentPromotionListState;

  @override
  LoadingState get loadingState;
  @override
  bool? get isError;
  @override
  RepaymentVoucher? get selectedVoucher;
  @override
  RepaymentVoucher? get appliedVoucher;
  @override
  Decimal? get selectedAmount;
  @override
  RepaymentContract? get selectedContract;
  @override
  RepaymentUserPaymentMethod? get selectedPaymentMethod;
  @override
  RepaymentBank? get selectedBank;
  @override
  List<RepaymentVoucher> get vouchers;
  @override
  bool get isValidInput;
  @override
  Option<Either<RepaymentFailure, List<RepaymentVoucher>>>
      get failureOrSuccessFetchVouchers;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPromotionListStateCopyWith<_$_RepaymentPromotionListState>
      get copyWith => throw _privateConstructorUsedError;
}
