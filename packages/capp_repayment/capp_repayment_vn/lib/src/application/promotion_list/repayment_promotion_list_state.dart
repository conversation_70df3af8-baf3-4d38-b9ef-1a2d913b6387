part of 'repayment_promotion_list_bloc.dart';

@freezed
class RepaymentPromotionListState with _$RepaymentPromotionListState {
  const factory RepaymentPromotionListState({
    required LoadingState loadingState,
    bool? isError,
    RepaymentVoucher? selectedVoucher,
    RepaymentVoucher? appliedVoucher,
    Decimal? selectedAmount,
    required RepaymentContract? selectedContract,
    RepaymentUserPaymentMethod? selectedPaymentMethod,
    RepaymentBank? selectedBank,
    @Default(<RepaymentVoucher>[]) List<RepaymentVoucher> vouchers,
    @Default(false) bool isValidInput,
    required Option<Either<RepaymentFailure, List<RepaymentVoucher>>> failureOrSuccessFetchVouchers,
  }) = _RepaymentPromotionListState;

  factory RepaymentPromotionListState.initialize() => RepaymentPromotionListState(
        loadingState: LoadingState.isInitial,
        isError: false,
        selectedContract: null,
        failureOrSuccessFetchVouchers: none(),
      );
}
