part of 'repayment_promotion_list_bloc.dart';

@freezed
class RepaymentPromotionListEvent with _$RepaymentPromotionListEvent {
  const factory RepaymentPromotionListEvent.initialize({
    RepaymentContract? selectedContract,
    Decimal? selectedAmount,
    RepaymentUserPaymentMethod? selectedPaymentMethod,
    RepaymentBank? selectedBank,
    RepaymentVoucher? appliedVoucher,
  }) = _Initialize;
  const factory RepaymentPromotionListEvent.selectVoucher({
    required RepaymentVoucher selectedVoucher,
  }) = _SelectVoucher;
  const factory RepaymentPromotionListEvent.fetchVouchers() = _FetchVouchers;
}
