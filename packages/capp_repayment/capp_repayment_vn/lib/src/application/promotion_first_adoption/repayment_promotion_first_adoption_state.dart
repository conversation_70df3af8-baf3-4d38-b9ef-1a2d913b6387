part of 'repayment_promotion_first_adoption_bloc.dart';

@freezed
class RepaymentPromotionFirstAdoptionState with _$RepaymentPromotionFirstAdoptionState {
  const factory RepaymentPromotionFirstAdoptionState({
    @Default(false) bool isLoading,
    @Default(false) bool isLoaded,
    RepaymentPromotionVoucher? repaymentPromotionVoucher,
    num? amount,
  }) = _RepaymentPromotionFirstAdoptionState;
  factory RepaymentPromotionFirstAdoptionState.initialize() => const RepaymentPromotionFirstAdoptionState();
}
