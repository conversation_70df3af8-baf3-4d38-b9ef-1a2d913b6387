import 'package:decimal/decimal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';

import '../../../../capp_repayment.dart';

part 'repayment_promotion_first_adoption_bloc.freezed.dart';
part 'repayment_promotion_first_adoption_event.dart';
part 'repayment_promotion_first_adoption_state.dart';

class RepaymentPromotionFirstAdoptionBloc
    extends Bloc<RepaymentPromotionFirstAdoptionEvent, RepaymentPromotionFirstAdoptionState> {
  final IPromotionRepository promotionRepository;
  final IUserRepository userRepository;
  final RepaymentStorage storage;

  RepaymentPromotionFirstAdoptionBloc({
    required this.promotionRepository,
    required this.userRepository,
    required this.storage,
  }) : super(RepaymentPromotionFirstAdoptionState.initialize()) {
    on<_Initialize>(_initialize);
    on<_FetchPromoFirstAdoption>(_fetchPromoFirstAdoption);
  }

  Future<void> _initialize(_Initialize e, Emitter<RepaymentPromotionFirstAdoptionState> emit) async {}

  Future<void> _fetchPromoFirstAdoption(
    _FetchPromoFirstAdoption e,
    Emitter<RepaymentPromotionFirstAdoptionState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, isLoaded: false));
    final request = RepaymentVoucherRequest(
      contractNumber: e.contractNumber ?? '',
      selectedAmount: Decimal.fromInt(e.amount?.toInt() ?? 0),
    );
    final response = await promotionRepository.fetchFirstAdoptionVoucher(request);
    emit(
      await response.fold((l) {
        return state.copyWith(
          isLoading: false,
          isLoaded: true,
          amount: null,
          repaymentPromotionVoucher: null,
        );
      }, (r) async {
        final userId = await userRepository.userCuid();
        var isDisplay = r.firstRepaymentAdoption?.isDisplay ?? false;
        final displayCountS = r.firstRepaymentAdoption?.displayCount ?? 0;

        if (!isDisplay) {
          return state.copyWith(
            isLoading: false,
            isLoaded: true,
            amount: e.amount,
            repaymentPromotionVoucher: r,
          );
        } else {
          if (userId != null && userId.isNotEmpty) {
            final displayCountL = await storage.getPromotionFirstAdoptionDisplayCount(userId);
            if (displayCountL == null) {
              await storage.setPromotionFirstAdoptionDisplayCount(userId, 1);
              isDisplay = true;
            } else if (displayCountL < displayCountS) {
              await storage.setPromotionFirstAdoptionDisplayCount(userId, displayCountL + 1);
              isDisplay = true;
            } else {
              isDisplay = false;
            }
          }
          final r2 = RepaymentPromotionVoucher(
            availableVouchers: r.availableVouchers,
            firstRepaymentAdoption: RepaymentPromotionFirstAdoption(
              isDisplay: isDisplay,
              displayCount: r.firstRepaymentAdoption?.displayCount,
              isReset: r.firstRepaymentAdoption?.isReset,
            ),
          );
          return state.copyWith(
            isLoading: false,
            isLoaded: true,
            amount: e.amount,
            repaymentPromotionVoucher: r2,
          );
        }
      }),
    );
  }
}
