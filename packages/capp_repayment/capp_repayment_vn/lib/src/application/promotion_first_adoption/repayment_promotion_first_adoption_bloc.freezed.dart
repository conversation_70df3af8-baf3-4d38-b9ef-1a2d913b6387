// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_promotion_first_adoption_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentPromotionFirstAdoptionEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String? contractNumber, num? amount)
        fetchPromoFirstAdoption,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String? contractNumber, num? amount)?
        fetchPromoFirstAdoption,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String? contractNumber, num? amount)?
        fetchPromoFirstAdoption,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPromoFirstAdoption value)
        fetchPromoFirstAdoption,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPromoFirstAdoption value)? fetchPromoFirstAdoption,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPromoFirstAdoption value)? fetchPromoFirstAdoption,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPromotionFirstAdoptionEventCopyWith<$Res> {
  factory $RepaymentPromotionFirstAdoptionEventCopyWith(
          RepaymentPromotionFirstAdoptionEvent value,
          $Res Function(RepaymentPromotionFirstAdoptionEvent) then) =
      _$RepaymentPromotionFirstAdoptionEventCopyWithImpl<$Res,
          RepaymentPromotionFirstAdoptionEvent>;
}

/// @nodoc
class _$RepaymentPromotionFirstAdoptionEventCopyWithImpl<$Res,
        $Val extends RepaymentPromotionFirstAdoptionEvent>
    implements $RepaymentPromotionFirstAdoptionEventCopyWith<$Res> {
  _$RepaymentPromotionFirstAdoptionEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentPromotionFirstAdoptionEventCopyWithImpl<$Res,
        _$_Initialize> implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize();

  @override
  String toString() {
    return 'RepaymentPromotionFirstAdoptionEvent.initialize()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Initialize);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String? contractNumber, num? amount)
        fetchPromoFirstAdoption,
  }) {
    return initialize();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String? contractNumber, num? amount)?
        fetchPromoFirstAdoption,
  }) {
    return initialize?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String? contractNumber, num? amount)?
        fetchPromoFirstAdoption,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPromoFirstAdoption value)
        fetchPromoFirstAdoption,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPromoFirstAdoption value)? fetchPromoFirstAdoption,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPromoFirstAdoption value)? fetchPromoFirstAdoption,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentPromotionFirstAdoptionEvent {
  const factory _Initialize() = _$_Initialize;
}

/// @nodoc
abstract class _$$_FetchPromoFirstAdoptionCopyWith<$Res> {
  factory _$$_FetchPromoFirstAdoptionCopyWith(_$_FetchPromoFirstAdoption value,
          $Res Function(_$_FetchPromoFirstAdoption) then) =
      __$$_FetchPromoFirstAdoptionCopyWithImpl<$Res>;
  @useResult
  $Res call({String? contractNumber, num? amount});
}

/// @nodoc
class __$$_FetchPromoFirstAdoptionCopyWithImpl<$Res>
    extends _$RepaymentPromotionFirstAdoptionEventCopyWithImpl<$Res,
        _$_FetchPromoFirstAdoption>
    implements _$$_FetchPromoFirstAdoptionCopyWith<$Res> {
  __$$_FetchPromoFirstAdoptionCopyWithImpl(_$_FetchPromoFirstAdoption _value,
      $Res Function(_$_FetchPromoFirstAdoption) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contractNumber = freezed,
    Object? amount = freezed,
  }) {
    return _then(_$_FetchPromoFirstAdoption(
      contractNumber: freezed == contractNumber
          ? _value.contractNumber
          : contractNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as num?,
    ));
  }
}

/// @nodoc

class _$_FetchPromoFirstAdoption implements _FetchPromoFirstAdoption {
  const _$_FetchPromoFirstAdoption({this.contractNumber, this.amount});

  @override
  final String? contractNumber;
  @override
  final num? amount;

  @override
  String toString() {
    return 'RepaymentPromotionFirstAdoptionEvent.fetchPromoFirstAdoption(contractNumber: $contractNumber, amount: $amount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FetchPromoFirstAdoption &&
            (identical(other.contractNumber, contractNumber) ||
                other.contractNumber == contractNumber) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contractNumber, amount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FetchPromoFirstAdoptionCopyWith<_$_FetchPromoFirstAdoption>
      get copyWith =>
          __$$_FetchPromoFirstAdoptionCopyWithImpl<_$_FetchPromoFirstAdoption>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function(String? contractNumber, num? amount)
        fetchPromoFirstAdoption,
  }) {
    return fetchPromoFirstAdoption(contractNumber, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function(String? contractNumber, num? amount)?
        fetchPromoFirstAdoption,
  }) {
    return fetchPromoFirstAdoption?.call(contractNumber, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function(String? contractNumber, num? amount)?
        fetchPromoFirstAdoption,
    required TResult orElse(),
  }) {
    if (fetchPromoFirstAdoption != null) {
      return fetchPromoFirstAdoption(contractNumber, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchPromoFirstAdoption value)
        fetchPromoFirstAdoption,
  }) {
    return fetchPromoFirstAdoption(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchPromoFirstAdoption value)? fetchPromoFirstAdoption,
  }) {
    return fetchPromoFirstAdoption?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchPromoFirstAdoption value)? fetchPromoFirstAdoption,
    required TResult orElse(),
  }) {
    if (fetchPromoFirstAdoption != null) {
      return fetchPromoFirstAdoption(this);
    }
    return orElse();
  }
}

abstract class _FetchPromoFirstAdoption
    implements RepaymentPromotionFirstAdoptionEvent {
  const factory _FetchPromoFirstAdoption(
      {final String? contractNumber,
      final num? amount}) = _$_FetchPromoFirstAdoption;

  String? get contractNumber;
  num? get amount;
  @JsonKey(ignore: true)
  _$$_FetchPromoFirstAdoptionCopyWith<_$_FetchPromoFirstAdoption>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentPromotionFirstAdoptionState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isLoaded => throw _privateConstructorUsedError;
  RepaymentPromotionVoucher? get repaymentPromotionVoucher =>
      throw _privateConstructorUsedError;
  num? get amount => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentPromotionFirstAdoptionStateCopyWith<
          RepaymentPromotionFirstAdoptionState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentPromotionFirstAdoptionStateCopyWith<$Res> {
  factory $RepaymentPromotionFirstAdoptionStateCopyWith(
          RepaymentPromotionFirstAdoptionState value,
          $Res Function(RepaymentPromotionFirstAdoptionState) then) =
      _$RepaymentPromotionFirstAdoptionStateCopyWithImpl<$Res,
          RepaymentPromotionFirstAdoptionState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isLoaded,
      RepaymentPromotionVoucher? repaymentPromotionVoucher,
      num? amount});
}

/// @nodoc
class _$RepaymentPromotionFirstAdoptionStateCopyWithImpl<$Res,
        $Val extends RepaymentPromotionFirstAdoptionState>
    implements $RepaymentPromotionFirstAdoptionStateCopyWith<$Res> {
  _$RepaymentPromotionFirstAdoptionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoaded = null,
    Object? repaymentPromotionVoucher = freezed,
    Object? amount = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoaded: null == isLoaded
          ? _value.isLoaded
          : isLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      repaymentPromotionVoucher: freezed == repaymentPromotionVoucher
          ? _value.repaymentPromotionVoucher
          : repaymentPromotionVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentPromotionVoucher?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as num?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentPromotionFirstAdoptionStateCopyWith<$Res>
    implements $RepaymentPromotionFirstAdoptionStateCopyWith<$Res> {
  factory _$$_RepaymentPromotionFirstAdoptionStateCopyWith(
          _$_RepaymentPromotionFirstAdoptionState value,
          $Res Function(_$_RepaymentPromotionFirstAdoptionState) then) =
      __$$_RepaymentPromotionFirstAdoptionStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isLoaded,
      RepaymentPromotionVoucher? repaymentPromotionVoucher,
      num? amount});
}

/// @nodoc
class __$$_RepaymentPromotionFirstAdoptionStateCopyWithImpl<$Res>
    extends _$RepaymentPromotionFirstAdoptionStateCopyWithImpl<$Res,
        _$_RepaymentPromotionFirstAdoptionState>
    implements _$$_RepaymentPromotionFirstAdoptionStateCopyWith<$Res> {
  __$$_RepaymentPromotionFirstAdoptionStateCopyWithImpl(
      _$_RepaymentPromotionFirstAdoptionState _value,
      $Res Function(_$_RepaymentPromotionFirstAdoptionState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoaded = null,
    Object? repaymentPromotionVoucher = freezed,
    Object? amount = freezed,
  }) {
    return _then(_$_RepaymentPromotionFirstAdoptionState(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoaded: null == isLoaded
          ? _value.isLoaded
          : isLoaded // ignore: cast_nullable_to_non_nullable
              as bool,
      repaymentPromotionVoucher: freezed == repaymentPromotionVoucher
          ? _value.repaymentPromotionVoucher
          : repaymentPromotionVoucher // ignore: cast_nullable_to_non_nullable
              as RepaymentPromotionVoucher?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as num?,
    ));
  }
}

/// @nodoc

class _$_RepaymentPromotionFirstAdoptionState
    implements _RepaymentPromotionFirstAdoptionState {
  const _$_RepaymentPromotionFirstAdoptionState(
      {this.isLoading = false,
      this.isLoaded = false,
      this.repaymentPromotionVoucher,
      this.amount});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isLoaded;
  @override
  final RepaymentPromotionVoucher? repaymentPromotionVoucher;
  @override
  final num? amount;

  @override
  String toString() {
    return 'RepaymentPromotionFirstAdoptionState(isLoading: $isLoading, isLoaded: $isLoaded, repaymentPromotionVoucher: $repaymentPromotionVoucher, amount: $amount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentPromotionFirstAdoptionState &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoaded, isLoaded) ||
                other.isLoaded == isLoaded) &&
            (identical(other.repaymentPromotionVoucher,
                    repaymentPromotionVoucher) ||
                other.repaymentPromotionVoucher == repaymentPromotionVoucher) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, isLoading, isLoaded, repaymentPromotionVoucher, amount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentPromotionFirstAdoptionStateCopyWith<
          _$_RepaymentPromotionFirstAdoptionState>
      get copyWith => __$$_RepaymentPromotionFirstAdoptionStateCopyWithImpl<
          _$_RepaymentPromotionFirstAdoptionState>(this, _$identity);
}

abstract class _RepaymentPromotionFirstAdoptionState
    implements RepaymentPromotionFirstAdoptionState {
  const factory _RepaymentPromotionFirstAdoptionState(
      {final bool isLoading,
      final bool isLoaded,
      final RepaymentPromotionVoucher? repaymentPromotionVoucher,
      final num? amount}) = _$_RepaymentPromotionFirstAdoptionState;

  @override
  bool get isLoading;
  @override
  bool get isLoaded;
  @override
  RepaymentPromotionVoucher? get repaymentPromotionVoucher;
  @override
  num? get amount;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentPromotionFirstAdoptionStateCopyWith<
          _$_RepaymentPromotionFirstAdoptionState>
      get copyWith => throw _privateConstructorUsedError;
}
