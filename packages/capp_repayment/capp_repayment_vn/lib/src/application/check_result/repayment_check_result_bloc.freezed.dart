// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_check_result_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentCheckResultEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentUserPaymentMethod paymentMethod,
            String uuid, String previousRouteName, String phoneNumber)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
    required TResult Function(RepaymentTransactionStatus status)
        updateTransactionStatus,
    required TResult Function() endTimer,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
    TResult? Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult? Function()? endTimer,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    TResult Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult Function()? endTimer,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_UpdateTransactionStatus value)
        updateTransactionStatus,
    required TResult Function(_EndTimer value) endTimer,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult? Function(_EndTimer value)? endTimer,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult Function(_EndTimer value)? endTimer,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentCheckResultEventCopyWith<$Res> {
  factory $RepaymentCheckResultEventCopyWith(RepaymentCheckResultEvent value,
          $Res Function(RepaymentCheckResultEvent) then) =
      _$RepaymentCheckResultEventCopyWithImpl<$Res, RepaymentCheckResultEvent>;
}

/// @nodoc
class _$RepaymentCheckResultEventCopyWithImpl<$Res,
        $Val extends RepaymentCheckResultEvent>
    implements $RepaymentCheckResultEventCopyWith<$Res> {
  _$RepaymentCheckResultEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {RepaymentUserPaymentMethod paymentMethod,
      String uuid,
      String previousRouteName,
      String phoneNumber});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentCheckResultEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentMethod = null,
    Object? uuid = null,
    Object? previousRouteName = null,
    Object? phoneNumber = null,
  }) {
    return _then(_$_Initialize(
      null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod,
      null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      null == previousRouteName
          ? _value.previousRouteName
          : previousRouteName // ignore: cast_nullable_to_non_nullable
              as String,
      null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      this.paymentMethod, this.uuid, this.previousRouteName, this.phoneNumber);

  @override
  final RepaymentUserPaymentMethod paymentMethod;
  @override
  final String uuid;
  @override
  final String previousRouteName;
  @override
  final String phoneNumber;

  @override
  String toString() {
    return 'RepaymentCheckResultEvent.initialize(paymentMethod: $paymentMethod, uuid: $uuid, previousRouteName: $previousRouteName, phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.uuid, uuid) || other.uuid == uuid) &&
            (identical(other.previousRouteName, previousRouteName) ||
                other.previousRouteName == previousRouteName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, paymentMethod, uuid, previousRouteName, phoneNumber);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentUserPaymentMethod paymentMethod,
            String uuid, String previousRouteName, String phoneNumber)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
    required TResult Function(RepaymentTransactionStatus status)
        updateTransactionStatus,
    required TResult Function() endTimer,
  }) {
    return initialize(paymentMethod, uuid, previousRouteName, phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
    TResult? Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult? Function()? endTimer,
  }) {
    return initialize?.call(
        paymentMethod, uuid, previousRouteName, phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    TResult Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult Function()? endTimer,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(paymentMethod, uuid, previousRouteName, phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_UpdateTransactionStatus value)
        updateTransactionStatus,
    required TResult Function(_EndTimer value) endTimer,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult? Function(_EndTimer value)? endTimer,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult Function(_EndTimer value)? endTimer,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentCheckResultEvent {
  const factory _Initialize(
      final RepaymentUserPaymentMethod paymentMethod,
      final String uuid,
      final String previousRouteName,
      final String phoneNumber) = _$_Initialize;

  RepaymentUserPaymentMethod get paymentMethod;
  String get uuid;
  String get previousRouteName;
  String get phoneNumber;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_CheckTransactionResultCopyWith<$Res> {
  factory _$$_CheckTransactionResultCopyWith(_$_CheckTransactionResult value,
          $Res Function(_$_CheckTransactionResult) then) =
      __$$_CheckTransactionResultCopyWithImpl<$Res>;
  @useResult
  $Res call({String transactionId});
}

/// @nodoc
class __$$_CheckTransactionResultCopyWithImpl<$Res>
    extends _$RepaymentCheckResultEventCopyWithImpl<$Res,
        _$_CheckTransactionResult>
    implements _$$_CheckTransactionResultCopyWith<$Res> {
  __$$_CheckTransactionResultCopyWithImpl(_$_CheckTransactionResult _value,
      $Res Function(_$_CheckTransactionResult) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactionId = null,
  }) {
    return _then(_$_CheckTransactionResult(
      null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_CheckTransactionResult implements _CheckTransactionResult {
  const _$_CheckTransactionResult(this.transactionId);

  @override
  final String transactionId;

  @override
  String toString() {
    return 'RepaymentCheckResultEvent.checkTransactionResult(transactionId: $transactionId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CheckTransactionResult &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, transactionId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CheckTransactionResultCopyWith<_$_CheckTransactionResult> get copyWith =>
      __$$_CheckTransactionResultCopyWithImpl<_$_CheckTransactionResult>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentUserPaymentMethod paymentMethod,
            String uuid, String previousRouteName, String phoneNumber)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
    required TResult Function(RepaymentTransactionStatus status)
        updateTransactionStatus,
    required TResult Function() endTimer,
  }) {
    return checkTransactionResult(transactionId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
    TResult? Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult? Function()? endTimer,
  }) {
    return checkTransactionResult?.call(transactionId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    TResult Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult Function()? endTimer,
    required TResult orElse(),
  }) {
    if (checkTransactionResult != null) {
      return checkTransactionResult(transactionId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_UpdateTransactionStatus value)
        updateTransactionStatus,
    required TResult Function(_EndTimer value) endTimer,
  }) {
    return checkTransactionResult(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult? Function(_EndTimer value)? endTimer,
  }) {
    return checkTransactionResult?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult Function(_EndTimer value)? endTimer,
    required TResult orElse(),
  }) {
    if (checkTransactionResult != null) {
      return checkTransactionResult(this);
    }
    return orElse();
  }
}

abstract class _CheckTransactionResult implements RepaymentCheckResultEvent {
  const factory _CheckTransactionResult(final String transactionId) =
      _$_CheckTransactionResult;

  String get transactionId;
  @JsonKey(ignore: true)
  _$$_CheckTransactionResultCopyWith<_$_CheckTransactionResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_UpdateTransactionStatusCopyWith<$Res> {
  factory _$$_UpdateTransactionStatusCopyWith(_$_UpdateTransactionStatus value,
          $Res Function(_$_UpdateTransactionStatus) then) =
      __$$_UpdateTransactionStatusCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentTransactionStatus status});
}

/// @nodoc
class __$$_UpdateTransactionStatusCopyWithImpl<$Res>
    extends _$RepaymentCheckResultEventCopyWithImpl<$Res,
        _$_UpdateTransactionStatus>
    implements _$$_UpdateTransactionStatusCopyWith<$Res> {
  __$$_UpdateTransactionStatusCopyWithImpl(_$_UpdateTransactionStatus _value,
      $Res Function(_$_UpdateTransactionStatus) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$_UpdateTransactionStatus(
      null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RepaymentTransactionStatus,
    ));
  }
}

/// @nodoc

class _$_UpdateTransactionStatus implements _UpdateTransactionStatus {
  const _$_UpdateTransactionStatus(this.status);

  @override
  final RepaymentTransactionStatus status;

  @override
  String toString() {
    return 'RepaymentCheckResultEvent.updateTransactionStatus(status: $status)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UpdateTransactionStatus &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UpdateTransactionStatusCopyWith<_$_UpdateTransactionStatus>
      get copyWith =>
          __$$_UpdateTransactionStatusCopyWithImpl<_$_UpdateTransactionStatus>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentUserPaymentMethod paymentMethod,
            String uuid, String previousRouteName, String phoneNumber)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
    required TResult Function(RepaymentTransactionStatus status)
        updateTransactionStatus,
    required TResult Function() endTimer,
  }) {
    return updateTransactionStatus(status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
    TResult? Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult? Function()? endTimer,
  }) {
    return updateTransactionStatus?.call(status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    TResult Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult Function()? endTimer,
    required TResult orElse(),
  }) {
    if (updateTransactionStatus != null) {
      return updateTransactionStatus(status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_UpdateTransactionStatus value)
        updateTransactionStatus,
    required TResult Function(_EndTimer value) endTimer,
  }) {
    return updateTransactionStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult? Function(_EndTimer value)? endTimer,
  }) {
    return updateTransactionStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult Function(_EndTimer value)? endTimer,
    required TResult orElse(),
  }) {
    if (updateTransactionStatus != null) {
      return updateTransactionStatus(this);
    }
    return orElse();
  }
}

abstract class _UpdateTransactionStatus implements RepaymentCheckResultEvent {
  const factory _UpdateTransactionStatus(
      final RepaymentTransactionStatus status) = _$_UpdateTransactionStatus;

  RepaymentTransactionStatus get status;
  @JsonKey(ignore: true)
  _$$_UpdateTransactionStatusCopyWith<_$_UpdateTransactionStatus>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_EndTimerCopyWith<$Res> {
  factory _$$_EndTimerCopyWith(
          _$_EndTimer value, $Res Function(_$_EndTimer) then) =
      __$$_EndTimerCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_EndTimerCopyWithImpl<$Res>
    extends _$RepaymentCheckResultEventCopyWithImpl<$Res, _$_EndTimer>
    implements _$$_EndTimerCopyWith<$Res> {
  __$$_EndTimerCopyWithImpl(
      _$_EndTimer _value, $Res Function(_$_EndTimer) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_EndTimer implements _EndTimer {
  const _$_EndTimer();

  @override
  String toString() {
    return 'RepaymentCheckResultEvent.endTimer()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_EndTimer);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentUserPaymentMethod paymentMethod,
            String uuid, String previousRouteName, String phoneNumber)
        initialize,
    required TResult Function(String transactionId) checkTransactionResult,
    required TResult Function(RepaymentTransactionStatus status)
        updateTransactionStatus,
    required TResult Function() endTimer,
  }) {
    return endTimer();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult? Function(String transactionId)? checkTransactionResult,
    TResult? Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult? Function()? endTimer,
  }) {
    return endTimer?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentUserPaymentMethod paymentMethod, String uuid,
            String previousRouteName, String phoneNumber)?
        initialize,
    TResult Function(String transactionId)? checkTransactionResult,
    TResult Function(RepaymentTransactionStatus status)?
        updateTransactionStatus,
    TResult Function()? endTimer,
    required TResult orElse(),
  }) {
    if (endTimer != null) {
      return endTimer();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_CheckTransactionResult value)
        checkTransactionResult,
    required TResult Function(_UpdateTransactionStatus value)
        updateTransactionStatus,
    required TResult Function(_EndTimer value) endTimer,
  }) {
    return endTimer(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult? Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult? Function(_EndTimer value)? endTimer,
  }) {
    return endTimer?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_CheckTransactionResult value)? checkTransactionResult,
    TResult Function(_UpdateTransactionStatus value)? updateTransactionStatus,
    TResult Function(_EndTimer value)? endTimer,
    required TResult orElse(),
  }) {
    if (endTimer != null) {
      return endTimer(this);
    }
    return orElse();
  }
}

abstract class _EndTimer implements RepaymentCheckResultEvent {
  const factory _EndTimer() = _$_EndTimer;
}

/// @nodoc
mixin _$RepaymentCheckResultState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  String? get previousRouteName => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
      get failureOrSuccess => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, RepaymentTransaction>>
      get failureOrSuccessTransactionResult =>
          throw _privateConstructorUsedError;
  RepaymentUserPaymentMethod? get paymentMethod =>
      throw _privateConstructorUsedError;
  int? get checkResultTimeCounter => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  RepaymentTransactionStatus get status => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentCheckResultStateCopyWith<RepaymentCheckResultState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentCheckResultStateCopyWith<$Res> {
  factory $RepaymentCheckResultStateCopyWith(RepaymentCheckResultState value,
          $Res Function(RepaymentCheckResultState) then) =
      _$RepaymentCheckResultStateCopyWithImpl<$Res, RepaymentCheckResultState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      String? previousRouteName,
      String? phoneNumber,
      Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
          failureOrSuccess,
      Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      RepaymentUserPaymentMethod? paymentMethod,
      int? checkResultTimeCounter,
      String? title,
      String? description,
      RepaymentTransactionStatus status});
}

/// @nodoc
class _$RepaymentCheckResultStateCopyWithImpl<$Res,
        $Val extends RepaymentCheckResultState>
    implements $RepaymentCheckResultStateCopyWith<$Res> {
  _$RepaymentCheckResultStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? previousRouteName = freezed,
    Object? phoneNumber = freezed,
    Object? failureOrSuccess = null,
    Object? failureOrSuccessTransactionResult = null,
    Object? paymentMethod = freezed,
    Object? checkResultTimeCounter = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      previousRouteName: freezed == previousRouteName
          ? _value.previousRouteName
          : previousRouteName // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<String, RepaymentTransactionSuccessRouteArgs>>,
      failureOrSuccessTransactionResult: null ==
              failureOrSuccessTransactionResult
          ? _value.failureOrSuccessTransactionResult
          : failureOrSuccessTransactionResult // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentTransaction>>,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      checkResultTimeCounter: freezed == checkResultTimeCounter
          ? _value.checkResultTimeCounter
          : checkResultTimeCounter // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RepaymentTransactionStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentCheckResultStateCopyWith<$Res>
    implements $RepaymentCheckResultStateCopyWith<$Res> {
  factory _$$_RepaymentCheckResultStateCopyWith(
          _$_RepaymentCheckResultState value,
          $Res Function(_$_RepaymentCheckResultState) then) =
      __$$_RepaymentCheckResultStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      String? previousRouteName,
      String? phoneNumber,
      Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
          failureOrSuccess,
      Option<Either<RepaymentFailure, RepaymentTransaction>>
          failureOrSuccessTransactionResult,
      RepaymentUserPaymentMethod? paymentMethod,
      int? checkResultTimeCounter,
      String? title,
      String? description,
      RepaymentTransactionStatus status});
}

/// @nodoc
class __$$_RepaymentCheckResultStateCopyWithImpl<$Res>
    extends _$RepaymentCheckResultStateCopyWithImpl<$Res,
        _$_RepaymentCheckResultState>
    implements _$$_RepaymentCheckResultStateCopyWith<$Res> {
  __$$_RepaymentCheckResultStateCopyWithImpl(
      _$_RepaymentCheckResultState _value,
      $Res Function(_$_RepaymentCheckResultState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? previousRouteName = freezed,
    Object? phoneNumber = freezed,
    Object? failureOrSuccess = null,
    Object? failureOrSuccessTransactionResult = null,
    Object? paymentMethod = freezed,
    Object? checkResultTimeCounter = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? status = null,
  }) {
    return _then(_$_RepaymentCheckResultState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      previousRouteName: freezed == previousRouteName
          ? _value.previousRouteName
          : previousRouteName // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<String, RepaymentTransactionSuccessRouteArgs>>,
      failureOrSuccessTransactionResult: null ==
              failureOrSuccessTransactionResult
          ? _value.failureOrSuccessTransactionResult
          : failureOrSuccessTransactionResult // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, RepaymentTransaction>>,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as RepaymentUserPaymentMethod?,
      checkResultTimeCounter: freezed == checkResultTimeCounter
          ? _value.checkResultTimeCounter
          : checkResultTimeCounter // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RepaymentTransactionStatus,
    ));
  }
}

/// @nodoc

class _$_RepaymentCheckResultState implements _RepaymentCheckResultState {
  const _$_RepaymentCheckResultState(
      {required this.loadingState,
      this.previousRouteName,
      this.phoneNumber,
      required this.failureOrSuccess,
      required this.failureOrSuccessTransactionResult,
      this.paymentMethod,
      this.checkResultTimeCounter,
      this.title,
      this.description,
      required this.status});

  @override
  final LoadingState loadingState;
  @override
  final String? previousRouteName;
  @override
  final String? phoneNumber;
  @override
  final Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
      failureOrSuccess;
  @override
  final Option<Either<RepaymentFailure, RepaymentTransaction>>
      failureOrSuccessTransactionResult;
  @override
  final RepaymentUserPaymentMethod? paymentMethod;
  @override
  final int? checkResultTimeCounter;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final RepaymentTransactionStatus status;

  @override
  String toString() {
    return 'RepaymentCheckResultState(loadingState: $loadingState, previousRouteName: $previousRouteName, phoneNumber: $phoneNumber, failureOrSuccess: $failureOrSuccess, failureOrSuccessTransactionResult: $failureOrSuccessTransactionResult, paymentMethod: $paymentMethod, checkResultTimeCounter: $checkResultTimeCounter, title: $title, description: $description, status: $status)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentCheckResultState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.previousRouteName, previousRouteName) ||
                other.previousRouteName == previousRouteName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.failureOrSuccess, failureOrSuccess) ||
                other.failureOrSuccess == failureOrSuccess) &&
            (identical(other.failureOrSuccessTransactionResult,
                    failureOrSuccessTransactionResult) ||
                other.failureOrSuccessTransactionResult ==
                    failureOrSuccessTransactionResult) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.checkResultTimeCounter, checkResultTimeCounter) ||
                other.checkResultTimeCounter == checkResultTimeCounter) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      previousRouteName,
      phoneNumber,
      failureOrSuccess,
      failureOrSuccessTransactionResult,
      paymentMethod,
      checkResultTimeCounter,
      title,
      description,
      status);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentCheckResultStateCopyWith<_$_RepaymentCheckResultState>
      get copyWith => __$$_RepaymentCheckResultStateCopyWithImpl<
          _$_RepaymentCheckResultState>(this, _$identity);
}

abstract class _RepaymentCheckResultState implements RepaymentCheckResultState {
  const factory _RepaymentCheckResultState(
          {required final LoadingState loadingState,
          final String? previousRouteName,
          final String? phoneNumber,
          required final Option<
                  Either<String, RepaymentTransactionSuccessRouteArgs>>
              failureOrSuccess,
          required final Option<Either<RepaymentFailure, RepaymentTransaction>>
              failureOrSuccessTransactionResult,
          final RepaymentUserPaymentMethod? paymentMethod,
          final int? checkResultTimeCounter,
          final String? title,
          final String? description,
          required final RepaymentTransactionStatus status}) =
      _$_RepaymentCheckResultState;

  @override
  LoadingState get loadingState;
  @override
  String? get previousRouteName;
  @override
  String? get phoneNumber;
  @override
  Option<Either<String, RepaymentTransactionSuccessRouteArgs>>
      get failureOrSuccess;
  @override
  Option<Either<RepaymentFailure, RepaymentTransaction>>
      get failureOrSuccessTransactionResult;
  @override
  RepaymentUserPaymentMethod? get paymentMethod;
  @override
  int? get checkResultTimeCounter;
  @override
  String? get title;
  @override
  String? get description;
  @override
  RepaymentTransactionStatus get status;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentCheckResultStateCopyWith<_$_RepaymentCheckResultState>
      get copyWith => throw _privateConstructorUsedError;
}
