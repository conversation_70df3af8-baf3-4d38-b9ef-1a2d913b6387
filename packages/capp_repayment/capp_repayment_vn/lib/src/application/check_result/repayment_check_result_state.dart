part of 'repayment_check_result_bloc.dart';

@freezed
class RepaymentCheckResultState with _$RepaymentCheckResultState {
  const factory RepaymentCheckResultState({
    required LoadingState loadingState,
    String? previousRouteName,
    String? phoneNumber,
    required Option<Either<String, RepaymentTransactionSuccessRouteArgs>> failureOrSuccess,
    required Option<Either<RepaymentFailure, RepaymentTransaction>> failureOrSuccessTransactionResult,
    RepaymentUserPaymentMethod? paymentMethod,
    int? checkResultTimeCounter,
    String? title,
    String? description,
    required RepaymentTransactionStatus status,
  }) = _RepaymentCheckResultState;

  factory RepaymentCheckResultState.initialize() => RepaymentCheckResultState(
        loadingState: LoadingState.isInitial,
        failureOrSuccess: none(),
        failureOrSuccessTransactionResult: none(),
        checkResultTimeCounter: 0,
        status: RepaymentTransactionStatus.inprogress,
      );
}
