import 'package:bloc_test/bloc_test.dart';
import 'package:capp_repayment/capp_repayment.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:decimal/decimal.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../shared_mocks.dart';

void main() {
  late TestMockRepaymentRepository repaymentRepository;
  late TestMockUserRepository identityRepository;
  late RepaymentPaymentSummaryBloc bloc;
  late CappRepaymentSettingsVn settings;

  setUp(() {
    repaymentRepository = TestMockRepaymentRepository();
    identityRepository = TestMockUserRepository();
    settings = const CappRepaymentSettingsFakeVn();

    bloc = RepaymentPaymentSummaryBloc(
      lockStatusBloc: null,
      logger: TestMockLogger(),
      repaymentRepository: repaymentRepository,
      userRepository: identityRepository,
      settings: settings,
    );
  });

  tearDown(() {
    bloc.close();
  });

  // Test data
  final selectedAmount = Decimal.fromInt(59000);
  final request = RepaymentOnePayTransactionRequest(
    transactionCreate: RepaymentTransactionCreate(
      amount: selectedAmount,
      contractNumber: '123456',
      cuid: '**********',
      currencyCode: 'VND',
    ),
    returnUrl: 'https://onepay_homecredit.vn',
  );

  final repaymentOnePayTransaction = RepaymentOnePayTransaction(
    transactionInfo: RepaymentTransaction(
      finalAmount: Decimal.fromInt(59000),
      contractNumber: '**********',
      createdDate: 1629453010021,
      gmaTransactionId: '*************-463e-87be-faf8196d73d0',
      partnerTransactionId: 'OPN${DateTime.now().millisecondsSinceEpoch}',
      provider: 'Onepay',
      paymentStatus: 'InProgress',
    ),
    paymentUrl: '',
  );

  //Momo request
  final momoRequest = RepaymentMomoTransactionRequest(
    transactionCreate: RepaymentTransactionCreate(
      amount: Decimal.fromInt(59000),
      contractNumber: '123456',
      currencyCode: 'VND',
    ),
    client: 'android_app',
    callbackUrl: Uri.encodeFull('hcvn://repayment/result/momo'),
    provider: RepaymentPaymentMethod.momo.name,
  );

  const paymentUrl = 'momo://?action=payment&amount=59000&billId=2d7a280c-00a5-4fd8-8f7e-1b0a0'
      '1be6d56&client=android_app&code=gwpayment&description=GMA'
      '&formData=&initTransBE=true&partnerId=gwpayment&partnerName=GWPayment'
      '&reference1=**********&requestId=3e9247eb-4a48-4048-a25a-899b4a8b30f4'
      '&serviceCode=gwpayment&callbackUrl=https://testing.momo.vn:22447/fi'
      '/callback/app/3e9247eb-4a48-4048-a25a-899b4a8b30f4'
      '/gwpayment_3e9247eb-4a48-4048-a25a-899b4a8b30f4/2427614042177510_37818';
  final repaymentMomoTransaction = RepaymentMomoTransaction(
    transactionInfo: RepaymentTransaction(
      gmaTransactionId: 'transactionId1212',
      partnerTransactionId: 'MM${DateTime.now().millisecondsSinceEpoch}',
      contractNumber: '123456',
      finalAmount: Decimal.fromInt(51000),
      currencyCode: 'VND',
      provider: RepaymentPaymentMethod.momo.name,
      paymentStatus: 'Success',
    ),
    paymentUrl: paymentUrl,
  );

  // VnPay
  final vnPayRequest = RepaymentVnPayTransactionRequest(
    transactionCreate: RepaymentTransactionCreate(
      amount: Decimal.fromInt(59000),
      contractNumber: '123456',
      currencyCode: 'VND',
    ),
  );

  final repaymentVnPayTransaction = RepaymentVnPayTransaction(
    transactionInfo: RepaymentTransaction(
      gmaTransactionId: 'transactionId1212',
      partnerTransactionId: 'MM${DateTime.now().millisecondsSinceEpoch}',
      contractNumber: '123456',
      finalAmount: Decimal.fromInt(59000),
      currencyCode: 'VND',
      provider: RepaymentPaymentMethod.vnpay.name,
      paymentStatus: 'Success',
    ),
    paymentUrl:
        'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html?vnp_Amount=5000000&vnp_BankCode=VNPAYQR&vnp_Command=pay&vnp_CreateDate=**************&vnp_CurrCode=VND&vnp_IpAddr=***************&vnp_Locale=vn&vnp_OrderInfo=**********&vnp_OrderType=other&vnp_ReturnUrl=https%3A%2F%2Feba9-42-119-155-161.ngrok.io%2Ftransaction%2Frepayment%2Fvnpay%2Freturn-url&vnp_TmnCode=HOMECRE1&vnp_TxnRef=41feb363-c4dc-4186-8911-f3c2ae005ade&vnp_Version=2.1.0&vnp_SecureHash=9e83fc1ae1dfdad652d1a6fc6f6b7e08a889f0fcd97c556212d4d8545a7f934422589ef9f160ef5a1735cc766a4f2d44fc48a3919e6146b3f2c5585d8f08914d',
  );

  // ShopeePay
  final sppRequest = RepaymentShopeePayTransactionRequest(
    transactionCreate: RepaymentTransactionCreate(
      amount: Decimal.fromInt(50000),
      contractNumber: '123456',
      provider: RepaymentPaymentMethod.shopee.name,
      currencyCode: 'VND',
    ),
    returnUrl: 'hcvn://repayment/result/shopeepay',
  );
  final sppTransaction = RepaymentShopeePayTransaction(
    transactionInfo: RepaymentTransaction(
      finalAmount: Decimal.fromInt(50000),
      contractNumber: '123456',
      createdBy: '',
      cuid: '123456',
    ),
    redirectUrl:
        'https://pay.uat.airpay.vn/h5pay/pay?type=start&app_id=11000276&key=RXOd7be668908d065cdb&order_id=b64deda9-6b5b-4cef-8e94-6b0b9ea1d803&return_url=aGN2bjovL3JlcGF5bWVudC9yZXN1bHQvc2hvcGVlcGF5P2Ftb3VudD0xMDAwMDAwJmNsaWVudF9pZD0xMTAwMDI3NiZyZWZlcmVuY2VfaWQ9YjY0ZGVkYTktNmI1Yi00Y2VmLThlOTQtNmIwYjllYTFkODAzJnJlc3VsdF9jb2RlPTIwMSZzaWduYXR1cmU9V2NFN09INnZad3MtZVpjTU02YXl0WGFBTGxrQ3c3UlVESU5NWk5mT0daUSUzRA%3D%3D&source=web',
  );

  // ZaloPay
  final zaloPayRequest = RepaymentZaloPayTransactionRequest(
    transactionCreate: RepaymentTransactionCreate(
      amount: Decimal.fromInt(51000),
      contractNumber: '123456',
      provider: RepaymentPaymentMethod.zalo.name,
      currencyCode: 'VND',
    ),
    redirectUrl: 'hcvn://repayment/result/zalopay',
  );
  final zaloPayTransaction = RepaymentZaloPayTransaction(
    transactionInfo: RepaymentTransaction(
      gmaTransactionId: '12345',
      partnerTransactionId: 'HCZAP${DateTime.now().millisecondsSinceEpoch}',
      contractNumber: '123456',
      finalAmount: Decimal.fromInt(51000),
      currencyCode: 'VND',
      provider: RepaymentPaymentMethod.zalo.name,
      paymentStatus: 'Success',
    ),
    zpTransToken: '211207000002943492V92q',
  );

  // ViettelModel
  final viettelMoneyRequest = RepaymentViettelMoneyTransactionRequest(
    transactionCreate: RepaymentTransactionCreate(
      amount: Decimal.fromInt(51000),
      contractNumber: '123456',
      provider: RepaymentPaymentMethod.viettel.name,
      currencyCode: 'VND',
    ),
    returnUrl: 'viettelmoney_homecredit.vn',
    cancelUrl: 'viettelmoney_homecredit.vn',
  );
  final viettelMoneyTransaction = RepaymentViettelMoneyTransaction(
    transactionInfo: RepaymentTransaction(
      gmaTransactionId: '12345',
      contractNumber: '123456',
      finalAmount: Decimal.fromInt(51000),
      currencyCode: 'VND',
      provider: RepaymentPaymentMethod.zalo.name,
      paymentStatus: 'InProgress',
    ),
  );

  blocTest<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
    'Create OnePay transaction successfully',
    build: () {
      when(() => repaymentRepository.createOnePayTransaction(request))
          .thenAnswer((_) => Future.value(right(repaymentOnePayTransaction)));
      return bloc;
    },
    act: (bloc) => bloc.add(RepaymentPaymentSummaryEvent.makeOnePayTransaction(request)),
    expect: () => <TypeMatcher>[
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isLoading),
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isCompleted)
          .having(
            (s) => s.failureOrSuccessOnePay,
            'Result',
            optionOf(
              right<RepaymentFailure, RepaymentOnePayTransaction>(repaymentOnePayTransaction),
            ),
          ),
    ],
  );

  blocTest<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
    'Create Momo transaction successfully',
    build: () {
      when(() => repaymentRepository.createMomoTransaction(momoRequest))
          .thenAnswer((_) => Future.value(right(repaymentMomoTransaction)));
      when(() => repaymentRepository.createMomoTransactionV1(momoRequest))
          .thenAnswer((_) => Future.value(right(repaymentMomoTransaction)));
      return bloc;
    },
    act: (bloc) {
      bloc.add(RepaymentPaymentSummaryEvent.makeMomoTransaction(momoRequest));
    },
    expect: () => <TypeMatcher>[
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isLoading),
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isCompleted)
          .having(
            (s) => s.failureOrSuccessMomo,
            'Result',
            optionOf(right<RepaymentFailure, RepaymentMomoTransaction>(repaymentMomoTransaction)),
          )
          .having((s) => s.repaymentMomoTransaction, 'Momo Transaction', repaymentMomoTransaction),
    ],
  );

  blocTest<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
    'Create VnPay transaction successfully',
    build: () {
      when(() => repaymentRepository.createVnPayTransaction(vnPayRequest))
          .thenAnswer((_) => Future.value(right(repaymentVnPayTransaction)));
      return bloc;
    },
    act: (bloc) => bloc.add(RepaymentPaymentSummaryEvent.makeVnPayTransaction(vnPayRequest)),
    expect: () => <TypeMatcher>[
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isLoading),
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isCompleted)
          .having(
            (s) => s.failureOrSuccessVnPay,
            'Result',
            optionOf(right<RepaymentFailure, RepaymentVnPayTransaction>(repaymentVnPayTransaction)),
          ),
    ],
  );

  blocTest<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
    'Create ShopeePay transaction successfully',
    build: () {
      when(() => repaymentRepository.createShopeePayTransaction(sppRequest))
          .thenAnswer((_) => Future.value(right(sppTransaction)));
      return bloc;
    },
    act: (bloc) => bloc.add(RepaymentPaymentSummaryEvent.makeShopeePayTransaction(sppRequest)),
    expect: () => <TypeMatcher>[
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isLoading),
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isCompleted)
          .having(
            (s) => s.failureOrSuccessShopeePay,
            'Result',
            optionOf(right<RepaymentFailure, RepaymentShopeePayTransaction>(sppTransaction)),
          ),
    ],
  );

  blocTest<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
    'Create ZaloPay transaction successfully',
    build: () {
      when(() => repaymentRepository.createZaloPayTransaction(zaloPayRequest))
          .thenAnswer((_) => Future.value(right(zaloPayTransaction)));
      return bloc;
    },
    act: (bloc) => bloc.add(RepaymentPaymentSummaryEvent.makeZaloPayTransaction(zaloPayRequest)),
    expect: () => <TypeMatcher>[
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isLoading),
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isCompleted)
          .having(
            (s) => s.failureOrSuccessZaloPay,
            'Result',
            optionOf(right<RepaymentFailure, RepaymentZaloPayTransaction>(zaloPayTransaction)),
          ),
    ],
  );

  blocTest<RepaymentPaymentSummaryBloc, RepaymentPaymentSummaryState>(
    'Create ViettelMoney transaction successfully',
    build: () {
      when(() => repaymentRepository.createViettelMoneyTransaction(viettelMoneyRequest))
          .thenAnswer((_) => Future.value(right(viettelMoneyTransaction)));
      return bloc;
    },
    act: (bloc) => bloc.add(RepaymentPaymentSummaryEvent.makeViettelMoneyTransaction(viettelMoneyRequest)),
    expect: () => <TypeMatcher>[
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isLoading),
      const TypeMatcher<RepaymentPaymentSummaryState>()
          .having((s) => s.loadingState, 'Loading status', LoadingState.isCompleted)
          .having(
            (s) => s.failureOrSuccessViettelMoney,
            'Result',
            optionOf(right<RepaymentFailure, RepaymentViettelMoneyTransaction>(viettelMoneyTransaction)),
          ),
    ],
  );
}
