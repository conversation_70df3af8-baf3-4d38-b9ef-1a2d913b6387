import 'package:capp_repayment/capp_repayment.dart';
import 'package:decimal/decimal.dart';
import 'package:test/test.dart';

import '../../../shared_mocks.dart';

void main() {
  late RepaymentPtpEligibilityCheckingBloc bloc;
  late RepaymentRepository repaymentRepository;
  late TestMockRepaymentStorage repaymentStorage;
  late TestMockVnRepaymentTxnApi vnRepaymentTxnApi;
  late TestMockRepaymentApi repaymentApi;
  late TestMockIdentityRepository identityRepository;
  late TestMockVnRepaymentPtpApi vnRepaymentPtpApi;
  late TestMockPaymentMethodsApi paymentMethodsApi;
  late TestMockFeatureFlagRepository featureFlagRepository;
  late TestMockLocalizationRepository localizationRepository;
  setUp(() {
    vnRepaymentTxnApi = TestMockVnRepaymentTxnApi();
    repaymentStorage = TestMockRepaymentStorage();
    repaymentApi = TestMockRepaymentApi();
    identityRepository = TestMockIdentityRepository();
    vnRepaymentPtpApi = TestMockVnRepaymentPtpApi();
    paymentMethodsApi = TestMockPaymentMethodsApi();
    featureFlagRepository = TestMockFeatureFlagRepository();
    localizationRepository = TestMockLocalizationRepository();
    repaymentRepository = RepaymentRepository(
      logger: TestMockLogger(),
      repaymentStorage: repaymentStorage,
      api: repaymentApi,
      txnApi: vnRepaymentTxnApi,
      ptpApi: vnRepaymentPtpApi,
      paymentMethodApi: paymentMethodsApi,
      featureFlagRepository: featureFlagRepository,
      localizationRepository: localizationRepository,
    );

    bloc = RepaymentPtpEligibilityCheckingBloc(
      logger: TestMockLogger(),
      repaymentRepository: repaymentRepository,
      identityRepository: identityRepository,
    );
  });

  tearDown(() {
    bloc.close();
  });

  group('Test redirect', () {
    test('- Intro, option', () async {
      final ptpContract = RepaymentPtpContract(
        entityType: 'CONTRACT',
        entityId: '2100167289',
        hasActivePTP: false,
        maximalLength: 4,
        minimumAmount: Decimal.tryParse('52000'),
        minimumRate: 5,
        ptpEligible: true,
        totalAmount: Decimal.tryParse('60000'),
        lastPTP: RepaymentPtpEvaluation(
          entityId: '6659877',
          status: 'active',
          promiseAmount: Decimal.tryParse('50000'),
        ),
      );
      const contractNumber = '2100167289';
      final redirectToIntro = await bloc.handleRedirect(
        ptpContract: ptpContract,
        contractNumber: contractNumber,
        isPtpIntroductionShow: true,
      );
      final redirectToOption = await bloc.handleRedirect(
        ptpContract: ptpContract,
        contractNumber: contractNumber,
        isPtpIntroductionShow: false,
      );
      expect(redirectToIntro, RepaymentPtpEligibilityCheckingRedirect.intro);
      expect(redirectToOption, RepaymentPtpEligibilityCheckingRedirect.option);
    });
    test('- Popup Eligibility', () async {
      final ptpContract = RepaymentPtpContract(
        entityType: 'CONTRACT',
        entityId: '2100167289',
        hasActivePTP: false,
        maximalLength: 4,
        minimumAmount: Decimal.tryParse('52000'),
        minimumRate: 5,
        ptpEligible: false,
        totalAmount: Decimal.tryParse('60000'),
        lastPTP: RepaymentPtpEvaluation(
          entityId: '6659877',
          status: 'active',
          promiseAmount: Decimal.tryParse('50000'),
        ),
      );
      const contractNumber = '2100167289';
      final redirect = await bloc.handleRedirect(
        ptpContract: ptpContract,
        contractNumber: contractNumber,
        isPtpIntroductionShow: true,
      );
      expect(redirect, RepaymentPtpEligibilityCheckingRedirect.failPopup);
    });
    test('- Processing', () async {
      final ptpContract = RepaymentPtpContract(
        entityType: 'CONTRACT',
        entityId: '2100167289',
        hasActivePTP: true,
        maximalLength: 4,
        minimumAmount: Decimal.tryParse('52000'),
        minimumRate: 5,
        ptpEligible: false,
        totalAmount: Decimal.tryParse('60000'),
        lastPTP: RepaymentPtpEvaluation(
          entityId: '6659877',
          status: 'processing',
          promiseAmount: Decimal.tryParse('50000'),
        ),
      );
      const contractNumber = '2100167289';
      final redirect = await bloc.handleRedirect(
        ptpContract: ptpContract,
        contractNumber: contractNumber,
        isPtpIntroductionShow: true,
      );
      expect(redirect, RepaymentPtpEligibilityCheckingRedirect.processing);
    });
    test('- Success', () async {
      final ptpContract = RepaymentPtpContract(
        entityType: 'CONTRACT',
        entityId: '2100167289',
        hasActivePTP: true,
        maximalLength: 4,
        minimumAmount: Decimal.tryParse('52000'),
        minimumRate: 5,
        ptpEligible: false,
        totalAmount: Decimal.tryParse('60000'),
        lastPTP: RepaymentPtpEvaluation(
          entityId: '6659877',
          status: 'active',
          promiseAmount: Decimal.tryParse('50000'),
        ),
      );
      const contractNumber = '2100167289';
      final redirect = await bloc.handleRedirect(
        ptpContract: ptpContract,
        contractNumber: contractNumber,
        isPtpIntroductionShow: true,
      );
      expect(redirect, RepaymentPtpEligibilityCheckingRedirect.success);
    });
  });
}
