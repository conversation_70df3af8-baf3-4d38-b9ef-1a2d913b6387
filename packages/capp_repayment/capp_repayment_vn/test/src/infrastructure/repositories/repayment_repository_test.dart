import 'dart:io';

import 'package:capp_api/capp_api.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_repayment/capp_repayment.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:selfcareapi/model/models.dart';

import '../../../shared_mocks.dart';

void main() {
  late IRepaymentRepository repaymentRepository;
  late TestMockRepaymentStorage repaymentStorage;
  late TestMockLogger logger;
  late TestMockVnRepaymentTxnApi vnRepaymentTxnApi;
  late TestMockRepaymentApi repaymentApi;
  late TestMockVnRepaymentPtpApi vnRepaymentPtpApi;
  late TestMockPaymentMethodsApi paymentMethodsApi;
  late TestMockFeatureFlagRepository featureFlagRepository;
  late TestMockLocalizationRepository localizationRepository;
  setUp(() {
    logger = TestMockLogger();
    vnRepaymentTxnApi = TestMockVnRepaymentTxnApi();
    repaymentApi = TestMockRepaymentApi();
    repaymentStorage = TestMockRepaymentStorage();
    vnRepaymentPtpApi = TestMockVnRepaymentPtpApi();
    paymentMethodsApi = TestMockPaymentMethodsApi();
    featureFlagRepository = TestMockFeatureFlagRepository();
    localizationRepository = TestMockLocalizationRepository();
    repaymentRepository = RepaymentRepository(
      txnApi: vnRepaymentTxnApi,
      api: repaymentApi,
      logger: logger,
      repaymentStorage: repaymentStorage,
      ptpApi: vnRepaymentPtpApi,
      paymentMethodApi: paymentMethodsApi,
      featureFlagRepository: featureFlagRepository,
      localizationRepository: localizationRepository,
    );

    registerFallbackValue(OnepayRepaymentTransactionCreateDto());
    registerFallbackValue(MomoRepaymentTransactionCreateDto());
    registerFallbackValue(ShopeepayRepaymentTransactionCreateDto());
    registerFallbackValue(MocaRepaymentTransactionCreateDto());
    registerFallbackValue(ViettelRepaymentTransactionCreateDto());
    registerFallbackValue(RepaymentTransactionCreateDto());
    registerFallbackValue(ZalopayRepaymentTransactionCreateDto());
  });

  tearDown(() {});

  group('Test getContracts API', () {
    test('Successful response', () async {
      when(
        () => repaymentApi.getRepaymentContracts(),
      ).thenAnswer(
        (_) => Future.value(
          RepaymentContractListDto(
            loanContracts: FakeRepaymentApi.celContracts,
            revolvingContracts: FakeRepaymentApi.relContracts,
          ),
        ),
      );
      when(
        () => featureFlagRepository.isEnabledCached(FeatureFlag.repaymentContractSourceGetFromPcs),
      ).thenAnswer(
        (_) => false,
      );

      final response = await repaymentRepository.getContracts();
      final res = response.fold((l) => null, (r) => r);
      expect(res?.loanContracts?[0], FakeRepaymentApi.celContracts[0].toDomain());
    });

    test('Error response with status not found', () async {
      when(
        () => repaymentApi.getRepaymentContracts(),
      ).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(requestOptions: RequestOptions(path: ''), statusCode: HttpStatus.notFound),
        ),
      );
      when(
        () => featureFlagRepository.isEnabledCached(FeatureFlag.repaymentContractSourceGetFromPcs),
      ).thenAnswer(
        (_) => false,
      );
      final response = await repaymentRepository.getContracts();
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.notFound());
    });

    test('Error response with status internal server error', () async {
      when(
        () => repaymentApi.getRepaymentContracts(),
      ).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response:
              Response<void>(requestOptions: RequestOptions(path: ''), statusCode: HttpStatus.internalServerError),
        ),
      );
      when(
        () => featureFlagRepository.isEnabledCached(FeatureFlag.repaymentContractSourceGetFromPcs),
      ).thenAnswer(
        (_) => false,
      );
      final response = await repaymentRepository.getContracts();
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.internalServerError());
    });

    test('Error response with status bad request', () async {
      when(
        () => repaymentApi.getRepaymentContracts(),
      ).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(requestOptions: RequestOptions(path: ''), statusCode: HttpStatus.badRequest),
        ),
      );
      when(
        () => featureFlagRepository.isEnabledCached(FeatureFlag.repaymentContractSourceGetFromPcs),
      ).thenAnswer(
        (_) => false,
      );
      final response = await repaymentRepository.getContracts();
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.badRequest());
    });

    test('Error response with status unexpected', () async {
      when(
        () => repaymentApi.getRepaymentContracts(),
      ).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      when(
        () => featureFlagRepository.isEnabledCached(FeatureFlag.repaymentContractSourceGetFromPcs),
      ).thenAnswer(
        (_) => false,
      );
      final response = await repaymentRepository.getContracts();
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test createOnePayTransaction API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.createOnepayRepaymentTransaction(any()))
          .thenAnswer((_) => Future.value(FakeVnRepaymentTxnApi.onepayTransactionDetail));
      final response = await repaymentRepository.createOnePayTransaction(const RepaymentOnePayTransactionRequest());
      final res = response.fold((l) => null, (r) => r);
      expect(res, FakeVnRepaymentTxnApi.onepayTransactionDetail.toDomain());
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.createOnepayRepaymentTransaction(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response = await repaymentRepository.createOnePayTransaction(const RepaymentOnePayTransactionRequest());
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test createMomoTransaction API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.createMomoRepaymentTransactionV2(any()))
          .thenAnswer((_) => Future.value(FakeVnRepaymentTxnApi.momoInitResponseV2));
      final response = await repaymentRepository.createMomoTransaction(const RepaymentMomoTransactionRequest());
      final res = response.fold((l) => null, (r) => r);
      expect(res, FakeVnRepaymentTxnApi.momoInitResponseV2.toDomain());
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.createMomoRepaymentTransactionV2(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response = await repaymentRepository.createMomoTransaction(const RepaymentMomoTransactionRequest());
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test getTransactionResult API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.getRepaymentTransaction(any()))
          .thenAnswer((_) => Future.value(FakeVnRepaymentTxnApi.repaymentTransaction));
      final response = await repaymentRepository.getTransactionResult('');
      final res = response.fold((l) => null, (r) => r);
      expect(res, FakeVnRepaymentTxnApi.repaymentTransaction.toDomain());
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.getRepaymentTransaction(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response = await repaymentRepository.getTransactionResult('');
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test createVnPayTransaction API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.createVnpayRepaymentTransaction(any()))
          .thenAnswer((_) => Future.value(FakeVnRepaymentTxnApi.vnpayTransactionDetail));
      final response = await repaymentRepository.createVnPayTransaction(
        const RepaymentVnPayTransactionRequest(
          transactionCreate: RepaymentTransactionCreate(),
        ),
      );
      final res = response.fold((l) => null, (r) => r);
      expect(res, FakeVnRepaymentTxnApi.vnpayTransactionDetail.toDomain());
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.createVnpayRepaymentTransaction(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response = await repaymentRepository.createVnPayTransaction(
        const RepaymentVnPayTransactionRequest(
          transactionCreate: RepaymentTransactionCreate(),
        ),
      );
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test createShopeePayTransaction API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.createShopeepayRepaymentTransaction(any()))
          .thenAnswer((_) => Future.value(FakeVnRepaymentTxnApi.shopeepayTransactionDetail));
      final response =
          await repaymentRepository.createShopeePayTransaction(const RepaymentShopeePayTransactionRequest());
      final res = response.fold((l) => null, (r) => r);
      expect(res, FakeVnRepaymentTxnApi.shopeepayTransactionDetail.toDomain());
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.createShopeepayRepaymentTransaction(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response =
          await repaymentRepository.createShopeePayTransaction(const RepaymentShopeePayTransactionRequest());
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test createZalopayRepaymentTransaction API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.createZalopayRepaymentTransaction(any()))
          .thenAnswer((_) => Future.value(FakeVnRepaymentTxnApi.zalopayTransactionDetail));
      final response = await repaymentRepository.createZaloPayTransaction(const RepaymentZaloPayTransactionRequest());
      final res = response.fold((l) => null, (r) => r);
      expect(res, FakeVnRepaymentTxnApi.zalopayTransactionDetail.toDomain());
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.createZalopayRepaymentTransaction(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response = await repaymentRepository.createZaloPayTransaction(const RepaymentZaloPayTransactionRequest());
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test createViettelMoneyTransaction API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.createViettelRepaymentTransaction(any()))
          .thenAnswer((_) => Future.value(FakeVnRepaymentTxnApi.viettelTransactionDetail));
      final response =
          await repaymentRepository.createViettelMoneyTransaction(const RepaymentViettelMoneyTransactionRequest());
      final res = response.fold((l) => null, (r) => r);
      expect(res, FakeVnRepaymentTxnApi.viettelTransactionDetail.toDomain());
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.createViettelRepaymentTransaction(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response =
          await repaymentRepository.createViettelMoneyTransaction(const RepaymentViettelMoneyTransactionRequest());
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });

  group('Test cancelTransaction API', () {
    test('Successful response', () async {
      when(() => vnRepaymentTxnApi.cancelTransaction(any())).thenAnswer((_) => Future.value());
      final response = await repaymentRepository.cancelTransaction('');
      final res = response.fold((l) => null, (r) => r);
      expect(res, true);
    });

    test('Error response with status unexpected', () async {
      when(() => vnRepaymentTxnApi.cancelTransaction(any())).thenThrow(
        DioError(
          requestOptions: RequestOptions(path: ''),
          response: Response<void>(
            requestOptions: RequestOptions(path: ''),
            statusCode: HttpStatus.networkConnectTimeoutError,
          ),
        ),
      );
      final response = await repaymentRepository.cancelTransaction('');
      final state = response.fold((l) => l, (r) => null);
      expect(state, const RepaymentFailure.unexpected());
    });
  });
}
