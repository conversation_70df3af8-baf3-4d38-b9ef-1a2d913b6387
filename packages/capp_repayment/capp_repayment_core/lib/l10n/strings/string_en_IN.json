{"continue_word": "Continue", "transaction_details_section": "Transaction Details", "back_to_homepage": "Back to homepage", "transaction_failed_try_again": "Try Again", "transaction_details_copy_btn": "Copy", "transaction_details_copy_success": "Copied successfully!", "payment_summary_button_cancel": "Cancel", "payment_summary_button_confirm": "Confirm payment", "transaction_details": "Transaction Details", "transaction_details_done_btn": "Done", "contract_details_section": "", "payment_success_body_message": "", "ewallet": "", "bank_account": "", "payment_success_title_message": "Successful Payment", "customer_name": "Customer Name: ${name}", "phone_number": "Phone Number: ${phoneNumber}", "payment_details": "Payment details", "transaction_details_no": "Transaction No.", "transaction_details_payment_option": "Payment Option", "transaction_details_date_processed": "Date Processed", "onepay_message_code_1": "", "onepay_message_code_3": "", "onepay_message_code_4": "", "onepay_message_code_5": "", "onepay_message_code_6": "", "onepay_message_code_7": "", "onepay_message_code_8": "", "onepay_message_code_9": "", "onepay_message_code_10": "", "onepay_message_code_11": "", "onepay_message_code_12": "", "onepay_message_code_13": "", "onepay_message_code_21": "", "onepay_message_code_22": "", "onepay_message_code_23": "", "onepay_message_code_24": "", "onepay_message_code_25": "", "onepay_message_code_253": "", "onepay_message_code_99": "", "onepay_consent_title": "", "onepay_consent_description": "", "onepay_got_it_continue": "Got it, Continue", "repayment_contract_no": "Contract No.", "repayment_account_name": "Account Name", "repayment_loan_repayment": "Loan Repayment", "repayment_select_online_payment_option": "Select online payment option", "repayment_payment_amount": "Payment Amount", "repayment_online_with_atm_card": "Online with ATM Card", "repayment_online_with_partner_ewallets": "", "repayment_continue_to_payment_summary": "Continue to payment summary", "repayment_payment_summary": "Payment Summary", "repayment_please_review_your_information": "Please review your information", "repayment_payment_information": "Payment information", "repayment_contract_number": "Contract number", "repayment_full_name": "Full Name", "repayment_phone_number": "Phone Number", "repayment_payment_option": "Payment Option", "repayment_total_amount": "Total Amount", "repayment_confirm_payment": "Confirm payment", "repayment_gateway_is_opening": "gateway is opening", "repayment_method_selection_continue": "Continue", "repayment_method_selection_title": "Repayment methods", "repayment_method_selection_choose_payment_method": "Pick your preferred payment method", "repayment_method_selection_pay_by_cash_title": "", "repayment_method_selection_pay_by_cash_subTitle": "", "repayment_method_selection_pay_via_bank_transfer_title": "", "repayment_method_selection_pay_via_bank_transfer_subTitle": "", "repayment": "Repayment", "repayment_pay_for_my_loans": "Pay for my Loans", "repayment_loan": "loan", "repayment_loans": "loans", "repayment_need_repayment": "need repayment", "repayment_account_no": "Account No.", "repayment_due_date": "Due Date", "repayment_due_amount": "Due Amount", "repayment_select_amount_choose_to_pay": "Choose amount to pay", "repayment_select_amount_enter_custom_amount": "Enter your own amount", "repayment_select_amount_custom_amount": "Custom Amount", "repayment_select_amount_min_amount_validation_mes": "The amount must be set above", "repayment_select_amount_over_total_debt_validation_mes": "The amount can not be greater than", "repayment_select_amount_continue_payment_options": "Continue to payment options", "repayment_select_amount_due_amount_validation_mes_heading": "Paying less than the Current Amount Due?", "repayment_select_amount_due_amount_validation_mes": "The amount proposed is less than the Current Due Amount. Please make sure that you pay the remaining amount before the Due Date to avoid penalties.", "repayment_select_amount_edit": "Edit Amount", "repayment_minimum_due_amount": "Minimum Amount Due", "repayment_total_due_amount": "Total Amount Due", "repayment_select_amount_rel_minimum_amount_validation_mes": "The amount is less than Minimum amount. You may be charged for DPD.", "repayment_select_amount_rel_minimum_amount_error": "The minimum amount to make payment through the application is ${minimumThresholdAmount}VND, so please pay this amount via bank transfer to Home Credit.", "repayment_there_is_no_available_loan": "There is no available loan to repay", "repayment_method_selection_pay_via_auto_debit_arrangement_title": "", "repayment_method_selection_pay_via_auto_debit_arrangement_sub_title": "", "repayment_method_selection_pay_in_app_title": "", "repayment_method_selection_pay_in_app_sub_title": "", "repayment_method_selection_pay_over_the_counter_title": "", "repayment_method_selection_pay_over_the_counter_sub_title": "", "repayment_transaction_failed_something_went_wrong": "Something went wrong", "repayment_transaction_failed_generic_message": "Please try again or go to homepage where you can check our other special offers and services for you!", "repayment_debit_cards": "Debit Cards", "repayment_pay_with_your_debit_cards": "Pay with your Debit Cards (Visa or Mastercard)", "repayment_card_number": "Card Number", "repayment_you_dont_have_any_card_saved": "", "repayment_card_enrollment": "", "repayment_your_card_provider_will_temporarily": "", "repayment_bank_transfer": "Bank Transfer", "repayment_in_app_payment": "", "repayment_emoney_and_ecommerce": "", "repayment_retail": "", "repayment_contract_information": "Contract Information", "repayment_bank_transfer_information": "Bank Transfer Information", "repayment_bank_transfer_information_title": "Bank Transfer\nInformation", "repayment_bank_name": "Bank Name", "repayment_account_number": "Account Number", "repayment_transfer_content": "Transfer Contents", "repayment_for_contract_number": "Payment for contract\nnumber", "repayment_guidance": "Payment Guidance", "repayment_bca_atm_step1": "", "repayment_bca_atm_step2": "", "repayment_bca_atm_step3": "", "repayment_bca_atm_step4": "", "repayment_bca_atm_step5": "", "repayment_bca_atm_step6": "", "repayment_bca_atm_step7": "", "repayment_bca_mobile_step1": "", "repayment_bca_mobile_step2": "", "repayment_bca_mobile_step3": "", "repayment_bca_mobile_step4": "", "repayment_bca_mobile_step5": "", "repayment_bca_mobile_step6": "", "repayment_bni_atm_step1": "", "repayment_bni_atm_step2": "", "repayment_bni_atm_step3": "", "repayment_bni_atm_step4": "", "repayment_bni_atm_step5": "", "repayment_bni_atm_step6": "", "repayment_bni_atm_step7": "", "repayment_bni_mobile_step1": "", "repayment_bni_mobile_step2": "", "repayment_bni_mobile_step3": "", "repayment_bni_mobile_step4": "", "repayment_bni_mobile_step5": "", "repayment_bni_mobile_step6": "", "repayment_bni_mobile_step7": "", "repayment_mandiri_atm_step1": "", "repayment_mandiri_atm_step2": "", "repayment_mandiri_atm_step3": "", "repayment_mandiri_atm_step4": "", "repayment_mandiri_atm_step5": "", "repayment_mandiri_atm_step6": "", "repayment_mandiri_mobile_step1": "", "repayment_mandiri_mobile_step2": "", "repayment_mandiri_mobile_step3": "", "repayment_mandiri_mobile_step4": "", "repayment_mandiri_mobile_step5": "", "repayment_mandiri_mobile_step6": "", "repayment_mandiri_mobile_step7": "", "repayment_bri_atm_step1": "", "repayment_bri_atm_step2": "", "repayment_bri_atm_step3": "", "repayment_bri_atm_step4": "", "repayment_bri_atm_step5": "", "repayment_bri_atm_step6": "", "repayment_bri_atm_step7": "", "repayment_bri_atm_step8": "", "repayment_bri_mobile_step1": "", "repayment_bri_mobile_step2": "", "repayment_bri_mobile_step3": "", "repayment_bri_mobile_step4": "", "repayment_bri_mobile_step5": "", "repayment_bri_mobile_step6": "", "repayment_permata_atm_step1": "", "repayment_permata_atm_step2": "", "repayment_permata_atm_step3": "", "repayment_permata_atm_step4": "", "repayment_permata_atm_step5": "", "repayment_permata_atm_step6": "", "repayment_permata_atm_step7": "", "repayment_permata_atm_step8": "", "repayment_permata_mobile_step1": "", "repayment_permata_mobile_step2": "", "repayment_permata_mobile_step3": "", "repayment_permata_mobile_step4": "", "repayment_permata_mobile_step5": "", "repayment_permata_mobile_step6": "", "repayment_btpn_atm_step1": "", "repayment_btpn_atm_step2": "", "repayment_btpn_atm_step3": "", "repayment_btpn_atm_step4": "", "repayment_btpn_atm_step5": "", "repayment_btpn_atm_step6": "", "repayment_btpn_atm_step7": "", "repayment_btpn_atm_step8": "", "repayment_btpn_mobile_step1": "", "repayment_btpn_mobile_step2": "", "repayment_btpn_mobile_step3": "", "repayment_btpn_mobile_step4": "", "repayment_btpn_mobile_step5": "", "repayment_btpn_mobile_step6": "", "repayment_btpn_mobile_step7": "", "repayment_pa_atm_step1": "", "repayment_pa_atm_step2": "", "repayment_pa_atm_step3": "", "repayment_pa_atm_step4": "", "repayment_pa_atm_step5": "", "repayment_pa_atm_step6": "", "repayment_pa_atm_step7": "", "repayment_bersama_atm_step1": "", "repayment_bersama_atm_step2": "", "repayment_bersama_atm_step3": "", "repayment_bersama_atm_step4": "", "repayment_bersama_atm_step5": "", "repayment_bersama_atm_step6": "", "repayment_bersama_atm_step7": "", "repayment_bersama_atm_step8": "", "repayment_atm": "ATM", "repayment_bank": "Bank", "repayment_mobile_banking": "Mobile Banking", "repayment_bank_transfer_select_option": "", "repayment_bank_transfer_virtual_account": "", "repayment_fee": "Fee", "repayment_add_card": "Add card", "repayment_vnpay_app_back_alert": "Are you sure to back?", "shopeepay_error_message_code_201": "", "shopeepay_error_message_code_202": "", "shopeepay_error_message_code_203": "", "shopeepay_error_message_code_204": "", "repayment_processing": "Processing...", "repayment_you_will_be_noticed_when": "", "repayment_timeout": "Time out", "repayment_please_try_again": "Please try again later", "zalopay_error_message_code_invalid_response": "", "zalopay_error_message_code_invalid_order": "", "zalopay_error_message_code_fail": "", "zalopay_error_message_code_cancel": "", "exception__occurred": "Exception occurred", "viettel_error_message_code_22": "", "viettel_error_message_code_V02": "", "viettel_error_message_code_V03": "", "viettel_error_message_code_21": "", "viettel_error_message_code_685": "", "viettel_error_message_code_16": "", "viettel_error_message_code_W04": "", "viettel_error_message_code_V04": "", "viettel_error_message_code_V05": "", "viettel_error_message_code_V06": "", "viettel_error_message_code_S_MAINTAIN": "", "viettel_error_message_code_99": "", "viettel_error_message_code_M03": "", "viettel_error_message_code_M04": "", "viettel_error_message_code_813": "", "viettel_error_message_code_V01": "", "viettel_error_message_code_M01": "", "viettel_error_message_code_M02": "", "viettel_error_message_code_P48": "", "viettel_error_message_code_430": "", "viettel_error_message_code_427": "", "viettel_error_message_code_191": "", "viettel_error_message_code_P01": "", "viettel_error_message_code_P03": "", "viettel_error_message_code_32": "", "repayment_no_payment_is_required": "No payment is required at this moment", "repayment_add_card_capitalize": "Add Card", "repayment_sit_tight": "Sit tight! We are processing your transaction", "repayment_we_will_immediately_notify": "We will immediately notify you once we have an update on your transaction request", "repayment_card_verification": "Card Verification", "repayment_please_enter_the_amount_indicated": "Please enter the amount indicated in the SMS or email or online/mobile banking accounts sent to you.", "repayment_enter_amount": "Enter Amount", "repayment_verify": "Verify", "payment_success_body_message_ph": "Thank you. The repayment amount is confirmed to be transferred to Home Credit.", "processing_fee": "Processing Fee", "waived": "W<PERSON><PERSON>", "repayment_customer_name": "Customer Name", "repayment_select_emoney_and_ecommerce": "", "repayment_electric_money": "", "repayment_emoney": "", "repayment_ecommerce": "", "repayment_instant_payment": "", "repayment_emoney_gotagihan_step1": "", "repayment_emoney_gotagihan_step2": "", "repayment_emoney_gotagihan_step3": "", "repayment_emoney_gotagihan_step4": "", "repayment_emoney_gotagihan_step5": "", "repayment_emoney_gotagihan_step6": "", "repayment_emoney_gotagihan_step7": "", "repayment_emoney_ayopop_step1": "", "repayment_emoney_ayopop_step2": "", "repayment_emoney_ayopop_step3": "", "repayment_emoney_ayopop_step4": "", "repayment_emoney_ayopop_step5": "", "repayment_emoney_ayopop_step6": "", "repayment_emoney_ayopop_step7": "", "repayment_emoney_ayopop_step8": "", "repayment_emoney_bebasbayar_step1": "", "repayment_emoney_bebasbayar_step2": "", "repayment_emoney_bebasbayar_step3": "", "repayment_emoney_bebasbayar_step4": "", "repayment_emoney_bebasbayar_step5": "", "repayment_emoney_bebasbayar_step6": "", "repayment_ecommerce_tokopedia_step1": "", "repayment_ecommerce_tokopedia_step2": "", "repayment_ecommerce_tokopedia_step3": "", "repayment_ecommerce_tokopedia_step4": "", "repayment_ecommerce_tokopedia_step5": "", "repayment_ecommerce_tokopedia_step6": "", "repayment_ecommerce_tokopedia_step7": "", "repayment_ecommerce_tokopedia_step8": "", "repayment_ecommerce_tokopedia_step9": "", "repayment_ecommerce_bukalapak_step1": "", "repayment_ecommerce_bukalapak_step2": "", "repayment_ecommerce_bukalapak_step3": "", "repayment_ecommerce_bukalapak_step4": "", "repayment_ecommerce_bukalapak_step5": "", "repayment_ecommerce_bukalapak_step6": "", "repayment_ecommerce_bukalapak_step7": "", "repayment_ecommerce_blibli_step1": "", "repayment_ecommerce_blibli_step2": "", "repayment_ecommerce_blibli_step3": "", "repayment_ecommerce_blibli_step4": "", "repayment_ecommerce_blibli_step5": "", "repayment_ecommerce_blibli_step6": "", "repayment_ecommerce_blibli_step7": "", "repayment_ecommerce_blibli_step8": "", "repayment_ecommerce_lazada_step1": "", "repayment_ecommerce_lazada_step2": "", "repayment_ecommerce_lazada_step3": "", "repayment_ecommerce_lazada_step4": "", "repayment_ecommerce_lazada_step5": "", "repayment_ecommerce_lazada_step6": "", "repayment_ecommerce_lazada_step7": "", "repayment_ecommerce_lazada_step8": "", "repayment_ada": "", "repayment_payments_made_convenient": "", "repayment_set_up_an_automatic": "", "repayment_what_accounts_can_i": "", "repayment_online_banking_accounts": "", "repayment_enroll_your_online": "", "repayment_enroll_your_mastercard": "", "repayment_regular_bank_accounts": "", "repayment_manually_enroll": "", "repayment_why_should_i": "", "repayment_easy_to_set_up": "", "repayment_hassle_free": "", "repayment_avoid_delays": "", "repayment_setup_now": "", "repayment_ada_terms_conditions_detail_title": "", "repayment_ada_terms_conditions_detail_agree_continue": "", "repayment_online_banks": "", "repayment_enroll_your_online_bank_account_to_auto": "", "repayment_use_your_visa_or_mastercard": "", "repayment_enjoy_hassle_free": "", "repayment_pay_with_auto_debit_arrangement": "", "repayment_we_willdeduct_your_loan": "", "repayment_make_sure_you_have_enough_funds": "", "repayment_by_tapping_on_continue": "", "repayment_term_and_conditions": "", "repayment_how_it_works": "", "repayment_ada_confirm_enrollment_title": "", "repayment_ada_confirm_enrollment_description": "", "repayment_ada_bank_name": "", "repayment_ada_card_type": "", "repayment_ada_card_number": "", "repayment_ada_loan_account_no": "", "repayment_ada_monthly_installment": "", "repayment_ada_loan_type": "", "repayment_ada_starting_from": "", "repayment_ada_up_until": "", "repayment_ada_total_monthly_installment": "", "repayment_ada_cancel_enrollment": "", "repayment_ada_confirm_enroll": "", "repayment_ada_enrollment_details": "", "repayment_ada_enrollment_successful": "", "repayment_ada_enrollment_successful_message": "", "repayment_ada_loan_account_number": "", "repayment_ada_total_monthly_deduction": "", "repayment_ada_back_to_dashboard": "", "repayment_ada_view_payment_schedule": "", "repayment_my_cards": "", "repayment_saved_cards": "", "repayment_all_your_saved_cards_in": "", "repayment_remove_card_ending_in": "", "repayment_yes_remove": "", "repayment_no_wait": "", "repayment_personal_loan": "Personal Loan", "repayment_consumer_loan": "Consumer loan", "repayment_bank_branch": "Bank Branch", "repayment_virtual_account_number": "Virtual Account Number", "repayment_qr_code": "QR Code", "repayment_you_can_pay_with_this_qr": "You can pay with this QR by Internet Banking App", "repayment_share": "Share", "repayment_download": "Download", "repayment_download_fail": "Download QR code failed, please try again", "repayment_download_successfully": "Download successfully", "repayment_close": "Close", "repayment_share_fail": "Share QR code failed, please try again", "repayment_open_settings": "Open settings", "repayment_to_have_permission_to_access_photo_ios": "To have photo gallery access, please open Settings and set up photo permissions for the application", "repayment_to_have_permission_to_access_photo_android": "To have photo gallery access, please open settings and set up file and media permissions for the application", "repayment_select_amount_minimum_amount_allowed": "Minimum repayment amount is ${minimumThresholdAmount}", "repayment_select_amount_cel_amount_must_be_equal": "The repayment amount is ${minimumThresholdAmount}", "repayment_minimum_amount": "Minimum Amount Due", "repayment_details": "Details", "repayment_retail_transfer": "", "repayment_available_partner_retail": "", "repayment_available_partner_retail_description": "", "repayment_maximum_payment_amount_for_transfer": "", "repayment_go_to_gerai_retail": "", "repayment_pay_according_to_the_amount": "", "repayment_make_sure_to_keep_your_payment_receipt": "", "cancel_payment_message": "Are you sure you want to cancel? This will stop your on-going transaction and cannot be undone.", "cancel_payment_yes": "Yes, I want to cancel", "cancel_payment_no": "No, I want to continue", "repayment_pay_via_bank_transfer_is_not_available": "", "repayment_got_it_change_method": "", "repayment_contract_repayment": "", "repayment_repay_now": "", "repayment_outstanding_balance": "", "repayment_select_amount_rel_bnpl_minimum_amount_validation_mes": "", "repayment_virtual_account_no": "", "repayment_product_credit_card": "Credit Card", "repayment_product_bnpl": "Home PayLater", "repayment_product_bnpl_new": "", "change_payment_amount": "", "repayment_select_over_the_counter_option": "", "repayment_generate_barcode": "", "repayment_zero": "", "repayment_preferred_partner_stores": "", "repayment_real_time_posting": "", "repayment_other_partner_stores": "", "repayment_payment_instruction": "", "repayment_find_nearest_store": "", "repayment_show_barcode_title": "", "repayment_show_barcode_message": "", "repayment_expiry_date": "", "repayment_account_type": "", "repayment_save_screenshot": "", "repayment_subtotal": "", "repayment_save_screenshot_successfully": "", "repayment_save_screenshot_fail": "", "repayment_scan_qr_code": "", "repayment_please_transfer_to_this_account_below": "", "repayment_please_download_and_scan": "", "repayment_qr_bank_transfer": "", "repayment_method_selection_ewallet_title": "", "repayment_method_selection_ewallet_subtitle": "", "repayment_method_selection_internet_banking_title": "", "repayment_method_selection_internet_banking_subtitle": "", "repayment_method_selection_atm_title": "", "repayment_method_selection_atm_subtitle": "", "repayment_select_amount_otc_amount_must_be_between_min_max": "${minimumThresholdAmount}${otcMaximumAmount}", "repayment_contract_number2": "", "repayment_main_title": "Repayment", "repayment_main_payment_amount": "", "repayment_main_custom_amount": "", "repayment_main_custom_amount_description": "", "repayment_main_custom_amount_header": "", "repayment_main_total_payment_amount": "", "repayment_main_proceed": "", "repayment_pay_before": "", "repayment_change": "", "repayment_select": "", "repayment_select_loan": "", "repayment_overdue": "", "repayment_my_loans": "", "repayment_contract_has_been_updated_back_to_home_message": "", "repayment_contract_has_been_updated_select_next_contract_message": "", "repayment_ok": "", "repayment_online_payment_via_vnpay": "", "repayment_online_payment_via_onepay": "", "repayment_online_transfer_directly": "", "repayment_choose_your_payment_method": "", "repayment_mobile_banking_desc": "", "repayment_virtual_account": "", "repayment_virtual_account_desc": "", "repayment_main_due_amount": "", "repayment_mobile_banking_apps": "", "repayment_mobile_banking_app": "", "repayment_payment_via_vnpay": "", "repayment_payment_via_onepay": "", "repayment_atm_card": "", "repayment_atm_card_header": "", "repayment_method": "", "repayment_internet_banking": "", "repayment_bank_transfer_header": "", "repayment_back_to_homepage": "", "repayment_select_amount_rel_minimum_amount_validation_title": "", "repayment_select_amount_rel_bnpl_minimum_amount_validation_title": "", "repayment_select_amount_due_amount_validation_title": "", "continue_payment": "", "got_it": "", "repayment_pay_via_bank_transfer_is_not_available_mes": "", "repayment_pay_via_bank_transfer_is_not_available_title": "", "transfer_to_account_number": "", "scan_qr_code": "", "repayment_contract_information_normal": "", "repayment_total_amount_normal": "", "repayment_due_date_normal": "", "repayment_account_name_normal": "", "repayment_bank_name_normal": "", "repayment_bank_branch_normal": "", "repayment_virtual_account_number_normal": "", "repayment_please_choose_your_banking_app": "", "introduction": "", "what_is_promise_to_pay": "", "promise_to_pay_description": "", "detail": "", "promise_to_pay_detail_title1": "", "promise_to_pay_detail_message1": "", "promise_to_pay_detail_title2": "", "promise_to_pay_detail_message2": "", "promise_to_pay_detail_title3": "", "promise_to_pay_detail_message3": "", "promise_to_pay_detail_title4": "", "promise_to_pay_detail_message4": "", "repayment_send": "", "repayment_select_payment_date": "", "repayment_payment_plan": "", "repayment_please_commit_as_selected": "", "repayment_day_later": "", "repayment_days_later": "", "repayment_current_due_date": "", "repayment_extend_util_date": "", "repayment_min_payment_amount_is": "", "repayment_want_to_extend_payment_date": "", "repayment_you_need_to_commit_payment_as_selected_date": "", "repayment_i_agree": "", "repayment_cancel": "", "repayment_ptp_processing_title": "", "repayment_ptp_processing_sub_title": "", "repayment_ptp_process_back_button": "", "promise_to_pay": "", "promise_to_pay_early_message": "", "promise_to_pay_late_message": "", "promise_to_pay_on_time_message": "", "promise_to_pay_remind_message": "", "repayment_total_due_amount_normal": "", "repayment_ptp_success_title": "", "repayment_ptp_success_main_title": "", "repayment_ptp_success_main_des": "", "repayment_ptp_success_detail_due_date": "", "repayment_ptp_success_detail_amount": "", "repayment_ptp_success_done_button": "", "repayment_cc_outstanding_period_amount": "", "repayment_ptp_eligibility_checking_title": "", "repayment_ptp_eligibility_popup_title": "", "repayment_ptp_eligibility_popup_i_see_button": "", "promotion": "", "select": "", "change": "", "select_voucher_to_get_discount": "", "voucher_is_not_applicable": "", "repayment_view_details": "", "repayment_min_repayment": "", "repayment_promotion": "", "repayment_apply": "", "repayment_use_now": "", "repayment_use_later": "", "repayment_voucher_details": "", "repayment_this_voucher_is_applied": "", "repayment_ptp_popup_retry_title": "", "repayment_ptp_popup_retry_retry_button": "", "repayment_ptp_popup_retry_cancel_button": "", "repayment_sorry_we_dont_have_any_voucher": "", "repayment_ptp_submit_error": "", "repayment_voucher_not_applicable": "", "repayment_your_voucher_is_not_applicable": "", "repayment_skip_voucher_and_continue": "", "repayment_skip_voucher_and_continue_question": "", "repayment_see_other_vouchers": "", "repayment_ptp_rejected_title": "", "repayment_ptp_rejected_main_title": "", "repayment_ptp_rejected_main_des": "", "repayment_ptp_rejected_detail_due_date": "", "repayment_ptp_rejected_detail_amount": "", "repayment_ptp_rejected_done_button": "", "payment_amount": "", "repayment_discount_amount": "", "repayment_final_amount": "", "repayment_refresh": "", "repayment_something_went_wrong_please_try_again": "", "repayment_cancel_payment": "", "repayment_cancel_payment_message": "", "repayment_quit": "", "repayment_description": "", "repayment_dismiss_offer": "", "repayment_download_qr_code": "", "repayment_download_the_qr_code": "", "repayment_to_apply": "", "repayment_dismiss_promo": "", "repayment_promotion_not_applicable": "", "repayment_transfer_to_account_number": "", "repayment_please_scan_qr_to_apply_promotion": "", "repayment_qr_discount": "", "repayment_cancel_voucher_if_not_apply": "", "repayment_skip_promotion_download_qr": "", "repayment_skip_promotion_and_payment": "", "repayment_skip_promotion_60_min": "", "repayment_skip_promotion_to_apply_voucher_cancel": "", "repayment_ewallet_title": "", "repayment_ewallet_introduce_title": "", "repayment_ewallet_introduce_message": "", "repayment_ewallet_alfamart": "", "repayment_ewallet_bca": "", "repayment_ewallet_bni": "", "repayment_ewallet_cimb": "", "repayment_ewallet_atm_bersama_prima": "", "repayment_ewallet_bersama_prima": "", "repayment_ewallet_alfamart_description": "", "repayment_ewallet_payment_guideline": "", "repayment_ewallet_alfamart_step1": "", "repayment_ewallet_alfamart_step2": "", "repayment_ewallet_alfamart_step3": "", "repayment_ewallet_alfamart_step4": "", "repayment_ewallet_alfamart_step5": "", "repayment_ewallet_alfamart_step6": "", "repayment_ewallet_virtual_account_number": "", "repayment_ewallet_atm": "", "repayment_ewallet_klik_bca": "", "repayment_ewallet_m_bca": "", "repayment_ewallet_sms_banking": "", "repayment_ewallet_internet_banking": "", "repayment_ewallet_m_banking": "", "repayment_ewallet_cimb_clicks": "", "repayment_ewallet_go_mobile_cimb": "", "repayment_ewallet_bca_atm_step1": "", "repayment_ewallet_bca_atm_step2": "", "repayment_ewallet_bca_atm_step3": "", "repayment_ewallet_bca_atm_step4": "", "repayment_ewallet_bca_atm_step5": "", "repayment_ewallet_bca_klik_bca_step1": "", "repayment_ewallet_bca_klik_bca_step2": "", "repayment_ewallet_bca_klik_bca_step3": "", "repayment_ewallet_bca_klik_bca_step4": "", "repayment_ewallet_bca_klik_bca_step5": "", "repayment_ewallet_bca_m_bca_step1": "", "repayment_ewallet_bca_m_bca_step2": "", "repayment_ewallet_bca_m_bca_step3": "", "repayment_ewallet_bca_m_bca_step4": "", "repayment_ewallet_bca_m_bca_step5": "", "repayment_ewallet_bca_m_bca_step6": "", "repayment_ewallet_bni_atm_step1": "", "repayment_ewallet_bni_atm_step2": "", "repayment_ewallet_bni_atm_step3": "", "repayment_ewallet_bni_atm_step4": "", "repayment_ewallet_bni_atm_step5": "", "repayment_ewallet_bni_sms_banking_step1": "", "repayment_ewallet_bni_sms_banking_step2": "", "repayment_ewallet_bni_internet_banking_step1": "", "repayment_ewallet_bni_internet_banking_step2": "", "repayment_ewallet_bni_internet_banking_step3": "", "repayment_ewallet_bni_internet_banking_step4": "", "repayment_ewallet_bni_internet_banking_step5": "", "repayment_ewallet_bni_internet_banking_step6": "", "repayment_ewallet_bni_m_banking_step1": "", "repayment_ewallet_bni_m_banking_step2": "", "repayment_ewallet_bni_m_banking_step3": "", "repayment_ewallet_bni_m_banking_step4": "", "repayment_ewallet_bni_m_banking_step5": "", "repayment_ewallet_bni_m_banking_step6": "", "repayment_ewallet_cimb_atm_step1": "", "repayment_ewallet_cimb_atm_step2": "", "repayment_ewallet_cimb_atm_step3": "", "repayment_ewallet_cimb_atm_step4": "", "repayment_ewallet_cimb_atm_step5": "", "repayment_ewallet_cimb_clicks_step1": "", "repayment_ewallet_cimb_clicks_step2": "", "repayment_ewallet_cimb_clicks_step3": "", "repayment_ewallet_cimb_clicks_step4": "", "repayment_ewallet_cimb_clicks_step5": "", "repayment_ewallet_cimb_clicks_step6": "", "repayment_ewallet_cimb_go_mobile_step1": "", "repayment_ewallet_cimb_go_mobile_step2": "", "repayment_ewallet_cimb_go_mobile_step3": "", "repayment_ewallet_cimb_go_mobile_step4": "", "repayment_ewallet_cimb_go_mobile_step5": "", "repayment_ewallet_cimb_go_mobile_step6": "", "repayment_ewallet_bersama_atm_step1": "", "repayment_ewallet_bersama_atm_step2": "", "repayment_ewallet_bersama_atm_step3": "", "repayment_ewallet_bersama_atm_step4": "", "repayment_ewallet_bersama_atm_step5": "", "repayment_ewallet_bersama_internet_banking_step1": "", "repayment_ewallet_bersama_internet_banking_step2": "", "repayment_ewallet_bersama_internet_banking_step3": "", "repayment_ewallet_bersama_internet_banking_step4": "", "repayment_ewallet_bersama_internet_banking_step5": "", "repayment_ewallet_bersama_internet_banking_step6": "", "repayment_ewallet_bersama_m_banking_step1": "", "repayment_ewallet_bersama_m_banking_step2": "", "repayment_ewallet_bersama_m_banking_step3": "", "repayment_ewallet_bersama_m_banking_step4": "", "repayment_ewallet_bersama_m_banking_step5": "", "repayment_ewallet_bersama_m_banking_step6": "", "repayment_ewallet_no_phone_number_title": "", "repayment_ewallet_no_phone_number_message": "", "repayment_success_loan_offers_section_title": "Offers for you", "repayment_momo": "", "repayment_online_repayment": "", "repayment_enjoy_quick_easy_online_repayment": "", "repayment_why_should_i_pay_online": "", "repayment_pay_online_anywhere": "", "repayment_connect_accounts": "", "repayment_fast_and_convenient": "", "repayment_what_account_i_can_use": "", "repayment_online_banking_accounts_des": "", "repayment_debit_cards_des": "", "repayment_online_bank_title": "", "repayment_online_bank_desc": "", "repayment_debit_card_title": "", "repayment_debit_card_desc": "", "repayment_no_fee": "", "repayment_choose_payment_option": "", "repayment_your_order": "", "repayment_ada_intro_easy_to_repay": "", "repayment_ada_intro_first_title": "", "repayment_ada_intro_first_subtitle": "", "repayment_ada_intro_second_title": "", "repayment_ada_intro_second_subtitle": "", "repayment_ada_intro_third_title": "", "repayment_ada_intro_third_subtitle": "", "repayment_ada_no_active_ada_title": "", "repayment_ada_no_active_ada_subtitle": "", "repayment_ada_enroll": "", "repayment_ada_payment_via_momo": "", "repayment_ada_enroll_success": "", "repayment_ada_enroll_success_description": "", "repayment_ada_contract_type": "", "repayment_ada_payment_method": "", "repayment_ada_due_date_cel": "", "repayment_ada_due_date_rel": "", "repayment_ada_amount": "", "repayment_ada_amount_limit_warning": "", "repayment_done": "", "repayment_ada_main_title": "", "repayment_ada_main_auto_deduct_date_cel": "", "repayment_ada_main_auto_deduct_date_rel": "", "repayment_ada_main_auto_deduct_amount": "", "repayment_ada_main_min_amount_due": "", "repayment_ada_main_pay_mad_note": "", "repayment_ada_main_payment_method_desc": "", "repayment_ada_main_due_date_rel_desc": "", "repayment_ada_main_due_date_cel_desc": "", "repayment_ada_main_auto_deduct_date_rel_desc": "", "repayment_ada_main_deduct": "", "repayment_ada_onepay": "", "repayment_ada_zalopay": "", "repayment_ada_main_cancel_ada_popup_title": "", "repayment_ada_main_cancel_ada_popup_desc": "", "repayment_delete": "", "repayment_back": "", "repayment_ada_main_payment_method": "", "service_fee": "Service Fee", "repayment_sappi_desc": "", "repayment_sappi_desc_with_contract_number": "", "valid_one_year": "Valid for 1 year", "valid_loan_duration": "Valid for loan duration", "repayment_ada_credit_card": "", "repayment_ada_cash_loan": "", "repayment_due_date_desc": "", "repayment_ada_due_date_cel_desc": "", "repayment_ada_due_date_rel_desc": "", "repayment_ada_success_desc": "", "repayment_ada_failed_register": "", "repayment_ada_cel_failed_register_desc": "", "repayment_ada_cc_failed_register_desc": "", "repayment_ada_bnpl_failed_register_desc": "", "repayment_ada_bnpl_failed_register_desc_new": "", "repayment_promotion_first_adoption_title": "Discount voucher ${voucher} for you!", "repayment_promotion_first_adoption_sutitle": "Apply for loan repayment on Home Credit app with amount from ${minAmount}", "repayment_promotion_first_adoption_pos": "Repay at stores", "repayment_promotion_first_adoption_app": "Repay on app", "repayment_promotion_first_adoption_voucher": "Discount ${voucher}", "repayment_promotion_first_adoption_button": "Claim voucher now", "repayment_back_later": "", "repayment_pay_with_auto_debit_arrangement_title": "", "repayment_we_willdeduct_your_loan_title": "", "repayment_make_sure_you_have_enough_funds_title": "", "repayment_card_details": "Card Details", "repayment_account_details": "Account Details", "repayment_additional_info_title": "", "repayment_additional_info_desc": "", "no_fee": "No Fee", "repayment_ada_explore_auto_debit": "", "repayment_ada_explore_auto_debit_description": "", "repayment_ada_explore": "", "repayment_ada_later": "", "repayment_ada_explore_auto_debit_feature": "", "repayment_payment_via": "", "repayment_monthly": "", "repayment_ada_no_loan_title": "", "repayment_ada_no_loan_description": "", "repayment_overdue_desc": "", "repayment_confirm": "", "repayment_ada_no_eligible_contract_message": "", "something_went_wrong": "", "repayment_ada_cancel_ada_error_try_again": "", "repayment_ada_back_later": "", "repayment_ada_try_again": "", "repayment_ada_payment_method_for_ad": "", "feature_unavailable": "This feature is unavailable at the moment", "repayment_brankas_notice": "Your payment success depends on whether your bank is currently online and operational.", "repayment_brankas_agreement_phrase_1": "By tapping on “Confirm Payment” means that you have fully read, understood, and agreed to the updated ", "repayment_brankas_hc_privacy_notice": "Home Credit Privacy Notice", "repayment_brankas_agreement_phrase_2": " and ", "repayment_tnc": "Terms & Conditions", "repayment_brankas_agreement_phrase_3": ", as well as the  ", "repayment_brankas_term_of_use": "Brankas Terms of Use and Policy", "repayment_brankas_agreement_phrase_4": " and the ", "repayment_brankas_privacy_notice": "Brankas Privacy Notice and Consent Form", "repayment_brankas_terms_of_use": "Brankas Terms of Use", "repayment_brankas_privacy_notice_header": "Brankas Privacy Notice", "repayment_bank_unavailable": "Bank is currently unavailable", "repayment_bank_transfer_title": "", "repayment_bank_transfer_download_and_open_bank": "", "repayment_bank_transfer_copy_and_open_bank": "", "repayment_bank_transfer_download_qr": "", "repayment_bank_transfer_save_account_number": "", "repayment_bank_transfer_payment_info": "", "repayment_bank_transfer_download_qr_alert": "", "repayment_bank_transfer_save_account_number_alert": "", "repayment_bank_transfer_total_amount": "", "repayment_bank_transfer_view_intro": "", "repayment_bank_transfer_home_credit_name": "", "repayment_bank_transfer_home_credit_bidv_bank": "", "repayment_bank_transfer_account_number": "", "repayment_bank_transfer_intro_title": "", "repayment_bank_transfer_intro_guide_title": "", "repayment_bank_transfer_intro_guide_subtitle": "", "repayment_bank_transfer_intro_1": "", "repayment_bank_transfer_intro_2": "", "repayment_bank_transfer_intro_3": "", "repayment_bank_transfer_intro_ok": "", "repayment_bank_transfer_qr_waiting_title": "", "repayment_bank_transfer_qr_waiting_desc": "", "repayment_bank_transfer_qr_waiting_primary_btn_title": "", "repayment_bank_transfer_qr_waiting_second_btn_title": "", "repayment_choose_bank": "", "repayment_search_bank_hint": "", "repayment_valid_to": ""}