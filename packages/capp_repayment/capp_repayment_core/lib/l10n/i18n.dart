// DO NOT EDIT. This is code generated via package:gen_lang/generate.dart

import 'dart:async';

import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:multiple_localization/multiple_localization.dart';

import 'messages_all.dart';

class L10nCappRepayment {
  static Map<String, Map<String, String>> translations = {};
  static late Locale _locale;
  static bool showKeys = false;

  String? overridenTranslation(
      String key, String languageCode, String? countryCode) {
    var languageTag = getLanguageTag(languageCode, countryCode);
    var languageTranslations = translations[languageTag];
    if (languageTranslations != null &&
        languageTranslations.containsKey(key) &&
        languageTranslations[key]!.isNotEmpty) {
      return languageTranslations[key]!;
    }

    return null;
  }

  String getLanguageTag(String languageCode, String? countryCode) {
    if (countryCode == null) {
      return languageCode;
    }

    return '$languageCode-$countryCode';
  }

  static const GeneratedLocalizationsDelegate delegate = GeneratedLocalizationsDelegate();

  static L10nCappRepayment of(BuildContext context) {
    return Localizations.of<L10nCappRepayment>(context, L10nCappRepayment)!;
  }
  
  static L10nCappRepayment load(Locale locale) {
    _locale = locale;
    return L10nCappRepayment();
  }
  
  String get continueWord {
    if(showKeys){
      return 'capp_repayment.continue_word';
    }
    var ot = overridenTranslation(
        'capp_repayment.continue_word', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục", name: 'capp_repayment.continue_word');
    return result != '' ? result : 'continue_word';
  }

  String get transactionDetailsSection {
    if(showKeys){
      return 'capp_repayment.transaction_details_section';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details_section', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết giao dịch", name: 'capp_repayment.transaction_details_section');
    return result != '' ? result : 'transaction_details_section';
  }

  String get backToHomepage {
    if(showKeys){
      return 'capp_repayment.back_to_homepage';
    }
    var ot = overridenTranslation(
        'capp_repayment.back_to_homepage', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quay lại Trang chủ", name: 'capp_repayment.back_to_homepage');
    return result != '' ? result : 'back_to_homepage';
  }

  String get transactionFailedTryAgain {
    if(showKeys){
      return 'capp_repayment.transaction_failed_try_again';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_failed_try_again', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thử lại", name: 'capp_repayment.transaction_failed_try_again');
    return result != '' ? result : 'transaction_failed_try_again';
  }

  String get transactionDetailsCopyBtn {
    if(showKeys){
      return 'capp_repayment.transaction_details_copy_btn';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details_copy_btn', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sao chép", name: 'capp_repayment.transaction_details_copy_btn');
    return result != '' ? result : 'transaction_details_copy_btn';
  }

  String get transactionDetailsCopySuccess {
    if(showKeys){
      return 'capp_repayment.transaction_details_copy_success';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details_copy_success', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sao chép thành công!", name: 'capp_repayment.transaction_details_copy_success');
    return result != '' ? result : 'transaction_details_copy_success';
  }

  String get paymentSummaryButtonCancel {
    if(showKeys){
      return 'capp_repayment.payment_summary_button_cancel';
    }
    var ot = overridenTranslation(
        'capp_repayment.payment_summary_button_cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Huỷ", name: 'capp_repayment.payment_summary_button_cancel');
    return result != '' ? result : 'payment_summary_button_cancel';
  }

  String get paymentSummaryButtonConfirm {
    if(showKeys){
      return 'capp_repayment.payment_summary_button_confirm';
    }
    var ot = overridenTranslation(
        'capp_repayment.payment_summary_button_confirm', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác nhận", name: 'capp_repayment.payment_summary_button_confirm');
    return result != '' ? result : 'payment_summary_button_confirm';
  }

  String get transactionDetails {
    if(showKeys){
      return 'capp_repayment.transaction_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết giao dịch", name: 'capp_repayment.transaction_details');
    return result != '' ? result : 'transaction_details';
  }

  String get transactionDetailsDoneBtn {
    if(showKeys){
      return 'capp_repayment.transaction_details_done_btn';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details_done_btn', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xong", name: 'capp_repayment.transaction_details_done_btn');
    return result != '' ? result : 'transaction_details_done_btn';
  }

  String get contractDetailsSection {
    if(showKeys){
      return 'capp_repayment.contract_details_section';
    }
    var ot = overridenTranslation(
        'capp_repayment.contract_details_section', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết Hợp đồng", name: 'capp_repayment.contract_details_section');
    return result != '' ? result : 'contract_details_section';
  }

  String paymentSuccessBodyMessage(dynamic accountEwallet) {
  if(showKeys){
      return 'capp_repayment.payment_success_body_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.payment_success_body_message', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Cảm ơn bạn đã thực hiện thanh toán! Số tiền hoàn trả được xác nhận là sẽ được chuyển từ ${accountEwallet} của bạn sang Home Credit. Tình trạng thanh toán sẽ được cập nhật trong vòng 24 giờ tới. Vui lòng truy cập mục Quản lý khoản vay để kiểm tra tình trạng thanh toán.", name: 'capp_repayment.payment_success_body_message', args: [accountEwallet]);
  }

  String get ewallet {
    if(showKeys){
      return 'capp_repayment.ewallet';
    }
    var ot = overridenTranslation(
        'capp_repayment.ewallet', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("ví điện tử", name: 'capp_repayment.ewallet');
    return result != '' ? result : 'ewallet';
  }

  String get bankAccount {
    if(showKeys){
      return 'capp_repayment.bank_account';
    }
    var ot = overridenTranslation(
        'capp_repayment.bank_account', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("tài khoản ngân hàng", name: 'capp_repayment.bank_account');
    return result != '' ? result : 'bank_account';
  }

  String get paymentSuccessTitleMessage {
    if(showKeys){
      return 'capp_repayment.payment_success_title_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.payment_success_title_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán thành công!", name: 'capp_repayment.payment_success_title_message');
    return result != '' ? result : 'payment_success_title_message';
  }

  String customerName(dynamic name) {
  if(showKeys){
      return 'capp_repayment.customer_name';
    }
    var ot = overridenTranslation(
        'capp_repayment.customer_name', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Tên khách hàng: ${name}", name: 'capp_repayment.customer_name', args: [name]);
  }

  String phoneNumber(dynamic phoneNumber) {
  if(showKeys){
      return 'capp_repayment.phone_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.phone_number', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Số điện thoại: ${phoneNumber}", name: 'capp_repayment.phone_number', args: [phoneNumber]);
  }

  String get paymentDetails {
    if(showKeys){
      return 'capp_repayment.payment_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.payment_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết giao dịch", name: 'capp_repayment.payment_details');
    return result != '' ? result : 'payment_details';
  }

  String get transactionDetailsNo {
    if(showKeys){
      return 'capp_repayment.transaction_details_no';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details_no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số giao dịch", name: 'capp_repayment.transaction_details_no');
    return result != '' ? result : 'transaction_details_no';
  }

  String get transactionDetailsPaymentOption {
    if(showKeys){
      return 'capp_repayment.transaction_details_payment_option';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details_payment_option', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phương thức thanh toán", name: 'capp_repayment.transaction_details_payment_option');
    return result != '' ? result : 'transaction_details_payment_option';
  }

  String get transactionDetailsDateProcessed {
    if(showKeys){
      return 'capp_repayment.transaction_details_date_processed';
    }
    var ot = overridenTranslation(
        'capp_repayment.transaction_details_date_processed', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày xử lý", name: 'capp_repayment.transaction_details_date_processed');
    return result != '' ? result : 'transaction_details_date_processed';
  }

  String get onepayMessageCode1 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_1';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngân hàng phát hành thẻ từ chối cấp phép cho giao dịch. Vui lòng liên hệ ngân hàng theo số điện thoại in ở mặt sau thẻ để biết chính xác nguyên nhân ngân hàng từ chối.", name: 'capp_repayment.onepay_message_code_1');
    return result != '' ? result : 'onepay_message_code_1';
  }

  String get onepayMessageCode3 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_3';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có lỗi kỹ thuật trong quá trình cài đặt cổng thanh toán. Vui lòng liên hệ với OnePAY để được hỗ trợ (Hotline 1900 633 927)", name: 'capp_repayment.onepay_message_code_3');
    return result != '' ? result : 'onepay_message_code_3';
  }

  String get onepayMessageCode4 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_4';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có lỗi kỹ thuật trong quá trình cài đặt cổng thanh toán. Vui lòng liên hệ với OnePAY để được hỗ trợ (Hotline 1900 633 927)", name: 'capp_repayment.onepay_message_code_4');
    return result != '' ? result : 'onepay_message_code_4';
  }

  String get onepayMessageCode5 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_5';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán không hợp lệ. Vui lòng liên hệ với OnePAY để được hỗ trợ (Hotline 1900 633 927)", name: 'capp_repayment.onepay_message_code_5');
    return result != '' ? result : 'onepay_message_code_5';
  }

  String get onepayMessageCode6 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_6';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Loại tiền tệ không hợp lệ. Vui lòng liên hệ với OnePAY để được hỗ trợ (Hotline 1900 633 927)", name: 'capp_repayment.onepay_message_code_6');
    return result != '' ? result : 'onepay_message_code_6';
  }

  String get onepayMessageCode7 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_7';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngân hàng phát hành thẻ từ chối cấp phép cho giao dịch. Vui lòng liên hệ ngân hàng theo số điện thoại in ở mặt sau thẻ để biết chính xác nguyên nhân ngân hàng từ chối.", name: 'capp_repayment.onepay_message_code_7');
    return result != '' ? result : 'onepay_message_code_7';
  }

  String get onepayMessageCode8 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_8';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số thẻ không đúng. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_8');
    return result != '' ? result : 'onepay_message_code_8';
  }

  String get onepayMessageCode9 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_9';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_9', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tên chủ thẻ không đúng. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_9');
    return result != '' ? result : 'onepay_message_code_9';
  }

  String get onepayMessageCode10 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_10';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_10', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ đã hết hạn hoặc Thẻ đã bị khóa. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_10');
    return result != '' ? result : 'onepay_message_code_10';
  }

  String get onepayMessageCode11 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_11';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_11', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ chưa đăng ký sử dụng dịch vụ thanh toán trực tuyến của ngân hàng. Vui lòng liên hệ ngân hàng theo số điện thoại in ở mặt sau thẻ để được hỗ trợ", name: 'capp_repayment.onepay_message_code_11');
    return result != '' ? result : 'onepay_message_code_11';
  }

  String get onepayMessageCode12 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_12';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_12', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày phát hành hoặc Ngày hết hạn đã nhập không đúng. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_12');
    return result != '' ? result : 'onepay_message_code_12';
  }

  String get onepayMessageCode13 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_13';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_13', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán vượt quá hạn mức thanh toán cho phép của một giao dịch thanh toán trực tuyến. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_13');
    return result != '' ? result : 'onepay_message_code_13';
  }

  String get onepayMessageCode21 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_21';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_21', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số dư Tài khoản Ngân hàng không đủ để thực hiện giao dịch. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_21');
    return result != '' ? result : 'onepay_message_code_21';
  }

  String get onepayMessageCode22 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_22';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_22', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin tài khoản không đúng. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_22');
    return result != '' ? result : 'onepay_message_code_22';
  }

  String get onepayMessageCode23 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_23';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_23', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản bị khóa. Vui lòng liên hê ngân hàng theo số điện thoại sau mặt thẻ để được hỗ trợ", name: 'capp_repayment.onepay_message_code_23');
    return result != '' ? result : 'onepay_message_code_23';
  }

  String get onepayMessageCode24 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_24';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_24', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin thẻ không đúng. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_24');
    return result != '' ? result : 'onepay_message_code_24';
  }

  String get onepayMessageCode25 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_25';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_25', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mã OTP không đúng. Vui lòng kiểm tra và thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_25');
    return result != '' ? result : 'onepay_message_code_25';
  }

  String get onepayMessageCode253 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_253';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_253', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quá thời gian thanh toán quy định. Vui lòng thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_253');
    return result != '' ? result : 'onepay_message_code_253';
  }

  String get onepayMessageCode99 {
    if(showKeys){
      return 'capp_repayment.onepay_message_code_99';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_message_code_99', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chủ thẻ đã hủy giao dịch. Vui lòng thực hiện thanh toán lại", name: 'capp_repayment.onepay_message_code_99');
    return result != '' ? result : 'onepay_message_code_99';
  }

  String get onepayConsentTitle {
    if(showKeys){
      return 'capp_repayment.onepay_consent_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_consent_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông báo quan trọng cho OnePAY", name: 'capp_repayment.onepay_consent_title');
    return result != '' ? result : 'onepay_consent_title';
  }

  String get onepayConsentDescription {
    if(showKeys){
      return 'capp_repayment.onepay_consent_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_consent_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn cần:\n\n1. Thẻ ghi nợ \n\n2. Đã đăng ký thành công dịch vụ ngân hàng trực tuyến\n\n3. Số tiền hiện có phải lớn hơn số tiền thanh toán.", name: 'capp_repayment.onepay_consent_description');
    return result != '' ? result : 'onepay_consent_description';
  }

  String get onepayGotItContinue {
    if(showKeys){
      return 'capp_repayment.onepay_got_it_continue';
    }
    var ot = overridenTranslation(
        'capp_repayment.onepay_got_it_continue', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã hiểu. Tiếp tục", name: 'capp_repayment.onepay_got_it_continue');
    return result != '' ? result : 'onepay_got_it_continue';
  }

  String get repaymentContractNo {
    if(showKeys){
      return 'capp_repayment.repayment_contract_no';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số hợp đồng", name: 'capp_repayment.repayment_contract_no');
    return result != '' ? result : 'repayment_contract_no';
  }

  String get repaymentAccountName {
    if(showKeys){
      return 'capp_repayment.repayment_account_name';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_account_name', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tên tài khoản", name: 'capp_repayment.repayment_account_name');
    return result != '' ? result : 'repayment_account_name';
  }

  String get repaymentLoanRepayment {
    if(showKeys){
      return 'capp_repayment.repayment_loan_repayment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_loan_repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán Khoản vay", name: 'capp_repayment.repayment_loan_repayment');
    return result != '' ? result : 'repayment_loan_repayment';
  }

  String get repaymentSelectOnlinePaymentOption {
    if(showKeys){
      return 'capp_repayment.repayment_select_online_payment_option';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_online_payment_option', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn phương thức thanh toán trực tuyến", name: 'capp_repayment.repayment_select_online_payment_option');
    return result != '' ? result : 'repayment_select_online_payment_option';
  }

  String get repaymentPaymentAmount {
    if(showKeys){
      return 'capp_repayment.repayment_payment_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán", name: 'capp_repayment.repayment_payment_amount');
    return result != '' ? result : 'repayment_payment_amount';
  }

  String get repaymentOnlineWithAtmCard {
    if(showKeys){
      return 'capp_repayment.repayment_online_with_atm_card';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_with_atm_card', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ ATM", name: 'capp_repayment.repayment_online_with_atm_card');
    return result != '' ? result : 'repayment_online_with_atm_card';
  }

  String get repaymentOnlineWithPartnerEwallets {
    if(showKeys){
      return 'capp_repayment.repayment_online_with_partner_ewallets';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_with_partner_ewallets', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ví điện tử", name: 'capp_repayment.repayment_online_with_partner_ewallets');
    return result != '' ? result : 'repayment_online_with_partner_ewallets';
  }

  String get repaymentContinueToPaymentSummary {
    if(showKeys){
      return 'capp_repayment.repayment_continue_to_payment_summary';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_continue_to_payment_summary', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem tóm tắt thông tin thanh toán", name: 'capp_repayment.repayment_continue_to_payment_summary');
    return result != '' ? result : 'repayment_continue_to_payment_summary';
  }

  String get repaymentPaymentSummary {
    if(showKeys){
      return 'capp_repayment.repayment_payment_summary';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_summary', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tóm tắt thông tin thanh toán", name: 'capp_repayment.repayment_payment_summary');
    return result != '' ? result : 'repayment_payment_summary';
  }

  String get repaymentPleaseReviewYourInformation {
    if(showKeys){
      return 'capp_repayment.repayment_please_review_your_information';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_review_your_information', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng kiểm tra lại thông tin", name: 'capp_repayment.repayment_please_review_your_information');
    return result != '' ? result : 'repayment_please_review_your_information';
  }

  String get repaymentPaymentInformation {
    if(showKeys){
      return 'capp_repayment.repayment_payment_information';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_information', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin thanh toán", name: 'capp_repayment.repayment_payment_information');
    return result != '' ? result : 'repayment_payment_information';
  }

  String get repaymentContractNumber {
    if(showKeys){
      return 'capp_repayment.repayment_contract_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số hợp đồng", name: 'capp_repayment.repayment_contract_number');
    return result != '' ? result : 'repayment_contract_number';
  }

  String get repaymentFullName {
    if(showKeys){
      return 'capp_repayment.repayment_full_name';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_full_name', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Họ và tên", name: 'capp_repayment.repayment_full_name');
    return result != '' ? result : 'repayment_full_name';
  }

  String get repaymentPhoneNumber {
    if(showKeys){
      return 'capp_repayment.repayment_phone_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_phone_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số điện thoại", name: 'capp_repayment.repayment_phone_number');
    return result != '' ? result : 'repayment_phone_number';
  }

  String get repaymentPaymentOption {
    if(showKeys){
      return 'capp_repayment.repayment_payment_option';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_option', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phương thức thanh toán", name: 'capp_repayment.repayment_payment_option');
    return result != '' ? result : 'repayment_payment_option';
  }

  String get repaymentTotalAmount {
    if(showKeys){
      return 'capp_repayment.repayment_total_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_total_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền", name: 'capp_repayment.repayment_total_amount');
    return result != '' ? result : 'repayment_total_amount';
  }

  String get repaymentConfirmPayment {
    if(showKeys){
      return 'capp_repayment.repayment_confirm_payment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_confirm_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác nhận thanh toán", name: 'capp_repayment.repayment_confirm_payment');
    return result != '' ? result : 'repayment_confirm_payment';
  }

  String get repaymentGatewayIsOpening {
    if(showKeys){
      return 'capp_repayment.repayment_gateway_is_opening';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_gateway_is_opening', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kết nối cổng thanh toán", name: 'capp_repayment.repayment_gateway_is_opening');
    return result != '' ? result : 'repayment_gateway_is_opening';
  }

  String get repaymentMethodSelectionContinue {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_continue';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_continue', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục", name: 'capp_repayment.repayment_method_selection_continue');
    return result != '' ? result : 'repayment_method_selection_continue';
  }

  String get repaymentMethodSelectionTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phương thức thanh toán", name: 'capp_repayment.repayment_method_selection_title');
    return result != '' ? result : 'repayment_method_selection_title';
  }

  String get repaymentMethodSelectionChoosePaymentMethod {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_choose_payment_method';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_choose_payment_method', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn phương thức thanh toán", name: 'capp_repayment.repayment_method_selection_choose_payment_method');
    return result != '' ? result : 'repayment_method_selection_choose_payment_method';
  }

  String get repaymentMethodSelectionPayByCashTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_by_cash_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_by_cash_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán bằng tiền mặt", name: 'capp_repayment.repayment_method_selection_pay_by_cash_title');
    return result != '' ? result : 'repayment_method_selection_pay_by_cash_title';
  }

  String get repaymentMethodSelectionPayByCashSubTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_by_cash_subTitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_by_cash_subTitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đến quầy dịch vụ của Thế Giới Di Động, cửa hàng FPT, ...", name: 'capp_repayment.repayment_method_selection_pay_by_cash_subTitle');
    return result != '' ? result : 'repayment_method_selection_pay_by_cash_subTitle';
  }

  String get repaymentMethodSelectionPayViaBankTransferTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_via_bank_transfer_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_via_bank_transfer_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản ngân hàng", name: 'capp_repayment.repayment_method_selection_pay_via_bank_transfer_title');
    return result != '' ? result : 'repayment_method_selection_pay_via_bank_transfer_title';
  }

  String get repaymentMethodSelectionPayViaBankTransferSubTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_via_bank_transfer_subTitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_via_bank_transfer_subTitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sử dụng dịch vụ trực tuyến của ngân hàng", name: 'capp_repayment.repayment_method_selection_pay_via_bank_transfer_subTitle');
    return result != '' ? result : 'repayment_method_selection_pay_via_bank_transfer_subTitle';
  }

  String get repayment {
    if(showKeys){
      return 'capp_repayment.repayment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("thanh toán khoản vay", name: 'capp_repayment.repayment');
    return result != '' ? result : 'repayment';
  }

  String get repaymentPayForMyLoans {
    if(showKeys){
      return 'capp_repayment.repayment_pay_for_my_loans';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_for_my_loans', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán cho các hợp đồng của tôi", name: 'capp_repayment.repayment_pay_for_my_loans');
    return result != '' ? result : 'repayment_pay_for_my_loans';
  }

  String get repaymentLoan {
    if(showKeys){
      return 'capp_repayment.repayment_loan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("khoản vay", name: 'capp_repayment.repayment_loan');
    return result != '' ? result : 'repayment_loan';
  }

  String get repaymentLoans {
    if(showKeys){
      return 'capp_repayment.repayment_loans';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_loans', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("khoản vay", name: 'capp_repayment.repayment_loans');
    return result != '' ? result : 'repayment_loans';
  }

  String get repaymentNeedRepayment {
    if(showKeys){
      return 'capp_repayment.repayment_need_repayment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_need_repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("cần được thanh toán", name: 'capp_repayment.repayment_need_repayment');
    return result != '' ? result : 'repayment_need_repayment';
  }

  String get repaymentAccountNo {
    if(showKeys){
      return 'capp_repayment.repayment_account_no';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_account_no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tài khoản", name: 'capp_repayment.repayment_account_no');
    return result != '' ? result : 'repayment_account_no';
  }

  String get repaymentDueDate {
    if(showKeys){
      return 'capp_repayment.repayment_due_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_due_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày đến hạn", name: 'capp_repayment.repayment_due_date');
    return result != '' ? result : 'repayment_due_date';
  }

  String get repaymentDueAmount {
    if(showKeys){
      return 'capp_repayment.repayment_due_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_due_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền đến hạn", name: 'capp_repayment.repayment_due_amount');
    return result != '' ? result : 'repayment_due_amount';
  }

  String get repaymentSelectAmountChooseToPay {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_choose_to_pay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_choose_to_pay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn số tiền thanh toán", name: 'capp_repayment.repayment_select_amount_choose_to_pay');
    return result != '' ? result : 'repayment_select_amount_choose_to_pay';
  }

  String get repaymentSelectAmountEnterCustomAmount {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_enter_custom_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_enter_custom_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhập số tiền muốn thanh toán", name: 'capp_repayment.repayment_select_amount_enter_custom_amount');
    return result != '' ? result : 'repayment_select_amount_enter_custom_amount';
  }

  String get repaymentSelectAmountCustomAmount {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_custom_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_custom_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tùy chỉnh số tiền", name: 'capp_repayment.repayment_select_amount_custom_amount');
    return result != '' ? result : 'repayment_select_amount_custom_amount';
  }

  String get repaymentSelectAmountMinAmountValidationMes {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_min_amount_validation_mes';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_min_amount_validation_mes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền phải lớn hơn hoặc bằng", name: 'capp_repayment.repayment_select_amount_min_amount_validation_mes');
    return result != '' ? result : 'repayment_select_amount_min_amount_validation_mes';
  }

  String get repaymentSelectAmountOverTotalDebtValidationMes {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_over_total_debt_validation_mes';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_over_total_debt_validation_mes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền không được lớn hơn", name: 'capp_repayment.repayment_select_amount_over_total_debt_validation_mes');
    return result != '' ? result : 'repayment_select_amount_over_total_debt_validation_mes';
  }

  String get repaymentSelectAmountContinuePaymentOptions {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_continue_payment_options';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_continue_payment_options', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục chọn phương thức thanh toán", name: 'capp_repayment.repayment_select_amount_continue_payment_options');
    return result != '' ? result : 'repayment_select_amount_continue_payment_options';
  }

  String get repaymentSelectAmountDueAmountValidationMesHeading {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_due_amount_validation_mes_heading';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_due_amount_validation_mes_heading', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_select_amount_due_amount_validation_mes_heading');
    return result != '' ? result : 'repayment_select_amount_due_amount_validation_mes_heading';
  }

  String get repaymentSelectAmountDueAmountValidationMes {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_due_amount_validation_mes';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_due_amount_validation_mes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng thanh toán số tiền còn lại trước ngày đến hạn để tránh phát sinh phí phạt và ảnh hưởng lịch sử tín dụng", name: 'capp_repayment.repayment_select_amount_due_amount_validation_mes');
    return result != '' ? result : 'repayment_select_amount_due_amount_validation_mes';
  }

  String get repaymentSelectAmountEdit {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_edit';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_edit', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thay đổi số tiền", name: 'capp_repayment.repayment_select_amount_edit');
    return result != '' ? result : 'repayment_select_amount_edit';
  }

  String get repaymentMinimumDueAmount {
    if(showKeys){
      return 'capp_repayment.repayment_minimum_due_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_minimum_due_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền tối thiểu đến hạn", name: 'capp_repayment.repayment_minimum_due_amount');
    return result != '' ? result : 'repayment_minimum_due_amount';
  }

  String get repaymentTotalDueAmount {
    if(showKeys){
      return 'capp_repayment.repayment_total_due_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_total_due_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền đến hạn", name: 'capp_repayment.repayment_total_due_amount');
    return result != '' ? result : 'repayment_total_due_amount';
  }

  String get repaymentMethodSelectionPayViaAutoDebitArrangementTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_via_auto_debit_arrangement_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_via_auto_debit_arrangement_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_method_selection_pay_via_auto_debit_arrangement_title');
    return result != '' ? result : 'repayment_method_selection_pay_via_auto_debit_arrangement_title';
  }

  String get repaymentMethodSelectionPayViaAutoDebitArrangementSubTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_via_auto_debit_arrangement_sub_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_via_auto_debit_arrangement_sub_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_method_selection_pay_via_auto_debit_arrangement_sub_title');
    return result != '' ? result : 'repayment_method_selection_pay_via_auto_debit_arrangement_sub_title';
  }

  String get repaymentMethodSelectionPayInAppTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_in_app_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_in_app_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_method_selection_pay_in_app_title');
    return result != '' ? result : 'repayment_method_selection_pay_in_app_title';
  }

  String get repaymentMethodSelectionPayInAppSubTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_in_app_sub_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_in_app_sub_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_method_selection_pay_in_app_sub_title');
    return result != '' ? result : 'repayment_method_selection_pay_in_app_sub_title';
  }

  String get repaymentMethodSelectionPayOverTheCounterTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_over_the_counter_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_over_the_counter_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_method_selection_pay_over_the_counter_title');
    return result != '' ? result : 'repayment_method_selection_pay_over_the_counter_title';
  }

  String get repaymentMethodSelectionPayOverTheCounterSubTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_pay_over_the_counter_sub_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_pay_over_the_counter_sub_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_method_selection_pay_over_the_counter_sub_title');
    return result != '' ? result : 'repayment_method_selection_pay_over_the_counter_sub_title';
  }

  String get repaymentSelectAmountRelMinimumAmountValidationMes {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_rel_minimum_amount_validation_mes';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_rel_minimum_amount_validation_mes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền của bạn ít hơn số tiền tối thiểu đến hạn. Bạn có thể bị tính phí phạt chậm thanh toán", name: 'capp_repayment.repayment_select_amount_rel_minimum_amount_validation_mes');
    return result != '' ? result : 'repayment_select_amount_rel_minimum_amount_validation_mes';
  }

  String repaymentSelectAmountRelMinimumAmountError(dynamic minimumThresholdAmount) {
  if(showKeys){
      return 'capp_repayment.repayment_select_amount_rel_minimum_amount_error';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_rel_minimum_amount_error', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Số tiền tối thiểu để thực hiện thanh toán qua ứng dụng là ${minimumThresholdAmount}đồng, do đó, bạn vui lòng thực hiện thanh toán số tiền này qua hình thức chuyển khoản ngân hàng cho Home Credit nhé.", name: 'capp_repayment.repayment_select_amount_rel_minimum_amount_error', args: [minimumThresholdAmount]);
  }

  String get repaymentThereIsNoAvailableLoan {
    if(showKeys){
      return 'capp_repayment.repayment_there_is_no_available_loan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_there_is_no_available_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không còn hợp đồng nào cần thanh toán", name: 'capp_repayment.repayment_there_is_no_available_loan');
    return result != '' ? result : 'repayment_there_is_no_available_loan';
  }

  String get repaymentTransactionFailedSomethingWentWrong {
    if(showKeys){
      return 'capp_repayment.repayment_transaction_failed_something_went_wrong';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_transaction_failed_something_went_wrong', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã có lỗi xảy ra", name: 'capp_repayment.repayment_transaction_failed_something_went_wrong');
    return result != '' ? result : 'repayment_transaction_failed_something_went_wrong';
  }

  String get repaymentTransactionFailedGenericMessage {
    if(showKeys){
      return 'capp_repayment.repayment_transaction_failed_generic_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_transaction_failed_generic_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng thử lại hoặc truy cập Trang chủ để khám phá ưu đãi dành cho bạn và trải nghiệm các dịch vụ khác!", name: 'capp_repayment.repayment_transaction_failed_generic_message');
    return result != '' ? result : 'repayment_transaction_failed_generic_message';
  }

  String get repaymentDebitCards {
    if(showKeys){
      return 'capp_repayment.repayment_debit_cards';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_debit_cards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ ghi nợ", name: 'capp_repayment.repayment_debit_cards');
    return result != '' ? result : 'repayment_debit_cards';
  }

  String get repaymentPayWithYourDebitCards {
    if(showKeys){
      return 'capp_repayment.repayment_pay_with_your_debit_cards';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_with_your_debit_cards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán bằng Thẻ ghi nợ của bạn (Visa hoặc Mastercard)", name: 'capp_repayment.repayment_pay_with_your_debit_cards');
    return result != '' ? result : 'repayment_pay_with_your_debit_cards';
  }

  String get repaymentCardNumber {
    if(showKeys){
      return 'capp_repayment.repayment_card_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_card_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số thẻ", name: 'capp_repayment.repayment_card_number');
    return result != '' ? result : 'repayment_card_number';
  }

  String get repaymentYouDontHaveAnyCardSaved {
    if(showKeys){
      return 'capp_repayment.repayment_you_dont_have_any_card_saved';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_you_dont_have_any_card_saved', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_you_dont_have_any_card_saved');
    return result != '' ? result : 'repayment_you_dont_have_any_card_saved';
  }

  String get repaymentCardEnrollment {
    if(showKeys){
      return 'capp_repayment.repayment_card_enrollment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_card_enrollment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_card_enrollment');
    return result != '' ? result : 'repayment_card_enrollment';
  }

  String get repaymentYourCardProviderWillTemporarily {
    if(showKeys){
      return 'capp_repayment.repayment_your_card_provider_will_temporarily';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_your_card_provider_will_temporarily', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_your_card_provider_will_temporarily');
    return result != '' ? result : 'repayment_your_card_provider_will_temporarily';
  }

  String get repaymentBankTransfer {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản ngân hàng", name: 'capp_repayment.repayment_bank_transfer');
    return result != '' ? result : 'repayment_bank_transfer';
  }

  String get repaymentInAppPayment {
    if(showKeys){
      return 'capp_repayment.repayment_in_app_payment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_in_app_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_in_app_payment');
    return result != '' ? result : 'repayment_in_app_payment';
  }

  String get repaymentEmoneyAndEcommerce {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_and_ecommerce';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_and_ecommerce', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_and_ecommerce');
    return result != '' ? result : 'repayment_emoney_and_ecommerce';
  }

  String get repaymentRetail {
    if(showKeys){
      return 'capp_repayment.repayment_retail';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_retail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_retail');
    return result != '' ? result : 'repayment_retail';
  }

  String get repaymentContractInformation {
    if(showKeys){
      return 'capp_repayment.repayment_contract_information';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_information', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin Hợp đồng", name: 'capp_repayment.repayment_contract_information');
    return result != '' ? result : 'repayment_contract_information';
  }

  String get repaymentBankTransferInformation {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_information';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_information', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin Chuyển khoản Ngân hàng", name: 'capp_repayment.repayment_bank_transfer_information');
    return result != '' ? result : 'repayment_bank_transfer_information';
  }

  String get repaymentBankTransferInformationTitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_information_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_information_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin\nChuyển khoản Ngân hàng", name: 'capp_repayment.repayment_bank_transfer_information_title');
    return result != '' ? result : 'repayment_bank_transfer_information_title';
  }

  String get repaymentBankName {
    if(showKeys){
      return 'capp_repayment.repayment_bank_name';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_name', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tên ngân hàng", name: 'capp_repayment.repayment_bank_name');
    return result != '' ? result : 'repayment_bank_name';
  }

  String get repaymentAccountNumber {
    if(showKeys){
      return 'capp_repayment.repayment_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tài khoản", name: 'capp_repayment.repayment_account_number');
    return result != '' ? result : 'repayment_account_number';
  }

  String get repaymentTransferContent {
    if(showKeys){
      return 'capp_repayment.repayment_transfer_content';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_transfer_content', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nội dung chuyển khoản", name: 'capp_repayment.repayment_transfer_content');
    return result != '' ? result : 'repayment_transfer_content';
  }

  String get repaymentForContractNumber {
    if(showKeys){
      return 'capp_repayment.repayment_for_contract_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_for_contract_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán cho số\nhợp đồng", name: 'capp_repayment.repayment_for_contract_number');
    return result != '' ? result : 'repayment_for_contract_number';
  }

  String get repaymentGuidance {
    if(showKeys){
      return 'capp_repayment.repayment_guidance';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_guidance', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hướng dẫn thanh toán", name: 'capp_repayment.repayment_guidance');
    return result != '' ? result : 'repayment_guidance';
  }

  String get repaymentBcaAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_atm_step1');
    return result != '' ? result : 'repayment_bca_atm_step1';
  }

  String get repaymentBcaAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_atm_step2');
    return result != '' ? result : 'repayment_bca_atm_step2';
  }

  String get repaymentBcaAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_atm_step3');
    return result != '' ? result : 'repayment_bca_atm_step3';
  }

  String get repaymentBcaAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_atm_step4');
    return result != '' ? result : 'repayment_bca_atm_step4';
  }

  String get repaymentBcaAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_atm_step5');
    return result != '' ? result : 'repayment_bca_atm_step5';
  }

  String get repaymentBcaAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_atm_step6');
    return result != '' ? result : 'repayment_bca_atm_step6';
  }

  String get repaymentBcaAtmStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_atm_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_atm_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_atm_step7');
    return result != '' ? result : 'repayment_bca_atm_step7';
  }

  String get repaymentBcaMobileStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_mobile_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_mobile_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_mobile_step1');
    return result != '' ? result : 'repayment_bca_mobile_step1';
  }

  String get repaymentBcaMobileStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_mobile_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_mobile_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_mobile_step2');
    return result != '' ? result : 'repayment_bca_mobile_step2';
  }

  String get repaymentBcaMobileStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_mobile_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_mobile_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_mobile_step3');
    return result != '' ? result : 'repayment_bca_mobile_step3';
  }

  String get repaymentBcaMobileStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_mobile_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_mobile_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_mobile_step4');
    return result != '' ? result : 'repayment_bca_mobile_step4';
  }

  String get repaymentBcaMobileStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_mobile_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_mobile_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_mobile_step5');
    return result != '' ? result : 'repayment_bca_mobile_step5';
  }

  String get repaymentBcaMobileStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_bca_mobile_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bca_mobile_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bca_mobile_step6');
    return result != '' ? result : 'repayment_bca_mobile_step6';
  }

  String get repaymentBniAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step1');
    return result != '' ? result : 'repayment_bni_atm_step1';
  }

  String get repaymentBniAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step2');
    return result != '' ? result : 'repayment_bni_atm_step2';
  }

  String get repaymentBniAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step3');
    return result != '' ? result : 'repayment_bni_atm_step3';
  }

  String get repaymentBniAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step4');
    return result != '' ? result : 'repayment_bni_atm_step4';
  }

  String get repaymentBniAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step5');
    return result != '' ? result : 'repayment_bni_atm_step5';
  }

  String get repaymentBniAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step6');
    return result != '' ? result : 'repayment_bni_atm_step6';
  }

  String get repaymentBniAtmStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step7');
    return result != '' ? result : 'repayment_bni_atm_step7';
  }

  String get repaymentBniAtmStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step8');
    return result != '' ? result : 'repayment_bni_atm_step8';
  }

  String get repaymentBniAtmStep9 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step9';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step9', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step9');
    return result != '' ? result : 'repayment_bni_atm_step9';
  }

  String get repaymentBniAtmStep10 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step10';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step10', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step10');
    return result != '' ? result : 'repayment_bni_atm_step10';
  }

  String get repaymentBniAtmStep11 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_atm_step11';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_atm_step11', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_atm_step11');
    return result != '' ? result : 'repayment_bni_atm_step11';
  }

  String get repaymentBniMobileStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_mobile_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_mobile_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_mobile_step1');
    return result != '' ? result : 'repayment_bni_mobile_step1';
  }

  String get repaymentBniMobileStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_mobile_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_mobile_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_mobile_step2');
    return result != '' ? result : 'repayment_bni_mobile_step2';
  }

  String get repaymentBniMobileStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_mobile_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_mobile_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_mobile_step3');
    return result != '' ? result : 'repayment_bni_mobile_step3';
  }

  String get repaymentBniMobileStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_mobile_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_mobile_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_mobile_step4');
    return result != '' ? result : 'repayment_bni_mobile_step4';
  }

  String get repaymentBniMobileStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_mobile_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_mobile_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_mobile_step5');
    return result != '' ? result : 'repayment_bni_mobile_step5';
  }

  String get repaymentBniMobileStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_mobile_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_mobile_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_mobile_step6');
    return result != '' ? result : 'repayment_bni_mobile_step6';
  }

  String get repaymentBniMobileStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_bni_mobile_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bni_mobile_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bni_mobile_step7');
    return result != '' ? result : 'repayment_bni_mobile_step7';
  }

  String get repaymentMandiriAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_atm_step1');
    return result != '' ? result : 'repayment_mandiri_atm_step1';
  }

  String get repaymentMandiriAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_atm_step2');
    return result != '' ? result : 'repayment_mandiri_atm_step2';
  }

  String get repaymentMandiriAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_atm_step3');
    return result != '' ? result : 'repayment_mandiri_atm_step3';
  }

  String get repaymentMandiriAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_atm_step4');
    return result != '' ? result : 'repayment_mandiri_atm_step4';
  }

  String get repaymentMandiriAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_atm_step5');
    return result != '' ? result : 'repayment_mandiri_atm_step5';
  }

  String get repaymentMandiriAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_atm_step6');
    return result != '' ? result : 'repayment_mandiri_atm_step6';
  }

  String get repaymentMandiriMobileStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_mobile_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_mobile_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_mobile_step1');
    return result != '' ? result : 'repayment_mandiri_mobile_step1';
  }

  String get repaymentMandiriMobileStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_mobile_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_mobile_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_mobile_step2');
    return result != '' ? result : 'repayment_mandiri_mobile_step2';
  }

  String get repaymentMandiriMobileStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_mobile_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_mobile_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_mobile_step3');
    return result != '' ? result : 'repayment_mandiri_mobile_step3';
  }

  String get repaymentMandiriMobileStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_mobile_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_mobile_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_mobile_step4');
    return result != '' ? result : 'repayment_mandiri_mobile_step4';
  }

  String get repaymentMandiriMobileStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_mobile_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_mobile_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_mobile_step5');
    return result != '' ? result : 'repayment_mandiri_mobile_step5';
  }

  String get repaymentMandiriMobileStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_mobile_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_mobile_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_mobile_step6');
    return result != '' ? result : 'repayment_mandiri_mobile_step6';
  }

  String get repaymentMandiriMobileStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_mandiri_mobile_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mandiri_mobile_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_mandiri_mobile_step7');
    return result != '' ? result : 'repayment_mandiri_mobile_step7';
  }

  String get repaymentBriAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step1');
    return result != '' ? result : 'repayment_bri_atm_step1';
  }

  String get repaymentBriAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step2');
    return result != '' ? result : 'repayment_bri_atm_step2';
  }

  String get repaymentBriAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step3');
    return result != '' ? result : 'repayment_bri_atm_step3';
  }

  String get repaymentBriAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step4');
    return result != '' ? result : 'repayment_bri_atm_step4';
  }

  String get repaymentBriAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step5');
    return result != '' ? result : 'repayment_bri_atm_step5';
  }

  String get repaymentBriAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step6');
    return result != '' ? result : 'repayment_bri_atm_step6';
  }

  String get repaymentBriAtmStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step7');
    return result != '' ? result : 'repayment_bri_atm_step7';
  }

  String get repaymentBriAtmStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_atm_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_atm_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_atm_step8');
    return result != '' ? result : 'repayment_bri_atm_step8';
  }

  String get repaymentBriMobileStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_mobile_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_mobile_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_mobile_step1');
    return result != '' ? result : 'repayment_bri_mobile_step1';
  }

  String get repaymentBriMobileStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_mobile_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_mobile_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_mobile_step2');
    return result != '' ? result : 'repayment_bri_mobile_step2';
  }

  String get repaymentBriMobileStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_mobile_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_mobile_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_mobile_step3');
    return result != '' ? result : 'repayment_bri_mobile_step3';
  }

  String get repaymentBriMobileStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_mobile_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_mobile_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_mobile_step4');
    return result != '' ? result : 'repayment_bri_mobile_step4';
  }

  String get repaymentBriMobileStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_mobile_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_mobile_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_mobile_step5');
    return result != '' ? result : 'repayment_bri_mobile_step5';
  }

  String get repaymentBriMobileStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_bri_mobile_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bri_mobile_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bri_mobile_step6');
    return result != '' ? result : 'repayment_bri_mobile_step6';
  }

  String get repaymentPermataAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step1');
    return result != '' ? result : 'repayment_permata_atm_step1';
  }

  String get repaymentPermataAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step2');
    return result != '' ? result : 'repayment_permata_atm_step2';
  }

  String get repaymentPermataAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step3');
    return result != '' ? result : 'repayment_permata_atm_step3';
  }

  String get repaymentPermataAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step4');
    return result != '' ? result : 'repayment_permata_atm_step4';
  }

  String get repaymentPermataAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step5');
    return result != '' ? result : 'repayment_permata_atm_step5';
  }

  String get repaymentPermataAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step6');
    return result != '' ? result : 'repayment_permata_atm_step6';
  }

  String get repaymentPermataAtmStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step7');
    return result != '' ? result : 'repayment_permata_atm_step7';
  }

  String get repaymentPermataAtmStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_atm_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_atm_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_atm_step8');
    return result != '' ? result : 'repayment_permata_atm_step8';
  }

  String get repaymentPermataMobileStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_mobile_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_mobile_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_mobile_step1');
    return result != '' ? result : 'repayment_permata_mobile_step1';
  }

  String get repaymentPermataMobileStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_mobile_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_mobile_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_mobile_step2');
    return result != '' ? result : 'repayment_permata_mobile_step2';
  }

  String get repaymentPermataMobileStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_mobile_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_mobile_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_mobile_step3');
    return result != '' ? result : 'repayment_permata_mobile_step3';
  }

  String get repaymentPermataMobileStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_mobile_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_mobile_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_mobile_step4');
    return result != '' ? result : 'repayment_permata_mobile_step4';
  }

  String get repaymentPermataMobileStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_mobile_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_mobile_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_mobile_step5');
    return result != '' ? result : 'repayment_permata_mobile_step5';
  }

  String get repaymentPermataMobileStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_permata_mobile_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_permata_mobile_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_permata_mobile_step6');
    return result != '' ? result : 'repayment_permata_mobile_step6';
  }

  String get repaymentBtpnAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step1');
    return result != '' ? result : 'repayment_btpn_atm_step1';
  }

  String get repaymentBtpnAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step2');
    return result != '' ? result : 'repayment_btpn_atm_step2';
  }

  String get repaymentBtpnAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step3');
    return result != '' ? result : 'repayment_btpn_atm_step3';
  }

  String get repaymentBtpnAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step4');
    return result != '' ? result : 'repayment_btpn_atm_step4';
  }

  String get repaymentBtpnAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step5');
    return result != '' ? result : 'repayment_btpn_atm_step5';
  }

  String get repaymentBtpnAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step6');
    return result != '' ? result : 'repayment_btpn_atm_step6';
  }

  String get repaymentBtpnAtmStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step7');
    return result != '' ? result : 'repayment_btpn_atm_step7';
  }

  String get repaymentBtpnAtmStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_atm_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_atm_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_atm_step8');
    return result != '' ? result : 'repayment_btpn_atm_step8';
  }

  String get repaymentBtpnMobileStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_mobile_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_mobile_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_mobile_step1');
    return result != '' ? result : 'repayment_btpn_mobile_step1';
  }

  String get repaymentBtpnMobileStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_mobile_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_mobile_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_mobile_step2');
    return result != '' ? result : 'repayment_btpn_mobile_step2';
  }

  String get repaymentBtpnMobileStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_mobile_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_mobile_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_mobile_step3');
    return result != '' ? result : 'repayment_btpn_mobile_step3';
  }

  String get repaymentBtpnMobileStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_mobile_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_mobile_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_mobile_step4');
    return result != '' ? result : 'repayment_btpn_mobile_step4';
  }

  String get repaymentBtpnMobileStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_mobile_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_mobile_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_mobile_step5');
    return result != '' ? result : 'repayment_btpn_mobile_step5';
  }

  String get repaymentBtpnMobileStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_mobile_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_mobile_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_mobile_step6');
    return result != '' ? result : 'repayment_btpn_mobile_step6';
  }

  String get repaymentBtpnMobileStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_btpn_mobile_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_btpn_mobile_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_btpn_mobile_step7');
    return result != '' ? result : 'repayment_btpn_mobile_step7';
  }

  String get repaymentPaAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_pa_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pa_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pa_atm_step1');
    return result != '' ? result : 'repayment_pa_atm_step1';
  }

  String get repaymentPaAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_pa_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pa_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pa_atm_step2');
    return result != '' ? result : 'repayment_pa_atm_step2';
  }

  String get repaymentPaAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_pa_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pa_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pa_atm_step3');
    return result != '' ? result : 'repayment_pa_atm_step3';
  }

  String get repaymentPaAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_pa_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pa_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pa_atm_step4');
    return result != '' ? result : 'repayment_pa_atm_step4';
  }

  String get repaymentPaAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_pa_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pa_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pa_atm_step5');
    return result != '' ? result : 'repayment_pa_atm_step5';
  }

  String get repaymentPaAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_pa_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pa_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pa_atm_step6');
    return result != '' ? result : 'repayment_pa_atm_step6';
  }

  String get repaymentPaAtmStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_pa_atm_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pa_atm_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pa_atm_step7');
    return result != '' ? result : 'repayment_pa_atm_step7';
  }

  String get repaymentBersamaAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step1');
    return result != '' ? result : 'repayment_bersama_atm_step1';
  }

  String get repaymentBersamaAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step2');
    return result != '' ? result : 'repayment_bersama_atm_step2';
  }

  String get repaymentBersamaAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step3');
    return result != '' ? result : 'repayment_bersama_atm_step3';
  }

  String get repaymentBersamaAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step4');
    return result != '' ? result : 'repayment_bersama_atm_step4';
  }

  String get repaymentBersamaAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step5');
    return result != '' ? result : 'repayment_bersama_atm_step5';
  }

  String get repaymentBersamaAtmStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step6');
    return result != '' ? result : 'repayment_bersama_atm_step6';
  }

  String get repaymentBersamaAtmStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step7');
    return result != '' ? result : 'repayment_bersama_atm_step7';
  }

  String get repaymentBersamaAtmStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_bersama_atm_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bersama_atm_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bersama_atm_step8');
    return result != '' ? result : 'repayment_bersama_atm_step8';
  }

  String get repaymentAtm {
    if(showKeys){
      return 'capp_repayment.repayment_atm';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_atm', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("ATM", name: 'capp_repayment.repayment_atm');
    return result != '' ? result : 'repayment_atm';
  }

  String get repaymentBank {
    if(showKeys){
      return 'capp_repayment.repayment_bank';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngân hàng", name: 'capp_repayment.repayment_bank');
    return result != '' ? result : 'repayment_bank';
  }

  String get repaymentMobileBanking {
    if(showKeys){
      return 'capp_repayment.repayment_mobile_banking';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mobile_banking', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngân hàng trực tuyến", name: 'capp_repayment.repayment_mobile_banking');
    return result != '' ? result : 'repayment_mobile_banking';
  }

  String get repaymentBankTransferSelectOption {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_select_option';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_select_option', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bank_transfer_select_option');
    return result != '' ? result : 'repayment_bank_transfer_select_option';
  }

  String get repaymentBankTransferVirtualAccount {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_virtual_account';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_virtual_account', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_bank_transfer_virtual_account');
    return result != '' ? result : 'repayment_bank_transfer_virtual_account';
  }

  String get repaymentFee {
    if(showKeys){
      return 'capp_repayment.repayment_fee';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_fee', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phí", name: 'capp_repayment.repayment_fee');
    return result != '' ? result : 'repayment_fee';
  }

  String get repaymentAddCard {
    if(showKeys){
      return 'capp_repayment.repayment_add_card';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_add_card', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thêm thẻ", name: 'capp_repayment.repayment_add_card');
    return result != '' ? result : 'repayment_add_card';
  }

  String get repaymentVnpayAppBackAlert {
    if(showKeys){
      return 'capp_repayment.repayment_vnpay_app_back_alert';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_vnpay_app_back_alert', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có chắc muốn quay lại không?", name: 'capp_repayment.repayment_vnpay_app_back_alert');
    return result != '' ? result : 'repayment_vnpay_app_back_alert';
  }

  String get shopeepayErrorMessageCode201 {
    if(showKeys){
      return 'capp_repayment.shopeepay_error_message_code_201';
    }
    var ot = overridenTranslation(
        'capp_repayment.shopeepay_error_message_code_201', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán đã bị huỷ", name: 'capp_repayment.shopeepay_error_message_code_201');
    return result != '' ? result : 'shopeepay_error_message_code_201';
  }

  String get shopeepayErrorMessageCode202 {
    if(showKeys){
      return 'capp_repayment.shopeepay_error_message_code_202';
    }
    var ot = overridenTranslation(
        'capp_repayment.shopeepay_error_message_code_202', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán thất bại", name: 'capp_repayment.shopeepay_error_message_code_202');
    return result != '' ? result : 'shopeepay_error_message_code_202';
  }

  String get shopeepayErrorMessageCode203 {
    if(showKeys){
      return 'capp_repayment.shopeepay_error_message_code_203';
    }
    var ot = overridenTranslation(
        'capp_repayment.shopeepay_error_message_code_203', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán đang được xử lý", name: 'capp_repayment.shopeepay_error_message_code_203');
    return result != '' ? result : 'shopeepay_error_message_code_203';
  }

  String get shopeepayErrorMessageCode204 {
    if(showKeys){
      return 'capp_repayment.shopeepay_error_message_code_204';
    }
    var ot = overridenTranslation(
        'capp_repayment.shopeepay_error_message_code_204', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã quá thời hạn giao dịch", name: 'capp_repayment.shopeepay_error_message_code_204');
    return result != '' ? result : 'shopeepay_error_message_code_204';
  }

  String get repaymentProcessing {
    if(showKeys){
      return 'capp_repayment.repayment_processing';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_processing', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đang xử lý...", name: 'capp_repayment.repayment_processing');
    return result != '' ? result : 'repayment_processing';
  }

  String get repaymentYouWillBeNoticedWhen {
    if(showKeys){
      return 'capp_repayment.repayment_you_will_be_noticed_when';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_you_will_be_noticed_when', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn sẽ nhận được thông báo ngay khi yêu cầu hoàn tất", name: 'capp_repayment.repayment_you_will_be_noticed_when');
    return result != '' ? result : 'repayment_you_will_be_noticed_when';
  }

  String get repaymentTimeout {
    if(showKeys){
      return 'capp_repayment.repayment_timeout';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_timeout', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hết thời gian cho phép", name: 'capp_repayment.repayment_timeout');
    return result != '' ? result : 'repayment_timeout';
  }

  String get repaymentPleaseTryAgain {
    if(showKeys){
      return 'capp_repayment.repayment_please_try_again';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_try_again', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng thử lại sau", name: 'capp_repayment.repayment_please_try_again');
    return result != '' ? result : 'repayment_please_try_again';
  }

  String get zalopayErrorMessageCodeInvalidResponse {
    if(showKeys){
      return 'capp_repayment.zalopay_error_message_code_invalid_response';
    }
    var ot = overridenTranslation(
        'capp_repayment.zalopay_error_message_code_invalid_response', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không tìm thấy dữ liệu", name: 'capp_repayment.zalopay_error_message_code_invalid_response');
    return result != '' ? result : 'zalopay_error_message_code_invalid_response';
  }

  String get zalopayErrorMessageCodeInvalidOrder {
    if(showKeys){
      return 'capp_repayment.zalopay_error_message_code_invalid_order';
    }
    var ot = overridenTranslation(
        'capp_repayment.zalopay_error_message_code_invalid_order', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Yêu cầu không hợp lệ", name: 'capp_repayment.zalopay_error_message_code_invalid_order');
    return result != '' ? result : 'zalopay_error_message_code_invalid_order';
  }

  String get zalopayErrorMessageCodeFail {
    if(showKeys){
      return 'capp_repayment.zalopay_error_message_code_fail';
    }
    var ot = overridenTranslation(
        'capp_repayment.zalopay_error_message_code_fail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán thất bại", name: 'capp_repayment.zalopay_error_message_code_fail');
    return result != '' ? result : 'zalopay_error_message_code_fail';
  }

  String get zalopayErrorMessageCodeCancel {
    if(showKeys){
      return 'capp_repayment.zalopay_error_message_code_cancel';
    }
    var ot = overridenTranslation(
        'capp_repayment.zalopay_error_message_code_cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán đã bị huỷ", name: 'capp_repayment.zalopay_error_message_code_cancel');
    return result != '' ? result : 'zalopay_error_message_code_cancel';
  }

  String get exceptionOccurred {
    if(showKeys){
      return 'capp_repayment.exception__occurred';
    }
    var ot = overridenTranslation(
        'capp_repayment.exception__occurred', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có lỗi xảy ra", name: 'capp_repayment.exception__occurred');
    return result != '' ? result : 'exception__occurred';
  }

  String get viettelErrorMessageCode22 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_22';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_22', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khách hàng nhập sai OTP tại cổng thanh toán", name: 'capp_repayment.viettel_error_message_code_22');
    return result != '' ? result : 'viettel_error_message_code_22';
  }

  String get viettelErrorMessageCodeV02 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_V02';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_V02', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khách hàng nhập sai OTP tại cổng thanh toán", name: 'capp_repayment.viettel_error_message_code_V02');
    return result != '' ? result : 'viettel_error_message_code_V02';
  }

  String get viettelErrorMessageCodeV03 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_V03';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_V03', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("OTP đã hết hạn", name: 'capp_repayment.viettel_error_message_code_V03');
    return result != '' ? result : 'viettel_error_message_code_V03';
  }

  String get viettelErrorMessageCode21 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_21';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_21', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khách hàng nhập sai mã PIN", name: 'capp_repayment.viettel_error_message_code_21');
    return result != '' ? result : 'viettel_error_message_code_21';
  }

  String get viettelErrorMessageCode685 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_685';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_685', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khách hàng nhập sai mã PIN", name: 'capp_repayment.viettel_error_message_code_685');
    return result != '' ? result : 'viettel_error_message_code_685';
  }

  String get viettelErrorMessageCode16 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_16';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_16', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khách hàng không đủ số dư để thanh toán", name: 'capp_repayment.viettel_error_message_code_16');
    return result != '' ? result : 'viettel_error_message_code_16';
  }

  String get viettelErrorMessageCodeW04 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_W04';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_W04', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kết nối của bạn đã hết hạn", name: 'capp_repayment.viettel_error_message_code_W04');
    return result != '' ? result : 'viettel_error_message_code_W04';
  }

  String get viettelErrorMessageCodeV04 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_V04';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_V04', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có lỗi khi truy vấn hệ thống tại VIETTEL", name: 'capp_repayment.viettel_error_message_code_V04');
    return result != '' ? result : 'viettel_error_message_code_V04';
  }

  String get viettelErrorMessageCodeV05 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_V05';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_V05', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không xác nhận được giao dịch (Đã có lỗi xảy ra. Quý khách vui lòng kiểm tra lại)", name: 'capp_repayment.viettel_error_message_code_V05');
    return result != '' ? result : 'viettel_error_message_code_V05';
  }

  String get viettelErrorMessageCodeV06 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_V06';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_V06', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khách hàng hủy thanh toán", name: 'capp_repayment.viettel_error_message_code_V06');
    return result != '' ? result : 'viettel_error_message_code_V06';
  }

  String get viettelErrorMessageCodeSMaintain {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_S_MAINTAIN';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_S_MAINTAIN', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cổng thanh toán đang được bảo trì. Quý khách vui lòng thử lại sau", name: 'capp_repayment.viettel_error_message_code_S_MAINTAIN');
    return result != '' ? result : 'viettel_error_message_code_S_MAINTAIN';
  }

  String get viettelErrorMessageCode99 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_99';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_99', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Lỗi không xác định", name: 'capp_repayment.viettel_error_message_code_99');
    return result != '' ? result : 'viettel_error_message_code_99';
  }

  String get viettelErrorMessageCodeM03 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_M03';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_M03', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hình thức thanh toán không phù hợp.", name: 'capp_repayment.viettel_error_message_code_M03');
    return result != '' ? result : 'viettel_error_message_code_M03';
  }

  String get viettelErrorMessageCodeM04 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_M04';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_M04', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ảnh QR bị lỗi hoặc không đọc được  giá trị cần thiết từ ảnh", name: 'capp_repayment.viettel_error_message_code_M04');
    return result != '' ? result : 'viettel_error_message_code_M04';
  }

  String get viettelErrorMessageCode813 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_813';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_813', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Lỗi kết nối tới Cổng thanh toán", name: 'capp_repayment.viettel_error_message_code_813');
    return result != '' ? result : 'viettel_error_message_code_813';
  }

  String get viettelErrorMessageCodeV01 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_V01';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_V01', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sai checksum", name: 'capp_repayment.viettel_error_message_code_V01');
    return result != '' ? result : 'viettel_error_message_code_V01';
  }

  String get viettelErrorMessageCodeM01 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_M01';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_M01', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mã đối tác chưa được đăng ký (liên hệ kỹ thuật Viettel để kiểm tra)", name: 'capp_repayment.viettel_error_message_code_M01');
    return result != '' ? result : 'viettel_error_message_code_M01';
  }

  String get viettelErrorMessageCodeM02 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_M02';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_M02', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chưa thiết lập tài khoản nhận tiền cho đối tác (liên hệ kỹ thuật Viettel)", name: 'capp_repayment.viettel_error_message_code_M02');
    return result != '' ? result : 'viettel_error_message_code_M02';
  }

  String get viettelErrorMessageCodeP48 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_P48';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_P48', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giao dịch không thành công do gói cước ViettelPay của khách hàng chưa được nâng cấp, vui lòng truy cập ứng dụng ViettelPay để được hướng dẫn nâng cấp gói cước.", name: 'capp_repayment.viettel_error_message_code_P48');
    return result != '' ? result : 'viettel_error_message_code_P48';
  }

  String get viettelErrorMessageCode430 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_430';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_430', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số điện thoại đã được liên kết trước đó", name: 'capp_repayment.viettel_error_message_code_430');
    return result != '' ? result : 'viettel_error_message_code_430';
  }

  String get viettelErrorMessageCode427 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_427';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_427', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khách hàng chưa đăng kí thanh toán tự động", name: 'capp_repayment.viettel_error_message_code_427');
    return result != '' ? result : 'viettel_error_message_code_427';
  }

  String get viettelErrorMessageCode191 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_191';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_191', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Lỗi xác thực giao dịch khi thanh toán Token", name: 'capp_repayment.viettel_error_message_code_191');
    return result != '' ? result : 'viettel_error_message_code_191';
  }

  String get viettelErrorMessageCodeP01 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_P01';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_P01', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán không hợp lệ hoặc vượt hạn mức thanh toán được cấp", name: 'capp_repayment.viettel_error_message_code_P01');
    return result != '' ? result : 'viettel_error_message_code_P01';
  }

  String get viettelErrorMessageCodeP03 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_P03';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_P03', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số điện thoại không đủ điều kiện liên kết", name: 'capp_repayment.viettel_error_message_code_P03');
    return result != '' ? result : 'viettel_error_message_code_P03';
  }

  String get viettelErrorMessageCode32 {
    if(showKeys){
      return 'capp_repayment.viettel_error_message_code_32';
    }
    var ot = overridenTranslation(
        'capp_repayment.viettel_error_message_code_32', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giao dịch hết thời lượng", name: 'capp_repayment.viettel_error_message_code_32');
    return result != '' ? result : 'viettel_error_message_code_32';
  }

  String get repaymentNoPaymentIsRequired {
    if(showKeys){
      return 'capp_repayment.repayment_no_payment_is_required';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_no_payment_is_required', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không cần thanh toán vào lúc này", name: 'capp_repayment.repayment_no_payment_is_required');
    return result != '' ? result : 'repayment_no_payment_is_required';
  }

  String get repaymentAddCardCapitalize {
    if(showKeys){
      return 'capp_repayment.repayment_add_card_capitalize';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_add_card_capitalize', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thêm Thẻ", name: 'capp_repayment.repayment_add_card_capitalize');
    return result != '' ? result : 'repayment_add_card_capitalize';
  }

  String get repaymentSitTight {
    if(showKeys){
      return 'capp_repayment.repayment_sit_tight';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_sit_tight', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xin chờ trong chốc lát! Chúng tôi đang xử lý giao dịch của bạn", name: 'capp_repayment.repayment_sit_tight');
    return result != '' ? result : 'repayment_sit_tight';
  }

  String get repaymentWeWillImmediatelyNotify {
    if(showKeys){
      return 'capp_repayment.repayment_we_will_immediately_notify';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_we_will_immediately_notify', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chúng tôi sẽ thông báo ngay cho bạn khi chúng tôi có thông tin cập nhật về yêu cầu giao dịch của bạn", name: 'capp_repayment.repayment_we_will_immediately_notify');
    return result != '' ? result : 'repayment_we_will_immediately_notify';
  }

  String get repaymentCardVerification {
    if(showKeys){
      return 'capp_repayment.repayment_card_verification';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_card_verification', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác thực thẻ", name: 'capp_repayment.repayment_card_verification');
    return result != '' ? result : 'repayment_card_verification';
  }

  String get repaymentPleaseEnterTheAmountIndicated {
    if(showKeys){
      return 'capp_repayment.repayment_please_enter_the_amount_indicated';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_enter_the_amount_indicated', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng nhập số tiền được chỉ định trong SMS hoặc email hoặc tài khoản ngân hàng trực tuyến / di động được gửi cho bạn.", name: 'capp_repayment.repayment_please_enter_the_amount_indicated');
    return result != '' ? result : 'repayment_please_enter_the_amount_indicated';
  }

  String get repaymentEnterAmount {
    if(showKeys){
      return 'capp_repayment.repayment_enter_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_enter_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhập số tiền", name: 'capp_repayment.repayment_enter_amount');
    return result != '' ? result : 'repayment_enter_amount';
  }

  String get repaymentVerify {
    if(showKeys){
      return 'capp_repayment.repayment_verify';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_verify', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác thực", name: 'capp_repayment.repayment_verify');
    return result != '' ? result : 'repayment_verify';
  }

  String get paymentSuccessBodyMessagePh {
    if(showKeys){
      return 'capp_repayment.payment_success_body_message_ph';
    }
    var ot = overridenTranslation(
        'capp_repayment.payment_success_body_message_ph', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cảm ơn bạn! Số tiền thanh toán đã được xác nhận để chuyển đến Home Credit", name: 'capp_repayment.payment_success_body_message_ph');
    return result != '' ? result : 'payment_success_body_message_ph';
  }

  String get processingFee {
    if(showKeys){
      return 'capp_repayment.processing_fee';
    }
    var ot = overridenTranslation(
        'capp_repayment.processing_fee', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phí giao dịch", name: 'capp_repayment.processing_fee');
    return result != '' ? result : 'processing_fee';
  }

  String get waived {
    if(showKeys){
      return 'capp_repayment.waived';
    }
    var ot = overridenTranslation(
        'capp_repayment.waived', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Miễn trừ", name: 'capp_repayment.waived');
    return result != '' ? result : 'waived';
  }

  String get repaymentCustomerName {
    if(showKeys){
      return 'capp_repayment.repayment_customer_name';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_customer_name', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tên khách hàng", name: 'capp_repayment.repayment_customer_name');
    return result != '' ? result : 'repayment_customer_name';
  }

  String get repaymentSelectEmoneyAndEcommerce {
    if(showKeys){
      return 'capp_repayment.repayment_select_emoney_and_ecommerce';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_emoney_and_ecommerce', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_select_emoney_and_ecommerce');
    return result != '' ? result : 'repayment_select_emoney_and_ecommerce';
  }

  String get repaymentElectricMoney {
    if(showKeys){
      return 'capp_repayment.repayment_electric_money';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_electric_money', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_electric_money');
    return result != '' ? result : 'repayment_electric_money';
  }

  String get repaymentEmoney {
    if(showKeys){
      return 'capp_repayment.repayment_emoney';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney');
    return result != '' ? result : 'repayment_emoney';
  }

  String get repaymentEcommerce {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce');
    return result != '' ? result : 'repayment_ecommerce';
  }

  String get repaymentInstantPayment {
    if(showKeys){
      return 'capp_repayment.repayment_instant_payment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_instant_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_instant_payment');
    return result != '' ? result : 'repayment_instant_payment';
  }

  String get repaymentEmoneyGotagihanStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_gotagihan_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_gotagihan_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_gotagihan_step1');
    return result != '' ? result : 'repayment_emoney_gotagihan_step1';
  }

  String get repaymentEmoneyGotagihanStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_gotagihan_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_gotagihan_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_gotagihan_step2');
    return result != '' ? result : 'repayment_emoney_gotagihan_step2';
  }

  String get repaymentEmoneyGotagihanStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_gotagihan_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_gotagihan_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_gotagihan_step3');
    return result != '' ? result : 'repayment_emoney_gotagihan_step3';
  }

  String get repaymentEmoneyGotagihanStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_gotagihan_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_gotagihan_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_gotagihan_step4');
    return result != '' ? result : 'repayment_emoney_gotagihan_step4';
  }

  String get repaymentEmoneyGotagihanStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_gotagihan_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_gotagihan_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_gotagihan_step5');
    return result != '' ? result : 'repayment_emoney_gotagihan_step5';
  }

  String get repaymentEmoneyGotagihanStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_gotagihan_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_gotagihan_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_gotagihan_step6');
    return result != '' ? result : 'repayment_emoney_gotagihan_step6';
  }

  String get repaymentEmoneyGotagihanStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_gotagihan_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_gotagihan_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_gotagihan_step7');
    return result != '' ? result : 'repayment_emoney_gotagihan_step7';
  }

  String get repaymentEmoneyAyopopStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step1');
    return result != '' ? result : 'repayment_emoney_ayopop_step1';
  }

  String get repaymentEmoneyAyopopStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step2');
    return result != '' ? result : 'repayment_emoney_ayopop_step2';
  }

  String get repaymentEmoneyAyopopStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step3');
    return result != '' ? result : 'repayment_emoney_ayopop_step3';
  }

  String get repaymentEmoneyAyopopStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step4');
    return result != '' ? result : 'repayment_emoney_ayopop_step4';
  }

  String get repaymentEmoneyAyopopStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step5');
    return result != '' ? result : 'repayment_emoney_ayopop_step5';
  }

  String get repaymentEmoneyAyopopStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step6');
    return result != '' ? result : 'repayment_emoney_ayopop_step6';
  }

  String get repaymentEmoneyAyopopStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step7');
    return result != '' ? result : 'repayment_emoney_ayopop_step7';
  }

  String get repaymentEmoneyAyopopStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_ayopop_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_ayopop_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_ayopop_step8');
    return result != '' ? result : 'repayment_emoney_ayopop_step8';
  }

  String get repaymentEmoneyBebasbayarStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_bebasbayar_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_bebasbayar_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_bebasbayar_step1');
    return result != '' ? result : 'repayment_emoney_bebasbayar_step1';
  }

  String get repaymentEmoneyBebasbayarStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_bebasbayar_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_bebasbayar_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_bebasbayar_step2');
    return result != '' ? result : 'repayment_emoney_bebasbayar_step2';
  }

  String get repaymentEmoneyBebasbayarStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_bebasbayar_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_bebasbayar_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_bebasbayar_step3');
    return result != '' ? result : 'repayment_emoney_bebasbayar_step3';
  }

  String get repaymentEmoneyBebasbayarStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_bebasbayar_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_bebasbayar_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_bebasbayar_step4');
    return result != '' ? result : 'repayment_emoney_bebasbayar_step4';
  }

  String get repaymentEmoneyBebasbayarStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_bebasbayar_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_bebasbayar_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_bebasbayar_step5');
    return result != '' ? result : 'repayment_emoney_bebasbayar_step5';
  }

  String get repaymentEmoneyBebasbayarStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_emoney_bebasbayar_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_emoney_bebasbayar_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_emoney_bebasbayar_step6');
    return result != '' ? result : 'repayment_emoney_bebasbayar_step6';
  }

  String get repaymentEcommerceTokopediaStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step1');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step1';
  }

  String get repaymentEcommerceTokopediaStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step2');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step2';
  }

  String get repaymentEcommerceTokopediaStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step3');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step3';
  }

  String get repaymentEcommerceTokopediaStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step4');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step4';
  }

  String get repaymentEcommerceTokopediaStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step5');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step5';
  }

  String get repaymentEcommerceTokopediaStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step6');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step6';
  }

  String get repaymentEcommerceTokopediaStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step7');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step7';
  }

  String get repaymentEcommerceTokopediaStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step8');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step8';
  }

  String get repaymentEcommerceTokopediaStep9 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_tokopedia_step9';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_tokopedia_step9', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_tokopedia_step9');
    return result != '' ? result : 'repayment_ecommerce_tokopedia_step9';
  }

  String get repaymentEcommerceBukalapakStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_bukalapak_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_bukalapak_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_bukalapak_step1');
    return result != '' ? result : 'repayment_ecommerce_bukalapak_step1';
  }

  String get repaymentEcommerceBukalapakStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_bukalapak_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_bukalapak_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_bukalapak_step2');
    return result != '' ? result : 'repayment_ecommerce_bukalapak_step2';
  }

  String get repaymentEcommerceBukalapakStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_bukalapak_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_bukalapak_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_bukalapak_step3');
    return result != '' ? result : 'repayment_ecommerce_bukalapak_step3';
  }

  String get repaymentEcommerceBukalapakStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_bukalapak_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_bukalapak_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_bukalapak_step4');
    return result != '' ? result : 'repayment_ecommerce_bukalapak_step4';
  }

  String get repaymentEcommerceBukalapakStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_bukalapak_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_bukalapak_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_bukalapak_step5');
    return result != '' ? result : 'repayment_ecommerce_bukalapak_step5';
  }

  String get repaymentEcommerceBukalapakStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_bukalapak_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_bukalapak_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_bukalapak_step6');
    return result != '' ? result : 'repayment_ecommerce_bukalapak_step6';
  }

  String get repaymentEcommerceBukalapakStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_bukalapak_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_bukalapak_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_bukalapak_step7');
    return result != '' ? result : 'repayment_ecommerce_bukalapak_step7';
  }

  String get repaymentEcommerceBlibliStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step1');
    return result != '' ? result : 'repayment_ecommerce_blibli_step1';
  }

  String get repaymentEcommerceBlibliStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step2');
    return result != '' ? result : 'repayment_ecommerce_blibli_step2';
  }

  String get repaymentEcommerceBlibliStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step3');
    return result != '' ? result : 'repayment_ecommerce_blibli_step3';
  }

  String get repaymentEcommerceBlibliStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step4');
    return result != '' ? result : 'repayment_ecommerce_blibli_step4';
  }

  String get repaymentEcommerceBlibliStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step5');
    return result != '' ? result : 'repayment_ecommerce_blibli_step5';
  }

  String get repaymentEcommerceBlibliStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step6');
    return result != '' ? result : 'repayment_ecommerce_blibli_step6';
  }

  String get repaymentEcommerceBlibliStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step7');
    return result != '' ? result : 'repayment_ecommerce_blibli_step7';
  }

  String get repaymentEcommerceBlibliStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_blibli_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_blibli_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_blibli_step8');
    return result != '' ? result : 'repayment_ecommerce_blibli_step8';
  }

  String get repaymentEcommerceLazadaStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step1');
    return result != '' ? result : 'repayment_ecommerce_lazada_step1';
  }

  String get repaymentEcommerceLazadaStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step2');
    return result != '' ? result : 'repayment_ecommerce_lazada_step2';
  }

  String get repaymentEcommerceLazadaStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step3');
    return result != '' ? result : 'repayment_ecommerce_lazada_step3';
  }

  String get repaymentEcommerceLazadaStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step4');
    return result != '' ? result : 'repayment_ecommerce_lazada_step4';
  }

  String get repaymentEcommerceLazadaStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step5');
    return result != '' ? result : 'repayment_ecommerce_lazada_step5';
  }

  String get repaymentEcommerceLazadaStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step6');
    return result != '' ? result : 'repayment_ecommerce_lazada_step6';
  }

  String get repaymentEcommerceLazadaStep7 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step7';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step7', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step7');
    return result != '' ? result : 'repayment_ecommerce_lazada_step7';
  }

  String get repaymentEcommerceLazadaStep8 {
    if(showKeys){
      return 'capp_repayment.repayment_ecommerce_lazada_step8';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ecommerce_lazada_step8', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ecommerce_lazada_step8');
    return result != '' ? result : 'repayment_ecommerce_lazada_step8';
  }

  String get repaymentAda {
    if(showKeys){
      return 'capp_repayment.repayment_ada';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tự động", name: 'capp_repayment.repayment_ada');
    return result != '' ? result : 'repayment_ada';
  }

  String get repaymentPaymentsMadeConvenient {
    if(showKeys){
      return 'capp_repayment.repayment_payments_made_convenient';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payments_made_convenient', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_payments_made_convenient');
    return result != '' ? result : 'repayment_payments_made_convenient';
  }

  String get repaymentSetUpAnAutomatic {
    if(showKeys){
      return 'capp_repayment.repayment_set_up_an_automatic';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_set_up_an_automatic', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_set_up_an_automatic');
    return result != '' ? result : 'repayment_set_up_an_automatic';
  }

  String get repaymentWhatAccountsCanI {
    if(showKeys){
      return 'capp_repayment.repayment_what_accounts_can_i';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_what_accounts_can_i', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_what_accounts_can_i');
    return result != '' ? result : 'repayment_what_accounts_can_i';
  }

  String get repaymentOnlineBankingAccounts {
    if(showKeys){
      return 'capp_repayment.repayment_online_banking_accounts';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_banking_accounts', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_online_banking_accounts');
    return result != '' ? result : 'repayment_online_banking_accounts';
  }

  String get repaymentEnrollYourOnline {
    if(showKeys){
      return 'capp_repayment.repayment_enroll_your_online';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_enroll_your_online', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_enroll_your_online');
    return result != '' ? result : 'repayment_enroll_your_online';
  }

  String get repaymentEnrollYourMastercard {
    if(showKeys){
      return 'capp_repayment.repayment_enroll_your_mastercard';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_enroll_your_mastercard', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_enroll_your_mastercard');
    return result != '' ? result : 'repayment_enroll_your_mastercard';
  }

  String get repaymentRegularBankAccounts {
    if(showKeys){
      return 'capp_repayment.repayment_regular_bank_accounts';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_regular_bank_accounts', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_regular_bank_accounts');
    return result != '' ? result : 'repayment_regular_bank_accounts';
  }

  String get repaymentManuallyEnroll {
    if(showKeys){
      return 'capp_repayment.repayment_manually_enroll';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_manually_enroll', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_manually_enroll');
    return result != '' ? result : 'repayment_manually_enroll';
  }

  String get repaymentWhyShouldI {
    if(showKeys){
      return 'capp_repayment.repayment_why_should_i';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_why_should_i', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_why_should_i');
    return result != '' ? result : 'repayment_why_should_i';
  }

  String get repaymentEasyToSetUp {
    if(showKeys){
      return 'capp_repayment.repayment_easy_to_set_up';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_easy_to_set_up', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_easy_to_set_up');
    return result != '' ? result : 'repayment_easy_to_set_up';
  }

  String get repaymentHassleFree {
    if(showKeys){
      return 'capp_repayment.repayment_hassle_free';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_hassle_free', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_hassle_free');
    return result != '' ? result : 'repayment_hassle_free';
  }

  String get repaymentAvoidDelays {
    if(showKeys){
      return 'capp_repayment.repayment_avoid_delays';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_avoid_delays', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_avoid_delays');
    return result != '' ? result : 'repayment_avoid_delays';
  }

  String get repaymentSetupNow {
    if(showKeys){
      return 'capp_repayment.repayment_setup_now';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_setup_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_setup_now');
    return result != '' ? result : 'repayment_setup_now';
  }

  String get repaymentAdaTermsConditionsDetailTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_terms_conditions_detail_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_terms_conditions_detail_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_terms_conditions_detail_title');
    return result != '' ? result : 'repayment_ada_terms_conditions_detail_title';
  }

  String get repaymentAdaTermsConditionsDetailAgreeContinue {
    if(showKeys){
      return 'capp_repayment.repayment_ada_terms_conditions_detail_agree_continue';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_terms_conditions_detail_agree_continue', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_terms_conditions_detail_agree_continue');
    return result != '' ? result : 'repayment_ada_terms_conditions_detail_agree_continue';
  }

  String get repaymentOnlineBanks {
    if(showKeys){
      return 'capp_repayment.repayment_online_banks';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_banks', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_online_banks');
    return result != '' ? result : 'repayment_online_banks';
  }

  String get repaymentEnrollYourOnlineBankAccountToAuto {
    if(showKeys){
      return 'capp_repayment.repayment_enroll_your_online_bank_account_to_auto';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_enroll_your_online_bank_account_to_auto', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_enroll_your_online_bank_account_to_auto');
    return result != '' ? result : 'repayment_enroll_your_online_bank_account_to_auto';
  }

  String get repaymentUseYourVisaOrMastercard {
    if(showKeys){
      return 'capp_repayment.repayment_use_your_visa_or_mastercard';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_use_your_visa_or_mastercard', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_use_your_visa_or_mastercard');
    return result != '' ? result : 'repayment_use_your_visa_or_mastercard';
  }

  String get repaymentEnjoyHassleFree {
    if(showKeys){
      return 'capp_repayment.repayment_enjoy_hassle_free';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_enjoy_hassle_free', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_enjoy_hassle_free');
    return result != '' ? result : 'repayment_enjoy_hassle_free';
  }

  String get repaymentPayWithAutoDebitArrangement {
    if(showKeys){
      return 'capp_repayment.repayment_pay_with_auto_debit_arrangement';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_with_auto_debit_arrangement', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pay_with_auto_debit_arrangement');
    return result != '' ? result : 'repayment_pay_with_auto_debit_arrangement';
  }

  String get repaymentWeWilldeductYourLoan {
    if(showKeys){
      return 'capp_repayment.repayment_we_willdeduct_your_loan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_we_willdeduct_your_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_we_willdeduct_your_loan');
    return result != '' ? result : 'repayment_we_willdeduct_your_loan';
  }

  String get repaymentMakeSureYouHaveEnoughFunds {
    if(showKeys){
      return 'capp_repayment.repayment_make_sure_you_have_enough_funds';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_make_sure_you_have_enough_funds', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_make_sure_you_have_enough_funds');
    return result != '' ? result : 'repayment_make_sure_you_have_enough_funds';
  }

  String get repaymentByTappingOnContinue {
    if(showKeys){
      return 'capp_repayment.repayment_by_tapping_on_continue';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_by_tapping_on_continue', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_by_tapping_on_continue');
    return result != '' ? result : 'repayment_by_tapping_on_continue';
  }

  String get repaymentTermAndConditions {
    if(showKeys){
      return 'capp_repayment.repayment_term_and_conditions';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_term_and_conditions', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_term_and_conditions');
    return result != '' ? result : 'repayment_term_and_conditions';
  }

  String get repaymentHowItWorks {
    if(showKeys){
      return 'capp_repayment.repayment_how_it_works';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_how_it_works', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_how_it_works');
    return result != '' ? result : 'repayment_how_it_works';
  }

  String get repaymentAdaConfirmEnrollmentTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_confirm_enrollment_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_confirm_enrollment_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_confirm_enrollment_title');
    return result != '' ? result : 'repayment_ada_confirm_enrollment_title';
  }

  String get repaymentAdaConfirmEnrollmentDescription {
    if(showKeys){
      return 'capp_repayment.repayment_ada_confirm_enrollment_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_confirm_enrollment_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_confirm_enrollment_description');
    return result != '' ? result : 'repayment_ada_confirm_enrollment_description';
  }

  String get repaymentAdaBankName {
    if(showKeys){
      return 'capp_repayment.repayment_ada_bank_name';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_bank_name', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_bank_name');
    return result != '' ? result : 'repayment_ada_bank_name';
  }

  String get repaymentAdaCardType {
    if(showKeys){
      return 'capp_repayment.repayment_ada_card_type';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_card_type', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_card_type');
    return result != '' ? result : 'repayment_ada_card_type';
  }

  String get repaymentAdaCardNumber {
    if(showKeys){
      return 'capp_repayment.repayment_ada_card_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_card_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_card_number');
    return result != '' ? result : 'repayment_ada_card_number';
  }

  String get repaymentAdaLoanAccountNo {
    if(showKeys){
      return 'capp_repayment.repayment_ada_loan_account_no';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_loan_account_no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_loan_account_no');
    return result != '' ? result : 'repayment_ada_loan_account_no';
  }

  String get repaymentAdaMonthlyInstallment {
    if(showKeys){
      return 'capp_repayment.repayment_ada_monthly_installment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_monthly_installment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_monthly_installment');
    return result != '' ? result : 'repayment_ada_monthly_installment';
  }

  String get repaymentAdaLoanType {
    if(showKeys){
      return 'capp_repayment.repayment_ada_loan_type';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_loan_type', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_loan_type');
    return result != '' ? result : 'repayment_ada_loan_type';
  }

  String get repaymentAdaStartingFrom {
    if(showKeys){
      return 'capp_repayment.repayment_ada_starting_from';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_starting_from', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_starting_from');
    return result != '' ? result : 'repayment_ada_starting_from';
  }

  String get repaymentAdaUpUntil {
    if(showKeys){
      return 'capp_repayment.repayment_ada_up_until';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_up_until', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_up_until');
    return result != '' ? result : 'repayment_ada_up_until';
  }

  String get repaymentAdaTotalMonthlyInstallment {
    if(showKeys){
      return 'capp_repayment.repayment_ada_total_monthly_installment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_total_monthly_installment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_total_monthly_installment');
    return result != '' ? result : 'repayment_ada_total_monthly_installment';
  }

  String get repaymentAdaCancelEnrollment {
    if(showKeys){
      return 'capp_repayment.repayment_ada_cancel_enrollment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_cancel_enrollment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_cancel_enrollment');
    return result != '' ? result : 'repayment_ada_cancel_enrollment';
  }

  String get repaymentAdaConfirmEnroll {
    if(showKeys){
      return 'capp_repayment.repayment_ada_confirm_enroll';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_confirm_enroll', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_confirm_enroll');
    return result != '' ? result : 'repayment_ada_confirm_enroll';
  }

  String get repaymentAdaEnrollmentDetails {
    if(showKeys){
      return 'capp_repayment.repayment_ada_enrollment_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_enrollment_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_enrollment_details');
    return result != '' ? result : 'repayment_ada_enrollment_details';
  }

  String get repaymentAdaEnrollmentSuccessful {
    if(showKeys){
      return 'capp_repayment.repayment_ada_enrollment_successful';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_enrollment_successful', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_enrollment_successful');
    return result != '' ? result : 'repayment_ada_enrollment_successful';
  }

  String get repaymentAdaEnrollmentSuccessfulMessage {
    if(showKeys){
      return 'capp_repayment.repayment_ada_enrollment_successful_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_enrollment_successful_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_enrollment_successful_message');
    return result != '' ? result : 'repayment_ada_enrollment_successful_message';
  }

  String get repaymentAdaLoanAccountNumber {
    if(showKeys){
      return 'capp_repayment.repayment_ada_loan_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_loan_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_loan_account_number');
    return result != '' ? result : 'repayment_ada_loan_account_number';
  }

  String get repaymentAdaTotalMonthlyDeduction {
    if(showKeys){
      return 'capp_repayment.repayment_ada_total_monthly_deduction';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_total_monthly_deduction', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_total_monthly_deduction');
    return result != '' ? result : 'repayment_ada_total_monthly_deduction';
  }

  String get repaymentAdaBackToDashboard {
    if(showKeys){
      return 'capp_repayment.repayment_ada_back_to_dashboard';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_back_to_dashboard', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_back_to_dashboard');
    return result != '' ? result : 'repayment_ada_back_to_dashboard';
  }

  String get repaymentAdaViewPaymentSchedule {
    if(showKeys){
      return 'capp_repayment.repayment_ada_view_payment_schedule';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_view_payment_schedule', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ada_view_payment_schedule');
    return result != '' ? result : 'repayment_ada_view_payment_schedule';
  }

  String get repaymentMyCards {
    if(showKeys){
      return 'capp_repayment.repayment_my_cards';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_my_cards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_my_cards');
    return result != '' ? result : 'repayment_my_cards';
  }

  String get repaymentSavedCards {
    if(showKeys){
      return 'capp_repayment.repayment_saved_cards';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_saved_cards', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_saved_cards');
    return result != '' ? result : 'repayment_saved_cards';
  }

  String get repaymentAllYourSavedCardsIn {
    if(showKeys){
      return 'capp_repayment.repayment_all_your_saved_cards_in';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_all_your_saved_cards_in', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_all_your_saved_cards_in');
    return result != '' ? result : 'repayment_all_your_saved_cards_in';
  }

  String get repaymentRemoveCardEndingIn {
    if(showKeys){
      return 'capp_repayment.repayment_remove_card_ending_in';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_remove_card_ending_in', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_remove_card_ending_in');
    return result != '' ? result : 'repayment_remove_card_ending_in';
  }

  String get repaymentYesRemove {
    if(showKeys){
      return 'capp_repayment.repayment_yes_remove';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_yes_remove', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_yes_remove');
    return result != '' ? result : 'repayment_yes_remove';
  }

  String get repaymentNoWait {
    if(showKeys){
      return 'capp_repayment.repayment_no_wait';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_no_wait', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_no_wait');
    return result != '' ? result : 'repayment_no_wait';
  }

  String get repaymentPersonalLoan {
    if(showKeys){
      return 'capp_repayment.repayment_personal_loan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_personal_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay tiền mặt", name: 'capp_repayment.repayment_personal_loan');
    return result != '' ? result : 'repayment_personal_loan';
  }

  String get repaymentConsumerLoan {
    if(showKeys){
      return 'capp_repayment.repayment_consumer_loan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_consumer_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay tiêu dùng", name: 'capp_repayment.repayment_consumer_loan');
    return result != '' ? result : 'repayment_consumer_loan';
  }

  String get repaymentBankBranch {
    if(showKeys){
      return 'capp_repayment.repayment_bank_branch';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_branch', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi nhánh ngân hàng", name: 'capp_repayment.repayment_bank_branch');
    return result != '' ? result : 'repayment_bank_branch';
  }

  String get repaymentVirtualAccountNumber {
    if(showKeys){
      return 'capp_repayment.repayment_virtual_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_virtual_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tài khoản ảo", name: 'capp_repayment.repayment_virtual_account_number');
    return result != '' ? result : 'repayment_virtual_account_number';
  }

  String get repaymentQrCode {
    if(showKeys){
      return 'capp_repayment.repayment_qr_code';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_qr_code', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mã QR", name: 'capp_repayment.repayment_qr_code');
    return result != '' ? result : 'repayment_qr_code';
  }

  String get repaymentYouCanPayWithThisQr {
    if(showKeys){
      return 'capp_repayment.repayment_you_can_pay_with_this_qr';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_you_can_pay_with_this_qr', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có thể thanh toán bằng QR này bằng Ứng dụng Ngân hàng trực tuyến", name: 'capp_repayment.repayment_you_can_pay_with_this_qr');
    return result != '' ? result : 'repayment_you_can_pay_with_this_qr';
  }

  String get repaymentShare {
    if(showKeys){
      return 'capp_repayment.repayment_share';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_share', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chia sẻ", name: 'capp_repayment.repayment_share');
    return result != '' ? result : 'repayment_share';
  }

  String get repaymentDownload {
    if(showKeys){
      return 'capp_repayment.repayment_download';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_download', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải về", name: 'capp_repayment.repayment_download');
    return result != '' ? result : 'repayment_download';
  }

  String get repaymentDownloadFail {
    if(showKeys){
      return 'capp_repayment.repayment_download_fail';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_download_fail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải về mã QR thất bại, xin hãy thử lại", name: 'capp_repayment.repayment_download_fail');
    return result != '' ? result : 'repayment_download_fail';
  }

  String get repaymentDownloadSuccessfully {
    if(showKeys){
      return 'capp_repayment.repayment_download_successfully';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_download_successfully', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải về thành công", name: 'capp_repayment.repayment_download_successfully');
    return result != '' ? result : 'repayment_download_successfully';
  }

  String get repaymentClose {
    if(showKeys){
      return 'capp_repayment.repayment_close';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_close', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đóng", name: 'capp_repayment.repayment_close');
    return result != '' ? result : 'repayment_close';
  }

  String get repaymentShareFail {
    if(showKeys){
      return 'capp_repayment.repayment_share_fail';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_share_fail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chia sẻ mã QR thất bại, xin hãy thử lại", name: 'capp_repayment.repayment_share_fail');
    return result != '' ? result : 'repayment_share_fail';
  }

  String get repaymentOpenSettings {
    if(showKeys){
      return 'capp_repayment.repayment_open_settings';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_open_settings', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mở cài đặt", name: 'capp_repayment.repayment_open_settings');
    return result != '' ? result : 'repayment_open_settings';
  }

  String get repaymentToHavePermissionToAccessPhotoIos {
    if(showKeys){
      return 'capp_repayment.repayment_to_have_permission_to_access_photo_ios';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_to_have_permission_to_access_photo_ios', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Để có quyền truy cập thư viện ảnh, vui lòng mở Cài đặt và thiết lập quyền truy cập ảnh cho ứng dụng", name: 'capp_repayment.repayment_to_have_permission_to_access_photo_ios');
    return result != '' ? result : 'repayment_to_have_permission_to_access_photo_ios';
  }

  String get repaymentToHavePermissionToAccessPhotoAndroid {
    if(showKeys){
      return 'capp_repayment.repayment_to_have_permission_to_access_photo_android';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_to_have_permission_to_access_photo_android', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Để có quyền truy cập thư viện ảnh, vui lòng mở Cài đặt và thiết lập quyền truy cập tệp và nội dung nghe nhìn cho ứng dụng", name: 'capp_repayment.repayment_to_have_permission_to_access_photo_android');
    return result != '' ? result : 'repayment_to_have_permission_to_access_photo_android';
  }

  String repaymentSelectAmountMinimumAmountAllowed(dynamic minimumThresholdAmount) {
  if(showKeys){
      return 'capp_repayment.repayment_select_amount_minimum_amount_allowed';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_minimum_amount_allowed', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Số tiền thanh toán tối thiểu là ${minimumThresholdAmount}", name: 'capp_repayment.repayment_select_amount_minimum_amount_allowed', args: [minimumThresholdAmount]);
  }

  String repaymentSelectAmountCelAmountMustBeEqual(dynamic minimumThresholdAmount) {
  if(showKeys){
      return 'capp_repayment.repayment_select_amount_cel_amount_must_be_equal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_cel_amount_must_be_equal', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Vui lòng thanh toán tổng dư nợ ${minimumThresholdAmount}", name: 'capp_repayment.repayment_select_amount_cel_amount_must_be_equal', args: [minimumThresholdAmount]);
  }

  String get repaymentMinimumAmount {
    if(showKeys){
      return 'capp_repayment.repayment_minimum_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_minimum_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền tối thiểu", name: 'capp_repayment.repayment_minimum_amount');
    return result != '' ? result : 'repayment_minimum_amount';
  }

  String get repaymentDetails {
    if(showKeys){
      return 'capp_repayment.repayment_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết", name: 'capp_repayment.repayment_details');
    return result != '' ? result : 'repayment_details';
  }

  String get repaymentRetailTransfer {
    if(showKeys){
      return 'capp_repayment.repayment_retail_transfer';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_retail_transfer', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_retail_transfer');
    return result != '' ? result : 'repayment_retail_transfer';
  }

  String get repaymentAvailablePartnerRetail {
    if(showKeys){
      return 'capp_repayment.repayment_available_partner_retail';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_available_partner_retail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_available_partner_retail');
    return result != '' ? result : 'repayment_available_partner_retail';
  }

  String get repaymentAvailablePartnerRetailDescription {
    if(showKeys){
      return 'capp_repayment.repayment_available_partner_retail_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_available_partner_retail_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_available_partner_retail_description');
    return result != '' ? result : 'repayment_available_partner_retail_description';
  }

  String get repaymentMaximumPaymentAmountForTransfer {
    if(showKeys){
      return 'capp_repayment.repayment_maximum_payment_amount_for_transfer';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_maximum_payment_amount_for_transfer', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_maximum_payment_amount_for_transfer');
    return result != '' ? result : 'repayment_maximum_payment_amount_for_transfer';
  }

  String get repaymentGoToGeraiRetail {
    if(showKeys){
      return 'capp_repayment.repayment_go_to_gerai_retail';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_go_to_gerai_retail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_go_to_gerai_retail');
    return result != '' ? result : 'repayment_go_to_gerai_retail';
  }

  String get repaymentPayAccordingToTheAmount {
    if(showKeys){
      return 'capp_repayment.repayment_pay_according_to_the_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_according_to_the_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pay_according_to_the_amount');
    return result != '' ? result : 'repayment_pay_according_to_the_amount';
  }

  String get repaymentMakeSureToKeepYourPaymentReceipt {
    if(showKeys){
      return 'capp_repayment.repayment_make_sure_to_keep_your_payment_receipt';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_make_sure_to_keep_your_payment_receipt', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_make_sure_to_keep_your_payment_receipt');
    return result != '' ? result : 'repayment_make_sure_to_keep_your_payment_receipt';
  }

  String get cancelPaymentMessage {
    if(showKeys){
      return 'capp_repayment.cancel_payment_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.cancel_payment_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có chắc muốn hủy thanh toán? Giao dịch sẽ bị hủy và không thể làm lại", name: 'capp_repayment.cancel_payment_message');
    return result != '' ? result : 'cancel_payment_message';
  }

  String get cancelPaymentYes {
    if(showKeys){
      return 'capp_repayment.cancel_payment_yes';
    }
    var ot = overridenTranslation(
        'capp_repayment.cancel_payment_yes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đúng, tôi muốn hủy", name: 'capp_repayment.cancel_payment_yes');
    return result != '' ? result : 'cancel_payment_yes';
  }

  String get cancelPaymentNo {
    if(showKeys){
      return 'capp_repayment.cancel_payment_no';
    }
    var ot = overridenTranslation(
        'capp_repayment.cancel_payment_no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tôi sẽ tiếp tục", name: 'capp_repayment.cancel_payment_no');
    return result != '' ? result : 'cancel_payment_no';
  }

  String get repaymentPayViaBankTransferIsNotAvailable {
    if(showKeys){
      return 'capp_repayment.repayment_pay_via_bank_transfer_is_not_available';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_via_bank_transfer_is_not_available', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán qua chuyển khoản ngân hàng hiện không khả dụng, vui lòng thử phương thức khác", name: 'capp_repayment.repayment_pay_via_bank_transfer_is_not_available');
    return result != '' ? result : 'repayment_pay_via_bank_transfer_is_not_available';
  }

  String get repaymentGotItChangeMethod {
    if(showKeys){
      return 'capp_repayment.repayment_got_it_change_method';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_got_it_change_method', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thay đổi phương thức thanh toán khác", name: 'capp_repayment.repayment_got_it_change_method');
    return result != '' ? result : 'repayment_got_it_change_method';
  }

  String get repaymentContractRepayment {
    if(showKeys){
      return 'capp_repayment.repayment_contract_repayment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán hợp đồng", name: 'capp_repayment.repayment_contract_repayment');
    return result != '' ? result : 'repayment_contract_repayment';
  }

  String get repaymentRepayNow {
    if(showKeys){
      return 'capp_repayment.repayment_repay_now';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_repay_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán ngay", name: 'capp_repayment.repayment_repay_now');
    return result != '' ? result : 'repayment_repay_now';
  }

  String get repaymentOutstandingBalance {
    if(showKeys){
      return 'capp_repayment.repayment_outstanding_balance';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_outstanding_balance', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_outstanding_balance');
    return result != '' ? result : 'repayment_outstanding_balance';
  }

  String get repaymentSelectAmountRelBnplMinimumAmountValidationMes {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_rel_bnpl_minimum_amount_validation_mes';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_rel_bnpl_minimum_amount_validation_mes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng thanh toán số tiền còn lại trước ngày đến hạn để tránh phát sinh phí phạt và ảnh hưởng lịch sử tín dụng", name: 'capp_repayment.repayment_select_amount_rel_bnpl_minimum_amount_validation_mes');
    return result != '' ? result : 'repayment_select_amount_rel_bnpl_minimum_amount_validation_mes';
  }

  String get repaymentVirtualAccountNo {
    if(showKeys){
      return 'capp_repayment.repayment_virtual_account_no';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_virtual_account_no', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_virtual_account_no');
    return result != '' ? result : 'repayment_virtual_account_no';
  }

  String get repaymentProductCreditCard {
    if(showKeys){
      return 'capp_repayment.repayment_product_credit_card';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_product_credit_card', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ tín dụng", name: 'capp_repayment.repayment_product_credit_card');
    return result != '' ? result : 'repayment_product_credit_card';
  }

  String get repaymentProductBnpl {
    if(showKeys){
      return 'capp_repayment.repayment_product_bnpl';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_product_bnpl', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Home PayLater", name: 'capp_repayment.repayment_product_bnpl');
    return result != '' ? result : 'repayment_product_bnpl';
  }

  String get repaymentProductBnplNew {
    if(showKeys){
      return 'capp_repayment.repayment_product_bnpl_new';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_product_bnpl_new', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản trả sau", name: 'capp_repayment.repayment_product_bnpl_new');
    return result != '' ? result : 'repayment_product_bnpl_new';
  }

  String get changePaymentAmount {
    if(showKeys){
      return 'capp_repayment.change_payment_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.change_payment_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.change_payment_amount');
    return result != '' ? result : 'change_payment_amount';
  }

  String get repaymentSelectOverTheCounterOption {
    if(showKeys){
      return 'capp_repayment.repayment_select_over_the_counter_option';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_over_the_counter_option', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_select_over_the_counter_option');
    return result != '' ? result : 'repayment_select_over_the_counter_option';
  }

  String get repaymentGenerateBarcode {
    if(showKeys){
      return 'capp_repayment.repayment_generate_barcode';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_generate_barcode', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_generate_barcode');
    return result != '' ? result : 'repayment_generate_barcode';
  }

  String get repaymentZero {
    if(showKeys){
      return 'capp_repayment.repayment_zero';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_zero', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_zero');
    return result != '' ? result : 'repayment_zero';
  }

  String get repaymentPreferredPartnerStores {
    if(showKeys){
      return 'capp_repayment.repayment_preferred_partner_stores';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_preferred_partner_stores', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_preferred_partner_stores');
    return result != '' ? result : 'repayment_preferred_partner_stores';
  }

  String get repaymentRealTimePosting {
    if(showKeys){
      return 'capp_repayment.repayment_real_time_posting';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_real_time_posting', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_real_time_posting');
    return result != '' ? result : 'repayment_real_time_posting';
  }

  String get repaymentOtherPartnerStores {
    if(showKeys){
      return 'capp_repayment.repayment_other_partner_stores';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_other_partner_stores', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_other_partner_stores');
    return result != '' ? result : 'repayment_other_partner_stores';
  }

  String get repaymentPaymentInstruction {
    if(showKeys){
      return 'capp_repayment.repayment_payment_instruction';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_instruction', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_payment_instruction');
    return result != '' ? result : 'repayment_payment_instruction';
  }

  String get repaymentFindNearestStore {
    if(showKeys){
      return 'capp_repayment.repayment_find_nearest_store';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_find_nearest_store', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_find_nearest_store');
    return result != '' ? result : 'repayment_find_nearest_store';
  }

  String get repaymentShowBarcodeTitle {
    if(showKeys){
      return 'capp_repayment.repayment_show_barcode_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_show_barcode_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_show_barcode_title');
    return result != '' ? result : 'repayment_show_barcode_title';
  }

  String get repaymentShowBarcodeMessage {
    if(showKeys){
      return 'capp_repayment.repayment_show_barcode_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_show_barcode_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_show_barcode_message');
    return result != '' ? result : 'repayment_show_barcode_message';
  }

  String get repaymentExpiryDate {
    if(showKeys){
      return 'capp_repayment.repayment_expiry_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_expiry_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_expiry_date');
    return result != '' ? result : 'repayment_expiry_date';
  }

  String get repaymentAccountType {
    if(showKeys){
      return 'capp_repayment.repayment_account_type';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_account_type', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_account_type');
    return result != '' ? result : 'repayment_account_type';
  }

  String get repaymentSaveScreenshot {
    if(showKeys){
      return 'capp_repayment.repayment_save_screenshot';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_save_screenshot', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_save_screenshot');
    return result != '' ? result : 'repayment_save_screenshot';
  }

  String get repaymentSubtotal {
    if(showKeys){
      return 'capp_repayment.repayment_subtotal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_subtotal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_subtotal');
    return result != '' ? result : 'repayment_subtotal';
  }

  String get repaymentSaveScreenshotSuccessfully {
    if(showKeys){
      return 'capp_repayment.repayment_save_screenshot_successfully';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_save_screenshot_successfully', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_save_screenshot_successfully');
    return result != '' ? result : 'repayment_save_screenshot_successfully';
  }

  String get repaymentSaveScreenshotFail {
    if(showKeys){
      return 'capp_repayment.repayment_save_screenshot_fail';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_save_screenshot_fail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_save_screenshot_fail');
    return result != '' ? result : 'repayment_save_screenshot_fail';
  }

  String get repaymentScanQrCode {
    if(showKeys){
      return 'capp_repayment.repayment_scan_qr_code';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_scan_qr_code', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quét mã QR", name: 'capp_repayment.repayment_scan_qr_code');
    return result != '' ? result : 'repayment_scan_qr_code';
  }

  String get repaymentPleaseTransferToThisAccountBelow {
    if(showKeys){
      return 'capp_repayment.repayment_please_transfer_to_this_account_below';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_transfer_to_this_account_below', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng chuyển khoản đến tài khoản dưới đây", name: 'capp_repayment.repayment_please_transfer_to_this_account_below');
    return result != '' ? result : 'repayment_please_transfer_to_this_account_below';
  }

  String get repaymentPleaseDownloadAndScan {
    if(showKeys){
      return 'capp_repayment.repayment_please_download_and_scan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_download_and_scan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng tải mã này và quét bằng ứng dụng ngân hàng trực tuyến", name: 'capp_repayment.repayment_please_download_and_scan');
    return result != '' ? result : 'repayment_please_download_and_scan';
  }

  String get repaymentQrBankTransfer {
    if(showKeys){
      return 'capp_repayment.repayment_qr_bank_transfer';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_qr_bank_transfer', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản", name: 'capp_repayment.repayment_qr_bank_transfer');
    return result != '' ? result : 'repayment_qr_bank_transfer';
  }

  String get repaymentMethodSelectionEwalletTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_ewallet_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_ewallet_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ví điện tử", name: 'capp_repayment.repayment_method_selection_ewallet_title');
    return result != '' ? result : 'repayment_method_selection_ewallet_title';
  }

  String get repaymentMethodSelectionEwalletSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_ewallet_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_ewallet_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Momo, ZaloPay, ShopeePay, ...", name: 'capp_repayment.repayment_method_selection_ewallet_subtitle');
    return result != '' ? result : 'repayment_method_selection_ewallet_subtitle';
  }

  String get repaymentMethodSelectionInternetBankingTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_internet_banking_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_internet_banking_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ứng dụng ngân hàng", name: 'capp_repayment.repayment_method_selection_internet_banking_title');
    return result != '' ? result : 'repayment_method_selection_internet_banking_title';
  }

  String get repaymentMethodSelectionInternetBankingSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_internet_banking_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_internet_banking_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác nhận thanh toán chỉ 1 bước", name: 'capp_repayment.repayment_method_selection_internet_banking_subtitle');
    return result != '' ? result : 'repayment_method_selection_internet_banking_subtitle';
  }

  String get repaymentMethodSelectionAtmTitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_atm_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_atm_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ ATM", name: 'capp_repayment.repayment_method_selection_atm_title');
    return result != '' ? result : 'repayment_method_selection_atm_title';
  }

  String get repaymentMethodSelectionAtmSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_method_selection_atm_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method_selection_atm_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán trực tuyến qua OnePay", name: 'capp_repayment.repayment_method_selection_atm_subtitle');
    return result != '' ? result : 'repayment_method_selection_atm_subtitle';
  }

  String repaymentSelectAmountOtcAmountMustBeBetweenMinMax(dynamic minimumThresholdAmount, dynamic otcMaximumAmount) {
  if(showKeys){
      return 'capp_repayment.repayment_select_amount_otc_amount_must_be_between_min_max';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_otc_amount_must_be_between_min_max', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("${minimumThresholdAmount}${otcMaximumAmount}", name: 'capp_repayment.repayment_select_amount_otc_amount_must_be_between_min_max', args: [minimumThresholdAmount, otcMaximumAmount]);
  }

  String get repaymentContractNumber2 {
    if(showKeys){
      return 'capp_repayment.repayment_contract_number2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_number2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_contract_number2');
    return result != '' ? result : 'repayment_contract_number2';
  }

  String get repaymentMainTitle {
    if(showKeys){
      return 'capp_repayment.repayment_main_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán khoản vay", name: 'capp_repayment.repayment_main_title');
    return result != '' ? result : 'repayment_main_title';
  }

  String get repaymentMainPaymentAmount {
    if(showKeys){
      return 'capp_repayment.repayment_main_payment_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_payment_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán", name: 'capp_repayment.repayment_main_payment_amount');
    return result != '' ? result : 'repayment_main_payment_amount';
  }

  String get repaymentMainCustomAmount {
    if(showKeys){
      return 'capp_repayment.repayment_main_custom_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_custom_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tùy chọn", name: 'capp_repayment.repayment_main_custom_amount');
    return result != '' ? result : 'repayment_main_custom_amount';
  }

  String get repaymentMainCustomAmountDescription {
    if(showKeys){
      return 'capp_repayment.repayment_main_custom_amount_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_custom_amount_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán số tiền tùy chỉnh", name: 'capp_repayment.repayment_main_custom_amount_description');
    return result != '' ? result : 'repayment_main_custom_amount_description';
  }

  String get repaymentMainCustomAmountHeader {
    if(showKeys){
      return 'capp_repayment.repayment_main_custom_amount_header';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_custom_amount_header', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhập số tiền tùy chọn", name: 'capp_repayment.repayment_main_custom_amount_header');
    return result != '' ? result : 'repayment_main_custom_amount_header';
  }

  String get repaymentMainTotalPaymentAmount {
    if(showKeys){
      return 'capp_repayment.repayment_main_total_payment_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_total_payment_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền thanh toán", name: 'capp_repayment.repayment_main_total_payment_amount');
    return result != '' ? result : 'repayment_main_total_payment_amount';
  }

  String get repaymentMainProceed {
    if(showKeys){
      return 'capp_repayment.repayment_main_proceed';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_proceed', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục thanh toán", name: 'capp_repayment.repayment_main_proceed');
    return result != '' ? result : 'repayment_main_proceed';
  }

  String get repaymentPayBefore {
    if(showKeys){
      return 'capp_repayment.repayment_pay_before';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_before', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán trước", name: 'capp_repayment.repayment_pay_before');
    return result != '' ? result : 'repayment_pay_before';
  }

  String get repaymentChange {
    if(showKeys){
      return 'capp_repayment.repayment_change';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_change', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thay đổi", name: 'capp_repayment.repayment_change');
    return result != '' ? result : 'repayment_change';
  }

  String get repaymentSelect {
    if(showKeys){
      return 'capp_repayment.repayment_select';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn", name: 'capp_repayment.repayment_select');
    return result != '' ? result : 'repayment_select';
  }

  String get repaymentSelectLoan {
    if(showKeys){
      return 'capp_repayment.repayment_select_loan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn khoản vay", name: 'capp_repayment.repayment_select_loan');
    return result != '' ? result : 'repayment_select_loan';
  }

  String get repaymentOverdue {
    if(showKeys){
      return 'capp_repayment.repayment_overdue';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_overdue', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quá hạn", name: 'capp_repayment.repayment_overdue');
    return result != '' ? result : 'repayment_overdue';
  }

  String get repaymentMyLoans {
    if(showKeys){
      return 'capp_repayment.repayment_my_loans';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_my_loans', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay của tôi", name: 'capp_repayment.repayment_my_loans');
    return result != '' ? result : 'repayment_my_loans';
  }

  String get repaymentContractHasBeenUpdatedBackToHomeMessage {
    if(showKeys){
      return 'capp_repayment.repayment_contract_has_been_updated_back_to_home_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_has_been_updated_back_to_home_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hợp đồng này có cập nhật mới. Vui lòng quay lại trang chủ", name: 'capp_repayment.repayment_contract_has_been_updated_back_to_home_message');
    return result != '' ? result : 'repayment_contract_has_been_updated_back_to_home_message';
  }

  String get repaymentContractHasBeenUpdatedSelectNextContractMessage {
    if(showKeys){
      return 'capp_repayment.repayment_contract_has_been_updated_select_next_contract_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_has_been_updated_select_next_contract_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hợp đồng này hiện đang không thể thanh toán. Vui lòng bấm OK để xem hợp đồng khác", name: 'capp_repayment.repayment_contract_has_been_updated_select_next_contract_message');
    return result != '' ? result : 'repayment_contract_has_been_updated_select_next_contract_message';
  }

  String get repaymentOk {
    if(showKeys){
      return 'capp_repayment.repayment_ok';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ok', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("OK", name: 'capp_repayment.repayment_ok');
    return result != '' ? result : 'repayment_ok';
  }

  String get repaymentOnlinePaymentViaVnpay {
    if(showKeys){
      return 'capp_repayment.repayment_online_payment_via_vnpay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_payment_via_vnpay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán qua VNPAY", name: 'capp_repayment.repayment_online_payment_via_vnpay');
    return result != '' ? result : 'repayment_online_payment_via_vnpay';
  }

  String get repaymentOnlinePaymentViaOnepay {
    if(showKeys){
      return 'capp_repayment.repayment_online_payment_via_onepay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_payment_via_onepay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán qua Onepay", name: 'capp_repayment.repayment_online_payment_via_onepay');
    return result != '' ? result : 'repayment_online_payment_via_onepay';
  }

  String get repaymentOnlineTransferDirectly {
    if(showKeys){
      return 'capp_repayment.repayment_online_transfer_directly';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_transfer_directly', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sử dụng dịch vụ chuyển khoản trực tuyến của Ngân Hàng", name: 'capp_repayment.repayment_online_transfer_directly');
    return result != '' ? result : 'repayment_online_transfer_directly';
  }

  String get repaymentChooseYourPaymentMethod {
    if(showKeys){
      return 'capp_repayment.repayment_choose_your_payment_method';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_choose_your_payment_method', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn phương thức thanh toán của bạn", name: 'capp_repayment.repayment_choose_your_payment_method');
    return result != '' ? result : 'repayment_choose_your_payment_method';
  }

  String get repaymentMobileBankingDesc {
    if(showKeys){
      return 'capp_repayment.repayment_mobile_banking_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mobile_banking_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mở ứng dụng để chuyển khoản", name: 'capp_repayment.repayment_mobile_banking_desc');
    return result != '' ? result : 'repayment_mobile_banking_desc';
  }

  String get repaymentVirtualAccount {
    if(showKeys){
      return 'capp_repayment.repayment_virtual_account';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_virtual_account', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản ngân hàng", name: 'capp_repayment.repayment_virtual_account');
    return result != '' ? result : 'repayment_virtual_account';
  }

  String get repaymentVirtualAccountDesc {
    if(showKeys){
      return 'capp_repayment.repayment_virtual_account_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_virtual_account_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán bằng chuyển khoản ngân hàng", name: 'capp_repayment.repayment_virtual_account_desc');
    return result != '' ? result : 'repayment_virtual_account_desc';
  }

  String get repaymentMainDueAmount {
    if(showKeys){
      return 'capp_repayment.repayment_main_due_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_main_due_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền đến hạn", name: 'capp_repayment.repayment_main_due_amount');
    return result != '' ? result : 'repayment_main_due_amount';
  }

  String get repaymentMobileBankingApps {
    if(showKeys){
      return 'capp_repayment.repayment_mobile_banking_apps';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mobile_banking_apps', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ứng dụng ngân hàng", name: 'capp_repayment.repayment_mobile_banking_apps');
    return result != '' ? result : 'repayment_mobile_banking_apps';
  }

  String get repaymentMobileBankingApp {
    if(showKeys){
      return 'capp_repayment.repayment_mobile_banking_app';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_mobile_banking_app', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ứng dụng ngân hàng", name: 'capp_repayment.repayment_mobile_banking_app');
    return result != '' ? result : 'repayment_mobile_banking_app';
  }

  String get repaymentPaymentViaVnpay {
    if(showKeys){
      return 'capp_repayment.repayment_payment_via_vnpay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_via_vnpay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán qua VNPAY", name: 'capp_repayment.repayment_payment_via_vnpay');
    return result != '' ? result : 'repayment_payment_via_vnpay';
  }

  String get repaymentPaymentViaOnepay {
    if(showKeys){
      return 'capp_repayment.repayment_payment_via_onepay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_via_onepay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nhập số thẻ ATM để thanh toán", name: 'capp_repayment.repayment_payment_via_onepay');
    return result != '' ? result : 'repayment_payment_via_onepay';
  }

  String get repaymentAtmCard {
    if(showKeys){
      return 'capp_repayment.repayment_atm_card';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_atm_card', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ ATM/Tài khoản ngân hàng", name: 'capp_repayment.repayment_atm_card');
    return result != '' ? result : 'repayment_atm_card';
  }

  String get repaymentAtmCardHeader {
    if(showKeys){
      return 'capp_repayment.repayment_atm_card_header';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_atm_card_header', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ ATM", name: 'capp_repayment.repayment_atm_card_header');
    return result != '' ? result : 'repayment_atm_card_header';
  }

  String get repaymentMethod {
    if(showKeys){
      return 'capp_repayment.repayment_method';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_method', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phương thức thanh toán", name: 'capp_repayment.repayment_method');
    return result != '' ? result : 'repayment_method';
  }

  String get repaymentInternetBanking {
    if(showKeys){
      return 'capp_repayment.repayment_internet_banking';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_internet_banking', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngân hàng trực tuyến", name: 'capp_repayment.repayment_internet_banking');
    return result != '' ? result : 'repayment_internet_banking';
  }

  String get repaymentBankTransferHeader {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_header';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_header', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản", name: 'capp_repayment.repayment_bank_transfer_header');
    return result != '' ? result : 'repayment_bank_transfer_header';
  }

  String get repaymentBackToHomepage {
    if(showKeys){
      return 'capp_repayment.repayment_back_to_homepage';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_back_to_homepage', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trở về trang chủ", name: 'capp_repayment.repayment_back_to_homepage');
    return result != '' ? result : 'repayment_back_to_homepage';
  }

  String get repaymentSelectAmountRelMinimumAmountValidationTitle {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_rel_minimum_amount_validation_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_rel_minimum_amount_validation_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn muốn thanh toán số tiền này?", name: 'capp_repayment.repayment_select_amount_rel_minimum_amount_validation_title');
    return result != '' ? result : 'repayment_select_amount_rel_minimum_amount_validation_title';
  }

  String get repaymentSelectAmountRelBnplMinimumAmountValidationTitle {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_rel_bnpl_minimum_amount_validation_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_rel_bnpl_minimum_amount_validation_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền đã chọn nhỏ hơn dư nợ cuối kỳ", name: 'capp_repayment.repayment_select_amount_rel_bnpl_minimum_amount_validation_title');
    return result != '' ? result : 'repayment_select_amount_rel_bnpl_minimum_amount_validation_title';
  }

  String get repaymentSelectAmountDueAmountValidationTitle {
    if(showKeys){
      return 'capp_repayment.repayment_select_amount_due_amount_validation_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_amount_due_amount_validation_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền đã chọn nhỏ hơn tổng số tiền đến hạn", name: 'capp_repayment.repayment_select_amount_due_amount_validation_title');
    return result != '' ? result : 'repayment_select_amount_due_amount_validation_title';
  }

  String get continuePayment {
    if(showKeys){
      return 'capp_repayment.continue_payment';
    }
    var ot = overridenTranslation(
        'capp_repayment.continue_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tiếp tục thanh toán", name: 'capp_repayment.continue_payment');
    return result != '' ? result : 'continue_payment';
  }

  String get gotIt {
    if(showKeys){
      return 'capp_repayment.got_it';
    }
    var ot = overridenTranslation(
        'capp_repayment.got_it', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã hiểu", name: 'capp_repayment.got_it');
    return result != '' ? result : 'got_it';
  }

  String get repaymentPayViaBankTransferIsNotAvailableMes {
    if(showKeys){
      return 'capp_repayment.repayment_pay_via_bank_transfer_is_not_available_mes';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_via_bank_transfer_is_not_available_mes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Các hình thức chuyển khoản hiện không khả dụng", name: 'capp_repayment.repayment_pay_via_bank_transfer_is_not_available_mes');
    return result != '' ? result : 'repayment_pay_via_bank_transfer_is_not_available_mes';
  }

  String get repaymentPayViaBankTransferIsNotAvailableTitle {
    if(showKeys){
      return 'capp_repayment.repayment_pay_via_bank_transfer_is_not_available_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_via_bank_transfer_is_not_available_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phương thức thanh toán không khả dụng", name: 'capp_repayment.repayment_pay_via_bank_transfer_is_not_available_title');
    return result != '' ? result : 'repayment_pay_via_bank_transfer_is_not_available_title';
  }

  String get transferToAccountNumber {
    if(showKeys){
      return 'capp_repayment.transfer_to_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.transfer_to_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản đến số tài khoản", name: 'capp_repayment.transfer_to_account_number');
    return result != '' ? result : 'transfer_to_account_number';
  }

  String get scanQrCode {
    if(showKeys){
      return 'capp_repayment.scan_qr_code';
    }
    var ot = overridenTranslation(
        'capp_repayment.scan_qr_code', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quét mã QR", name: 'capp_repayment.scan_qr_code');
    return result != '' ? result : 'scan_qr_code';
  }

  String get repaymentContractInformationNormal {
    if(showKeys){
      return 'capp_repayment.repayment_contract_information_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_contract_information_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin Hợp đồng", name: 'capp_repayment.repayment_contract_information_normal');
    return result != '' ? result : 'repayment_contract_information_normal';
  }

  String get repaymentTotalAmountNormal {
    if(showKeys){
      return 'capp_repayment.repayment_total_amount_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_total_amount_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền", name: 'capp_repayment.repayment_total_amount_normal');
    return result != '' ? result : 'repayment_total_amount_normal';
  }

  String get repaymentDueDateNormal {
    if(showKeys){
      return 'capp_repayment.repayment_due_date_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_due_date_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày đến hạn", name: 'capp_repayment.repayment_due_date_normal');
    return result != '' ? result : 'repayment_due_date_normal';
  }

  String get repaymentAccountNameNormal {
    if(showKeys){
      return 'capp_repayment.repayment_account_name_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_account_name_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tên tài khoản", name: 'capp_repayment.repayment_account_name_normal');
    return result != '' ? result : 'repayment_account_name_normal';
  }

  String get repaymentBankNameNormal {
    if(showKeys){
      return 'capp_repayment.repayment_bank_name_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_name_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tên ngân hàng", name: 'capp_repayment.repayment_bank_name_normal');
    return result != '' ? result : 'repayment_bank_name_normal';
  }

  String get repaymentBankBranchNormal {
    if(showKeys){
      return 'capp_repayment.repayment_bank_branch_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_branch_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi nhánh ngân hàng", name: 'capp_repayment.repayment_bank_branch_normal');
    return result != '' ? result : 'repayment_bank_branch_normal';
  }

  String get repaymentVirtualAccountNumberNormal {
    if(showKeys){
      return 'capp_repayment.repayment_virtual_account_number_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_virtual_account_number_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tài khoản ảo", name: 'capp_repayment.repayment_virtual_account_number_normal');
    return result != '' ? result : 'repayment_virtual_account_number_normal';
  }

  String get repaymentPleaseChooseYourBankingApp {
    if(showKeys){
      return 'capp_repayment.repayment_please_choose_your_banking_app';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_choose_your_banking_app', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng chọn ứng dụng ngân hàng bạn đang sử dụng trong điện thoại của bạn", name: 'capp_repayment.repayment_please_choose_your_banking_app');
    return result != '' ? result : 'repayment_please_choose_your_banking_app';
  }

  String get introduction {
    if(showKeys){
      return 'capp_repayment.introduction';
    }
    var ot = overridenTranslation(
        'capp_repayment.introduction', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giới thiệu", name: 'capp_repayment.introduction');
    return result != '' ? result : 'introduction';
  }

  String get whatIsPromiseToPay {
    if(showKeys){
      return 'capp_repayment.what_is_promise_to_pay';
    }
    var ot = overridenTranslation(
        'capp_repayment.what_is_promise_to_pay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoãn ngày thanh toán là gì?", name: 'capp_repayment.what_is_promise_to_pay');
    return result != '' ? result : 'what_is_promise_to_pay';
  }

  String get promiseToPayDescription {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoãn ngày thanh toán khi khách hàng chưa có khả năng thanh toán vào ngày đến hạn", name: 'capp_repayment.promise_to_pay_description');
    return result != '' ? result : 'promise_to_pay_description';
  }

  String get detail {
    if(showKeys){
      return 'capp_repayment.detail';
    }
    var ot = overridenTranslation(
        'capp_repayment.detail', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết", name: 'capp_repayment.detail');
    return result != '' ? result : 'detail';
  }

  String get promiseToPayDetailTitle1 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_title1';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_title1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn cần đóng tiền lãi dựa trên tổng dư nợ cần thanh toán", name: 'capp_repayment.promise_to_pay_detail_title1');
    return result != '' ? result : 'promise_to_pay_detail_title1';
  }

  String get promiseToPayDetailMessage1 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_message1';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_message1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn sẽ chỉ được miễn phí phạt do thanh toán trễ hạn", name: 'capp_repayment.promise_to_pay_detail_message1');
    return result != '' ? result : 'promise_to_pay_detail_message1';
  }

  String get promiseToPayDetailTitle2 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_title2';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_title2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không gọi điện nhắc thanh toán", name: 'capp_repayment.promise_to_pay_detail_title2');
    return result != '' ? result : 'promise_to_pay_detail_title2';
  }

  String get promiseToPayDetailMessage2 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_message2';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_message2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chúng tôi sẽ không nhắc bạn thanh toán trong thời gian mà bạn đã hoãn", name: 'capp_repayment.promise_to_pay_detail_message2');
    return result != '' ? result : 'promise_to_pay_detail_message2';
  }

  String get promiseToPayDetailTitle3 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_title3';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_title3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thời gian hoãn tối đa 4 ngày", name: 'capp_repayment.promise_to_pay_detail_title3');
    return result != '' ? result : 'promise_to_pay_detail_title3';
  }

  String get promiseToPayDetailMessage3 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_message3';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_message3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn có thể dời tối đa 4 ngày cho 1 lần hoãn thanh toán", name: 'capp_repayment.promise_to_pay_detail_message3');
    return result != '' ? result : 'promise_to_pay_detail_message3';
  }

  String get promiseToPayDetailTitle4 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_title4';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_title4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thao tác nhanh chóng", name: 'capp_repayment.promise_to_pay_detail_title4');
    return result != '' ? result : 'promise_to_pay_detail_title4';
  }

  String get promiseToPayDetailMessage4 {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_detail_message4';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_detail_message4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác nhận ngay sau khi hoàn thành, không cần chờ xét duyệt", name: 'capp_repayment.promise_to_pay_detail_message4');
    return result != '' ? result : 'promise_to_pay_detail_message4';
  }

  String get repaymentSend {
    if(showKeys){
      return 'capp_repayment.repayment_send';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_send', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Gửi", name: 'capp_repayment.repayment_send');
    return result != '' ? result : 'repayment_send';
  }

  String get repaymentSelectPaymentDate {
    if(showKeys){
      return 'capp_repayment.repayment_select_payment_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_select_payment_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn ngày thanh toán", name: 'capp_repayment.repayment_select_payment_date');
    return result != '' ? result : 'repayment_select_payment_date';
  }

  String get repaymentPaymentPlan {
    if(showKeys){
      return 'capp_repayment.repayment_payment_plan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_plan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kế hoạch thanh toán", name: 'capp_repayment.repayment_payment_plan');
    return result != '' ? result : 'repayment_payment_plan';
  }

  String get repaymentPleaseCommitAsSelected {
    if(showKeys){
      return 'capp_repayment.repayment_please_commit_as_selected';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_commit_as_selected', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng cam kết thanh toán như ngày đã chọn", name: 'capp_repayment.repayment_please_commit_as_selected');
    return result != '' ? result : 'repayment_please_commit_as_selected';
  }

  String get repaymentDayLater {
    if(showKeys){
      return 'capp_repayment.repayment_day_later';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_day_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("ngày sau", name: 'capp_repayment.repayment_day_later');
    return result != '' ? result : 'repayment_day_later';
  }

  String get repaymentDaysLater {
    if(showKeys){
      return 'capp_repayment.repayment_days_later';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_days_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("ngày sau", name: 'capp_repayment.repayment_days_later');
    return result != '' ? result : 'repayment_days_later';
  }

  String get repaymentCurrentDueDate {
    if(showKeys){
      return 'capp_repayment.repayment_current_due_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_current_due_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày đến hạn hiện tại", name: 'capp_repayment.repayment_current_due_date');
    return result != '' ? result : 'repayment_current_due_date';
  }

  String get repaymentExtendUtilDate {
    if(showKeys){
      return 'capp_repayment.repayment_extend_util_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_extend_util_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Gia hạn đến ngày", name: 'capp_repayment.repayment_extend_util_date');
    return result != '' ? result : 'repayment_extend_util_date';
  }

  String get repaymentMinPaymentAmountIs {
    if(showKeys){
      return 'capp_repayment.repayment_min_payment_amount_is';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_min_payment_amount_is', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền hoãn thanh toán tối thiểu là", name: 'capp_repayment.repayment_min_payment_amount_is');
    return result != '' ? result : 'repayment_min_payment_amount_is';
  }

  String get repaymentWantToExtendPaymentDate {
    if(showKeys){
      return 'capp_repayment.repayment_want_to_extend_payment_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_want_to_extend_payment_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn muốn hoãn ngày thanh toán?", name: 'capp_repayment.repayment_want_to_extend_payment_date');
    return result != '' ? result : 'repayment_want_to_extend_payment_date';
  }

  String get repaymentYouNeedToCommitPaymentAsSelectedDate {
    if(showKeys){
      return 'capp_repayment.repayment_you_need_to_commit_payment_as_selected_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_you_need_to_commit_payment_as_selected_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn cần cam kết thanh toán như ngày đã chọn. Nếu thanh toán trễ, bạn phải chịu phí phạt và điểm tín dụng của bạn  sẽ bị ảnh hưởng xấu", name: 'capp_repayment.repayment_you_need_to_commit_payment_as_selected_date');
    return result != '' ? result : 'repayment_you_need_to_commit_payment_as_selected_date';
  }

  String get repaymentIAgree {
    if(showKeys){
      return 'capp_repayment.repayment_i_agree';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_i_agree', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tôi đồng ý", name: 'capp_repayment.repayment_i_agree');
    return result != '' ? result : 'repayment_i_agree';
  }

  String get repaymentCancel {
    if(showKeys){
      return 'capp_repayment.repayment_cancel';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Huỷ bỏ", name: 'capp_repayment.repayment_cancel');
    return result != '' ? result : 'repayment_cancel';
  }

  String get repaymentPtpProcessingTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_processing_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_processing_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng chờ trong giây lát", name: 'capp_repayment.repayment_ptp_processing_title');
    return result != '' ? result : 'repayment_ptp_processing_title';
  }

  String get repaymentPtpProcessingSubTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_processing_sub_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_processing_sub_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hệ thống đang xử lý yêu cầu của bạn", name: 'capp_repayment.repayment_ptp_processing_sub_title');
    return result != '' ? result : 'repayment_ptp_processing_sub_title';
  }

  String get repaymentPtpProcessBackButton {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_process_back_button';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_process_back_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trở về", name: 'capp_repayment.repayment_ptp_process_back_button');
    return result != '' ? result : 'repayment_ptp_process_back_button';
  }

  String get promiseToPay {
    if(showKeys){
      return 'capp_repayment.promise_to_pay';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoãn ngày thanh toán", name: 'capp_repayment.promise_to_pay');
    return result != '' ? result : 'promise_to_pay';
  }

  String promiseToPayEarlyMessage(dynamic days) {
  if(showKeys){
      return 'capp_repayment.promise_to_pay_early_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_early_message', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("${days} ngày nữa sẽ đến kỳ thanh toán của bạn", name: 'capp_repayment.promise_to_pay_early_message', args: [days]);
  }

  String promiseToPayLateMessage(dynamic days) {
  if(showKeys){
      return 'capp_repayment.promise_to_pay_late_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_late_message', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Đã trễ hạn thanh toán ${days} ngày", name: 'capp_repayment.promise_to_pay_late_message', args: [days]);
  }

  String get promiseToPayOnTimeMessage {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_on_time_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_on_time_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã đến kỳ thanh toán của bạn", name: 'capp_repayment.promise_to_pay_on_time_message');
    return result != '' ? result : 'promise_to_pay_on_time_message';
  }

  String get promiseToPayRemindMessage {
    if(showKeys){
      return 'capp_repayment.promise_to_pay_remind_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.promise_to_pay_remind_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng thanh toán đúng hạn để tránh bị phát sinh phí phạt", name: 'capp_repayment.promise_to_pay_remind_message');
    return result != '' ? result : 'promise_to_pay_remind_message';
  }

  String get repaymentTotalDueAmountNormal {
    if(showKeys){
      return 'capp_repayment.repayment_total_due_amount_normal';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_total_due_amount_normal', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền đến hạn", name: 'capp_repayment.repayment_total_due_amount_normal');
    return result != '' ? result : 'repayment_total_due_amount_normal';
  }

  String get repaymentPtpSuccessTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_success_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_success_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kế hoạch thanh toán", name: 'capp_repayment.repayment_ptp_success_title');
    return result != '' ? result : 'repayment_ptp_success_title';
  }

  String get repaymentPtpSuccessMainTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_success_main_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_success_main_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn đã hoãn ngày thanh toán thành công", name: 'capp_repayment.repayment_ptp_success_main_title');
    return result != '' ? result : 'repayment_ptp_success_main_title';
  }

  String get repaymentPtpSuccessMainDes {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_success_main_des';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_success_main_des', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Việc hoãn ngày thanh toán này chỉ áp dụng cho kì thanh toán gần nhất", name: 'capp_repayment.repayment_ptp_success_main_des');
    return result != '' ? result : 'repayment_ptp_success_main_des';
  }

  String get repaymentPtpSuccessDetailDueDate {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_success_detail_due_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_success_detail_due_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Gia hạn đến ngày", name: 'capp_repayment.repayment_ptp_success_detail_due_date');
    return result != '' ? result : 'repayment_ptp_success_detail_due_date';
  }

  String get repaymentPtpSuccessDetailAmount {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_success_detail_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_success_detail_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán", name: 'capp_repayment.repayment_ptp_success_detail_amount');
    return result != '' ? result : 'repayment_ptp_success_detail_amount';
  }

  String get repaymentPtpSuccessDoneButton {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_success_done_button';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_success_done_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trở về", name: 'capp_repayment.repayment_ptp_success_done_button');
    return result != '' ? result : 'repayment_ptp_success_done_button';
  }

  String get repaymentCcOutstandingPeriodAmount {
    if(showKeys){
      return 'capp_repayment.repayment_cc_outstanding_period_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_cc_outstanding_period_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Dư nợ cuối kỳ", name: 'capp_repayment.repayment_cc_outstanding_period_amount');
    return result != '' ? result : 'repayment_cc_outstanding_period_amount';
  }

  String get repaymentPtpEligibilityCheckingTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_eligibility_checking_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_eligibility_checking_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoãn ngày thanh toán", name: 'capp_repayment.repayment_ptp_eligibility_checking_title');
    return result != '' ? result : 'repayment_ptp_eligibility_checking_title';
  }

  String get repaymentPtpEligibilityPopupTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_eligibility_popup_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_eligibility_popup_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chưa đủ điều kiện để gia hạn thanh toán", name: 'capp_repayment.repayment_ptp_eligibility_popup_title');
    return result != '' ? result : 'repayment_ptp_eligibility_popup_title';
  }

  String get repaymentPtpEligibilityPopupISeeButton {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_eligibility_popup_i_see_button';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_eligibility_popup_i_see_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã hiểu", name: 'capp_repayment.repayment_ptp_eligibility_popup_i_see_button');
    return result != '' ? result : 'repayment_ptp_eligibility_popup_i_see_button';
  }

  String get promotion {
    if(showKeys){
      return 'capp_repayment.promotion';
    }
    var ot = overridenTranslation(
        'capp_repayment.promotion', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi", name: 'capp_repayment.promotion');
    return result != '' ? result : 'promotion';
  }

  String get select {
    if(showKeys){
      return 'capp_repayment.select';
    }
    var ot = overridenTranslation(
        'capp_repayment.select', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn", name: 'capp_repayment.select');
    return result != '' ? result : 'select';
  }

  String get change {
    if(showKeys){
      return 'capp_repayment.change';
    }
    var ot = overridenTranslation(
        'capp_repayment.change', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thay đổi", name: 'capp_repayment.change');
    return result != '' ? result : 'change';
  }

  String get selectVoucherToGetDiscount {
    if(showKeys){
      return 'capp_repayment.select_voucher_to_get_discount';
    }
    var ot = overridenTranslation(
        'capp_repayment.select_voucher_to_get_discount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn thẻ giảm giá để nhận được ưu đãi", name: 'capp_repayment.select_voucher_to_get_discount');
    return result != '' ? result : 'select_voucher_to_get_discount';
  }

  String get voucherIsNotApplicable {
    if(showKeys){
      return 'capp_repayment.voucher_is_not_applicable';
    }
    var ot = overridenTranslation(
        'capp_repayment.voucher_is_not_applicable', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không thể áp dụng thẻ giảm giá này", name: 'capp_repayment.voucher_is_not_applicable');
    return result != '' ? result : 'voucher_is_not_applicable';
  }

  String get repaymentViewDetails {
    if(showKeys){
      return 'capp_repayment.repayment_view_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_view_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem chi tiết", name: 'capp_repayment.repayment_view_details');
    return result != '' ? result : 'repayment_view_details';
  }

  String get repaymentMinRepayment {
    if(showKeys){
      return 'capp_repayment.repayment_min_repayment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_min_repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tối thiểu", name: 'capp_repayment.repayment_min_repayment');
    return result != '' ? result : 'repayment_min_repayment';
  }

  String get repaymentPromotion {
    if(showKeys){
      return 'capp_repayment.repayment_promotion';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi", name: 'capp_repayment.repayment_promotion');
    return result != '' ? result : 'repayment_promotion';
  }

  String get repaymentApply {
    if(showKeys){
      return 'capp_repayment.repayment_apply';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_apply', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Áp dụng", name: 'capp_repayment.repayment_apply');
    return result != '' ? result : 'repayment_apply';
  }

  String get repaymentUseNow {
    if(showKeys){
      return 'capp_repayment.repayment_use_now';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_use_now', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Áp dụng ngay", name: 'capp_repayment.repayment_use_now');
    return result != '' ? result : 'repayment_use_now';
  }

  String get repaymentUseLater {
    if(showKeys){
      return 'capp_repayment.repayment_use_later';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_use_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Áp dụng sau", name: 'capp_repayment.repayment_use_later');
    return result != '' ? result : 'repayment_use_later';
  }

  String get repaymentVoucherDetails {
    if(showKeys){
      return 'capp_repayment.repayment_voucher_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_voucher_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chi tiết", name: 'capp_repayment.repayment_voucher_details');
    return result != '' ? result : 'repayment_voucher_details';
  }

  String get repaymentThisVoucherIsApplied {
    if(showKeys){
      return 'capp_repayment.repayment_this_voucher_is_applied';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_this_voucher_is_applied', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đang áp dụng thẻ giảm giá này", name: 'capp_repayment.repayment_this_voucher_is_applied');
    return result != '' ? result : 'repayment_this_voucher_is_applied';
  }

  String get repaymentPtpPopupRetryTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_popup_retry_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_popup_retry_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã có lỗi xảy ra, vui lòng thử lại sau", name: 'capp_repayment.repayment_ptp_popup_retry_title');
    return result != '' ? result : 'repayment_ptp_popup_retry_title';
  }

  String get repaymentPtpPopupRetryRetryButton {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_popup_retry_retry_button';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_popup_retry_retry_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thử lại", name: 'capp_repayment.repayment_ptp_popup_retry_retry_button');
    return result != '' ? result : 'repayment_ptp_popup_retry_retry_button';
  }

  String get repaymentPtpPopupRetryCancelButton {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_popup_retry_cancel_button';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_popup_retry_cancel_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trở về", name: 'capp_repayment.repayment_ptp_popup_retry_cancel_button');
    return result != '' ? result : 'repayment_ptp_popup_retry_cancel_button';
  }

  String get repaymentSorryWeDontHaveAnyVoucher {
    if(showKeys){
      return 'capp_repayment.repayment_sorry_we_dont_have_any_voucher';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_sorry_we_dont_have_any_voucher', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn chưa có ưu đãi nào", name: 'capp_repayment.repayment_sorry_we_dont_have_any_voucher');
    return result != '' ? result : 'repayment_sorry_we_dont_have_any_voucher';
  }

  String get repaymentPtpSubmitError {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_submit_error';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_submit_error', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Yêu cầu của bạn không thể thực hiện. Vui lòng thử lại!", name: 'capp_repayment.repayment_ptp_submit_error');
    return result != '' ? result : 'repayment_ptp_submit_error';
  }

  String get repaymentVoucherNotApplicable {
    if(showKeys){
      return 'capp_repayment.repayment_voucher_not_applicable';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_voucher_not_applicable', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Không thể áp dụng thẻ giảm giá", name: 'capp_repayment.repayment_voucher_not_applicable');
    return result != '' ? result : 'repayment_voucher_not_applicable';
  }

  String get repaymentYourVoucherIsNotApplicable {
    if(showKeys){
      return 'capp_repayment.repayment_your_voucher_is_not_applicable';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_your_voucher_is_not_applicable', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ giảm giá của bạn không khả dụng", name: 'capp_repayment.repayment_your_voucher_is_not_applicable');
    return result != '' ? result : 'repayment_your_voucher_is_not_applicable';
  }

  String get repaymentSkipVoucherAndContinue {
    if(showKeys){
      return 'capp_repayment.repayment_skip_voucher_and_continue';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_skip_voucher_and_continue', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bỏ qua ưu đãi và tiếp tục", name: 'capp_repayment.repayment_skip_voucher_and_continue');
    return result != '' ? result : 'repayment_skip_voucher_and_continue';
  }

  String get repaymentSkipVoucherAndContinueQuestion {
    if(showKeys){
      return 'capp_repayment.repayment_skip_voucher_and_continue_question';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_skip_voucher_and_continue_question', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bỏ qua ưu đãi và tiếp tục?", name: 'capp_repayment.repayment_skip_voucher_and_continue_question');
    return result != '' ? result : 'repayment_skip_voucher_and_continue_question';
  }

  String get repaymentSeeOtherVouchers {
    if(showKeys){
      return 'capp_repayment.repayment_see_other_vouchers';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_see_other_vouchers', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem ưu đãi khác", name: 'capp_repayment.repayment_see_other_vouchers');
    return result != '' ? result : 'repayment_see_other_vouchers';
  }

  String get repaymentPtpRejectedTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_rejected_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_rejected_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Kế hoạch thanh toán", name: 'capp_repayment.repayment_ptp_rejected_title');
    return result != '' ? result : 'repayment_ptp_rejected_title';
  }

  String get repaymentPtpRejectedMainTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_rejected_main_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_rejected_main_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hoãn ngày thanh toán thất bại", name: 'capp_repayment.repayment_ptp_rejected_main_title');
    return result != '' ? result : 'repayment_ptp_rejected_main_title';
  }

  String get repaymentPtpRejectedMainDes {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_rejected_main_des';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_rejected_main_des', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng thanh toán đúng hạn để tránh bị phát sinh phí phạt", name: 'capp_repayment.repayment_ptp_rejected_main_des');
    return result != '' ? result : 'repayment_ptp_rejected_main_des';
  }

  String get repaymentPtpRejectedDetailDueDate {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_rejected_detail_due_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_rejected_detail_due_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày đến hạn", name: 'capp_repayment.repayment_ptp_rejected_detail_due_date');
    return result != '' ? result : 'repayment_ptp_rejected_detail_due_date';
  }

  String get repaymentPtpRejectedDetailAmount {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_rejected_detail_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_rejected_detail_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền đến hạn", name: 'capp_repayment.repayment_ptp_rejected_detail_amount');
    return result != '' ? result : 'repayment_ptp_rejected_detail_amount';
  }

  String get repaymentPtpRejectedDoneButton {
    if(showKeys){
      return 'capp_repayment.repayment_ptp_rejected_done_button';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ptp_rejected_done_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trở về", name: 'capp_repayment.repayment_ptp_rejected_done_button');
    return result != '' ? result : 'repayment_ptp_rejected_done_button';
  }

  String get paymentAmount {
    if(showKeys){
      return 'capp_repayment.payment_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.payment_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền", name: 'capp_repayment.payment_amount');
    return result != '' ? result : 'payment_amount';
  }

  String get repaymentDiscountAmount {
    if(showKeys){
      return 'capp_repayment.repayment_discount_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_discount_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giảm giá", name: 'capp_repayment.repayment_discount_amount');
    return result != '' ? result : 'repayment_discount_amount';
  }

  String get repaymentFinalAmount {
    if(showKeys){
      return 'capp_repayment.repayment_final_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_final_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền cần thanh toán", name: 'capp_repayment.repayment_final_amount');
    return result != '' ? result : 'repayment_final_amount';
  }

  String get repaymentRefresh {
    if(showKeys){
      return 'capp_repayment.repayment_refresh';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_refresh', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải lại", name: 'capp_repayment.repayment_refresh');
    return result != '' ? result : 'repayment_refresh';
  }

  String get repaymentSomethingWentWrongPleaseTryAgain {
    if(showKeys){
      return 'capp_repayment.repayment_something_went_wrong_please_try_again';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_something_went_wrong_please_try_again', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có vấn đề đã xảy ra. Vui lòng thử lại", name: 'capp_repayment.repayment_something_went_wrong_please_try_again');
    return result != '' ? result : 'repayment_something_went_wrong_please_try_again';
  }

  String get repaymentCancelPayment {
    if(showKeys){
      return 'capp_repayment.repayment_cancel_payment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_cancel_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn muốn dừng thanh toán khoản vay?", name: 'capp_repayment.repayment_cancel_payment');
    return result != '' ? result : 'repayment_cancel_payment';
  }

  String get repaymentCancelPaymentMessage {
    if(showKeys){
      return 'capp_repayment.repayment_cancel_payment_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_cancel_payment_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán ngay để tránh trễ hạn khoản vay của bạn", name: 'capp_repayment.repayment_cancel_payment_message');
    return result != '' ? result : 'repayment_cancel_payment_message';
  }

  String get repaymentQuit {
    if(showKeys){
      return 'capp_repayment.repayment_quit';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_quit', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thoát", name: 'capp_repayment.repayment_quit');
    return result != '' ? result : 'repayment_quit';
  }

  String get repaymentDescription {
    if(showKeys){
      return 'capp_repayment.repayment_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mô tả", name: 'capp_repayment.repayment_description');
    return result != '' ? result : 'repayment_description';
  }

  String get repaymentDismissOffer {
    if(showKeys){
      return 'capp_repayment.repayment_dismiss_offer';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_dismiss_offer', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn muốn bỏ qua ưu đãi?", name: 'capp_repayment.repayment_dismiss_offer');
    return result != '' ? result : 'repayment_dismiss_offer';
  }

  String get repaymentDownloadQrCode {
    if(showKeys){
      return 'capp_repayment.repayment_download_qr_code';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_download_qr_code', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải mã QR", name: 'capp_repayment.repayment_download_qr_code');
    return result != '' ? result : 'repayment_download_qr_code';
  }

  String get repaymentDownloadTheQrCode {
    if(showKeys){
      return 'capp_repayment.repayment_download_the_qr_code';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_download_the_qr_code', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Download QR", name: 'capp_repayment.repayment_download_the_qr_code');
    return result != '' ? result : 'repayment_download_the_qr_code';
  }

  String get repaymentToApply {
    if(showKeys){
      return 'capp_repayment.repayment_to_apply';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_to_apply', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("để áp dụng mã ưu đãi, hoặc huỷ ưu đãi nếu bạn chưa muốn áp dụng mã ưu đãi này", name: 'capp_repayment.repayment_to_apply');
    return result != '' ? result : 'repayment_to_apply';
  }

  String get repaymentDismissPromo {
    if(showKeys){
      return 'capp_repayment.repayment_dismiss_promo';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_dismiss_promo', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sử dụng sau", name: 'capp_repayment.repayment_dismiss_promo');
    return result != '' ? result : 'repayment_dismiss_promo';
  }

  String get repaymentPromotionNotApplicable {
    if(showKeys){
      return 'capp_repayment.repayment_promotion_not_applicable';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion_not_applicable', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi giảm giá không được áp dụng cho phương thức này", name: 'capp_repayment.repayment_promotion_not_applicable');
    return result != '' ? result : 'repayment_promotion_not_applicable';
  }

  String get repaymentTransferToAccountNumber {
    if(showKeys){
      return 'capp_repayment.repayment_transfer_to_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_transfer_to_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán qua chuyển khoản", name: 'capp_repayment.repayment_transfer_to_account_number');
    return result != '' ? result : 'repayment_transfer_to_account_number';
  }

  String get repaymentPleaseScanQrToApplyPromotion {
    if(showKeys){
      return 'capp_repayment.repayment_please_scan_qr_to_apply_promotion';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_scan_qr_to_apply_promotion', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng quét QR thanh toán trong 60 phút để hưởng ưu đãi", name: 'capp_repayment.repayment_please_scan_qr_to_apply_promotion');
    return result != '' ? result : 'repayment_please_scan_qr_to_apply_promotion';
  }

  String get repaymentQrDiscount {
    if(showKeys){
      return 'capp_repayment.repayment_qr_discount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_qr_discount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi", name: 'capp_repayment.repayment_qr_discount');
    return result != '' ? result : 'repayment_qr_discount';
  }

  String get repaymentCancelVoucherIfNotApply {
    if(showKeys){
      return 'capp_repayment.repayment_cancel_voucher_if_not_apply';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_cancel_voucher_if_not_apply', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Huỷ ưu đãi nếu bạn chưa muốn áp dụng mã ưu đãi này", name: 'capp_repayment.repayment_cancel_voucher_if_not_apply');
    return result != '' ? result : 'repayment_cancel_voucher_if_not_apply';
  }

  String get repaymentSkipPromotionDownloadQr {
    if(showKeys){
      return 'capp_repayment.repayment_skip_promotion_download_qr';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_skip_promotion_download_qr', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Download QR", name: 'capp_repayment.repayment_skip_promotion_download_qr');
    return result != '' ? result : 'repayment_skip_promotion_download_qr';
  }

  String get repaymentSkipPromotionAndPayment {
    if(showKeys){
      return 'capp_repayment.repayment_skip_promotion_and_payment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_skip_promotion_and_payment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("và thanh toán trong", name: 'capp_repayment.repayment_skip_promotion_and_payment');
    return result != '' ? result : 'repayment_skip_promotion_and_payment';
  }

  String get repaymentSkipPromotion60Min {
    if(showKeys){
      return 'capp_repayment.repayment_skip_promotion_60_min';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_skip_promotion_60_min', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("60 phút", name: 'capp_repayment.repayment_skip_promotion_60_min');
    return result != '' ? result : 'repayment_skip_promotion_60_min';
  }

  String get repaymentSkipPromotionToApplyVoucherCancel {
    if(showKeys){
      return 'capp_repayment.repayment_skip_promotion_to_apply_voucher_cancel';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_skip_promotion_to_apply_voucher_cancel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("để áp dụng ưu đãi\n\nHoặc Sử dụng sau để bỏ qua", name: 'capp_repayment.repayment_skip_promotion_to_apply_voucher_cancel');
    return result != '' ? result : 'repayment_skip_promotion_to_apply_voucher_cancel';
  }

  String get repaymentSuccessful {
    if(showKeys){
      return 'capp_repayment.repayment_successful';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_successful', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán thành công", name: 'capp_repayment.repayment_successful');
    return result != '' ? result : 'repayment_successful';
  }

  String get repaymentPleaseWaitFewMinutes {
    if(showKeys){
      return 'capp_repayment.repayment_please_wait_few_minutes';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_please_wait_few_minutes', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng chờ ít phút để cập nhật thông tin thanh toán", name: 'capp_repayment.repayment_please_wait_few_minutes');
    return result != '' ? result : 'repayment_please_wait_few_minutes';
  }

  String get repaymentAmount {
    if(showKeys){
      return 'capp_repayment.repayment_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán", name: 'capp_repayment.repayment_amount');
    return result != '' ? result : 'repayment_amount';
  }

  String get repaymentTotal {
    if(showKeys){
      return 'capp_repayment.repayment_total';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_total', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng số tiền thanh toán ", name: 'capp_repayment.repayment_total');
    return result != '' ? result : 'repayment_total';
  }

  String get repaymentTransactionInformation {
    if(showKeys){
      return 'capp_repayment.repayment_transaction_information';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_transaction_information', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin giao dịch", name: 'capp_repayment.repayment_transaction_information');
    return result != '' ? result : 'repayment_transaction_information';
  }

  String get repaymentProcessingDate {
    if(showKeys){
      return 'capp_repayment.repayment_processing_date';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_processing_date', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày xử lý", name: 'capp_repayment.repayment_processing_date');
    return result != '' ? result : 'repayment_processing_date';
  }

  String get repaymentPaymentMethods {
    if(showKeys){
      return 'capp_repayment.repayment_payment_methods';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_methods', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phương thức thanh toán", name: 'capp_repayment.repayment_payment_methods');
    return result != '' ? result : 'repayment_payment_methods';
  }

  String get repaymentEwalletTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_title');
    return result != '' ? result : 'repayment_ewallet_title';
  }

  String get repaymentEwalletIntroduceTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_introduce_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_introduce_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_introduce_title');
    return result != '' ? result : 'repayment_ewallet_introduce_title';
  }

  String get repaymentEwalletIntroduceMessage {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_introduce_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_introduce_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_introduce_message');
    return result != '' ? result : 'repayment_ewallet_introduce_message';
  }

  String get repaymentEwalletAlfamart {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart');
    return result != '' ? result : 'repayment_ewallet_alfamart';
  }

  String get repaymentEwalletBca {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca');
    return result != '' ? result : 'repayment_ewallet_bca';
  }

  String get repaymentEwalletBni {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni');
    return result != '' ? result : 'repayment_ewallet_bni';
  }

  String get repaymentEwalletCimb {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb');
    return result != '' ? result : 'repayment_ewallet_cimb';
  }

  String get repaymentEwalletAtmBersamaPrima {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_atm_bersama_prima';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_atm_bersama_prima', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_atm_bersama_prima');
    return result != '' ? result : 'repayment_ewallet_atm_bersama_prima';
  }

  String get repaymentEwalletBersamaPrima {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_prima';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_prima', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_prima');
    return result != '' ? result : 'repayment_ewallet_bersama_prima';
  }

  String get repaymentEwalletAlfamartDescription {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart_description');
    return result != '' ? result : 'repayment_ewallet_alfamart_description';
  }

  String get repaymentEwalletPaymentGuideline {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_payment_guideline';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_payment_guideline', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_payment_guideline');
    return result != '' ? result : 'repayment_ewallet_payment_guideline';
  }

  String get repaymentEwalletAlfamartStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart_step1');
    return result != '' ? result : 'repayment_ewallet_alfamart_step1';
  }

  String get repaymentEwalletAlfamartStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart_step2');
    return result != '' ? result : 'repayment_ewallet_alfamart_step2';
  }

  String get repaymentEwalletAlfamartStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart_step3');
    return result != '' ? result : 'repayment_ewallet_alfamart_step3';
  }

  String get repaymentEwalletAlfamartStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart_step4');
    return result != '' ? result : 'repayment_ewallet_alfamart_step4';
  }

  String get repaymentEwalletAlfamartStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart_step5');
    return result != '' ? result : 'repayment_ewallet_alfamart_step5';
  }

  String get repaymentEwalletAlfamartStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_alfamart_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_alfamart_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_alfamart_step6');
    return result != '' ? result : 'repayment_ewallet_alfamart_step6';
  }

  String get repaymentEwalletVirtualAccountNumber {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_virtual_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_virtual_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_virtual_account_number');
    return result != '' ? result : 'repayment_ewallet_virtual_account_number';
  }

  String get repaymentEwalletAtm {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_atm';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_atm', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_atm');
    return result != '' ? result : 'repayment_ewallet_atm';
  }

  String get repaymentEwalletKlikBca {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_klik_bca';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_klik_bca', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_klik_bca');
    return result != '' ? result : 'repayment_ewallet_klik_bca';
  }

  String get repaymentEwalletMBca {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_m_bca';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_m_bca', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_m_bca');
    return result != '' ? result : 'repayment_ewallet_m_bca';
  }

  String get repaymentEwalletSmsBanking {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_sms_banking';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_sms_banking', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_sms_banking');
    return result != '' ? result : 'repayment_ewallet_sms_banking';
  }

  String get repaymentEwalletInternetBanking {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_internet_banking';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_internet_banking', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_internet_banking');
    return result != '' ? result : 'repayment_ewallet_internet_banking';
  }

  String get repaymentEwalletMBanking {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_m_banking';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_m_banking', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_m_banking');
    return result != '' ? result : 'repayment_ewallet_m_banking';
  }

  String get repaymentEwalletCimbClicks {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_clicks';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_clicks', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_clicks');
    return result != '' ? result : 'repayment_ewallet_cimb_clicks';
  }

  String get repaymentEwalletGoMobileCimb {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_go_mobile_cimb';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_go_mobile_cimb', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_go_mobile_cimb');
    return result != '' ? result : 'repayment_ewallet_go_mobile_cimb';
  }

  String get repaymentEwalletBcaAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_atm_step1');
    return result != '' ? result : 'repayment_ewallet_bca_atm_step1';
  }

  String get repaymentEwalletBcaAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_atm_step2');
    return result != '' ? result : 'repayment_ewallet_bca_atm_step2';
  }

  String get repaymentEwalletBcaAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_atm_step3');
    return result != '' ? result : 'repayment_ewallet_bca_atm_step3';
  }

  String get repaymentEwalletBcaAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_atm_step4');
    return result != '' ? result : 'repayment_ewallet_bca_atm_step4';
  }

  String get repaymentEwalletBcaAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_atm_step5');
    return result != '' ? result : 'repayment_ewallet_bca_atm_step5';
  }

  String get repaymentEwalletBcaKlikBcaStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_klik_bca_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_klik_bca_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_klik_bca_step1');
    return result != '' ? result : 'repayment_ewallet_bca_klik_bca_step1';
  }

  String get repaymentEwalletBcaKlikBcaStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_klik_bca_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_klik_bca_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_klik_bca_step2');
    return result != '' ? result : 'repayment_ewallet_bca_klik_bca_step2';
  }

  String get repaymentEwalletBcaKlikBcaStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_klik_bca_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_klik_bca_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_klik_bca_step3');
    return result != '' ? result : 'repayment_ewallet_bca_klik_bca_step3';
  }

  String get repaymentEwalletBcaKlikBcaStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_klik_bca_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_klik_bca_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_klik_bca_step4');
    return result != '' ? result : 'repayment_ewallet_bca_klik_bca_step4';
  }

  String get repaymentEwalletBcaKlikBcaStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_klik_bca_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_klik_bca_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_klik_bca_step5');
    return result != '' ? result : 'repayment_ewallet_bca_klik_bca_step5';
  }

  String get repaymentEwalletBcaMBcaStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_m_bca_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_m_bca_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_m_bca_step1');
    return result != '' ? result : 'repayment_ewallet_bca_m_bca_step1';
  }

  String get repaymentEwalletBcaMBcaStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_m_bca_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_m_bca_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_m_bca_step2');
    return result != '' ? result : 'repayment_ewallet_bca_m_bca_step2';
  }

  String get repaymentEwalletBcaMBcaStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_m_bca_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_m_bca_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_m_bca_step3');
    return result != '' ? result : 'repayment_ewallet_bca_m_bca_step3';
  }

  String get repaymentEwalletBcaMBcaStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_m_bca_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_m_bca_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_m_bca_step4');
    return result != '' ? result : 'repayment_ewallet_bca_m_bca_step4';
  }

  String get repaymentEwalletBcaMBcaStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_m_bca_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_m_bca_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_m_bca_step5');
    return result != '' ? result : 'repayment_ewallet_bca_m_bca_step5';
  }

  String get repaymentEwalletBcaMBcaStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bca_m_bca_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bca_m_bca_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bca_m_bca_step6');
    return result != '' ? result : 'repayment_ewallet_bca_m_bca_step6';
  }

  String get repaymentEwalletBniAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_atm_step1');
    return result != '' ? result : 'repayment_ewallet_bni_atm_step1';
  }

  String get repaymentEwalletBniAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_atm_step2');
    return result != '' ? result : 'repayment_ewallet_bni_atm_step2';
  }

  String get repaymentEwalletBniAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_atm_step3');
    return result != '' ? result : 'repayment_ewallet_bni_atm_step3';
  }

  String get repaymentEwalletBniAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_atm_step4');
    return result != '' ? result : 'repayment_ewallet_bni_atm_step4';
  }

  String get repaymentEwalletBniAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_atm_step5');
    return result != '' ? result : 'repayment_ewallet_bni_atm_step5';
  }

  String get repaymentEwalletBniSmsBankingStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_sms_banking_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_sms_banking_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_sms_banking_step1');
    return result != '' ? result : 'repayment_ewallet_bni_sms_banking_step1';
  }

  String get repaymentEwalletBniSmsBankingStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_sms_banking_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_sms_banking_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_sms_banking_step2');
    return result != '' ? result : 'repayment_ewallet_bni_sms_banking_step2';
  }

  String get repaymentEwalletBniInternetBankingStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_internet_banking_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_internet_banking_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_internet_banking_step1');
    return result != '' ? result : 'repayment_ewallet_bni_internet_banking_step1';
  }

  String get repaymentEwalletBniInternetBankingStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_internet_banking_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_internet_banking_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_internet_banking_step2');
    return result != '' ? result : 'repayment_ewallet_bni_internet_banking_step2';
  }

  String get repaymentEwalletBniInternetBankingStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_internet_banking_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_internet_banking_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_internet_banking_step3');
    return result != '' ? result : 'repayment_ewallet_bni_internet_banking_step3';
  }

  String get repaymentEwalletBniInternetBankingStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_internet_banking_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_internet_banking_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_internet_banking_step4');
    return result != '' ? result : 'repayment_ewallet_bni_internet_banking_step4';
  }

  String get repaymentEwalletBniInternetBankingStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_internet_banking_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_internet_banking_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_internet_banking_step5');
    return result != '' ? result : 'repayment_ewallet_bni_internet_banking_step5';
  }

  String get repaymentEwalletBniInternetBankingStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_internet_banking_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_internet_banking_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_internet_banking_step6');
    return result != '' ? result : 'repayment_ewallet_bni_internet_banking_step6';
  }

  String get repaymentEwalletBniMBankingStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_m_banking_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_m_banking_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_m_banking_step1');
    return result != '' ? result : 'repayment_ewallet_bni_m_banking_step1';
  }

  String get repaymentEwalletBniMBankingStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_m_banking_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_m_banking_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_m_banking_step2');
    return result != '' ? result : 'repayment_ewallet_bni_m_banking_step2';
  }

  String get repaymentEwalletBniMBankingStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_m_banking_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_m_banking_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_m_banking_step3');
    return result != '' ? result : 'repayment_ewallet_bni_m_banking_step3';
  }

  String get repaymentEwalletBniMBankingStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_m_banking_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_m_banking_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_m_banking_step4');
    return result != '' ? result : 'repayment_ewallet_bni_m_banking_step4';
  }

  String get repaymentEwalletBniMBankingStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_m_banking_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_m_banking_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_m_banking_step5');
    return result != '' ? result : 'repayment_ewallet_bni_m_banking_step5';
  }

  String get repaymentEwalletBniMBankingStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bni_m_banking_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bni_m_banking_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bni_m_banking_step6');
    return result != '' ? result : 'repayment_ewallet_bni_m_banking_step6';
  }

  String get repaymentEwalletCimbAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_atm_step1');
    return result != '' ? result : 'repayment_ewallet_cimb_atm_step1';
  }

  String get repaymentEwalletCimbAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_atm_step2');
    return result != '' ? result : 'repayment_ewallet_cimb_atm_step2';
  }

  String get repaymentEwalletCimbAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_atm_step3');
    return result != '' ? result : 'repayment_ewallet_cimb_atm_step3';
  }

  String get repaymentEwalletCimbAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_atm_step4');
    return result != '' ? result : 'repayment_ewallet_cimb_atm_step4';
  }

  String get repaymentEwalletCimbAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_atm_step5');
    return result != '' ? result : 'repayment_ewallet_cimb_atm_step5';
  }

  String get repaymentEwalletCimbClicksStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_clicks_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_clicks_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_clicks_step1');
    return result != '' ? result : 'repayment_ewallet_cimb_clicks_step1';
  }

  String get repaymentEwalletCimbClicksStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_clicks_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_clicks_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_clicks_step2');
    return result != '' ? result : 'repayment_ewallet_cimb_clicks_step2';
  }

  String get repaymentEwalletCimbClicksStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_clicks_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_clicks_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_clicks_step3');
    return result != '' ? result : 'repayment_ewallet_cimb_clicks_step3';
  }

  String get repaymentEwalletCimbClicksStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_clicks_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_clicks_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_clicks_step4');
    return result != '' ? result : 'repayment_ewallet_cimb_clicks_step4';
  }

  String get repaymentEwalletCimbClicksStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_clicks_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_clicks_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_clicks_step5');
    return result != '' ? result : 'repayment_ewallet_cimb_clicks_step5';
  }

  String get repaymentEwalletCimbClicksStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_clicks_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_clicks_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_clicks_step6');
    return result != '' ? result : 'repayment_ewallet_cimb_clicks_step6';
  }

  String get repaymentEwalletCimbGoMobileStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_go_mobile_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_go_mobile_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_go_mobile_step1');
    return result != '' ? result : 'repayment_ewallet_cimb_go_mobile_step1';
  }

  String get repaymentEwalletCimbGoMobileStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_go_mobile_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_go_mobile_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_go_mobile_step2');
    return result != '' ? result : 'repayment_ewallet_cimb_go_mobile_step2';
  }

  String get repaymentEwalletCimbGoMobileStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_go_mobile_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_go_mobile_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_go_mobile_step3');
    return result != '' ? result : 'repayment_ewallet_cimb_go_mobile_step3';
  }

  String get repaymentEwalletCimbGoMobileStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_go_mobile_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_go_mobile_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_go_mobile_step4');
    return result != '' ? result : 'repayment_ewallet_cimb_go_mobile_step4';
  }

  String get repaymentEwalletCimbGoMobileStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_go_mobile_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_go_mobile_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_go_mobile_step5');
    return result != '' ? result : 'repayment_ewallet_cimb_go_mobile_step5';
  }

  String get repaymentEwalletCimbGoMobileStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_cimb_go_mobile_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_cimb_go_mobile_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_cimb_go_mobile_step6');
    return result != '' ? result : 'repayment_ewallet_cimb_go_mobile_step6';
  }

  String get repaymentEwalletBersamaAtmStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_atm_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_atm_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_atm_step1');
    return result != '' ? result : 'repayment_ewallet_bersama_atm_step1';
  }

  String get repaymentEwalletBersamaAtmStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_atm_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_atm_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_atm_step2');
    return result != '' ? result : 'repayment_ewallet_bersama_atm_step2';
  }

  String get repaymentEwalletBersamaAtmStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_atm_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_atm_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_atm_step3');
    return result != '' ? result : 'repayment_ewallet_bersama_atm_step3';
  }

  String get repaymentEwalletBersamaAtmStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_atm_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_atm_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_atm_step4');
    return result != '' ? result : 'repayment_ewallet_bersama_atm_step4';
  }

  String get repaymentEwalletBersamaAtmStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_atm_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_atm_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_atm_step5');
    return result != '' ? result : 'repayment_ewallet_bersama_atm_step5';
  }

  String get repaymentEwalletBersamaInternetBankingStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_internet_banking_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_internet_banking_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_internet_banking_step1');
    return result != '' ? result : 'repayment_ewallet_bersama_internet_banking_step1';
  }

  String get repaymentEwalletBersamaInternetBankingStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_internet_banking_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_internet_banking_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_internet_banking_step2');
    return result != '' ? result : 'repayment_ewallet_bersama_internet_banking_step2';
  }

  String get repaymentEwalletBersamaInternetBankingStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_internet_banking_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_internet_banking_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_internet_banking_step3');
    return result != '' ? result : 'repayment_ewallet_bersama_internet_banking_step3';
  }

  String get repaymentEwalletBersamaInternetBankingStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_internet_banking_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_internet_banking_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_internet_banking_step4');
    return result != '' ? result : 'repayment_ewallet_bersama_internet_banking_step4';
  }

  String get repaymentEwalletBersamaInternetBankingStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_internet_banking_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_internet_banking_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_internet_banking_step5');
    return result != '' ? result : 'repayment_ewallet_bersama_internet_banking_step5';
  }

  String get repaymentEwalletBersamaInternetBankingStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_internet_banking_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_internet_banking_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_internet_banking_step6');
    return result != '' ? result : 'repayment_ewallet_bersama_internet_banking_step6';
  }

  String get repaymentEwalletBersamaMBankingStep1 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_m_banking_step1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_m_banking_step1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_m_banking_step1');
    return result != '' ? result : 'repayment_ewallet_bersama_m_banking_step1';
  }

  String get repaymentEwalletBersamaMBankingStep2 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_m_banking_step2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_m_banking_step2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_m_banking_step2');
    return result != '' ? result : 'repayment_ewallet_bersama_m_banking_step2';
  }

  String get repaymentEwalletBersamaMBankingStep3 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_m_banking_step3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_m_banking_step3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_m_banking_step3');
    return result != '' ? result : 'repayment_ewallet_bersama_m_banking_step3';
  }

  String get repaymentEwalletBersamaMBankingStep4 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_m_banking_step4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_m_banking_step4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_m_banking_step4');
    return result != '' ? result : 'repayment_ewallet_bersama_m_banking_step4';
  }

  String get repaymentEwalletBersamaMBankingStep5 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_m_banking_step5';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_m_banking_step5', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_m_banking_step5');
    return result != '' ? result : 'repayment_ewallet_bersama_m_banking_step5';
  }

  String get repaymentEwalletBersamaMBankingStep6 {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_bersama_m_banking_step6';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_bersama_m_banking_step6', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_bersama_m_banking_step6');
    return result != '' ? result : 'repayment_ewallet_bersama_m_banking_step6';
  }

  String get repaymentEwalletNoPhoneNumberTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_no_phone_number_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_no_phone_number_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_no_phone_number_title');
    return result != '' ? result : 'repayment_ewallet_no_phone_number_title';
  }

  String get repaymentEwalletNoPhoneNumberMessage {
    if(showKeys){
      return 'capp_repayment.repayment_ewallet_no_phone_number_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ewallet_no_phone_number_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_ewallet_no_phone_number_message');
    return result != '' ? result : 'repayment_ewallet_no_phone_number_message';
  }

  String get repaymentSuccessLoanOffersSectionTitle {
    if(showKeys){
      return 'capp_repayment.repayment_success_loan_offers_section_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_success_loan_offers_section_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ưu đãi cho bạn", name: 'capp_repayment.repayment_success_loan_offers_section_title');
    return result != '' ? result : 'repayment_success_loan_offers_section_title';
  }

  String get repaymentMomo {
    if(showKeys){
      return 'capp_repayment.repayment_momo';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_momo', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ví MoMo", name: 'capp_repayment.repayment_momo');
    return result != '' ? result : 'repayment_momo';
  }

  String get repaymentOnlineRepayment {
    if(showKeys){
      return 'capp_repayment.repayment_online_repayment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_online_repayment');
    return result != '' ? result : 'repayment_online_repayment';
  }

  String get repaymentEnjoyQuickEasyOnlineRepayment {
    if(showKeys){
      return 'capp_repayment.repayment_enjoy_quick_easy_online_repayment';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_enjoy_quick_easy_online_repayment', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_enjoy_quick_easy_online_repayment');
    return result != '' ? result : 'repayment_enjoy_quick_easy_online_repayment';
  }

  String get repaymentWhyShouldIPayOnline {
    if(showKeys){
      return 'capp_repayment.repayment_why_should_i_pay_online';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_why_should_i_pay_online', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_why_should_i_pay_online');
    return result != '' ? result : 'repayment_why_should_i_pay_online';
  }

  String get repaymentPayOnlineAnywhere {
    if(showKeys){
      return 'capp_repayment.repayment_pay_online_anywhere';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_online_anywhere', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pay_online_anywhere');
    return result != '' ? result : 'repayment_pay_online_anywhere';
  }

  String get repaymentConnectAccounts {
    if(showKeys){
      return 'capp_repayment.repayment_connect_accounts';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_connect_accounts', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_connect_accounts');
    return result != '' ? result : 'repayment_connect_accounts';
  }

  String get repaymentFastAndConvenient {
    if(showKeys){
      return 'capp_repayment.repayment_fast_and_convenient';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_fast_and_convenient', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_fast_and_convenient');
    return result != '' ? result : 'repayment_fast_and_convenient';
  }

  String get repaymentWhatAccountICanUse {
    if(showKeys){
      return 'capp_repayment.repayment_what_account_i_can_use';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_what_account_i_can_use', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_what_account_i_can_use');
    return result != '' ? result : 'repayment_what_account_i_can_use';
  }

  String get repaymentOnlineBankingAccountsDes {
    if(showKeys){
      return 'capp_repayment.repayment_online_banking_accounts_des';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_banking_accounts_des', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_online_banking_accounts_des');
    return result != '' ? result : 'repayment_online_banking_accounts_des';
  }

  String get repaymentDebitCardsDes {
    if(showKeys){
      return 'capp_repayment.repayment_debit_cards_des';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_debit_cards_des', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_debit_cards_des');
    return result != '' ? result : 'repayment_debit_cards_des';
  }

  String get repaymentOnlineBankTitle {
    if(showKeys){
      return 'capp_repayment.repayment_online_bank_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_bank_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_online_bank_title');
    return result != '' ? result : 'repayment_online_bank_title';
  }

  String get repaymentOnlineBankDesc {
    if(showKeys){
      return 'capp_repayment.repayment_online_bank_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_online_bank_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_online_bank_desc');
    return result != '' ? result : 'repayment_online_bank_desc';
  }

  String get repaymentDebitCardTitle {
    if(showKeys){
      return 'capp_repayment.repayment_debit_card_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_debit_card_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_debit_card_title');
    return result != '' ? result : 'repayment_debit_card_title';
  }

  String get repaymentDebitCardDesc {
    if(showKeys){
      return 'capp_repayment.repayment_debit_card_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_debit_card_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_debit_card_desc');
    return result != '' ? result : 'repayment_debit_card_desc';
  }

  String get repaymentNoFee {
    if(showKeys){
      return 'capp_repayment.repayment_no_fee';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_no_fee', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_no_fee');
    return result != '' ? result : 'repayment_no_fee';
  }

  String get repaymentChoosePaymentOption {
    if(showKeys){
      return 'capp_repayment.repayment_choose_payment_option';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_choose_payment_option', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_choose_payment_option');
    return result != '' ? result : 'repayment_choose_payment_option';
  }

  String get repaymentYourOrder {
    if(showKeys){
      return 'capp_repayment.repayment_your_order';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_your_order', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_your_order');
    return result != '' ? result : 'repayment_your_order';
  }

  String get repaymentAdaIntroEasyToRepay {
    if(showKeys){
      return 'capp_repayment.repayment_ada_intro_easy_to_repay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_intro_easy_to_repay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán khoản vay dễ dàng và đúng hạn", name: 'capp_repayment.repayment_ada_intro_easy_to_repay');
    return result != '' ? result : 'repayment_ada_intro_easy_to_repay';
  }

  String get repaymentAdaIntroFirstTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_intro_first_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_intro_first_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sử dụng nguồn tiền từ tài khoản ngân hàng hoặc ví điện tử", name: 'capp_repayment.repayment_ada_intro_first_title');
    return result != '' ? result : 'repayment_ada_intro_first_title';
  }

  String get repaymentAdaIntroFirstSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_intro_first_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_intro_first_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thủ tục đăng ký đơn giản, nhanh chóng", name: 'capp_repayment.repayment_ada_intro_first_subtitle');
    return result != '' ? result : 'repayment_ada_intro_first_subtitle';
  }

  String get repaymentAdaIntroSecondTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_intro_second_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_intro_second_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tự động thanh toán", name: 'capp_repayment.repayment_ada_intro_second_title');
    return result != '' ? result : 'repayment_ada_intro_second_title';
  }

  String get repaymentAdaIntroSecondSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_intro_second_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_intro_second_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản trả góp hàng tháng của bạn sẽ được tự động thanh toán vào trước ngày đến hạn 1 ngày", name: 'capp_repayment.repayment_ada_intro_second_subtitle');
    return result != '' ? result : 'repayment_ada_intro_second_subtitle';
  }

  String get repaymentAdaIntroThirdTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_intro_third_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_intro_third_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bảo mật tài khoản", name: 'capp_repayment.repayment_ada_intro_third_title');
    return result != '' ? result : 'repayment_ada_intro_third_title';
  }

  String get repaymentAdaIntroThirdSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_intro_third_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_intro_third_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin tài khoản thanh toán của bạn được bảo mật và an toàn tuyệt đối", name: 'capp_repayment.repayment_ada_intro_third_subtitle');
    return result != '' ? result : 'repayment_ada_intro_third_subtitle';
  }

  String get repaymentAdaNoActiveAdaTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_no_active_ada_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_no_active_ada_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn chưa đăng ký thanh toán tự động", name: 'capp_repayment.repayment_ada_no_active_ada_title');
    return result != '' ? result : 'repayment_ada_no_active_ada_title';
  }

  String get repaymentAdaNoActiveAdaSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_no_active_ada_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_no_active_ada_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tự động giúp bạn luôn thanh toán đúng hạn và tránh khỏi phí trả chậm hoặc nợ xấu", name: 'capp_repayment.repayment_ada_no_active_ada_subtitle');
    return result != '' ? result : 'repayment_ada_no_active_ada_subtitle';
  }

  String get repaymentAdaEnroll {
    if(showKeys){
      return 'capp_repayment.repayment_ada_enroll';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_enroll', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thêm thanh toán tự động", name: 'capp_repayment.repayment_ada_enroll');
    return result != '' ? result : 'repayment_ada_enroll';
  }

  String get repaymentAdaPaymentViaMomo {
    if(showKeys){
      return 'capp_repayment.repayment_ada_payment_via_momo';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_payment_via_momo', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán qua ví Momo", name: 'capp_repayment.repayment_ada_payment_via_momo');
    return result != '' ? result : 'repayment_ada_payment_via_momo';
  }

  String get repaymentAdaEnrollSuccess {
    if(showKeys){
      return 'capp_repayment.repayment_ada_enroll_success';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_enroll_success', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng ký thành công", name: 'capp_repayment.repayment_ada_enroll_success');
    return result != '' ? result : 'repayment_ada_enroll_success';
  }

  String get repaymentAdaEnrollSuccessDescription {
    if(showKeys){
      return 'capp_repayment.repayment_ada_enroll_success_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_enroll_success_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Cảm ơn Quý khách đã đăng ký Thanh toán tự động. Tính năng sẽ được kích hoạt sau 1 ngày.", name: 'capp_repayment.repayment_ada_enroll_success_description');
    return result != '' ? result : 'repayment_ada_enroll_success_description';
  }

  String get repaymentAdaContractType {
    if(showKeys){
      return 'capp_repayment.repayment_ada_contract_type';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_contract_type', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay", name: 'capp_repayment.repayment_ada_contract_type');
    return result != '' ? result : 'repayment_ada_contract_type';
  }

  String get repaymentAdaPaymentMethod {
    if(showKeys){
      return 'capp_repayment.repayment_ada_payment_method';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_payment_method', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Phương thức thanh toán", name: 'capp_repayment.repayment_ada_payment_method');
    return result != '' ? result : 'repayment_ada_payment_method';
  }

  String get repaymentAdaDueDateCel {
    if(showKeys){
      return 'capp_repayment.repayment_ada_due_date_cel';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_due_date_cel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày thanh toán", name: 'capp_repayment.repayment_ada_due_date_cel');
    return result != '' ? result : 'repayment_ada_due_date_cel';
  }

  String get repaymentAdaDueDateRel {
    if(showKeys){
      return 'capp_repayment.repayment_ada_due_date_rel';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_due_date_rel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày thanh toán", name: 'capp_repayment.repayment_ada_due_date_rel');
    return result != '' ? result : 'repayment_ada_due_date_rel';
  }

  String get repaymentAdaAmount {
    if(showKeys){
      return 'capp_repayment.repayment_ada_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền", name: 'capp_repayment.repayment_ada_amount');
    return result != '' ? result : 'repayment_ada_amount';
  }

  String get repaymentAdaAmountLimitWarning {
    if(showKeys){
      return 'capp_repayment.repayment_ada_amount_limit_warning';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_amount_limit_warning', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tùy vào quy định của ngân hàng / Ví điện tử tại từng thời điểm, khoản thanh toán của Quý khách sẽ được chia nhỏ thành nhiều lần thanh toán khác nhau.", name: 'capp_repayment.repayment_ada_amount_limit_warning');
    return result != '' ? result : 'repayment_ada_amount_limit_warning';
  }

  String get repaymentDone {
    if(showKeys){
      return 'capp_repayment.repayment_done';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_done', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xong", name: 'capp_repayment.repayment_done');
    return result != '' ? result : 'repayment_done';
  }

  String get repaymentAdaMainTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tự động", name: 'capp_repayment.repayment_ada_main_title');
    return result != '' ? result : 'repayment_ada_main_title';
  }

  String get repaymentAdaMainAutoDeductDateCel {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_auto_deduct_date_cel';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_auto_deduct_date_cel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày thanh toán", name: 'capp_repayment.repayment_ada_main_auto_deduct_date_cel');
    return result != '' ? result : 'repayment_ada_main_auto_deduct_date_cel';
  }

  String get repaymentAdaMainAutoDeductDateRel {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_auto_deduct_date_rel';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_auto_deduct_date_rel', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày thanh toán", name: 'capp_repayment.repayment_ada_main_auto_deduct_date_rel');
    return result != '' ? result : 'repayment_ada_main_auto_deduct_date_rel';
  }

  String get repaymentAdaMainAutoDeductAmount {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_auto_deduct_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_auto_deduct_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán tự động", name: 'capp_repayment.repayment_ada_main_auto_deduct_amount');
    return result != '' ? result : 'repayment_ada_main_auto_deduct_amount';
  }

  String get repaymentAdaMainMinAmountDue {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_min_amount_due';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_min_amount_due', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền tối thiểu đến hạn ", name: 'capp_repayment.repayment_ada_main_min_amount_due');
    return result != '' ? result : 'repayment_ada_main_min_amount_due';
  }

  String get repaymentAdaMainPayMadNote {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_pay_mad_note';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_pay_mad_note', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán số tiền tối thiểu giúp bạn không bị phát sinh phí phạt và nợ xấu. Dư nợ còn lại tính lãi suất theo quy định của thẻ.", name: 'capp_repayment.repayment_ada_main_pay_mad_note');
    return result != '' ? result : 'repayment_ada_main_pay_mad_note';
  }

  String get repaymentAdaMainPaymentMethodDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_payment_method_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_payment_method_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vui lòng đăng ký trước hạn ít nhất 2 ngày và đảm bảo tài khoản đủ số dư để thanh toán tự động đúng hạn", name: 'capp_repayment.repayment_ada_main_payment_method_desc');
    return result != '' ? result : 'repayment_ada_main_payment_method_desc';
  }

  String get repaymentAdaMainDueDateRelDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_due_date_rel_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_due_date_rel_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("14 ngày kể từ ngày sao kê", name: 'capp_repayment.repayment_ada_main_due_date_rel_desc');
    return result != '' ? result : 'repayment_ada_main_due_date_rel_desc';
  }

  String repaymentAdaMainDueDateCelDesc(dynamic dueDay) {
  if(showKeys){
      return 'capp_repayment.repayment_ada_main_due_date_cel_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_due_date_cel_desc', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Ngày ${dueDay} hàng tháng", name: 'capp_repayment.repayment_ada_main_due_date_cel_desc', args: [dueDay]);
  }

  String get repaymentAdaMainAutoDeductDateRelDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_auto_deduct_date_rel_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_auto_deduct_date_rel_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("1 ngày trước ngày đến hạn", name: 'capp_repayment.repayment_ada_main_auto_deduct_date_rel_desc');
    return result != '' ? result : 'repayment_ada_main_auto_deduct_date_rel_desc';
  }

  String get repaymentAdaMainDeduct {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_deduct';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_deduct', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Giảm", name: 'capp_repayment.repayment_ada_main_deduct');
    return result != '' ? result : 'repayment_ada_main_deduct';
  }

  String get repaymentAdaOnepay {
    if(showKeys){
      return 'capp_repayment.repayment_ada_onepay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_onepay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ ATM/Tài khoản ngân hàng", name: 'capp_repayment.repayment_ada_onepay');
    return result != '' ? result : 'repayment_ada_onepay';
  }

  String get repaymentAdaZalopay {
    if(showKeys){
      return 'capp_repayment.repayment_ada_zalopay';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_zalopay', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Zalopay", name: 'capp_repayment.repayment_ada_zalopay');
    return result != '' ? result : 'repayment_ada_zalopay';
  }

  String get repaymentAdaMainCancelAdaPopupTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_cancel_ada_popup_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_cancel_ada_popup_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn muốn xoá thanh toán tự động?", name: 'capp_repayment.repayment_ada_main_cancel_ada_popup_title');
    return result != '' ? result : 'repayment_ada_main_cancel_ada_popup_title';
  }

  String get repaymentAdaMainCancelAdaPopupDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_cancel_ada_popup_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_cancel_ada_popup_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tự động giúp bạn thanh toán đúng hạn, tránh khỏi phí phạt và nợ xấu", name: 'capp_repayment.repayment_ada_main_cancel_ada_popup_desc');
    return result != '' ? result : 'repayment_ada_main_cancel_ada_popup_desc';
  }

  String get repaymentDelete {
    if(showKeys){
      return 'capp_repayment.repayment_delete';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_delete', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xoá", name: 'capp_repayment.repayment_delete');
    return result != '' ? result : 'repayment_delete';
  }

  String get repaymentBack {
    if(showKeys){
      return 'capp_repayment.repayment_back';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_back', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quay lại", name: 'capp_repayment.repayment_back');
    return result != '' ? result : 'repayment_back';
  }

  String get repaymentAdaMainPaymentMethod {
    if(showKeys){
      return 'capp_repayment.repayment_ada_main_payment_method';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_main_payment_method', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản thanh toán", name: 'capp_repayment.repayment_ada_main_payment_method');
    return result != '' ? result : 'repayment_ada_main_payment_method';
  }

  String get serviceFee {
    if(showKeys){
      return 'capp_repayment.service_fee';
    }
    var ot = overridenTranslation(
        'capp_repayment.service_fee', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Service Fee", name: 'capp_repayment.service_fee');
    return result != '' ? result : 'service_fee';
  }

  String get repaymentSappiDesc {
    if(showKeys){
      return 'capp_repayment.repayment_sappi_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_sappi_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tiền thanh toán đã bao gồm tiền trả góp hàng tháng cho HĐ vay tiền mặt này và HĐ vay mua gói An Tâm Tài Chính", name: 'capp_repayment.repayment_sappi_desc');
    return result != '' ? result : 'repayment_sappi_desc';
  }

  String repaymentSappiDescWithContractNumber(dynamic sappiContractNumber) {
  if(showKeys){
      return 'capp_repayment.repayment_sappi_desc_with_contract_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_sappi_desc_with_contract_number', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Số tiền thanh toán đã bao gồm tiền trả góp hàng tháng cho HĐ vay tiền mặt này và HĐ vay mua gói An Tâm Tài Chính số ${sappiContractNumber}", name: 'capp_repayment.repayment_sappi_desc_with_contract_number', args: [sappiContractNumber]);
  }

  String get validOneYear {
    if(showKeys){
      return 'capp_repayment.valid_one_year';
    }
    var ot = overridenTranslation(
        'capp_repayment.valid_one_year', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Valid for 1 year", name: 'capp_repayment.valid_one_year');
    return result != '' ? result : 'valid_one_year';
  }

  String get validLoanDuration {
    if(showKeys){
      return 'capp_repayment.valid_loan_duration';
    }
    var ot = overridenTranslation(
        'capp_repayment.valid_loan_duration', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Valid for loan duration", name: 'capp_repayment.valid_loan_duration');
    return result != '' ? result : 'valid_loan_duration';
  }

  String get repaymentAdaCreditCard {
    if(showKeys){
      return 'capp_repayment.repayment_ada_credit_card';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_credit_card', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thẻ tín dụng", name: 'capp_repayment.repayment_ada_credit_card');
    return result != '' ? result : 'repayment_ada_credit_card';
  }

  String get repaymentAdaCashLoan {
    if(showKeys){
      return 'capp_repayment.repayment_ada_cash_loan';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_cash_loan', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Vay tiền mặt", name: 'capp_repayment.repayment_ada_cash_loan');
    return result != '' ? result : 'repayment_ada_cash_loan';
  }

  String get repaymentDueDateDesc {
    if(showKeys){
      return 'capp_repayment.repayment_due_date_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_due_date_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày 15 hàng tháng", name: 'capp_repayment.repayment_due_date_desc');
    return result != '' ? result : 'repayment_due_date_desc';
  }

  String get repaymentAdaDueDateCelDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_due_date_cel_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_due_date_cel_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngày 12 hàng tháng", name: 'capp_repayment.repayment_ada_due_date_cel_desc');
    return result != '' ? result : 'repayment_ada_due_date_cel_desc';
  }

  String get repaymentAdaDueDateRelDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_due_date_rel_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_due_date_rel_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("1 ngày trước ngày đến hạn", name: 'capp_repayment.repayment_ada_due_date_rel_desc');
    return result != '' ? result : 'repayment_ada_due_date_rel_desc';
  }

  String get repaymentAdaFailedRegister {
    if(showKeys){
      return 'capp_repayment.repayment_ada_failed_register';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_failed_register', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đăng ký thất bại", name: 'capp_repayment.repayment_ada_failed_register');
    return result != '' ? result : 'repayment_ada_failed_register';
  }

  String get repaymentAdaCelFailedRegisterDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_cel_failed_register_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_cel_failed_register_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khoản vay của bạn chưa được thanh toán tự động. Vui lòng thử lại.", name: 'capp_repayment.repayment_ada_cel_failed_register_desc');
    return result != '' ? result : 'repayment_ada_cel_failed_register_desc';
  }

  String get repaymentAdaCcFailedRegisterDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_cc_failed_register_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_cc_failed_register_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Dư nợ thẻ tín dụng của bạn chưa được thanh toán tự động. Vui lòng thử lại.", name: 'capp_repayment.repayment_ada_cc_failed_register_desc');
    return result != '' ? result : 'repayment_ada_cc_failed_register_desc';
  }

  String get repaymentAdaBnplFailedRegisterDesc {
    if(showKeys){
      return 'capp_repayment.repayment_ada_bnpl_failed_register_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_bnpl_failed_register_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản Home PayLater của bạn chưa được thanh toán tự động. Vui lòng thử lại.", name: 'capp_repayment.repayment_ada_bnpl_failed_register_desc');
    return result != '' ? result : 'repayment_ada_bnpl_failed_register_desc';
  }

  String get repaymentAdaBnplFailedRegisterDescNew {
    if(showKeys){
      return 'capp_repayment.repayment_ada_bnpl_failed_register_desc_new';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_bnpl_failed_register_desc_new', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tài khoản trả sau Home PayLater của bạn chưa được thanh toán tự động. Vui lòng thử lại.", name: 'capp_repayment.repayment_ada_bnpl_failed_register_desc_new');
    return result != '' ? result : 'repayment_ada_bnpl_failed_register_desc_new';
  }

  String repaymentPromotionFirstAdoptionTitle(dynamic voucher) {
  if(showKeys){
      return 'capp_repayment.repayment_promotion_first_adoption_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion_first_adoption_title', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Tặng bạn mã giảm giá ${voucher}", name: 'capp_repayment.repayment_promotion_first_adoption_title', args: [voucher]);
  }

  String repaymentPromotionFirstAdoptionSutitle(dynamic minAmount) {
  if(showKeys){
      return 'capp_repayment.repayment_promotion_first_adoption_sutitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion_first_adoption_sutitle', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Khi thanh toán khoản vay trên ứng dụng Home Credit với số tiền từ ${minAmount}", name: 'capp_repayment.repayment_promotion_first_adoption_sutitle', args: [minAmount]);
  }

  String get repaymentPromotionFirstAdoptionPos {
    if(showKeys){
      return 'capp_repayment.repayment_promotion_first_adoption_pos';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion_first_adoption_pos', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tại quầy", name: 'capp_repayment.repayment_promotion_first_adoption_pos');
    return result != '' ? result : 'repayment_promotion_first_adoption_pos';
  }

  String get repaymentPromotionFirstAdoptionApp {
    if(showKeys){
      return 'capp_repayment.repayment_promotion_first_adoption_app';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion_first_adoption_app', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán trên app", name: 'capp_repayment.repayment_promotion_first_adoption_app');
    return result != '' ? result : 'repayment_promotion_first_adoption_app';
  }

  String repaymentPromotionFirstAdoptionVoucher(dynamic voucher) {
  if(showKeys){
      return 'capp_repayment.repayment_promotion_first_adoption_voucher';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion_first_adoption_voucher', _locale.languageCode, _locale.countryCode);

    return ot ?? Intl.message("Giảm ${voucher}", name: 'capp_repayment.repayment_promotion_first_adoption_voucher', args: [voucher]);
  }

  String get repaymentPromotionFirstAdoptionButton {
    if(showKeys){
      return 'capp_repayment.repayment_promotion_first_adoption_button';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_promotion_first_adoption_button', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Lấy mã ngay", name: 'capp_repayment.repayment_promotion_first_adoption_button');
    return result != '' ? result : 'repayment_promotion_first_adoption_button';
  }

  String get repaymentBackLater {
    if(showKeys){
      return 'capp_repayment.repayment_back_later';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_back_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Quay lại sau", name: 'capp_repayment.repayment_back_later');
    return result != '' ? result : 'repayment_back_later';
  }

  String get repaymentPayWithAutoDebitArrangementTitle {
    if(showKeys){
      return 'capp_repayment.repayment_pay_with_auto_debit_arrangement_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_pay_with_auto_debit_arrangement_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_pay_with_auto_debit_arrangement_title');
    return result != '' ? result : 'repayment_pay_with_auto_debit_arrangement_title';
  }

  String get repaymentWeWilldeductYourLoanTitle {
    if(showKeys){
      return 'capp_repayment.repayment_we_willdeduct_your_loan_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_we_willdeduct_your_loan_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_we_willdeduct_your_loan_title');
    return result != '' ? result : 'repayment_we_willdeduct_your_loan_title';
  }

  String get repaymentMakeSureYouHaveEnoughFundsTitle {
    if(showKeys){
      return 'capp_repayment.repayment_make_sure_you_have_enough_funds_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_make_sure_you_have_enough_funds_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_make_sure_you_have_enough_funds_title');
    return result != '' ? result : 'repayment_make_sure_you_have_enough_funds_title';
  }

  String get repaymentCardDetails {
    if(showKeys){
      return 'capp_repayment.repayment_card_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_card_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_card_details');
    return result != '' ? result : 'repayment_card_details';
  }

  String get repaymentAccountDetails {
    if(showKeys){
      return 'capp_repayment.repayment_account_details';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_account_details', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.repayment_account_details');
    return result != '' ? result : 'repayment_account_details';
  }

  String get repaymentAdditionalInfoTitle {
    if(showKeys){
      return 'capp_repayment.repayment_additional_info_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_additional_info_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin bổ sung", name: 'capp_repayment.repayment_additional_info_title');
    return result != '' ? result : 'repayment_additional_info_title';
  }

  String get repaymentAdditionalInfoDesc {
    if(showKeys){
      return 'capp_repayment.repayment_additional_info_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_additional_info_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tùy vào quy định của ngân hàng / Ví điện tử tại từng thời điểm, khoản thanh toán của Quý khách sẽ được chia nhỏ thành nhiều lần thanh toán khác nhau.", name: 'capp_repayment.repayment_additional_info_desc');
    return result != '' ? result : 'repayment_additional_info_desc';
  }

  String get noFee {
    if(showKeys){
      return 'capp_repayment.no_fee';
    }
    var ot = overridenTranslation(
        'capp_repayment.no_fee', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("No Fee", name: 'capp_repayment.no_fee');
    return result != '' ? result : 'no_fee';
  }

  String get repaymentAdaExploreAutoDebit {
    if(showKeys){
      return 'capp_repayment.repayment_ada_explore_auto_debit';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_explore_auto_debit', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khám phá thanh toán tự động", name: 'capp_repayment.repayment_ada_explore_auto_debit');
    return result != '' ? result : 'repayment_ada_explore_auto_debit';
  }

  String get repaymentAdaExploreAutoDebitDescription {
    if(showKeys){
      return 'capp_repayment.repayment_ada_explore_auto_debit_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_explore_auto_debit_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán tự động giúp bạn thanh toán đúng hạn, tránh khỏi phí phạt và nợ xấu", name: 'capp_repayment.repayment_ada_explore_auto_debit_description');
    return result != '' ? result : 'repayment_ada_explore_auto_debit_description';
  }

  String get repaymentAdaExplore {
    if(showKeys){
      return 'capp_repayment.repayment_ada_explore';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_explore', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khám phá", name: 'capp_repayment.repayment_ada_explore');
    return result != '' ? result : 'repayment_ada_explore';
  }

  String get repaymentAdaLater {
    if(showKeys){
      return 'capp_repayment.repayment_ada_later';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Để sau", name: 'capp_repayment.repayment_ada_later');
    return result != '' ? result : 'repayment_ada_later';
  }

  String get repaymentAdaExploreAutoDebitFeature {
    if(showKeys){
      return 'capp_repayment.repayment_ada_explore_auto_debit_feature';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_explore_auto_debit_feature', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khám phá tính năng thanh toán tự động", name: 'capp_repayment.repayment_ada_explore_auto_debit_feature');
    return result != '' ? result : 'repayment_ada_explore_auto_debit_feature';
  }

  String get repaymentPaymentVia {
    if(showKeys){
      return 'capp_repayment.repayment_payment_via';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_payment_via', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thanh toán qua", name: 'capp_repayment.repayment_payment_via');
    return result != '' ? result : 'repayment_payment_via';
  }

  String get repaymentMonthly {
    if(showKeys){
      return 'capp_repayment.repayment_monthly';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_monthly', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("hàng tháng", name: 'capp_repayment.repayment_monthly');
    return result != '' ? result : 'repayment_monthly';
  }

  String get repaymentAdaNoLoanTitle {
    if(showKeys){
      return 'capp_repayment.repayment_ada_no_loan_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_no_loan_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bạn chưa có khoản vay nào", name: 'capp_repayment.repayment_ada_no_loan_title');
    return result != '' ? result : 'repayment_ada_no_loan_title';
  }

  String get repaymentAdaNoLoanDescription {
    if(showKeys){
      return 'capp_repayment.repayment_ada_no_loan_description';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_no_loan_description', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Khám phá khoản vay ưu đãi của chúng tôi và đăng ký thanh toán tự động nhé!", name: 'capp_repayment.repayment_ada_no_loan_description');
    return result != '' ? result : 'repayment_ada_no_loan_description';
  }

  String get repaymentOverdueDesc {
    if(showKeys){
      return 'capp_repayment.repayment_overdue_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_overdue_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã trễ hạn thanh toán", name: 'capp_repayment.repayment_overdue_desc');
    return result != '' ? result : 'repayment_overdue_desc';
  }

  String get repaymentConfirm {
    if(showKeys){
      return 'capp_repayment.repayment_confirm';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_confirm', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xác nhận", name: 'capp_repayment.repayment_confirm');
    return result != '' ? result : 'repayment_confirm';
  }

  String get repaymentAdaNoEligibleContractMessage {
    if(showKeys){
      return 'capp_repayment.repayment_ada_no_eligible_contract_message';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_no_eligible_contract_message', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hợp đồng không hợp lệ để đăng ký thanh toán tự động, vui lòng thử lại sau", name: 'capp_repayment.repayment_ada_no_eligible_contract_message');
    return result != '' ? result : 'repayment_ada_no_eligible_contract_message';
  }

  String get somethingWentWrong {
    if(showKeys){
      return 'capp_repayment.something_went_wrong';
    }
    var ot = overridenTranslation(
        'capp_repayment.something_went_wrong', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Có vấn đề đã xảy ra", name: 'capp_repayment.something_went_wrong');
    return result != '' ? result : 'something_went_wrong';
  }

  String get repaymentAdaCancelAdaErrorTryAgain {
    if(showKeys){
      return 'capp_repayment.repayment_ada_cancel_ada_error_try_again';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_cancel_ada_error_try_again', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Huỷ thanh toán tự động không thành công. Vui lòng thử lại.", name: 'capp_repayment.repayment_ada_cancel_ada_error_try_again');
    return result != '' ? result : 'repayment_ada_cancel_ada_error_try_again';
  }

  String get repaymentAdaBackLater {
    if(showKeys){
      return 'capp_repayment.repayment_ada_back_later';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_back_later', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tôi sẽ quay lại sau", name: 'capp_repayment.repayment_ada_back_later');
    return result != '' ? result : 'repayment_ada_back_later';
  }

  String get repaymentAdaTryAgain {
    if(showKeys){
      return 'capp_repayment.repayment_ada_try_again';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_try_again', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thử lại", name: 'capp_repayment.repayment_ada_try_again');
    return result != '' ? result : 'repayment_ada_try_again';
  }

  String get repaymentAdaPaymentMethodForAd {
    if(showKeys){
      return 'capp_repayment.repayment_ada_payment_method_for_ad';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_ada_payment_method_for_ad', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tôi đồng ý sử dụng nguồn tiền này để thanh toán tự động cho hợp đồng.", name: 'capp_repayment.repayment_ada_payment_method_for_ad');
    return result != '' ? result : 'repayment_ada_payment_method_for_ad';
  }

  String get featureUnavailable {
    if(showKeys){
      return 'capp_repayment.feature_unavailable';
    }
    var ot = overridenTranslation(
        'capp_repayment.feature_unavailable', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("", name: 'capp_repayment.feature_unavailable');
    return result != '' ? result : 'feature_unavailable';
  }

  String get repaymentBrankasNotice {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_notice';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_notice', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Your payment success depends on whether your bank is currently online and operational.", name: 'capp_repayment.repayment_brankas_notice');
    return result != '' ? result : 'repayment_brankas_notice';
  }

  String get repaymentBrankasAgreementPhrase1 {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_agreement_phrase_1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_agreement_phrase_1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("By tapping on “Confirm Payment” means that you have fully read, understood, and agreed to the updated ", name: 'capp_repayment.repayment_brankas_agreement_phrase_1');
    return result != '' ? result : 'repayment_brankas_agreement_phrase_1';
  }

  String get repaymentBrankasHcPrivacyNotice {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_hc_privacy_notice';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_hc_privacy_notice', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Home Credit Privacy Notice", name: 'capp_repayment.repayment_brankas_hc_privacy_notice');
    return result != '' ? result : 'repayment_brankas_hc_privacy_notice';
  }

  String get repaymentBrankasAgreementPhrase2 {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_agreement_phrase_2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_agreement_phrase_2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message(" and ", name: 'capp_repayment.repayment_brankas_agreement_phrase_2');
    return result != '' ? result : 'repayment_brankas_agreement_phrase_2';
  }

  String get repaymentTnc {
    if(showKeys){
      return 'capp_repayment.repayment_tnc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_tnc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Terms & Conditions", name: 'capp_repayment.repayment_tnc');
    return result != '' ? result : 'repayment_tnc';
  }

  String get repaymentBrankasAgreementPhrase3 {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_agreement_phrase_3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_agreement_phrase_3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message(", as well as the  ", name: 'capp_repayment.repayment_brankas_agreement_phrase_3');
    return result != '' ? result : 'repayment_brankas_agreement_phrase_3';
  }

  String get repaymentBrankasTermOfUse {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_term_of_use';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_term_of_use', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Brankas Terms of Use and Policy", name: 'capp_repayment.repayment_brankas_term_of_use');
    return result != '' ? result : 'repayment_brankas_term_of_use';
  }

  String get repaymentBrankasAgreementPhrase4 {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_agreement_phrase_4';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_agreement_phrase_4', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message(" and the ", name: 'capp_repayment.repayment_brankas_agreement_phrase_4');
    return result != '' ? result : 'repayment_brankas_agreement_phrase_4';
  }

  String get repaymentBrankasPrivacyNotice {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_privacy_notice';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_privacy_notice', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Brankas Privacy Notice and Consent Form", name: 'capp_repayment.repayment_brankas_privacy_notice');
    return result != '' ? result : 'repayment_brankas_privacy_notice';
  }

  String get repaymentBrankasTermsOfUse {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_terms_of_use';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_terms_of_use', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Brankas Terms of Use", name: 'capp_repayment.repayment_brankas_terms_of_use');
    return result != '' ? result : 'repayment_brankas_terms_of_use';
  }

  String get repaymentBrankasPrivacyNoticeHeader {
    if(showKeys){
      return 'capp_repayment.repayment_brankas_privacy_notice_header';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_brankas_privacy_notice_header', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Brankas Privacy Notice", name: 'capp_repayment.repayment_brankas_privacy_notice_header');
    return result != '' ? result : 'repayment_brankas_privacy_notice_header';
  }

  String get repaymentBankUnavailable {
    if(showKeys){
      return 'capp_repayment.repayment_bank_unavailable';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_unavailable', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Bank is currently unavailable", name: 'capp_repayment.repayment_bank_unavailable');
    return result != '' ? result : 'repayment_bank_unavailable';
  }

  String get repaymentBankTransferTitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản ngân hàng", name: 'capp_repayment.repayment_bank_transfer_title');
    return result != '' ? result : 'repayment_bank_transfer_title';
  }

  String get repaymentBankTransferDownloadAndOpenBank {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_download_and_open_bank';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_download_and_open_bank', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải mã QR và mở ứng dụng ngân hàng", name: 'capp_repayment.repayment_bank_transfer_download_and_open_bank');
    return result != '' ? result : 'repayment_bank_transfer_download_and_open_bank';
  }

  String get repaymentBankTransferCopyAndOpenBank {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_copy_and_open_bank';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_copy_and_open_bank', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Sao chép và mở ứng dụng ngân hàng", name: 'capp_repayment.repayment_bank_transfer_copy_and_open_bank');
    return result != '' ? result : 'repayment_bank_transfer_copy_and_open_bank';
  }

  String get repaymentBankTransferDownloadQr {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_download_qr';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_download_qr', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải mã QR", name: 'capp_repayment.repayment_bank_transfer_download_qr');
    return result != '' ? result : 'repayment_bank_transfer_download_qr';
  }

  String get repaymentBankTransferSaveAccountNumber {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_save_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_save_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Lưu số tài khoản", name: 'capp_repayment.repayment_bank_transfer_save_account_number');
    return result != '' ? result : 'repayment_bank_transfer_save_account_number';
  }

  String get repaymentBankTransferPaymentInfo {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_payment_info';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_payment_info', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thông tin thanh toán", name: 'capp_repayment.repayment_bank_transfer_payment_info');
    return result != '' ? result : 'repayment_bank_transfer_payment_info';
  }

  String get repaymentBankTransferDownloadQrAlert {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_download_qr_alert';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_download_qr_alert', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mở ứng dụng ngân hàng > Chọn Quét mã QR > Tải QR lên từ bộ sưu tập ảnh.", name: 'capp_repayment.repayment_bank_transfer_download_qr_alert');
    return result != '' ? result : 'repayment_bank_transfer_download_qr_alert';
  }

  String get repaymentBankTransferSaveAccountNumberAlert {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_save_account_number_alert';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_save_account_number_alert', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mở ứng dụng ngân hàng và chuyển khoản đến số tài khoản dưới đây.", name: 'capp_repayment.repayment_bank_transfer_save_account_number_alert');
    return result != '' ? result : 'repayment_bank_transfer_save_account_number_alert';
  }

  String get repaymentBankTransferTotalAmount {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_total_amount';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_total_amount', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tổng thanh toán", name: 'capp_repayment.repayment_bank_transfer_total_amount');
    return result != '' ? result : 'repayment_bank_transfer_total_amount';
  }

  String get repaymentBankTransferViewIntro {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_view_intro';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_view_intro', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem hướng dẫn bằng hình ảnh", name: 'capp_repayment.repayment_bank_transfer_view_intro');
    return result != '' ? result : 'repayment_bank_transfer_view_intro';
  }

  String get repaymentBankTransferHomeCreditName {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_home_credit_name';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_home_credit_name', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Home Credit Vietnam", name: 'capp_repayment.repayment_bank_transfer_home_credit_name');
    return result != '' ? result : 'repayment_bank_transfer_home_credit_name';
  }

  String get repaymentBankTransferHomeCreditBidvBank {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_home_credit_bidv_bank';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_home_credit_bidv_bank', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Ngân hàng BIDV", name: 'capp_repayment.repayment_bank_transfer_home_credit_bidv_bank');
    return result != '' ? result : 'repayment_bank_transfer_home_credit_bidv_bank';
  }

  String get repaymentBankTransferAccountNumber {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_account_number';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_account_number', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Số tài khoản: ", name: 'capp_repayment.repayment_bank_transfer_account_number');
    return result != '' ? result : 'repayment_bank_transfer_account_number';
  }

  String get repaymentBankTransferIntroTitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_intro_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_intro_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Hướng dẫn", name: 'capp_repayment.repayment_bank_transfer_intro_title');
    return result != '' ? result : 'repayment_bank_transfer_intro_title';
  }

  String get repaymentBankTransferIntroGuideTitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_intro_guide_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_intro_guide_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chuyển khoản bằng mã QR", name: 'capp_repayment.repayment_bank_transfer_intro_guide_title');
    return result != '' ? result : 'repayment_bank_transfer_intro_guide_title';
  }

  String get repaymentBankTransferIntroGuideSubtitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_intro_guide_subtitle';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_intro_guide_subtitle', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Thực hiện theo các bước dưới đây", name: 'capp_repayment.repayment_bank_transfer_intro_guide_subtitle');
    return result != '' ? result : 'repayment_bank_transfer_intro_guide_subtitle';
  }

  String get repaymentBankTransferIntro1 {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_intro_1';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_intro_1', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trên ứng dụng ngân hàng, chọn mục <b>“Quét mã QR”<b>", name: 'capp_repayment.repayment_bank_transfer_intro_1');
    return result != '' ? result : 'repayment_bank_transfer_intro_1';
  }

  String get repaymentBankTransferIntro2 {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_intro_2';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_intro_2', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn <b>“Tải ảnh từ bộ sưu tập”<b>", name: 'capp_repayment.repayment_bank_transfer_intro_2');
    return result != '' ? result : 'repayment_bank_transfer_intro_2';
  }

  String get repaymentBankTransferIntro3 {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_intro_3';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_intro_3', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tải lên ảnh QR bạn vừa lưu", name: 'capp_repayment.repayment_bank_transfer_intro_3');
    return result != '' ? result : 'repayment_bank_transfer_intro_3';
  }

  String get repaymentBankTransferIntroOk {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_intro_ok';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_intro_ok', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đã hiểu", name: 'capp_repayment.repayment_bank_transfer_intro_ok');
    return result != '' ? result : 'repayment_bank_transfer_intro_ok';
  }

  String get repaymentBankTransferQrWaitingTitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_qr_waiting_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_qr_waiting_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Đang chờ ghi nhận thanh toán", name: 'capp_repayment.repayment_bank_transfer_qr_waiting_title');
    return result != '' ? result : 'repayment_bank_transfer_qr_waiting_title';
  }

  String get repaymentBankTransferQrWaitingDesc {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_qr_waiting_desc';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_qr_waiting_desc', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Nếu bạn đã chuyển khoản nhưng chưa được cập nhật sau 30 giây, vui lòng gọi Hotline 1900 633 633", name: 'capp_repayment.repayment_bank_transfer_qr_waiting_desc');
    return result != '' ? result : 'repayment_bank_transfer_qr_waiting_desc';
  }

  String get repaymentBankTransferQrWaitingPrimaryBtnTitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_qr_waiting_primary_btn_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_qr_waiting_primary_btn_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Trở về quản lý khoản vay", name: 'capp_repayment.repayment_bank_transfer_qr_waiting_primary_btn_title');
    return result != '' ? result : 'repayment_bank_transfer_qr_waiting_primary_btn_title';
  }

  String get repaymentBankTransferQrWaitingSecondBtnTitle {
    if(showKeys){
      return 'capp_repayment.repayment_bank_transfer_qr_waiting_second_btn_title';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_bank_transfer_qr_waiting_second_btn_title', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Xem lại mã QR thanh toán", name: 'capp_repayment.repayment_bank_transfer_qr_waiting_second_btn_title');
    return result != '' ? result : 'repayment_bank_transfer_qr_waiting_second_btn_title';
  }

  String get repaymentChooseBank {
    if(showKeys){
      return 'capp_repayment.repayment_choose_bank';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_choose_bank', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Chọn ngân hàng của bạn", name: 'capp_repayment.repayment_choose_bank');
    return result != '' ? result : 'repayment_choose_bank';
  }

  String get repaymentSearchBankHint {
    if(showKeys){
      return 'capp_repayment.repayment_search_bank_hint';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_search_bank_hint', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Tìm theo tên ngân hàng", name: 'capp_repayment.repayment_search_bank_hint');
    return result != '' ? result : 'repayment_search_bank_hint';
  }

  String get repaymentValidTo {
    if(showKeys){
      return 'capp_repayment.repayment_valid_to';
    }
    var ot = overridenTranslation(
        'capp_repayment.repayment_valid_to', _locale.languageCode, _locale.countryCode);
    var result = ot ?? Intl.message("Mã hết hạn vào:", name: 'capp_repayment.repayment_valid_to');
    return result != '' ? result : 'repayment_valid_to';
  }



  Map<String, String> get _translate => <String, String>{
		'continue_word' : continueWord,
		'transaction_details_section' : transactionDetailsSection,
		'back_to_homepage' : backToHomepage,
		'transaction_failed_try_again' : transactionFailedTryAgain,
		'transaction_details_copy_btn' : transactionDetailsCopyBtn,
		'transaction_details_copy_success' : transactionDetailsCopySuccess,
		'payment_summary_button_cancel' : paymentSummaryButtonCancel,
		'payment_summary_button_confirm' : paymentSummaryButtonConfirm,
		'transaction_details' : transactionDetails,
		'transaction_details_done_btn' : transactionDetailsDoneBtn,
		'contract_details_section' : contractDetailsSection,
		'ewallet' : ewallet,
		'bank_account' : bankAccount,
		'payment_success_title_message' : paymentSuccessTitleMessage,
		'payment_details' : paymentDetails,
		'transaction_details_no' : transactionDetailsNo,
		'transaction_details_payment_option' : transactionDetailsPaymentOption,
		'transaction_details_date_processed' : transactionDetailsDateProcessed,
		'onepay_message_code_1' : onepayMessageCode1,
		'onepay_message_code_3' : onepayMessageCode3,
		'onepay_message_code_4' : onepayMessageCode4,
		'onepay_message_code_5' : onepayMessageCode5,
		'onepay_message_code_6' : onepayMessageCode6,
		'onepay_message_code_7' : onepayMessageCode7,
		'onepay_message_code_8' : onepayMessageCode8,
		'onepay_message_code_9' : onepayMessageCode9,
		'onepay_message_code_10' : onepayMessageCode10,
		'onepay_message_code_11' : onepayMessageCode11,
		'onepay_message_code_12' : onepayMessageCode12,
		'onepay_message_code_13' : onepayMessageCode13,
		'onepay_message_code_21' : onepayMessageCode21,
		'onepay_message_code_22' : onepayMessageCode22,
		'onepay_message_code_23' : onepayMessageCode23,
		'onepay_message_code_24' : onepayMessageCode24,
		'onepay_message_code_25' : onepayMessageCode25,
		'onepay_message_code_253' : onepayMessageCode253,
		'onepay_message_code_99' : onepayMessageCode99,
		'onepay_consent_title' : onepayConsentTitle,
		'onepay_consent_description' : onepayConsentDescription,
		'onepay_got_it_continue' : onepayGotItContinue,
		'repayment_contract_no' : repaymentContractNo,
		'repayment_account_name' : repaymentAccountName,
		'repayment_loan_repayment' : repaymentLoanRepayment,
		'repayment_select_online_payment_option' : repaymentSelectOnlinePaymentOption,
		'repayment_payment_amount' : repaymentPaymentAmount,
		'repayment_online_with_atm_card' : repaymentOnlineWithAtmCard,
		'repayment_online_with_partner_ewallets' : repaymentOnlineWithPartnerEwallets,
		'repayment_continue_to_payment_summary' : repaymentContinueToPaymentSummary,
		'repayment_payment_summary' : repaymentPaymentSummary,
		'repayment_please_review_your_information' : repaymentPleaseReviewYourInformation,
		'repayment_payment_information' : repaymentPaymentInformation,
		'repayment_contract_number' : repaymentContractNumber,
		'repayment_full_name' : repaymentFullName,
		'repayment_phone_number' : repaymentPhoneNumber,
		'repayment_payment_option' : repaymentPaymentOption,
		'repayment_total_amount' : repaymentTotalAmount,
		'repayment_confirm_payment' : repaymentConfirmPayment,
		'repayment_gateway_is_opening' : repaymentGatewayIsOpening,
		'repayment_method_selection_continue' : repaymentMethodSelectionContinue,
		'repayment_method_selection_title' : repaymentMethodSelectionTitle,
		'repayment_method_selection_choose_payment_method' : repaymentMethodSelectionChoosePaymentMethod,
		'repayment_method_selection_pay_by_cash_title' : repaymentMethodSelectionPayByCashTitle,
		'repayment_method_selection_pay_by_cash_subTitle' : repaymentMethodSelectionPayByCashSubTitle,
		'repayment_method_selection_pay_via_bank_transfer_title' : repaymentMethodSelectionPayViaBankTransferTitle,
		'repayment_method_selection_pay_via_bank_transfer_subTitle' : repaymentMethodSelectionPayViaBankTransferSubTitle,
		'repayment' : repayment,
		'repayment_pay_for_my_loans' : repaymentPayForMyLoans,
		'repayment_loan' : repaymentLoan,
		'repayment_loans' : repaymentLoans,
		'repayment_need_repayment' : repaymentNeedRepayment,
		'repayment_account_no' : repaymentAccountNo,
		'repayment_due_date' : repaymentDueDate,
		'repayment_due_amount' : repaymentDueAmount,
		'repayment_select_amount_choose_to_pay' : repaymentSelectAmountChooseToPay,
		'repayment_select_amount_enter_custom_amount' : repaymentSelectAmountEnterCustomAmount,
		'repayment_select_amount_custom_amount' : repaymentSelectAmountCustomAmount,
		'repayment_select_amount_min_amount_validation_mes' : repaymentSelectAmountMinAmountValidationMes,
		'repayment_select_amount_over_total_debt_validation_mes' : repaymentSelectAmountOverTotalDebtValidationMes,
		'repayment_select_amount_continue_payment_options' : repaymentSelectAmountContinuePaymentOptions,
		'repayment_select_amount_due_amount_validation_mes_heading' : repaymentSelectAmountDueAmountValidationMesHeading,
		'repayment_select_amount_due_amount_validation_mes' : repaymentSelectAmountDueAmountValidationMes,
		'repayment_select_amount_edit' : repaymentSelectAmountEdit,
		'repayment_minimum_due_amount' : repaymentMinimumDueAmount,
		'repayment_total_due_amount' : repaymentTotalDueAmount,
		'repayment_method_selection_pay_via_auto_debit_arrangement_title' : repaymentMethodSelectionPayViaAutoDebitArrangementTitle,
		'repayment_method_selection_pay_via_auto_debit_arrangement_sub_title' : repaymentMethodSelectionPayViaAutoDebitArrangementSubTitle,
		'repayment_method_selection_pay_in_app_title' : repaymentMethodSelectionPayInAppTitle,
		'repayment_method_selection_pay_in_app_sub_title' : repaymentMethodSelectionPayInAppSubTitle,
		'repayment_method_selection_pay_over_the_counter_title' : repaymentMethodSelectionPayOverTheCounterTitle,
		'repayment_method_selection_pay_over_the_counter_sub_title' : repaymentMethodSelectionPayOverTheCounterSubTitle,
		'repayment_select_amount_rel_minimum_amount_validation_mes' : repaymentSelectAmountRelMinimumAmountValidationMes,
		'repayment_there_is_no_available_loan' : repaymentThereIsNoAvailableLoan,
		'repayment_transaction_failed_something_went_wrong' : repaymentTransactionFailedSomethingWentWrong,
		'repayment_transaction_failed_generic_message' : repaymentTransactionFailedGenericMessage,
		'repayment_debit_cards' : repaymentDebitCards,
		'repayment_pay_with_your_debit_cards' : repaymentPayWithYourDebitCards,
		'repayment_card_number' : repaymentCardNumber,
		'repayment_you_dont_have_any_card_saved' : repaymentYouDontHaveAnyCardSaved,
		'repayment_card_enrollment' : repaymentCardEnrollment,
		'repayment_your_card_provider_will_temporarily' : repaymentYourCardProviderWillTemporarily,
		'repayment_bank_transfer' : repaymentBankTransfer,
		'repayment_in_app_payment' : repaymentInAppPayment,
		'repayment_emoney_and_ecommerce' : repaymentEmoneyAndEcommerce,
		'repayment_retail' : repaymentRetail,
		'repayment_contract_information' : repaymentContractInformation,
		'repayment_bank_transfer_information' : repaymentBankTransferInformation,
		'repayment_bank_transfer_information_title' : repaymentBankTransferInformationTitle,
		'repayment_bank_name' : repaymentBankName,
		'repayment_account_number' : repaymentAccountNumber,
		'repayment_transfer_content' : repaymentTransferContent,
		'repayment_for_contract_number' : repaymentForContractNumber,
		'repayment_guidance' : repaymentGuidance,
		'repayment_bca_atm_step1' : repaymentBcaAtmStep1,
		'repayment_bca_atm_step2' : repaymentBcaAtmStep2,
		'repayment_bca_atm_step3' : repaymentBcaAtmStep3,
		'repayment_bca_atm_step4' : repaymentBcaAtmStep4,
		'repayment_bca_atm_step5' : repaymentBcaAtmStep5,
		'repayment_bca_atm_step6' : repaymentBcaAtmStep6,
		'repayment_bca_atm_step7' : repaymentBcaAtmStep7,
		'repayment_bca_mobile_step1' : repaymentBcaMobileStep1,
		'repayment_bca_mobile_step2' : repaymentBcaMobileStep2,
		'repayment_bca_mobile_step3' : repaymentBcaMobileStep3,
		'repayment_bca_mobile_step4' : repaymentBcaMobileStep4,
		'repayment_bca_mobile_step5' : repaymentBcaMobileStep5,
		'repayment_bca_mobile_step6' : repaymentBcaMobileStep6,
		'repayment_bni_atm_step1' : repaymentBniAtmStep1,
		'repayment_bni_atm_step2' : repaymentBniAtmStep2,
		'repayment_bni_atm_step3' : repaymentBniAtmStep3,
		'repayment_bni_atm_step4' : repaymentBniAtmStep4,
		'repayment_bni_atm_step5' : repaymentBniAtmStep5,
		'repayment_bni_atm_step6' : repaymentBniAtmStep6,
		'repayment_bni_atm_step7' : repaymentBniAtmStep7,
		'repayment_bni_atm_step8' : repaymentBniAtmStep8,
		'repayment_bni_atm_step9' : repaymentBniAtmStep9,
		'repayment_bni_atm_step10' : repaymentBniAtmStep10,
		'repayment_bni_atm_step11' : repaymentBniAtmStep11,
		'repayment_bni_mobile_step1' : repaymentBniMobileStep1,
		'repayment_bni_mobile_step2' : repaymentBniMobileStep2,
		'repayment_bni_mobile_step3' : repaymentBniMobileStep3,
		'repayment_bni_mobile_step4' : repaymentBniMobileStep4,
		'repayment_bni_mobile_step5' : repaymentBniMobileStep5,
		'repayment_bni_mobile_step6' : repaymentBniMobileStep6,
		'repayment_bni_mobile_step7' : repaymentBniMobileStep7,
		'repayment_mandiri_atm_step1' : repaymentMandiriAtmStep1,
		'repayment_mandiri_atm_step2' : repaymentMandiriAtmStep2,
		'repayment_mandiri_atm_step3' : repaymentMandiriAtmStep3,
		'repayment_mandiri_atm_step4' : repaymentMandiriAtmStep4,
		'repayment_mandiri_atm_step5' : repaymentMandiriAtmStep5,
		'repayment_mandiri_atm_step6' : repaymentMandiriAtmStep6,
		'repayment_mandiri_mobile_step1' : repaymentMandiriMobileStep1,
		'repayment_mandiri_mobile_step2' : repaymentMandiriMobileStep2,
		'repayment_mandiri_mobile_step3' : repaymentMandiriMobileStep3,
		'repayment_mandiri_mobile_step4' : repaymentMandiriMobileStep4,
		'repayment_mandiri_mobile_step5' : repaymentMandiriMobileStep5,
		'repayment_mandiri_mobile_step6' : repaymentMandiriMobileStep6,
		'repayment_mandiri_mobile_step7' : repaymentMandiriMobileStep7,
		'repayment_bri_atm_step1' : repaymentBriAtmStep1,
		'repayment_bri_atm_step2' : repaymentBriAtmStep2,
		'repayment_bri_atm_step3' : repaymentBriAtmStep3,
		'repayment_bri_atm_step4' : repaymentBriAtmStep4,
		'repayment_bri_atm_step5' : repaymentBriAtmStep5,
		'repayment_bri_atm_step6' : repaymentBriAtmStep6,
		'repayment_bri_atm_step7' : repaymentBriAtmStep7,
		'repayment_bri_atm_step8' : repaymentBriAtmStep8,
		'repayment_bri_mobile_step1' : repaymentBriMobileStep1,
		'repayment_bri_mobile_step2' : repaymentBriMobileStep2,
		'repayment_bri_mobile_step3' : repaymentBriMobileStep3,
		'repayment_bri_mobile_step4' : repaymentBriMobileStep4,
		'repayment_bri_mobile_step5' : repaymentBriMobileStep5,
		'repayment_bri_mobile_step6' : repaymentBriMobileStep6,
		'repayment_permata_atm_step1' : repaymentPermataAtmStep1,
		'repayment_permata_atm_step2' : repaymentPermataAtmStep2,
		'repayment_permata_atm_step3' : repaymentPermataAtmStep3,
		'repayment_permata_atm_step4' : repaymentPermataAtmStep4,
		'repayment_permata_atm_step5' : repaymentPermataAtmStep5,
		'repayment_permata_atm_step6' : repaymentPermataAtmStep6,
		'repayment_permata_atm_step7' : repaymentPermataAtmStep7,
		'repayment_permata_atm_step8' : repaymentPermataAtmStep8,
		'repayment_permata_mobile_step1' : repaymentPermataMobileStep1,
		'repayment_permata_mobile_step2' : repaymentPermataMobileStep2,
		'repayment_permata_mobile_step3' : repaymentPermataMobileStep3,
		'repayment_permata_mobile_step4' : repaymentPermataMobileStep4,
		'repayment_permata_mobile_step5' : repaymentPermataMobileStep5,
		'repayment_permata_mobile_step6' : repaymentPermataMobileStep6,
		'repayment_btpn_atm_step1' : repaymentBtpnAtmStep1,
		'repayment_btpn_atm_step2' : repaymentBtpnAtmStep2,
		'repayment_btpn_atm_step3' : repaymentBtpnAtmStep3,
		'repayment_btpn_atm_step4' : repaymentBtpnAtmStep4,
		'repayment_btpn_atm_step5' : repaymentBtpnAtmStep5,
		'repayment_btpn_atm_step6' : repaymentBtpnAtmStep6,
		'repayment_btpn_atm_step7' : repaymentBtpnAtmStep7,
		'repayment_btpn_atm_step8' : repaymentBtpnAtmStep8,
		'repayment_btpn_mobile_step1' : repaymentBtpnMobileStep1,
		'repayment_btpn_mobile_step2' : repaymentBtpnMobileStep2,
		'repayment_btpn_mobile_step3' : repaymentBtpnMobileStep3,
		'repayment_btpn_mobile_step4' : repaymentBtpnMobileStep4,
		'repayment_btpn_mobile_step5' : repaymentBtpnMobileStep5,
		'repayment_btpn_mobile_step6' : repaymentBtpnMobileStep6,
		'repayment_btpn_mobile_step7' : repaymentBtpnMobileStep7,
		'repayment_pa_atm_step1' : repaymentPaAtmStep1,
		'repayment_pa_atm_step2' : repaymentPaAtmStep2,
		'repayment_pa_atm_step3' : repaymentPaAtmStep3,
		'repayment_pa_atm_step4' : repaymentPaAtmStep4,
		'repayment_pa_atm_step5' : repaymentPaAtmStep5,
		'repayment_pa_atm_step6' : repaymentPaAtmStep6,
		'repayment_pa_atm_step7' : repaymentPaAtmStep7,
		'repayment_bersama_atm_step1' : repaymentBersamaAtmStep1,
		'repayment_bersama_atm_step2' : repaymentBersamaAtmStep2,
		'repayment_bersama_atm_step3' : repaymentBersamaAtmStep3,
		'repayment_bersama_atm_step4' : repaymentBersamaAtmStep4,
		'repayment_bersama_atm_step5' : repaymentBersamaAtmStep5,
		'repayment_bersama_atm_step6' : repaymentBersamaAtmStep6,
		'repayment_bersama_atm_step7' : repaymentBersamaAtmStep7,
		'repayment_bersama_atm_step8' : repaymentBersamaAtmStep8,
		'repayment_atm' : repaymentAtm,
		'repayment_bank' : repaymentBank,
		'repayment_mobile_banking' : repaymentMobileBanking,
		'repayment_bank_transfer_select_option' : repaymentBankTransferSelectOption,
		'repayment_bank_transfer_virtual_account' : repaymentBankTransferVirtualAccount,
		'repayment_fee' : repaymentFee,
		'repayment_add_card' : repaymentAddCard,
		'repayment_vnpay_app_back_alert' : repaymentVnpayAppBackAlert,
		'shopeepay_error_message_code_201' : shopeepayErrorMessageCode201,
		'shopeepay_error_message_code_202' : shopeepayErrorMessageCode202,
		'shopeepay_error_message_code_203' : shopeepayErrorMessageCode203,
		'shopeepay_error_message_code_204' : shopeepayErrorMessageCode204,
		'repayment_processing' : repaymentProcessing,
		'repayment_you_will_be_noticed_when' : repaymentYouWillBeNoticedWhen,
		'repayment_timeout' : repaymentTimeout,
		'repayment_please_try_again' : repaymentPleaseTryAgain,
		'zalopay_error_message_code_invalid_response' : zalopayErrorMessageCodeInvalidResponse,
		'zalopay_error_message_code_invalid_order' : zalopayErrorMessageCodeInvalidOrder,
		'zalopay_error_message_code_fail' : zalopayErrorMessageCodeFail,
		'zalopay_error_message_code_cancel' : zalopayErrorMessageCodeCancel,
		'exception__occurred' : exceptionOccurred,
		'viettel_error_message_code_22' : viettelErrorMessageCode22,
		'viettel_error_message_code_V02' : viettelErrorMessageCodeV02,
		'viettel_error_message_code_V03' : viettelErrorMessageCodeV03,
		'viettel_error_message_code_21' : viettelErrorMessageCode21,
		'viettel_error_message_code_685' : viettelErrorMessageCode685,
		'viettel_error_message_code_16' : viettelErrorMessageCode16,
		'viettel_error_message_code_W04' : viettelErrorMessageCodeW04,
		'viettel_error_message_code_V04' : viettelErrorMessageCodeV04,
		'viettel_error_message_code_V05' : viettelErrorMessageCodeV05,
		'viettel_error_message_code_V06' : viettelErrorMessageCodeV06,
		'viettel_error_message_code_S_MAINTAIN' : viettelErrorMessageCodeSMaintain,
		'viettel_error_message_code_99' : viettelErrorMessageCode99,
		'viettel_error_message_code_M03' : viettelErrorMessageCodeM03,
		'viettel_error_message_code_M04' : viettelErrorMessageCodeM04,
		'viettel_error_message_code_813' : viettelErrorMessageCode813,
		'viettel_error_message_code_V01' : viettelErrorMessageCodeV01,
		'viettel_error_message_code_M01' : viettelErrorMessageCodeM01,
		'viettel_error_message_code_M02' : viettelErrorMessageCodeM02,
		'viettel_error_message_code_P48' : viettelErrorMessageCodeP48,
		'viettel_error_message_code_430' : viettelErrorMessageCode430,
		'viettel_error_message_code_427' : viettelErrorMessageCode427,
		'viettel_error_message_code_191' : viettelErrorMessageCode191,
		'viettel_error_message_code_P01' : viettelErrorMessageCodeP01,
		'viettel_error_message_code_P03' : viettelErrorMessageCodeP03,
		'viettel_error_message_code_32' : viettelErrorMessageCode32,
		'repayment_no_payment_is_required' : repaymentNoPaymentIsRequired,
		'repayment_add_card_capitalize' : repaymentAddCardCapitalize,
		'repayment_sit_tight' : repaymentSitTight,
		'repayment_we_will_immediately_notify' : repaymentWeWillImmediatelyNotify,
		'repayment_card_verification' : repaymentCardVerification,
		'repayment_please_enter_the_amount_indicated' : repaymentPleaseEnterTheAmountIndicated,
		'repayment_enter_amount' : repaymentEnterAmount,
		'repayment_verify' : repaymentVerify,
		'payment_success_body_message_ph' : paymentSuccessBodyMessagePh,
		'processing_fee' : processingFee,
		'waived' : waived,
		'repayment_customer_name' : repaymentCustomerName,
		'repayment_select_emoney_and_ecommerce' : repaymentSelectEmoneyAndEcommerce,
		'repayment_electric_money' : repaymentElectricMoney,
		'repayment_emoney' : repaymentEmoney,
		'repayment_ecommerce' : repaymentEcommerce,
		'repayment_instant_payment' : repaymentInstantPayment,
		'repayment_emoney_gotagihan_step1' : repaymentEmoneyGotagihanStep1,
		'repayment_emoney_gotagihan_step2' : repaymentEmoneyGotagihanStep2,
		'repayment_emoney_gotagihan_step3' : repaymentEmoneyGotagihanStep3,
		'repayment_emoney_gotagihan_step4' : repaymentEmoneyGotagihanStep4,
		'repayment_emoney_gotagihan_step5' : repaymentEmoneyGotagihanStep5,
		'repayment_emoney_gotagihan_step6' : repaymentEmoneyGotagihanStep6,
		'repayment_emoney_gotagihan_step7' : repaymentEmoneyGotagihanStep7,
		'repayment_emoney_ayopop_step1' : repaymentEmoneyAyopopStep1,
		'repayment_emoney_ayopop_step2' : repaymentEmoneyAyopopStep2,
		'repayment_emoney_ayopop_step3' : repaymentEmoneyAyopopStep3,
		'repayment_emoney_ayopop_step4' : repaymentEmoneyAyopopStep4,
		'repayment_emoney_ayopop_step5' : repaymentEmoneyAyopopStep5,
		'repayment_emoney_ayopop_step6' : repaymentEmoneyAyopopStep6,
		'repayment_emoney_ayopop_step7' : repaymentEmoneyAyopopStep7,
		'repayment_emoney_ayopop_step8' : repaymentEmoneyAyopopStep8,
		'repayment_emoney_bebasbayar_step1' : repaymentEmoneyBebasbayarStep1,
		'repayment_emoney_bebasbayar_step2' : repaymentEmoneyBebasbayarStep2,
		'repayment_emoney_bebasbayar_step3' : repaymentEmoneyBebasbayarStep3,
		'repayment_emoney_bebasbayar_step4' : repaymentEmoneyBebasbayarStep4,
		'repayment_emoney_bebasbayar_step5' : repaymentEmoneyBebasbayarStep5,
		'repayment_emoney_bebasbayar_step6' : repaymentEmoneyBebasbayarStep6,
		'repayment_ecommerce_tokopedia_step1' : repaymentEcommerceTokopediaStep1,
		'repayment_ecommerce_tokopedia_step2' : repaymentEcommerceTokopediaStep2,
		'repayment_ecommerce_tokopedia_step3' : repaymentEcommerceTokopediaStep3,
		'repayment_ecommerce_tokopedia_step4' : repaymentEcommerceTokopediaStep4,
		'repayment_ecommerce_tokopedia_step5' : repaymentEcommerceTokopediaStep5,
		'repayment_ecommerce_tokopedia_step6' : repaymentEcommerceTokopediaStep6,
		'repayment_ecommerce_tokopedia_step7' : repaymentEcommerceTokopediaStep7,
		'repayment_ecommerce_tokopedia_step8' : repaymentEcommerceTokopediaStep8,
		'repayment_ecommerce_tokopedia_step9' : repaymentEcommerceTokopediaStep9,
		'repayment_ecommerce_bukalapak_step1' : repaymentEcommerceBukalapakStep1,
		'repayment_ecommerce_bukalapak_step2' : repaymentEcommerceBukalapakStep2,
		'repayment_ecommerce_bukalapak_step3' : repaymentEcommerceBukalapakStep3,
		'repayment_ecommerce_bukalapak_step4' : repaymentEcommerceBukalapakStep4,
		'repayment_ecommerce_bukalapak_step5' : repaymentEcommerceBukalapakStep5,
		'repayment_ecommerce_bukalapak_step6' : repaymentEcommerceBukalapakStep6,
		'repayment_ecommerce_bukalapak_step7' : repaymentEcommerceBukalapakStep7,
		'repayment_ecommerce_blibli_step1' : repaymentEcommerceBlibliStep1,
		'repayment_ecommerce_blibli_step2' : repaymentEcommerceBlibliStep2,
		'repayment_ecommerce_blibli_step3' : repaymentEcommerceBlibliStep3,
		'repayment_ecommerce_blibli_step4' : repaymentEcommerceBlibliStep4,
		'repayment_ecommerce_blibli_step5' : repaymentEcommerceBlibliStep5,
		'repayment_ecommerce_blibli_step6' : repaymentEcommerceBlibliStep6,
		'repayment_ecommerce_blibli_step7' : repaymentEcommerceBlibliStep7,
		'repayment_ecommerce_blibli_step8' : repaymentEcommerceBlibliStep8,
		'repayment_ecommerce_lazada_step1' : repaymentEcommerceLazadaStep1,
		'repayment_ecommerce_lazada_step2' : repaymentEcommerceLazadaStep2,
		'repayment_ecommerce_lazada_step3' : repaymentEcommerceLazadaStep3,
		'repayment_ecommerce_lazada_step4' : repaymentEcommerceLazadaStep4,
		'repayment_ecommerce_lazada_step5' : repaymentEcommerceLazadaStep5,
		'repayment_ecommerce_lazada_step6' : repaymentEcommerceLazadaStep6,
		'repayment_ecommerce_lazada_step7' : repaymentEcommerceLazadaStep7,
		'repayment_ecommerce_lazada_step8' : repaymentEcommerceLazadaStep8,
		'repayment_ada' : repaymentAda,
		'repayment_payments_made_convenient' : repaymentPaymentsMadeConvenient,
		'repayment_set_up_an_automatic' : repaymentSetUpAnAutomatic,
		'repayment_what_accounts_can_i' : repaymentWhatAccountsCanI,
		'repayment_online_banking_accounts' : repaymentOnlineBankingAccounts,
		'repayment_enroll_your_online' : repaymentEnrollYourOnline,
		'repayment_enroll_your_mastercard' : repaymentEnrollYourMastercard,
		'repayment_regular_bank_accounts' : repaymentRegularBankAccounts,
		'repayment_manually_enroll' : repaymentManuallyEnroll,
		'repayment_why_should_i' : repaymentWhyShouldI,
		'repayment_easy_to_set_up' : repaymentEasyToSetUp,
		'repayment_hassle_free' : repaymentHassleFree,
		'repayment_avoid_delays' : repaymentAvoidDelays,
		'repayment_setup_now' : repaymentSetupNow,
		'repayment_ada_terms_conditions_detail_title' : repaymentAdaTermsConditionsDetailTitle,
		'repayment_ada_terms_conditions_detail_agree_continue' : repaymentAdaTermsConditionsDetailAgreeContinue,
		'repayment_online_banks' : repaymentOnlineBanks,
		'repayment_enroll_your_online_bank_account_to_auto' : repaymentEnrollYourOnlineBankAccountToAuto,
		'repayment_use_your_visa_or_mastercard' : repaymentUseYourVisaOrMastercard,
		'repayment_enjoy_hassle_free' : repaymentEnjoyHassleFree,
		'repayment_pay_with_auto_debit_arrangement' : repaymentPayWithAutoDebitArrangement,
		'repayment_we_willdeduct_your_loan' : repaymentWeWilldeductYourLoan,
		'repayment_make_sure_you_have_enough_funds' : repaymentMakeSureYouHaveEnoughFunds,
		'repayment_by_tapping_on_continue' : repaymentByTappingOnContinue,
		'repayment_term_and_conditions' : repaymentTermAndConditions,
		'repayment_how_it_works' : repaymentHowItWorks,
		'repayment_ada_confirm_enrollment_title' : repaymentAdaConfirmEnrollmentTitle,
		'repayment_ada_confirm_enrollment_description' : repaymentAdaConfirmEnrollmentDescription,
		'repayment_ada_bank_name' : repaymentAdaBankName,
		'repayment_ada_card_type' : repaymentAdaCardType,
		'repayment_ada_card_number' : repaymentAdaCardNumber,
		'repayment_ada_loan_account_no' : repaymentAdaLoanAccountNo,
		'repayment_ada_monthly_installment' : repaymentAdaMonthlyInstallment,
		'repayment_ada_loan_type' : repaymentAdaLoanType,
		'repayment_ada_starting_from' : repaymentAdaStartingFrom,
		'repayment_ada_up_until' : repaymentAdaUpUntil,
		'repayment_ada_total_monthly_installment' : repaymentAdaTotalMonthlyInstallment,
		'repayment_ada_cancel_enrollment' : repaymentAdaCancelEnrollment,
		'repayment_ada_confirm_enroll' : repaymentAdaConfirmEnroll,
		'repayment_ada_enrollment_details' : repaymentAdaEnrollmentDetails,
		'repayment_ada_enrollment_successful' : repaymentAdaEnrollmentSuccessful,
		'repayment_ada_enrollment_successful_message' : repaymentAdaEnrollmentSuccessfulMessage,
		'repayment_ada_loan_account_number' : repaymentAdaLoanAccountNumber,
		'repayment_ada_total_monthly_deduction' : repaymentAdaTotalMonthlyDeduction,
		'repayment_ada_back_to_dashboard' : repaymentAdaBackToDashboard,
		'repayment_ada_view_payment_schedule' : repaymentAdaViewPaymentSchedule,
		'repayment_my_cards' : repaymentMyCards,
		'repayment_saved_cards' : repaymentSavedCards,
		'repayment_all_your_saved_cards_in' : repaymentAllYourSavedCardsIn,
		'repayment_remove_card_ending_in' : repaymentRemoveCardEndingIn,
		'repayment_yes_remove' : repaymentYesRemove,
		'repayment_no_wait' : repaymentNoWait,
		'repayment_personal_loan' : repaymentPersonalLoan,
		'repayment_consumer_loan' : repaymentConsumerLoan,
		'repayment_bank_branch' : repaymentBankBranch,
		'repayment_virtual_account_number' : repaymentVirtualAccountNumber,
		'repayment_qr_code' : repaymentQrCode,
		'repayment_you_can_pay_with_this_qr' : repaymentYouCanPayWithThisQr,
		'repayment_share' : repaymentShare,
		'repayment_download' : repaymentDownload,
		'repayment_download_fail' : repaymentDownloadFail,
		'repayment_download_successfully' : repaymentDownloadSuccessfully,
		'repayment_close' : repaymentClose,
		'repayment_share_fail' : repaymentShareFail,
		'repayment_open_settings' : repaymentOpenSettings,
		'repayment_to_have_permission_to_access_photo_ios' : repaymentToHavePermissionToAccessPhotoIos,
		'repayment_to_have_permission_to_access_photo_android' : repaymentToHavePermissionToAccessPhotoAndroid,
		'repayment_minimum_amount' : repaymentMinimumAmount,
		'repayment_details' : repaymentDetails,
		'repayment_retail_transfer' : repaymentRetailTransfer,
		'repayment_available_partner_retail' : repaymentAvailablePartnerRetail,
		'repayment_available_partner_retail_description' : repaymentAvailablePartnerRetailDescription,
		'repayment_maximum_payment_amount_for_transfer' : repaymentMaximumPaymentAmountForTransfer,
		'repayment_go_to_gerai_retail' : repaymentGoToGeraiRetail,
		'repayment_pay_according_to_the_amount' : repaymentPayAccordingToTheAmount,
		'repayment_make_sure_to_keep_your_payment_receipt' : repaymentMakeSureToKeepYourPaymentReceipt,
		'cancel_payment_message' : cancelPaymentMessage,
		'cancel_payment_yes' : cancelPaymentYes,
		'cancel_payment_no' : cancelPaymentNo,
		'repayment_pay_via_bank_transfer_is_not_available' : repaymentPayViaBankTransferIsNotAvailable,
		'repayment_got_it_change_method' : repaymentGotItChangeMethod,
		'repayment_contract_repayment' : repaymentContractRepayment,
		'repayment_repay_now' : repaymentRepayNow,
		'repayment_outstanding_balance' : repaymentOutstandingBalance,
		'repayment_select_amount_rel_bnpl_minimum_amount_validation_mes' : repaymentSelectAmountRelBnplMinimumAmountValidationMes,
		'repayment_virtual_account_no' : repaymentVirtualAccountNo,
		'repayment_product_credit_card' : repaymentProductCreditCard,
		'repayment_product_bnpl' : repaymentProductBnpl,
		'repayment_product_bnpl_new' : repaymentProductBnplNew,
		'change_payment_amount' : changePaymentAmount,
		'repayment_select_over_the_counter_option' : repaymentSelectOverTheCounterOption,
		'repayment_generate_barcode' : repaymentGenerateBarcode,
		'repayment_zero' : repaymentZero,
		'repayment_preferred_partner_stores' : repaymentPreferredPartnerStores,
		'repayment_real_time_posting' : repaymentRealTimePosting,
		'repayment_other_partner_stores' : repaymentOtherPartnerStores,
		'repayment_payment_instruction' : repaymentPaymentInstruction,
		'repayment_find_nearest_store' : repaymentFindNearestStore,
		'repayment_show_barcode_title' : repaymentShowBarcodeTitle,
		'repayment_show_barcode_message' : repaymentShowBarcodeMessage,
		'repayment_expiry_date' : repaymentExpiryDate,
		'repayment_account_type' : repaymentAccountType,
		'repayment_save_screenshot' : repaymentSaveScreenshot,
		'repayment_subtotal' : repaymentSubtotal,
		'repayment_save_screenshot_successfully' : repaymentSaveScreenshotSuccessfully,
		'repayment_save_screenshot_fail' : repaymentSaveScreenshotFail,
		'repayment_scan_qr_code' : repaymentScanQrCode,
		'repayment_please_transfer_to_this_account_below' : repaymentPleaseTransferToThisAccountBelow,
		'repayment_please_download_and_scan' : repaymentPleaseDownloadAndScan,
		'repayment_qr_bank_transfer' : repaymentQrBankTransfer,
		'repayment_method_selection_ewallet_title' : repaymentMethodSelectionEwalletTitle,
		'repayment_method_selection_ewallet_subtitle' : repaymentMethodSelectionEwalletSubtitle,
		'repayment_method_selection_internet_banking_title' : repaymentMethodSelectionInternetBankingTitle,
		'repayment_method_selection_internet_banking_subtitle' : repaymentMethodSelectionInternetBankingSubtitle,
		'repayment_method_selection_atm_title' : repaymentMethodSelectionAtmTitle,
		'repayment_method_selection_atm_subtitle' : repaymentMethodSelectionAtmSubtitle,
		'repayment_contract_number2' : repaymentContractNumber2,
		'repayment_main_title' : repaymentMainTitle,
		'repayment_main_payment_amount' : repaymentMainPaymentAmount,
		'repayment_main_custom_amount' : repaymentMainCustomAmount,
		'repayment_main_custom_amount_description' : repaymentMainCustomAmountDescription,
		'repayment_main_custom_amount_header' : repaymentMainCustomAmountHeader,
		'repayment_main_total_payment_amount' : repaymentMainTotalPaymentAmount,
		'repayment_main_proceed' : repaymentMainProceed,
		'repayment_pay_before' : repaymentPayBefore,
		'repayment_change' : repaymentChange,
		'repayment_select' : repaymentSelect,
		'repayment_select_loan' : repaymentSelectLoan,
		'repayment_overdue' : repaymentOverdue,
		'repayment_my_loans' : repaymentMyLoans,
		'repayment_contract_has_been_updated_back_to_home_message' : repaymentContractHasBeenUpdatedBackToHomeMessage,
		'repayment_contract_has_been_updated_select_next_contract_message' : repaymentContractHasBeenUpdatedSelectNextContractMessage,
		'repayment_ok' : repaymentOk,
		'repayment_online_payment_via_vnpay' : repaymentOnlinePaymentViaVnpay,
		'repayment_online_payment_via_onepay' : repaymentOnlinePaymentViaOnepay,
		'repayment_online_transfer_directly' : repaymentOnlineTransferDirectly,
		'repayment_choose_your_payment_method' : repaymentChooseYourPaymentMethod,
		'repayment_mobile_banking_desc' : repaymentMobileBankingDesc,
		'repayment_virtual_account' : repaymentVirtualAccount,
		'repayment_virtual_account_desc' : repaymentVirtualAccountDesc,
		'repayment_main_due_amount' : repaymentMainDueAmount,
		'repayment_mobile_banking_apps' : repaymentMobileBankingApps,
		'repayment_mobile_banking_app' : repaymentMobileBankingApp,
		'repayment_payment_via_vnpay' : repaymentPaymentViaVnpay,
		'repayment_payment_via_onepay' : repaymentPaymentViaOnepay,
		'repayment_atm_card' : repaymentAtmCard,
		'repayment_atm_card_header' : repaymentAtmCardHeader,
		'repayment_method' : repaymentMethod,
		'repayment_internet_banking' : repaymentInternetBanking,
		'repayment_bank_transfer_header' : repaymentBankTransferHeader,
		'repayment_back_to_homepage' : repaymentBackToHomepage,
		'repayment_select_amount_rel_minimum_amount_validation_title' : repaymentSelectAmountRelMinimumAmountValidationTitle,
		'repayment_select_amount_rel_bnpl_minimum_amount_validation_title' : repaymentSelectAmountRelBnplMinimumAmountValidationTitle,
		'repayment_select_amount_due_amount_validation_title' : repaymentSelectAmountDueAmountValidationTitle,
		'continue_payment' : continuePayment,
		'got_it' : gotIt,
		'repayment_pay_via_bank_transfer_is_not_available_mes' : repaymentPayViaBankTransferIsNotAvailableMes,
		'repayment_pay_via_bank_transfer_is_not_available_title' : repaymentPayViaBankTransferIsNotAvailableTitle,
		'transfer_to_account_number' : transferToAccountNumber,
		'scan_qr_code' : scanQrCode,
		'repayment_contract_information_normal' : repaymentContractInformationNormal,
		'repayment_total_amount_normal' : repaymentTotalAmountNormal,
		'repayment_due_date_normal' : repaymentDueDateNormal,
		'repayment_account_name_normal' : repaymentAccountNameNormal,
		'repayment_bank_name_normal' : repaymentBankNameNormal,
		'repayment_bank_branch_normal' : repaymentBankBranchNormal,
		'repayment_virtual_account_number_normal' : repaymentVirtualAccountNumberNormal,
		'repayment_please_choose_your_banking_app' : repaymentPleaseChooseYourBankingApp,
		'introduction' : introduction,
		'what_is_promise_to_pay' : whatIsPromiseToPay,
		'promise_to_pay_description' : promiseToPayDescription,
		'detail' : detail,
		'promise_to_pay_detail_title1' : promiseToPayDetailTitle1,
		'promise_to_pay_detail_message1' : promiseToPayDetailMessage1,
		'promise_to_pay_detail_title2' : promiseToPayDetailTitle2,
		'promise_to_pay_detail_message2' : promiseToPayDetailMessage2,
		'promise_to_pay_detail_title3' : promiseToPayDetailTitle3,
		'promise_to_pay_detail_message3' : promiseToPayDetailMessage3,
		'promise_to_pay_detail_title4' : promiseToPayDetailTitle4,
		'promise_to_pay_detail_message4' : promiseToPayDetailMessage4,
		'repayment_send' : repaymentSend,
		'repayment_select_payment_date' : repaymentSelectPaymentDate,
		'repayment_payment_plan' : repaymentPaymentPlan,
		'repayment_please_commit_as_selected' : repaymentPleaseCommitAsSelected,
		'repayment_day_later' : repaymentDayLater,
		'repayment_days_later' : repaymentDaysLater,
		'repayment_current_due_date' : repaymentCurrentDueDate,
		'repayment_extend_util_date' : repaymentExtendUtilDate,
		'repayment_min_payment_amount_is' : repaymentMinPaymentAmountIs,
		'repayment_want_to_extend_payment_date' : repaymentWantToExtendPaymentDate,
		'repayment_you_need_to_commit_payment_as_selected_date' : repaymentYouNeedToCommitPaymentAsSelectedDate,
		'repayment_i_agree' : repaymentIAgree,
		'repayment_cancel' : repaymentCancel,
		'repayment_ptp_processing_title' : repaymentPtpProcessingTitle,
		'repayment_ptp_processing_sub_title' : repaymentPtpProcessingSubTitle,
		'repayment_ptp_process_back_button' : repaymentPtpProcessBackButton,
		'promise_to_pay' : promiseToPay,
		'promise_to_pay_on_time_message' : promiseToPayOnTimeMessage,
		'promise_to_pay_remind_message' : promiseToPayRemindMessage,
		'repayment_total_due_amount_normal' : repaymentTotalDueAmountNormal,
		'repayment_ptp_success_title' : repaymentPtpSuccessTitle,
		'repayment_ptp_success_main_title' : repaymentPtpSuccessMainTitle,
		'repayment_ptp_success_main_des' : repaymentPtpSuccessMainDes,
		'repayment_ptp_success_detail_due_date' : repaymentPtpSuccessDetailDueDate,
		'repayment_ptp_success_detail_amount' : repaymentPtpSuccessDetailAmount,
		'repayment_ptp_success_done_button' : repaymentPtpSuccessDoneButton,
		'repayment_cc_outstanding_period_amount' : repaymentCcOutstandingPeriodAmount,
		'repayment_ptp_eligibility_checking_title' : repaymentPtpEligibilityCheckingTitle,
		'repayment_ptp_eligibility_popup_title' : repaymentPtpEligibilityPopupTitle,
		'repayment_ptp_eligibility_popup_i_see_button' : repaymentPtpEligibilityPopupISeeButton,
		'promotion' : promotion,
		'select' : select,
		'change' : change,
		'select_voucher_to_get_discount' : selectVoucherToGetDiscount,
		'voucher_is_not_applicable' : voucherIsNotApplicable,
		'repayment_view_details' : repaymentViewDetails,
		'repayment_min_repayment' : repaymentMinRepayment,
		'repayment_promotion' : repaymentPromotion,
		'repayment_apply' : repaymentApply,
		'repayment_use_now' : repaymentUseNow,
		'repayment_use_later' : repaymentUseLater,
		'repayment_voucher_details' : repaymentVoucherDetails,
		'repayment_this_voucher_is_applied' : repaymentThisVoucherIsApplied,
		'repayment_ptp_popup_retry_title' : repaymentPtpPopupRetryTitle,
		'repayment_ptp_popup_retry_retry_button' : repaymentPtpPopupRetryRetryButton,
		'repayment_ptp_popup_retry_cancel_button' : repaymentPtpPopupRetryCancelButton,
		'repayment_sorry_we_dont_have_any_voucher' : repaymentSorryWeDontHaveAnyVoucher,
		'repayment_ptp_submit_error' : repaymentPtpSubmitError,
		'repayment_voucher_not_applicable' : repaymentVoucherNotApplicable,
		'repayment_your_voucher_is_not_applicable' : repaymentYourVoucherIsNotApplicable,
		'repayment_skip_voucher_and_continue' : repaymentSkipVoucherAndContinue,
		'repayment_skip_voucher_and_continue_question' : repaymentSkipVoucherAndContinueQuestion,
		'repayment_see_other_vouchers' : repaymentSeeOtherVouchers,
		'repayment_ptp_rejected_title' : repaymentPtpRejectedTitle,
		'repayment_ptp_rejected_main_title' : repaymentPtpRejectedMainTitle,
		'repayment_ptp_rejected_main_des' : repaymentPtpRejectedMainDes,
		'repayment_ptp_rejected_detail_due_date' : repaymentPtpRejectedDetailDueDate,
		'repayment_ptp_rejected_detail_amount' : repaymentPtpRejectedDetailAmount,
		'repayment_ptp_rejected_done_button' : repaymentPtpRejectedDoneButton,
		'payment_amount' : paymentAmount,
		'repayment_discount_amount' : repaymentDiscountAmount,
		'repayment_final_amount' : repaymentFinalAmount,
		'repayment_refresh' : repaymentRefresh,
		'repayment_something_went_wrong_please_try_again' : repaymentSomethingWentWrongPleaseTryAgain,
		'repayment_cancel_payment' : repaymentCancelPayment,
		'repayment_cancel_payment_message' : repaymentCancelPaymentMessage,
		'repayment_quit' : repaymentQuit,
		'repayment_description' : repaymentDescription,
		'repayment_dismiss_offer' : repaymentDismissOffer,
		'repayment_download_qr_code' : repaymentDownloadQrCode,
		'repayment_download_the_qr_code' : repaymentDownloadTheQrCode,
		'repayment_to_apply' : repaymentToApply,
		'repayment_dismiss_promo' : repaymentDismissPromo,
		'repayment_promotion_not_applicable' : repaymentPromotionNotApplicable,
		'repayment_transfer_to_account_number' : repaymentTransferToAccountNumber,
		'repayment_please_scan_qr_to_apply_promotion' : repaymentPleaseScanQrToApplyPromotion,
		'repayment_qr_discount' : repaymentQrDiscount,
		'repayment_cancel_voucher_if_not_apply' : repaymentCancelVoucherIfNotApply,
		'repayment_skip_promotion_download_qr' : repaymentSkipPromotionDownloadQr,
		'repayment_skip_promotion_and_payment' : repaymentSkipPromotionAndPayment,
		'repayment_skip_promotion_60_min' : repaymentSkipPromotion60Min,
		'repayment_skip_promotion_to_apply_voucher_cancel' : repaymentSkipPromotionToApplyVoucherCancel,
		'repayment_successful' : repaymentSuccessful,
		'repayment_please_wait_few_minutes' : repaymentPleaseWaitFewMinutes,
		'repayment_amount' : repaymentAmount,
		'repayment_total' : repaymentTotal,
		'repayment_transaction_information' : repaymentTransactionInformation,
		'repayment_processing_date' : repaymentProcessingDate,
		'repayment_payment_methods' : repaymentPaymentMethods,
		'repayment_ewallet_title' : repaymentEwalletTitle,
		'repayment_ewallet_introduce_title' : repaymentEwalletIntroduceTitle,
		'repayment_ewallet_introduce_message' : repaymentEwalletIntroduceMessage,
		'repayment_ewallet_alfamart' : repaymentEwalletAlfamart,
		'repayment_ewallet_bca' : repaymentEwalletBca,
		'repayment_ewallet_bni' : repaymentEwalletBni,
		'repayment_ewallet_cimb' : repaymentEwalletCimb,
		'repayment_ewallet_atm_bersama_prima' : repaymentEwalletAtmBersamaPrima,
		'repayment_ewallet_bersama_prima' : repaymentEwalletBersamaPrima,
		'repayment_ewallet_alfamart_description' : repaymentEwalletAlfamartDescription,
		'repayment_ewallet_payment_guideline' : repaymentEwalletPaymentGuideline,
		'repayment_ewallet_alfamart_step1' : repaymentEwalletAlfamartStep1,
		'repayment_ewallet_alfamart_step2' : repaymentEwalletAlfamartStep2,
		'repayment_ewallet_alfamart_step3' : repaymentEwalletAlfamartStep3,
		'repayment_ewallet_alfamart_step4' : repaymentEwalletAlfamartStep4,
		'repayment_ewallet_alfamart_step5' : repaymentEwalletAlfamartStep5,
		'repayment_ewallet_alfamart_step6' : repaymentEwalletAlfamartStep6,
		'repayment_ewallet_virtual_account_number' : repaymentEwalletVirtualAccountNumber,
		'repayment_ewallet_atm' : repaymentEwalletAtm,
		'repayment_ewallet_klik_bca' : repaymentEwalletKlikBca,
		'repayment_ewallet_m_bca' : repaymentEwalletMBca,
		'repayment_ewallet_sms_banking' : repaymentEwalletSmsBanking,
		'repayment_ewallet_internet_banking' : repaymentEwalletInternetBanking,
		'repayment_ewallet_m_banking' : repaymentEwalletMBanking,
		'repayment_ewallet_cimb_clicks' : repaymentEwalletCimbClicks,
		'repayment_ewallet_go_mobile_cimb' : repaymentEwalletGoMobileCimb,
		'repayment_ewallet_bca_atm_step1' : repaymentEwalletBcaAtmStep1,
		'repayment_ewallet_bca_atm_step2' : repaymentEwalletBcaAtmStep2,
		'repayment_ewallet_bca_atm_step3' : repaymentEwalletBcaAtmStep3,
		'repayment_ewallet_bca_atm_step4' : repaymentEwalletBcaAtmStep4,
		'repayment_ewallet_bca_atm_step5' : repaymentEwalletBcaAtmStep5,
		'repayment_ewallet_bca_klik_bca_step1' : repaymentEwalletBcaKlikBcaStep1,
		'repayment_ewallet_bca_klik_bca_step2' : repaymentEwalletBcaKlikBcaStep2,
		'repayment_ewallet_bca_klik_bca_step3' : repaymentEwalletBcaKlikBcaStep3,
		'repayment_ewallet_bca_klik_bca_step4' : repaymentEwalletBcaKlikBcaStep4,
		'repayment_ewallet_bca_klik_bca_step5' : repaymentEwalletBcaKlikBcaStep5,
		'repayment_ewallet_bca_m_bca_step1' : repaymentEwalletBcaMBcaStep1,
		'repayment_ewallet_bca_m_bca_step2' : repaymentEwalletBcaMBcaStep2,
		'repayment_ewallet_bca_m_bca_step3' : repaymentEwalletBcaMBcaStep3,
		'repayment_ewallet_bca_m_bca_step4' : repaymentEwalletBcaMBcaStep4,
		'repayment_ewallet_bca_m_bca_step5' : repaymentEwalletBcaMBcaStep5,
		'repayment_ewallet_bca_m_bca_step6' : repaymentEwalletBcaMBcaStep6,
		'repayment_ewallet_bni_atm_step1' : repaymentEwalletBniAtmStep1,
		'repayment_ewallet_bni_atm_step2' : repaymentEwalletBniAtmStep2,
		'repayment_ewallet_bni_atm_step3' : repaymentEwalletBniAtmStep3,
		'repayment_ewallet_bni_atm_step4' : repaymentEwalletBniAtmStep4,
		'repayment_ewallet_bni_atm_step5' : repaymentEwalletBniAtmStep5,
		'repayment_ewallet_bni_sms_banking_step1' : repaymentEwalletBniSmsBankingStep1,
		'repayment_ewallet_bni_sms_banking_step2' : repaymentEwalletBniSmsBankingStep2,
		'repayment_ewallet_bni_internet_banking_step1' : repaymentEwalletBniInternetBankingStep1,
		'repayment_ewallet_bni_internet_banking_step2' : repaymentEwalletBniInternetBankingStep2,
		'repayment_ewallet_bni_internet_banking_step3' : repaymentEwalletBniInternetBankingStep3,
		'repayment_ewallet_bni_internet_banking_step4' : repaymentEwalletBniInternetBankingStep4,
		'repayment_ewallet_bni_internet_banking_step5' : repaymentEwalletBniInternetBankingStep5,
		'repayment_ewallet_bni_internet_banking_step6' : repaymentEwalletBniInternetBankingStep6,
		'repayment_ewallet_bni_m_banking_step1' : repaymentEwalletBniMBankingStep1,
		'repayment_ewallet_bni_m_banking_step2' : repaymentEwalletBniMBankingStep2,
		'repayment_ewallet_bni_m_banking_step3' : repaymentEwalletBniMBankingStep3,
		'repayment_ewallet_bni_m_banking_step4' : repaymentEwalletBniMBankingStep4,
		'repayment_ewallet_bni_m_banking_step5' : repaymentEwalletBniMBankingStep5,
		'repayment_ewallet_bni_m_banking_step6' : repaymentEwalletBniMBankingStep6,
		'repayment_ewallet_cimb_atm_step1' : repaymentEwalletCimbAtmStep1,
		'repayment_ewallet_cimb_atm_step2' : repaymentEwalletCimbAtmStep2,
		'repayment_ewallet_cimb_atm_step3' : repaymentEwalletCimbAtmStep3,
		'repayment_ewallet_cimb_atm_step4' : repaymentEwalletCimbAtmStep4,
		'repayment_ewallet_cimb_atm_step5' : repaymentEwalletCimbAtmStep5,
		'repayment_ewallet_cimb_clicks_step1' : repaymentEwalletCimbClicksStep1,
		'repayment_ewallet_cimb_clicks_step2' : repaymentEwalletCimbClicksStep2,
		'repayment_ewallet_cimb_clicks_step3' : repaymentEwalletCimbClicksStep3,
		'repayment_ewallet_cimb_clicks_step4' : repaymentEwalletCimbClicksStep4,
		'repayment_ewallet_cimb_clicks_step5' : repaymentEwalletCimbClicksStep5,
		'repayment_ewallet_cimb_clicks_step6' : repaymentEwalletCimbClicksStep6,
		'repayment_ewallet_cimb_go_mobile_step1' : repaymentEwalletCimbGoMobileStep1,
		'repayment_ewallet_cimb_go_mobile_step2' : repaymentEwalletCimbGoMobileStep2,
		'repayment_ewallet_cimb_go_mobile_step3' : repaymentEwalletCimbGoMobileStep3,
		'repayment_ewallet_cimb_go_mobile_step4' : repaymentEwalletCimbGoMobileStep4,
		'repayment_ewallet_cimb_go_mobile_step5' : repaymentEwalletCimbGoMobileStep5,
		'repayment_ewallet_cimb_go_mobile_step6' : repaymentEwalletCimbGoMobileStep6,
		'repayment_ewallet_bersama_atm_step1' : repaymentEwalletBersamaAtmStep1,
		'repayment_ewallet_bersama_atm_step2' : repaymentEwalletBersamaAtmStep2,
		'repayment_ewallet_bersama_atm_step3' : repaymentEwalletBersamaAtmStep3,
		'repayment_ewallet_bersama_atm_step4' : repaymentEwalletBersamaAtmStep4,
		'repayment_ewallet_bersama_atm_step5' : repaymentEwalletBersamaAtmStep5,
		'repayment_ewallet_bersama_internet_banking_step1' : repaymentEwalletBersamaInternetBankingStep1,
		'repayment_ewallet_bersama_internet_banking_step2' : repaymentEwalletBersamaInternetBankingStep2,
		'repayment_ewallet_bersama_internet_banking_step3' : repaymentEwalletBersamaInternetBankingStep3,
		'repayment_ewallet_bersama_internet_banking_step4' : repaymentEwalletBersamaInternetBankingStep4,
		'repayment_ewallet_bersama_internet_banking_step5' : repaymentEwalletBersamaInternetBankingStep5,
		'repayment_ewallet_bersama_internet_banking_step6' : repaymentEwalletBersamaInternetBankingStep6,
		'repayment_ewallet_bersama_m_banking_step1' : repaymentEwalletBersamaMBankingStep1,
		'repayment_ewallet_bersama_m_banking_step2' : repaymentEwalletBersamaMBankingStep2,
		'repayment_ewallet_bersama_m_banking_step3' : repaymentEwalletBersamaMBankingStep3,
		'repayment_ewallet_bersama_m_banking_step4' : repaymentEwalletBersamaMBankingStep4,
		'repayment_ewallet_bersama_m_banking_step5' : repaymentEwalletBersamaMBankingStep5,
		'repayment_ewallet_bersama_m_banking_step6' : repaymentEwalletBersamaMBankingStep6,
		'repayment_ewallet_no_phone_number_title' : repaymentEwalletNoPhoneNumberTitle,
		'repayment_ewallet_no_phone_number_message' : repaymentEwalletNoPhoneNumberMessage,
		'repayment_success_loan_offers_section_title' : repaymentSuccessLoanOffersSectionTitle,
		'repayment_momo' : repaymentMomo,
		'repayment_online_repayment' : repaymentOnlineRepayment,
		'repayment_enjoy_quick_easy_online_repayment' : repaymentEnjoyQuickEasyOnlineRepayment,
		'repayment_why_should_i_pay_online' : repaymentWhyShouldIPayOnline,
		'repayment_pay_online_anywhere' : repaymentPayOnlineAnywhere,
		'repayment_connect_accounts' : repaymentConnectAccounts,
		'repayment_fast_and_convenient' : repaymentFastAndConvenient,
		'repayment_what_account_i_can_use' : repaymentWhatAccountICanUse,
		'repayment_online_banking_accounts_des' : repaymentOnlineBankingAccountsDes,
		'repayment_debit_cards_des' : repaymentDebitCardsDes,
		'repayment_online_bank_title' : repaymentOnlineBankTitle,
		'repayment_online_bank_desc' : repaymentOnlineBankDesc,
		'repayment_debit_card_title' : repaymentDebitCardTitle,
		'repayment_debit_card_desc' : repaymentDebitCardDesc,
		'repayment_no_fee' : repaymentNoFee,
		'repayment_choose_payment_option' : repaymentChoosePaymentOption,
		'repayment_your_order' : repaymentYourOrder,
		'repayment_ada_intro_easy_to_repay' : repaymentAdaIntroEasyToRepay,
		'repayment_ada_intro_first_title' : repaymentAdaIntroFirstTitle,
		'repayment_ada_intro_first_subtitle' : repaymentAdaIntroFirstSubtitle,
		'repayment_ada_intro_second_title' : repaymentAdaIntroSecondTitle,
		'repayment_ada_intro_second_subtitle' : repaymentAdaIntroSecondSubtitle,
		'repayment_ada_intro_third_title' : repaymentAdaIntroThirdTitle,
		'repayment_ada_intro_third_subtitle' : repaymentAdaIntroThirdSubtitle,
		'repayment_ada_no_active_ada_title' : repaymentAdaNoActiveAdaTitle,
		'repayment_ada_no_active_ada_subtitle' : repaymentAdaNoActiveAdaSubtitle,
		'repayment_ada_enroll' : repaymentAdaEnroll,
		'repayment_ada_payment_via_momo' : repaymentAdaPaymentViaMomo,
		'repayment_ada_enroll_success' : repaymentAdaEnrollSuccess,
		'repayment_ada_enroll_success_description' : repaymentAdaEnrollSuccessDescription,
		'repayment_ada_contract_type' : repaymentAdaContractType,
		'repayment_ada_payment_method' : repaymentAdaPaymentMethod,
		'repayment_ada_due_date_cel' : repaymentAdaDueDateCel,
		'repayment_ada_due_date_rel' : repaymentAdaDueDateRel,
		'repayment_ada_amount' : repaymentAdaAmount,
		'repayment_ada_amount_limit_warning' : repaymentAdaAmountLimitWarning,
		'repayment_done' : repaymentDone,
		'repayment_ada_main_title' : repaymentAdaMainTitle,
		'repayment_ada_main_auto_deduct_date_cel' : repaymentAdaMainAutoDeductDateCel,
		'repayment_ada_main_auto_deduct_date_rel' : repaymentAdaMainAutoDeductDateRel,
		'repayment_ada_main_auto_deduct_amount' : repaymentAdaMainAutoDeductAmount,
		'repayment_ada_main_min_amount_due' : repaymentAdaMainMinAmountDue,
		'repayment_ada_main_pay_mad_note' : repaymentAdaMainPayMadNote,
		'repayment_ada_main_payment_method_desc' : repaymentAdaMainPaymentMethodDesc,
		'repayment_ada_main_due_date_rel_desc' : repaymentAdaMainDueDateRelDesc,
		'repayment_ada_main_auto_deduct_date_rel_desc' : repaymentAdaMainAutoDeductDateRelDesc,
		'repayment_ada_main_deduct' : repaymentAdaMainDeduct,
		'repayment_ada_onepay' : repaymentAdaOnepay,
		'repayment_ada_zalopay' : repaymentAdaZalopay,
		'repayment_ada_main_cancel_ada_popup_title' : repaymentAdaMainCancelAdaPopupTitle,
		'repayment_ada_main_cancel_ada_popup_desc' : repaymentAdaMainCancelAdaPopupDesc,
		'repayment_delete' : repaymentDelete,
		'repayment_back' : repaymentBack,
		'repayment_ada_main_payment_method' : repaymentAdaMainPaymentMethod,
		'service_fee' : serviceFee,
		'repayment_sappi_desc' : repaymentSappiDesc,
		'valid_one_year' : validOneYear,
		'valid_loan_duration' : validLoanDuration,
		'repayment_ada_credit_card' : repaymentAdaCreditCard,
		'repayment_ada_cash_loan' : repaymentAdaCashLoan,
		'repayment_due_date_desc' : repaymentDueDateDesc,
		'repayment_ada_due_date_cel_desc' : repaymentAdaDueDateCelDesc,
		'repayment_ada_due_date_rel_desc' : repaymentAdaDueDateRelDesc,
		'repayment_ada_failed_register' : repaymentAdaFailedRegister,
		'repayment_ada_cel_failed_register_desc' : repaymentAdaCelFailedRegisterDesc,
		'repayment_ada_cc_failed_register_desc' : repaymentAdaCcFailedRegisterDesc,
		'repayment_ada_bnpl_failed_register_desc' : repaymentAdaBnplFailedRegisterDesc,
		'repayment_ada_bnpl_failed_register_desc_new' : repaymentAdaBnplFailedRegisterDescNew,
		'repayment_promotion_first_adoption_pos' : repaymentPromotionFirstAdoptionPos,
		'repayment_promotion_first_adoption_app' : repaymentPromotionFirstAdoptionApp,
		'repayment_promotion_first_adoption_button' : repaymentPromotionFirstAdoptionButton,
		'repayment_back_later' : repaymentBackLater,
		'repayment_pay_with_auto_debit_arrangement_title' : repaymentPayWithAutoDebitArrangementTitle,
		'repayment_we_willdeduct_your_loan_title' : repaymentWeWilldeductYourLoanTitle,
		'repayment_make_sure_you_have_enough_funds_title' : repaymentMakeSureYouHaveEnoughFundsTitle,
		'repayment_card_details' : repaymentCardDetails,
		'repayment_account_details' : repaymentAccountDetails,
		'repayment_additional_info_title' : repaymentAdditionalInfoTitle,
		'repayment_additional_info_desc' : repaymentAdditionalInfoDesc,
		'no_fee' : noFee,
		'repayment_ada_explore_auto_debit' : repaymentAdaExploreAutoDebit,
		'repayment_ada_explore_auto_debit_description' : repaymentAdaExploreAutoDebitDescription,
		'repayment_ada_explore' : repaymentAdaExplore,
		'repayment_ada_later' : repaymentAdaLater,
		'repayment_ada_explore_auto_debit_feature' : repaymentAdaExploreAutoDebitFeature,
		'repayment_payment_via' : repaymentPaymentVia,
		'repayment_monthly' : repaymentMonthly,
		'repayment_ada_no_loan_title' : repaymentAdaNoLoanTitle,
		'repayment_ada_no_loan_description' : repaymentAdaNoLoanDescription,
		'repayment_overdue_desc' : repaymentOverdueDesc,
		'repayment_confirm' : repaymentConfirm,
		'repayment_ada_no_eligible_contract_message' : repaymentAdaNoEligibleContractMessage,
		'something_went_wrong' : somethingWentWrong,
		'repayment_ada_cancel_ada_error_try_again' : repaymentAdaCancelAdaErrorTryAgain,
		'repayment_ada_back_later' : repaymentAdaBackLater,
		'repayment_ada_try_again' : repaymentAdaTryAgain,
		'repayment_ada_payment_method_for_ad' : repaymentAdaPaymentMethodForAd,
		'feature_unavailable' : featureUnavailable,
		'repayment_brankas_notice' : repaymentBrankasNotice,
		'repayment_brankas_agreement_phrase_1' : repaymentBrankasAgreementPhrase1,
		'repayment_brankas_hc_privacy_notice' : repaymentBrankasHcPrivacyNotice,
		'repayment_brankas_agreement_phrase_2' : repaymentBrankasAgreementPhrase2,
		'repayment_tnc' : repaymentTnc,
		'repayment_brankas_agreement_phrase_3' : repaymentBrankasAgreementPhrase3,
		'repayment_brankas_term_of_use' : repaymentBrankasTermOfUse,
		'repayment_brankas_agreement_phrase_4' : repaymentBrankasAgreementPhrase4,
		'repayment_brankas_privacy_notice' : repaymentBrankasPrivacyNotice,
		'repayment_brankas_terms_of_use' : repaymentBrankasTermsOfUse,
		'repayment_brankas_privacy_notice_header' : repaymentBrankasPrivacyNoticeHeader,
		'repayment_bank_unavailable' : repaymentBankUnavailable,
		'repayment_bank_transfer_title' : repaymentBankTransferTitle,
		'repayment_bank_transfer_download_and_open_bank' : repaymentBankTransferDownloadAndOpenBank,
		'repayment_bank_transfer_copy_and_open_bank' : repaymentBankTransferCopyAndOpenBank,
		'repayment_bank_transfer_download_qr' : repaymentBankTransferDownloadQr,
		'repayment_bank_transfer_save_account_number' : repaymentBankTransferSaveAccountNumber,
		'repayment_bank_transfer_payment_info' : repaymentBankTransferPaymentInfo,
		'repayment_bank_transfer_download_qr_alert' : repaymentBankTransferDownloadQrAlert,
		'repayment_bank_transfer_save_account_number_alert' : repaymentBankTransferSaveAccountNumberAlert,
		'repayment_bank_transfer_total_amount' : repaymentBankTransferTotalAmount,
		'repayment_bank_transfer_view_intro' : repaymentBankTransferViewIntro,
		'repayment_bank_transfer_home_credit_name' : repaymentBankTransferHomeCreditName,
		'repayment_bank_transfer_home_credit_bidv_bank' : repaymentBankTransferHomeCreditBidvBank,
		'repayment_bank_transfer_account_number' : repaymentBankTransferAccountNumber,
		'repayment_bank_transfer_intro_title' : repaymentBankTransferIntroTitle,
		'repayment_bank_transfer_intro_guide_title' : repaymentBankTransferIntroGuideTitle,
		'repayment_bank_transfer_intro_guide_subtitle' : repaymentBankTransferIntroGuideSubtitle,
		'repayment_bank_transfer_intro_1' : repaymentBankTransferIntro1,
		'repayment_bank_transfer_intro_2' : repaymentBankTransferIntro2,
		'repayment_bank_transfer_intro_3' : repaymentBankTransferIntro3,
		'repayment_bank_transfer_intro_ok' : repaymentBankTransferIntroOk,
		'repayment_bank_transfer_qr_waiting_title' : repaymentBankTransferQrWaitingTitle,
		'repayment_bank_transfer_qr_waiting_desc' : repaymentBankTransferQrWaitingDesc,
		'repayment_bank_transfer_qr_waiting_primary_btn_title' : repaymentBankTransferQrWaitingPrimaryBtnTitle,
		'repayment_bank_transfer_qr_waiting_second_btn_title' : repaymentBankTransferQrWaitingSecondBtnTitle,
		'repayment_choose_bank' : repaymentChooseBank,
		'repayment_search_bank_hint' : repaymentSearchBankHint,
		'repayment_valid_to' : repaymentValidTo,

  };

  @deprecated
  String tr(String key) {
    return _translate[key] ?? key;
  }
}

class GeneratedLocalizationsDelegate extends LocalizationsDelegate<L10nCappRepayment> {
  const GeneratedLocalizationsDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
			Locale("vi", "VN"),
			Locale("hi", "IN"),
			Locale("en", "VN"),
			Locale("en", "IN"),

    ];
  }

  LocaleListResolutionCallback listResolution({Locale? fallback}) {
    return (locales, supported) {
      if (locales == null || locales.isEmpty) {
        return fallback ?? supported.first;
      } else {
        return _resolve(locales.first, fallback, supported);
      }
    };
  }

  LocaleResolutionCallback resolution({Locale? fallback}) {
    return (locale, supported) {
      return _resolve(locale, fallback, supported);
    };
  }

  Locale _resolve(Locale? locale, Locale? fallback, Iterable<Locale> supported) {
    if (locale == null || !isSupported(locale)) {
      return fallback ?? supported.first;
    }

    final Locale languageLocale = Locale(locale.languageCode, "");
    if (supported.contains(locale)) {
      return locale;
    } else if (supported.contains(languageLocale)) {
      return languageLocale;
    } else {
      final Locale fallbackLocale = fallback ?? supported.first;
      return fallbackLocale;
    }
  }

  @override
  Future<L10nCappRepayment> load(Locale locale) {
    return MultipleLocalizations.load(
        initializeMessages, locale, (l) => L10nCappRepayment.load(locale),
        setDefaultLocale: true);
  }

  @override
  bool isSupported(Locale locale) =>
    locale != null && supportedLocales.contains(locale);

  @override
  bool shouldReload(GeneratedLocalizationsDelegate old) => false;
}

// ignore_for_file: unnecessary_brace_in_string_interps
