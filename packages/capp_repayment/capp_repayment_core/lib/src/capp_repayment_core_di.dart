import 'package:get_it/get_it.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../capp_repayment_core.dart';

void registerCoreDependencies(GetIt c) {
  c
    ..registerLazySingleton(
      () => CappRepaymentTrackingService(
        eventTrackingService: c<EventTrackingService>(),
        currentUserRepository: c<ICurrentUserRepository>(),
        userRepository: c<IUserRepository>(),
        gtp: c<GlobalTrackingProperties>(),
      ),
    )
    ..registerLazySingleton<IBannerRepository>(
      () => BannerRepository(
        bannerApi: c<BannerApi>(),
        logger: c.get<Logger>(),
      ),
    )
    ..registerTrackingFactory(
      () => RepaymentBannerBloc(
        bannerRepository: c.get<IBannerRepository>(),
        imageService: c<ImageServiceBase>(),
        logger: c.get<Logger>(),
      ),
    );
}
