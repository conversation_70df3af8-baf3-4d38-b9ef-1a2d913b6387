class CappRepaymentTrackingCategories {
  static const screenView = 'screen_view';
  static const accountTransactionDashboard = 'account_transaction_dashboard';
  static const repaymentForOwnContractSelection = 'rpy_cntct_select';
  static const repaymentForOwnNoLoan = 'rpy_no_loan';
  static const repaymentForOwnMethodSelection = 'rpy_methd_select';
  static const repaymentForOwnCelAmount = 'rpy_cel_amt';
  static const repaymentForOwnOnlinePaymentOption = 'rpy_onl_pmt_opt';
  static const repaymentForOwnOnlinePaymentIntro = 'rpy_onl_pmt_intro';
  static const repaymentForOwnBankTransfer = 'rpy_bnk_trnsf';
  static const repaymentForOwnOnlinePaymentSummary = 'rpy_onl_pmt_smry';
  static const repaymentForOwnTransactionSuccess = 'rpy_trans_success';
  static const repaymentForOwnTransactionError = 'rpy_trans_error';
  static const repaymentForOwnRelAmount = 'rpy_rel_amt';
  static const repaymentForOwnOnlineCardVerification = 'rpy_onl_card_verification';
  static const repaymentForOwnBankTransferInstruction = 'rpy_bnk_trnsf_instrtn';
  static const repaymentForOwnEcomEmoney = 'rpy_ecom_emoney';
  static const repaymentForOwnVirtualAccountBankTransfer = 'rpy_va_bnk_trnsf';
  static const repaymentForOwnInAppMethodSelection = 'rpy_inapp_methd_select';
  static const repaymentForOwnAdaIntroduction = 'rpy_ada_intro';
  static const repaymentForOwnAdaPaymentOption = 'rpy_ada_pmt_opt';
  static const repaymentForOwnAdaTermsAndConditions = 'rpy_ada_tnc';
  static const repaymentForOwnAdaTermsAndConditionsDetail = 'rpy_ada_tnc_detail';
  static const repaymentForOwnAdaConfirmation = 'rpy_ada_confirmation';
  static const repaymentForOwnAdaSuccess = 'rpy_ada_success';
  static const repaymentForOwnOtcPartnerSelection = 'rpy_otc_prtnr_select';
  static const repaymentForOwnOtcPartnerInstruction = 'rpy_otc_prtnr_instrtn';
  static const repaymentForOwnOtcPartnerSummary = 'rpy_otc_prtnr_smry';
  static const repaymentForOwnOtcCelAmount = 'rpy_otc_cel_amt';
  static const repaymentForOwnOtcRelAmount = 'rpy_otc_rel_amt';
  static const repaymentForOwnAtmCardProcessing = 'rpy_atm_processing';
  static const repaymentForOwnMain = 'rpy_main';
  static const repaymentPromiseToPayEligibilityChecking = 'ptp_checking';
  static const repaymentPromiseToPaySuccess = 'ptp_success';
  static const repaymentPromiseToPayProcessing = 'ptp_loading';
  static const repaymentPromiseToPayIntroduction = 'ptp_intro_first_open';
  static const repaymentPromiseToPayOptions = 'ptp_information';
  static const repaymentPromiseToPayPlan = 'ptp_plan';
  static const repaymentDiscountUnselectedVoucherDetail = 'discnt_unsel_vcher_detail';
  static const repaymentDiscountSelectedVoucherDetail = 'discnt_sel_vcher_detail';
  static const repaymentAdaIntro = 'ad_intro';
  static const repaymentAdaMain = 'ad_main';
  static const repaymentAdaManagement = 'ad_mgm';
  static const repaymentAdaDetail = 'ad_detail';
  static const repaymentAdaSuccess = 'ad_success';
  static const repaymentAdaFail = 'ad_fail';
  static const repaymentAdaOnepayRegister = 'ad_atm_processing';
  static const repaymentAdaOnepayCancelOtp = 'ad_atm_otp_cnl_processing';
  static const loanOriginationAutoDebitAgreement = 'loo_auto_debit_agr';
  static const loanOriginationAutoDebitAgreementIntro = 'loo_auto_debit_agr_intro';
  static const loanOriginationAutoDebitAgreementCancel = 'loo_auto_debit_agr_cancel';
  static const loanOriginationAutoDebitAgreementError = 'loo_auto_debit_agr_error';
  static const loanOriginationPaymentMethod = 'loo_payment_method';
}
