class CappRepaymentTrackingLabels {
  static const repayment = 'repayment';
  static const celContract = 'cel_cntct';
  static const relContract = 'rel_cntct';
  static const repaymentContinue = 'continue';
  static const online = 'online';
  static const payByCash = 'pay_by_cash';
  static const bankTransfer = 'bnk_trnsf';
  static const repaymentForOwnCelAmount = 'rpy_cel_amt';
  static const fullDueAmount = 'full_due_amt';
  static const customAmount = 'custom_amt';
  static const warningPopup = 'warning_ppup';
  static const warningPopupEditAmount = 'warning_ppup_edit_amt';
  static const warningPopupContinue = 'warning_ppup_ctn';
  static const repaymentForOwnOnlinePaymentOption = 'rpy_onl_pmt_opt';
  static const onepay = 'onepay';
  static const momo = 'momo';
  static const viettelpay = 'viettelpay';
  static const shopeePay = 'shopeepay';
  static const zaloPay = 'zalopay';
  static const vnPay = 'vnpay';
  static const copyAccountNumber = 'copy_acct_no';
  static const copyTransferContent = 'copy_transfer_content';
  static const repaymentForOwnBankTransfer = 'rpy_bnk_trnsf';
  static const repaymentForOwnOnlinePaymentSummary = 'rpy_onl_pmt_smry';
  static const cancel = 'cancel';
  static const confirm = 'confirm';
  static const repaymentForOwnTransactionSuccess = 'rpy_trans_success';
  static const copyTransactionNo = 'copy_trans_no';
  static const done = 'done';
  static const repaymentForOwnTransactionError = 'rpy_trans_error';
  static const backToHomepage = 'back_to_home';
  static const tryAgain = 'try_again';
  static const repaymentForOwnRelAmount = 'rpy_rel_amt';
  static const minimumAmount = 'minimum_amt';
  static const addCard = 'add_card';
  static const masterCard = 'master_card';
  static const visaCard = 'visa_card';
  static const verify = 'verify';
  static const amount = 'amt';
  static const autoDebit = 'auto_debit';
  static const looAutoDebitAgreement = 'loo_auto_debit_agreement';
  static const looAutoDebitAgreementIntro = 'loo_auto_debit_agreement_intro';
  static const agrIntro = 'agr_intro';
  static const payOverCounter = 'pay_over_counter';
  static const atmGuidance = 'atm_guidance';
  static const mobileBankingGuidance = 'mobbnk_guidance';
  static const ecomBlibli = 'ecom_bibli';
  static const ecomBukalapak = 'ecom_bukalapak';
  static const ecomLazada = 'ecom_lazada';
  static const ecomQris = 'ecom_qris';
  static const ecomTakopedia = 'ecom_takopedia';
  static const emoneyAyopop = 'emoney_ayopop';
  static const emoneyBebasbayar = 'emoney_BebasBayar';
  static const emoneyGotagihan = 'emoney_gotagihan';
  static const atmPrimaAlto = 'atm_bms_pr_al';
  static const atmBersama = 'atm_bersama';
  static const bankBca = 'bank_bca';
  static const bankBni = 'bank_bni';
  static const bankBtpn = 'bank_btpn';
  static const bankBri = 'bank_bri';
  static const bankMandiri = 'bank_mandiri';
  static const bankPermata = 'bank_permata';
  static const debitCard = 'debit_card';
  static const bcaKlikpay = 'bca_klikpay';
  static const briEpay = 'bri_e_pay';
  static const cimbOctoclick = 'cimb_octoclick';
  static const danamonInternetBanking = 'dnmn_intnetbnk';
  static const setUpNow = 'set_up_now';
  static const termsAndConditions = 'terms_and_conditions';
  static const confirmAndEnroll = 'confirm';
  static const cancelEnrollment = 'cancel_enroll';
  static const eWallet = 'ewallet';
  static const internetBanking = 'intnetbnk';
  static const atm = 'atm';
  static const otcPartnerId = 'prtnr_';
  static const otcPartnerInfoId = 'prtnr_inf_';
  static const repaymentForOwnOtcCelAmount = 'rpy_otc_cel_amt';
  static const repaymentForOwnOtcRelAmount = 'rpy_otc_rel_amt';
  static const back = 'back';
  static const findNearestStore = 'find_near_store';
  static const saveScreenShot = 'save_screen_shot';
  static const outstandingBalance = 'outstanding_balance';
  static const popupWarningNo = 'ppup_wrn_no';
  static const popupWarningYes = 'ppup_wrn_yes';
  static const download = 'download';
  static const share = 'share';
  static const paymentMethodChange = 'pmt_methd_change';
  static const paymentMethodMomo = 'pmt_methd_ewal_momo';
  static const paymentMethodOnePay = 'pmt_methd_atm_onepay';
  static const paymentMethodShopeePay = 'pmt_methd_ewal_shopeepay';
  static const paymentMethodViettelMoney = 'pmt_methd_ewal_viettel_money';
  static const paymentMethodVnPay = 'pmt_methd_vnpay';
  static const paymentMethodZaloPay = 'pmt_methd_ewal_zalopay';
  static const paymentMethodBankTransferMobileBanking = 'pmt_methd_bnk_trnsf_mobbnk';
  static const paymentMethodBankTransferVirtualAccount = 'pmt_methd_bnk_trnsf_va';
  static const proceed = 'proceed';
  static const paymentAmountCelCustom = 'pmt_amt_cel_custom';
  static const paymentAmountCelDue = 'pmt_amt_cel_due';
  static const paymentAmountCelPopupWarning = 'pmt_amt_cel_ppup_warning';
  static const paymentAmountCelPopupWarningContinue = 'pmt_amt_cel_ppup_wrn_ctn';
  static const paymentAmountCelPopupWarningEdit = 'pmt_amt_cel_ppup_wrn_edit';
  static const paymentAmountRelCustom = 'pmt_amt_rel_custom';
  static const paymentAmountRelTotalAmount = 'pmt_amt_rel_due';
  static const paymentAmountRelMinimumAmount = 'pmt_amt_rel_minimum';
  static const paymentAmountRelPopupWarning = 'pmt_amt_rel_ppup_warning';
  static const paymentAmountRelPopupWarningContinue = 'pmt_amt_rel_ppup_wrn_ctn';
  static const paymentAmountRelPopupWarningEdit = 'pmt_amt_rel_ppup_wrn_edit';
  static const popUpUnavailablePaymentMethod = 'ppup_unavail_pmt_method';
  static const popUpUnavailablePaymentMethodOk = 'ppup_unavail_pmt_methd_ok';
  static const myLoanRelSelectContract = 'my_loan_rel_select_cntct';
  static const myLoanChange = 'my_loan_change';
  static const popUpContractUpdated = 'ppup_cntct_updated';
  static const popUpContractUpdatedBack = 'ppup_cntct_updated_back';
  static const popUpContractUpdatedOk = 'ppup_cntct_updated_ok';
  static const myLoanCelSelectContract = 'my_loan_cel_select_cntct';
  static const repaymentForOwnMain = 'rpy_main';
  static const close = 'close';
  static const understand = 'understand';
  static const promise = 'promise';
  static const repayNow = 'repay_now';
  static const question = 'question';
  static const afterNumberDay = 'after_number_day';
  static const dueAmount = 'due_amt';
  static const submit = 'submit';
  static const ptpPopUpConfirmation = 'ppup_confirmation';
  static const ptpPopUpConfirmationAgree = 'ppup_confirmation_agree';
  static const ptpPopUpConfirmationCancel = 'ppup_confirmation_cancel';

  static const viewVoucherListPopup = 'vcher_list_ppup';
  static const seeDetailValidVoucher = 'see_detail_valid_vcher';
  static const seeDetailInvalidVoucher = 'see_detail_inval_vcher';
  static const selectValidVoucher = 'select_valid_vcher';
  static const selectInvalidVoucher = 'select_inval_vcher';
  static const applyVoucher = 'apply_vcher';
  static const noVoucherPopup = 'no_vcher_ppup';
  static const noVoucherGoBack = 'no_vcher_go_back';
  static const techErrorVoucherPopup = 'tech_error_vcher_ppup';
  static const techErrorRefresh = 'tech_error_refresh';
  static const applyVoucherLater = 'apply_latr';
  static const changeVoucher = 'change_vcher';
  static const deleteVoucher = 'delete_vcher';
  static const changeMethod = 'change_method';
  static const unexpectedInvalidVoucherPopup = 'unexp_inval_vcher_ppup';
  static const unexpectedInvalidVoucherContinue = 'unexp_inval_vcher_ctn';
  static const unexpectedInvalidVoucherSeeOther = 'unexp_inval_vcher_seeothr';
  static const expectedInvalidVoucherPopup = 'exp_inval_vcher_ppup';
  static const expectedInvalidVoucherContinue = 'exp_inval_vcher_ctn';
  static const expectedInvalidVoucherSeeOther = 'exp_inval_vcher_seeothr';
  static const copyVaTransId = 'copy_va_transid';
  static const popUpVaBack = 'ppup_va_back';
  static const popUpDownQr = 'ppup_down_qr';
  static const popUpCancelVoucher = 'ppup_cancel_vcher';
  static const viewVoucherList = 'view_vcher_list';
  static const mobileBankingApps = 'rpy_mobbnk_apps';
  static const voucherListPopup = 'vcher_list_ppup';
  static const popupDismissPromo = 'ppup_dismiss_promo';
  static const popupDismissPromoContinue = 'ppup_dismiss_promo_cont';
  static const popupDismissPromoQuit = 'ppup_dismiss_promo_quit';
  static const infoExpand = 'info_expand';
  static const emoneyEcommerce = 'emon_n_ecom';
  static const retail = 'retail';
  static const popupError = 'ppup_error';

  static const promotionFirstAdoptionView = '1strpy_voucher_bs';
  static const promotionFirstAdoptionClaim = '1strpy_voucher_bs_claim';

  static const exploreAda = 'explore_ad';
  static const adaRemindPopupView = 'ad_remind_ppup';
  static const adaRemindPopupExplore = 'explore_ad_ppup';
  static const adaRemindPopupLater = 'later_ad_ppup';
  static const tncDetail = 'tnc_detail';
  static const addNew = 'add_new';
  static const cancelConfirmPopupView = 'cancel_conf_ppup';
  static const cancelConfirmPopupConfirm = 'conf_cancel_ppup';
  static const cancelConfirmPopupBack = 'back_ppup';
  static const later = 'later';
  static const feedback = 'feedback';
  static const remove = 'remove';
  static const method = 'method';
  static const confirmation = 'confirmation';
  static const again = 'again';
}
