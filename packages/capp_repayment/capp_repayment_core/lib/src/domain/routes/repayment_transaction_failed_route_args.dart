import 'package:koyal_core/koyal_core.dart';

class RepaymentTransactionFailedRouteArgs extends ScreenArguments {
  final String? title;
  final String errorMessage;
  final String? rootRouteName;
  final bool? isBackToHomePageOnly;
  final String? tryAgainPopUntilScreenName;
  final bool fromOnlineMethod;
  final bool isShowFeedback;
  final String? errorType;

  RepaymentTransactionFailedRouteArgs({
    this.title,
    required this.errorMessage,
    this.rootRouteName,
    this.isBackToHomePageOnly,
    this.tryAgainPopUntilScreenName,
    this.fromOnlineMethod = true,
    this.isShowFeedback = false,
    this.errorType,
  });
}
