import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

class RepaymentFailedRouteArgs extends ScreenArguments {
  final String screenTitle;
  final String? errorTitle;
  final String? errorMessage;
  final String? rootRouteName;
  final bool? isBackToHomePageOnly;
  final String? tryAgainPopUntilScreenName;
  final String? errorType;
  final VoidCallback? onRetry;
  final VoidCallback? onBackToHome;
  final VoidCallback? onTrackRetry;
  final VoidCallback? onTrackBackToHome;

  RepaymentFailedRouteArgs({
    required this.screenTitle,
    this.errorTitle,
    this.errorMessage,
    this.rootRouteName,
    this.isBackToHomePageOnly,
    this.tryAgainPopUntilScreenName,
    this.errorType,
    this.onRetry,
    this.onBackToHome,
    this.onTrackRetry,
    this.onTrackBackToHome,
  });
}
