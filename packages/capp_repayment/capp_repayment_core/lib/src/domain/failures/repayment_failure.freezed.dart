// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentFailure {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() badRequest,
    required TResult Function() notFound,
    required TResult Function() internalServerError,
    required TResult Function() unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? badRequest,
    TResult? Function()? notFound,
    TResult? Function()? internalServerError,
    TResult? Function()? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? badRequest,
    TResult Function()? notFound,
    TResult Function()? internalServerError,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadRequest value) badRequest,
    required TResult Function(_NotFound value) notFound,
    required TResult Function(_InternalServerError value) internalServerError,
    required TResult Function(_$Unexpected value) unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadRequest value)? badRequest,
    TResult? Function(_NotFound value)? notFound,
    TResult? Function(_InternalServerError value)? internalServerError,
    TResult? Function(_$Unexpected value)? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadRequest value)? badRequest,
    TResult Function(_NotFound value)? notFound,
    TResult Function(_InternalServerError value)? internalServerError,
    TResult Function(_$Unexpected value)? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentFailureCopyWith<$Res> {
  factory $RepaymentFailureCopyWith(
          RepaymentFailure value, $Res Function(RepaymentFailure) then) =
      _$RepaymentFailureCopyWithImpl<$Res, RepaymentFailure>;
}

/// @nodoc
class _$RepaymentFailureCopyWithImpl<$Res, $Val extends RepaymentFailure>
    implements $RepaymentFailureCopyWith<$Res> {
  _$RepaymentFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_BadRequestCopyWith<$Res> {
  factory _$$_BadRequestCopyWith(
          _$_BadRequest value, $Res Function(_$_BadRequest) then) =
      __$$_BadRequestCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_BadRequestCopyWithImpl<$Res>
    extends _$RepaymentFailureCopyWithImpl<$Res, _$_BadRequest>
    implements _$$_BadRequestCopyWith<$Res> {
  __$$_BadRequestCopyWithImpl(
      _$_BadRequest _value, $Res Function(_$_BadRequest) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_BadRequest implements _BadRequest {
  const _$_BadRequest();

  @override
  String toString() {
    return 'RepaymentFailure.badRequest()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_BadRequest);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() badRequest,
    required TResult Function() notFound,
    required TResult Function() internalServerError,
    required TResult Function() unexpected,
  }) {
    return badRequest();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? badRequest,
    TResult? Function()? notFound,
    TResult? Function()? internalServerError,
    TResult? Function()? unexpected,
  }) {
    return badRequest?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? badRequest,
    TResult Function()? notFound,
    TResult Function()? internalServerError,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (badRequest != null) {
      return badRequest();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadRequest value) badRequest,
    required TResult Function(_NotFound value) notFound,
    required TResult Function(_InternalServerError value) internalServerError,
    required TResult Function(_$Unexpected value) unexpected,
  }) {
    return badRequest(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadRequest value)? badRequest,
    TResult? Function(_NotFound value)? notFound,
    TResult? Function(_InternalServerError value)? internalServerError,
    TResult? Function(_$Unexpected value)? unexpected,
  }) {
    return badRequest?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadRequest value)? badRequest,
    TResult Function(_NotFound value)? notFound,
    TResult Function(_InternalServerError value)? internalServerError,
    TResult Function(_$Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (badRequest != null) {
      return badRequest(this);
    }
    return orElse();
  }
}

abstract class _BadRequest implements RepaymentFailure {
  const factory _BadRequest() = _$_BadRequest;
}

/// @nodoc
abstract class _$$_NotFoundCopyWith<$Res> {
  factory _$$_NotFoundCopyWith(
          _$_NotFound value, $Res Function(_$_NotFound) then) =
      __$$_NotFoundCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_NotFoundCopyWithImpl<$Res>
    extends _$RepaymentFailureCopyWithImpl<$Res, _$_NotFound>
    implements _$$_NotFoundCopyWith<$Res> {
  __$$_NotFoundCopyWithImpl(
      _$_NotFound _value, $Res Function(_$_NotFound) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_NotFound implements _NotFound {
  const _$_NotFound();

  @override
  String toString() {
    return 'RepaymentFailure.notFound()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_NotFound);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() badRequest,
    required TResult Function() notFound,
    required TResult Function() internalServerError,
    required TResult Function() unexpected,
  }) {
    return notFound();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? badRequest,
    TResult? Function()? notFound,
    TResult? Function()? internalServerError,
    TResult? Function()? unexpected,
  }) {
    return notFound?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? badRequest,
    TResult Function()? notFound,
    TResult Function()? internalServerError,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadRequest value) badRequest,
    required TResult Function(_NotFound value) notFound,
    required TResult Function(_InternalServerError value) internalServerError,
    required TResult Function(_$Unexpected value) unexpected,
  }) {
    return notFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadRequest value)? badRequest,
    TResult? Function(_NotFound value)? notFound,
    TResult? Function(_InternalServerError value)? internalServerError,
    TResult? Function(_$Unexpected value)? unexpected,
  }) {
    return notFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadRequest value)? badRequest,
    TResult Function(_NotFound value)? notFound,
    TResult Function(_InternalServerError value)? internalServerError,
    TResult Function(_$Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(this);
    }
    return orElse();
  }
}

abstract class _NotFound implements RepaymentFailure {
  const factory _NotFound() = _$_NotFound;
}

/// @nodoc
abstract class _$$_InternalServerErrorCopyWith<$Res> {
  factory _$$_InternalServerErrorCopyWith(_$_InternalServerError value,
          $Res Function(_$_InternalServerError) then) =
      __$$_InternalServerErrorCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InternalServerErrorCopyWithImpl<$Res>
    extends _$RepaymentFailureCopyWithImpl<$Res, _$_InternalServerError>
    implements _$$_InternalServerErrorCopyWith<$Res> {
  __$$_InternalServerErrorCopyWithImpl(_$_InternalServerError _value,
      $Res Function(_$_InternalServerError) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_InternalServerError implements _InternalServerError {
  const _$_InternalServerError();

  @override
  String toString() {
    return 'RepaymentFailure.internalServerError()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_InternalServerError);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() badRequest,
    required TResult Function() notFound,
    required TResult Function() internalServerError,
    required TResult Function() unexpected,
  }) {
    return internalServerError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? badRequest,
    TResult? Function()? notFound,
    TResult? Function()? internalServerError,
    TResult? Function()? unexpected,
  }) {
    return internalServerError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? badRequest,
    TResult Function()? notFound,
    TResult Function()? internalServerError,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (internalServerError != null) {
      return internalServerError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadRequest value) badRequest,
    required TResult Function(_NotFound value) notFound,
    required TResult Function(_InternalServerError value) internalServerError,
    required TResult Function(_$Unexpected value) unexpected,
  }) {
    return internalServerError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadRequest value)? badRequest,
    TResult? Function(_NotFound value)? notFound,
    TResult? Function(_InternalServerError value)? internalServerError,
    TResult? Function(_$Unexpected value)? unexpected,
  }) {
    return internalServerError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadRequest value)? badRequest,
    TResult Function(_NotFound value)? notFound,
    TResult Function(_InternalServerError value)? internalServerError,
    TResult Function(_$Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (internalServerError != null) {
      return internalServerError(this);
    }
    return orElse();
  }
}

abstract class _InternalServerError implements RepaymentFailure {
  const factory _InternalServerError() = _$_InternalServerError;
}

/// @nodoc
abstract class _$$_$UnexpectedCopyWith<$Res> {
  factory _$$_$UnexpectedCopyWith(
          _$_$Unexpected value, $Res Function(_$_$Unexpected) then) =
      __$$_$UnexpectedCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_$UnexpectedCopyWithImpl<$Res>
    extends _$RepaymentFailureCopyWithImpl<$Res, _$_$Unexpected>
    implements _$$_$UnexpectedCopyWith<$Res> {
  __$$_$UnexpectedCopyWithImpl(
      _$_$Unexpected _value, $Res Function(_$_$Unexpected) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_$Unexpected implements _$Unexpected {
  const _$_$Unexpected();

  @override
  String toString() {
    return 'RepaymentFailure.unexpected()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_$Unexpected);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() badRequest,
    required TResult Function() notFound,
    required TResult Function() internalServerError,
    required TResult Function() unexpected,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? badRequest,
    TResult? Function()? notFound,
    TResult? Function()? internalServerError,
    TResult? Function()? unexpected,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? badRequest,
    TResult Function()? notFound,
    TResult Function()? internalServerError,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_BadRequest value) badRequest,
    required TResult Function(_NotFound value) notFound,
    required TResult Function(_InternalServerError value) internalServerError,
    required TResult Function(_$Unexpected value) unexpected,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_BadRequest value)? badRequest,
    TResult? Function(_NotFound value)? notFound,
    TResult? Function(_InternalServerError value)? internalServerError,
    TResult? Function(_$Unexpected value)? unexpected,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_BadRequest value)? badRequest,
    TResult Function(_NotFound value)? notFound,
    TResult Function(_InternalServerError value)? internalServerError,
    TResult Function(_$Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class _$Unexpected implements RepaymentFailure {
  const factory _$Unexpected() = _$_$Unexpected;
}
