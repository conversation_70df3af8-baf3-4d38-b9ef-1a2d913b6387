import 'package:freezed_annotation/freezed_annotation.dart';

part 'repayment_failure.freezed.dart';

@freezed
class RepaymentFailure with _$RepaymentFailure {
  const factory RepaymentFailure.badRequest() = _BadRequest; // 400
  const factory RepaymentFailure.notFound() = _NotFound; // 404
  const factory RepaymentFailure.internalServerError() = _InternalServerError; // 500
  const factory RepaymentFailure.unexpected() = _$Unexpected;
}
