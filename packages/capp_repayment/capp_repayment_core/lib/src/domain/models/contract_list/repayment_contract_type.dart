enum RepaymentContractType { cel, rel }

extension RepaymentContractTypeX on RepaymentContractType {
  String get id {
    switch (this) {
      case RepaymentContractType.cel:
        return 'CEL';
      case RepaymentContractType.rel:
        return 'REL';
    }
  }

  String toJson() {
    return toString().split('.').last;
  }

  static RepaymentContractType fromJson(String json) {
    return RepaymentContractType.values.firstWhere(
      (e) => e.toString().split('.').last == json,
    );
  }
}

// ignore: avoid_classes_with_only_static_members
class RepaymentContractTypeHelper {
  static RepaymentContractType? parse(String? contractTypeString) {
    if (contractTypeString == RepaymentContractType.cel.id) {
      return RepaymentContractType.cel;
    } else if (contractTypeString == RepaymentContractType.rel.id) {
      return RepaymentContractType.rel;
    }

    return null;
  }
}
