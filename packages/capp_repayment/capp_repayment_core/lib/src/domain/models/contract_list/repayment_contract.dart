import 'package:equatable/equatable.dart';

import 'repayment_contract_type.dart';
import 'repayment_loan_contract.dart';
import 'repayment_rel_contract.dart';

class RepaymentContract extends Equatable {
  final RepaymentContractType? contractType;
  final RepaymentLoanContract? loanContract;
  final RepaymentRelContract? relContract;

  const RepaymentContract({
    this.contractType,
    this.loanContract,
    this.relContract,
  });

  // Copy with method
  RepaymentContract copyWith({
    RepaymentContractType? contractType,
    RepaymentLoanContract? loanContract,
    RepaymentRelContract? relContract,
  }) {
    return RepaymentContract(
      contractType: contractType ?? this.contractType,
      loanContract: loanContract ?? this.loanContract,
      relContract: relContract ?? this.relContract,
    );
  }

  // FromJson method
  factory RepaymentContract.fromJson(Map<String, dynamic> json) {
    return RepaymentContract(
      contractType: json['contractType'] != null ? RepaymentContractTypeX.fromJson(json['contractType']) : null,
      loanContract: json['loanContract'] != null ? RepaymentLoanContract.fromJson(json['loanContract']) : null,
      relContract: json['relContract'] != null ? RepaymentRelContract.fromJson(json['relContract']) : null,
    );
  }

  // ToJson method
  Map<String, dynamic> toJson() {
    return {
      'contractType': contractType?.toJson(),
      'loanContract': loanContract?.toJson(),
      'relContract': relContract?.toJson(),
    };
  }

  @override
  List<Object?> get props => [contractType, loanContract, relContract];
}

extension RepaymentContractX on RepaymentContract {
  String? get contractNumber {
    return contractType == RepaymentContractType.cel ? loanContract?.contractNumber : relContract?.accountNumber;
  }

  DateTime? get dueDate {
    return contractType == RepaymentContractType.cel ? loanContract?.dueDate : relContract?.dueDate;
  }

  int? get dpd {
    return contractType == RepaymentContractType.cel ? loanContract?.dpd : relContract?.dpd;
  }

  String? get contractStatus {
    return contractType == RepaymentContractType.cel ? loanContract?.contractStatus : relContract?.contractStatus;
  }

  bool get isContractValid {
    return loanContract != null || relContract != null;
  }
}
