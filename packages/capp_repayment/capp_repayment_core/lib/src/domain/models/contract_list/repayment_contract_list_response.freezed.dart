// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_contract_list_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentContractListResponse {
  List<RepaymentLoanContract>? get loanContracts =>
      throw _privateConstructorUsedError;
  List<RepaymentRelContract>? get relContracts =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentContractListResponseCopyWith<RepaymentContractListResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentContractListResponseCopyWith<$Res> {
  factory $RepaymentContractListResponseCopyWith(
          RepaymentContractListResponse value,
          $Res Function(RepaymentContractListResponse) then) =
      _$RepaymentContractListResponseCopyWithImpl<$Res,
          RepaymentContractListResponse>;
  @useResult
  $Res call(
      {List<RepaymentLoanContract>? loanContracts,
      List<RepaymentRelContract>? relContracts});
}

/// @nodoc
class _$RepaymentContractListResponseCopyWithImpl<$Res,
        $Val extends RepaymentContractListResponse>
    implements $RepaymentContractListResponseCopyWith<$Res> {
  _$RepaymentContractListResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loanContracts = freezed,
    Object? relContracts = freezed,
  }) {
    return _then(_value.copyWith(
      loanContracts: freezed == loanContracts
          ? _value.loanContracts
          : loanContracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentLoanContract>?,
      relContracts: freezed == relContracts
          ? _value.relContracts
          : relContracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentRelContract>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentContractListResponseCopyWith<$Res>
    implements $RepaymentContractListResponseCopyWith<$Res> {
  factory _$$_RepaymentContractListResponseCopyWith(
          _$_RepaymentContractListResponse value,
          $Res Function(_$_RepaymentContractListResponse) then) =
      __$$_RepaymentContractListResponseCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<RepaymentLoanContract>? loanContracts,
      List<RepaymentRelContract>? relContracts});
}

/// @nodoc
class __$$_RepaymentContractListResponseCopyWithImpl<$Res>
    extends _$RepaymentContractListResponseCopyWithImpl<$Res,
        _$_RepaymentContractListResponse>
    implements _$$_RepaymentContractListResponseCopyWith<$Res> {
  __$$_RepaymentContractListResponseCopyWithImpl(
      _$_RepaymentContractListResponse _value,
      $Res Function(_$_RepaymentContractListResponse) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loanContracts = freezed,
    Object? relContracts = freezed,
  }) {
    return _then(_$_RepaymentContractListResponse(
      loanContracts: freezed == loanContracts
          ? _value._loanContracts
          : loanContracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentLoanContract>?,
      relContracts: freezed == relContracts
          ? _value._relContracts
          : relContracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentRelContract>?,
    ));
  }
}

/// @nodoc

class _$_RepaymentContractListResponse
    implements _RepaymentContractListResponse {
  const _$_RepaymentContractListResponse(
      {final List<RepaymentLoanContract>? loanContracts,
      final List<RepaymentRelContract>? relContracts})
      : _loanContracts = loanContracts,
        _relContracts = relContracts;

  final List<RepaymentLoanContract>? _loanContracts;
  @override
  List<RepaymentLoanContract>? get loanContracts {
    final value = _loanContracts;
    if (value == null) return null;
    if (_loanContracts is EqualUnmodifiableListView) return _loanContracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<RepaymentRelContract>? _relContracts;
  @override
  List<RepaymentRelContract>? get relContracts {
    final value = _relContracts;
    if (value == null) return null;
    if (_relContracts is EqualUnmodifiableListView) return _relContracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RepaymentContractListResponse(loanContracts: $loanContracts, relContracts: $relContracts)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentContractListResponse &&
            const DeepCollectionEquality()
                .equals(other._loanContracts, _loanContracts) &&
            const DeepCollectionEquality()
                .equals(other._relContracts, _relContracts));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_loanContracts),
      const DeepCollectionEquality().hash(_relContracts));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentContractListResponseCopyWith<_$_RepaymentContractListResponse>
      get copyWith => __$$_RepaymentContractListResponseCopyWithImpl<
          _$_RepaymentContractListResponse>(this, _$identity);
}

abstract class _RepaymentContractListResponse
    implements RepaymentContractListResponse {
  const factory _RepaymentContractListResponse(
          {final List<RepaymentLoanContract>? loanContracts,
          final List<RepaymentRelContract>? relContracts}) =
      _$_RepaymentContractListResponse;

  @override
  List<RepaymentLoanContract>? get loanContracts;
  @override
  List<RepaymentRelContract>? get relContracts;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentContractListResponseCopyWith<_$_RepaymentContractListResponse>
      get copyWith => throw _privateConstructorUsedError;
}
