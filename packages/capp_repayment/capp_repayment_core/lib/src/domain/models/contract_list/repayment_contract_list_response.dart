import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../capp_repayment_core.dart';

part 'repayment_contract_list_response.freezed.dart';

@freezed
class RepaymentContractListResponse with _$RepaymentContractListResponse {
  const factory RepaymentContractListResponse({
    List<RepaymentLoanContract>? loanContracts,
    List<RepaymentRelContract>? relContracts,
  }) = _RepaymentContractListResponse;
}

extension RepaymentContractListResponseX on RepaymentContractListResponse {
  List<RepaymentContract> parseToRepaymentContract() {
    final contracts = <RepaymentContract>[];
    if (loanContracts != null && loanContracts!.isNotEmpty) {
      contracts.addAll(
        loanContracts!.map((e) {
          return RepaymentContract(
            contractType: RepaymentContractTypeHelper.parse(e.contractType),
            loanContract: e,
          );
        }).toList(),
      );
    }
    if (relContracts != null && relContracts!.isNotEmpty) {
      contracts.addAll(
        relContracts!.map((e) {
          return RepaymentContract(
            contractType: RepaymentContractTypeHelper.parse(e.contractType),
            relContract: e,
          );
        }).toList(),
      );
    }
    contracts.sort((a, b) {
      if (a.dueDate != null && b.dueDate != null) {
        return a.dueDate!.compareTo(b.dueDate!);
      }

      return 0;
    });

    return contracts;
  }
}
