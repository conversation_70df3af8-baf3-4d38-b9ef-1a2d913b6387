import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

abstract class IBannerRepository {
  Future<Either<ContentFailure, List<ContentBanner>>> getRepaymentBanners({
    required api_models.BannerCtaType type,
  });
  Future<Either<ContentFailure, ContentBanner>> getRepaymentBanner({required String bannerId});
}
