// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_banner_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentBannerEvent {
  BannerMode get mode => throw _privateConstructorUsedError;
  String? get bannerId => throw _privateConstructorUsedError;
  api_models.BannerCtaType? get type => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            BannerMode mode, String? bannerId, api_models.BannerCtaType? type)
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            BannerMode mode, String? bannerId, api_models.BannerCtaType? type)?
        initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            BannerMode mode, String? bannerId, api_models.BannerCtaType? type)?
        initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentBannerEventCopyWith<RepaymentBannerEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBannerEventCopyWith<$Res> {
  factory $RepaymentBannerEventCopyWith(RepaymentBannerEvent value,
          $Res Function(RepaymentBannerEvent) then) =
      _$RepaymentBannerEventCopyWithImpl<$Res, RepaymentBannerEvent>;
  @useResult
  $Res call(
      {BannerMode mode, String? bannerId, api_models.BannerCtaType? type});
}

/// @nodoc
class _$RepaymentBannerEventCopyWithImpl<$Res,
        $Val extends RepaymentBannerEvent>
    implements $RepaymentBannerEventCopyWith<$Res> {
  _$RepaymentBannerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
    Object? bannerId = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as BannerMode,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as api_models.BannerCtaType?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res>
    implements $RepaymentBannerEventCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BannerMode mode, String? bannerId, api_models.BannerCtaType? type});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentBannerEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
    Object? bannerId = freezed,
    Object? type = freezed,
  }) {
    return _then(_$_Initialize(
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as BannerMode,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as api_models.BannerCtaType?,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({required this.mode, this.bannerId, this.type});

  @override
  final BannerMode mode;
  @override
  final String? bannerId;
  @override
  final api_models.BannerCtaType? type;

  @override
  String toString() {
    return 'RepaymentBannerEvent.initialize(mode: $mode, bannerId: $bannerId, type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.mode, mode) || other.mode == mode) &&
            (identical(other.bannerId, bannerId) ||
                other.bannerId == bannerId) &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mode, bannerId, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            BannerMode mode, String? bannerId, api_models.BannerCtaType? type)
        initialize,
  }) {
    return initialize(mode, bannerId, type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            BannerMode mode, String? bannerId, api_models.BannerCtaType? type)?
        initialize,
  }) {
    return initialize?.call(mode, bannerId, type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            BannerMode mode, String? bannerId, api_models.BannerCtaType? type)?
        initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(mode, bannerId, type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentBannerEvent {
  const factory _Initialize(
      {required final BannerMode mode,
      final String? bannerId,
      final api_models.BannerCtaType? type}) = _$_Initialize;

  @override
  BannerMode get mode;
  @override
  String? get bannerId;
  @override
  api_models.BannerCtaType? get type;
  @override
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentBannerState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  bool get isError => throw _privateConstructorUsedError;
  BannerMode get mode => throw _privateConstructorUsedError;
  List<ContentBanner> get banners => throw _privateConstructorUsedError;
  String? get bannerId => throw _privateConstructorUsedError;
  api_models.BannerCtaType? get type => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentBannerStateCopyWith<RepaymentBannerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentBannerStateCopyWith<$Res> {
  factory $RepaymentBannerStateCopyWith(RepaymentBannerState value,
          $Res Function(RepaymentBannerState) then) =
      _$RepaymentBannerStateCopyWithImpl<$Res, RepaymentBannerState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool isError,
      BannerMode mode,
      List<ContentBanner> banners,
      String? bannerId,
      api_models.BannerCtaType? type});
}

/// @nodoc
class _$RepaymentBannerStateCopyWithImpl<$Res,
        $Val extends RepaymentBannerState>
    implements $RepaymentBannerStateCopyWith<$Res> {
  _$RepaymentBannerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = null,
    Object? mode = null,
    Object? banners = null,
    Object? bannerId = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as BannerMode,
      banners: null == banners
          ? _value.banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<ContentBanner>,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as api_models.BannerCtaType?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentBannerStateCopyWith<$Res>
    implements $RepaymentBannerStateCopyWith<$Res> {
  factory _$$_RepaymentBannerStateCopyWith(_$_RepaymentBannerState value,
          $Res Function(_$_RepaymentBannerState) then) =
      __$$_RepaymentBannerStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      bool isError,
      BannerMode mode,
      List<ContentBanner> banners,
      String? bannerId,
      api_models.BannerCtaType? type});
}

/// @nodoc
class __$$_RepaymentBannerStateCopyWithImpl<$Res>
    extends _$RepaymentBannerStateCopyWithImpl<$Res, _$_RepaymentBannerState>
    implements _$$_RepaymentBannerStateCopyWith<$Res> {
  __$$_RepaymentBannerStateCopyWithImpl(_$_RepaymentBannerState _value,
      $Res Function(_$_RepaymentBannerState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? isError = null,
    Object? mode = null,
    Object? banners = null,
    Object? bannerId = freezed,
    Object? type = freezed,
  }) {
    return _then(_$_RepaymentBannerState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isError: null == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool,
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as BannerMode,
      banners: null == banners
          ? _value._banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<ContentBanner>,
      bannerId: freezed == bannerId
          ? _value.bannerId
          : bannerId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as api_models.BannerCtaType?,
    ));
  }
}

/// @nodoc

class _$_RepaymentBannerState implements _RepaymentBannerState {
  const _$_RepaymentBannerState(
      {required this.loadingState,
      required this.isError,
      this.mode = BannerMode.single,
      final List<ContentBanner> banners = const <ContentBanner>[],
      this.bannerId,
      this.type})
      : _banners = banners;

  @override
  final LoadingState loadingState;
  @override
  final bool isError;
  @override
  @JsonKey()
  final BannerMode mode;
  final List<ContentBanner> _banners;
  @override
  @JsonKey()
  List<ContentBanner> get banners {
    if (_banners is EqualUnmodifiableListView) return _banners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_banners);
  }

  @override
  final String? bannerId;
  @override
  final api_models.BannerCtaType? type;

  @override
  String toString() {
    return 'RepaymentBannerState(loadingState: $loadingState, isError: $isError, mode: $mode, banners: $banners, bannerId: $bannerId, type: $type)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentBannerState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.mode, mode) || other.mode == mode) &&
            const DeepCollectionEquality().equals(other._banners, _banners) &&
            (identical(other.bannerId, bannerId) ||
                other.bannerId == bannerId) &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loadingState, isError, mode,
      const DeepCollectionEquality().hash(_banners), bannerId, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentBannerStateCopyWith<_$_RepaymentBannerState> get copyWith =>
      __$$_RepaymentBannerStateCopyWithImpl<_$_RepaymentBannerState>(
          this, _$identity);
}

abstract class _RepaymentBannerState implements RepaymentBannerState {
  const factory _RepaymentBannerState(
      {required final LoadingState loadingState,
      required final bool isError,
      final BannerMode mode,
      final List<ContentBanner> banners,
      final String? bannerId,
      final api_models.BannerCtaType? type}) = _$_RepaymentBannerState;

  @override
  LoadingState get loadingState;
  @override
  bool get isError;
  @override
  BannerMode get mode;
  @override
  List<ContentBanner> get banners;
  @override
  String? get bannerId;
  @override
  api_models.BannerCtaType? get type;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentBannerStateCopyWith<_$_RepaymentBannerState> get copyWith =>
      throw _privateConstructorUsedError;
}
