import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment_core.dart';

part 'repayment_banner_bloc.freezed.dart';
part 'repayment_banner_event.dart';
part 'repayment_banner_state.dart';

class RepaymentBannerBloc extends Bloc<RepaymentBannerEvent, RepaymentBannerState> {
  final Logger logger;
  final IBannerRepository bannerRepository;
  final ImageServiceBase imageService;
  RepaymentBannerBloc({
    required this.logger,
    required this.bannerRepository,
    required this.imageService,
  }) : super(RepaymentBannerState.initialize()) {
    on<RepaymentBannerEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(_Initialize e, Emitter<RepaymentBannerState> emit) async {
    final mode = e.mode;
    final bannerId = e.bannerId ?? '';
    final type = e.type;

    var repaymentBanners = <ContentBanner>[];

    // Single banner
    if (mode == BannerMode.single) {
      final repaymentBannerResult = await bannerRepository.getRepaymentBanner(bannerId: bannerId);

      final repaymentBanner = repaymentBannerResult.fold((l) => null, (r) => r);
      if (repaymentBanner != null) {
        repaymentBanners.add(repaymentBanner);
      }
    }
    // Multiple banner
    else if (type != null) {
      final repaymentBannersResult = await bannerRepository.getRepaymentBanners(type: type);

      repaymentBanners = repaymentBannersResult.fold((l) => null, (r) => r) ?? [];
    }

    // Update banner image url for banner
    final newBanners = <ContentBanner>[];
    if (repaymentBanners.isNotEmpty) {
      for (final banner in repaymentBanners) {
        final bannerImage = banner.image;
        if (bannerImage != null) {
          final bannerImageId = bannerImage.id ?? '';
          final bannerImageUrl = imageService.getUrlFromId(bannerImageId) ?? '';
          if (bannerImageUrl.isNotEmpty) {
            newBanners.add(banner.copyWith(image: banner.image?.copyWith(url: bannerImageUrl)));
          }
        }
      }

      emit(
        state.copyWith(
          banners: newBanners,
          mode: mode,
          bannerId: bannerId,
          type: type,
        ),
      );
    }
  }
}
