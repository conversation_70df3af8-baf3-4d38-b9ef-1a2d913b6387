part of 'repayment_banner_bloc.dart';

@freezed
class RepaymentBannerState with _$RepaymentBannerState {
  const factory RepaymentBannerState({
    required LoadingState loadingState,
    required bool isError,
    @Default(BannerMode.single) BannerMode mode,
    @Default(<ContentBanner>[]) List<ContentBanner> banners,
    String? bannerId,
    api_models.BannerCtaType? type,
  }) = _RepaymentBannerState;

  factory RepaymentBannerState.initialize() => const RepaymentBannerState(
        loadingState: LoadingState.isInitial,
        isError: false,
      );
}
