import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment_core.dart';

enum CancelPaymentOption {
  cancelPayment,
  continuePayment,
}

Future<CancelPaymentOption?> showCancelPaymentDialog(
  BuildContext context, {
  Function()? trackingPaymentNoClick,
  Function()? trackingPaymentYesClick,
}) async {
  return showKoyalOverlay<CancelPaymentOption?>(
    context,
    dismissible: false,
    body: KoyalText.subtitle2(
      L10nCappRepayment.of(context).cancelPaymentMessage,
      color: ColorTheme.of(context).defaultTextColor,
      textAlign: TextAlign.center,
    ),
    primaryButtonBuilder: (context) => PrimaryButton(
      key: const Key('__cancelPaymentNo__'),
      text: L10nCappRepayment.of(context).cancelPaymentNo,
      onPressed: () {
        trackingPaymentNoClick?.call();
        context.navigator.pop(CancelPaymentOption.continuePayment);
      },
    ),
    tertiaryButtonBuilder: (context) => TertiaryButton(
      key: const Key('__cancelPaymentYes__'),
      text: L10nCappRepayment.of(context).cancelPaymentYes,
      onPressed: () {
        trackingPaymentYesClick?.call();
        context.navigator.pop(CancelPaymentOption.cancelPayment);
      },
    ),
  );
}
