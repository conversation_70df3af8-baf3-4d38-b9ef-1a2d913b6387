import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart' hide ChipTheme;

import '../../../capp_repayment_core.dart';

class RepaymentSelectableNewContainer extends StatelessWidget {
  final bool isSelected;
  final Function()? onTap;
  final String? title;
  final String? subtitle;
  final Widget? avatar;
  final Widget? expandedContent;
  final Widget? body;
  final bool isDisabled;
  final bool contentCenter;
  final RepaymentPaymentStatus? paymentStatus;
  final double? customHeight;

  const RepaymentSelectableNewContainer({
    Key? key,
    this.isSelected = false,
    this.onTap,
    this.title,
    this.subtitle,
    this.avatar,
    this.body,
    this.expandedContent,
    this.isDisabled = false,
    this.paymentStatus,
    this.contentCenter = false,
    this.customHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: customHeight,
      child: Opacity(
        opacity: isDisabled ? 0.4 : 1,
        child: GestureDetector(
          onTap: isDisabled ? null : onTap,
          child: LayoutBuilder(
            builder: (context, constraints) => Stack(
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: _padding(),
                  decoration: isSelected && !isDisabled
                      ? BoxDecoration(
                          color: ColorTheme.of(context).backgroundColor,
                          border: Border.all(
                            color: ColorTheme.of(context).infoIndicatorColor,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        )
                      : BoxDecoration(
                          color: ColorTheme.of(context).backgroundColor,
                          border: Border.all(
                            color: ColorTheme.of(context).foreground5Color,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                  child: Row(
                    crossAxisAlignment: contentCenter ? CrossAxisAlignment.center : CrossAxisAlignment.start,
                    children: [
                      if (null != avatar)
                        KoyalPadding.normalAll(
                          top: false,
                          left: false,
                          bottom: false,
                          child: avatar ?? Container(),
                        ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: contentCenter ? MainAxisAlignment.center : MainAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: KoyalPadding.xSmall(
                                    left: false,
                                    right: false,
                                    bottom: false,
                                    child: KoyalText.subtitle2(
                                      title ?? '',
                                      color: ColorTheme.of(context).defaultTextColor,
                                      key: const Key('__selectableContainerTitle__'),
                                    ),
                                  ),
                                ),
                                if (paymentStatus == RepaymentPaymentStatus.overdue)
                                  RepaymentSimpleChip(
                                    key: const Key('__selectableContainerOverdueChip__'),
                                    label: L10nCappRepayment.of(context).repaymentOverdue,
                                    chipTheme: RepaymentChipTheme.error(),
                                  ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (subtitle != null) ...[
                                  KoyalText.body2(
                                    subtitle ?? '',
                                    color: ColorTheme.of(context).secondaryTextColor,
                                    key: const Key('__selectableContainerSubtitle__'),
                                  ),
                                  if (body != null || expandedContent != null) const SizedBox(height: 8),
                                ],
                                if (body != null) ...[
                                  body!,
                                  if (expandedContent != null) const SizedBox(height: 8),
                                ],
                                if (expandedContent != null && isSelected) expandedContent!,
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  EdgeInsets _padding() {
    if (isSelected) {
      return const EdgeInsets.fromLTRB(12, 12, 12, 12);
    } else {
      return const EdgeInsets.fromLTRB(14, 14, 14, 14);
    }
  }
}
