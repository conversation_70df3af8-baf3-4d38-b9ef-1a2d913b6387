import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentProgressContainer extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? loadingText;
  final bool? isEnableBack;

  const RepaymentProgressContainer({
    Key? key,
    required this.child,
    required this.isLoading,
    this.loadingText,
    this.isEnableBack,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KoyalWillPopScope(
      onWillPop: () async {
        if (isEnableBack ?? false) {
          // For progress dialog that not occupy full screen
          return true;
        } else {
          // For full screen progress dialog
          return !isLoading;
        }
      },
      child: Stack(
        children: [
          child,
          if (isLoading)
            Container(
              color: ColorTheme.of(context).windowBackgroundColor.withAlpha(0xCC),
              width: double.infinity,
              height: double.infinity,
              child: Material(
                type: MaterialType.transparency,
                child: loadingText?.isNotEmpty ?? false
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const KoyalProgressIndicator.large(),
                          const SizedBox(height: 16),
                          KoyalText.body1(
                            loadingText!,
                            color: ColorTheme.of(context).defaultTextColor,
                          ),
                        ],
                      )
                    : const Center(child: KoyalProgressIndicator.large()),
              ),
            ),
        ],
      ),
    );
  }
}
