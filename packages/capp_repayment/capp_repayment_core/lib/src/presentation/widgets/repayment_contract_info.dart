import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../l10n/i18n.dart';

class RepaymentContractInfo extends StatelessWidget {
  final String totalAmount;
  final String dueDate;
  final String accountNo;
  final String? minimumAmount;

  const RepaymentContractInfo({
    Key? key,
    required this.totalAmount,
    required this.dueDate,
    required this.accountNo,
    this.minimumAmount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        KoyalPadding.normalAll(
          key: const Key('__contractInfoTitle__'),
          child: KoyalText.subtitle1(
            L10nCappRepayment.of(context).repaymentContractInformation,
            color: ColorTheme.of(context).defaultTextColor,
          ),
        ),
        if (minimumAmount != null)
          KoyalPadding.normalAll(
            key: const Key('__minimumAmountInfo__'),
            child: Row(
              children: [
                KoyalText.body2(
                  L10nCappRepayment.of(context).repaymentMinimumAmount,
                  color: ColorTheme.of(context).secondaryTextColor,
                ),
                Expanded(
                  child: KoyalText.body2(
                    minimumAmount!,
                    color: ColorTheme.of(context).defaultTextColor,
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          )
        else
          const SizedBox.shrink(),
        KoyalPadding.normalAll(
          key: const Key('__totalAmountInfo__'),
          child: Row(
            children: [
              KoyalText.body2(
                L10nCappRepayment.of(context).repaymentTotalAmount,
                color: ColorTheme.of(context).secondaryTextColor,
              ),
              Expanded(
                child: KoyalText.body2(
                  totalAmount,
                  color: ColorTheme.of(context).defaultTextColor,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
        KoyalPadding.normalAll(
          key: const Key('__dueDateInfo__'),
          child: Row(
            children: [
              KoyalText.body2(
                L10nCappRepayment.of(context).repaymentDueDate,
                color: ColorTheme.of(context).secondaryTextColor,
              ),
              Expanded(
                child: KoyalText.body2(
                  dueDate,
                  color: ColorTheme.of(context).defaultTextColor,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
        KoyalPadding.normalAll(
          key: const Key('__contractNumberInfo__'),
          child: Row(
            children: [
              KoyalText.body2(
                L10nCappRepayment.of(context).repaymentContractNo,
                color: ColorTheme.of(context).secondaryTextColor,
              ),
              Expanded(
                child: KoyalText.body2(
                  accountNo,
                  color: ColorTheme.of(context).defaultTextColor,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
