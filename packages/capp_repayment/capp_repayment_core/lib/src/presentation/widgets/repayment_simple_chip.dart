import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentChipTheme {
  Color foregroundColor;
  Color backgroundColor;
  RepaymentChipTheme._(this.foregroundColor, this.backgroundColor);

  RepaymentChipTheme.info() : this._(HciColors.secondary600, HciColors.secondary50);
  RepaymentChipTheme.warning() : this._(HciColors.orangeText, HciColors.orangeBg);
  RepaymentChipTheme.error() : this._(HciColors.primary500, HciColors.redBg);
  RepaymentChipTheme.success() : this._(HciColors.semanticGreen900, HciColors.greenBg);
  RepaymentChipTheme.progress() : this._(HciColors.orangeIndicator, HciColors.yellowBg);
  RepaymentChipTheme.inactive() : this._(HciColors.supplementary500, HciColors.supplementary50);
  RepaymentChipTheme.highlight() : this._(HciColors.primary500, HciColors.primary50);
  RepaymentChipTheme.fromColors(Color foregroundColor, Color backgroundColor)
      : this._(foregroundColor, backgroundColor);
}

class RepaymentSimpleChip extends StatelessWidget {
  final RepaymentChipTheme chipTheme;
  final String label;

  const RepaymentSimpleChip({
    Key? key,
    required this.label,
    required this.chipTheme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: chipTheme.backgroundColor,
        borderRadius: BorderRadius.circular(KoyalRoundedCorner.cardCorner),
      ),
      child: KoyalPadding.smallHorizontal(
        child: KoyalText.caption1(
          label,
          textAlign: TextAlign.center,
          color: chipTheme.foregroundColor,
        ),
      ),
    );
  }
}
