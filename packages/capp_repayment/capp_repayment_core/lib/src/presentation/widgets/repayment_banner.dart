import 'package:capp_domain/capp_domain.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment_core.dart';

enum BannerMode { single, multiple }

class RepaymentBanner extends StatefulWidget {
  // Banner on main screen -> use specific banner ID config from FF
  // Banner on repayment success screen -> fetch list banner type BannerCtaType.loanRepayment
  final api_models.BannerCtaType? bannerType;
  final BannerMode bannerMode;

  // For tracking purpose
  final String contractNumber;

  // Get specific banner
  final String? bannerId;

  const RepaymentBanner({
    Key? key,
    this.bannerType,
    required this.contractNumber,
    this.bannerId,
    required this.bannerMode,
  }) : super(key: key);

  @override
  State<RepaymentBanner> createState() => _RepaymentBannerState();
}

class _RepaymentBannerState extends State<RepaymentBanner> {
  int currentIndex = 0;
  bool padEnds = false;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (c) => c.get<RepaymentBannerBloc>()
        ..add(
          RepaymentBannerEvent.initialize(
            mode: widget.bannerMode,
            bannerId: widget.bannerId,
            type: widget.bannerType,
          ),
        ),
      child: BlocBuilder<RepaymentBannerBloc, RepaymentBannerState>(
        builder: (context, state) {
          final banners = state.banners;
          if (banners.length == 1) {
            final imageUrl = banners.first.image?.url ?? '';
            final deepLinkUrl = banners.first.link ?? '';
            final bannerWidth = MediaQuery.of(context).size.width - 32;
            final bannerHeight = bannerWidth * 408 / 1080;

            return GestureDetector(
              onTap: () => context.get<IDeeplinkService>().deeplinkOrLaunch(deepLinkUrl, context),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: Image.network(
                  imageUrl,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) {
                      return child;
                    } else {
                      return KoyalShimmer(
                        child: Container(
                          decoration: BoxDecoration(
                            color: HciColors.black,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: SizedBox(
                            height: bannerHeight,
                            width: bannerWidth,
                          ),
                        ),
                      );
                    }
                  },
                  errorBuilder: (context, exception, stackTrace) {
                    return const SizedBox.shrink();
                  },
                  fit: BoxFit.fill,
                  height: bannerHeight,
                  width: bannerWidth,
                ),
              ),
            );
          } else if (banners.length > 1) {
            final bannerWidth = MediaQuery.of(context).size.width - 64;
            final bannerHeight = bannerWidth * 408 / 1080;
            const viewportFaction = 0.95;

            return KoyalPadding.normalHorizontal(
              child: Column(
                children: [
                  const SizedBox(height: 8),
                  CarouselSlider.builder(
                    itemCount: banners.length,
                    options: CarouselOptions(
                      height: bannerHeight,
                      enableInfiniteScroll: false,
                      viewportFraction: viewportFaction,
                      clipBehavior: Clip.none,
                      padEnds: padEnds,
                      disableCenter: true,
                      onPageChanged: (index, reason) {
                        setState(() {
                          currentIndex = index;

                          if (index == 0) {
                            padEnds = false;
                          } else if (index == banners.length - 1) {
                            padEnds = false;
                          } else {
                            padEnds = true;
                          }
                        });
                      },
                    ),
                    itemBuilder: (context, index, realIndex) {
                      final banner = banners[index];
                      final imageUrl = banner.image?.url ?? '';
                      final deepLinkUrl = banner.link ?? '';
                      EdgeInsets pad;
                      if (index == 0) {
                        pad = const EdgeInsets.only(right: 8);
                      } else if (index == banners.length - 1) {
                        pad = const EdgeInsets.only(left: 8);
                      } else {
                        pad = const EdgeInsets.symmetric(horizontal: 8);
                      }
                      return Container(
                        padding: pad,
                        child: InkWell(
                          onTap: () {
                            if (deepLinkUrl.isNotEmpty) {
                              context.get<IDeeplinkService>().deeplinkOrLaunch(deepLinkUrl, context);
                            }
                          },
                          child: Container(
                            height: bannerHeight,
                            clipBehavior: Clip.antiAlias,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Image.network(
                              imageUrl,
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) {
                                  return child;
                                } else {
                                  return KoyalShimmer(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: HciColors.black,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: SizedBox(
                                        height: bannerHeight,
                                        width: bannerWidth,
                                      ),
                                    ),
                                  );
                                }
                              },
                              errorBuilder: (context, exception, stackTrace) {
                                return const SizedBox.shrink();
                              },
                              fit: BoxFit.fill,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  DotsIndicator(
                    dotsCount: banners.length,
                    position: currentIndex.toDouble(),
                    decorator: DotsDecorator(
                      color: Theme.of(context).disabledColor,
                      activeColor: Theme.of(context).primaryColor,
                      spacing: const EdgeInsets.all(4.0),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            );
          } else {
            return Container();
          }
        },
      ),
    );
  }
}
