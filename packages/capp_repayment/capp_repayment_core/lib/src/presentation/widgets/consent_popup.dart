import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment_core.dart';

class ConsentPopup extends StatelessWidget {
  final Function()? onTapOk;
  final String? imagePath;
  final String? title;
  final String? description;
  final TextAlign? descriptionTextAlign;
  final String? buttonText;

  const ConsentPopup({
    Key? key,
    this.imagePath,
    this.title,
    this.description,
    this.descriptionTextAlign,
    this.buttonText,
    this.onTapOk,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      key: const Key('__consentPopup__'),
      child: Container(
        padding: const EdgeInsets.only(top: 15, bottom: 3),
        decoration: BoxDecoration(
          color: ColorTheme.of(context).backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
            //ignore: no-direct-padding
            Padding(
              padding: const EdgeInsets.only(left: 24, right: 24, bottom: 16),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 8,
                    decoration: BoxDecoration(
                      color: ColorTheme.of(context).foreground5Color,
                      borderRadius: BorderRadius.circular(100.0),
                    ),
                  ),
                  const SizedBox(height: 14),
                  Visibility(
                    visible: imagePath != null && imagePath!.isNotEmpty,
                    child: Image.asset(
                      imagePath ?? '',
                      package: 'capp_repayment',
                      height: 106,
                      width: 141,
                    ),
                  ),
                  Visibility(
                    visible: title != null && title!.isNotEmpty,
                    child: Column(
                      children: [
                        const SizedBox(height: 32),
                        KoyalText.header5(
                          title ?? '',
                          color: ColorTheme.of(context).defaultTextColor,
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: double.infinity,
                    child: KoyalText.body2(
                      description ?? '',
                      color: ColorTheme.of(context).secondaryTextColor,
                      textAlign: descriptionTextAlign ?? TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
            VerticalButtonsLayout(
              primaryButton: PrimaryButton(
                key: const Key('__consentPopupContinueButton__'),
                text: buttonText ?? L10nCappRepayment.of(context).onepayGotItContinue,
                onPressed: () {
                  onPressedOk(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> onPressedOk(BuildContext context) async {
    context.navigator.pop();
    if (null != onTapOk) {
      onTapOk!();
    }
  }
}
