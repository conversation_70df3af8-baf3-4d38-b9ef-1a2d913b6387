import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentNonSelectableContainer extends StatelessWidget {
  final String imagePath;
  final String title;
  final String des;

  const RepaymentNonSelectableContainer({Key? key, required this.imagePath, required this.title, required this.des})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalHorizontal(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          imagePath.asImage(
            fit: BoxFit.contain,
            height: 56,
            width: 56,
            imagePackage: 'capp_repayment_core',
          ),
          const SizedBox(
            width: 16,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                KoyalText.subtitle2(
                  title,
                  color: ColorTheme.of(context).defaultTextColor,
                  textAlign: TextAlign.left,
                ),
                const SizedBox(height: 4),
                KoyalText.body2(
                  des,
                  color: ColorTheme.of(context).secondaryTextColor,
                  textAlign: TextAlign.left,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
