import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentMethodNonSelectableContainer extends StatelessWidget {
  final String? imagePath;
  final String title;
  final String? description;
  final VoidCallback? onTap;
  final String? imagePackage;

  const RepaymentMethodNonSelectableContainer({
    this.imagePath,
    required this.title,
    this.description,
    this.onTap,
    this.imagePackage,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: _content(),
    );
  }

  Widget _content() {
    final des = description ?? '';
    return Container(
      margin: const EdgeInsets.fromLTRB(6, 0, 6, 8),
      padding: const EdgeInsets.fromLTRB(12, 12, 12, 12),
      decoration: BoxDecoration(
        border: Border.all(color: HciColors.supplementary50),
        borderRadius: BorderRadius.circular(8),
        color: HciColors.supplementary0,
      ),
      child: Row(
        children: [
          SizedBox(
            width: 56,
            height: 56,
            child: imagePath.asImage(
              height: 56,
              imagePackage: imagePackage,
            ),
          ),
          Expanded(
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
                          //ignore: no-direct-padding
                          Padding(
                            padding: const EdgeInsets.only(
                              left: 12,
                              top: 4,
                            ),
                            child: KoyalText.subtitle2(
                              title,
                              color: HciColors.supplementary900,
                            ),
                          ),
                          if (des.isNotEmpty)
                            // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
                            //ignore: no-direct-padding
                            Padding(
                              padding: const EdgeInsets.only(
                                left: 12,
                                top: 4,
                              ),
                              child: KoyalText.body2(
                                des,
                                color: HciColors.supplementary500,
                              ),
                            ),
                          if (null == description) const SizedBox(height: 4),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
