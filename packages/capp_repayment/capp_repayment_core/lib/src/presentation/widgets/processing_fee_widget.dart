import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart' hide ChipTheme;
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment_core.dart';

class ProcessingFeeWidget extends StatelessWidget {
  final bool isWaived;
  final Decimal? processingFee;

  const ProcessingFeeWidget({
    Key? key,
    required this.isWaived,
    this.processingFee,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.normalAll(
      child: Row(
        children: [
          KoyalText.body2(
            L10nCappRepayment.of(context).serviceFee,
            color: ColorTheme.of(context).secondaryTextColor,
          ),
          _waivedWidget(context, isWaived),
          Expanded(
            child: KoyalPadding.normalAll(
              top: false,
              right: false,
              bottom: false,
              child: KoyalText.body2(
                (isWaived ? Decimal.zero : (processingFee ?? Decimal.zero)).formatCurrency(),
                color: ColorTheme.of(context).defaultTextColor,
                key: const Key('__paymentProcessingFeeText__'),
                textAlign: TextAlign.end,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _waivedWidget(BuildContext context, bool isWaived) {
    return isWaived
        ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 8),
              SimpleChip(
                label: L10nCappRepayment.of(context).waived,
                chipTheme: ChipTheme.fromColors(
                  ColorTheme.of(context).successIndicatorColor,
                  // TODO(responsible-team): Consider to replace with color from ColorTheme.
                  // ignore: no-hcicolors
                  HciColors.greenBg,
                ),
              ),
            ],
          )
        : const SizedBox.shrink();
  }
}
