import 'package:capp_ui_core/capp_ui.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '../../../capp_repayment_core.dart';

class RepaymentPaymentGuideContainer extends StatefulWidget {
  final List<TabModel> tabModels;
  final Widget? pageTitle;
  final List<Widget> pages;
  final Function(int)? onTabChange;
  final bool? hideGuidelineTitle;
  final bool? scrollable;
  final double? height;

  const RepaymentPaymentGuideContainer({
    Key? key,
    required this.tabModels,
    required this.pages,
    this.onTabChange,
    this.scrollable,
    this.hideGuidelineTitle,
    this.height,
    this.pageTitle,
  }) : super(key: key);

  @override
  RepaymentPaymentGuideContainerState createState() => RepaymentPaymentGuideContainerState();
}

class RepaymentPaymentGuideContainerState extends State<RepaymentPaymentGuideContainer>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  int selectedIndex = 0;

  @override
  void dispose() {
    _tabController?.removeListener(_handleTabChange);
    _tabController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (null == _tabController && widget.tabModels.isNotEmpty) {
      _initTabController(widget.tabModels.length);
    }
    final guidelineContent = widget.pages.isNotEmpty ? _buildTabBarView(widget.pages) : Container();
    return SizedBox(
      height: widget.height,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.hideGuidelineTitle != true)
            KoyalPadding.normalAll(
              child: KoyalText.subtitle2(
                L10nCappRepayment.of(context).repaymentGuidance,
                color: ColorTheme.of(context).defaultTextColor,
              ),
            ),
          if (widget.tabModels.length > 1) _buildTabBar(context, widget.tabModels) else Container(),
          if (widget.pageTitle != null) widget.pageTitle!,
          if (widget.height != null)
            Expanded(
              child: guidelineContent,
            )
          else
            guidelineContent,
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context, List<TabModel> tabModels, {bool dense = false}) {
    return Tabs(
      tabController: _tabController ?? TabController(length: 0, vsync: this),
      scrollable: widget.scrollable ?? false,
      darkenBackground: dense,
      tabs: tabModels,
    );
  }

  void _handleTabChange() {
    final selectedTabIndex = _tabController?.index ?? 0;
    if (widget.onTabChange != null) {
      // ignore: prefer_null_aware_method_calls
      widget.onTabChange!(selectedTabIndex);
    }
    setState(() {
      FocusScope.of(context).unfocus();
      selectedIndex = selectedTabIndex;
    });
  }

  void _initTabController(int numberOfTabs) {
    _tabController = TabController(length: numberOfTabs, initialIndex: selectedIndex, vsync: this);
    _tabController?.addListener(_handleTabChange);
  }

  Widget _buildTabBarView(List<Widget> views) {
    final pages = <Widget>[];
    views.forEachIndexed((i, view) {
      pages.add(Visibility(visible: selectedIndex == i, child: view));
    });

    if (pages.length > 1) {
      return IndexedStack(
        index: selectedIndex,
        children: pages,
      );
    } else if (pages.length == 1) {
      return pages[0];
    } else {
      return Container();
    }
  }
}
