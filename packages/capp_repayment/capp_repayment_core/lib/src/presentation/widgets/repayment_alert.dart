import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentAlert extends StatelessWidget {
  final RichText text;
  final AlertTheme theme;
  final Key? textKey;
  final VoidCallback? onClose;
  final Key? closeButtonKey;
  final IconData? customIcon;
  final bool hasPadding;
  final bool topBorderRadius;
  final PrimaryButton? primaryButton;

  const RepaymentAlert({
    required this.text,
    required this.theme,
    this.hasPadding = false,
    this.onClose,
    this.closeButtonKey,
    this.customIcon,
    Key? key,
    this.textKey,
    this.topBorderRadius = true,
    this.primaryButton,
  }) : super(key: key);

  const RepaymentAlert._({
    required this.text,
    required this.theme,
    this.hasPadding = false,
    this.onClose,
    this.closeButtonKey,
    this.customIcon,
    Key? key,
    this.textKey,
    this.topBorderRadius = true,
    this.primaryButton,
  }) : super(key: key);

  const RepaymentAlert.padding({
    required RichText text,
    required AlertTheme theme,
    VoidCallback? onClose,
    Key? closeButtonKey,
    IconData? customIcon,
    Key? textKey,
    Key? key,
    PrimaryButton? primaryButton,
  }) : this._(
          key: key,
          text: text,
          theme: theme,
          onClose: onClose,
          closeButtonKey: closeButtonKey,
          customIcon: customIcon,
          textKey: textKey,
          hasPadding: true,
          primaryButton: primaryButton,
        );
  @override
  Widget build(BuildContext context) {
    return KoyalSemantics(
      customIdentifier: 'Alert',
      child: Padding(
        padding: hasPadding ? const EdgeInsets.all(KoyalPadding.paddingNormal) : EdgeInsets.zero,
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: topBorderRadius
                ? BorderRadius.circular(8)
                : const BorderRadius.only(
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
            color: theme.backgroundColor,
          ),
          padding: primaryButton == null
              ? const EdgeInsets.all(KoyalPadding.paddingSmall)
              : const EdgeInsets.all(KoyalPadding.paddingNormal),
          child: Column(
            children: [
              Row(
                children: [
                  SizedBox(
                    height: 24,
                    child: Align(
                      child: Icon(
                        icon,
                        size: 20,
                        color: theme.foregroundColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: KoyalPadding.paddingSmall),
                  Expanded(
                    child: text,
                  ),
                  if (onClose != null)
                    const SizedBox(
                      width: KoyalPadding.paddingNormal,
                    ),
                  if (onClose != null)
                    InkWell(
                      key: closeButtonKey,
                      onTap: onClose,
                      child: Icon(
                        KoyalIcons.close_outline,
                        color: theme.foregroundColor,
                        size: 24,
                      ),
                    ),
                ],
              ),
              if (primaryButton != null)
                KoyalPadding.normalAll(
                  left: false,
                  right: false,
                  bottom: false,
                  child: primaryButton!,
                ),
            ],
          ),
        ),
      ),
    );
  }

  IconData get icon =>
      customIcon ??
      (theme.bannerType == BannerType.error
          ? KoyalIcons.alert_circle_outline
          : theme.bannerType == BannerType.success
              ? KoyalIcons.checkmark_circle_outline
              : KoyalIcons.information_circle_outline);
}
