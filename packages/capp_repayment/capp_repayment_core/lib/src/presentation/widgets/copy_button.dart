import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../capp_repayment_core.dart';

class CopyButton extends StatelessWidget {
  final String? label;
  final String copyText;
  final double? size;
  final Function()? onTap;
  const CopyButton({Key? key, required this.copyText, this.label, this.onTap, this.size}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onTap != null) {
          onTap!.call();
        }
        Clipboard.setData(ClipboardData(text: copyText));
        showToast(context, L10nCappRepayment.of(context).transactionDetailsCopySuccess);
      },
      child: Row(
        children: [
          Icon(
            KoyalIcons.copy_outline,
            color: ColorTheme.of(context).primaryColor,
            size: size ?? 16.0,
          ),
          const SizedBox(width: 2),
          KoyalText.body3(
            label != null ? label! : L10nCappRepayment.of(context).transactionDetailsCopyBtn,
            color: ColorTheme.of(context).errorTextColor,
          ),
        ],
      ),
    );
  }
}
