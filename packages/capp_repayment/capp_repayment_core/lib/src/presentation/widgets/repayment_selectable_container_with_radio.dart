import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class RepaymentSelectableContainerWithRadio<T> extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String description;
  final Widget avatar;
  final String? actionText;
  final bool isValid;
  final VoidCallback? action;
  final ValueChanged<T?> onChanged;
  final T? groupValue;
  final T value;

  const RepaymentSelectableContainerWithRadio({
    Key? key,
    required this.title,
    this.subtitle,
    required this.description,
    required this.avatar,
    this.actionText,
    this.isValid = true,
    this.action,
    required this.onChanged,
    required this.groupValue,
    required this.value,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChanged.call(value);
      },
      child: Container(
        decoration: BoxDecoration(
          color: ColorTheme.of(context).backgroundColor,
        ),
        child: Container(
          decoration: groupValue == value
              ? BoxDecoration(
                  color: ColorTheme.of(context).backgroundColor,
                  border: Border.all(
                    color: ColorTheme.of(context).infoIndicatorColor,
                  ),
                  borderRadius: BorderRadius.circular(KoyalRoundedCorner.cardCorner),
                )
              : BoxDecoration(
                  color: ColorTheme.of(context).backgroundColor,
                  border: Border.all(
                    color: ColorTheme.of(context).foreground15Color,
                  ),
                  borderRadius: BorderRadius.circular(KoyalRoundedCorner.cardCorner),
                ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              KoyalPadding.normalAll(
                child: avatar,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
                              //ignore: no-direct-padding
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: KoyalPadding.paddingNormal,
                                  right: KoyalPadding.paddingNormal,
                                ),
                                child: KoyalText.subtitle2(
                                  color: isValid
                                      ? ColorTheme.of(context).defaultTextColor
                                      : ColorTheme.of(context).foreground50Color,
                                  title,
                                  key: const Key('__selectableContainerWithRadioTitle__'),
                                ),
                              ),
                              if ((subtitle ?? '').isNotEmpty)
                                KoyalText.body2(
                                  color: ColorTheme.of(context).defaultTextColor,
                                  subtitle!,
                                  key: const Key('__selectableContainerWithRadioSubtitle__'),
                                ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            top: KoyalPadding.paddingNormal,
                            right: KoyalPadding.paddingNormal,
                          ),
                          child: KoyalRadio<T>(
                            key: const Key('__selectableContainerWithRadioRadio__'),
                            onChanged: isValid ? onChanged : null,
                            value: value,
                            groupValue: groupValue,
                          ),
                        ),
                      ],
                    ),
                    // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
                    //ignore: no-direct-padding
                    Padding(
                      padding: const EdgeInsets.only(
                        right: 12,
                      ),
                      child: KoyalText.body2(
                        color: isValid
                            ? ColorTheme.of(context).secondaryTextColor
                            : ColorTheme.of(context).foreground40Color,
                        description,
                        key: const Key('__selectableContainerWithRadioDescription__'),
                      ),
                    ),
                    if (action != null)
                      TertiaryButton(
                        key: Key('__selectableContainerWithRadioButton-${title}__'),
                        text: actionText ?? L10nCappUi.of(context).learnMore,
                        onPressed: action,
                        padding: EdgeInsets.zero,
                      )
                    else
                      const SizedBox(
                        height: KoyalPadding.paddingNormal,
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
