import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../capp_repayment_core.dart';

class RepaymentBankTransferQrInfo extends StatelessWidget {
  final String accountName;
  final String bankName;
  final String bankBranch;
  final String virtualAccountNumber;
  final bool isBankTransferInfoExpanded;
  final bool isHasPromotion;

  final Function()? onTapCopyVirtualAccountNumber;
  final Function()? onToggleVaInfo;

  const RepaymentBankTransferQrInfo({
    Key? key,
    required this.accountName,
    required this.bankName,
    required this.bankBranch,
    required this.virtualAccountNumber,
    this.onTapCopyVirtualAccountNumber,
    this.onToggleVaInfo,
    this.isBankTransferInfoExpanded = false,
    this.isHasPromotion = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            onToggleVaInfo?.call();
          },
          child: KoyalPadding.normalAll(
            key: const Key('__transferToAccountNumberHeader__'),
            child: Row(
              children: [
                KoyalText.subtitle1(
                  L10nCappRepayment.of(context).transferToAccountNumber,
                ),
                const Spacer(),
                Icon(
                  isBankTransferInfoExpanded ? KoyalIcons.chevron_up_outline : KoyalIcons.chevron_down_outline,
                  size: 24,
                ),
              ],
            ),
          ),
        ),
        AnimatedClipRect(
          visible: isBankTransferInfoExpanded,
          animatedContent: Column(
            children: [
              if (isHasPromotion)
                KoyalPadding.normalAll(
                  child: Alert(
                    key: const Key('__notApplicableAlert__'),
                    text: L10nCappRepayment.of(context).repaymentPromotionNotApplicable,
                    theme: AlertTheme.warning(),
                  ),
                ),
              ExtendedInfoListItem(
                key: const Key('__accountNameInfo__'),
                title: L10nCappRepayment.of(context).repaymentAccountNameNormal,
                bodyText: accountName,
              ),
              const ListDivider(),
              ExtendedInfoListItem(
                key: const Key('__bankNameInfo__'),
                title: L10nCappRepayment.of(context).repaymentBankNameNormal,
                bodyText: bankName,
              ),
              const ListDivider(),
              ExtendedInfoListItem(
                key: const Key('__bankBranchInfo__'),
                title: L10nCappRepayment.of(context).repaymentBankBranchNormal,
                bodyText: bankBranch,
              ),
              const ListDivider(),
              KoyalPadding.normalAll(
                key: const Key('__virtualAccountNumberInfo__'),
                child: Row(
                  children: [
                    KoyalText.body2(
                      L10nCappRepayment.of(context).repaymentVirtualAccountNumberNormal,
                      color: ColorTheme.of(context).secondaryTextColor,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: KoyalText.subtitle1(
                        virtualAccountNumber,
                        color: ColorTheme.of(context).defaultTextColor,
                        textAlign: TextAlign.end,
                      ),
                    ),
                    const SizedBox(width: 21),
                    CopyButton(
                      label: '',
                      copyText: virtualAccountNumber,
                      onTap: onTapCopyVirtualAccountNumber,
                    ),
                  ],
                ),
              ),
              const ListDivider(),
            ],
          ),
        ),
      ],
    );
  }
}
