import 'dart:io';

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../../capp_repayment_core.dart';

class RepaymentWebViewScreen extends StatefulWidget {
  final RepaymentWebViewRouteArgs? arguments;

  const RepaymentWebViewScreen({
    this.arguments,
    Key? key,
  }) : super(key: key);

  @override
  State createState() {
    return RepaymentWebViewScreenState();
  }
}

class RepaymentWebViewScreenState extends State<RepaymentWebViewScreen> {
  late InAppWebViewSettings options;

  @override
  void initState() {
    options = InAppWebViewSettings(
      useShouldOverrideUrlLoading: false,
      mediaPlaybackRequiresUserGesture: false,
      clearCache: true,
      cacheEnabled: false,
      allowsInlineMediaPlayback: Platform.isIOS,
    );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      appBar: KoyalAppBar(
        customTitle: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if ((widget.arguments?.title ?? '').isNotEmpty)
              KoyalText.subtitle2(
                widget.arguments!.title!,
                color: ColorTheme.of(context).defaultTextColor,
              ),
            KoyalText.caption2(
              widget.arguments?.url ?? '',
              color: ColorTheme.of(context).defaultTextColor,
            ),
          ],
        ),
      ),
      body: Stack(
        children: [
          if (!Platform.isLinux)
            InAppWebView(
              key: const Key('__repaymentInAppWebView__'),
              initialUrlRequest: URLRequest(url: WebUri.uri(Uri.parse(widget.arguments?.url ?? ''))),
              initialSettings: options,
              onWebViewCreated: (controller) {},
              onPermissionRequest: (controller, permissionRequest) async {
                return PermissionResponse(
                  resources: permissionRequest.resources,
                  action: PermissionResponseAction.GRANT,
                );
              },
              onReceivedError: (controller, request, error) {},
              onProgressChanged: (controller, progress) {},
              gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{}
                ..add(const Factory<VerticalDragGestureRecognizer>(VerticalDragGestureRecognizer.new)),
              onUpdateVisitedHistory: (controller, url, androidIsReload) {},
              onConsoleMessage: (controller, consoleMessage) {
                debugPrint(consoleMessage.message);
              },
              onReceivedServerTrustAuthRequest: (controller, challenge) async {
                debugPrint(challenge.toString());
                return ServerTrustAuthResponse(action: ServerTrustAuthResponseAction.PROCEED);
              },
            )
          else
            const SizedBox(),
        ],
      ),
    );
  }
}
