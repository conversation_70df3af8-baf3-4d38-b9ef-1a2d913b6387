// ignore_for_file: use_build_context_synchronously

import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_home_core/capp_home_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_repayment_core.dart';

class RepaymentTransactionFailedScreen extends StatefulWidget {
  final RepaymentTransactionFailedRouteArgs? arguments;

  const RepaymentTransactionFailedScreen({Key? key, this.arguments}) : super(key: key);

  @override
  State<RepaymentTransactionFailedScreen> createState() => _RepaymentTransactionFailedScreenState();
}

class _RepaymentTransactionFailedScreenState extends State<RepaymentTransactionFailedScreen> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final countryFlavor = await PackageUtils.getFlavor();
      var journeyId = '';
      switch (countryFlavor) {
        case CountryFlavor.id:
          context.get<CappRepaymentTrackingService>().trackOwnTransactionErrorScreenView();
          journeyId = CoreConstants.idSuccessFailedFeedbackJourneyId;
          break;
        case CountryFlavor.vn:
          // Handle tracking error type for VN
          final errorType = widget.arguments?.errorType ?? '';
          if (errorType.isNotEmpty) {
            context.get<CappRepaymentTrackingService>().trackOwnTransactionErrorView(errorType: errorType);
          } else {
            context.get<CappRepaymentTrackingService>().trackOwnTransactionErrorScreenView();
          }
          journeyId = CoreConstants.vnSuccessFailedFeedbackJourneyId;
          break;
        case CountryFlavor.ph:
          context.get<CappRepaymentTrackingService>().trackOwnTransactionErrorScreenView();
          journeyId = CoreConstants.phSuccessFailedFeedbackJourneyId;
          break;
        default:
          break;
      }

      if ((widget.arguments?.isShowFeedback ?? false) &&
          context.isFlagEnabledRead(FeatureFlag.repaymentSatisfactionFeedback)) {
        Future.delayed(const Duration(seconds: 1), () async {
          if (journeyId.isNotEmpty) {
            await showFeedbackOverlay(context, journeyId: journeyId);
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final title = widget.arguments?.title ?? '';
    final isBackToHomePageOnly = widget.arguments?.isBackToHomePageOnly ?? false;
    return KoyalWillPopScope(
      onWillPop: () async => false,
      child: KoyalScaffold(
        key: const Key('__transactionFailedScreen__'),
        appBar: KoyalAppBar(
          key: const Key('__transactionFailedAppBar__'),
          leading: KoyalAppBarLeading.none,
          title: L10nCappRepayment.of(context).transactionDetailsSection,
        ),
        backgroundColor: ColorTheme.of(context).backgroundColor,
        body: Stack(
          children: [
            Positioned(
              top: 24,
              left: 24,
              right: 24,
              child: Column(
                children: [
                  KoyalText.header5(
                    (title.isNotEmpty)
                        ? title
                        : L10nCappRepayment.of(context).repaymentTransactionFailedSomethingWentWrong,
                    textAlign: TextAlign.center,
                    color: ColorTheme.of(context).defaultTextColor,
                  ),
                  const SizedBox(height: 8),
                  KoyalText.body2(
                    (widget.arguments != null && widget.arguments!.errorMessage.isNotEmpty)
                        ? widget.arguments!.errorMessage
                        : L10nCappRepayment.of(context).repaymentTransactionFailedGenericMessage,
                    textAlign: TextAlign.center,
                    color: ColorTheme.of(context).secondaryTextColor,
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: isBackToHomePageOnly
                  ? VerticalButtonsLayout(
                      primaryButton: PrimaryButton(
                        text: L10nCappRepayment.of(context).backToHomepage,
                        onPressed: () {
                          context.get<CappRepaymentTrackingService>().trackOwnTransactionErrorClickBackToHome();
                          context.get<GlobalTrackingProperties>().repaymentAbTest = null;
                          context.navigator.toMainScreen();
                        },
                      ),
                    )
                  : VerticalButtonsLayout(
                      primaryButton: PrimaryButton(
                        text: L10nCappRepayment.of(context).transactionFailedTryAgain,
                        onPressed: () {
                          context.get<CappRepaymentTrackingService>().trackOwnTransactionErrorClickTryAgain();

                          final rootRouteName = widget.arguments?.rootRouteName;
                          final tryAgainPopUntilScreenName = widget.arguments?.tryAgainPopUntilScreenName ?? '';
                          if (tryAgainPopUntilScreenName.isNotEmpty) {
                            context.navigator.popUntilFromPackage('CappRepayment', tryAgainPopUntilScreenName);
                          } else if (rootRouteName == null || rootRouteName == 'repayment_payment_summary_screen') {
                            final fromOnlineMethod = widget.arguments?.fromOnlineMethod ?? true;
                            if (fromOnlineMethod) {
                              context.navigator.popUntilFromPackage(
                                'CappRepayment',
                                context.isFlagEnabledRead(FeatureFlag.repaymentNew)
                                    ? 'RepaymentMainScreen'
                                    : 'RepaymentOnlinePaymentScreen',
                              );
                            } else {
                              context.navigator.popUntilFromPackage(
                                'CappRepayment',
                                context.isFlagEnabledRead(FeatureFlag.repaymentNew)
                                    ? 'RepaymentMainScreen'
                                    : 'RepaymentMethodSelectionScreen',
                              );
                            }
                          } else {
                            context.get<GlobalTrackingProperties>().repaymentAbTest = null;
                            context.navigator.toMainScreen();
                          }
                        },
                      ),
                      secondaryButton: SecondaryButton(
                        text: L10nCappRepayment.of(context).backToHomepage,
                        onPressed: () {
                          context.get<CappRepaymentTrackingService>().trackOwnTransactionErrorClickBackToHome();
                          context.get<GlobalTrackingProperties>().repaymentAbTest = null;
                          context.navigator.toMainScreen();
                        },
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
