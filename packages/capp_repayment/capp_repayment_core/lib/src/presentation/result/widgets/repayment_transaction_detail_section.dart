import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import '../../../../capp_repayment_core.dart';

class TransactionDetailSection extends StatelessWidget {
  final String transactionNo;
  final String paymentOption;
  final String dateProcessed;
  final String dateFormat;
  final Widget? processingFee;
  final Function()? onTapCopyTransNo;

  const TransactionDetailSection({
    Key? key,
    required this.transactionNo,
    required this.paymentOption,
    required this.dateProcessed,
    this.dateFormat = 'MMMM dd, yyyy hh:mm a',
    this.processingFee,
    this.onTapCopyTransNo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SectionHeading(title: L10nCappRepayment.of(context).transactionDetails),
        ExtendedInfoListItem(
          title: L10nCappRepayment.of(context).transactionDetailsNo,
          bodyText: transactionNo,
          iconButton: copyBtn(context),
        ),
        const ListDivider(),
        ExtendedInfoListItem(
          title: L10nCappRepayment.of(context).transactionDetailsPaymentOption,
          bodyText: paymentOption,
        ),
        const ListDivider(),
        processingFee ?? const SizedBox.shrink(),
        const ListDivider(),
        ExtendedInfoListItem(
          title: L10nCappRepayment.of(context).transactionDetailsDateProcessed,
          bodyText: DateFormat(dateFormat).format(DateTime.parse(dateProcessed).toLocal()),
        ),
      ],
    );
  }

  KoyalIconButton copyBtn(BuildContext context) {
    return KoyalIconButton(
      iconData: KoyalIcons.copy_outline,
      onPressed: () {
        if (onTapCopyTransNo != null) {
          onTapCopyTransNo!.call();
        }
        Clipboard.setData(ClipboardData(text: transactionNo));
        showToast(context, L10nCappRepayment.of(context).transactionDetailsCopySuccess);
      },
    );
  }
}
