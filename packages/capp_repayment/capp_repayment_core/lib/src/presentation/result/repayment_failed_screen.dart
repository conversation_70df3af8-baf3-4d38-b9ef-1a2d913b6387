import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_repayment_core.dart';

class RepaymentFailedScreen extends StatefulWidget {
  final RepaymentFailedRouteArgs arguments;

  const RepaymentFailedScreen({Key? key, required this.arguments}) : super(key: key);

  @override
  State<RepaymentFailedScreen> createState() => _RepaymentFailedScreenState();
}

class _RepaymentFailedScreenState extends State<RepaymentFailedScreen> {
  @override
  Widget build(BuildContext context) {
    final errorTitle = widget.arguments.errorTitle ?? '';
    final errorMessage = widget.arguments.errorMessage ?? '';
    final isBackToHomePageOnly = widget.arguments.isBackToHomePageOnly ?? false;
    return KoyalWillPopScope(
      onWillPop: () async => false,
      child: KoyalScaffold(
        key: const Key('__failedScreen__'),
        appBar: KoyalAppBar(
          key: const Key('__failedAppBar__'),
          leading: KoyalAppBarLeading.none,
          title: widget.arguments.screenTitle,
        ),
        backgroundColor: ColorTheme.of(context).backgroundColor,
        body: Stack(
          children: [
            Positioned(
              top: 24,
              left: 24,
              right: 24,
              child: Column(
                children: [
                  KoyalText.header5(
                    (errorTitle.isNotEmpty)
                        ? errorTitle
                        : L10nCappRepayment.of(context).repaymentTransactionFailedSomethingWentWrong,
                    textAlign: TextAlign.center,
                    color: ColorTheme.of(context).defaultTextColor,
                  ),
                  const SizedBox(height: 8),
                  KoyalText.body2(
                    (errorMessage.isNotEmpty)
                        ? errorMessage
                        : L10nCappRepayment.of(context).repaymentTransactionFailedGenericMessage,
                    textAlign: TextAlign.center,
                    color: ColorTheme.of(context).secondaryTextColor,
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: isBackToHomePageOnly
                  ? VerticalButtonsLayout(
                      primaryButton: PrimaryButton(
                        text: L10nCappRepayment.of(context).backToHomepage,
                        onPressed: () {
                          widget.arguments.onTrackBackToHome?.call();

                          if (widget.arguments.onBackToHome != null) {
                            widget.arguments.onBackToHome?.call();
                          } else {
                            context.navigator.toMainScreen();
                          }
                        },
                      ),
                    )
                  : VerticalButtonsLayout(
                      primaryButton: PrimaryButton(
                        text: L10nCappRepayment.of(context).transactionFailedTryAgain,
                        onPressed: () {
                          widget.arguments.onTrackRetry?.call();

                          if (widget.arguments.onRetry != null) {
                            widget.arguments.onRetry?.call();
                          } else {
                            final tryAgainPopUntilScreenName = widget.arguments.tryAgainPopUntilScreenName ?? '';
                            if (tryAgainPopUntilScreenName.isNotEmpty) {
                              context.navigator.popUntilFromPackage('CappRepayment', tryAgainPopUntilScreenName);
                            } else {
                              context.navigator.pop();
                            }
                          }
                        },
                      ),
                      secondaryButton: SecondaryButton(
                        text: L10nCappRepayment.of(context).backToHomepage,
                        onPressed: () {
                          widget.arguments.onTrackBackToHome?.call();

                          if (widget.arguments.onBackToHome != null) {
                            widget.arguments.onBackToHome?.call();
                          } else {
                            context.navigator.toMainScreen();
                          }
                        },
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
