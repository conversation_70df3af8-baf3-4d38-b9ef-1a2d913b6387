import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_repayment_core.dart';

class RepaymentNoLoanScreen extends StatefulWidget {
  final RepaymentNoLoanRouteArgs? arguments;

  const RepaymentNoLoanScreen({Key? key, this.arguments}) : super(key: key);

  @override
  State<RepaymentNoLoanScreen> createState() => _RepaymentNoLoanScreenState();
}

class _RepaymentNoLoanScreenState extends State<RepaymentNoLoanScreen> {
  @override
  void initState() {
    context.get<CappRepaymentTrackingService>().trackOwnNoLoanScreenView();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__noLoanScreen__'),
      appBar: KoyalAppBar(
        title: widget.arguments?.isAda == true
            ? L10nCappRepayment.of(context).repaymentAda
            : L10nCappRepayment.of(context).repaymentLoanRepayment,
      ),
      backgroundColor: ColorTheme.of(context).backgroundColor,
      body: Column(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const AssetSvgImage(
                  'assets/icons/ic_generic_error.svg',
                  package: 'capp_repayment_core',
                  height: 240,
                ),
                if (widget.arguments?.isAda == true) ...[
                  KoyalText.header5(
                    L10nCappRepayment.of(context).repaymentAdaNoLoanTitle,
                    color: ColorTheme.of(context).defaultTextColor,
                    textAlign: TextAlign.center,
                  ),
                  KoyalPadding.normalHorizontal(
                    child: KoyalText.body2(
                      L10nCappRepayment.of(context).repaymentAdaNoLoanDescription,
                      textAlign: TextAlign.center,
                      color: ColorTheme.of(context).secondaryTextColor,
                    ),
                  ),
                ] else
                  KoyalPadding.large(
                    child: KoyalText.header5(
                      L10nCappRepayment.of(context).repaymentThereIsNoAvailableLoan,
                      color: ColorTheme.of(context).defaultTextColor,
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),
          VerticalButtonsLayout(
            primaryButton: PrimaryButton(
              text: L10nCappRepayment.of(context).backToHomepage,
              onPressed: () {
                context.get<GlobalTrackingProperties>().repaymentAbTest = null;
                context.navigator.toMainScreen();
              },
            ),
          ),
        ],
      ),
    );
  }
}
