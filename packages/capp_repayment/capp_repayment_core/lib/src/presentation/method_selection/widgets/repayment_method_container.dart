import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../capp_repayment_core.dart';

class RepaymentMethodContainer extends StatelessWidget {
  final String title;
  final String subTitle;
  final String icon;
  final Function() onTap;

  const RepaymentMethodContainer(
    this.title,
    this.subTitle,
    this.icon,
    this.onTap, {
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ColorFiltered(
      colorFilter: const ColorFilter.mode(
        Colors.transparent,
        BlendMode.multiply,
      ),
      child: Container(
        decoration: BoxDecoration(
          color: ColorTheme.of(context).backgroundColor,
          border: Border.all(
            color: ColorTheme.of(context).foreground5Color,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
        //ignore: no-direct-padding
        child: Padding(
          padding: const EdgeInsets.only(left: 12, top: 12, bottom: 12, right: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image.asset(
                icon,
                package: 'capp_repayment_core',
                fit: BoxFit.contain,
                width: 56,
                height: 56,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    KoyalPadding.normalAll(
                      top: false,
                      right: false,
                      bottom: false,
                      child: KoyalText.subtitle2(
                        title,
                        color: ColorTheme.of(context).defaultTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    KoyalPadding.normalAll(
                      top: false,
                      right: false,
                      bottom: false,
                      child: KoyalText.body2(
                        subTitle,
                        color: ColorTheme.of(context).secondaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TertiaryButton(
                      key: Key('__methodContainerContinueButton-${title}__'),
                      onPressed: onTap,
                      text: L10nCappRepayment.of(context).repaymentMethodSelectionContinue,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
