import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../capp_repayment_core.dart';

class RepaymentOnlinePaymentScreenBase extends StatelessWidget {
  final String screenTitle;
  final String? screenSubTitle;
  final String? continueText;
  final String paymentAmount;
  final Widget body;
  final VoidCallback? onTapContinue;
  final bool isValidInput;

  const RepaymentOnlinePaymentScreenBase({
    Key? key,
    required this.screenTitle,
    this.screenSubTitle,
    this.continueText,
    required this.paymentAmount,
    required this.body,
    this.isValidInput = false,
    this.onTapContinue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__onlinePaymentBaseScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__onlinePaymentBaseAppBar__'),
        title: screenTitle,
      ),
      backgroundColor: ColorTheme.of(context).backgroundColor,
      body: Column(
        children: [
          Expanded(
            child: Container(
              color: ColorTheme.of(context).backgroundColor,
              child: Column(
                children: [
                  Container(
                    color: ColorTheme.of(context).backgroundColor,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
                        //ignore: no-direct-padding
                        Padding(
                          padding: const EdgeInsets.all(12),
                          child: KoyalText.subtitle1(
                            screenSubTitle ?? L10nCappRepayment.of(context).repaymentSelectOnlinePaymentOption,
                            color: ColorTheme.of(context).defaultTextColor,
                          ),
                        ),
                        // TODO(package-responsible-team): Please check if we can use KoyalPadding instead
                        //ignore: no-direct-padding
                        Padding(
                          padding: const EdgeInsets.only(top: 16, left: 12, right: 12, bottom: 16),
                          child: Row(
                            children: [
                              KoyalText.body3(
                                L10nCappRepayment.of(context).repaymentPaymentAmount,
                                color: ColorTheme.of(context).defaultTextColor,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: KoyalText.header5(
                                  paymentAmount,
                                  color: ColorTheme.of(context).defaultTextColor,
                                  key: const Key('__onlinePaymentBaseAmountText__'),
                                  textAlign: TextAlign.end,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  body,
                ],
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 4,
                  offset: const Offset(0, -2), // changes position of shadow
                ),
              ],
            ),
            child: VerticalButtonsLayout(
              primaryButton: PrimaryButton(
                key: const Key('__onlinePaymentBaseContinueButton__'),
                text: continueText ?? L10nCappRepayment.of(context).repaymentContinueToPaymentSummary,
                onPressed: isValidInput ? onTapContinue : null,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
