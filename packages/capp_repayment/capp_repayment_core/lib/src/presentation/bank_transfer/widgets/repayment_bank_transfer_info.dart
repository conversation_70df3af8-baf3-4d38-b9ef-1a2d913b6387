import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../capp_repayment_core.dart';

class RepaymentBankTransferInfo extends StatelessWidget {
  final Function()? onTapCopyAccountNum;
  final Function()? onTapCopyTransferContent;
  static const double titleWidth = 146;

  final String accountName;
  final String bankName;
  final String accountNumber;
  final String? transferContent;

  const RepaymentBankTransferInfo({
    Key? key,
    required this.accountName,
    required this.bankName,
    required this.accountNumber,
    this.transferContent,
    this.onTapCopyAccountNum,
    this.onTapCopyTransferContent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        KoyalPadding.normalAll(
          key: const Key('__bankTransferInfoTitle__'),
          child: KoyalText.subtitle2(
            L10nCappRepayment.of(context).repaymentBankTransferInformation,
            color: ColorTheme.of(context).defaultTextColor,
          ),
        ),
        KoyalPadding.normalAll(
          key: const Key('__accountNameInfo__'),
          child: Row(
            children: [
              KoyalText.body2(
                L10nCappRepayment.of(context).repaymentAccountName,
                color: ColorTheme.of(context).secondaryTextColor,
              ),
              Expanded(
                child: KoyalText.body2(
                  accountName,
                  color: ColorTheme.of(context).defaultTextColor,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
        KoyalPadding.normalAll(
          key: const Key('__bankNameInfo__'),
          child: Row(
            children: [
              KoyalText.body2(
                L10nCappRepayment.of(context).repaymentBankName,
                color: ColorTheme.of(context).secondaryTextColor,
              ),
              Expanded(
                child: KoyalText.body2(
                  bankName,
                  color: ColorTheme.of(context).defaultTextColor,
                  textAlign: TextAlign.end,
                ),
              ),
            ],
          ),
        ),
        KoyalPadding.normalAll(
          key: const Key('__accountNumberInfo__'),
          child: Row(
            children: [
              SizedBox(
                width: titleWidth,
                child: KoyalText.body2(
                  L10nCappRepayment.of(context).repaymentVirtualAccountNo,
                  color: ColorTheme.of(context).secondaryTextColor,
                ),
              ),
              Expanded(
                child: KoyalText.body2(
                  accountNumber,
                  color: ColorTheme.of(context).defaultTextColor,
                  textAlign: TextAlign.start,
                ),
              ),
              CopyButton(
                copyText: accountNumber,
                onTap: onTapCopyAccountNum,
              ),
            ],
          ),
        ),
        if (transferContent?.isNotEmpty ?? false)
          KoyalPadding.normalAll(
            key: const Key('__bankTransferContent__'),
            child: Row(
              children: [
                SizedBox(
                  width: titleWidth,
                  child: KoyalText.body2(
                    L10nCappRepayment.of(context).repaymentTransferContent,
                    color: ColorTheme.of(context).secondaryTextColor,
                  ),
                ),
                Expanded(
                  child: KoyalText.body2(
                    transferContent!,
                    color: ColorTheme.of(context).defaultTextColor,
                    textAlign: TextAlign.start,
                  ),
                ),
                CopyButton(
                  copyText: transferContent!,
                  onTap: onTapCopyTransferContent,
                ),
              ],
            ),
          ),
      ],
    );
  }
}
