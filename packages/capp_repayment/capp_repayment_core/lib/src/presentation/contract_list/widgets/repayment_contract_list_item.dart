import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_repayment_core.dart';

class RepaymentContractListItem extends StatelessWidget {
  static const dueDateWidth = 100.0;
  final RepaymentContract contract;
  final String contractNumber;
  final String? productImageUrl;
  final String productName;
  final DateTime? dueDate;
  final int? dpd;
  final Decimal dueAmount;
  final Function(RepaymentContract)? onSelectContract;
  final bool selected;

  const RepaymentContractListItem({
    Key? key,
    required this.contract,
    this.onSelectContract,
    required this.contractNumber,
    this.productImageUrl,
    required this.productName,
    required this.dueDate,
    this.dpd,
    required this.dueAmount,
    required this.selected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final dueDateString = null != dueDate ? dueDate!.toLocal().mediumDate() : '-';
    var imageExtension = '';
    if (null != productImageUrl && productImageUrl!.isNotEmpty) {
      final parts = productImageUrl!.split('.');
      if (parts.length > 1) {
        imageExtension = parts[parts.length - 1].toLowerCase();
      }
    }
    var imagePath = '';
    if (contract.contractType == RepaymentContractType.cel) {
      if (contract.loanContract?.loanType?.toLowerCase() == RepaymentLoanType.cash.id) {
        imagePath = 'assets/icons/ic_personal_loan.png';
      } else {
        imagePath = 'assets/icons/ic_generic_commodity.svg';
      }
    } else if (contract.contractType == RepaymentContractType.rel) {
      if (contract.relContract?.gmaProductType == RepaymentGmaProductType.bnpl.id) {
        imagePath = 'assets/icons/ic_bnpl.png';
      } else {
        imagePath = 'assets/icons/ic_credit_card_red.png';
      }
    }

    return KoyalPadding.small(
      top: false,
      left: false,
      right: false,
      child: SelectableContainer(
        avatar: SizedBox(
          width: 56,
          height: 56,
          child: null != productImageUrl && productImageUrl!.isNotEmpty
              ? imageExtension == 'svg'
                  ? SvgPicture.network(
                      productImageUrl!,
                      width: 56,
                      placeholderBuilder: (context) {
                        return Image.asset(
                          'assets/images/no_picture.jpg',
                          fit: BoxFit.fill,
                          width: 56,
                          height: 56,
                        );
                      },
                    )
                  : Image.network(
                      productImageUrl!,
                      errorBuilder: (context, exception, stackTrace) {
                        return Image.asset(
                          'assets/images/no_picture.jpg',
                          fit: BoxFit.fill,
                          width: 56,
                          height: 56,
                        );
                      },
                      fit: BoxFit.fitWidth,
                      width: 56,
                    )
              : imagePath.asImage(
                  fit: BoxFit.fill,
                  width: 56,
                  height: 56,
                  imagePackage: imagePath.isNotEmpty ? 'capp_repayment_core' : null,
                ),
        ),
        onTap: () {
          if (null != onSelectContract) {
            onSelectContract!(contract);
          }
        },
        isSelected: selected,
        title: productName,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Flexible(
                  child: KoyalText.body2(
                    '${L10nCappRepayment.of(context).repaymentAccountNo} $contractNumber',
                    color: ColorTheme.of(context).defaultTextColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                SizedBox(
                  width: dueDateWidth,
                  child: KoyalText.caption2(
                    L10nCappRepayment.of(context).repaymentDueDate,
                    color: ColorTheme.of(context).secondaryTextColor,
                  ),
                ),
                KoyalText.caption2(
                  L10nCappRepayment.of(context).repaymentDueAmount,
                  color: ColorTheme.of(context).secondaryTextColor,
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                SizedBox(
                  width: dueDateWidth,
                  child: KoyalText.body2(
                    dueDateString,
                    color: ColorTheme.of(context).defaultTextColor,
                  ),
                ),
                Expanded(
                  // Replace empty space by zero width space character to fix TextOverflow.ellipsis
                  child: KoyalText.body2(
                    dueAmount.formatCurrency().replaceAll('', '\u200B'),
                    color: ColorTheme.of(context).defaultTextColor,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    softwrap: false,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
