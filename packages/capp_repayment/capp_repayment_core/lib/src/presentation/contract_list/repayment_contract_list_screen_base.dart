import 'package:capp_ui_core/capp_ui.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_repayment_core.dart';

class RepaymentContractListScreenBase extends StatelessWidget {
  final String screenTitle;
  final List<RepaymentContract> contracts;
  final RepaymentContract? selectedContract;
  final LoadingState loadingState;
  final bool isError;
  final bool isRefreshing;
  final VoidCallback? onRetry;
  final VoidCallback? onRefresh;
  final VoidCallback? onTapContinue;
  final Function(RepaymentContract)? onSelectContract;
  final bool isValidInput;

  const RepaymentContractListScreenBase({
    Key? key,
    required this.screenTitle,
    required this.loadingState,
    this.isError = false,
    this.isRefreshing = false,
    this.onRetry,
    this.onRefresh,
    this.isValidInput = false,
    required this.contracts,
    this.selectedContract,
    this.onSelectContract,
    this.onTapContinue,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final items = _getBodySection(context, contracts, selectedContract);
    return KoyalScaffold(
      key: const Key('__contractListBaseScreen__'),
      appBar: KoyalAppBar(
        key: const Key('__contractListBaseAppBar__'),
        title: screenTitle,
      ),
      backgroundColor: ColorTheme.of(context).backgroundColor,
      body: AdvancedRetryContainer(
        screenName: 'repayment_contract_list_screen',
        isLoading: loadingState == LoadingState.isLoading,
        isLoaded: loadingState == LoadingState.isCompleted,
        isError: isError,
        handleLoading: loadingState == LoadingState.isLoading,
        onRetry: onRetry,
        child: isError
            ? Container()
            : Column(
                children: [
                  Expanded(
                    child: Container(
                      color: ColorTheme.of(context).backgroundColor,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Container(
                            key: const Key('__contractListBaseHeader__'),
                            width: double.infinity,
                            color: Colors.white,
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                KoyalText.subtitle1(
                                  L10nCappRepayment.of(context).repaymentPayForMyLoans,
                                  color: ColorTheme.of(context).defaultTextColor,
                                ),
                                const SizedBox(height: 4),
                                KoyalText.body2(
                                  contracts.length == 1
                                      ? '${contracts.length} ${L10nCappRepayment.of(context).repaymentLoan} ${L10nCappRepayment.of(context).repaymentNeedRepayment}'
                                      : '${contracts.length} ${L10nCappRepayment.of(context).repaymentLoans} ${L10nCappRepayment.of(context).repaymentNeedRepayment}',
                                  key: const Key('__contractListBaseNumOfLoanText__'),
                                  color: ColorTheme.of(context).secondaryTextColor,
                                ),
                              ],
                            ),
                          ),
                          Flexible(
                            child: PullToRefresh(
                              onRefreshStarted: onRefresh!,
                              isLoading: isRefreshing,
                              child: Container(
                                color: Colors.white,
                                child: ListView.builder(
                                  key: const Key('__contractListBaseContractList__'),
                                  shrinkWrap: true,
                                  itemCount: items.length,
                                  itemBuilder: (context, index) => items.elementAt(index),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  VerticalButtonsLayout(
                    primaryButton: PrimaryButton(
                      key: const Key('__contractListBaseContinueButton__'),
                      text: L10nCappRepayment.of(context).continueWord,
                      onPressed: isValidInput ? onTapContinue : null,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  List<Widget> _getBodySection(
    BuildContext context,
    List<RepaymentContract> contracts,
    RepaymentContract? selectedContract,
  ) {
    if (contracts.isEmpty) {
      return [];
    }
    final selectedContractNumber = selectedContract?.contractNumber;

    // Contracts
    final contractWidgets = contracts.mapWithIndex((index, contract) {
      String? contractNum;
      DateTime? dueDate;
      String? productName;
      String? productImageUrl;
      Decimal? dueAmount;
      int? dpd;
      if (contract.contractType == RepaymentContractType.cel) {
        final con = contract.loanContract;
        contractNum = con?.contractNumber;
        dueDate = con?.dueDate;
        productName = _getCelProductName(con, context);
        productImageUrl = con?.productImageUrl;
        dueAmount = con?.dueAmount;
        dpd = con?.dpd;
      } else {
        final con = contract.relContract;
        if (con != null) {
          final gmaProductType = con.gmaProductType;
          contractNum = con.accountNumber;
          dueDate = con.dueDate;
          dpd = con.dpd;
          productName = gmaProductType == RepaymentGmaProductType.bnpl.id
              ? L10nCappRepayment.of(context).repaymentProductBnpl
              : L10nCappRepayment.of(context).repaymentProductCreditCard;
          productImageUrl = '';
          dueAmount = con.totalAmountDue;
        }
      }

      return KoyalPadding.normalHorizontal(
        child: RepaymentContractListItem(
          key: Key('__contractListBaseContractListItem-${contractNum}__'),
          contract: contract,
          contractNumber: contractNum ?? '',
          productName: productName ?? '',
          dueDate: dueDate,
          dpd: dpd,
          productImageUrl: productImageUrl,
          dueAmount: dueAmount ?? Decimal.zero,
          selected: selectedContract != null && selectedContractNumber == contractNum,
          onSelectContract: onSelectContract,
        ),
      );
    }).toList();

    return contractWidgets;
  }

  String _getCelProductName(RepaymentLoanContract? loanContract, BuildContext context) {
    final productName = loanContract?.productName ?? '';
    if (loanContract == null) {
      return '';
    } else if (productName.isNotEmpty) {
      return productName;
    }
    return loanContract.loanType?.toLowerCase() == RepaymentLoanType.cash.id
        ? L10nCappRepayment.of(context).repaymentPersonalLoan
        : L10nCappRepayment.of(context).repaymentConsumerLoan;
  }
}
