import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

abstract class IDownloadService {
  Future<File?> downloadFile({required String url, required String fullPath});
  Future<List<int>?> downloadFileAsBytes({required String url});
}

class DownloadService implements IDownloadService {
  final Dio dio;
  DownloadService({
    required this.dio,
  });

  @override
  Future<File?> downloadFile({required String url, required String fullPath}) async {
    try {
      await dio.download(url, fullPath);
      return File(fullPath);
    } on DioError catch (e) {
      if (kDebugMode) {
        debugPrint('Download service error: $e');
      }
      return null;
    }
  }

  @override
  Future<List<int>?> downloadFileAsBytes({required String url}) async {
    try {
      final response = await dio.get<List<int>>(url, options: Options(responseType: ResponseType.bytes));
      return response.data;
    } on DioError catch (e) {
      if (kDebugMode) {
        debugPrint('Download service error: $e');
      }
      return null;
    }
  }
}
