import 'package:capp_api/capp_api.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/model/models.dart' as api_models;
import 'package:selfcareapi/selfcareapi.dart';

import '../../../capp_repayment_core.dart';

class BannerRepository implements IBannerRepository {
  final BannerApi bannerApi;
  final Logger logger;
  BannerRepository({
    required this.logger,
    required this.bannerApi,
  });

  @override
  Future<Either<ContentFailure, List<ContentBanner>>> getRepaymentBanners({
    required api_models.BannerCtaType type,
  }) async {
    try {
      final result = await bannerApi.contentActiveBannersGet(ctaType: type);
      return right((result.items ?? []).map((b) => b.toDomain()).toList());
    } on StorageRecordNotFoundException catch (_) {
      return left(const ContentFailure.notFound());
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const ContentFailure.unexpected());
    }
  }

  @override
  Future<Either<ContentFailure, ContentBanner>> getRepaymentBanner({required String bannerId}) async {
    try {
      final result = await bannerApi.contentBannersBannerIdGet(bannerId);
      return right(result.toDomain());
    } on StorageRecordNotFoundException catch (_) {
      return left(const ContentFailure.notFound());
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in repayment repository', e, s);
      return left(const ContentFailure.unexpected());
    }
  }
}
