import 'package:capp_tracking/capp_tracking.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../domain/tracking/index.dart';

class CappRepaymentTrackingService extends CappTrackingService {
  final GlobalTrackingProperties gtp;

  CappRepaymentTrackingService({
    required EventTrackingService eventTrackingService,
    required ICurrentUserRepository currentUserRepository,
    required IUserRepository userRepository,
    required this.gtp,
  }) : super(
          eventTrackingService: eventTrackingService,
          currentUserRepository: currentUserRepository,
          userRepository: userRepository,
        ) {
    defaultUserPropertyMap.addAll(_getDefaultDimension());
  }

  Map<String, String> _getDefaultDimension() {
    return {
      TrackingProperties.propertyClientId: '',
      TrackingProperties.propertyUserId: '',
      TrackingProperties.propertyJourneyType: 'CAPP',
      TrackingProperties.propertyPropertyType: 'app_capp',
    };
  }

  @override
  Future<void> trackEvent({
    required String eventCategory,
    required String eventAction,
    required String eventLabel,
    Map<String, String>? customDimensions,
    KoyalEvent? event,
  }) async {
    final mergedUserPropertyMap = <String, String>{}..addAll(_getDefaultDimension());
    if (customDimensions != null) {
      mergedUserPropertyMap.addAll(customDimensions);
    }

    await super.trackEvent(
      eventCategory: eventCategory,
      eventAction: eventAction,
      eventLabel: eventLabel,
      customDimensions: mergedUserPropertyMap,
      event: event,
    );
  }

  void trackOwnContractSelectionCelContractClick() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnContractSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.celContract,
      event: KoyalEvent.repaymentOwnContractSelectionClickCelContract,
    );
  }

  void trackOwnContractSelectionRelContractClick() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnContractSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.relContract,
      event: KoyalEvent.repaymentOwnContractSelectionClickRelContract,
    );
  }

  void trackOwnContractSelectionContinueClick() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnContractSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnContractSelectionClickContinue,
    );
  }

  void trackOwnMethodSelectionClickOnline() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.online,
      event: KoyalEvent.repaymentOwnMethodSelectionClickOnline,
    );
  }

  void trackOwnMethodSelectionClickPayByCash() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.payByCash,
      event: KoyalEvent.repaymentOwnMethodSelectionClickPayByCash,
    );
  }

  void trackOwnMethodSelectionClickBankTransfer() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bankTransfer,
      event: KoyalEvent.repaymentOwnMethodSelectionClickBankTransfer,
    );
  }

  void trackOwnMethodSelectionClickEWallet() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.eWallet,
      event: KoyalEvent.repaymentOwnMethodSelectionClickEWallet,
    );
  }

  void trackOwnMethodSelectionClickInternetBanking() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.internetBanking,
      event: KoyalEvent.repaymentOwnMethodSelectionClickInternetBanking,
    );
  }

  void trackOwnMethodSelectionClickAtmCard() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.atm,
      event: KoyalEvent.repaymentOwnMethodSelectionClickAtm,
    );
  }

  void trackOwnCelAmountClickFullDueAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.fullDueAmount,
      event: KoyalEvent.repaymentOwnCelAmountClickFullDueAmount,
    );
  }

  void trackOwnCelAmountEnterCustomAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnCelAmount,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.customAmount,
      event: KoyalEvent.repaymentOwnCelAmountEnterCustomAmount,
    );
  }

  void trackOwnCelAmountClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnCelAmountClickContinue,
    );
  }

  void trackOwnCelAmountViewWarningPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnCelAmount,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.warningPopup,
      event: KoyalEvent.repaymentOwnCelAmountViewWarningPopup,
    );
  }

  void trackOwnCelAmountClickWarningPopupEditAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupEditAmount,
      event: KoyalEvent.repaymentOwnCelAmountClickWarningPopupEditAmount,
    );
  }

  void trackOwnCelAmountClickWarningPopupContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupContinue,
      event: KoyalEvent.repaymentOwnCelAmountClickWarningPopupContinue,
    );
  }

  void trackOwnOnlinePaymentOptionClickOnePay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.onepay,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickOnePay,
    );
  }

  void trackOwnOnlinePaymentOptionClickMomo() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.momo,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickMomo,
    );
  }

  void trackOwnOnlinePaymentOptionClickViettelPay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.viettelpay,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickViettelPay,
    );
  }

  void trackOwnOnlinePaymentOptionClickShopeePay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.shopeePay,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickShopeePay,
    );
  }

  void trackOwnOnlinePaymentOptionClickZaloPay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.zaloPay,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickZaloPay,
    );
  }

  void trackOwnOnlinePaymentOptionClickVnPay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.vnPay,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickVnPay,
    );
  }

  void trackOwnOnlinePaymentOptionClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickContinue,
    );
  }

  void trackOwnBankTransferClickCopyAccountNumber() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.copyAccountNumber,
      event: KoyalEvent.repaymentOwnBankTransferClickCopyAccountNumber,
    );
  }

  void trackOwnBankTransferClickCopyTransferContent() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.copyTransferContent,
      event: KoyalEvent.repaymentOwnBankTransferClickCopyTransferContent,
    );
  }

  void trackOwnOnlinePaymentSummaryClickCancel() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.cancel,
      event: KoyalEvent.repaymentOwnOnlinePaymentSummaryClickCancel,
    );
  }

  void trackOwnOnlinePaymentSummaryClickConfirm() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.confirm,
      event: KoyalEvent.repaymentOwnOnlinePaymentSummaryClickConfirm,
    );
  }

  void trackOwnTransactionErrorClickBackToHome() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionError,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.backToHomepage,
      event: KoyalEvent.repaymentOwnTransactionErrorClickBackToHome,
    );
  }

  void trackOwnTransactionErrorView({required String errorType}) {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnTransactionError);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnTransactionError,
      event: KoyalEvent.repaymentOwnTransactionErrorView,
      customDimensions: {TrackingProperties.propertyCdErrorType: errorType},
    );
  }

  void trackOwnTransactionErrorClickTryAgain() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionError,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.tryAgain,
      event: KoyalEvent.repaymentOwnTransactionErrorClickTryAgain,
    );
  }

  void trackOwnTransactionSuccessClickCopyTransactionNo() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.copyTransactionNo,
      event: KoyalEvent.repaymentOwnTransactionSuccessClickCopyTransactionNo,
    );
  }

  void trackOwnTransactionSuccessClickDone() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.done,
      event: KoyalEvent.repaymentOwnTransactionSuccessClickDone,
    );
  }

  void trackOwnTransactionSuccessClickBackToHome() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.backToHomepage,
      event: KoyalEvent.repaymentOwnTransactionSuccessClickDone,
    );
  }

  void trackOwnRelAmountClickMinimumAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.minimumAmount,
      event: KoyalEvent.repaymentOwnRelAmountClickMinimumAmount,
    );
  }

  void trackOwnRelAmountClickFullDueAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.fullDueAmount,
      event: KoyalEvent.repaymentOwnRelAmountClickFullDueAmount,
    );
  }

  void trackOwnRelAmountClickOutstandingBalance() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.outstandingBalance,
      event: KoyalEvent.repaymentOwnRelAmountClickOutstandingBalance,
    );
  }

  void trackOwnRelAmountEnterCustomAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.customAmount,
      event: KoyalEvent.repaymentOwnRelAmountEnterCustomAmount,
    );
  }

  void trackOwnRelAmountClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnRelAmountClickContinue,
    );
  }

  void trackOwnRelAmountViewWarningPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.warningPopup,
      event: KoyalEvent.repaymentOwnRelAmountViewWarningPopup,
    );
  }

  void trackOwnRelAmountClickWarningPopupEditAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupEditAmount,
      event: KoyalEvent.repaymentOwnRelAmountClickWarningPopupEditAmount,
    );
  }

  void trackOwnRelAmountClickWarningPopupContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupContinue,
      event: KoyalEvent.repaymentOwnRelAmountClickWarningPopupContinue,
    );
  }

  void trackOwnOnlinePaymentOptionClickAddCard() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.addCard,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickAddCard,
    );
  }

  void trackOwnOnlinePaymentOptionClickMasterCard() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.masterCard,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickMasterCard,
    );
  }

  void trackOwnOnlinePaymentOptionClickVisaCard() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.visaCard,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickVisaCard,
    );
  }

  void trackOwnOnlineCardVerificationEnterAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlineCardVerification,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.amount,
      event: KoyalEvent.repaymentOwnOnlineCardVerificationEnterAmount,
    );
  }

  void trackOwnOnlineCardVerificationClickVerify() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlineCardVerification,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.verify,
      event: KoyalEvent.repaymentOwnOnlineCardVerificationClickVerify,
    );
  }

  void trackOwnMethodSelectionClickAutoDebit() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.autoDebit,
      event: KoyalEvent.repaymentOwnMethodSelectionClickAutoDebit,
    );
  }

  void trackOwnMethodSelectionClickPayOverCounter() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.payOverCounter,
      event: KoyalEvent.repaymentOwnMethodSelectionClickPayOverCounter,
    );
  }

  void trackOwnBankTransferInstructionClickAtmGuidance() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransferInstruction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.atmGuidance,
      event: KoyalEvent.repaymentOwnBankTransferInstructionClickAtmGuidance,
    );
  }

  void trackOwnBankTransferInstructionClickCopyAccountNumber() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransferInstruction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.copyAccountNumber,
      event: KoyalEvent.repaymentOwnBankTransferInstructionClickCopyAccountNumber,
    );
  }

  void trackOwnBankTransferInstructionClickMobileBankingGuidance() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransferInstruction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.mobileBankingGuidance,
      event: KoyalEvent.repaymentOwnBankTransferInstructionClickMobileBankingGuidance,
    );
  }

  void trackOwnEcomEmoneyClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickContinue,
    );
  }

  void trackOwnEcomEmoneyClickEcomBliBli() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.ecomBlibli,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEcomBliBli,
    );
  }

  void trackOwnEcomEmoneyClickEcomBukalapak() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.ecomBukalapak,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEcomBukalapak,
    );
  }

  void trackOwnEcomEmoneyClickEcomLazada() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.ecomLazada,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEcomLazada,
    );
  }

  void trackOwnEcomEmoneyClickEcomQris() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.ecomQris,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEcomQris,
    );
  }

  void trackOwnEcomEmoneyClickEcomTakopedia() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.ecomTakopedia,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEcomTakopedia,
    );
  }

  void trackOwnEcomEmoneyClickEmoneyAyopop() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.emoneyAyopop,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEmoneyAyopop,
    );
  }

  void trackOwnEcomEmoneyClickEmoneyBebasBayar() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.emoneyBebasbayar,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEmoneyBebasBayar,
    );
  }

  void trackOwnEcomEmoneyClickEmoneyGotagihan() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.emoneyGotagihan,
      event: KoyalEvent.repaymentOwnEcomEmoneyClickEmoneyGotaGihan,
    );
  }

  void trackOwnVirtualAccountBankTransferClickAtmPrimaAlto() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.atmPrimaAlto,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickAtmBersamaPrimaAlto,
    );
  }

  void trackOwnVirtualAccountBankTransferClickAtmBersama() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.atmBersama,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickAtmBersamaPrimaAlto,
    );
  }

  void trackOwnVirtualAccountBankTransferClickBankBca() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bankBca,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickBankBca,
    );
  }

  void trackOwnVirtualAccountBankTransferClickBankBni() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bankBni,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickBankBni,
    );
  }

  void trackOwnVirtualAccountBankTransferClickBankBptn() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bankBtpn,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickBankBptn,
    );
  }

  void trackOwnVirtualAccountBankTransferClickBankBri() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bankBri,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickBankBri,
    );
  }

  void trackOwnVirtualAccountBankTransferClickBankMandiri() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bankMandiri,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickBankMandiri,
    );
  }

  void trackOwnVirtualAccountBankTransferClickBankPermata() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bankPermata,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickBankPermata,
    );
  }

  void trackOwnVirtualAccountBankTransferClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferClickContinue,
    );
  }

  void trackOwnInAppMethodSelectionClickDebitCard() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnInAppMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.debitCard,
      event: KoyalEvent.repaymentOwnInAppMethodSelectionClickDebitCard,
    );
  }

  void trackOwnInAppMethodSelectionClickBcaKlikpay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnInAppMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.bcaKlikpay,
      event: KoyalEvent.repaymentOwnInAppMethodSelectionClickBcaKlikpay,
    );
  }

  void trackOwnInAppMethodSelectionClickBriEpay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnInAppMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.briEpay,
      event: KoyalEvent.repaymentOwnInAppMethodSelectionClickBriEpay,
    );
  }

  void trackOwnInAppMethodSelectionClickCimbOctoclick() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnInAppMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.cimbOctoclick,
      event: KoyalEvent.repaymentOwnInAppMethodSelectionClickCimbOctoclick,
    );
  }

  void trackOwnInAppMethodSelectionClickDanamonInternetBanking() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnInAppMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.danamonInternetBanking,
      event: KoyalEvent.repaymentOwnInAppMethodSelectionClickDanamonInternetBanking,
    );
  }

  void trackOwnInAppMethodSelectionClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnInAppMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnInAppMethodSelectionClickContinue,
    );
  }

  void trackOwnAdaIntroductionClickSetUpNow() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaIntroduction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.setUpNow,
      event: KoyalEvent.repaymentOwnAdaIntroductionClickSetUpNow,
    );
  }

  void trackOwnAdaPaymentOptionClickAddCard() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaPaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.addCard,
      event: KoyalEvent.repaymentOwnAdaPaymentOptionClickAddCard,
    );
  }

  void trackOwnAdaTermsAndConditionsClickTermsAndConditions() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaTermsAndConditions,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.termsAndConditions,
      event: KoyalEvent.repaymentOwnAdaTermsAndConditionsClickTermsAndConditions,
    );
  }

  void trackOwnAdaTermsAndConditionsClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaTermsAndConditions,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnAdaTermsAndConditionsClickContinue,
    );
  }

  void trackOwnAdaTermsAndConditionsDetailClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaTermsAndConditionsDetail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnAdaTermsAndConditionsDetailClickContinue,
    );
  }

  void trackOwnAdaConfirmationClickConfirmAndEnroll() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaConfirmation,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.confirmAndEnroll,
      event: KoyalEvent.repaymentOwnAdaConfirmationClickConfirmAndEnroll,
    );
  }

  void trackOwnAdaConfirmationClickCancelEnroll() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaConfirmation,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.cancelEnrollment,
      event: KoyalEvent.repaymentOwnAdaConfirmationClickCancelEnroll,
    );
  }

  void trackOwnAdaSuccessClickBackToHomePage() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAdaSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.backToHomepage,
      event: KoyalEvent.repaymentOwnAdaSuccessClickBackToHomePage,
    );
  }

  void trackOwnOtcPartnerSelectionClickPartner(String id) {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: '${CappRepaymentTrackingLabels.otcPartnerId}$id',
      event: KoyalEvent.repaymentOwnOtcPartnerSelectionClickPartner,
    );
  }

  void trackOwnOtcPartnerSelectionClickPartnerInfo(String id) {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: '${CappRepaymentTrackingLabels.otcPartnerInfoId}$id',
      event: KoyalEvent.repaymentOwnOtcPartnerSelectionClickPartnerInfo,
    );
  }

  void trackOwnOtcPartnerSelectionClickBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentOwnOtcPartnerSelectionClickBack,
    );
  }

  void trackOwnOtcPartnerInstructionClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerInstruction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnOtcPartnerInstructionClickContinue,
    );
  }

  void trackOwnOtcPartnerInstructionClickFindNearestStore() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerInstruction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.findNearestStore,
      event: KoyalEvent.repaymentOwnOtcPartnerInstructionClickFindNearestStore,
    );
  }

  void trackOwnOtcPartnerInstructionClickBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerInstruction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentOwnOtcPartnerInstructionClickBack,
    );
  }

  void trackOwnOtcPartnerSummaryClickBackToHome() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.backToHomepage,
      event: KoyalEvent.repaymentOwnOtcPartnerSummaryClickBackToHome,
    );
  }

  void trackOwnOtcPartnerSummaryClickFindNearestStore() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.findNearestStore,
      event: KoyalEvent.repaymentOwnOtcPartnerSummaryClickFindNearestStore,
    );
  }

  void trackOwnOtcPartnerSummaryClickSaveScreenShot() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.saveScreenShot,
      event: KoyalEvent.repaymentOwnOtcPartnerSummaryClickSaveScreenShot,
    );
  }

  void trackOwnOtcPartnerSummaryClickBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentOwnOtcPartnerSummaryClickBack,
    );
  }

  void trackOwnOtcCelAmountScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOtcCelAmount);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.repaymentForOwnOtcCelAmount,
      event: KoyalEvent.repaymentOwnOtcCelAmountScreenView,
    );
  }

  void trackOwnOtcCelAmountClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnOtcCelAmountClickContinue,
    );
  }

  void trackOwnOtcCelAmountEnterCustomAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcCelAmount,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.customAmount,
      event: KoyalEvent.repaymentOwnOtcCelAmountEnterCustomAmount,
    );
  }

  void trackOwnOtcCelAmountClickFullDueAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.fullDueAmount,
      event: KoyalEvent.repaymentOwnOtcCelAmountClickFullDueAmount,
    );
  }

  void trackOwnOtcCelAmountViewWarningPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcCelAmount,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.warningPopup,
      event: KoyalEvent.repaymentOwnOtcCelAmountViewWarningPopup,
    );
  }

  void trackOwnOtcCelAmountClickWarningPopupEditAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupEditAmount,
      event: KoyalEvent.repaymentOwnOtcCelAmountClickWarningPopupEditAmount,
    );
  }

  void trackOwnOtcCelAmountClickWarningPopupContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcCelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupContinue,
      event: KoyalEvent.repaymentOwnOtcCelAmountClickWarningPopupContinue,
    );
  }

  void trackOwnOtcRelAmountScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.repaymentForOwnOtcRelAmount,
      event: KoyalEvent.repaymentOwnOtcRelAmountScreenView,
    );
  }

  void trackOwnOtcRelAmountClickMinimumAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.minimumAmount,
      event: KoyalEvent.repaymentOwnOtcRelAmountClickMinimumAmount,
    );
  }

  void trackOwnOtcRelAmountClickFullDueAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.fullDueAmount,
      event: KoyalEvent.repaymentOwnOtcRelAmountClickFullDueAmount,
    );
  }

  void trackOwnOtcRelAmountEnterCustomAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.customAmount,
      event: KoyalEvent.repaymentOwnOtcRelAmountEnterCustomAmount,
    );
  }

  void trackOwnOtcRelAmountClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnOtcRelAmountClickContinue,
    );
  }

  void trackOwnOtcRelAmountViewWarningPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.warningPopup,
      event: KoyalEvent.repaymentOwnOtcRelAmountViewWarningPopup,
    );
  }

  void trackOwnOtcRelAmountClickWarningPopupEditAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupEditAmount,
      event: KoyalEvent.repaymentOwnOtcRelAmountClickWarningPopupEditAmount,
    );
  }

  void trackOwnOtcRelAmountClickWarningPopupContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.warningPopupContinue,
      event: KoyalEvent.repaymentOwnOtcRelAmountClickWarningPopupContinue,
    );
  }

  void trackOwnOtcRelAmountClickOutstandingBalance() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOtcRelAmount,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.outstandingBalance,
      event: KoyalEvent.repaymentOwnOtcRelAmountClickOutstandingBalance,
    );
  }

  void trackOwnOnlinePaymentSummaryPopupWarningClickYes() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popupWarningYes,
      event: KoyalEvent.repaymentOwnOnlinePaymentSummaryPopupWarningClickYes,
    );
  }

  void trackOwnOnlinePaymentSummaryPopupWarningClickNo() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentSummary,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popupWarningNo,
      event: KoyalEvent.repaymentOwnOnlinePaymentSummaryPopupWarningClickNo,
    );
  }

  void trackOwnBankTransferClickDownload() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.download,
      event: KoyalEvent.repaymentOwnBankTransferClickDownload,
    );
  }

  void trackOwnBankTransferClickShare() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.share,
      event: KoyalEvent.repaymentOwnBankTransferClickShare,
    );
  }

  void trackOwnAtmCardProcessingClickBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnAtmCardProcessing,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentOwnAtmCardProcessingClickBack,
    );
  }

  void trackOwnMainClickPaymentMethodChange() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodChange,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodChange,
    );
  }

  void trackOwnMainClickPaymentMethodMomo() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodMomo,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodMomo,
    );
  }

  void trackOwnMainClickPaymentMethodOnePay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodOnePay,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodOnePay,
    );
  }

  void trackOwnMainClickPaymentMethodShopeePay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodShopeePay,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodShopeePay,
    );
  }

  void trackOwnMainClickPaymentMethodViettelMoney() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodViettelMoney,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodViettelMoney,
    );
  }

  void trackOwnMainClickPaymentMethodVnPay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodVnPay,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodVnPay,
    );
  }

  void trackOwnMainClickPaymentMethodZaloPay() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodZaloPay,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodZaloPay,
    );
  }

  void trackOwnMainClickPaymentMethodBankTransferMobileBanking() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodBankTransferMobileBanking,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodBankTransferMobileBanking,
    );
  }

  void trackOwnMainClickPaymentMethodBankTransferVirtualAccount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodBankTransferVirtualAccount,
      event: KoyalEvent.repaymentOwnMainClickPaymentMethodBankTransferVirtualAccount,
    );
  }

  void trackOwnMainClickProceed() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.proceed,
      event: KoyalEvent.repaymentOwnMainClickProceed,
    );
  }

  void trackOwnMainEnterPaymentAmountCelCustom() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountCelCustom,
      event: KoyalEvent.repaymentOwnMainEnterCelCustomAmount,
    );
  }

  void trackOwnMainClickPaymentAmountCelCustom() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountCelCustom,
      event: KoyalEvent.repaymentOwnMainClickCelCustomAmount,
    );
  }

  void trackOwnMainClickPaymentAmountCelDue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountCelDue,
      event: KoyalEvent.repaymentOwnMainClickCelDueAmount,
    );
  }

  void trackOwnMainViewPaymentAmountCelPopupWarning() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountCelPopupWarning,
      event: KoyalEvent.repaymentOwnMainViewCelPopupWarning,
    );
  }

  void trackOwnMainClickPaymentAmountCelPopupWarningContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountCelPopupWarningContinue,
      event: KoyalEvent.repaymentOwnMainClickCelPopupWarningContinue,
    );
  }

  void trackOwnMainClickPaymentAmountCelPopupWarningEdit() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountCelPopupWarningEdit,
      event: KoyalEvent.repaymentOwnMainClickCelPopupWarningEdit,
    );
  }

  void trackOwnMainEnterPaymentAmountRelCustom() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountRelCustom,
      event: KoyalEvent.repaymentOwnMainEnterRelCustomAmount,
    );
  }

  void trackOwnMainClickPaymentAmountRelCustom() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountRelCustom,
      event: KoyalEvent.repaymentOwnMainClickRelCustomAmount,
    );
  }

  void trackOwnMainClickPaymentAmountRelTotalDue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountRelTotalAmount,
      event: KoyalEvent.repaymentOwnMainClickRelTotalDueAmount,
    );
  }

  void trackOwnMainClickPaymentAmountRelMinimumDue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountRelMinimumAmount,
      event: KoyalEvent.repaymentOwnMainClickRelMinimumDueAmount,
    );
  }

  void trackOwnMainViewPaymentAmountRelPopupWarning() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountRelPopupWarning,
      event: KoyalEvent.repaymentOwnMainViewRelPopupWarning,
    );
  }

  void trackOwnMainClickPaymentAmountRelPopupWarningContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountRelPopupWarningContinue,
      event: KoyalEvent.repaymentOwnMainClickRelPopupWarningContinue,
    );
  }

  void trackOwnMainClickPaymentAmountRelPopupWarningEdit() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentAmountRelPopupWarningEdit,
      event: KoyalEvent.repaymentOwnMainClickRelPopupWarningEdit,
    );
  }

  void trackOwnMainViewUnavailablePaymentMethodPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.popUpUnavailablePaymentMethod,
      event: KoyalEvent.repaymentOwnMainViewUnavailablePaymentMethodPopup,
    );
  }

  void trackOwnMainClickUnavailablePaymentMethodPopupOk() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popUpUnavailablePaymentMethodOk,
      event: KoyalEvent.repaymentOwnMainClickUnavailablePaymentMethodPopupOk,
    );
  }

  void trackOwnMainClickMyLoanRelSelectContract() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.myLoanRelSelectContract,
      event: KoyalEvent.repaymentOwnMainClickMyLoanRel,
    );
  }

  void trackOwnMainClickMyLoanCelSelectContract() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.myLoanCelSelectContract,
      event: KoyalEvent.repaymentOwnMainClickMyLoanCel,
    );
  }

  void trackOwnMainClickMyLoanChange() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.myLoanChange,
      event: KoyalEvent.repaymentOwnMainClickMyLoanChange,
    );
  }

  void trackOwnMainViewContractUpdatedPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.popUpContractUpdated,
      event: KoyalEvent.repaymentOwnMainViewContractUpdatedPopup,
    );
  }

  void trackOwnMainClickContractUpdatedPopupBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popUpContractUpdatedBack,
      event: KoyalEvent.repaymentOwnMainClickContractUpdatedPopupBack,
    );
  }

  void trackOwnMainClickContractUpdatedPopupOk() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popUpContractUpdatedOk,
      event: KoyalEvent.repaymentOwnMainClickContractUpdatedPopupOk,
    );
  }

  void trackOwnMainViewScreen() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnMain);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.repaymentForOwnMain,
      event: KoyalEvent.repaymentOwnMainScreen,
    );
  }

  void trackOwnTransactionSuccessViewScreen() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.repaymentForOwnTransactionSuccess,
      event: KoyalEvent.repaymentOwnTransactionSuccessView,
    );
  }

  void trackPromiseToPayIntroductionClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayIntroduction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentPromiseToPayIntroductionClickContinue,
    );
  }

  void trackPromiseToPayIntroductionClickUnderstand() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayIntroduction,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.understand,
      event: KoyalEvent.repaymentPromiseToPayIntroductionClickUnderstand,
    );
  }

  void trackPromiseToPayOptionsClickPromise() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayOptions,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.promise,
      event: KoyalEvent.repaymentPromiseToPayOptionsClickPromise,
    );
  }

  void trackPromiseToPayOptionsClickRepayNow() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayOptions,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repayNow,
      event: KoyalEvent.repaymentPromiseToPayOptionsClickRepayNow,
    );
  }

  void trackPromiseToPayOptionsClickQuestion() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayOptions,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.question,
      event: KoyalEvent.repaymentPromiseToPayOptionsClickQuestion,
    );
  }

  void trackPromiseToPayPlanClickAfterNumberOfDay(String numberOfDay) {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.afterNumberDay.replaceAll('number', numberOfDay),
      event: KoyalEvent.repaymentPromiseToPayPlanClickAfterNumberOfDay,
    );
  }

  void trackPromiseToPayPlanClickDueAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.dueAmount,
      event: KoyalEvent.repaymentPromiseToPayPlanClickDueAmount,
    );
  }

  void trackPromiseToPayPlanClickCustomAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.customAmount,
      event: KoyalEvent.repaymentPromiseToPayPlanClickCustomAmount,
    );
  }

  void trackPromiseToPayPlanEnterCustomAmount() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappRepaymentTrackingLabels.customAmount,
      event: KoyalEvent.repaymentPromiseToPayPlanEnterCustomAmount,
    );
  }

  void trackPromiseToPayPlanClickSubmit() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.submit,
      event: KoyalEvent.repaymentPromiseToPayPlanClickSubmit,
    );
  }

  void trackPromiseToPayPlanViewPopUpConfirmation() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.ptpPopUpConfirmation,
      event: KoyalEvent.repaymentPromiseToPayPlanViewPopupConfirmation,
    );
  }

  void trackPromiseToPayPlanClickPopUpConfirmationAgree() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.ptpPopUpConfirmationAgree,
      event: KoyalEvent.repaymentPromiseToPayPlanClickPopupConfirmationAgree,
    );
  }

  void trackPromiseToPayPlanClickPopUpConfirmationCancel() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.ptpPopUpConfirmationCancel,
      event: KoyalEvent.repaymentPromiseToPayPlanClickPopupConfirmationCancel,
    );
  }

  void trackPromiseToPaySuccessClickDone() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPaySuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.done,
      event: KoyalEvent.repaymentPromiseToPaySuccessClickDone,
    );
  }

  void trackPromiseToPayProcessingClickClose() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayProcessing,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.close,
      event: KoyalEvent.repaymentPromiseToPayProcessingClickClose,
    );
  }

  void trackDirectDiscountClickOpenVoucherList() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.viewVoucherList,
      event: KoyalEvent.repaymentDirectDiscountClickOpenVoucherList,
    );
  }

  void trackDirectDiscountViewVoucherListPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.viewVoucherListPopup,
      event: KoyalEvent.repaymentDirectDiscountViewVoucherListPopup,
    );
  }

  void trackDirectDiscountClickDetailValidVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.seeDetailValidVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickSeeDetailValidVoucher,
    );
  }

  void trackDirectDiscountClickDetailInvalidVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.seeDetailInvalidVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickSeeDetailInvalidVoucher,
    );
  }

  void trackDirectDiscountClickValidVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.selectValidVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickSelectValidVoucher,
    );
  }

  void trackDirectDiscountClickInvalidVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.selectInvalidVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickSelectInvalidVoucher,
    );
  }

  void trackDirectDiscountClickApplyVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.applyVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickApplyVoucher,
    );
  }

  void trackDirectDiscountViewNoVoucherPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.noVoucherPopup,
      event: KoyalEvent.repaymentDirectDiscountViewNoVoucherPopup,
    );
  }

  void trackDirectDiscountClickGoBackNoVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.noVoucherGoBack,
      event: KoyalEvent.repaymentDirectDiscountClickNoVoucherBack,
    );
  }

  void trackDirectDiscountViewErrorFetchVoucherPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.techErrorVoucherPopup,
      event: KoyalEvent.repaymentDirectDiscountViewTechErrorVoucher,
    );
  }

  void trackDirectDiscountClickErrorFetchVoucherRefresh() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.techErrorRefresh,
      event: KoyalEvent.repaymentDirectDiscountClickTechErrorVoucherRefresh,
    );
  }

  void trackDirectDiscountDetailScreenView({required bool isApplied}) {
    final category = isApplied
        ? CappRepaymentTrackingCategories.repaymentDiscountSelectedVoucherDetail
        : CappRepaymentTrackingCategories.repaymentDiscountUnselectedVoucherDetail;
    trackEvent(
      eventCategory: category,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: category,
      event: isApplied
          ? KoyalEvent.repaymentDirectDiscountSelectVoucherDetailScreenView
          : KoyalEvent.repaymentDirectDiscountUnselectVoucherDetailScreenView,
    );
  }

  void trackDirectDiscountDetailClickApplyVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentDiscountUnselectedVoucherDetail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.applyVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickVoucherDetailApply,
    );
  }

  void trackDirectDiscountDetailClickApplyVoucherLater() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentDiscountSelectedVoucherDetail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.applyVoucherLater,
      event: KoyalEvent.repaymentDirectDiscountClickVoucherDetailApplyLater,
    );
  }

  void trackDirectDiscountDetailClickBack({required bool isApplied}) {
    final category = isApplied
        ? CappRepaymentTrackingCategories.repaymentDiscountSelectedVoucherDetail
        : CappRepaymentTrackingCategories.repaymentDiscountUnselectedVoucherDetail;

    trackEvent(
      eventCategory: category,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentDirectDiscountClickVoucherDetailBack,
    );
  }

  void trackDirectDiscountClickChangeVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.changeVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickChangeVoucher,
    );
  }

  void trackDirectDiscountClickDeleteVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.deleteVoucher,
      event: KoyalEvent.repaymentDirectDiscountClickDeleteVoucher,
    );
  }

  void trackDirectDiscountViewUnexpectedInvalidVoucherPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.unexpectedInvalidVoucherPopup,
      event: KoyalEvent.repaymentDirectDiscountViewUnexpectedInvalidVoucher,
    );
  }

  void trackDirectDiscountClickUnexpectedInvalidVoucherContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.unexpectedInvalidVoucherContinue,
      event: KoyalEvent.repaymentDirectDiscountClickUnexpectedInvalidVoucherContinue,
    );
  }

  void trackDirectDiscountClickUnexpectedInvalidVoucherSeeOther() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.unexpectedInvalidVoucherSeeOther,
      event: KoyalEvent.repaymentDirectDiscountClickUnexpectedInvalidVoucherSeeOther,
    );
  }

  void trackDirectDiscountViewExpectedInvalidVoucherPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.expectedInvalidVoucherPopup,
      event: KoyalEvent.repaymentDirectDiscountViewExpectedInvalidVoucher,
    );
  }

  void trackDirectDiscountClickExpectedInvalidVoucherContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.expectedInvalidVoucherContinue,
      event: KoyalEvent.repaymentDirectDiscountClickExpectedInvalidVoucherContinue,
    );
  }

  void trackDirectDiscountClickExpectedInvalidVoucherSeeOther() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.expectedInvalidVoucherSeeOther,
      event: KoyalEvent.repaymentDirectDiscountClickExpectedInvalidVoucherSeeOther,
    );
  }

  void trackDirectDiscountBankTransferClickCopyVaTransactionId() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.copyVaTransId,
      event: KoyalEvent.repaymentOwnBankTransferClickCopyVaTransactionId,
    );
  }

  void trackDirectDiscountBankTransferClickBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentOwnBankTransferClickBack,
    );
  }

  void trackDirectDiscountBankTransferViewCancelVoucherPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.popUpVaBack,
      event: KoyalEvent.repaymentOwnBankTransferViewCancelVoucherPopup,
    );
  }

  void trackDirectDiscountBankTransferClickCancelVoucher() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popUpCancelVoucher,
      event: KoyalEvent.repaymentOwnBankTransferClickCancelVoucher,
    );
  }

  void trackDirectDiscountBankTransferClickDownloadQr() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popUpDownQr,
      event: KoyalEvent.repaymentOwnBankTransferClickDownloadQr,
    );
  }

  void trackOwnContractSelectionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnContractSelection);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnContractSelection,
      event: KoyalEvent.repaymentOwnContractSelectionScreenView,
    );
  }

  void trackOwnNoLoanScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnNoLoan);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnNoLoan,
      event: KoyalEvent.repaymentOwnContractNoLoanScreenView,
    );
  }

  void trackOwnMethodSelectionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnMethodSelection);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      event: KoyalEvent.repaymentOwnMethodSelectionScreenView,
    );
  }

  void trackOwnCelAmountScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnCelAmount);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnCelAmount,
      event: KoyalEvent.repaymentOwnCelAmountScreenView,
    );
  }

  void trackOwnOnlinePaymentOptionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionScreenView,
    );
  }

  void trackOwnBankTransferScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnBankTransfer);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      event: KoyalEvent.repaymentOwnBankTransferScreenView,
    );
  }

  void trackOwnOnlinePaymentSummaryScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentSummary);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentSummary,
      event: KoyalEvent.repaymentOwnOnlinePaymentSummaryScreenView,
    );
  }

  void trackOwnTransactionErrorScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnTransactionError);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnTransactionError,
      event: KoyalEvent.repaymentOwnTransactionErrorScreenView,
    );
  }

  void trackOwnRelAmountScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnRelAmount);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnRelAmount,
      event: KoyalEvent.repaymentOwnRelAmountScreenView,
    );
  }

  void trackOwnVirtualAccountBankTransferScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnVirtualAccountBankTransfer,
      event: KoyalEvent.repaymentOwnVirtualAccountBanktransferScreenView,
    );
  }

  void trackOwnEcomEmoneyScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnEcomEmoney,
      event: KoyalEvent.repaymentOwnEcomEmoneyScreenView,
    );
  }

  void trackOwnOnlineCardVerificationScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOnlineCardVerification);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnOnlineCardVerification,
      event: KoyalEvent.repaymentOwnOnlineCardVerificationScreenView,
    );
  }

  void trackOwnBankTransferInstructionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnBankTransferInstruction);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnBankTransferInstruction,
      event: KoyalEvent.repaymentOwnBankTransferInstructionScreenView,
    );
  }

  void trackOwnAdaIntroductionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnAdaIntroduction);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnAdaIntroduction,
      event: KoyalEvent.repaymentOwnAdaIntroductionScreenView,
    );
  }

  void trackOwnAdaPaymentOptionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnAdaPaymentOption);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnAdaPaymentOption,
      event: KoyalEvent.repaymentOwnAdaPaymentOptionScreenView,
    );
  }

  void trackOwnAdaTermsAndConditionsScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnAdaTermsAndConditions);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnAdaTermsAndConditions,
      event: KoyalEvent.repaymentOwnAdaTermsAndConditionsScreenView,
    );
  }

  void trackOwnAdaConfirmationScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnAdaConfirmation);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnAdaConfirmation,
      event: KoyalEvent.repaymentOwnAdaConfirmationScreenView,
    );
  }

  void trackOwnAdaSuccessScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnAdaSuccess);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnAdaSuccess,
      event: KoyalEvent.repaymentOwnAdaSuccessScreenView,
    );
  }

  void trackOwnAdaTermsAndConditionsDetailScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnAdaTermsAndConditionsDetail);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnAdaTermsAndConditionsDetail,
      event: KoyalEvent.repaymentOwnAdaTermsAndConditionsDetailScreenView,
    );
  }

  void trackOwnOtcPartnerSelectionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSelection);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSelection,
      event: KoyalEvent.repaymentOwnOtcPartnerSelectionScreenView,
    );
  }

  void trackOwnOtcPartnerInstructionScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerInstruction);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerInstruction,
      event: KoyalEvent.repaymentOwnOtcPartnerInstructionScreenView,
    );
  }

  void trackOwnOtcPartnerSummaryScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSummary);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnOtcPartnerSummary,
      event: KoyalEvent.repaymentOwnOtcPartnerSummaryScreenView,
    );
  }

  void trackMobileBankingAppScreenView() {
    _updateRouteScreen(CappRepaymentTrackingLabels.mobileBankingApps);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.mobileBankingApps,
      event: KoyalEvent.repaymentMobileBankingAppScreenView,
    );
  }

  void trackOnePayScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentForOwnAtmCardProcessing);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentForOwnAtmCardProcessing,
      event: KoyalEvent.repaymentOnePayScreenView,
    );
  }

  void trackPromiseToPayEligibilityCheckingScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentPromiseToPayEligibilityChecking);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentPromiseToPayEligibilityChecking,
      event: KoyalEvent.repaymentPromiseToPayEligibilityCheckingScreenView,
    );
  }

  void trackPromiseToPayEligibilityCheckingPopupError() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayEligibilityChecking,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.popupError,
      event: KoyalEvent.repaymentPromiseToPayEligibilityCheckingPopupError,
    );
  }

  void trackPromiseToPayEligibilityCheckingPopupErrorClickRetry() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayEligibilityChecking,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingCategories.repaymentPromiseToPayEligibilityChecking,
      event: KoyalEvent.repaymentPromiseToPayEligibilityCheckingPopupErrorClickRetry,
    );
  }

  void trackPromiseToPayEligibilityCheckingPopupErrorClickBackToHome() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentPromiseToPayEligibilityChecking,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.backToHomepage,
      event: KoyalEvent.repaymentPromiseToPayEligibilityCheckingPopupErrorClickBackToHome,
    );
  }

  void trackPromiseToPayIntroScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentPromiseToPayIntroduction);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentPromiseToPayIntroduction,
      event: KoyalEvent.repaymentPromiseToPayIntroScreenView,
    );
  }

  void trackPromiseToPayInformationScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentPromiseToPayOptions);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentPromiseToPayOptions,
      event: KoyalEvent.repaymentPromiseToPayInformationScreenView,
    );
  }

  void trackPromiseToPayPaymentPlanScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentPromiseToPayPlan);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentPromiseToPayPlan,
      event: KoyalEvent.repaymentPromiseToPayPaymentPlanScreenView,
    );
  }

  void trackPromiseToPayProcessingScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentPromiseToPayProcessing);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentPromiseToPayProcessing,
      event: KoyalEvent.repaymentPromiseToPayProcessingScreenView,
    );
  }

  void trackPromiseToPaySuccessScreenView() {
    _updateRouteScreen(CappRepaymentTrackingCategories.repaymentPromiseToPaySuccess);
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingCategories.repaymentPromiseToPaySuccess,
      event: KoyalEvent.repaymentPromiseToPaySuccessScreenView,
    );
  }

  void trackOwnBankTransferClickBackToHome() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.backToHomepage,
      event: KoyalEvent.repaymentOwnBankTransferClickBackToHome,
    );
  }

  void trackOwnBankTransferClickExpandInfo() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnBankTransfer,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.infoExpand,
      event: KoyalEvent.repaymentOwnBankTransferClickExpandInfo,
    );
  }

  void trackMainScreenViewDismissPromotionPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.popupDismissPromo,
      event: KoyalEvent.repaymentOwnMainViewDismissPromotionPopup,
    );
  }

  void trackDismissPromotionPopupContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popupDismissPromoContinue,
      event: KoyalEvent.repaymentDismissPromotionPopupContinue,
    );
  }

  void trackDismissPromotionPopupQuit() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.popupDismissPromoQuit,
      event: KoyalEvent.repaymentDismissPromotionPopupQuit,
    );
  }

  void trackOwnMethodSelectionClickEmoneyEcommerce() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.emoneyEcommerce,
      event: KoyalEvent.repaymentOwnMethodSelectionClickEmoneyEcommerce,
    );
  }

  void trackOwnMethodSelectionClickRetail() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMethodSelection,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.retail,
      event: KoyalEvent.repaymentOwnMethodSelectionClickRetail,
    );
  }

  void trackOwnOnlinePaymentOptionClickBankOption(String bank) {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentOption,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: bank,
      event: KoyalEvent.repaymentOwnOnlinePaymentOptionClickBankOption,
    );
  }

  void trackOwnOnlinePaymentIntroClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnOnlinePaymentIntro,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentOwnOnlinePaymentIntroClickContinue,
    );
  }

  void trackPromotionFirstAdoptionVoucherView() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.promotionFirstAdoptionView,
      event: KoyalEvent.repaymentPromotionFirstAdoptionView,
    );
  }

  void trackPromotionFirstAdoptionVoucherClick() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.promotionFirstAdoptionClaim,
      event: KoyalEvent.repaymentPromotionFirstAdoptionClick,
    );
  }

  void trackOwnTransactionSuccessClickExploreAda() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.exploreAda,
      event: KoyalEvent.repaymentAdaTransactionSuccessExploreClick,
    );
  }

  void trackOwnTransactionSuccessViewRemindAdaPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.adaRemindPopupView,
      event: KoyalEvent.repaymentAdaTransactionSuccessReminderPopupView,
    );
  }

  void trackOwnTransactionSuccessClickRemindAdaPopupExplore() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.adaRemindPopupExplore,
      event: KoyalEvent.repaymentAdaTransactionSuccessReminderPopupExploreClick,
    );
  }

  void trackOwnTransactionSuccessClickRemindAdaPopupLater() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentForOwnTransactionSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.adaRemindPopupLater,
      event: KoyalEvent.repaymentAdaTransactionSuccessReminderPopupLaterClick,
    );
  }

  void trackAdaIntroClickContinue() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaIntro,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.repaymentAdaIntroContinueClick,
    );
  }

  void trackAdaMainClickChangePaymentMethod() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodChange,
      event: KoyalEvent.repaymentAdaMainChangeMethodClick,
    );
  }

  void trackAdaMainClickZaloPayMethod() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodZaloPay,
      event: KoyalEvent.repaymentAdaMainZaloPayClick,
    );
  }

  void trackAdaMainClickProceed() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.proceed,
      event: KoyalEvent.repaymentAdaMainProceedClick,
    );
  }

  void trackAdaMainClickTncDetail() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.tncDetail,
      event: KoyalEvent.repaymentAdaMainTncDetailClick,
    );
  }

  void trackAdaManagementClickAddNew() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaManagement,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.addNew,
      event: KoyalEvent.repaymentAdaManagementAddNewClick,
    );
  }

  void trackAdaDetailClickCancel() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaDetail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.cancel,
      event: KoyalEvent.repaymentAdaDetailCancelClick,
    );
  }

  void trackAdaDetailClickTncDetail() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaDetail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.tncDetail,
      event: KoyalEvent.repaymentAdaDetailTncDetailClick,
    );
  }

  void trackAdaDetailViewConfirmPopup() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaDetail,
      eventAction: KoyalAnalyticsConstants.view,
      eventLabel: CappRepaymentTrackingLabels.cancelConfirmPopupView,
      event: KoyalEvent.repaymentAdaDetailCancelPopupView,
    );
  }

  void trackAdaDetailClickConfirmPopupCancel() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaDetail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.cancelConfirmPopupConfirm,
      event: KoyalEvent.repaymentAdaDetailCancelPopupConfirmClick,
    );
  }

  void trackAdaDetailClickConfirmPopupBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaDetail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.cancelConfirmPopupBack,
      event: KoyalEvent.repaymentAdaDetailCancelPopupBackClick,
    );
  }

  void trackAdaSuccessClickDone() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.done,
      event: KoyalEvent.repaymentAdaSuccessDoneClick,
    );
  }

  void trackAdaSuccessClickTncDetail() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaSuccess,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.tncDetail,
      event: KoyalEvent.repaymentAdaSuccessTncDetailClick,
    );
  }

  void trackAdaFailClickTryAgain() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaFail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.tryAgain,
      event: KoyalEvent.repaymentAdaFailTryAgainClick,
    );
  }

  void trackAdaFailClickLater() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaFail,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.later,
      event: KoyalEvent.repaymentAdaFailLaterClick,
    );
  }

  void trackAdaMainClickChangeContract() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.myLoanChange,
      event: KoyalEvent.repaymentAdaMainChangeContractClick,
    );
  }

  void trackAdaMainClickOnepayPayMethod() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaMain,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.paymentMethodOnePay,
      event: KoyalEvent.repaymentAdaMainOnepayClick,
    );
  }

  void trackAdaOnepayRegisterClickBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaOnepayRegister,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentAdaOnepayRegisterBackClick,
    );
  }

  void trackAdaOnepayCancelOtpClickBack() {
    trackEvent(
      eventCategory: CappRepaymentTrackingCategories.repaymentAdaOnepayCancelOtp,
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.repaymentAdaOnepayCancelOtpBackClick,
    );
  }

  void _updateRouteScreen(String currentScreen) {
    if (gtp.currScreen == null || gtp.currScreen != currentScreen) {
      // Update current/previous screen manually
      gtp
        ..prevScreen = gtp.currScreen
        ..currScreen = currentScreen;
    }
  }

  Future<void> trackPaymentSuccessFeedbackClick(String? eventCategory, KoyalEvent? event) async {
    await trackEvent(
      eventCategory: eventCategory ?? '',
      eventAction: KoyalAnalyticsConstants.click,
      eventLabel: CappRepaymentTrackingLabels.feedback,
      event: event,
    );
  }

  // loan origination auto-debit arrangements tracking methods
  void trackLoanOriginationAdaIntroScreenView() {
    _updateRouteScreen(CappRepaymentTrackingLabels.looAutoDebitAgreementIntro);
    trackViewEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventLabel: CappRepaymentTrackingLabels.looAutoDebitAgreementIntro,
      event: KoyalEvent.loanOriginationAdaIntroScreenView,
    );
  }

  void trackLoanOriginationAdaIntroView() {
    trackViewEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreementIntro,
      eventLabel: CappRepaymentTrackingLabels.agrIntro,
      event: KoyalEvent.loanOriginationAdaIntroView,
    );
  }

  void trackLoanOriginationAdaIntroContinueClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreementIntro,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.loanOriginationAdaIntroContinueClick,
    );
  }

  void trackLoanOriginationAdaMainScreenView() {
    _updateRouteScreen(CappRepaymentTrackingLabels.looAutoDebitAgreement);
    trackViewEvent(
      eventCategory: CappRepaymentTrackingCategories.screenView,
      eventLabel: CappRepaymentTrackingLabels.looAutoDebitAgreement,
      event: KoyalEvent.loanOriginationAdaMainScreenView,
    );
  }

  void trackLoanOriginationAdaMainView() {
    trackViewEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreement,
      eventLabel: CappRepaymentTrackingLabels.looAutoDebitAgreement,
      event: KoyalEvent.loanOriginationAdaMainView,
    );
  }

  void trackLoanOriginationAdaChangeMethodClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreement,
      eventLabel: CappRepaymentTrackingLabels.changeMethod,
      event: KoyalEvent.loanOriginationAdaChangeMethodClick,
    );
  }

  void trackLoanOriginationAdaContinueClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreement,
      eventLabel: CappRepaymentTrackingLabels.repaymentContinue,
      event: KoyalEvent.loanOriginationAdaContinueClick,
    );
  }

  void trackLoanOriginationAdaPaymentMethodClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationPaymentMethod,
      eventLabel: CappRepaymentTrackingLabels.method,
      event: KoyalEvent.loanOriginationAdaMethodClick,
    );
  }

  void trackLoanOriginationAdaRemoveClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreement,
      eventLabel: CappRepaymentTrackingLabels.remove,
      event: KoyalEvent.loanOriginationAdaRemoveClick,
    );
  }

  void trackLoanOriginationAdaConfirmationClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreement,
      eventLabel: CappRepaymentTrackingLabels.confirmation,
      event: KoyalEvent.loanOriginationAdaConfirmationClick,
    );
  }

  void trackLoanOriginationAdaCancelClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreementCancel,
      eventLabel: CappRepaymentTrackingLabels.back,
      event: KoyalEvent.loanOriginationAdaCancelClick,
    );
  }

  void trackLoanOriginationAdaErrorAgainClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreementError,
      eventLabel: CappRepaymentTrackingLabels.again,
      event: KoyalEvent.loanOriginationAdaErrorAgainClick,
    );
  }

  void trackLoanOriginationAdaErrorLaterClick() {
    trackClickEvent(
      eventCategory: CappRepaymentTrackingCategories.loanOriginationAutoDebitAgreementError,
      eventLabel: CappRepaymentTrackingLabels.later,
      event: KoyalEvent.loanOriginationAdaErrorLaterClick,
    );
  }
}
