import 'package:selfcareapi/model/models.dart' as api_models;

import '../../../capp_repayment_core.dart';

extension RepaymentContractListResponseMapper on api_models.RepaymentContractListDto {
  RepaymentContractListResponse toDomain() => RepaymentContractListResponse(
        loanContracts: loanContracts?.map((e) => e.toDomain()).toList(),
        relContracts: revolvingContracts?.map((e) => e.toDomain()).toList(),
      );
}

extension RepaymentContractListResponseV2Mapper on api_models.RepaymentContractListDtoV2 {
  RepaymentContractListResponse toDomain() => RepaymentContractListResponse(
        loanContracts: loanContracts?.map((e) => e.toDomain()).toList(),
        relContracts: revolvingContracts?.map((e) => e.toDomain()).toList(),
      );
}
