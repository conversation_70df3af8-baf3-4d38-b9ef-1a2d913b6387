import 'package:package_info_plus/package_info_plus.dart';

enum CountryFlavor { vn, id, ind, ph, fake, unknown }

class PackageUtils {
  const PackageUtils._();

  static Future<CountryFlavor> getFlavor() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final packageName = packageInfo.packageName;
      if (packageName.endsWith('fake')) {
        return CountryFlavor.fake;
      } else if (packageName.startsWith('vn')) {
        return CountryFlavor.vn;
      } else if (packageName.startsWith('id')) {
        return CountryFlavor.id;
      } else if (packageName.startsWith('in')) {
        return CountryFlavor.ind;
      } else if (packageName.startsWith('ph')) {
        return CountryFlavor.ph;
      }

      return CountryFlavor.unknown;
    } catch (e) {
      return CountryFlavor.unknown;
    }
  }
}
