import 'package:flutter/material.dart';

class DateTimeUtils {
  const DateTimeUtils._();

  static String getOrdinalSuffix(int day) {
    if (day < 1 || day > 31) {
      debugPrint('Day must be between 1 and 31');

      return '';
    }

    // Handle special cases for 11th, 12th, and 13th
    if (day >= 11 && day <= 13) {
      return 'th';
    }

    // Determine the suffix based on the last digit
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}
