import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_repayment_core.dart';

class NavigationUtils {
  const NavigationUtils._();

  static Future<void> navigateToRepaymentMain({
    required BuildContext context,
    bool? isFromPayNow,
    RepaymentContract? selectedContract,
    RepaymentViewType? viewType,
  }) async {
    context.get<GlobalTrackingProperties>().repaymentAbTest = KoyalTrackingLabels.newFlow;
    var screenName = 'RepaymentMainScreen';
    ScreenArguments? screenArguments = RepaymentMainRouteArgs(
      isFromPayNow: isFromPayNow,
      selectedContract: selectedContract,
      viewType: viewType ?? RepaymentViewType.methodSelection,
    );
    if (!context.isFlagEnabledRead(FeatureFlag.repaymentNew)) {
      context.get<GlobalTrackingProperties>().repaymentAbTest = null;
      // For old repayment flow
      if (viewType == RepaymentViewType.methodSelection) {
        screenName = 'RepaymentMethodSelectionScreen';
        screenArguments = RepaymentMethodSelectionRouteArgs(
          isFromPayNow: isFromPayNow,
          selectedContract: selectedContract!,
        );
      } else if (viewType == RepaymentViewType.contractList) {
        screenName = 'RepaymentContractListScreen';
        screenArguments = null;
      }
    }

    await context.navigator.pushFromPackage(
      package: 'CappRepayment',
      screen: screenName,
      arguments: screenArguments,
    );
  }

  static Future<void> navigateToRepaymentPromiseToPay({
    required BuildContext context,
    required String contractNumber,
  }) async {
    await context.navigator.pushFromPackage(
      package: 'CappRepayment',
      screen: 'RepaymentPtpEligibilityCheckingScreen',
      arguments: RepaymentPtpEligibilityCheckingRouteArgs(contractNumber: contractNumber),
    );
  }

  static Future<void> navigateToRepaymentAda({
    required BuildContext context,
  }) async {
    await context.navigator.pushFromPackage(
      package: 'CappRepayment',
      screen: 'RepaymentAdaManagementScreen',
    );
  }

  static Future<void> navigateToFailedScreen({
    required BuildContext context,
    required String screenTitle,
    String? errorTitle,
    String? errorMessage,
  }) async {
    await context.navigator.pushFromPackage(
      package: 'CappRepayment',
      screen: 'RepaymentFailedScreen',
      arguments: RepaymentFailedRouteArgs(
        screenTitle: screenTitle,
        errorTitle: errorTitle,
        errorMessage: errorMessage ?? '',
      ),
    );
  }

  static void openDeeplink({
    required BuildContext context,
    required String deeplink,
  }) {
    context.get<IDeeplinkService>().deeplinkOrLaunch(
      deeplink,
      context,
    );
  }

  static Future<void> navigateToLoanJourneyAda({
    required BuildContext context,
  }) async {
    await context.navigator.pushFromPackage(
      package: 'CappRepayment',
      screen: 'RepaymentAdaManagementScreen',
    );
  }
}
