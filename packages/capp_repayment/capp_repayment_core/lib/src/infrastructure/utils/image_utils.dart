import 'package:capp_ui_core/capp_ui.dart';
// ignore: avoid_classes_with_only_static_members
import 'package:flutter/material.dart';

class ImageUtils {
  const ImageUtils._();

  static ColorFilter getTintColorFilter({
    Color tintColor = HciColors.grey,
    double scale = 1,
  }) {
    final r = tintColor.red;
    final g = tintColor.green;
    final b = tintColor.blue;

    final rTint = r / 255;
    final gTint = g / 255;
    final bTint = b / 255;

    const rL = 0.2126;
    const gL = 0.7152;
    const bL = 0.0722;

    final translate = 1 - scale * 0.5;

    return ColorFilter.matrix(<double>[
      (rL * rTint * scale),
      (gL * rTint * scale),
      (bL * rTint * scale),
      0,
      (r * translate),
      (rL * gTint * scale),
      (gL * gTint * scale),
      (bL * gTint * scale),
      0,
      (g * translate),
      (rL * bTint * scale),
      (gL * bTint * scale),
      (bL * bTint * scale),
      0,
      (b * translate),
      0,
      0,
      0,
      1,
      0,
    ]);
  }
}
