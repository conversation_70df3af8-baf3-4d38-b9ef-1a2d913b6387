import 'dart:io';

import 'package:dio/dio.dart';

import '../../../capp_repayment_core.dart';

class ApiUtils {
  const ApiUtils._();

  static RepaymentFailure parseDioErrorToRepaymentFailure(DioError e) {
    final statusCode = e.response?.statusCode;
    if (statusCode == HttpStatus.badRequest) {
      return const RepaymentFailure.badRequest();
    } else if (e.response?.statusCode == HttpStatus.internalServerError) {
      return const RepaymentFailure.internalServerError();
    } else if (e.response?.statusCode == HttpStatus.notFound) {
      return const RepaymentFailure.notFound();
    } else {
      return const RepaymentFailure.unexpected();
    }
  }

  static RepaymentErrorType parseFromRepaymentFailureToErrorType(RepaymentFailure repaymentFailure) {
    return repaymentFailure.map(
      badRequest: (_) => RepaymentErrorType.badRequest,
      notFound: (_) => RepaymentErrorType.notFound,
      internalServerError: (_) => RepaymentErrorType.internalServerError,
      unexpected: (_) => RepaymentErrorType.unexpected,
    );
  }
}
