import 'package:gma_platform/gma_platform.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionUtils {
  const PermissionUtils._();

  static Future<PermissionStatus> getCurrentPermissionStatusForSaveImageToPhotos() async {
    // For Android 10 and above no need to request storage permission to save file
    // For Android 9 and below, save as image instead so no need request any permission
    if (GmaPlatform.isAndroid) {
      return PermissionStatus.granted;
    }

    final requestPermission = PermissionUtils.getPermissionForSaveImageToPhotos();

    final requestPermissionStatus = await requestPermission?.status;

    return requestPermissionStatus ?? PermissionStatus.granted;
  }

  static Permission? getPermissionForSaveImageToPhotos() {
    return GmaPlatform.isAndroid ? null : Permission.photosAddOnly;
  }
}
