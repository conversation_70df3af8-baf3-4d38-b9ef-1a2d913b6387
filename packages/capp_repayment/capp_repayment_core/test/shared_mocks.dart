import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:mocktail/mocktail.dart';

class TestMockFeatureFlagRepository extends Mock implements FeatureFlagRepository {
  @override
  StreamController<void> featureFlagsChanged = StreamController<void>.broadcast();
}

class TestMockIdentityRepository extends Mock implements IdentityRepository {}

class TestMockEnvironmentConfigRepository extends Mock implements IEnvironmentConfigRepository {}
