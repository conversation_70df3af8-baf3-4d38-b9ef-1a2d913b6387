// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_select_amount_cel_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentSelectAmountCelEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentSelectAmountCelEventCopyWith<$Res> {
  factory $RepaymentSelectAmountCelEventCopyWith(
          RepaymentSelectAmountCelEvent value,
          $Res Function(RepaymentSelectAmountCelEvent) then) =
      _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
          RepaymentSelectAmountCelEvent>;
}

/// @nodoc
class _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        $Val extends RepaymentSelectAmountCelEvent>
    implements $RepaymentSelectAmountCelEventCopyWith<$Res> {
  _$RepaymentSelectAmountCelEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {RepaymentLoanContract repaymentLoanContract,
      Decimal minAmount,
      bool preventOverPayment});
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repaymentLoanContract = null,
    Object? minAmount = null,
    Object? preventOverPayment = null,
  }) {
    return _then(_$_Initialize(
      repaymentLoanContract: null == repaymentLoanContract
          ? _value.repaymentLoanContract
          : repaymentLoanContract // ignore: cast_nullable_to_non_nullable
              as RepaymentLoanContract,
      minAmount: null == minAmount
          ? _value.minAmount
          : minAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      preventOverPayment: null == preventOverPayment
          ? _value.preventOverPayment
          : preventOverPayment // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize(
      {required this.repaymentLoanContract,
      required this.minAmount,
      required this.preventOverPayment});

  @override
  final RepaymentLoanContract repaymentLoanContract;
  @override
  final Decimal minAmount;
  @override
  final bool preventOverPayment;

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.initialize(repaymentLoanContract: $repaymentLoanContract, minAmount: $minAmount, preventOverPayment: $preventOverPayment)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Initialize &&
            (identical(other.repaymentLoanContract, repaymentLoanContract) ||
                other.repaymentLoanContract == repaymentLoanContract) &&
            (identical(other.minAmount, minAmount) ||
                other.minAmount == minAmount) &&
            (identical(other.preventOverPayment, preventOverPayment) ||
                other.preventOverPayment == preventOverPayment));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, repaymentLoanContract, minAmount, preventOverPayment);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      __$$_InitializeCopyWithImpl<_$_Initialize>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
  }) {
    return initialize(repaymentLoanContract, minAmount, preventOverPayment);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
  }) {
    return initialize?.call(
        repaymentLoanContract, minAmount, preventOverPayment);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(repaymentLoanContract, minAmount, preventOverPayment);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentSelectAmountCelEvent {
  const factory _Initialize(
      {required final RepaymentLoanContract repaymentLoanContract,
      required final Decimal minAmount,
      required final bool preventOverPayment}) = _$_Initialize;

  RepaymentLoanContract get repaymentLoanContract;
  Decimal get minAmount;
  bool get preventOverPayment;
  @JsonKey(ignore: true)
  _$$_InitializeCopyWith<_$_Initialize> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$_SelectDueAmountCopyWith<$Res> {
  factory _$$_SelectDueAmountCopyWith(
          _$_SelectDueAmount value, $Res Function(_$_SelectDueAmount) then) =
      __$$_SelectDueAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SelectDueAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        _$_SelectDueAmount> implements _$$_SelectDueAmountCopyWith<$Res> {
  __$$_SelectDueAmountCopyWithImpl(
      _$_SelectDueAmount _value, $Res Function(_$_SelectDueAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SelectDueAmount implements _SelectDueAmount {
  const _$_SelectDueAmount();

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.selectDueAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SelectDueAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
  }) {
    return selectDueAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
  }) {
    return selectDueAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    required TResult orElse(),
  }) {
    if (selectDueAmount != null) {
      return selectDueAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
  }) {
    return selectDueAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
  }) {
    return selectDueAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    required TResult orElse(),
  }) {
    if (selectDueAmount != null) {
      return selectDueAmount(this);
    }
    return orElse();
  }
}

abstract class _SelectDueAmount implements RepaymentSelectAmountCelEvent {
  const factory _SelectDueAmount() = _$_SelectDueAmount;
}

/// @nodoc
abstract class _$$_SelectCustomAmountCopyWith<$Res> {
  factory _$$_SelectCustomAmountCopyWith(_$_SelectCustomAmount value,
          $Res Function(_$_SelectCustomAmount) then) =
      __$$_SelectCustomAmountCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_SelectCustomAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res,
        _$_SelectCustomAmount> implements _$$_SelectCustomAmountCopyWith<$Res> {
  __$$_SelectCustomAmountCopyWithImpl(
      _$_SelectCustomAmount _value, $Res Function(_$_SelectCustomAmount) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_SelectCustomAmount implements _SelectCustomAmount {
  const _$_SelectCustomAmount();

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.selectCustomAmount()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_SelectCustomAmount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
  }) {
    return selectCustomAmount();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
  }) {
    return selectCustomAmount?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    required TResult orElse(),
  }) {
    if (selectCustomAmount != null) {
      return selectCustomAmount();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
  }) {
    return selectCustomAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
  }) {
    return selectCustomAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    required TResult orElse(),
  }) {
    if (selectCustomAmount != null) {
      return selectCustomAmount(this);
    }
    return orElse();
  }
}

abstract class _SelectCustomAmount implements RepaymentSelectAmountCelEvent {
  const factory _SelectCustomAmount() = _$_SelectCustomAmount;
}

/// @nodoc
abstract class _$$_SetAmountCopyWith<$Res> {
  factory _$$_SetAmountCopyWith(
          _$_SetAmount value, $Res Function(_$_SetAmount) then) =
      __$$_SetAmountCopyWithImpl<$Res>;
  @useResult
  $Res call({Decimal? amount});
}

/// @nodoc
class __$$_SetAmountCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelEventCopyWithImpl<$Res, _$_SetAmount>
    implements _$$_SetAmountCopyWith<$Res> {
  __$$_SetAmountCopyWithImpl(
      _$_SetAmount _value, $Res Function(_$_SetAmount) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
  }) {
    return _then(_$_SetAmount(
      freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
    ));
  }
}

/// @nodoc

class _$_SetAmount implements _SetAmount {
  const _$_SetAmount(this.amount);

  @override
  final Decimal? amount;

  @override
  String toString() {
    return 'RepaymentSelectAmountCelEvent.setAmount(amount: $amount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetAmount &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      __$$_SetAmountCopyWithImpl<_$_SetAmount>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)
        initialize,
    required TResult Function() selectDueAmount,
    required TResult Function() selectCustomAmount,
    required TResult Function(Decimal? amount) setAmount,
  }) {
    return setAmount(amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult? Function()? selectDueAmount,
    TResult? Function()? selectCustomAmount,
    TResult? Function(Decimal? amount)? setAmount,
  }) {
    return setAmount?.call(amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(RepaymentLoanContract repaymentLoanContract,
            Decimal minAmount, bool preventOverPayment)?
        initialize,
    TResult Function()? selectDueAmount,
    TResult Function()? selectCustomAmount,
    TResult Function(Decimal? amount)? setAmount,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_SelectDueAmount value) selectDueAmount,
    required TResult Function(_SelectCustomAmount value) selectCustomAmount,
    required TResult Function(_SetAmount value) setAmount,
  }) {
    return setAmount(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_SelectDueAmount value)? selectDueAmount,
    TResult? Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult? Function(_SetAmount value)? setAmount,
  }) {
    return setAmount?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_SelectDueAmount value)? selectDueAmount,
    TResult Function(_SelectCustomAmount value)? selectCustomAmount,
    TResult Function(_SetAmount value)? setAmount,
    required TResult orElse(),
  }) {
    if (setAmount != null) {
      return setAmount(this);
    }
    return orElse();
  }
}

abstract class _SetAmount implements RepaymentSelectAmountCelEvent {
  const factory _SetAmount(final Decimal? amount) = _$_SetAmount;

  Decimal? get amount;
  @JsonKey(ignore: true)
  _$$_SetAmountCopyWith<_$_SetAmount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentSelectAmountCelState {
  RepaymentLoanContract? get repaymentLoanContract =>
      throw _privateConstructorUsedError;
  Decimal get dueAmount => throw _privateConstructorUsedError;
  Decimal get minAmount => throw _privateConstructorUsedError;
  Decimal? get selectedAmount => throw _privateConstructorUsedError;
  bool get isDueAmountSelected => throw _privateConstructorUsedError;
  bool get isCustomAmountSelected => throw _privateConstructorUsedError;
  bool get isLessThanDueAmount => throw _privateConstructorUsedError;
  bool get isLessThanMinAmount => throw _privateConstructorUsedError;
  bool get isOverTotalDebt => throw _privateConstructorUsedError;
  bool get preventOverPayment => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentSelectAmountCelStateCopyWith<RepaymentSelectAmountCelState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentSelectAmountCelStateCopyWith<$Res> {
  factory $RepaymentSelectAmountCelStateCopyWith(
          RepaymentSelectAmountCelState value,
          $Res Function(RepaymentSelectAmountCelState) then) =
      _$RepaymentSelectAmountCelStateCopyWithImpl<$Res,
          RepaymentSelectAmountCelState>;
  @useResult
  $Res call(
      {RepaymentLoanContract? repaymentLoanContract,
      Decimal dueAmount,
      Decimal minAmount,
      Decimal? selectedAmount,
      bool isDueAmountSelected,
      bool isCustomAmountSelected,
      bool isLessThanDueAmount,
      bool isLessThanMinAmount,
      bool isOverTotalDebt,
      bool preventOverPayment});
}

/// @nodoc
class _$RepaymentSelectAmountCelStateCopyWithImpl<$Res,
        $Val extends RepaymentSelectAmountCelState>
    implements $RepaymentSelectAmountCelStateCopyWith<$Res> {
  _$RepaymentSelectAmountCelStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repaymentLoanContract = freezed,
    Object? dueAmount = null,
    Object? minAmount = null,
    Object? selectedAmount = freezed,
    Object? isDueAmountSelected = null,
    Object? isCustomAmountSelected = null,
    Object? isLessThanDueAmount = null,
    Object? isLessThanMinAmount = null,
    Object? isOverTotalDebt = null,
    Object? preventOverPayment = null,
  }) {
    return _then(_value.copyWith(
      repaymentLoanContract: freezed == repaymentLoanContract
          ? _value.repaymentLoanContract
          : repaymentLoanContract // ignore: cast_nullable_to_non_nullable
              as RepaymentLoanContract?,
      dueAmount: null == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      minAmount: null == minAmount
          ? _value.minAmount
          : minAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      isDueAmountSelected: null == isDueAmountSelected
          ? _value.isDueAmountSelected
          : isDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountSelected: null == isCustomAmountSelected
          ? _value.isCustomAmountSelected
          : isCustomAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanDueAmount: null == isLessThanDueAmount
          ? _value.isLessThanDueAmount
          : isLessThanDueAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanMinAmount: null == isLessThanMinAmount
          ? _value.isLessThanMinAmount
          : isLessThanMinAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverTotalDebt: null == isOverTotalDebt
          ? _value.isOverTotalDebt
          : isOverTotalDebt // ignore: cast_nullable_to_non_nullable
              as bool,
      preventOverPayment: null == preventOverPayment
          ? _value.preventOverPayment
          : preventOverPayment // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentSelectAmountCelStateCopyWith<$Res>
    implements $RepaymentSelectAmountCelStateCopyWith<$Res> {
  factory _$$_RepaymentSelectAmountCelStateCopyWith(
          _$_RepaymentSelectAmountCelState value,
          $Res Function(_$_RepaymentSelectAmountCelState) then) =
      __$$_RepaymentSelectAmountCelStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {RepaymentLoanContract? repaymentLoanContract,
      Decimal dueAmount,
      Decimal minAmount,
      Decimal? selectedAmount,
      bool isDueAmountSelected,
      bool isCustomAmountSelected,
      bool isLessThanDueAmount,
      bool isLessThanMinAmount,
      bool isOverTotalDebt,
      bool preventOverPayment});
}

/// @nodoc
class __$$_RepaymentSelectAmountCelStateCopyWithImpl<$Res>
    extends _$RepaymentSelectAmountCelStateCopyWithImpl<$Res,
        _$_RepaymentSelectAmountCelState>
    implements _$$_RepaymentSelectAmountCelStateCopyWith<$Res> {
  __$$_RepaymentSelectAmountCelStateCopyWithImpl(
      _$_RepaymentSelectAmountCelState _value,
      $Res Function(_$_RepaymentSelectAmountCelState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repaymentLoanContract = freezed,
    Object? dueAmount = null,
    Object? minAmount = null,
    Object? selectedAmount = freezed,
    Object? isDueAmountSelected = null,
    Object? isCustomAmountSelected = null,
    Object? isLessThanDueAmount = null,
    Object? isLessThanMinAmount = null,
    Object? isOverTotalDebt = null,
    Object? preventOverPayment = null,
  }) {
    return _then(_$_RepaymentSelectAmountCelState(
      repaymentLoanContract: freezed == repaymentLoanContract
          ? _value.repaymentLoanContract
          : repaymentLoanContract // ignore: cast_nullable_to_non_nullable
              as RepaymentLoanContract?,
      dueAmount: null == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      minAmount: null == minAmount
          ? _value.minAmount
          : minAmount // ignore: cast_nullable_to_non_nullable
              as Decimal,
      selectedAmount: freezed == selectedAmount
          ? _value.selectedAmount
          : selectedAmount // ignore: cast_nullable_to_non_nullable
              as Decimal?,
      isDueAmountSelected: null == isDueAmountSelected
          ? _value.isDueAmountSelected
          : isDueAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isCustomAmountSelected: null == isCustomAmountSelected
          ? _value.isCustomAmountSelected
          : isCustomAmountSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanDueAmount: null == isLessThanDueAmount
          ? _value.isLessThanDueAmount
          : isLessThanDueAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isLessThanMinAmount: null == isLessThanMinAmount
          ? _value.isLessThanMinAmount
          : isLessThanMinAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      isOverTotalDebt: null == isOverTotalDebt
          ? _value.isOverTotalDebt
          : isOverTotalDebt // ignore: cast_nullable_to_non_nullable
              as bool,
      preventOverPayment: null == preventOverPayment
          ? _value.preventOverPayment
          : preventOverPayment // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_RepaymentSelectAmountCelState
    implements _RepaymentSelectAmountCelState {
  const _$_RepaymentSelectAmountCelState(
      {required this.repaymentLoanContract,
      required this.dueAmount,
      required this.minAmount,
      required this.selectedAmount,
      required this.isDueAmountSelected,
      required this.isCustomAmountSelected,
      required this.isLessThanDueAmount,
      required this.isLessThanMinAmount,
      required this.isOverTotalDebt,
      required this.preventOverPayment});

  @override
  final RepaymentLoanContract? repaymentLoanContract;
  @override
  final Decimal dueAmount;
  @override
  final Decimal minAmount;
  @override
  final Decimal? selectedAmount;
  @override
  final bool isDueAmountSelected;
  @override
  final bool isCustomAmountSelected;
  @override
  final bool isLessThanDueAmount;
  @override
  final bool isLessThanMinAmount;
  @override
  final bool isOverTotalDebt;
  @override
  final bool preventOverPayment;

  @override
  String toString() {
    return 'RepaymentSelectAmountCelState(repaymentLoanContract: $repaymentLoanContract, dueAmount: $dueAmount, minAmount: $minAmount, selectedAmount: $selectedAmount, isDueAmountSelected: $isDueAmountSelected, isCustomAmountSelected: $isCustomAmountSelected, isLessThanDueAmount: $isLessThanDueAmount, isLessThanMinAmount: $isLessThanMinAmount, isOverTotalDebt: $isOverTotalDebt, preventOverPayment: $preventOverPayment)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentSelectAmountCelState &&
            (identical(other.repaymentLoanContract, repaymentLoanContract) ||
                other.repaymentLoanContract == repaymentLoanContract) &&
            (identical(other.dueAmount, dueAmount) ||
                other.dueAmount == dueAmount) &&
            (identical(other.minAmount, minAmount) ||
                other.minAmount == minAmount) &&
            (identical(other.selectedAmount, selectedAmount) ||
                other.selectedAmount == selectedAmount) &&
            (identical(other.isDueAmountSelected, isDueAmountSelected) ||
                other.isDueAmountSelected == isDueAmountSelected) &&
            (identical(other.isCustomAmountSelected, isCustomAmountSelected) ||
                other.isCustomAmountSelected == isCustomAmountSelected) &&
            (identical(other.isLessThanDueAmount, isLessThanDueAmount) ||
                other.isLessThanDueAmount == isLessThanDueAmount) &&
            (identical(other.isLessThanMinAmount, isLessThanMinAmount) ||
                other.isLessThanMinAmount == isLessThanMinAmount) &&
            (identical(other.isOverTotalDebt, isOverTotalDebt) ||
                other.isOverTotalDebt == isOverTotalDebt) &&
            (identical(other.preventOverPayment, preventOverPayment) ||
                other.preventOverPayment == preventOverPayment));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      repaymentLoanContract,
      dueAmount,
      minAmount,
      selectedAmount,
      isDueAmountSelected,
      isCustomAmountSelected,
      isLessThanDueAmount,
      isLessThanMinAmount,
      isOverTotalDebt,
      preventOverPayment);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentSelectAmountCelStateCopyWith<_$_RepaymentSelectAmountCelState>
      get copyWith => __$$_RepaymentSelectAmountCelStateCopyWithImpl<
          _$_RepaymentSelectAmountCelState>(this, _$identity);
}

abstract class _RepaymentSelectAmountCelState
    implements RepaymentSelectAmountCelState {
  const factory _RepaymentSelectAmountCelState(
          {required final RepaymentLoanContract? repaymentLoanContract,
          required final Decimal dueAmount,
          required final Decimal minAmount,
          required final Decimal? selectedAmount,
          required final bool isDueAmountSelected,
          required final bool isCustomAmountSelected,
          required final bool isLessThanDueAmount,
          required final bool isLessThanMinAmount,
          required final bool isOverTotalDebt,
          required final bool preventOverPayment}) =
      _$_RepaymentSelectAmountCelState;

  @override
  RepaymentLoanContract? get repaymentLoanContract;
  @override
  Decimal get dueAmount;
  @override
  Decimal get minAmount;
  @override
  Decimal? get selectedAmount;
  @override
  bool get isDueAmountSelected;
  @override
  bool get isCustomAmountSelected;
  @override
  bool get isLessThanDueAmount;
  @override
  bool get isLessThanMinAmount;
  @override
  bool get isOverTotalDebt;
  @override
  bool get preventOverPayment;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentSelectAmountCelStateCopyWith<_$_RepaymentSelectAmountCelState>
      get copyWith => throw _privateConstructorUsedError;
}
