// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'repayment_contract_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$RepaymentContractListEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function() fetchContracts,
    required TResult Function() refresh,
    required TResult Function(RepaymentContract contract) selectContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? refresh,
    TResult? Function(RepaymentContract contract)? selectContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function()? fetchContracts,
    TResult Function()? refresh,
    TResult Function(RepaymentContract contract)? selectContract,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_SelectContract value) selectContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_SelectContract value)? selectContract,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_SelectContract value)? selectContract,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentContractListEventCopyWith<$Res> {
  factory $RepaymentContractListEventCopyWith(RepaymentContractListEvent value,
          $Res Function(RepaymentContractListEvent) then) =
      _$RepaymentContractListEventCopyWithImpl<$Res,
          RepaymentContractListEvent>;
}

/// @nodoc
class _$RepaymentContractListEventCopyWithImpl<$Res,
        $Val extends RepaymentContractListEvent>
    implements $RepaymentContractListEventCopyWith<$Res> {
  _$RepaymentContractListEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_InitializeCopyWith<$Res> {
  factory _$$_InitializeCopyWith(
          _$_Initialize value, $Res Function(_$_Initialize) then) =
      __$$_InitializeCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_InitializeCopyWithImpl<$Res>
    extends _$RepaymentContractListEventCopyWithImpl<$Res, _$_Initialize>
    implements _$$_InitializeCopyWith<$Res> {
  __$$_InitializeCopyWithImpl(
      _$_Initialize _value, $Res Function(_$_Initialize) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize();

  @override
  String toString() {
    return 'RepaymentContractListEvent.initialize()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Initialize);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function() fetchContracts,
    required TResult Function() refresh,
    required TResult Function(RepaymentContract contract) selectContract,
  }) {
    return initialize();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? refresh,
    TResult? Function(RepaymentContract contract)? selectContract,
  }) {
    return initialize?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function()? fetchContracts,
    TResult Function()? refresh,
    TResult Function(RepaymentContract contract)? selectContract,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_SelectContract value) selectContract,
  }) {
    return initialize(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_SelectContract value)? selectContract,
  }) {
    return initialize?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_SelectContract value)? selectContract,
    required TResult orElse(),
  }) {
    if (initialize != null) {
      return initialize(this);
    }
    return orElse();
  }
}

abstract class _Initialize implements RepaymentContractListEvent {
  const factory _Initialize() = _$_Initialize;
}

/// @nodoc
abstract class _$$_FetchContractsCopyWith<$Res> {
  factory _$$_FetchContractsCopyWith(
          _$_FetchContracts value, $Res Function(_$_FetchContracts) then) =
      __$$_FetchContractsCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_FetchContractsCopyWithImpl<$Res>
    extends _$RepaymentContractListEventCopyWithImpl<$Res, _$_FetchContracts>
    implements _$$_FetchContractsCopyWith<$Res> {
  __$$_FetchContractsCopyWithImpl(
      _$_FetchContracts _value, $Res Function(_$_FetchContracts) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_FetchContracts implements _FetchContracts {
  const _$_FetchContracts();

  @override
  String toString() {
    return 'RepaymentContractListEvent.fetchContracts()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_FetchContracts);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function() fetchContracts,
    required TResult Function() refresh,
    required TResult Function(RepaymentContract contract) selectContract,
  }) {
    return fetchContracts();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? refresh,
    TResult? Function(RepaymentContract contract)? selectContract,
  }) {
    return fetchContracts?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function()? fetchContracts,
    TResult Function()? refresh,
    TResult Function(RepaymentContract contract)? selectContract,
    required TResult orElse(),
  }) {
    if (fetchContracts != null) {
      return fetchContracts();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_SelectContract value) selectContract,
  }) {
    return fetchContracts(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_SelectContract value)? selectContract,
  }) {
    return fetchContracts?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_SelectContract value)? selectContract,
    required TResult orElse(),
  }) {
    if (fetchContracts != null) {
      return fetchContracts(this);
    }
    return orElse();
  }
}

abstract class _FetchContracts implements RepaymentContractListEvent {
  const factory _FetchContracts() = _$_FetchContracts;
}

/// @nodoc
abstract class _$$_RefreshCopyWith<$Res> {
  factory _$$_RefreshCopyWith(
          _$_Refresh value, $Res Function(_$_Refresh) then) =
      __$$_RefreshCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_RefreshCopyWithImpl<$Res>
    extends _$RepaymentContractListEventCopyWithImpl<$Res, _$_Refresh>
    implements _$$_RefreshCopyWith<$Res> {
  __$$_RefreshCopyWithImpl(_$_Refresh _value, $Res Function(_$_Refresh) _then)
      : super(_value, _then);
}

/// @nodoc

class _$_Refresh implements _Refresh {
  const _$_Refresh();

  @override
  String toString() {
    return 'RepaymentContractListEvent.refresh()';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$_Refresh);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function() fetchContracts,
    required TResult Function() refresh,
    required TResult Function(RepaymentContract contract) selectContract,
  }) {
    return refresh();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? refresh,
    TResult? Function(RepaymentContract contract)? selectContract,
  }) {
    return refresh?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function()? fetchContracts,
    TResult Function()? refresh,
    TResult Function(RepaymentContract contract)? selectContract,
    required TResult orElse(),
  }) {
    if (refresh != null) {
      return refresh();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_SelectContract value) selectContract,
  }) {
    return refresh(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_SelectContract value)? selectContract,
  }) {
    return refresh?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_SelectContract value)? selectContract,
    required TResult orElse(),
  }) {
    if (refresh != null) {
      return refresh(this);
    }
    return orElse();
  }
}

abstract class _Refresh implements RepaymentContractListEvent {
  const factory _Refresh() = _$_Refresh;
}

/// @nodoc
abstract class _$$_SelectContractCopyWith<$Res> {
  factory _$$_SelectContractCopyWith(
          _$_SelectContract value, $Res Function(_$_SelectContract) then) =
      __$$_SelectContractCopyWithImpl<$Res>;
  @useResult
  $Res call({RepaymentContract contract});
}

/// @nodoc
class __$$_SelectContractCopyWithImpl<$Res>
    extends _$RepaymentContractListEventCopyWithImpl<$Res, _$_SelectContract>
    implements _$$_SelectContractCopyWith<$Res> {
  __$$_SelectContractCopyWithImpl(
      _$_SelectContract _value, $Res Function(_$_SelectContract) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contract = null,
  }) {
    return _then(_$_SelectContract(
      contract: null == contract
          ? _value.contract
          : contract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract,
    ));
  }
}

/// @nodoc

class _$_SelectContract implements _SelectContract {
  const _$_SelectContract({required this.contract});

  @override
  final RepaymentContract contract;

  @override
  String toString() {
    return 'RepaymentContractListEvent.selectContract(contract: $contract)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SelectContract &&
            (identical(other.contract, contract) ||
                other.contract == contract));
  }

  @override
  int get hashCode => Object.hash(runtimeType, contract);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SelectContractCopyWith<_$_SelectContract> get copyWith =>
      __$$_SelectContractCopyWithImpl<_$_SelectContract>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initialize,
    required TResult Function() fetchContracts,
    required TResult Function() refresh,
    required TResult Function(RepaymentContract contract) selectContract,
  }) {
    return selectContract(contract);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initialize,
    TResult? Function()? fetchContracts,
    TResult? Function()? refresh,
    TResult? Function(RepaymentContract contract)? selectContract,
  }) {
    return selectContract?.call(contract);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initialize,
    TResult Function()? fetchContracts,
    TResult Function()? refresh,
    TResult Function(RepaymentContract contract)? selectContract,
    required TResult orElse(),
  }) {
    if (selectContract != null) {
      return selectContract(contract);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialize value) initialize,
    required TResult Function(_FetchContracts value) fetchContracts,
    required TResult Function(_Refresh value) refresh,
    required TResult Function(_SelectContract value) selectContract,
  }) {
    return selectContract(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialize value)? initialize,
    TResult? Function(_FetchContracts value)? fetchContracts,
    TResult? Function(_Refresh value)? refresh,
    TResult? Function(_SelectContract value)? selectContract,
  }) {
    return selectContract?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialize value)? initialize,
    TResult Function(_FetchContracts value)? fetchContracts,
    TResult Function(_Refresh value)? refresh,
    TResult Function(_SelectContract value)? selectContract,
    required TResult orElse(),
  }) {
    if (selectContract != null) {
      return selectContract(this);
    }
    return orElse();
  }
}

abstract class _SelectContract implements RepaymentContractListEvent {
  const factory _SelectContract({required final RepaymentContract contract}) =
      _$_SelectContract;

  RepaymentContract get contract;
  @JsonKey(ignore: true)
  _$$_SelectContractCopyWith<_$_SelectContract> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$RepaymentContractListState {
  LoadingState get loadingState => throw _privateConstructorUsedError;
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccess => throw _privateConstructorUsedError;
  bool? get isRefreshing => throw _privateConstructorUsedError;
  bool? get isError => throw _privateConstructorUsedError;
  bool? get isShowNoLoan => throw _privateConstructorUsedError;
  RepaymentContract? get selectedContract => throw _privateConstructorUsedError;
  List<RepaymentContract>? get contracts => throw _privateConstructorUsedError;
  bool get isValidInput => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RepaymentContractListStateCopyWith<RepaymentContractListState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RepaymentContractListStateCopyWith<$Res> {
  factory $RepaymentContractListStateCopyWith(RepaymentContractListState value,
          $Res Function(RepaymentContractListState) then) =
      _$RepaymentContractListStateCopyWithImpl<$Res,
          RepaymentContractListState>;
  @useResult
  $Res call(
      {LoadingState loadingState,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccess,
      bool? isRefreshing,
      bool? isError,
      bool? isShowNoLoan,
      RepaymentContract? selectedContract,
      List<RepaymentContract>? contracts,
      bool isValidInput});
}

/// @nodoc
class _$RepaymentContractListStateCopyWithImpl<$Res,
        $Val extends RepaymentContractListState>
    implements $RepaymentContractListStateCopyWith<$Res> {
  _$RepaymentContractListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? failureOrSuccess = null,
    Object? isRefreshing = freezed,
    Object? isError = freezed,
    Object? isShowNoLoan = freezed,
    Object? selectedContract = freezed,
    Object? contracts = freezed,
    Object? isValidInput = null,
  }) {
    return _then(_value.copyWith(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      isRefreshing: freezed == isRefreshing
          ? _value.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool?,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isShowNoLoan: freezed == isShowNoLoan
          ? _value.isShowNoLoan
          : isShowNoLoan // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      contracts: freezed == contracts
          ? _value.contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>?,
      isValidInput: null == isValidInput
          ? _value.isValidInput
          : isValidInput // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RepaymentContractListStateCopyWith<$Res>
    implements $RepaymentContractListStateCopyWith<$Res> {
  factory _$$_RepaymentContractListStateCopyWith(
          _$_RepaymentContractListState value,
          $Res Function(_$_RepaymentContractListState) then) =
      __$$_RepaymentContractListStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState loadingState,
      Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccess,
      bool? isRefreshing,
      bool? isError,
      bool? isShowNoLoan,
      RepaymentContract? selectedContract,
      List<RepaymentContract>? contracts,
      bool isValidInput});
}

/// @nodoc
class __$$_RepaymentContractListStateCopyWithImpl<$Res>
    extends _$RepaymentContractListStateCopyWithImpl<$Res,
        _$_RepaymentContractListState>
    implements _$$_RepaymentContractListStateCopyWith<$Res> {
  __$$_RepaymentContractListStateCopyWithImpl(
      _$_RepaymentContractListState _value,
      $Res Function(_$_RepaymentContractListState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadingState = null,
    Object? failureOrSuccess = null,
    Object? isRefreshing = freezed,
    Object? isError = freezed,
    Object? isShowNoLoan = freezed,
    Object? selectedContract = freezed,
    Object? contracts = freezed,
    Object? isValidInput = null,
  }) {
    return _then(_$_RepaymentContractListState(
      loadingState: null == loadingState
          ? _value.loadingState
          : loadingState // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<RepaymentFailure, List<RepaymentContract>>>,
      isRefreshing: freezed == isRefreshing
          ? _value.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool?,
      isError: freezed == isError
          ? _value.isError
          : isError // ignore: cast_nullable_to_non_nullable
              as bool?,
      isShowNoLoan: freezed == isShowNoLoan
          ? _value.isShowNoLoan
          : isShowNoLoan // ignore: cast_nullable_to_non_nullable
              as bool?,
      selectedContract: freezed == selectedContract
          ? _value.selectedContract
          : selectedContract // ignore: cast_nullable_to_non_nullable
              as RepaymentContract?,
      contracts: freezed == contracts
          ? _value._contracts
          : contracts // ignore: cast_nullable_to_non_nullable
              as List<RepaymentContract>?,
      isValidInput: null == isValidInput
          ? _value.isValidInput
          : isValidInput // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_RepaymentContractListState implements _RepaymentContractListState {
  const _$_RepaymentContractListState(
      {required this.loadingState,
      required this.failureOrSuccess,
      this.isRefreshing,
      this.isError,
      this.isShowNoLoan,
      this.selectedContract,
      final List<RepaymentContract>? contracts,
      required this.isValidInput})
      : _contracts = contracts;

  @override
  final LoadingState loadingState;
  @override
  final Option<Either<RepaymentFailure, List<RepaymentContract>>>
      failureOrSuccess;
  @override
  final bool? isRefreshing;
  @override
  final bool? isError;
  @override
  final bool? isShowNoLoan;
  @override
  final RepaymentContract? selectedContract;
  final List<RepaymentContract>? _contracts;
  @override
  List<RepaymentContract>? get contracts {
    final value = _contracts;
    if (value == null) return null;
    if (_contracts is EqualUnmodifiableListView) return _contracts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool isValidInput;

  @override
  String toString() {
    return 'RepaymentContractListState(loadingState: $loadingState, failureOrSuccess: $failureOrSuccess, isRefreshing: $isRefreshing, isError: $isError, isShowNoLoan: $isShowNoLoan, selectedContract: $selectedContract, contracts: $contracts, isValidInput: $isValidInput)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RepaymentContractListState &&
            (identical(other.loadingState, loadingState) ||
                other.loadingState == loadingState) &&
            (identical(other.failureOrSuccess, failureOrSuccess) ||
                other.failureOrSuccess == failureOrSuccess) &&
            (identical(other.isRefreshing, isRefreshing) ||
                other.isRefreshing == isRefreshing) &&
            (identical(other.isError, isError) || other.isError == isError) &&
            (identical(other.isShowNoLoan, isShowNoLoan) ||
                other.isShowNoLoan == isShowNoLoan) &&
            (identical(other.selectedContract, selectedContract) ||
                other.selectedContract == selectedContract) &&
            const DeepCollectionEquality()
                .equals(other._contracts, _contracts) &&
            (identical(other.isValidInput, isValidInput) ||
                other.isValidInput == isValidInput));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loadingState,
      failureOrSuccess,
      isRefreshing,
      isError,
      isShowNoLoan,
      selectedContract,
      const DeepCollectionEquality().hash(_contracts),
      isValidInput);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RepaymentContractListStateCopyWith<_$_RepaymentContractListState>
      get copyWith => __$$_RepaymentContractListStateCopyWithImpl<
          _$_RepaymentContractListState>(this, _$identity);
}

abstract class _RepaymentContractListState
    implements RepaymentContractListState {
  const factory _RepaymentContractListState(
      {required final LoadingState loadingState,
      required final Option<Either<RepaymentFailure, List<RepaymentContract>>>
          failureOrSuccess,
      final bool? isRefreshing,
      final bool? isError,
      final bool? isShowNoLoan,
      final RepaymentContract? selectedContract,
      final List<RepaymentContract>? contracts,
      required final bool isValidInput}) = _$_RepaymentContractListState;

  @override
  LoadingState get loadingState;
  @override
  Option<Either<RepaymentFailure, List<RepaymentContract>>>
      get failureOrSuccess;
  @override
  bool? get isRefreshing;
  @override
  bool? get isError;
  @override
  bool? get isShowNoLoan;
  @override
  RepaymentContract? get selectedContract;
  @override
  List<RepaymentContract>? get contracts;
  @override
  bool get isValidInput;
  @override
  @JsonKey(ignore: true)
  _$$_RepaymentContractListStateCopyWith<_$_RepaymentContractListState>
      get copyWith => throw _privateConstructorUsedError;
}
