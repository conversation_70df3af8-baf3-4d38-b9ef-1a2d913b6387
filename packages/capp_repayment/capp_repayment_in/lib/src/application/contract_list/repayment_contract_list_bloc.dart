import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../../../capp_repayment.dart';

part 'repayment_contract_list_bloc.freezed.dart';
part 'repayment_contract_list_event.dart';
part 'repayment_contract_list_state.dart';

class RepaymentContractListBloc extends Bloc<RepaymentContractListEvent, RepaymentContractListState> {
  final IRepaymentRepository repaymentRepository;
  final IUserRepository userRepository;
  final Logger logger;

  RepaymentContractListBloc({required this.repaymentRepository, required this.userRepository, required this.logger})
      : super(RepaymentContractListState.initialize()) {
    on<RepaymentContractListEvent>(
      (event, emit) async {
        await event.map(
          initialize: (e) => _initialize(e, emit),
          fetchContracts: (e) => _fetchContracts(e, emit),
          refresh: (e) => _refresh(e, emit),
          selectContract: (e) => _selectContract(e, emit),
        );
      },
      transformer: sequential(),
    );
  }

  Future<void> _initialize(_Initialize e, Emitter<RepaymentContractListState> emit) async {
    final user = (await userRepository.currentUser()).fold((l) => null, (r) => r);
    if (user?.accessLevel?.toLowerCase() == CoreConstants.publicAccessLevel.toLowerCase() || user?.isInsider == true) {
      String? cuid;
      cuid = await userRepository.userCuid() ?? '';
      if (cuid.isEmpty) {
        emit(state.copyWith(isShowNoLoan: true));
        return;
      }
    }

    add(const RepaymentContractListEvent.fetchContracts());
  }

  Future<void> _fetchContracts(_FetchContracts e, Emitter<RepaymentContractListState> emit) async {
    emit(
      state.copyWith(
        loadingState: LoadingState.isLoading,
        isError: false,
      ),
    );

    final apiResponse = await repaymentRepository.getContracts();
    logger.d('getContracts response$apiResponse');
    final response = apiResponse.map((r) => r.parseToRepaymentContract());

    emit(
      await response.fold((l) {
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          isError: true,
          failureOrSuccess: optionOf(response),
        );
      }, (r) async {
        if (r.isEmpty) {
          await Future<dynamic>.delayed(const Duration(seconds: 1));
        }
        return state.copyWith(
          loadingState: LoadingState.isCompleted,
          failureOrSuccess: optionOf(response),
          isError: false,
          selectedContract: r.length == 1 ? r[0] : null,
          isValidInput: r.length == 1,
          contracts: r,
          isShowNoLoan: r.isEmpty,
        );
      }),
    );
  }

  Future<void> _refresh(_Refresh e, Emitter<RepaymentContractListState> emit) async {
    emit(state.copyWith(isRefreshing: true, isError: false));

    final apiResponse = await repaymentRepository.getContracts();
    logger.d('getContracts response$apiResponse');
    final response = apiResponse.map((r) => r.parseToRepaymentContract());

    emit(
      response.fold((l) {
        return state.copyWith(
          isRefreshing: false,
          failureOrSuccess: optionOf(response),
          isError: true,
        );
      }, (r) {
        return state.copyWith(
          isRefreshing: false,
          failureOrSuccess: optionOf(response),
          isError: false,
          selectedContract: r.length == 1 ? r[0] : null,
          isValidInput: r.length == 1,
          contracts: r,
          isShowNoLoan: r.isEmpty,
        );
      }),
    );
  }

  Future<void> _selectContract(_SelectContract e, Emitter<RepaymentContractListState> emit) async {
    emit(
      state.copyWith(selectedContract: e.contract, isValidInput: true),
    );
  }
}
