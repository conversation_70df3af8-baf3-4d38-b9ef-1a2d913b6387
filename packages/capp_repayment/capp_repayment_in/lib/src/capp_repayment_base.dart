import 'package:capp_repayment_core/capp_repayment_core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:selfcareapi/selfcareapi.dart';

import '../capp_repayment.dart';

class CappRepayment extends ChildPackage {
  @override
  void onNewTranslations(Translations translations) {
    L10nCappRepayment.translations[translations.contentLanguage] = translations.forPackage('capp_repayment');
  }

  @override
  LocalizationsDelegate get localizationsDelegate => L10nCappRepayment.delegate;

  @override
  List<KoyalRoute> get routes => [
        KoyalRoute(
          CappRepayment,
          RepaymentContractListScreen,
          'repayment_contract_list_screen',
          (context, args) => const RepaymentContractListScreen().wrappedRoute(context),
        ),
        KoyalRoute(
          CappRepayment,
          RepaymentNoLoanScreen,
          'repayment_no_loan_screen',
          (context, args) => const RepaymentNoLoanScreen(),
        ),
        KoyalRoute(
          CappRepayment,
          RepaymentSelectAmountCelScreen,
          'repayment_select_amount_cel_screen',
          (context, args) =>
              RepaymentSelectAmountCelScreen(args: args! as RepaymentSelectAmountRouteArgs).wrappedRoute(context),
        ),
      ];

  @override
  void registerDependencies(GetIt container) {
    registerStorage(container);
    registerApi(container);
    registerServices(container);
    registerRepositories(container);
    registerBlocs(container);
    registerDevTools(container);
  }

  void registerStorage(GetIt c) {}

  void registerApi(GetIt c) {}

  void registerBlocs(GetIt c) {
    c
      ..registerTrackingFactory(
        () => RepaymentContractListBloc(
          logger: c.get<Logger>(),
          repaymentRepository: c.get<IRepaymentRepository>(),
          userRepository: c<IUserRepository>(),
        ),
      )
      ..registerTrackingFactory(
        RepaymentSelectAmountCelBloc.new,
      );
  }

  void registerServices(GetIt c) {
    c.registerLazySingleton(
      () => CappRepaymentTrackingService(
        eventTrackingService: c<EventTrackingService>(),
        currentUserRepository: c<ICurrentUserRepository>(),
        userRepository: c<IUserRepository>(),
        gtp: c<GlobalTrackingProperties>(),
      ),
    );
  }

  void registerRepositories(GetIt c) {
    c.get<ITranslationOverrideRepository>().registerTranslation(() {
      L10nCappRepayment.showKeys = !L10nCappRepayment.showKeys;
    });
    c.registerLazySingleton<IRepaymentRepository>(
      () => RepaymentRepository(
        api: c.get<RepaymentApi>(),
        logger: c.get<Logger>(),
      ),
    );
  }

  void registerDevTools(GetIt c) {}
}
