# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: b1595874fbc8f7a50da90f5d8f327bb0bfd6a95dc906c390efe991540c3b54aa
      url: "https://pub.dev"
    source: hosted
    version: "1.3.40"
  a2a_intent_receiver:
    dependency: transitive
    description:
      path: "../../../plugins/a2a_intent_receiver"
      relative: true
    source: path
    version: "0.0.1"
  account_manager:
    dependency: transitive
    description:
      path: "../../../forks/account_manager"
      relative: true
    source: path
    version: "0.0.1"
  advertising_id:
    dependency: transitive
    description:
      name: advertising_id
      sha256: ab06ee85203ab500be85b7f45de2a75a629d8d9c453dba779276fbc4e97ad8d3
      url: "https://pub.dev"
    source: hosted
    version: "2.7.1"
  alice:
    dependency: transitive
    description:
      path: "../../../forks/alice"
      relative: true
    source: path
    version: "0.2.5"
  alphabet_list_scroll_view:
    dependency: transitive
    description:
      path: "../../../forks/alphabet_list_scroll_view"
      relative: true
    source: path
    version: "2.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  analyzer_plugin:
    dependency: transitive
    description:
      name: analyzer_plugin
      sha256: c1d5f167683de03d5ab6c3b53fc9aeefc5d59476e7810ba7bbddff50c6f4392d
      url: "https://pub.dev"
    source: hosted
    version: "0.11.2"
  android_id:
    dependency: transitive
    description:
      name: android_id
      sha256: "748ba5f93dd5c497e675d8eaa1404346ce4d1794464ea654576ff192d153b92a"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  android_intent_plus:
    dependency: transitive
    description:
      name: android_intent_plus
      sha256: dfc1fd3a577205ae8f11e990fb4ece8c90cceabbee56fcf48e463ecf0bd6aae3
      url: "https://pub.dev"
    source: hosted
    version: "5.3.0"
  android_play_install_referrer:
    dependency: transitive
    description:
      name: android_play_install_referrer
      sha256: cba8387c1aaa35d2441159beebe5e9160901c44632d02d0579c6875756ecb798
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  animations:
    dependency: transitive
    description:
      name: animations
      sha256: d3d6dcfb218225bbe68e87ccf6378bbb2e32a94900722c5f81611dad089911cb
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  ansicolor:
    dependency: transitive
    description:
      name: ansicolor
      sha256: "50e982d500bc863e1d703448afdbf9e5a72eb48840a4f766fa361ffd6877055f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  app_links:
    dependency: transitive
    description:
      name: app_links
      sha256: "85ed8fc1d25a76475914fff28cc994653bd900bc2c26e4b57a49e097febb54ba"
      url: "https://pub.dev"
    source: hosted
    version: "6.4.0"
  app_links_linux:
    dependency: transitive
    description:
      name: app_links_linux
      sha256: f5f7173a78609f3dfd4c2ff2c95bd559ab43c80a87dc6a095921d96c05688c81
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  app_links_platform_interface:
    dependency: transitive
    description:
      name: app_links_platform_interface
      sha256: "05f5379577c513b534a29ddea68176a4d4802c46180ee8e2e966257158772a3f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_links_web:
    dependency: transitive
    description:
      name: app_links_web
      sha256: af060ed76183f9e2b87510a9480e56a5352b6c249778d07bd2c95fc35632a555
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  app_settings:
    dependency: transitive
    description:
      name: app_settings
      sha256: "476df1d85cec143c3d27dd1c7451629a59c0c5ccf70a0adcbfa92a0a2d928705"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  app_tracking_transparency:
    dependency: transitive
    description:
      name: app_tracking_transparency
      sha256: "1f71f4d8402552fbf8b191d4edab301f233c1af794878b7bc56c708470ffd74c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6+1"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: "4bae5ae63e6d6dd17c4aac8086f3dec26c0236f6a0f03416c6c19d830c367cf5"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.8"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  azlistview:
    dependency: transitive
    description:
      path: "../../../forks/azlistview"
      relative: true
    source: path
    version: "2.0.0"
  base32:
    dependency: transitive
    description:
      name: base32
      sha256: ddad4ebfedf93d4500818ed8e61443b734ffe7cf8a45c668c9b34ef6adde02e2
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  biometric_signature:
    dependency: transitive
    description:
      name: biometric_signature
      sha256: "1c1e47c2e958f75f8ff54cfa63700514542b3b7ae51290798334e17c805b00e2"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.3"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "106842ad6569f0b60297619e9e0b1885c2fb9bf84812935490e6c5275777804e"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  bloc_concurrency:
    dependency: transitive
    description:
      name: bloc_concurrency
      sha256: "456b7a3616a7c1ceb975c14441b3f198bf57d81cb95b7c6de5cb0c60201afcd8"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.5"
  bloc_test:
    dependency: "direct dev"
    description:
      name: bloc_test
      sha256: "165a6ec950d9252ebe36dc5335f2e6eb13055f33d56db0eeb7642768849b43d2"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.7"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "79b2aef6ac2ed00046867ed354c88778c9c0f029df8a20fe10b5436826721ef9"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "339086358431fa15d7eca8b6a36e5d783728cf025e559b834f4609a1fcfb7b0a"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "028819cfb90051c6b5440c7e574d1896f8037e3c96cf17aaeb054c9311cfbf4d"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.13"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: f8126682b87a7282a339b871298cc12009cb67109cfa1614d6436fb0289193e0
      url: "https://pub.dev"
    source: hosted
    version: "7.3.2"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: ea90e81dc4a25a043d9bee692d20ed6d1c4a1662a28c03a96417446c093ed6b4
      url: "https://pub.dev"
    source: hosted
    version: "8.9.5"
  camera:
    dependency: transitive
    description:
      name: camera
      sha256: dfa8fc5a1adaeb95e7a54d86a5bd56f4bb0e035515354c8ac6d262e35cec2ec8
      url: "https://pub.dev"
    source: hosted
    version: "0.10.6"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      sha256: "007c57cdcace4751014071e3d42f2eb8a64a519254abed35b714223d81d66234"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.10"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: c3f150a6151fb5586d97ce2ad369bd42f6ba65d6cc5097591c919b996ecddf1e
      url: "https://pub.dev"
    source: hosted
    version: "0.9.18+10"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: "953e7baed3a7c8fae92f7200afeb2be503ff1a17c3b4e4ed7b76f008c2810a31"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.5"
  capp_api:
    dependency: transitive
    description:
      path: "../../capp_api"
      relative: true
    source: path
    version: "0.0.1"
  capp_appsflyer_core:
    dependency: transitive
    description:
      path: "../../capp_appsflyer/capp_appsflyer_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_auth_core:
    dependency: transitive
    description:
      path: "../../capp_auth/capp_auth_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_bank_validation:
    dependency: transitive
    description:
      path: "../../capp_bank_validation"
      relative: true
    source: path
    version: "0.0.1"
  capp_bigdata:
    dependency: transitive
    description:
      path: "../../capp_bigdata/capp_bigdata_vn"
      relative: true
    source: path
    version: "0.0.1"
  capp_bigdata_core:
    dependency: transitive
    description:
      path: "../../capp_bigdata/capp_bigdata_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_cards_core:
    dependency: transitive
    description:
      path: "../../capp_cards/capp_cards_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_config_core:
    dependency: transitive
    description:
      path: "../../capp_config/capp_config_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_content:
    dependency: transitive
    description:
      path: "../../capp_content/capp_content"
      relative: true
    source: path
    version: "0.0.1"
  capp_content_core:
    dependency: transitive
    description:
      path: "../../capp_content/capp_content_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_credolab_core:
    dependency: transitive
    description:
      path: "../../capp_credolab/capp_credolab_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_datascore_core:
    dependency: transitive
    description:
      path: "../../capp_datascore/capp_datascore_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_deals_core:
    dependency: transitive
    description:
      path: "../../capp_deals/capp_deals_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_demo_core:
    dependency: transitive
    description:
      path: "../../capp_demo/capp_demo_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_direct_debit_core:
    dependency: transitive
    description:
      path: "../../capp_direct_debit/capp_direct_debit_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_domain:
    dependency: transitive
    description:
      path: "../../capp_domain"
      relative: true
    source: path
    version: "0.0.1"
  capp_evoucher_core:
    dependency: transitive
    description:
      path: "../../capp_evoucher/capp_evoucher_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_face_guard:
    dependency: transitive
    description:
      path: "../../capp_face_guard"
      relative: true
    source: path
    version: "0.0.1"
  capp_feature_flags:
    dependency: transitive
    description:
      path: "../../capp_feature_flags"
      relative: true
    source: path
    version: "0.0.1"
  capp_finbox_core:
    dependency: transitive
    description:
      path: "../../capp_finbox/capp_finbox_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_golden_core:
    dependency: transitive
    description:
      path: "../../capp_golden/capp_golden_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_home_core:
    dependency: transitive
    description:
      path: "../../capp_home/capp_home_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_hyperverge_core:
    dependency: transitive
    description:
      path: "../../capp_hyperverge/capp_hyperverge_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_innovatrics_core:
    dependency: transitive
    description:
      path: "../../capp_innovatrics/capp_innovatrics_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_integrity_api_core:
    dependency: transitive
    description:
      path: "../../capp_integrity_api/capp_integrity_api_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_legal_logs:
    dependency: transitive
    description:
      path: "../../capp_legal_logs/capp_legal_logs_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_legal_permission_core:
    dependency: transitive
    description:
      path: "../../capp_legal_permission/capp_legal_permission_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_live_activities_core:
    dependency: transitive
    description:
      path: "../../capp_live_activities/capp_live_activities_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_origination_unified_core:
    dependency: transitive
    description:
      path: "../../capp_loan_origination_unified/capp_loan_origination_unified_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_shared_core:
    dependency: transitive
    description:
      path: "../../capp_loan_shared/capp_loan_shared_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_loan_signature_core:
    dependency: transitive
    description:
      path: "../../capp_loan_signature/capp_loan_signature_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_onboarding_core:
    dependency: transitive
    description:
      path: "../../capp_onboarding/capp_onboarding_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_payment_core:
    dependency: transitive
    description:
      path: "../../capp_payment/capp_payment_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_personal_core:
    dependency: transitive
    description:
      path: "../../capp_personal/capp_personal_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_plugins_core:
    dependency: transitive
    description:
      path: "../../capp_plugins/capp_plugins_core"
      relative: true
    source: path
    version: "1.0.0+1"
  capp_products_core:
    dependency: transitive
    description:
      path: "../../capp_products/capp_products_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_repayment_core:
    dependency: "direct main"
    description:
      path: "../capp_repayment_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_self_service_core:
    dependency: transitive
    description:
      path: "../../capp_self_service/capp_self_service_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_tracking:
    dependency: transitive
    description:
      path: "../../capp_tracking"
      relative: true
    source: path
    version: "0.0.1"
  capp_transaction_history_core:
    dependency: transitive
    description:
      path: "../../capp_transaction_history/capp_transaction_history_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_transaction_signature_core:
    dependency: transitive
    description:
      path: "../../capp_transaction_signature/capp_transaction_signature_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_transactions_core:
    dependency: transitive
    description:
      path: "../../capp_transactions/capp_transactions_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_ui_core:
    dependency: transitive
    description:
      path: "../../capp_ui/capp_ui_core"
      relative: true
    source: path
    version: "0.0.1"
  capp_vas_core:
    dependency: transitive
    description:
      path: "../../capp_vas/capp_vas_core"
      relative: true
    source: path
    version: "0.0.1"
  carousel_slider:
    dependency: transitive
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clipboard:
    dependency: transitive
    description:
      name: clipboard
      sha256: "2ec38f0e59878008ceca0ab122e4bfde98847f88ef0f83331362ba4521f565a9"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: transitive
    description:
      name: cloud_firestore
      sha256: a31eec60eadaa859f0677bf661d9f86ed15961c716512f64884e59edcb341472
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: "3224e6158441c8897325e74f9971140cde2c85ee75a26704407a91b969b50829"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: c1312945cb7dd55921bcc10445f6c9a494bc04104b7d0821c3ed577b970ab088
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0ec10bf4a89e4c613960bf1e8b42c64127021740fb21640c29c909826a5eea3e"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  confetti:
    dependency: transitive
    description:
      name: confetti
      sha256: "979aafde2428c53947892c95eb244466c109c129b7eee9011f0a66caaca52267"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "04bf81bb0b77de31557b58d052b24b3eee33f09a6e7a8c68a3e247c7df19ec27"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.3"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: "42657c1715d48b167930d5f34d00222ac100475f73d10162ddf43e714932f204"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  converter:
    dependency: transitive
    description:
      name: converter
      sha256: "92bd8c7bf5ab3e083f7aad80ee5d587d265f70707807310384bd6c4a7bc57ada"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.6"
  cool_devtool:
    dependency: transitive
    description:
      name: cool_devtool
      sha256: "84911cde32e522c070e99cc934b9725113959ff7984ff2b6736a64b5c8d26e24"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  coverage:
    dependency: transitive
    description:
      name: coverage
      sha256: e3493833ea012784c740e341952298f1cc77f1f01b1bbc3eb4eecf6984fb7f43
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  credolab_core:
    dependency: transitive
    description:
      path: "../../../plugins/credolab/credolab_core"
      relative: true
    source: path
    version: "0.0.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  currency_text_input_formatter:
    dependency: transitive
    description:
      name: currency_text_input_formatter
      sha256: cfa1267c65d154a302d80372b3d8e157a83c3f61ab87ad03294466bdfd5ced6f
      url: "https://pub.dev"
    source: hosted
    version: "2.2.9"
  custom_refresh_indicator:
    dependency: transitive
    description:
      name: custom_refresh_indicator
      sha256: c34dd1dfb1f6b9ee2db9c5972586dba5e4445d79f8431f6ab098a6e963ccd39c
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  dart_code_metrics:
    dependency: transitive
    description:
      path: "."
      ref: ebfa4c53bc62ff88eaca414043ffe571027ff7ac
      resolved-ref: ebfa4c53bc62ff88eaca414043ffe571027ff7ac
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-code-metrics"
    source: git
    version: "5.7.6"
  dart_code_metrics_presets:
    dependency: transitive
    description:
      name: dart_code_metrics_presets
      sha256: b71eadf02a3787ebd5c887623f83f6fdc204d45c75a081bd636c4104b3fd8b73
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  dart_ipify:
    dependency: transitive
    description:
      name: dart_ipify
      sha256: "3b70d589504126107e81ad0703d91394cc8e2039cb0a11ebd92b9b824a5e9561"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  dartz:
    dependency: transitive
    description:
      name: dartz
      sha256: e6acf34ad2e31b1eb00948692468c30ab48ac8250e0f0df661e29f12dd252168
      url: "https://pub.dev"
    source: hosted
    version: "0.10.1"
  datascore_core:
    dependency: transitive
    description:
      path: "../../../plugins/datascore/datascore_core"
      relative: true
    source: path
    version: "0.0.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.11"
  decimal:
    dependency: transitive
    description:
      name: decimal
      sha256: "24a261d5d5c87e86c7651c417a5dbdf8bcd7080dd592533910e8d0505a279f21"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  device_calendar:
    dependency: transitive
    description:
      name: device_calendar
      sha256: "683fb93ec302b6a65c0ce57df40ff9dcc2404f59c67a2f8b93e59318c8a0a225"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.3"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  diacritic:
    dependency: transitive
    description:
      name: diacritic
      sha256: "12981945ec38931748836cd76f2b38773118d0baef3c68404bdfde9566147876"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  diff_match_patch:
    dependency: transitive
    description:
      name: diff_match_patch
      sha256: "2efc9e6e8f449d0abe15be240e2c2a3bcd977c8d126cfd70598aee60af35c0a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.1"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "7d328c4d898a61efc3cd93655a0955858e29a0aa647f0f9e02d59b3bb275e2e8"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  dio_cache_interceptor:
    dependency: transitive
    description:
      path: "../../../plugins/dio_cache_interceptor"
      relative: true
    source: path
    version: "0.6.0"
  dots_indicator:
    dependency: transitive
    description:
      name: dots_indicator
      sha256: f1599baa429936ba87f06ae5f2adc920a367b16d08f74db58c3d0f6e93bcdb5c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  dotted_decoration:
    dependency: transitive
    description:
      name: dotted_decoration
      sha256: a5c5771367690b4f64ebfa7911954ab472b9675f025c373f514e32ac4bb81d5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  dynamic_forms:
    dependency: transitive
    description:
      path: "../../../plugins/flutter_dynamic_forms/packages/dynamic_forms"
      relative: true
    source: path
    version: "1.0.0"
  dynamic_forms_generator:
    dependency: "direct dev"
    description:
      path: "../../../plugins/flutter_dynamic_forms/packages/dynamic_forms_generator"
      relative: true
    source: path
    version: "1.0.0"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  expandable:
    dependency: transitive
    description:
      name: expandable
      sha256: "9604d612d4d1146dafa96c6d8eec9c2ff0994658d6d09fed720ab788c7f5afc2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  expression_language:
    dependency: transitive
    description:
      path: "../../../plugins/flutter_dynamic_forms/packages/expression_language"
      relative: true
    source: path
    version: "1.0.1"
  extended_image:
    dependency: transitive
    description:
      name: extended_image
      sha256: "69d4299043334ecece679996e47d0b0891cd8c29d8da0034868443506f1d9a78"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.1"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: e61dafd94400fff6ef7ed1523d445ff3af137f198f3228e4a3107bc5b4bec5d1
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  faker:
    dependency: transitive
    description:
      name: faker
      sha256: "544c34e9e1d322824156d5a8d451bc1bb778263b892aded24ec7ba77b0706624"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_saver:
    dependency: transitive
    description:
      path: "../../../forks/file_saver"
      relative: true
    source: path
    version: "0.2.9"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "320fcfb6f33caa90f0b58380489fc5ac05d99ee94b61aa96ec2bff0ba81d3c2b"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+4"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: "064e5b57b0693305946b7caa6a80ed80a918f46804c247b6cd7ed9cd327df48f"
      url: "https://pub.dev"
    source: hosted
    version: "11.2.1"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: d094547c9022c404b5ca39b7209607fc80e75e39d38875f050508fa4346b3e74
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "06dc023b0144c0df630a56b6262cc9e7d6069fe78148853d97614dbefb6ea923"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.9+1"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "3187f4f8e49968573fd7403011dca67ba95aae419bc0d8131500fae160d94f92"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "3c3a1e92d6f4916c32deea79c4a7587aa0e9dbbe5889c7a16afcf005a485ee02"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: e8d1e22de72cb21cdcfc5eed7acddab3e99cd83f3b317f54f7a96c32f25fd11e
      url: "https://pub.dev"
    source: hosted
    version: "2.17.4"
  firebase_crashlytics:
    dependency: transitive
    description:
      name: firebase_crashlytics
      sha256: "30260e1b8ad1464b41ca4531b44ce63d752daaf2f12c92ca6cdcd82b270abecc"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: a75e1826d92ea4e86e4a753c7b5d64b844a362676fa653185f1581c859186d18
      url: "https://pub.dev"
    source: hosted
    version: "3.6.40"
  firebase_dynamic_links:
    dependency: transitive
    description:
      name: firebase_dynamic_links
      sha256: f094b1f90951981328abdb39c4140c0b0590c8d56fe23a9ad987b654a33000d0
      url: "https://pub.dev"
    source: hosted
    version: "6.0.4"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: a9af616ec8e33c739b12153c420d16d6987532e13c8d764a0f20a64031bb93f1
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6+40"
  firebase_messaging:
    dependency: transitive
    description:
      name: firebase_messaging
      sha256: "1b0a4f9ecbaf9007771bac152afad738ddfacc4b8431a7591c00829480d99553"
      url: "https://pub.dev"
    source: hosted
    version: "15.0.4"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: c5a6443e66ae064fe186901d740ee7ce648ca2a6fd0484b8c5e963849ac0fc28
      url: "https://pub.dev"
    source: hosted
    version: "4.5.42"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "232ef63b986467ae5b5577a09c2502b26e2e2aebab5b85e6c966a5ca9b038b89"
      url: "https://pub.dev"
    source: hosted
    version: "3.8.12"
  firebase_performance:
    dependency: transitive
    description:
      name: firebase_performance
      sha256: "6d17133458b9627f15f278d6f71bebbbce885d393f3462b690e55deeb5c36b90"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.0+4"
  firebase_performance_platform_interface:
    dependency: transitive
    description:
      name: firebase_performance_platform_interface
      sha256: "28dc0a70a3459fe51d1c1be5754803a9a0db0e210322ec7526f6ce42bf6ad83e"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+40"
  firebase_performance_web:
    dependency: transitive
    description:
      name: firebase_performance_web
      sha256: db91d86b34280f5253d2913945fdd51d7114486584a298a7bedf1c4b2ab08f79
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6+12"
  firebase_remote_config:
    dependency: transitive
    description:
      name: firebase_remote_config
      sha256: "62e86ed64370c382a2f872fbcabcae591c404776eb84685eb535bab53c0c00d5"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.4"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "80973fa763b7c9a0fc0596afed7063f2378de2cf2d37b017254e613160b43135"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.40"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "14ba362bdcf7abda12fa9060f2ebae7d342153e4d619007071e98cd557ce29a3"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.12"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flagsmith:
    dependency: transitive
    description:
      path: "../../../forks/flagsmith"
      relative: true
    source: path
    version: "2.0.0-fork.4"
  flare_flutter:
    dependency: transitive
    description:
      name: flare_flutter
      sha256: "99d63c60f00fac81249ce6410ee015d7b125c63d8278a30da81edf3317a1f6a0"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flip_card:
    dependency: transitive
    description:
      name: flip_card
      sha256: "5d4aa58f3983cced0782f4ce45826b7eea36e8e464964d9209dcbc7a87b2292f"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_app_badger:
    dependency: transitive
    description:
      path: "../../../forks/flutter_app_badger"
      relative: true
    source: path
    version: "1.5.0"
  flutter_arc_text:
    dependency: transitive
    description:
      name: flutter_arc_text
      sha256: "38e9cda592ea61af4c7f2446641416580240da4be8a5427a99bf70045533ad16"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  flutter_bloc:
    dependency: transitive
    description:
      name: flutter_bloc
      sha256: b594505eac31a0518bdcb4b5b79573b8d9117b193cc80cc12e17d639b10aa27a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.6"
  flutter_custom_tabs:
    dependency: transitive
    description:
      name: flutter_custom_tabs
      sha256: "34167bd15fa3479855c011f868e0789c9569c12b64358ca7250accc5a24c3312"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_android:
    dependency: transitive
    description:
      name: flutter_custom_tabs_android
      sha256: cf06fde8c002f326dc6cbf69ee3f97c3feead4436229da02d2e2aa39d5a5dbf4
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_ios:
    dependency: transitive
    description:
      name: flutter_custom_tabs_ios
      sha256: ef2de533bc45fb84fefc3854bc8b1e43001671c6bc6bc55faa57942eecd3f70a
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_platform_interface:
    dependency: transitive
    description:
      name: flutter_custom_tabs_platform_interface
      sha256: e18e9b08f92582123bdb84fb6e4c91804b0579700fed6f887d32fd9a1e96a5d5
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_custom_tabs_web:
    dependency: transitive
    description:
      name: flutter_custom_tabs_web
      sha256: "08ae322b11e1972a233d057542279873d0f9d1d5f8159c2c741457239d9d562c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_document_picker:
    dependency: transitive
    description:
      name: flutter_document_picker
      sha256: "5229e22fb9ac7939c516b56736714bb92305762cf4027f34aaf9edda229c5004"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.3"
  flutter_dynamic_forms:
    dependency: transitive
    description:
      path: "../../../plugins/flutter_dynamic_forms/packages/flutter_dynamic_forms"
      relative: true
    source: path
    version: "1.0.0"
  flutter_dynamic_forms_components:
    dependency: transitive
    description:
      path: "../../../plugins/flutter_dynamic_forms/packages/flutter_dynamic_forms_components"
      relative: true
    source: path
    version: "1.0.0"
  flutter_image_compress:
    dependency: transitive
    description:
      name: flutter_image_compress
      sha256: "45a3071868092a61b11044c70422b04d39d4d9f2ef536f3c5b11fb65a1e7dd90"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  flutter_image_compress_common:
    dependency: transitive
    description:
      name: flutter_image_compress_common
      sha256: c5c5d50c15e97dd7dc72ff96bd7077b9f791932f2076c5c5b6c43f2c88607bfb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  flutter_image_compress_macos:
    dependency: transitive
    description:
      name: flutter_image_compress_macos
      sha256: "20019719b71b743aba0ef874ed29c50747461e5e8438980dfa5c2031898f7337"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  flutter_image_compress_ohos:
    dependency: transitive
    description:
      name: flutter_image_compress_ohos
      sha256: e76b92bbc830ee08f5b05962fc78a532011fcd2041f620b5400a593e96da3f51
      url: "https://pub.dev"
    source: hosted
    version: "0.0.3"
  flutter_image_compress_platform_interface:
    dependency: transitive
    description:
      name: flutter_image_compress_platform_interface
      sha256: "579cb3947fd4309103afe6442a01ca01e1e6f93dc53bb4cbd090e8ce34a41889"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_web:
    dependency: transitive
    description:
      name: flutter_image_compress_web
      sha256: f02fe352b17f82b72f481de45add240db062a2585850bea1667e82cc4cd6c311
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4+1"
  flutter_inappwebview:
    dependency: transitive
    description:
      path: "../../../forks/flutter_inappwebview/flutter_inappwebview"
      relative: true
    source: path
    version: "6.0.0"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      path: "../../../forks/flutter_inappwebview/flutter_inappwebview_android"
      relative: true
    source: path
    version: "1.0.13"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "787171d43f8af67864740b6f04166c13190aa74a1468a1f1f1e9ee5b90c359cd"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      path: "../../../forks/flutter_inappwebview/flutter_inappwebview_ios"
      relative: true
    source: path
    version: "1.0.13"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      path: "../../../forks/flutter_inappwebview/flutter_inappwebview_macos"
      relative: true
    source: path
    version: "1.0.11"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      path: "../../../forks/flutter_inappwebview/flutter_inappwebview_platform_interface"
      relative: true
    source: path
    version: "1.0.11"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      path: "../../../forks/flutter_inappwebview/flutter_inappwebview_web"
      relative: true
    source: path
    version: "1.0.8"
  flutter_libphonenumber:
    dependency: transitive
    description:
      path: "../../../forks/flutter_libphonenumber"
      relative: true
    source: path
    version: "1.1.0"
  flutter_local_notifications:
    dependency: transitive
    description:
      path: "../../../forks/flutter_local_notifications"
      relative: true
    source: path
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_markdown:
    dependency: transitive
    description:
      name: flutter_markdown
      sha256: "04c4722cc36ec5af38acc38ece70d22d3c2123c61305d555750a091517bbe504"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.23"
  flutter_native_contact_picker:
    dependency: transitive
    description:
      path: "../../../forks/flutter_native_contact_picker"
      relative: true
    source: path
    version: "0.0.4"
  flutter_pin_encryption:
    dependency: transitive
    description:
      path: "../../../plugins/flutter_pin_encryption"
      relative: true
    source: path
    version: "0.0.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "1c2b787f99bdca1f3718543f81d38aa1b124817dfeb9fb196201bea85b6134bf"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.26"
  flutter_scroll_shadow:
    dependency: transitive
    description:
      name: flutter_scroll_shadow
      sha256: "9ec6a566831a7251b4ffbee0a3691fbe02d3dd16a9d26c96838c02958f27d456"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.6"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: ffdbb60130e4665d2af814a0267c481bcf522c41ae2e43caf69fa0146876d685
      url: "https://pub.dev"
    source: hosted
    version: "9.0.0"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: bf7404619d7ab5c0a1151d7c4e802edad8f33535abfbeff2f9e1fe1274e2d705
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "6c0a2795a2d1de26ae202a0d78527d163f4acbb11cde4c75c670f3a0fc064247"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: cf91ad32ce5adef6fba4d736a542baca9daf3beac4db2d04be350b87f69ac4a8
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: f4ebff989b4f07b2656fb16b47852c0aab9fed9b4ec1c70103368337bc1886a9
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: b20b07cb5ed4ed74fc567b78a72936203f587eba460af1df11281c9326cd3709
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  flutter_sticky_header:
    dependency: transitive
    description:
      name: flutter_sticky_header
      sha256: "017f398fbb45a589e01491861ca20eb6570a763fd9f3888165a978e11248c709"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: d44bf546b13025ec7353091516f6881f1d4c633993cb109c3916c3a0159dadf1
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_timezone:
    dependency: transitive
    description:
      name: flutter_timezone
      sha256: bc286cecb0366d88e6c4644e3962ebd1ce1d233abc658eb1e0cd803389f84b64
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_windowmanager_plus:
    dependency: transitive
    description:
      name: flutter_windowmanager_plus
      sha256: "4e2bf7c7f374699fd74d59785f1d74efd40052c24a5edde5a4d825cc72608d40"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  fluttertoast:
    dependency: transitive
    description:
      name: fluttertoast
      sha256: "81b68579e23fcbcada2db3d50302813d2371664afe6165bc78148050ab94bf66"
      url: "https://pub.dev"
    source: hosted
    version: "8.2.5"
  freezed:
    dependency: "direct dev"
    description:
      name: freezed
      sha256: a9520490532087cf38bf3f7de478ab6ebeb5f68bb1eb2641546d92719b224445
      url: "https://pub.dev"
    source: hosted
    version: "2.3.5"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: aeac15850ef1b38ee368d4c53ba9a847e900bb2c53a4db3f6881cbb3cb684338
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  geolocator:
    dependency: transitive
    description:
      name: geolocator
      sha256: f4efb8d3c4cdcad2e226af9661eb1a0dd38c71a9494b22526f9da80ab79520e5
      url: "https://pub.dev"
    source: hosted
    version: "10.1.1"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: f15d1536cd01b1399578f1da1eb5d566e7a718db6a3648f2c24d2e2f859f0692
      url: "https://pub.dev"
    source: hosted
    version: "4.5.4"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "419e50f754281d3606750af07b198ecfe938e8648d3e30a898d3ac342ab717e6"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.12"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "722db30c0a2caa82a59d6655f04ef0a492da003036b880342cc67e6f1abc188f"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.5"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "102e7da05b48ca6bf0a5bda0010f886b171d1a08059f01bfe02addd0175ebece"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "4862e798b8a84ec300531888e7acd137b74637636069df230d79fabd110e2734"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.4"
  get_it:
    dependency: transitive
    description:
      name: get_it
      sha256: d85128a5dae4ea777324730dc65edd9c9f43155c109d5cc0a69cab74139fbac1
      url: "https://pub.dev"
    source: hosted
    version: "7.7.0"
  getwidget:
    dependency: transitive
    description:
      name: getwidget
      sha256: "91df14a8d80e21f3ec02759295b90cc8badb8a872b90d34ad4aeb4085d833b5c"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: c3f1ee72c96f8f78935e18aa8cecced9ab132419e8625dc187e1c2408efc20de
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  gma_lints:
    dependency: "direct dev"
    description:
      path: "../../gma_lints"
      relative: true
    source: path
    version: "1.0.1"
  gma_map_core:
    dependency: transitive
    description:
      path: "../../gma_map/gma_map_core"
      relative: true
    source: path
    version: "0.0.1"
  gma_pdf_core:
    dependency: transitive
    description:
      path: "../../gma_pdf/gma_pdf_core"
      relative: true
    source: path
    version: "0.0.1"
  gma_platform:
    dependency: transitive
    description:
      path: "../../gma_platform"
      relative: true
    source: path
    version: "0.0.1"
  gma_storage:
    dependency: transitive
    description:
      path: "../../gma_storage"
      relative: true
    source: path
    version: "1.0.0"
  gma_vault:
    dependency: transitive
    description:
      path: "../../../plugins/gma_vault"
      relative: true
    source: path
    version: "0.0.1"
  google_api_availability:
    dependency: transitive
    description:
      name: google_api_availability
      sha256: "2ffdc91e1e0cf4e7974fef6c2988a24cefa81f03526ff04b694df6dc0fcbca03"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  google_api_availability_android:
    dependency: transitive
    description:
      name: google_api_availability_android
      sha256: "4794147f43a8f3eee6b514d3ae30dbe6f7b9048cae8cd2a74cb4055cd28d74a8"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  google_api_availability_platform_interface:
    dependency: transitive
    description:
      name: google_api_availability_platform_interface
      sha256: "65b7da62fe5b582bb3d508628ad827d36d890710ea274766a992a56fa5420da6"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  google_fonts:
    dependency: transitive
    description:
      name: google_fonts
      sha256: b1ac0fe2832c9cc95e5e88b57d627c5e68c223b9657f4b96e1487aa9098c7b82
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "4d6e199c561ca06792c964fa24b2bac7197bf4b401c2e1d23e345e5f9939f531"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.1"
  google_maps_flutter:
    dependency: transitive
    description:
      name: google_maps_flutter
      sha256: "621125e35e81ca39ef600e45243d2be93167e61def72bc7207b0c4a635c58506"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: "721ffae2240e957c04b0de19ffd4b68580adb57a8224496b7fb55fad23aec98a"
      url: "https://pub.dev"
    source: hosted
    version: "2.14.13"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: "7b5663bfcfbbe8ff96f394ba351b53fa00a8ba75cfc894b790426df4e2d888d6"
      url: "https://pub.dev"
    source: hosted
    version: "2.14.0"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: "970c8f766c02909c7be282dea923c971f83a88adaf07f8871d0aacebc3b07bb2"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.1"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: a45786ea6691cc7cdbe2cf3ce2c2daf4f82a885745666b4a36baada3a4e12897
      url: "https://pub.dev"
    source: hosted
    version: "0.5.12"
  google_mlkit_barcode_scanning:
    dependency: transitive
    description:
      name: google_mlkit_barcode_scanning
      sha256: f1a2a39cf1730b9a5e2784a07efa1ca5bfdfdde6aa00b193b3f8cd1953c638ec
      url: "https://pub.dev"
    source: hosted
    version: "0.12.0"
  google_mlkit_commons:
    dependency: transitive
    description:
      name: google_mlkit_commons
      sha256: "27d626c66a181351a953eba5b6ff1ff123aadb891b4dab085b292118f039d6ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.1"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  grouped_list:
    dependency: transitive
    description:
      name: grouped_list
      sha256: fef106470186081c32636aa055492eee7fc7fe8bf0921a48d31ded24821af19f
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  gtk:
    dependency: transitive
    description:
      name: gtk
      sha256: e8ce9ca4b1df106e4d72dad201d345ea1a036cc12c360f1a7d5a758f78ffa42c
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  hashcodes:
    dependency: transitive
    description:
      name: hashcodes
      sha256: "80f9410a5b3c8e110c4b7604546034749259f5d6dcca63e0d3c17c9258f1a651"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  hive_ce:
    dependency: transitive
    description:
      name: hive_ce
      sha256: ac66daee46ad46486a1ed12cf91e9d7479c875fb46889be8d2c96b557406647f
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  hive_ce_flutter:
    dependency: transitive
    description:
      name: hive_ce_flutter
      sha256: e74e11d3ed622630ff1ee758037dab21a59d2182fa7b61378726c673742d2ab9
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "1fc58edeaec4307368c60d59b7e15b9d658b57d7f3125098b6294153c75337ec"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.5"
  http:
    dependency: transitive
    description:
      name: http
      sha256: fe7ab022b76f3034adc518fb6ea04a82387620e19977665ea18d30a1cf43442f
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: aa6199f908078bb1c5efb8d8638d4ae191aac11b311132c3ef48ce352fb52ef8
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  idb_shim:
    dependency: transitive
    description:
      name: idb_shim
      sha256: "11f4ba861df756b60ef2f799dd5540e77f27cf4a44d9e5f6127ad47d67f65602"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1+2"
  identityapi:
    dependency: transitive
    description:
      path: "public/identityapi"
      ref: ff6540d40cf259de0a4eb5b3a767192f4106277d
      resolved-ref: ff6540d40cf259de0a4eb5b3a767192f4106277d
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients"
    source: git
    version: "1.0.0"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "8e9d133755c3e84c73288363e6343157c383a0c6c56fc51afcc5d4d7180306d6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  image_gallery_saver:
    dependency: transitive
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  image_picker:
    dependency: transitive
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "82652a75e3dd667a91187769a6a2cc81bd8c111bbead698d8e938d2b63e5e89a"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+21"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "05da758e67bc7839e886b3959848aa6b44ff123ab4b28f67891008afe8ef9100"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+2"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "34a65f6740df08bbbeb0a1abd8e6d32107941fd4868f67a507b25601651022c9"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "1b90ebbd9dcf98fb6c1d01427e49a55bd96b5d67b8c67cf955d60a5de74207c1"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_size_getter:
    dependency: transitive
    description:
      name: image_size_getter
      sha256: "414ebd27e9967fa7adbcf4a10c8409522faa6f98c87e3e54d2c423db8ad75320"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  in_app_review:
    dependency: transitive
    description:
      name: in_app_review
      sha256: "36a06771b88fb0e79985b15e7f2ac0f1142e903fe72517f3c055d78bc3bc1819"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.10"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: fed2c755f2125caa9ae10495a3c163aa7fab5af3585a9c62ef4a6920c5b45f10
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  installed_app_detector:
    dependency: transitive
    description:
      path: "../../../plugins/installed_app_detector"
      relative: true
    source: path
    version: "0.0.1"
  installer_checker:
    dependency: transitive
    description:
      path: "../../../plugins/installer_checker"
      relative: true
    source: path
    version: "0.0.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  invertible:
    dependency: transitive
    description:
      name: invertible
      sha256: "2e88300b67085adf5e766839a49e3a8da7db85d8826a9068bba24e015b79381a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: dfd5a80599cf0165756e3181807ed3e77daf6dd4137caaad72d0b7931597650b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  ios_teamid:
    dependency: transitive
    description:
      name: ios_teamid
      sha256: af8cfe090c583612b61855eee4c83bf10b77c980bf8682ce0c54efa685037404
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: aa1f5a8912615733e0fdc7a02af03308933c93235bdc8d50d0b0c8a8ccb0b969
      url: "https://pub.dev"
    source: hosted
    version: "6.7.1"
  jwt_decoder:
    dependency: transitive
    description:
      name: jwt_decoder
      sha256: "54774aebf83f2923b99e6416b4ea915d47af3bde56884eb622de85feabbc559f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  koyal_auth:
    dependency: transitive
    description:
      path: "../../koyal_auth"
      relative: true
    source: path
    version: "0.0.1"
  koyal_camera:
    dependency: transitive
    description:
      path: "../../koyal_camera"
      relative: true
    source: path
    version: "0.0.1"
  koyal_core:
    dependency: transitive
    description:
      path: "../../koyal_core"
      relative: true
    source: path
    version: "0.0.1"
  koyal_dynamic_forms:
    dependency: transitive
    description:
      path: "../../koyal_dynamic_forms"
      relative: true
    source: path
    version: "0.0.1"
  koyal_installer_checker:
    dependency: transitive
    description:
      path: "../../koyal_installer_checker"
      relative: true
    source: path
    version: "0.0.1"
  koyal_localizations:
    dependency: transitive
    description:
      path: "../../koyal_localizations"
      relative: true
    source: path
    version: "0.0.1"
  koyal_lock:
    dependency: transitive
    description:
      path: "../../koyal_lock"
      relative: true
    source: path
    version: "0.0.1"
  koyal_machine_learning:
    dependency: transitive
    description:
      path: "../../koyal_machine_learning"
      relative: true
    source: path
    version: "0.0.1"
  koyal_messaging:
    dependency: transitive
    description:
      path: "../../koyal_messaging"
      relative: true
    source: path
    version: "0.0.1"
  koyal_navigation_annotation:
    dependency: transitive
    description:
      path: "../../../plugins/koyal_navigation_annotation"
      relative: true
    source: path
    version: "1.0.0"
  koyal_otp:
    dependency: transitive
    description:
      path: "../../koyal_otp"
      relative: true
    source: path
    version: "0.0.1"
  koyal_shared:
    dependency: transitive
    description:
      path: "../../koyal_shared/koyal_shared"
      relative: true
    source: path
    version: "0.0.1"
  koyal_shared_core:
    dependency: transitive
    description:
      path: "../../koyal_shared/koyal_shared_core"
      relative: true
    source: path
    version: "0.0.1"
  koyal_testing:
    dependency: transitive
    description:
      path: "../../koyal_testing"
      relative: true
    source: path
    version: "0.0.1"
  launch_app_store:
    dependency: transitive
    description:
      name: launch_app_store
      sha256: "2334891556eebe35689ac2eda9791b93d0a29803a2642714d34cefaac2a1e155"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "3f87a60e8c63aecc975dda1ceedbc8f24de75f09e4856ea27daf8958f2f0ce05"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.5"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "932549fb305594d82d7183ecd9fa93463e9914e1b67cacc34bc40906594a1806"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  linkify:
    dependency: transitive
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  liquid_pull_to_refresh:
    dependency: transitive
    description:
      name: liquid_pull_to_refresh
      sha256: "11e4cd8c5460085a31b479ec4e1cd063eb8e599f35684d57a44dafa1fd1f67f3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  local_auth:
    dependency: transitive
    description:
      name: local_auth
      sha256: "434d854cf478f17f12ab29a76a02b3067f86a63a6d6c4eb8fbfdcfe4879c1b7b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      sha256: e0e5b1ea247c5a0951c13a7ee13dc1beae69750e6a2e1910d1ed6a3cd4d56943
      url: "https://pub.dev"
    source: hosted
    version: "1.0.38"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "630996cd7b7f28f5ab92432c4b35d055dd03a747bc319e5ffbb3c4806a3e50d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "1b842ff177a7068442eae093b64abe3592f816afd2a533c0ebcdbe40f9d2075a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: bc4e66a29b0fdf751aafbec923b5bed7ad6ed3614875d8151afe2578520b2ab5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: db2ff852ed77090ba9f62d3611e4208a3d11dfa35991a81ae724c113fcb3e3f7
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  logger_flutter:
    dependency: transitive
    description:
      path: "../../../forks/logger_flutter"
      relative: true
    source: path
    version: "0.9.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: "893da7a0022ec2fcaa616f34529a081f617e86cc501105b856e5a3184c58c7c2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  maps_launcher:
    dependency: transitive
    description:
      name: maps_launcher
      sha256: "57ba3c31db96e30f58c23fcb22a1fac6accc5683535b2cf344c534bbb9f8f910"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: "935e23e1ff3bc02d390bad4d4be001208ee92cc217cb5b5a6c19bc14aaa318c1"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  mask_text_input_formatter:
    dependency: transitive
    description:
      name: mask_text_input_formatter
      sha256: "978c58ec721c25621ceb468e633f4eef64b64d45424ac4540e0565d4f7c800cd"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  memory_info:
    dependency: transitive
    description:
      name: memory_info
      sha256: "2d2150e070e1ceeb44d9ac3edad3d03d19022c4e0a4ddb08a3f8a494fc41d1c4"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  memory_info_platform_interface:
    dependency: transitive
    description:
      name: memory_info_platform_interface
      sha256: "07c5ad0625f9810fb378fabd69a38c19a800865e11433e767804ff9bd84796db"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  merge_images:
    dependency: transitive
    description:
      name: merge_images
      sha256: d8162a42642d88d447e11f887394261fb213aac0523111ccd48f0d815d10a9b3
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0-nullsafety"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: bdb68674043280c3428e9ec998512fb681678676b3c54e773629ffe74419f8c7
      url: "https://pub.dev"
    source: hosted
    version: "1.15.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  mobilecommonapi:
    dependency: transitive
    description:
      path: "public/mobilecommonapi"
      ref: f98979b2c59590ea38ffd151c8fe8b11bc5624b7
      resolved-ref: f98979b2c59590ea38ffd151c8fe8b11bc5624b7
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients"
    source: git
    version: "1.0.0"
  mocktail:
    dependency: transitive
    description:
      name: mocktail
      sha256: dd85ca5229cf677079fd9ac740aebfc34d9287cdf294e6b2ba9fae25c39e4dc2
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  multiple_localization:
    dependency: transitive
    description:
      name: multiple_localization
      sha256: "8b071f538bdf087a7bf13cd8b78a1a041994960c8e9f0a1aaaaf0cfb39845019"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  navigation_history_observer:
    dependency: transitive
    description:
      name: navigation_history_observer
      sha256: "5e0b94ebda685ff763c4c04f832bd21d4441a00b7d9dd9f99a171d98eceebac4"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  navigator:
    dependency: transitive
    description:
      path: "../../navigator"
      relative: true
    source: path
    version: "0.0.1"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  nock:
    dependency: transitive
    description:
      name: nock
      sha256: e5bb7dae9c94405477988cf3bd8a94ef141abd74577125d6a51d47909ff107f9
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  node_preamble:
    dependency: transitive
    description:
      name: node_preamble
      sha256: "6e7eac89047ab8a8d26cf16127b5ed26de65209847630400f9aefd7cd5c730db"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  onepay_custom_deeplink:
    dependency: transitive
    description:
      path: "../../../plugins/onepay_custom_deeplink"
      relative: true
    source: path
    version: "0.0.1"
  open_filex:
    dependency: transitive
    description:
      name: open_filex
      sha256: "854aefd72dfd74219dc8c8d1767c34ec1eae64b8399a5be317bddb1ec2108915"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.2"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: f096c55ebb7deb7e384101542bfba8c52696c1b56fca2eb62827989ef2353bbc
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: "7976bfe4c583170d6cdc7077e3237560b364149fcd268b5f53d95a991963b191"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "6c935fb612dff8e3cc9632c2b301720c77450a126114126ffaafe28d2e87956c"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "4adf4fd5423ec60a29506c76581bc05854c55e3a0b72d35bb28d661c9686edf2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.15"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  percent_indicator:
    dependency: transitive
    description:
      name: percent_indicator
      sha256: "0d77d5c6fa9b7f60202cedf748b568ba9ba38d3f30405d6ceae4da76f5185462"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: "59adad729136f01ea9e35a48f5d1395e25cba6cea552249ddbe9cf950f5d7849"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d3971dcdd76182a0c198c096b5db2f0884b0d4196723d21a866fc4cdea057ebc
      url: "https://pub.dev"
    source: hosted
    version: "12.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f84a188e79a35c687c132a0a0556c254747a08561e99ab933f12f6ca71ef3c98
      url: "https://pub.dev"
    source: hosted
    version: "9.4.6"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "38f000e83355abb3392140f6bc3030660cfaef189e1f87824facb76300b4ff24"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+5"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: eb99b295153abce5d683cac8c02e22faab63e50679b937fa1bf67d58bb282878
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  pin_input_text_field:
    dependency: transitive
    description:
      name: pin_input_text_field
      sha256: f45683032283d30b670ec343781660655e3e1953438b281a0bc6e2d358486236
      url: "https://pub.dev"
    source: hosted
    version: "4.5.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.dev"
    source: hosted
    version: "3.9.1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  pretty_dio_logger:
    dependency: transitive
    description:
      name: pretty_dio_logger
      sha256: "948f7eeb36e7aa0760b51c1a8e3331d4b21e36fabd39efca81f585ed93893544"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0-beta-1"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "107d8be718f120bbba9dcd1e95e3bd325b1b4a4f07db64154635ba03f2567a0d"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  progress_dialog:
    dependency: transitive
    description:
      path: "../../../forks/progress_dialog"
      relative: true
    source: path
    version: "1.2.4-nullsafety"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  pub_updater:
    dependency: transitive
    description:
      name: pub_updater
      sha256: "54e8dc865349059ebe7f163d6acce7c89eb958b8047e6d6e80ce93b13d7c9e60"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "81876843eb50dc2e1e5b151792c9a985c5ed2536914115ed04e9c8528f6647b0"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5c4208b4dc0d55c3184d10d83ee0ded6212dc2b5e2ba17c5a0c0aab279128d21"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: transitive
    description:
      name: qr_flutter
      sha256: c5c121c54cb6dd837b9b9d57eb7bc7ec6df4aee741032060c8833a678c80b87e
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  rational:
    dependency: transitive
    description:
      name: rational
      sha256: cb808fb6f1a839e6fc5f7d8cb3b0a10e1db48b3be102de73938c627f0b636336
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  restart_app:
    dependency: transitive
    description:
      name: restart_app
      sha256: "16edcc213625bf4d94cf7f5ae38e08d816473ee44e86d26f2f4b1a998db51cfb"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  retrofit:
    dependency: transitive
    description:
      name: retrofit
      sha256: "9254ec985d5e26a839a9070ae25b98f0781c9c420e4241c5fb8b8965aa1fc7f2"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5d22055fd443806c03ef24a02000637cf51eae49c2a0168d38a43fc166b0209c"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.5"
  safe_device:
    dependency: transitive
    description:
      name: safe_device
      sha256: "953aeac3486180df9118a1a3f5fb842d84015e8aa6f2607edeb5fb881b67a669"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "12669c4a913688a26555323fb9cec373d8f9fbe091f2d01c40c723b33caa8989"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  scrollable_positioned_list:
    dependency: transitive
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  secure_application:
    dependency: transitive
    description:
      name: secure_application
      sha256: b8e34b4bc2467a3a3c0a649e46ae6a442df7ca27aeaddebb8a53c40656da0385
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  selfcareapi:
    dependency: transitive
    description:
      path: "public/selfcareapi"
      ref: "37954c3482f43d8e6544a342be7f3a84980026c4"
      resolved-ref: "37954c3482f43d8e6544a342be7f3a84980026c4"
      url: "https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients"
    source: git
    version: "1.0.0"
  sembast:
    dependency: transitive
    description:
      name: sembast
      sha256: "9a9f0c7aca07043fef857b8b365f41592e48832b61462292699b57978e241c11"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.0"
  sembast_web:
    dependency: transitive
    description:
      name: sembast_web
      sha256: "1ba0402b9491b76312358ec3cb3a33b9fc22eb673d4f9a3e99b2425135add72c"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  sensors_plus:
    dependency: transitive
    description:
      name: sensors_plus
      sha256: "905282c917c6bb731c242f928665c2ea15445aa491249dea9d98d7c79dc8fd39"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  sensors_plus_platform_interface:
    dependency: transitive
    description:
      name: sensors_plus_platform_interface
      sha256: "58815d2f5e46c0c41c40fb39375d3f127306f7742efe3b891c0b1c87e2b5cd5d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  share_plus:
    dependency: transitive
    description:
      name: share_plus
      sha256: fb5319f3aab4c5dda5ebb92dca978179ba21f8c783ee4380910ef4c1c6824f51
      url: "https://pub.dev"
    source: hosted
    version: "8.0.3"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "846849e3e9b68f3ef4b60c60cf4b3e02e9321bc7f4d8c4692cf87ffa82fc8a3a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "9f9f3d372d4304723e6136663bb291c0b93f5e4c8a4a6314347f481a33bda2b1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.7"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: c49bd060261c9a3f0ff445892695d6212ff603ef3115edbb448509d407600019
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_packages_handler:
    dependency: transitive
    description:
      name: shelf_packages_handler
      sha256: "89f967eca29607c933ba9571d838be31d67f53f6e4ee15147d5dc2934fee1b1e"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  shelf_static:
    dependency: transitive
    description:
      name: shelf_static
      sha256: c87c3875f91262785dade62d135760c2c69cb217ac759485334c5857ad89f6e3
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: cc36c297b52866d203dbf9332263c94becc2fe0ceaa9681d07b6ef9807023b67
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  shimmer:
    dependency: transitive
    description:
      name: shimmer
      sha256: "1f1009b5845a1f88f1c5630212279540486f97409e9fc3f63883e71070d107bf"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sms_autofill:
    dependency: transitive
    description:
      name: sms_autofill
      sha256: c65836abe9c1f62ce411bb78d5546a09ece4297558070b1bd871db1db283aaf9
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "14658ba5f669685cd3d63701d01b31ea748310f7ab854e471962670abcf57832"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "86d247119aedce8e63f4751bd9626fc9613255935558447569ad42f9f5b48b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.5"
  source_map_stack_trace:
    dependency: transitive
    description:
      name: source_map_stack_trace
      sha256: c0713a43e323c3302c2abe2a1cc89aa057a387101ebd280371d6a6c9fa68516b
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  source_maps:
    dependency: transitive
    description:
      name: source_maps
      sha256: "190222579a448b03896e0ca6eca5998fa810fda630c1d65e2f78b3f638f54812"
      url: "https://pub.dev"
    source: hosted
    version: "0.10.13"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: "2d7299468485dca85efeeadf5d38986909c5eb0cd71fd3db2c2f000e6c9454bb"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "78f489aab276260cdd26676d2169446c7ecd3484bbd5fead4ca14f3ed4dd9ee3"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "761b9740ecbd4d3e66b8916d784e581861fd3c3553eda85e167bc49fdb68f709"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4+6"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "22adfd9a2c7d634041e96d6241e6e1c8138ca6817018afc5d443fef91dcefa9c"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1+1"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  string_unescape:
    dependency: transitive
    description:
      name: string_unescape
      sha256: b9628c8516cbb47ffced9a47440942ac7b480efd9b7dcd5058e27cf6819e99f7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "69fe30f3a8b04a0be0c15ae6490fc859a78ef4c43ae2dd5e8a623d45bfcf9225"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0+3"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test:
    dependency: transitive
    description:
      name: test
      sha256: "7ee44229615f8f642b68120165ae4c2a75fe77ae2065b1e55ae4711f6cf0899e"
      url: "https://pub.dev"
    source: hosted
    version: "1.25.7"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5b8a98dafc4d5c4c9c72d8b31ab2b23fc13422348d2997120294d3bac86b4ddb"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  test_core:
    dependency: transitive
    description:
      name: test_core
      sha256: "55ea5a652e38a1dfb32943a7973f3681a60f872f8c3a05a14664ad54ef9c6696"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.4"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: "direct dev"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "6fc2f56536ee873eeb867ad176ae15f304ccccc357848b351f6f0d8d4a40d193"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.14"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "16a513b6c12bb419304e72ea0ae2ab4fed569920d1c7cb850263fe3acc824626"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.2"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  value_layout_builder:
    dependency: transitive
    description:
      name: value_layout_builder
      sha256: "98202ec1807e94ac72725b7f0d15027afde513c55c69ff3f41bcfccb950831bc"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "44cc7104ff32563122a929e4620cf3efd584194eec6d1d913eb5ba593dbcf6de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.18"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "99fd9fbd34d9f9a32efd7b6a6aae14125d8237b10403b422a6a6dfeac2806146"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.13"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "1b4b9e706a10294258727674a340ae0d6e64a7231980f9f9a3d12e4b42407aad"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.16"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  vibration:
    dependency: transitive
    description:
      name: vibration
      sha256: "804ee8f9628f31ee71fbe6137a2bc6206a64e101ec22cd9dd6d3a7dc0272591b"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  vibration_platform_interface:
    dependency: transitive
    description:
      name: vibration_platform_interface
      sha256: "03e9deaa4df48a1a6212e281bfee5f610d62e9247929dd2f26f4efd4fa5e225c"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  video_player:
    dependency: transitive
    description:
      name: video_player
      sha256: "48941c8b05732f9582116b1c01850b74dbee1d8520cd7e34ad4609d6df666845"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.3"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "391e092ba4abe2f93b3e625bd6b6a6ec7d7414279462c1c0ee42b5ab8d0a0898"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.16"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "84b4752745eeccb6e75865c9aab39b3d28eb27ba5726d352d45db8297fbd75bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: df534476c341ab2c6a835078066fc681b8265048addd853a1e3c78740316a844
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "3ef40ea6d72434edbfdba4624b90fd3a80a0740d260667d91e7ecd2d79e13476"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "5c5f338a667b4c644744b661f309fb8080bb94b18a7e91ef1dbd343bed00ed6d"
      url: "https://pub.dev"
    source: hosted
    version: "14.2.5"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "69da27e49efa56a15f8afe8f4438c4ec02eff0a117df1b22ea4aad194fe1c104"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_ffi:
    dependency: transitive
    description:
      name: web_ffi
      sha256: "48ef8037f7bc051d11b88d0f2903e02bec21092c51833d37c3361c36e3edc4f7"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.2"
  web_socket:
    dependency: transitive
    description:
      name: web_socket
      sha256: "3c12d96c0c9a4eec095246debcea7b86c0324f22df69893d538fcc6f1b8cce83"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.6"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "0b8e2457400d8a859b7b2030786835a28a8e80836ef64402abef392ff4f1d0e5"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  webkit_inspection_protocol:
    dependency: transitive
    description:
      name: webkit_inspection_protocol
      sha256: "87d3f2333bb240704cd3f1c6b5b7acd8a10e7f0bc28c28dcf14e782014f4a572"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "512c26ccc5b8a571fd5d13ec994b7509f142ff6faf85835e243dde3538fdc713"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.2"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: c49a98510080378b1525132f407a92c3dcd3b7145bef04fb8137724aadcf1cf0
      url: "https://pub.dev"
    source: hosted
    version: "3.18.4"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: daf97c9d80197ed7b619040e86c8ab9a9dad285e7671ee7390f9180cc828a51e
      url: "https://pub.dev"
    source: hosted
    version: "5.10.1"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  workmanager:
    dependency: transitive
    description:
      path: "../../../forks/flutter_workmanager"
      relative: true
    source: path
    version: "0.5.1-fork.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  youtube_player_flutter:
    dependency: transitive
    description:
      path: "../../../forks/youtube_player_flutter/packages/youtube_player_flutter"
      relative: true
    source: path
    version: "9.0.1"
sdks:
  dart: ">=3.5.1 <4.0.0"
  flutter: ">=3.24.0"
