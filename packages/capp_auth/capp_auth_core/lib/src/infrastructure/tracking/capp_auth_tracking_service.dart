import 'package:capp_tracking/capp_tracking.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../capp_auth_core.dart';

class CappAuthTrackingService extends KoyalTrackingService {
  CappAuthTrackingService({
    required EventTrackingService eventTrackingService,
  }) : super(eventTrackingService: eventTrackingService) {
    defaultUserPropertyMap.addAll(_getDefaultDimension());
  }

  Map<String, String> _getDefaultDimension() {
    return {
      TrackingProperties.propertyClientId: '',
      TrackingProperties.propertyUserId: '',
      TrackingProperties.propertyJourneyType: 'CAPP',
      TrackingProperties.propertyPropertyType: 'app_capp',
    };
  }

  // USO187
  void trackForgotPasswordSuccessPassResetOnContinueToHomepageClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordSuccessPassResetOnContinueToHomepageClick,
      eventCategory: CappAuthTrackingCategories.forgotPasswordSuccessPassReset,
      eventLabel: CappAuthTrackingLabels.continueToHomepage,
    );
  }

  void trackForgotPasswordResetPassCriteriaViolationView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordResetPassCriteriaViolationView,
      eventCategory: CappAuthTrackingCategories.forgotPasswordResetPass,
      eventLabel: CappAuthTrackingLabels.unqualifiedPin,
    );
  }

  void trackForgotPasswordResetPassOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordResetPassOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.forgotPasswordResetPass,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  void trackForgotPasswordResetPassOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordResetPassOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotPasswordResetPass,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  void trackChangePasswordScreenOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePasswordScreenOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.changePasswordScreen,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  // USO044
  void trackAccountLoginScreeInvalidCredentialsView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountLoginScreeInvalidCredentialsView,
      eventCategory: CappAuthTrackingCategories.loginPin,
      eventLabel: CappAuthTrackingLabels.incorrectPassword,
    );
  }

  void trackAccountUnblockedScreenOnForgotPasswordClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountUnblockedScreenOnForgotPasswordClick,
      eventCategory: CappAuthTrackingCategories.accountUnblockedScreen,
      eventLabel: CappAuthTrackingLabels.forgotPassword,
    );
  }

  void trackAccountUnblockedScreenOnLoginClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountUnblockedScreenOnLoginClick,
      eventCategory: CappAuthTrackingCategories.accountUnblockedScreen,
      eventLabel: CappAuthTrackingLabels.login,
    );
  }

  // USO185
  void trackUnblockAccountVerificationScreenOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnblockAccountVerificationScreenOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.unblockAccountVerification,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  // USO186
  void trackUnblockAccountVerificationScreenOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnblockAccountVerificationScreenOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.unblockAccountVerification,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  void trackUnblockAccountOtpScreenOnBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnblockAccountOtpScreenOnBackClick,
      eventCategory: CappAuthTrackingCategories.unblockAccountScreen,
      eventLabel: CappAuthTrackingLabels.otpScreenGoBack,
    );
  }

  void trackUnblockAccountScreenOnBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnblockAccountScreenOnBackClick,
      eventCategory: CappAuthTrackingCategories.unblockAccountScreen,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  // USO027
  void trackUnblockAccountScreenOnContinue() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnblockAccountScreenOnContinue,
      eventCategory: CappAuthTrackingCategories.unblockAccountScreen,
      eventLabel: CappAuthTrackingLabels.continueLabel,
    );
  }

  void trackAccountLoginScreenOnLoginClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountLoginScreenOnLoginClick,
      eventCategory: CappAuthTrackingCategories.accountLogin,
      eventLabel: CappAuthTrackingLabels.login,
    );
  }

  void trackAccountLoginScreenOnContinueAsGuestClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountLoginScreenOnContinueAsGuestClick,
      eventCategory: CappAuthTrackingCategories.accountLogin,
      eventLabel: CappAuthTrackingLabels.continueAsGuest,
    );
  }

  void trackAccountLoginScreenOnCreateAccountClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountLoginScreenOnCreateAccountClick,
      eventCategory: CappAuthTrackingCategories.accountLogin,
      eventLabel: CappAuthTrackingLabels.createAccount,
    );
  }

  // USO190
  void trackAccountLoginScreenOnForgotUserPasswordClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountLoginScreenOnForgotUserPasswordClick,
      eventCategory: CappAuthTrackingCategories.accountLogin,
      eventLabel: CappAuthTrackingLabels.forgotUsernameOrPassword,
    );
  }

  /// USO306
  void trackAccountCreationSetupPasswordOnMismatchPasswordImpression() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationSetupPasswordOnMismatchPasswordImpression,
      eventCategory: CappAuthTrackingCategories.accountCreationSetupPassword,
      eventLabel: CappAuthTrackingLabels.mismatchPin,
    );
  }

  /// USO307
  void trackAccountCreationSetupPasswordOnUnqualifiedPassword() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationSetupPasswordOnUnqualifiedPassword,
      eventCategory: CappAuthTrackingCategories.accountCreationSetupPassword,
      eventLabel: CappAuthTrackingLabels.unqualifiedPin,
    );
  }

  /// USO180
  void trackAccountCreationSetupPasswordOnRetypePasswordEvent() {
    trackAnalyticsEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationSetupPasswordOnRetypePasswordEvent,
      eventCategory: CappAuthTrackingCategories.accountCreationSetupPassword,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappAuthTrackingLabels.retype,
    );
  }

  /// USO309
  void trackAccountCreationSetupPinOnMismatchPinImpression() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationSetupPinOnMismatchPinImpression,
      eventCategory: CappAuthTrackingCategories.accountCreationSetupPin,
      eventLabel: CappAuthTrackingLabels.mismatchPin,
    );
  }

  /// USO310
  void trackAccountCreationSetupPinOnUnqualifiedPinImpression() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationSetupPinOnUnqualifiedPinImpression,
      eventCategory: CappAuthTrackingCategories.accountCreationSetupPin,
      eventLabel: CappAuthTrackingLabels.unqualifiedPin,
    );
  }

  void trackAccountCreationSetupPinOnReenterPinEvent() {
    trackAnalyticsEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationSetupPinOnReenterPinEvent,
      eventCategory: CappAuthTrackingCategories.accountCreationSetupPin,
      eventAction: KoyalAnalyticsConstants.enter,
      eventLabel: CappAuthTrackingLabels.retype,
    );
  }

  // USO181
  void trackAccountCreationVerifyCustomerOnIdNumberClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationVerifyCustomerOnIdNumberClick,
      eventCategory: CappAuthTrackingCategories.accountCreationVerifyCustomer,
      eventLabel: CappAuthTrackingLabels.idNumber,
    );
  }

  // USO182
  void trackAccountCreationVerifyCustomerOnContractNumberClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationVerifyCustomerOnContractNumberClick,
      eventCategory: CappAuthTrackingCategories.accountCreationVerifyCustomer,
      eventLabel: CappAuthTrackingLabels.contractNumber,
    );
  }

  // USO183
  void trackAccountCreationVerifyCustomerOnNotCustomerClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationVerifyCustomerOnNotCustomerClick,
      eventCategory: CappAuthTrackingCategories.accountCreationVerifyCustomer,
      eventLabel: CappAuthTrackingLabels.guest,
    );
  }

  // USO184
  void trackAccountCreationVerifyCustomerOnContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountCreationVerifyCustomerOnContinueClick,
      eventCategory: CappAuthTrackingCategories.accountCreationVerifyCustomer,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  void trackForgotPasswordEnterUsernameOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordEnterUsernameOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotPasswordEnterUsername,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  void trackForgotPasswordEnterUsernameOnContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordEnterUsernameOnContinueClick,
      eventCategory: CappAuthTrackingCategories.forgotPasswordEnterUsername,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  // USO188
  void trackForgotPasswordSecondIdentifierOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordSecondIdentifierOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotPasswordSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  // USO189
  void trackForgotPasswordSecondIdentifierOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPasswordSecondIdentifierOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.forgotPasswordSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  // USO191
  void trackForgotUsernameEnterPhoneNumberOnGobackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotUsernameEnterPhoneNumberOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotUsernameEnterPhoneNumber,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  // USO192
  void trackForgotUsernameEnterPhoneNumberOnContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotUsernameEnterPhoneNumberOnContinueClick,
      eventCategory: CappAuthTrackingCategories.forgotUsernameEnterPhoneNumber,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  // USO193
  void trackForgotUsernameSecondIdentifierOnGobackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotUsernameSecondIdentifierOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotUsernameSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  // USO194
  void trackForgotUsernameSecondIdentifierOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotUsernameSecondIdentifierOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.forgotUsernameSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  // USO196
  void trackForgotUsernameSuccessPhoneNumberRemindedContinueToLoginClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotUsernameSuccessPhoneNumberRemindedContinueToLoginClick,
      eventCategory: CappAuthTrackingCategories.forgotUsernameSuccessPhoneNumberReminded,
      eventLabel: CappAuthTrackingLabels.login,
    );
  }

  // USO198
  void trackChangePhoneNumberEnterCurrentNumberOnContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterCurrentNumberOnContinueClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterCurrent,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  // USO199
  void trackChangePhoneNumberEnterCurrentNumberOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterCurrentNumberOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterCurrent,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  // USO231
  void trackChangePhoneNumberEnterUsernameOnContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterUsernameOnContinueClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterUsername,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  // USO232
  void trackChangePhoneNumberEnterUsernameOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterUsernameOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterUsername,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  // USO202
  void trackChangePhoneNumberSecondIdentifierOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberSecondIdentifierOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  // USO201
  void trackChangePhoneNumberSecondIdentifierOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberSecondIdentifierOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  // USO204
  void trackChangePhoneNumberEnterPassOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPassOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPass,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  // USO205
  void trackChangePhoneNumberEnterPassOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPassOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPass,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  // USO207
  void trackChangePhoneNumberEnterPassErrorIncorrectPassAccountTempBlockedView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPassErrorIncorrectPassAccountTempBlockedView,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPass,
      eventLabel: CappAuthTrackingLabels.temporaryBlock,
    );
  }

  // USO208
  void trackChangePhoneNumberEnterPassErrorIncorrectPassAccountPermBlockedView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPassErrorIncorrectPassAccountPermBlockedView,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPass,
      eventLabel: CappAuthTrackingLabels.permanentBlock,
    );
  }

  // USO206
  void trackChangePhoneNumberEnterPassErrorIncorrectDialogView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPassErrorTrackIncorrectDialogView,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPass,
      eventLabel: CappAuthTrackingLabels.wrong,
    );
  }

  // USO210
  void trackChangePhoneNumberEnterNewNumberOnContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterNewNumberOnContinueClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterNewNumber,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  // USO211
  void trackChangePhoneNumberEnterNewNumberOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterNewNumberOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterNewNumber,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  // USO213
  void trackChangePhoneNumberSuccessPhoneClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberSuccessOnHomeClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberSuccess,
      eventLabel: CappAuthTrackingLabels.continueToHomepage,
    );
  }

  // USO216
  void trackOnNoAccessToPhoneClick({required String eventCategory}) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authOnNoAccessToPhoneClick,
      eventCategory: eventCategory,
      eventLabel: KoyalTrackingLabels.noAccessToPhone,
    );
  }

  void trackUnlockScreenOnForgotPinClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnlockScreenOnForgotPinClick,
      eventCategory: CappAuthTrackingCategories.unlockScreen,
      eventLabel: CappAuthTrackingLabels.forgotPin,
    );
  }

  void trackUnlockScreenOnSwitchUserClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnlockScreenOnSwitchUserClick,
      eventCategory: CappAuthTrackingCategories.unlockScreen,
      eventLabel: CappAuthTrackingLabels.switchUser,
    );
  }

  void trackUnlockScreenOnEnterPinClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnlockScreenOnEnterPinClick,
      eventCategory: CappAuthTrackingCategories.unlockScreen,
      eventLabel: CappAuthTrackingLabels.enterPin,
    );
  }

  void trackUnlockScreenOnBiometricsLoginClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authUnlockScreenOnBiometricsLoginClick,
      eventCategory: CappAuthTrackingCategories.unlockScreen,
      eventLabel: CappAuthTrackingLabels.biometricsLogin,
    );
  }

  void trackForgotPinEnterPhoneNumberOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPinEnterPhoneNumberOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotPinEnterPhoneNumber,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  void trackForgotPinEnterPhoneNumberOnContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPinEnterPhoneNumberOnContinueClick,
      eventCategory: CappAuthTrackingCategories.forgotPinEnterPhoneNumber,
      eventLabel: CappAuthTrackingLabels.send,
    );
  }

  void trackForgotPinSecondIdentifierrOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPinSecondIdentifierrOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotPinSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  void trackForgotPinSecondIdentifierrOnConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPinSecondIdentifierrOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.forgotPinSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  void trackForgotPinResetPinOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPinResetPinOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.forgotPinResetPin,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  void trackForgotPinResetPinOnCriteriaViolationView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPinResetPinOnCriteriaViolationView,
      eventCategory: CappAuthTrackingCategories.forgotPinResetPin,
      eventLabel: CappAuthTrackingLabels.unqualifiedPin,
    );
  }

  // USO230
  void trackForgotPinSuccessPinResetOnContinueToHomepageClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotPinSuccessPinResetOnContinueToHomepageClick,
      eventCategory: CappAuthTrackingCategories.forgotPinSuccessPinReset,
      eventLabel: CappAuthTrackingLabels.continueToHomepage,
    );
  }

  void trackAccountUnblockedScreenOnForgotPinClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authAccountUnblockedScreenOnForgotPinClick,
      eventCategory: CappAuthTrackingCategories.accountUnblockedScreen,
      eventLabel: CappAuthTrackingLabels.forgotPin,
    );
  }

  // USO268
  void trackChangePhoneNumberEnterPinOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPinOnGoBackClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPin,
      eventLabel: CappAuthTrackingLabels.goBack,
    );
  }

  // USO269
  void trackChangePhoneNumberEnterPinOnForgotPinClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPinOnForgotPinClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPin,
      eventLabel: CappAuthTrackingLabels.forgotPin,
    );
  }

  // USO270
  void trackChangePhoneNumberEnterPinOnErrorIncorrectPinView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPinOnErrorIncorrectPinView,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPin,
      eventLabel: CappAuthTrackingLabels.wrong,
    );
  }

  // USO271
  void trackChangePhoneNumberEnterPinOnErrorIncorrectPinAccountPermBlockedView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneNumberEnterPinOnErrorIncorrectPinAccountPermBlockedView,
      eventCategory: CappAuthTrackingCategories.changePhoneNumberEnterPin,
      eventLabel: CappAuthTrackingLabels.permanentBlock,
    );
  }

  /// USO234
  void trackAccountCreationSecondIdentifierBackButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountCreationSecondIdentifierBackButtonClick,
      eventCategory: CappAuthTrackingCategories.accountCreationSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.backButton,
    );
  }

  /// USO235
  void trackAccountCreationSecondIdentifierSecondIdEnter(UserSecondIdType secondId) {
    trackEnterEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountCreationSecondIdentifierSecondIdEnter,
      eventCategory: CappAuthTrackingCategories.accountCreationSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.secondIdToLabel(secondId),
    );
  }

  /// USO236
  void trackAccountCreationSecondIdentifierConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountCreationSecondIdentifierConfirmClick,
      eventCategory: CappAuthTrackingCategories.accountCreationSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  /// USO237
  void trackAccountCreationSecondIdentifierIAmNotHciCustomerClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountCreationSecondIdentifierIAmNotHciCustomerClick,
      eventCategory: CappAuthTrackingCategories.accountCreationSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.notHciCustomer,
    );
  }

  /// USO153
  void trackLogin2ndIdBackButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.login2ndIdBackButtonClick,
      eventCategory: CappAuthTrackingCategories.login2ndId,
      eventLabel: CappAuthTrackingLabels.backButton,
    );
  }

  /// USO154
  void trackLogin2ndIdSecondIdEnter(UserSecondIdType secondId) {
    trackEnterEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.login2ndIdSecondIdEnter,
      eventCategory: CappAuthTrackingCategories.login2ndId,
      eventLabel: CappAuthTrackingLabels.secondIdToLabel(secondId),
    );
  }

  /// USO155
  void trackLogin2ndIdConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.login2ndIdConfirmClick,
      eventCategory: CappAuthTrackingCategories.login2ndId,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  /// USO161
  void trackLogin2ndIdIAmNotHciCustomerClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.login2ndIdIAmNotHciCustomerClick,
      eventCategory: CappAuthTrackingCategories.login2ndId,
      eventLabel: CappAuthTrackingLabels.iAmNotHciCustomer,
    );
  }

  /// USO238
  void trackAccountCreationSecondIdentifierOverlay2ndIdInvalidCredentialsView(UserSecondIdType secondId) {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountCreationSecondIdentifierOverlay2ndIdInvalidCredentialsView,
      eventCategory: CappAuthTrackingCategories.accountCreationSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.overlay2ndInvalid(secondId),
    );
  }

  /// USO239
  void trackAccountCreationSecondIdentifierOverlay2ndIdTryAgainClick(UserSecondIdType secondId) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountCreationSecondIdentifierOverlay2ndIdTryAgainClick,
      eventCategory: CappAuthTrackingCategories.accountCreationSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.overlay2ndIdTryAgain(secondId),
    );
  }

  /// USO240
  void trackAccountCreationSecondIdentifierOverlay2ndIdContactHomeCreditClick(UserSecondIdType secondId) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountCreationSecondIdentifierOverlay2ndIdContactHomeCreditClick,
      eventCategory: CappAuthTrackingCategories.accountCreationSecondIdentifier,
      eventLabel: CappAuthTrackingLabels.overlay2ndIdContactHomeCredit(secondId),
    );
  }

  /// USO303
  void trackLogin2ndIdOverlay2ndIdInvalidCredentialsView(UserSecondIdType secondId) {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.login2ndIdOverlay2ndIdInvalidCredentialsView,
      eventCategory: CappAuthTrackingCategories.login2ndId,
      eventLabel: CappAuthTrackingLabels.overlay2ndIdInvalidCredentials(secondId),
    );
  }

  /// USO241
  void trackLogin2ndIdOverlay2ndIdOkGotItClick(UserSecondIdType secondId) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.login2ndIdOverlay2ndIdOkGotItClick,
      eventCategory: CappAuthTrackingCategories.login2ndId,
      eventLabel: CappAuthTrackingLabels.overlay2ndIdOkGotIt(secondId),
    );
  }

  /// USO164
  void trackAccountAuthenticationConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountAuthenticationConfirmClick,
      eventCategory: CappAuthTrackingCategories.accountAuthentication,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  /// USO165
  void trackAccountAuthenticationGuestClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.accountAuthenticationGuestClick,
      eventCategory: CappAuthTrackingCategories.accountAuthentication,
      eventLabel: CappAuthTrackingLabels.guest,
    );
  }

  /// USO167
  void trackAuthVerificationConfirm2ndIdTypeClick(UserSecondIdType secondId) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authVerificationConfirm2ndIdTypeClick,
      eventCategory: CappAuthTrackingCategories.authVerification,
      eventLabel: CappAuthTrackingLabels.secondIdConfirm(secondId),
    );
  }

  /// USO168
  void trackAuthVerificationOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authVerificationBackButtonClick,
      eventCategory: CappAuthTrackingCategories.authVerification,
      eventLabel: CappAuthTrackingLabels.backButton,
    );
  }

  /// USO169
  void trackAuthVerificationNotCustomerClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authVerificationNotCustomerClick,
      eventCategory: CappAuthTrackingCategories.authVerification,
      eventLabel: CappAuthTrackingLabels.notCustomer,
    );
  }

  /// USO172
  void trackAuthCrossroadConfirmSelectedOptionClick(SelectableOptionType optionType) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authCrossroadConfirmSelectedOptionClick,
      eventCategory: CappAuthTrackingCategories.authCrossroad,
      eventLabel: CappAuthTrackingLabels.selectableOptionTypeConfirm(optionType),
    );
  }

  /// USO173
  void trackAuthCrossroadNotCustomerClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authCrossroadNotCustomerClick,
      eventCategory: CappAuthTrackingCategories.authCrossroad,
      eventLabel: CappAuthTrackingLabels.notCustomer,
    );
  }

  /// USO264
  void trackRetrieveAccountVerificationConfirm2ndIdTypeClick(UserSecondIdType secondId) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.retrieveAccountVerificationConfirm2ndIdTypeClick,
      eventCategory: CappAuthTrackingCategories.retrieveAccountVerification,
      eventLabel: CappAuthTrackingLabels.secondIdConfirm(secondId),
    );
  }

  /// USO265
  void trackRetrieveAccountVerificationOnGoBackClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.retrieveAccountVerificationBackButtonClick,
      eventCategory: CappAuthTrackingCategories.retrieveAccountVerification,
      eventLabel: CappAuthTrackingLabels.backButton,
    );
  }

  /// USO266
  void trackRetrieveAccountVerificationNotCustomerClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.retrieveAccountVerificationNotCustomerClick,
      eventCategory: CappAuthTrackingCategories.retrieveAccountVerification,
      eventLabel: CappAuthTrackingLabels.notCustomer,
    );
  }

  /// USO274
  void trackRetrieveAccountCrossroadConfirmSelectedOptionClick(SelectableOptionType optionType) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.retrieveAccountCrossroadConfirmSelectedOptionClick,
      eventCategory: CappAuthTrackingCategories.retrieveAccountCrossroads,
      eventLabel: CappAuthTrackingLabels.selectableOptionTypeConfirm(optionType),
    );
  }

  /// USO275
  void trackRetrieveAccountCrossroadNotCustomerClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.retrieveAccountCrossroadNotCustomerClick,
      eventCategory: CappAuthTrackingCategories.retrieveAccountCrossroads,
      eventLabel: CappAuthTrackingLabels.notCustomer,
    );
  }

  /// USO174
  void trackAuthenticationTncBackButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.tncAuthenticationBackButtonClick,
      eventCategory: CappAuthTrackingCategories.authenticationTnc,
      eventLabel: CappAuthTrackingLabels.backButton,
    );
  }

  /// USO175
  void trackAuthenticationTncConfirmClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.tncAuthenticationConfirmClick,
      eventCategory: CappAuthTrackingCategories.authenticationTnc,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  // USO342
  void trackVerificationErrorScreenContinueClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authVerificationErrorScreenContinueClick,
      eventCategory: CappAuthTrackingCategories.verificationError,
      eventLabel: CappAuthTrackingLabels.verificationGotIt,
    );
  }

  /// USO343
  void trackGoToHomepageClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authGoToHomepageClick,
      eventCategory: CappAuthTrackingCategories.verificationDeduplication,
      eventLabel: CappAuthTrackingLabels.verificationGotIt,
    );
  }

  /// USO344
  void trackLoginButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authLoginButtonClick,
      eventCategory: CappAuthTrackingCategories.accCreationExisting,
      eventLabel: CappAuthTrackingLabels.login,
    );
  }

  /// USO345
  void trackForgotYourPasswordClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authForgotYourPasswordClick,
      eventCategory: CappAuthTrackingCategories.accCreationExisting,
      eventLabel: CappAuthTrackingLabels.forgotPassword,
    );
  }

  /// USO346
  void trackBackButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authBackButtonClick,
      eventCategory: CappAuthTrackingCategories.accCreationExisting,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  // USO348
  void trackBiometricAuthenticationView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authBiometricAuthenticationView,
      eventCategory: CappAuthTrackingCategories.enableBioOverlay,
      eventLabel: CappAuthTrackingLabels.setting,
    );
  }

  // USO349
  void trackBiometricProceedClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authBiometricProccedClick,
      eventCategory: CappAuthTrackingCategories.enableBioOverlay,
      eventLabel: CappAuthTrackingLabels.proceed,
    );
  }

  // USO350
  void trackBiometricLaterClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authBiometricLaterClick,
      eventCategory: CappAuthTrackingCategories.enableBioOverlay,
      eventLabel: CappAuthTrackingLabels.later,
    );
  }

  /// USO319
  void trackChangePhoneReasonSuccessContinueButtonClick({required String eventCategory}) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonSuccessContinueButtonClick,
      eventCategory: eventCategory,
      eventLabel: CappAuthTrackingLabels.continueLabel,
    );
  }

  /// USO321
  void trackChangePhoneReasonRejectOkGotItButtonClick({required String eventCategory}) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonCancelOkGotItButtonClick,
      eventCategory: eventCategory,
      eventLabel: CappAuthTrackingLabels.verificationGotIt,
    );
  }

  /// USO322
  void trackChangePhoneReasonRejectContactHcButtonClick({required String eventCategory}) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonCancelContactHcButtonClick,
      eventCategory: eventCategory,
      eventLabel: CappAuthTrackingLabels.contactHomeCredit,
    );
  }

  /// USO324
  void trackChangePhoneReasonCancelTryAgainButtonClick({required String eventCategory}) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonCancelTryAgainButtonClick,
      eventCategory: eventCategory,
      eventLabel: CappAuthTrackingLabels.retry,
    );
  }

  /// USO325
  void trackChangePhoneReasonCancelDoItLaterButtonClick({required String eventCategory}) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonCancelDoItLaterButtonClick,
      eventCategory: eventCategory,
      eventLabel: CappAuthTrackingLabels.later,
    );
  }

  /// USO327
  void trackChangePhoneReasonContinueButtonClick({required String label}) {
    trackSubmitEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonContinueButtonClick,
      eventCategory: CappAuthTrackingCategories.changePhoneReason,
      eventLabel: label,
    );
  }

  /// USO328
  void trackChangePhoneReasonBackButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonBackButtonClick,
      eventCategory: CappAuthTrackingCategories.changePhoneReason,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  /// USO331
  void trackChangePhoneNumBosBackButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneEnterNewPhoneBackButtonClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumBos,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  /// USO332
  void trackChangePhoneNumBosContinueButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneEnterNewPhoneContinueButtonClick,
      eventCategory: CappAuthTrackingCategories.changePhoneNumBos,
      eventLabel: CappAuthTrackingLabels.continueLabel,
    );
  }

  /// USO340
  void trackChangePhoneReasonAttemptsReachedOkGotItButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authChangePhoneReasonAttemptsReachedOkGotItButtonClick,
      eventCategory: CappAuthTrackingCategories.changePhoneErrLimit,
      eventLabel: CappAuthTrackingLabels.verificationGotIt,
    );
  }

  /// USO365
  void trackChangeEmailEditEmailAddressButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeEmailEditEmailButtonClick,
      eventCategory: CappAuthTrackingCategories.personalDetail,
      eventLabel: CappAuthTrackingLabels.editEmail,
    );
  }

  /// USO366
  void trackChangeEmailProfileEmailConfirmButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeEmailProfileEmailConfirmButtonClick,
      eventCategory: CappAuthTrackingCategories.profileEmailAddressEdit,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  /// USO367
  void trackChangeEmailProfileEmailBackButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeEmailProfileEmailBackButtonClick,
      eventCategory: CappAuthTrackingCategories.profileEmailAddressEdit,
      eventLabel: CappAuthTrackingLabels.back,
    );
  }

  /// USO368
  void trackChangeEmailProfileEmailChangeFreezeDialogView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeEmailProfileEmailChangeFreezeDialogView,
      eventCategory: CappAuthTrackingCategories.personalDetail,
      eventLabel: CappAuthTrackingLabels.personalDetailEmailChangeFreeze,
    );
  }

  /// USO369
  void trackAttemptsReachedIdentityVerificationDialogView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeEmailProfileEmailChangeFreezeDialogView,
      eventCategory: CappAuthTrackingCategories.personalDetail,
      eventLabel: CappAuthTrackingLabels.attemptsReachedIdentityVerification,
    );
  }

  /// USO375
  void trackAttemptsReachedCooloffChangePwdDialogView() {
    trackViewEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changePasswordDialogCoolOfView,
      eventCategory: CappAuthTrackingCategories.changePasswordScreen,
      eventLabel: CappAuthTrackingLabels.changePasswordCoolOff,
    );
  }

  /// USO376
  void trackEditNationalIdPersonalDetailsButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.editNationalIdPersonalDetailsButtonClick,
      eventCategory: CappAuthTrackingCategories.personalDetail,
      eventLabel: CappAuthTrackingLabels.changeNationalId,
    );
  }

  /// USO378
  void trackChangeNationalIdContinueButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeNationalIdContinueButtonClick,
      eventCategory: CappAuthTrackingCategories.personalDetail,
      eventLabel: CappAuthTrackingLabels.continueLabel,
    );
  }

  /// USO379
  void trackChangeNationalIdCloseButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeNationalIdCloseButtonClick,
      eventCategory: CappAuthTrackingCategories.personalDetail,
      eventLabel: CappAuthTrackingLabels.closeLabel,
    );
  }

  /// USO381
  void trackSetUpPasswordConfirmButtonClick() {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.authSetUpPasswordOnConfirmClick,
      eventCategory: CappAuthTrackingCategories.accountCreationSetupPassword,
      eventLabel: CappAuthTrackingLabels.confirm,
    );
  }

  /// USO383
  void trackChangeNationalIdFailedScreenRetryButtonClick(String eventCategory) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeNationalIdFailedScreenRetryButtonClick,
      eventCategory: eventCategory,
      eventLabel: CappAuthTrackingLabels.retry,
    );
  }

  /// USO384
  void trackChangeNationalIdFailedScreenLaterButtonClick(String eventCategory) {
    trackClickEvent(
      userPropertyMap: defaultUserPropertyMap,
      event: KoyalEvent.changeNationalIdFailedScreenLaterButtonClick,
      eventCategory: CappAuthTrackingCategories.updateIdCardBos,
      eventLabel: CappAuthTrackingLabels.later,
    );
  }
}
