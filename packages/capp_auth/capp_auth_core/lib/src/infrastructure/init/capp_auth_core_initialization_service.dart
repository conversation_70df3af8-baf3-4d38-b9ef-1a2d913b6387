import 'package:koyal_core/koyal_core.dart';

import '../../domain/password_length_provider/i_password_length_provider.dart';

class CappAuthCoreInitializationService extends RetryModuleInitializationService {
  final IPasswordLengthProvider passwordLengthProvider;

  CappAuthCoreInitializationService({
    required this.passwordLengthProvider,
  }) : super();

  @override
  Future<void> initialize() async {
    await initInputListWithRetry([
      InitializationInput(
        initFunction: passwordLengthProvider.init,
      ),
    ]);
  }
}
