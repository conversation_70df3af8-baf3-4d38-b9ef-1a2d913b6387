import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:identityapi/identityapi.dart';
import 'package:identityapi/model/models.dart' as identity_models;
import 'package:koyal_auth/koyal_auth.dart';
import 'package:logger/logger.dart';

import '../domain/i_retrieve_account_repository.dart';
import 'constants.dart';

class RetrieveAccountRepository implements IRetrieveAccountRepository {
  final RetrieveAccountApi retrieveAccountApi;
  final Logger logger;

  const RetrieveAccountRepository({
    required this.retrieveAccountApi,
    required this.logger,
  });
  @override
  Future<Either<UserVerificationFailure, RetrieveAccountStartResponse>> retrieveAccountStart(String phoneNumber) async {
    try {
      final response = await retrieveAccountApi.retrieveAccountPost(
        retrieveAccountStartRequest: identity_models.RetrieveAccountStartRequest(phoneNumber: phoneNumber),
      );
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(_onDioError(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in retrieveAccount repository (retrieveAccountStart)', e, s);
      return left(
        UserVerificationFailure(type: UserVerificationFailureType.unexpected),
      );
    }
  }

  @override
  Future<Either<UserVerificationFailure, RetrieveAccountSecondIdVerificationResponse>> retrieveAccountVerify2ndId({
    required UserSecondIdType idType,
    required String? value,
    required String sessionId,
  }) async {
    try {
      final request = identity_models.RetrieveAccountSecondIdVerificationRequest(
        type: idType.toApi(),
        value: value,
      );

      final response = await retrieveAccountApi.retrieveAccountSessionIdSecondIdVerificationPost(
        sessionId,
        retrieveAccountSecondIdVerificationRequest: request,
      );
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(_onDioError(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in retrieveAccount repository (retrieveAccountVerify2ndId)', e, s);
      return left(
        UserVerificationFailure(type: UserVerificationFailureType.unexpected),
      );
    }
  }

  @override
  Future<Either<UserVerificationFailure, RetrieveAccountAuthenticateResultResponse>> retrieveAccountAuthenticateResult(
    String sessionId,
  ) async {
    try {
      final response = await retrieveAccountApi.retrieveAccountSessionIdAuthenticateResultGet(sessionId);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(_onDioError(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in retrieveAccount repository (retrieveAccountAuthenticateResult)', e, s);
      return left(
        UserVerificationFailure(type: UserVerificationFailureType.unexpected),
      );
    }
  }

  @override
  Future<Either<UserVerificationFailure, RetrieveAccountIdentifyResponse>> retrieveAccountIdentifyPost({
    required String sessionId,
    required RetrieveAccountIdentifyRequest retrieveAccountIdentifyRequest,
  }) async {
    try {
      final response = await retrieveAccountApi.retrieveAccountSessionIdIdentifyPost(
        sessionId,
        retrieveAccountIdentifyRequest: RetrieveAccountIdentifyRequestMapper.fromDomain(retrieveAccountIdentifyRequest),
      );
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(_onDioError(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in retrieveAccount repository (retrieveAccountIdentifyPost)', e, s);
      return left(
        UserVerificationFailure(type: UserVerificationFailureType.unexpected),
      );
    }
  }

  @override
  Future<Either<UserVerificationFailure, RetrieveAccountIdentifyResultResponse>> retrieveAccountIdentifyResult(
    String sessionId,
  ) async {
    try {
      final response = await retrieveAccountApi.retrieveAccountV2SessionIdIdentifyResultGet(sessionId);
      return right(response.toDomain());
    } on DioError catch (e) {
      return left(_onDioError(e));
    } on Exception catch (e, s) {
      logger.wtf('Unexpected error in retrieveAccount repository (retrieveAccountIdentifyResult)', e, s);
      return left(
        UserVerificationFailure(type: UserVerificationFailureType.unexpected),
      );
    }
  }

  UserVerificationFailure _onDioError(DioError e) {
    switch (e.response?.statusCode) {
      case 400:
        return UserVerificationFailure(type: UserVerificationFailureType.invalid);
      case 403:
        return UserVerificationFailure(type: UserVerificationFailureType.sessionStartsRateExceeded);
      case 404:
        return UserVerificationFailure(type: UserVerificationFailureType.sessionNotFoundOrExpired);
      case 409:
        return UserVerificationFailure(type: UserVerificationFailureType.invalidOtp);
      case 429:
        final tooManyRequestTimeOut = e.response?.headers.value(timeOutedUntil);
        int? timeOutSeconds;
        if (tooManyRequestTimeOut != null) {
          timeOutSeconds = int.tryParse(tooManyRequestTimeOut);
        }
        return UserVerificationFailure(
          type: UserVerificationFailureType.sessionStartsRateExceeded,
          header: timeOutSeconds,
        );
      default:
        return UserVerificationFailure(type: UserVerificationFailureType.unexpected);
    }
  }
}
