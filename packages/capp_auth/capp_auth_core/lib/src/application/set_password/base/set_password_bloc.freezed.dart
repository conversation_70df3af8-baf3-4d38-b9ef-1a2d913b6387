// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'set_password_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$SetPasswordEvent {}

/// @nodoc

class _$_Init implements _Init {
  const _$_Init(
      {this.sessionId, this.faceGuardToken, this.publicKey, this.challenge});

  @override
  final String? sessionId;
  @override
  final String? faceGuardToken;
  @override
  final String? publicKey;
  @override
  final String? challenge;

  @override
  String toString() {
    return 'SetPasswordEvent.init(sessionId: $sessionId, faceGuardToken: $faceGuardToken, publicKey: $publicKey, challenge: $challenge)';
  }
}

abstract class _Init implements SetPasswordEvent {
  const factory _Init(
      {final String? sessionId,
      final String? faceGuardToken,
      final String? publicKey,
      final String? challenge}) = _$_Init;

  String? get sessionId;
  String? get faceGuardToken;
  String? get publicKey;
  String? get challenge;
}

/// @nodoc

class _$_AddDigit implements _AddDigit {
  const _$_AddDigit(this.digit);

  @override
  final int digit;

  @override
  String toString() {
    return 'SetPasswordEvent.addDigit(digit: $digit)';
  }
}

abstract class _AddDigit implements SetPasswordEvent {
  const factory _AddDigit(final int digit) = _$_AddDigit;

  int get digit;
}

/// @nodoc

class _$_Backspace implements _Backspace {
  const _$_Backspace();

  @override
  String toString() {
    return 'SetPasswordEvent.backspace()';
  }
}

abstract class _Backspace implements SetPasswordEvent {
  const factory _Backspace() = _$_Backspace;
}

/// @nodoc

class _$_SetPassword implements _SetPassword {
  const _$_SetPassword(this.password);

  @override
  final String password;

  @override
  String toString() {
    return 'SetPasswordEvent.setPassword(password: $password)';
  }
}

abstract class _SetPassword implements SetPasswordEvent {
  const factory _SetPassword(final String password) = _$_SetPassword;

  String get password;
}

/// @nodoc

class _$_SubmitAgain implements _SubmitAgain {
  const _$_SubmitAgain();

  @override
  String toString() {
    return 'SetPasswordEvent.submitAgain()';
  }
}

abstract class _SubmitAgain implements SetPasswordEvent {
  const factory _SubmitAgain() = _$_SubmitAgain;
}

/// @nodoc

class _$_Clear implements _Clear {
  const _$_Clear();

  @override
  String toString() {
    return 'SetPasswordEvent.clear()';
  }
}

abstract class _Clear implements SetPasswordEvent {
  const factory _Clear() = _$_Clear;
}

/// @nodoc
mixin _$SetPasswordState<T> {
  LoadingState get state => throw _privateConstructorUsedError;
  Option<Either<SetPasswordFailure, T>> get failureOrSuccess =>
      throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  int get requiredPasswordLength => throw _privateConstructorUsedError;
  bool get showPasswordNotMatch => throw _privateConstructorUsedError;
  bool get showPasswordDoesNotMeetCriteria =>
      throw _privateConstructorUsedError;
  bool get isPasswordHasThreeRepeatingConsecutiveDigits =>
      throw _privateConstructorUsedError;
  bool get isPasswordHasThreeSucceedingConsecutiveDigits =>
      throw _privateConstructorUsedError;
  PasswordScreenType get screen => throw _privateConstructorUsedError;
  String get firstPassword => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  String? get challenge => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  String? get faceGuardToken => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SetPasswordStateCopyWith<T, SetPasswordState<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SetPasswordStateCopyWith<T, $Res> {
  factory $SetPasswordStateCopyWith(
          SetPasswordState<T> value, $Res Function(SetPasswordState<T>) then) =
      _$SetPasswordStateCopyWithImpl<T, $Res, SetPasswordState<T>>;
  @useResult
  $Res call(
      {LoadingState state,
      Option<Either<SetPasswordFailure, T>> failureOrSuccess,
      String password,
      int requiredPasswordLength,
      bool showPasswordNotMatch,
      bool showPasswordDoesNotMeetCriteria,
      bool isPasswordHasThreeRepeatingConsecutiveDigits,
      bool isPasswordHasThreeSucceedingConsecutiveDigits,
      PasswordScreenType screen,
      String firstPassword,
      String? publicKey,
      String? challenge,
      String? sessionId,
      String? faceGuardToken});
}

/// @nodoc
class _$SetPasswordStateCopyWithImpl<T, $Res, $Val extends SetPasswordState<T>>
    implements $SetPasswordStateCopyWith<T, $Res> {
  _$SetPasswordStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? failureOrSuccess = null,
    Object? password = null,
    Object? requiredPasswordLength = null,
    Object? showPasswordNotMatch = null,
    Object? showPasswordDoesNotMeetCriteria = null,
    Object? isPasswordHasThreeRepeatingConsecutiveDigits = null,
    Object? isPasswordHasThreeSucceedingConsecutiveDigits = null,
    Object? screen = null,
    Object? firstPassword = null,
    Object? publicKey = freezed,
    Object? challenge = freezed,
    Object? sessionId = freezed,
    Object? faceGuardToken = freezed,
  }) {
    return _then(_value.copyWith(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<SetPasswordFailure, T>>,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      requiredPasswordLength: null == requiredPasswordLength
          ? _value.requiredPasswordLength
          : requiredPasswordLength // ignore: cast_nullable_to_non_nullable
              as int,
      showPasswordNotMatch: null == showPasswordNotMatch
          ? _value.showPasswordNotMatch
          : showPasswordNotMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      showPasswordDoesNotMeetCriteria: null == showPasswordDoesNotMeetCriteria
          ? _value.showPasswordDoesNotMeetCriteria
          : showPasswordDoesNotMeetCriteria // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordHasThreeRepeatingConsecutiveDigits: null ==
              isPasswordHasThreeRepeatingConsecutiveDigits
          ? _value.isPasswordHasThreeRepeatingConsecutiveDigits
          : isPasswordHasThreeRepeatingConsecutiveDigits // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordHasThreeSucceedingConsecutiveDigits: null ==
              isPasswordHasThreeSucceedingConsecutiveDigits
          ? _value.isPasswordHasThreeSucceedingConsecutiveDigits
          : isPasswordHasThreeSucceedingConsecutiveDigits // ignore: cast_nullable_to_non_nullable
              as bool,
      screen: null == screen
          ? _value.screen
          : screen // ignore: cast_nullable_to_non_nullable
              as PasswordScreenType,
      firstPassword: null == firstPassword
          ? _value.firstPassword
          : firstPassword // ignore: cast_nullable_to_non_nullable
              as String,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      challenge: freezed == challenge
          ? _value.challenge
          : challenge // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      faceGuardToken: freezed == faceGuardToken
          ? _value.faceGuardToken
          : faceGuardToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SetPasswordStateCopyWith<T, $Res>
    implements $SetPasswordStateCopyWith<T, $Res> {
  factory _$$_SetPasswordStateCopyWith(_$_SetPasswordState<T> value,
          $Res Function(_$_SetPasswordState<T>) then) =
      __$$_SetPasswordStateCopyWithImpl<T, $Res>;
  @override
  @useResult
  $Res call(
      {LoadingState state,
      Option<Either<SetPasswordFailure, T>> failureOrSuccess,
      String password,
      int requiredPasswordLength,
      bool showPasswordNotMatch,
      bool showPasswordDoesNotMeetCriteria,
      bool isPasswordHasThreeRepeatingConsecutiveDigits,
      bool isPasswordHasThreeSucceedingConsecutiveDigits,
      PasswordScreenType screen,
      String firstPassword,
      String? publicKey,
      String? challenge,
      String? sessionId,
      String? faceGuardToken});
}

/// @nodoc
class __$$_SetPasswordStateCopyWithImpl<T, $Res>
    extends _$SetPasswordStateCopyWithImpl<T, $Res, _$_SetPasswordState<T>>
    implements _$$_SetPasswordStateCopyWith<T, $Res> {
  __$$_SetPasswordStateCopyWithImpl(_$_SetPasswordState<T> _value,
      $Res Function(_$_SetPasswordState<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? failureOrSuccess = null,
    Object? password = null,
    Object? requiredPasswordLength = null,
    Object? showPasswordNotMatch = null,
    Object? showPasswordDoesNotMeetCriteria = null,
    Object? isPasswordHasThreeRepeatingConsecutiveDigits = null,
    Object? isPasswordHasThreeSucceedingConsecutiveDigits = null,
    Object? screen = null,
    Object? firstPassword = null,
    Object? publicKey = freezed,
    Object? challenge = freezed,
    Object? sessionId = freezed,
    Object? faceGuardToken = freezed,
  }) {
    return _then(_$_SetPasswordState<T>(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<SetPasswordFailure, T>>,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      requiredPasswordLength: null == requiredPasswordLength
          ? _value.requiredPasswordLength
          : requiredPasswordLength // ignore: cast_nullable_to_non_nullable
              as int,
      showPasswordNotMatch: null == showPasswordNotMatch
          ? _value.showPasswordNotMatch
          : showPasswordNotMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      showPasswordDoesNotMeetCriteria: null == showPasswordDoesNotMeetCriteria
          ? _value.showPasswordDoesNotMeetCriteria
          : showPasswordDoesNotMeetCriteria // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordHasThreeRepeatingConsecutiveDigits: null ==
              isPasswordHasThreeRepeatingConsecutiveDigits
          ? _value.isPasswordHasThreeRepeatingConsecutiveDigits
          : isPasswordHasThreeRepeatingConsecutiveDigits // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordHasThreeSucceedingConsecutiveDigits: null ==
              isPasswordHasThreeSucceedingConsecutiveDigits
          ? _value.isPasswordHasThreeSucceedingConsecutiveDigits
          : isPasswordHasThreeSucceedingConsecutiveDigits // ignore: cast_nullable_to_non_nullable
              as bool,
      screen: null == screen
          ? _value.screen
          : screen // ignore: cast_nullable_to_non_nullable
              as PasswordScreenType,
      firstPassword: null == firstPassword
          ? _value.firstPassword
          : firstPassword // ignore: cast_nullable_to_non_nullable
              as String,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      challenge: freezed == challenge
          ? _value.challenge
          : challenge // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      faceGuardToken: freezed == faceGuardToken
          ? _value.faceGuardToken
          : faceGuardToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_SetPasswordState<T> implements _SetPasswordState<T> {
  const _$_SetPasswordState(
      {required this.state,
      required this.failureOrSuccess,
      required this.password,
      required this.requiredPasswordLength,
      this.showPasswordNotMatch = false,
      this.showPasswordDoesNotMeetCriteria = false,
      this.isPasswordHasThreeRepeatingConsecutiveDigits = true,
      this.isPasswordHasThreeSucceedingConsecutiveDigits = true,
      this.screen = PasswordScreenType.password,
      this.firstPassword = '',
      this.publicKey,
      this.challenge,
      this.sessionId,
      this.faceGuardToken});

  @override
  final LoadingState state;
  @override
  final Option<Either<SetPasswordFailure, T>> failureOrSuccess;
  @override
  final String password;
  @override
  final int requiredPasswordLength;
  @override
  @JsonKey()
  final bool showPasswordNotMatch;
  @override
  @JsonKey()
  final bool showPasswordDoesNotMeetCriteria;
  @override
  @JsonKey()
  final bool isPasswordHasThreeRepeatingConsecutiveDigits;
  @override
  @JsonKey()
  final bool isPasswordHasThreeSucceedingConsecutiveDigits;
  @override
  @JsonKey()
  final PasswordScreenType screen;
  @override
  @JsonKey()
  final String firstPassword;
  @override
  final String? publicKey;
  @override
  final String? challenge;
  @override
  final String? sessionId;
  @override
  final String? faceGuardToken;

  @override
  String toString() {
    return 'SetPasswordState<$T>(state: $state, failureOrSuccess: $failureOrSuccess, password: $password, requiredPasswordLength: $requiredPasswordLength, showPasswordNotMatch: $showPasswordNotMatch, showPasswordDoesNotMeetCriteria: $showPasswordDoesNotMeetCriteria, isPasswordHasThreeRepeatingConsecutiveDigits: $isPasswordHasThreeRepeatingConsecutiveDigits, isPasswordHasThreeSucceedingConsecutiveDigits: $isPasswordHasThreeSucceedingConsecutiveDigits, screen: $screen, firstPassword: $firstPassword, publicKey: $publicKey, challenge: $challenge, sessionId: $sessionId, faceGuardToken: $faceGuardToken)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SetPasswordState<T> &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.failureOrSuccess, failureOrSuccess) ||
                other.failureOrSuccess == failureOrSuccess) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.requiredPasswordLength, requiredPasswordLength) ||
                other.requiredPasswordLength == requiredPasswordLength) &&
            (identical(other.showPasswordNotMatch, showPasswordNotMatch) ||
                other.showPasswordNotMatch == showPasswordNotMatch) &&
            (identical(other.showPasswordDoesNotMeetCriteria,
                    showPasswordDoesNotMeetCriteria) ||
                other.showPasswordDoesNotMeetCriteria ==
                    showPasswordDoesNotMeetCriteria) &&
            (identical(other.isPasswordHasThreeRepeatingConsecutiveDigits,
                    isPasswordHasThreeRepeatingConsecutiveDigits) ||
                other.isPasswordHasThreeRepeatingConsecutiveDigits ==
                    isPasswordHasThreeRepeatingConsecutiveDigits) &&
            (identical(other.isPasswordHasThreeSucceedingConsecutiveDigits,
                    isPasswordHasThreeSucceedingConsecutiveDigits) ||
                other.isPasswordHasThreeSucceedingConsecutiveDigits ==
                    isPasswordHasThreeSucceedingConsecutiveDigits) &&
            (identical(other.screen, screen) || other.screen == screen) &&
            (identical(other.firstPassword, firstPassword) ||
                other.firstPassword == firstPassword) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.challenge, challenge) ||
                other.challenge == challenge) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.faceGuardToken, faceGuardToken) ||
                other.faceGuardToken == faceGuardToken));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      state,
      failureOrSuccess,
      password,
      requiredPasswordLength,
      showPasswordNotMatch,
      showPasswordDoesNotMeetCriteria,
      isPasswordHasThreeRepeatingConsecutiveDigits,
      isPasswordHasThreeSucceedingConsecutiveDigits,
      screen,
      firstPassword,
      publicKey,
      challenge,
      sessionId,
      faceGuardToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SetPasswordStateCopyWith<T, _$_SetPasswordState<T>> get copyWith =>
      __$$_SetPasswordStateCopyWithImpl<T, _$_SetPasswordState<T>>(
          this, _$identity);
}

abstract class _SetPasswordState<T> implements SetPasswordState<T> {
  const factory _SetPasswordState(
      {required final LoadingState state,
      required final Option<Either<SetPasswordFailure, T>> failureOrSuccess,
      required final String password,
      required final int requiredPasswordLength,
      final bool showPasswordNotMatch,
      final bool showPasswordDoesNotMeetCriteria,
      final bool isPasswordHasThreeRepeatingConsecutiveDigits,
      final bool isPasswordHasThreeSucceedingConsecutiveDigits,
      final PasswordScreenType screen,
      final String firstPassword,
      final String? publicKey,
      final String? challenge,
      final String? sessionId,
      final String? faceGuardToken}) = _$_SetPasswordState<T>;

  @override
  LoadingState get state;
  @override
  Option<Either<SetPasswordFailure, T>> get failureOrSuccess;
  @override
  String get password;
  @override
  int get requiredPasswordLength;
  @override
  bool get showPasswordNotMatch;
  @override
  bool get showPasswordDoesNotMeetCriteria;
  @override
  bool get isPasswordHasThreeRepeatingConsecutiveDigits;
  @override
  bool get isPasswordHasThreeSucceedingConsecutiveDigits;
  @override
  PasswordScreenType get screen;
  @override
  String get firstPassword;
  @override
  String? get publicKey;
  @override
  String? get challenge;
  @override
  String? get sessionId;
  @override
  String? get faceGuardToken;
  @override
  @JsonKey(ignore: true)
  _$$_SetPasswordStateCopyWith<T, _$_SetPasswordState<T>> get copyWith =>
      throw _privateConstructorUsedError;
}
