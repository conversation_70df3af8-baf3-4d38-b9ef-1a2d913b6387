import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../../capp_auth_core.dart';

class PasswordRecoverySetPasswordShortcutBloc extends SetPasswordBloc<AuthenticationCompleteChangePasswordR?> {
  final IAuthenticationRepository authenticationRepository;

  PasswordRecoverySetPasswordShortcutBloc({
    required CappAuthTrackingService trackingService,
    required IPasswordLengthProvider passwordLengthProvider,
    required IEnvironmentConfigRepository environmentConfigRepository,
    required this.authenticationRepository,
  }) : super(
          passwordLengthProvider: passwordLengthProvider,
          environmentConfigRepository: environmentConfigRepository,
          trackingService: trackingService,
        );

  @override
  Future<void> submit(Emitter<SetPasswordState<AuthenticationCompleteChangePasswordR?>> emit) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        failureOrSuccess: none(),
      ),
    );

    final result = await authenticationRepository.authenticationChangePasswordPost(
      sessionId: state.sessionId!,
      password: state.password,
      challenge: state.challenge,
      publicKey: state.publicKey,
    );

    emit(
      result.fold(
        (l) {
          trackingService.trackForgotPinResetPinOnCriteriaViolationView();

          // Do not clean password/PIN when error is unexpected to
          // keep password in retry request
          final password = l.maybeMap(
            unexpected: (_) => state.password,
            orElse: () => '',
          );

          return state.copyWith(
            password: password,
            firstPassword: '',
            screen: PasswordScreenType.password,
            state: LoadingState.isCompleted,
            failureOrSuccess: optionOf(result),
            isPasswordHasThreeRepeatingConsecutiveDigits: true,
            isPasswordHasThreeSucceedingConsecutiveDigits: true,
            showPasswordDoesNotMeetCriteria: true,
          );
        },
        (_) => state.copyWith(
          password: '',
          failureOrSuccess: optionOf(result),
        ),
      ),
    );
  }
}
