import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../domain/environment_config/index.dart';
import '../../../domain/password_length_provider/i_password_length_provider.dart';
import '../../../infrastructure/tracking/capp_auth_tracking_service.dart';
import '../../../presentation/set_password/base/password_screen_type.dart';
import '../base/set_password_bloc.dart';

class PasswordRecoverySetPasswordBloc extends SetPasswordBloc<CappResetPasswordSessionCompleteResponse?> {
  final IResetPasswordRepository resetPasswordRepository;

  PasswordRecoverySetPasswordBloc({
    required this.resetPasswordRepository,
    required CappAuthTrackingService trackingService,
    required IPasswordLengthProvider passwordLengthProvider,
    required IEnvironmentConfigRepository environmentConfigRepository,
  }) : super(
          passwordLengthProvider: passwordLengthProvider,
          environmentConfigRepository: environmentConfigRepository,
          trackingService: trackingService,
        );

  @override
  Future<void> submit(Emitter<SetPasswordState<CappResetPasswordSessionCompleteResponse?>> emit) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        failureOrSuccess: none(),
      ),
    );

    final result = await resetPasswordRepository.completeSession(
      sessionId: state.sessionId!,
      password: state.password,
      publicKey: state.publicKey,
      challenge: state.challenge,
    );

    emit(
      result.fold(
        (l) {
          trackingService.trackForgotPinResetPinOnCriteriaViolationView();

          // Do not clean password/PIN when error is unexpected to
          // keep password in retry request
          final password = l.maybeMap(
            unexpected: (_) => state.password,
            orElse: () => '',
          );

          return state.copyWith(
            password: password,
            firstPassword: '',
            screen: PasswordScreenType.password,
            state: LoadingState.isCompleted,
            failureOrSuccess: optionOf(result),
            isPasswordHasThreeRepeatingConsecutiveDigits: true,
            isPasswordHasThreeSucceedingConsecutiveDigits: true,
            showPasswordDoesNotMeetCriteria: true,
          );
        },
        (_) => state.copyWith(
          password: '',
          failureOrSuccess: optionOf(result),
        ),
      ),
    );
  }
}
