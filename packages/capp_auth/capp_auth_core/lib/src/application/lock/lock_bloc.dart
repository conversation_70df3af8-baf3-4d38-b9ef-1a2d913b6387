// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:capp_legal_logs/capp_legal_logs_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:dartz/dartz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:installed_app_detector/installed_app_detector.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:rxdart/rxdart.dart';

import '../../../capp_auth_core.dart';

part 'lock_bloc.freezed.dart';
part 'lock_event.dart';
part 'lock_state.dart';

class LockBloc extends Bloc<LockEvent, LockState> {
  final IPasswordLengthProvider passwordLengthProvider;
  final IConnectUserRepository connectUserRepository;
  final StartupMeasurementService startupMeasurementService;
  final ICurrentUserRepository currentUserRepository;
  final LockStatusBloc lockStatusBloc;
  final ILegalLogTrackingService? legalLogTrackingService; // non null only in India
  final IUserBiometricRepository userBiometricRepository;
  final IBiometricProvider biometricProvider;
  final CappAuthTrackingService trackingService;
  final IFirebasePerformanceMonitoring firebasePerformanceMonitoring;
  final InstalledAppDetector? installedAppDetector;
  final IMaintenanceWindowRepository maintenanceWindowRepository;
  final String? snappDetectionPackageName;

  late final String localizedBiometricReason;

  LockBloc({
    required this.passwordLengthProvider,
    required this.startupMeasurementService,
    required this.connectUserRepository,
    required this.currentUserRepository,
    required this.lockStatusBloc,
    required this.biometricProvider,
    required this.userBiometricRepository,
    required this.trackingService,
    required this.firebasePerformanceMonitoring,
    required this.maintenanceWindowRepository,
    this.installedAppDetector,
    this.snappDetectionPackageName,
    this.legalLogTrackingService,
  }) : super(LockState.initialize()) {
    on<_Init>(_onInit, transformer: flatMap());
    on<_AddDigit>(_onAddDigit, transformer: flatMap());
    on<_Backspace>((_, emit) => _onBackspace(emit), transformer: flatMap());
    on<_Submit>((_, emit) => _onSubmit(emit), transformer: flatMap());
    on<_Clear>((_, emit) => _onClear(emit), transformer: flatMap());
    on<_BiometricPressed>((_, emit) => _onBiometricPressed(emit), transformer: flatMap());
  }

  EventTransformer<Event> flatMap<Event>() {
    return (events, mapper) => events.flatMap(mapper);
  }

  Future<bool> _isAppInstalled() async {
    if (snappDetectionPackageName == null) {
      return false;
    }
    final isAppInstalled = await installedAppDetector?.isAppInstalled(snappDetectionPackageName!);
    return isAppInstalled ?? false;
  }

  Future<void> _onInit(_Init event, Emitter<LockState> emit) async {
    localizedBiometricReason = event.localizedBiometricReason;

    lockStatusBloc.add(const LockStatusEvent.setEnabled(enabled: false));
    startupMeasurementService.stopMeasure(StartupMeasurementOrigin.lockScreen);

    final data = await Future.wait([
      currentUserRepository.getUser(),
      userBiometricRepository.getCurrentUserBiometricEnabled(),
      biometricProvider.availableBiometric(),
      biometricProvider.keyExits(),
      _isAppInstalled(),
      biometricProvider.publicKeyRegistered(),
    ]);

    final currentUser = (data[0]! as Either<UserFailure, CurrentUser>).fold((l) => null, (r) => r);
    final biometricEnabled = (data[1]! as Either<UserFailure, bool?>).fold((l) => false, (r) => r ?? false);
    final biometricType =
        (data[2]! as Either<BiometricAvailableFailure, BiometricAuthType>).fold((_) => null, (r) => r);
    var keyExist = data[3]! as bool;
    var publicKeyRegistered = data[5] as bool?;

    // biometric is enabled, but not migrated to use key-pair yet
    if (biometricEnabled && biometricType != null && !keyExist && publicKeyRegistered == null) {
      keyExist = await biometricProvider.createAndRegisterKeys();
      publicKeyRegistered = await biometricProvider.publicKeyRegistered();
    }

    emit(
      state.copyWith(
        userId: currentUser?.id,
        phoneNumber: currentUser?.phoneNumber ?? '',
        nickname: currentUser?.nickname,
        biometricEnabled: keyExist && publicKeyRegistered == true,
        biometricType: biometricType,
        requiredPasswordLength: passwordLengthProvider.passwordLength,
        forcedLogout: data[4]! as bool,
      ),
    );

    if (keyExist && publicKeyRegistered == true && biometricType != null) {
      add(const LockEvent.biometricPressed());
    }
  }

  void _onAddDigit(_AddDigit event, Emitter<LockState> emit) {
    final maxPasswordLength = state.requiredPasswordLength;
    if (state.password.length > maxPasswordLength || state.state == LoadingState.isLoading) {
      return;
    }

    final value = state.password + event.digit.toString();
    emit(
      state.copyWith(
        password: value,
        failureOrSuccess: none(),
      ),
    );

    if (value.length == maxPasswordLength) {
      add(const LockEvent.submit());
    }
  }

  void _onBackspace(Emitter<LockState> emit) {
    if (state.password.isEmpty || state.state == LoadingState.isLoading) {
      return;
    }

    final value = state.password.substring(0, state.password.length - 1);
    emit(
      state.copyWith(password: value),
    );
  }

  Future<void> _onSubmit(Emitter<LockState> emit) async {
    if (state.password.isEmpty || state.state == LoadingState.isLoading) {
      return;
    }

    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        failureOrSuccess: none(),
      ),
    );
    trackingService.trackUnlockScreenOnEnterPinClick();

    //start trace homepage when user has locked page
    unawaited(firebasePerformanceMonitoring.startTrace(TraceType.login));
    final result = await connectUserRepository.unlockWithPasswordAndId(
      password: state.password,
      userId: state.userId ?? '',
    );
    if (result.isRight()) {
      lockStatusBloc.add(const LockStatusEvent.setEnabled(enabled: true));
      unawaited(legalLogTrackingService?.trackUnlock(isBiometric: false));
    }

    emit(
      state.copyWith(
        failureOrSuccess: optionOf(result),
        state: LoadingState.isCompleted,
        password: '',
        maintenanceWindow: await maintenanceWindowRepository.getRemoteConfigMaintenance(),
      ),
    );
  }

  void _onClear(Emitter<LockState> emit) {
    emit(
      state.copyWith(
        password: '',
        state: LoadingState.isInitial,
      ),
    );
  }

  Future<void> _onBiometricPressed(Emitter<LockState> emit) async {
    final signature = await biometricProvider.createSignature(localizedBiometricReason);

    emit(state.copyWith(state: LoadingState.isLoading, failureOrSuccess: none()));

    await signature.fold((l) {
      emit(state.copyWith(state: LoadingState.isCompleted));
      return;
    }, (r) async {
      unawaited(firebasePerformanceMonitoring.startTrace(TraceType.login));
      final result = await biometricProvider.unlockWithBiometric(
        sessionId: r.sessionId,
        signature: r.signature,
      );
      if (result.isRight()) {
        lockStatusBloc.add(const LockStatusEvent.setEnabled(enabled: true));
        unawaited(legalLogTrackingService?.trackUnlock(isBiometric: true));
      }
      emit(
        state.copyWith(
          failureOrSuccess: optionOf(result),
          state: LoadingState.isCompleted,
          password: '',
          maintenanceWindow: await maintenanceWindowRepository.getRemoteConfigMaintenance(),
        ),
      );
    });
  }
}
