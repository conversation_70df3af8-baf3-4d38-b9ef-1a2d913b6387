import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'change_phone_init_state.dart';

class ChangePhoneInitCubit extends Cubit<ChangePhoneInitState> {
  final IChangePhoneRepository changePhoneRepository;

  ChangePhoneInitCubit({required this.changePhoneRepository}) : super(ChangePhoneInitState());

  Future<void> init() async {
    emit(ChangePhoneInitState(state: LoadingState.isLoading));

    final response = await changePhoneRepository.initSession();

    if (response.isLeft()) {
      emit(
        ChangePhoneInitState(
          state: LoadingState.isCompleted,
          isError: true,
        ),
      );
    } else {
      final r = response.fold((l) => null, (r) => r);
      emit(
        ChangePhoneInitState(
          response: r,
        ),
      );
    }
  }

  void stopLoading() {
    emit(
      ChangePhoneInitState(
        state: LoadingState.isCompleted,
      ),
    );
  }
}
