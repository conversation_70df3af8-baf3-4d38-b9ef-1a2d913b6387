part of 'pin_bloc.dart';

@Freezed(
  copyWith: false,
  equal: false,
  fromJson: false,
  toJson: false,
  map: FreezedMapOptions.none,
  when: FreezedWhenOptions.none,
)
class PinEvent with _$PinEvent {
  const factory PinEvent.init({
    String? sessionId,
    int? passwordLength,
    PrefilledInfo? prefilledInfo,
    bool? supportBiometric,
    String? localizedBiometricReason,
  }) = _Init;
  const factory PinEvent.addDigit(int digit) = _AddDigit;
  const factory PinEvent.backspace() = _Backspace;
  const factory PinEvent.submit() = _Submit;
  const factory PinEvent.clear() = _Clear;
  const factory PinEvent.biometricPressed() = _BiometricPressed;
}
