import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../capp_auth_core.dart';

typedef AuthenticationState = PhoneWithVerificationState<UserVerificationFailure, AuthenticationOtpResponse,
    UsernameVerificationSessionResponse>;

class AuthenticationBloc extends PhoneWithVerificationBloc<UserVerificationFailure, AuthenticationOtpResponse,
    UsernameVerificationSessionResponse> {
  final IAuthenticationRepository authenticationRepository;
  final IOtpRepository otpRepository;

  AuthenticationBloc({
    required this.authenticationRepository,
    required this.otpRepository,
  }) : super();

  @override
  Future<void> postValue(
    PostValue event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        phonePostResponse: none(),
        isUnexpectedError: none(),
      ),
    );

    final result = await authenticationRepository.authenticationStart(state.phoneNumber.value);

    if (result.isLeft()) {
      emit(
        state.copyWith(
          state: LoadingState.isCompleted,
          isUnexpectedError: optionOf(result.fold((l) => l, (r) => null)),
          phonePostResponse: optionOf(null),
        ),
      );
      return;
    }

    final usernameVerificationResponse = result.fold((l) => null, (r) => r)!;
    final newState = state.copyWith(
      sessionId: usernameVerificationResponse.sessionId ?? usernameVerificationResponse.loginSessionId,
      publicKey: usernameVerificationResponse.publicKey,
      challenge: usernameVerificationResponse.challenge,
    );

    if (state.externalLoginResponse == null || usernameVerificationResponse.sessionId == null) {
      emit(
        newState.copyWith(
          state: LoadingState.isCompleted,
          isUnexpectedError: none(),
          phonePostResponse: optionOf(usernameVerificationResponse),
          externalLoginResponse: null,
          externalLoginVerificationOtpToken: null,
        ),
      );
      return;
    }

    // only for truecaller
    final otpTokenResponse = await otpRepository.validateExternalLoginResponse(
      externalLoginResponse: state.externalLoginResponse!,
      otpType: OtpRequestType.usernameVerificationCapp,
      secret: usernameVerificationResponse.sessionId,
    );

    if (otpTokenResponse.isLeft()) {
      emit(
        result.fold(
          (l) => newState,
          (r) => newState.copyWith(
            challenge: r.challenge,
            publicKey: r.publicKey,
            state: LoadingState.isCompleted,
            isUnexpectedError: none(),
            phonePostResponse: optionOf(r),
            sessionId: r.sessionId ?? r.loginSessionId,
          ),
        ),
      );
      return;
    }

    emit(
      newState.copyWith(
        phonePostResponse: optionOf(usernameVerificationResponse),
        externalLoginVerificationOtpToken: otpTokenResponse.fold((l) => null, (r) => r),
        state: LoadingState.isCompleted,
      ),
    );
  }
}
