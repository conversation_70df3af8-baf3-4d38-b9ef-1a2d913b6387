import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:koyal_auth/koyal_auth.dart';

import '../../../../capp_auth_core.dart';

class RetrieveAccount2ndIdBloc extends SecondIdentifierBloc<RetrieveAccountSecondIdVerificationResponse> {
  final IRetrieveAccountRepository retrieveAccountRepository;
  final ICurrentUserRepository userRepository;

  RetrieveAccount2ndIdBloc({
    required this.retrieveAccountRepository,
    required this.userRepository,
    required IEnvironmentConfigRepository environmentConfigRepository,
  }) : super(environmentConfigRepository: environmentConfigRepository);

  @override
  Future<Either<UserVerificationFailure, RetrieveAccountSecondIdVerificationResponse>> post() {
    return retrieveAccountRepository.retrieveAccountVerify2ndId(
      idType: state.selectedId!,
      value: state.idValue,
      sessionId: state.sessionId!,
    );
  }

  @override
  Future<Either<UserVerificationFailure, SecondIdentifierStart>> initSession() async {
    final user = await userRepository.getCurrentUser();
    final phoneNumber = user?.phoneNumber;

    if (phoneNumber != null) {
      final result = await retrieveAccountRepository.retrieveAccountStart(phoneNumber);
      return result.map(
        (r) => SecondIdentifierStart(
          sessionId: r.sessionId,
          allowedSecondIds: r.allowed2ndIds,
          allowedAlternativeSteps: r.allowedAlternativeSteps,
        ),
      );
    }
    return left(UserVerificationFailure(type: UserVerificationFailureType.unexpected));
  }
}
