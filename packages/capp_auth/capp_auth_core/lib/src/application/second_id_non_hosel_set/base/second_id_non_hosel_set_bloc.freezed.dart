// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'second_id_non_hosel_set_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$SecondIdNonHoselSetEvent {}

/// @nodoc

class _$_Init implements _Init {
  const _$_Init(
      {this.usernameVerificationSessionId,
      final List<UserSecondIdType>? available2ndIds,
      this.signUpSessionId})
      : _available2ndIds = available2ndIds;

  @override
  final String? usernameVerificationSessionId;
  final List<UserSecondIdType>? _available2ndIds;
  @override
  List<UserSecondIdType>? get available2ndIds {
    final value = _available2ndIds;
    if (value == null) return null;
    if (_available2ndIds is EqualUnmodifiableListView) return _available2ndIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? signUpSessionId;

  @override
  String toString() {
    return 'SecondIdNonHoselSetEvent.init(usernameVerificationSessionId: $usernameVerificationSessionId, available2ndIds: $available2ndIds, signUpSessionId: $signUpSessionId)';
  }
}

abstract class _Init implements SecondIdNonHoselSetEvent {
  const factory _Init(
      {final String? usernameVerificationSessionId,
      final List<UserSecondIdType>? available2ndIds,
      final String? signUpSessionId}) = _$_Init;

  String? get usernameVerificationSessionId;
  List<UserSecondIdType>? get available2ndIds;
  String? get signUpSessionId;
}

/// @nodoc

class _$_Select implements _Select {
  const _$_Select(this.selectedId);

  @override
  final UserSecondIdType selectedId;

  @override
  String toString() {
    return 'SecondIdNonHoselSetEvent.select(selectedId: $selectedId)';
  }
}

abstract class _Select implements SecondIdNonHoselSetEvent {
  const factory _Select(final UserSecondIdType selectedId) = _$_Select;

  UserSecondIdType get selectedId;
}

/// @nodoc

class _$_ChangeValue implements _ChangeValue {
  const _$_ChangeValue(this.value, {this.isIdValueVerified});

  @override
  final String? value;
  @override
  final bool? isIdValueVerified;

  @override
  String toString() {
    return 'SecondIdNonHoselSetEvent.changeValue(value: $value, isIdValueVerified: $isIdValueVerified)';
  }
}

abstract class _ChangeValue implements SecondIdNonHoselSetEvent {
  const factory _ChangeValue(final String? value,
      {final bool? isIdValueVerified}) = _$_ChangeValue;

  String? get value;
  bool? get isIdValueVerified;
}

/// @nodoc

class _$_Submit implements _Submit {
  const _$_Submit({this.verificationToken});

  @override
  final String? verificationToken;

  @override
  String toString() {
    return 'SecondIdNonHoselSetEvent.submit(verificationToken: $verificationToken)';
  }
}

abstract class _Submit implements SecondIdNonHoselSetEvent {
  const factory _Submit({final String? verificationToken}) = _$_Submit;

  String? get verificationToken;
}

/// @nodoc

class _$_SetDataConstent implements _SetDataConstent {
  const _$_SetDataConstent({this.isDataConsentGiven});

  @override
  final bool? isDataConsentGiven;

  @override
  String toString() {
    return 'SecondIdNonHoselSetEvent.setDataConstent(isDataConsentGiven: $isDataConsentGiven)';
  }
}

abstract class _SetDataConstent implements SecondIdNonHoselSetEvent {
  const factory _SetDataConstent({final bool? isDataConsentGiven}) =
      _$_SetDataConstent;

  bool? get isDataConsentGiven;
}

/// @nodoc
mixin _$SecondIdNonHoselSetState {
  LoadingState get state => throw _privateConstructorUsedError;
  List<UserSecondIdType> get allowedIds => throw _privateConstructorUsedError;
  UserSecondIdType? get selectedId => throw _privateConstructorUsedError;
  String? get idValue => throw _privateConstructorUsedError;
  bool get isDataConsentGiven => throw _privateConstructorUsedError;
  bool get isIdValueVerified => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  String? get challenge => throw _privateConstructorUsedError;
  Option<Either<SignUpFailure, Unit>> get failureOrSuccess =>
      throw _privateConstructorUsedError;
  bool get isValid => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SecondIdNonHoselSetStateCopyWith<SecondIdNonHoselSetState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SecondIdNonHoselSetStateCopyWith<$Res> {
  factory $SecondIdNonHoselSetStateCopyWith(SecondIdNonHoselSetState value,
          $Res Function(SecondIdNonHoselSetState) then) =
      _$SecondIdNonHoselSetStateCopyWithImpl<$Res, SecondIdNonHoselSetState>;
  @useResult
  $Res call(
      {LoadingState state,
      List<UserSecondIdType> allowedIds,
      UserSecondIdType? selectedId,
      String? idValue,
      bool isDataConsentGiven,
      bool isIdValueVerified,
      String? sessionId,
      String? publicKey,
      String? challenge,
      Option<Either<SignUpFailure, Unit>> failureOrSuccess,
      bool isValid});
}

/// @nodoc
class _$SecondIdNonHoselSetStateCopyWithImpl<$Res,
        $Val extends SecondIdNonHoselSetState>
    implements $SecondIdNonHoselSetStateCopyWith<$Res> {
  _$SecondIdNonHoselSetStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? allowedIds = null,
    Object? selectedId = freezed,
    Object? idValue = freezed,
    Object? isDataConsentGiven = null,
    Object? isIdValueVerified = null,
    Object? sessionId = freezed,
    Object? publicKey = freezed,
    Object? challenge = freezed,
    Object? failureOrSuccess = null,
    Object? isValid = null,
  }) {
    return _then(_value.copyWith(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      allowedIds: null == allowedIds
          ? _value.allowedIds
          : allowedIds // ignore: cast_nullable_to_non_nullable
              as List<UserSecondIdType>,
      selectedId: freezed == selectedId
          ? _value.selectedId
          : selectedId // ignore: cast_nullable_to_non_nullable
              as UserSecondIdType?,
      idValue: freezed == idValue
          ? _value.idValue
          : idValue // ignore: cast_nullable_to_non_nullable
              as String?,
      isDataConsentGiven: null == isDataConsentGiven
          ? _value.isDataConsentGiven
          : isDataConsentGiven // ignore: cast_nullable_to_non_nullable
              as bool,
      isIdValueVerified: null == isIdValueVerified
          ? _value.isIdValueVerified
          : isIdValueVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      challenge: freezed == challenge
          ? _value.challenge
          : challenge // ignore: cast_nullable_to_non_nullable
              as String?,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<SignUpFailure, Unit>>,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SecondIdNonHoselSetStateCopyWith<$Res>
    implements $SecondIdNonHoselSetStateCopyWith<$Res> {
  factory _$$_SecondIdNonHoselSetStateCopyWith(
          _$_SecondIdNonHoselSetState value,
          $Res Function(_$_SecondIdNonHoselSetState) then) =
      __$$_SecondIdNonHoselSetStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState state,
      List<UserSecondIdType> allowedIds,
      UserSecondIdType? selectedId,
      String? idValue,
      bool isDataConsentGiven,
      bool isIdValueVerified,
      String? sessionId,
      String? publicKey,
      String? challenge,
      Option<Either<SignUpFailure, Unit>> failureOrSuccess,
      bool isValid});
}

/// @nodoc
class __$$_SecondIdNonHoselSetStateCopyWithImpl<$Res>
    extends _$SecondIdNonHoselSetStateCopyWithImpl<$Res,
        _$_SecondIdNonHoselSetState>
    implements _$$_SecondIdNonHoselSetStateCopyWith<$Res> {
  __$$_SecondIdNonHoselSetStateCopyWithImpl(_$_SecondIdNonHoselSetState _value,
      $Res Function(_$_SecondIdNonHoselSetState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? allowedIds = null,
    Object? selectedId = freezed,
    Object? idValue = freezed,
    Object? isDataConsentGiven = null,
    Object? isIdValueVerified = null,
    Object? sessionId = freezed,
    Object? publicKey = freezed,
    Object? challenge = freezed,
    Object? failureOrSuccess = null,
    Object? isValid = null,
  }) {
    return _then(_$_SecondIdNonHoselSetState(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      allowedIds: null == allowedIds
          ? _value._allowedIds
          : allowedIds // ignore: cast_nullable_to_non_nullable
              as List<UserSecondIdType>,
      selectedId: freezed == selectedId
          ? _value.selectedId
          : selectedId // ignore: cast_nullable_to_non_nullable
              as UserSecondIdType?,
      idValue: freezed == idValue
          ? _value.idValue
          : idValue // ignore: cast_nullable_to_non_nullable
              as String?,
      isDataConsentGiven: null == isDataConsentGiven
          ? _value.isDataConsentGiven
          : isDataConsentGiven // ignore: cast_nullable_to_non_nullable
              as bool,
      isIdValueVerified: null == isIdValueVerified
          ? _value.isIdValueVerified
          : isIdValueVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      challenge: freezed == challenge
          ? _value.challenge
          : challenge // ignore: cast_nullable_to_non_nullable
              as String?,
      failureOrSuccess: null == failureOrSuccess
          ? _value.failureOrSuccess
          : failureOrSuccess // ignore: cast_nullable_to_non_nullable
              as Option<Either<SignUpFailure, Unit>>,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_SecondIdNonHoselSetState implements _SecondIdNonHoselSetState {
  const _$_SecondIdNonHoselSetState(
      {required this.state,
      required final List<UserSecondIdType> allowedIds,
      this.selectedId,
      this.idValue,
      this.isDataConsentGiven = true,
      this.isIdValueVerified = false,
      this.sessionId,
      this.publicKey,
      this.challenge,
      required this.failureOrSuccess,
      this.isValid = false})
      : _allowedIds = allowedIds;

  @override
  final LoadingState state;
  final List<UserSecondIdType> _allowedIds;
  @override
  List<UserSecondIdType> get allowedIds {
    if (_allowedIds is EqualUnmodifiableListView) return _allowedIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allowedIds);
  }

  @override
  final UserSecondIdType? selectedId;
  @override
  final String? idValue;
  @override
  @JsonKey()
  final bool isDataConsentGiven;
  @override
  @JsonKey()
  final bool isIdValueVerified;
  @override
  final String? sessionId;
  @override
  final String? publicKey;
  @override
  final String? challenge;
  @override
  final Option<Either<SignUpFailure, Unit>> failureOrSuccess;
  @override
  @JsonKey()
  final bool isValid;

  @override
  String toString() {
    return 'SecondIdNonHoselSetState(state: $state, allowedIds: $allowedIds, selectedId: $selectedId, idValue: $idValue, isDataConsentGiven: $isDataConsentGiven, isIdValueVerified: $isIdValueVerified, sessionId: $sessionId, publicKey: $publicKey, challenge: $challenge, failureOrSuccess: $failureOrSuccess, isValid: $isValid)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SecondIdNonHoselSetState &&
            (identical(other.state, state) || other.state == state) &&
            const DeepCollectionEquality()
                .equals(other._allowedIds, _allowedIds) &&
            (identical(other.selectedId, selectedId) ||
                other.selectedId == selectedId) &&
            (identical(other.idValue, idValue) || other.idValue == idValue) &&
            (identical(other.isDataConsentGiven, isDataConsentGiven) ||
                other.isDataConsentGiven == isDataConsentGiven) &&
            (identical(other.isIdValueVerified, isIdValueVerified) ||
                other.isIdValueVerified == isIdValueVerified) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.challenge, challenge) ||
                other.challenge == challenge) &&
            (identical(other.failureOrSuccess, failureOrSuccess) ||
                other.failureOrSuccess == failureOrSuccess) &&
            (identical(other.isValid, isValid) || other.isValid == isValid));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      state,
      const DeepCollectionEquality().hash(_allowedIds),
      selectedId,
      idValue,
      isDataConsentGiven,
      isIdValueVerified,
      sessionId,
      publicKey,
      challenge,
      failureOrSuccess,
      isValid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SecondIdNonHoselSetStateCopyWith<_$_SecondIdNonHoselSetState>
      get copyWith => __$$_SecondIdNonHoselSetStateCopyWithImpl<
          _$_SecondIdNonHoselSetState>(this, _$identity);
}

abstract class _SecondIdNonHoselSetState implements SecondIdNonHoselSetState {
  const factory _SecondIdNonHoselSetState(
      {required final LoadingState state,
      required final List<UserSecondIdType> allowedIds,
      final UserSecondIdType? selectedId,
      final String? idValue,
      final bool isDataConsentGiven,
      final bool isIdValueVerified,
      final String? sessionId,
      final String? publicKey,
      final String? challenge,
      required final Option<Either<SignUpFailure, Unit>> failureOrSuccess,
      final bool isValid}) = _$_SecondIdNonHoselSetState;

  @override
  LoadingState get state;
  @override
  List<UserSecondIdType> get allowedIds;
  @override
  UserSecondIdType? get selectedId;
  @override
  String? get idValue;
  @override
  bool get isDataConsentGiven;
  @override
  bool get isIdValueVerified;
  @override
  String? get sessionId;
  @override
  String? get publicKey;
  @override
  String? get challenge;
  @override
  Option<Either<SignUpFailure, Unit>> get failureOrSuccess;
  @override
  bool get isValid;
  @override
  @JsonKey(ignore: true)
  _$$_SecondIdNonHoselSetStateCopyWith<_$_SecondIdNonHoselSetState>
      get copyWith => throw _privateConstructorUsedError;
}
