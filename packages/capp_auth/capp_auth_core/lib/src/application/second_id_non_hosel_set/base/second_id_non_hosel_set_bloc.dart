import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../domain/environment_config/index.dart';
import '../../second_id_verification/base/second_id_verifier_mixin.dart';

part 'second_id_non_hosel_set_bloc.freezed.dart';
part 'second_id_non_hosel_set_event.dart';
part 'second_id_non_hosel_set_state.dart';

class SecondIdNonHoselSetBloc extends Bloc<SecondIdNonHoselSetEvent, SecondIdNonHoselSetState>
    with SecondIdVerificationMixin {
  final IEnvironmentConfigRepository environmentConfigRepository;

  SecondIdNonHoselSetBloc({
    required this.environmentConfigRepository,
  }) : super(SecondIdNonHoselSetState.initialize()) {
    on<_Init>(_onInit);
    on<_Select>(_onSelect);
    on<_SetDataConstent>(
      (event, emit) => emit(
        state.copyWith(
          isDataConsentGiven: event.isDataConsentGiven ?? true,
        ),
      ),
    );
    on<_ChangeValue>(_onChangeValue);
    on<_Submit>(_onSubmit);
  }

  Future<void> _onInit(_Init event, Emitter<SecondIdNonHoselSetState> emit) async {
    if (event.available2ndIds == null || event.signUpSessionId == null) {
      final response = await getAllowedSecondIds(event.usernameVerificationSessionId!);

      emit(
        response.fold(
          (l) {
            return state.copyWith(
              failureOrSuccess: optionOf(left(l)),
              state: LoadingState.isCompleted,
            );
          },
          (r) {
            return state.copyWith(
              allowedIds: r.available2ndIds,
              selectedId: r.available2ndIds.length == 1 ? r.available2ndIds[0] : null,
              sessionId: r.sessionId,
              publicKey: r.publicKey,
              challenge: r.challenge,
              state: LoadingState.isCompleted,
            );
          },
        ),
      );
    } else {
      emit(
        state.copyWith(
          allowedIds: event.available2ndIds!,
          selectedId: event.available2ndIds!.length == 1 ? event.available2ndIds![0] : null,
          sessionId: event.signUpSessionId,
          state: LoadingState.isCompleted,
        ),
      );
    }
  }

  void _onSelect(_Select event, Emitter<SecondIdNonHoselSetState> emit) {
    if (state.state == LoadingState.isLoading) return;

    emit(
      state.copyWith(
        selectedId: event.selectedId,
        idValue: '',
      ),
    );
  }

  void _onChangeValue(_ChangeValue event, Emitter<SecondIdNonHoselSetState> emit) {
    if (state.state == LoadingState.isLoading) return;

    emit(
      state.copyWith(
        idValue: event.value,
        isIdValueVerified: event.isIdValueVerified ?? false,
        isValid: validate(
          event.value,
          state.selectedId,
          RegExp(environmentConfigRepository.currentConfig().cappIdentityConfiguration.emailRegex),
        ),
      ),
    );
  }

  Future<void> _onSubmit(_Submit event, Emitter<SecondIdNonHoselSetState> emit) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        failureOrSuccess: none(),
      ),
    );

    final response = await post(event.verificationToken);

    emit(
      state.copyWith(
        state: LoadingState.isCompleted,
        failureOrSuccess: optionOf(response),
      ),
    );
  }

  // this methods is supposed to be overriden, but cannot be marked abstract
  Future<Either<SignUpFailure, Unit>> post(String? otpVerificationToken) {
    throw UnimplementedError();
  }

  Future<Either<SignUpFailure, StartUserSignUpSessionResponse>> getAllowedSecondIds(String sessionId) {
    throw UnimplementedError();
  }
}
