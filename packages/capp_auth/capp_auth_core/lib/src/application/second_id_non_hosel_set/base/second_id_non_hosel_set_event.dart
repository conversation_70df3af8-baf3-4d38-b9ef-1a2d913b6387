part of 'second_id_non_hosel_set_bloc.dart';

@Freezed(
  copyWith: false,
  equal: false,
  fromJson: false,
  toJson: false,
  map: FreezedMapOptions.none,
  when: FreezedWhenOptions.none,
)
class SecondIdNonHoselSetEvent with _$SecondIdNonHoselSetEvent {
  const factory SecondIdNonHoselSetEvent.init({
    String? usernameVerificationSessionId,
    List<UserSecondIdType>? available2ndIds,
    String? signUpSessionId,
  }) = _Init;
  const factory SecondIdNonHoselSetEvent.select(UserSecondIdType selectedId) = _Select;
  const factory SecondIdNonHoselSetEvent.changeValue(String? value, {bool? isIdValueVerified}) = _ChangeValue;
  const factory SecondIdNonHoselSetEvent.submit({String? verificationToken}) = _Submit;
  const factory SecondIdNonHoselSetEvent.setDataConstent({bool? isDataConsentGiven}) = _SetDataConstent;
}
