import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:koyal_auth/koyal_auth.dart';

import '../../../domain/environment_config/index.dart';
import '../base/second_id_non_hosel_set_bloc.dart';

class ChangeSecondIdBloc extends SecondIdNonHoselSetBloc {
  final ICappSignUpRepository cappSignUpRepository;
  ChangeSecondIdBloc({
    required this.cappSignUpRepository,
    required IEnvironmentConfigRepository environmentConfigRepository,
  }) : super(environmentConfigRepository: environmentConfigRepository);

  @override
  Future<Either<SignUpFailure, Unit>> post(String? otpVerificationToken) {
    if (state.selectedId == UserSecondIdType.email) {
      return cappSignUpRepository.postChangeVerifiedEmail(
        email: state.idValue,
        isScraped: state.isIdValueVerified,
        otpVerificationToken: otpVerificationToken,
      );
    }

    return cappSignUpRepository.postChangedSecondId(
      idType: state.selectedId,
      value: state.idValue,
    );
  }

  @override
  Future<Either<SignUpFailure, StartUserSignUpSessionResponse>> getAllowedSecondIds(String sessionId) {
    return cappSignUpRepository.getAllowedSecondIds();
  }
}
