import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';

part 'change_phone_bos_verification_state.dart';

class ChangePhoneBosVerificationCubit extends Cubit<ChangePhoneBosVerificationState> {
  final IChangePhoneRepository changePhoneRepository;
  final IBigDataCollectionService bigDataCollectionService;

  Timer? _pollingTimer;

  ChangePhoneBosVerificationCubit({
    required this.changePhoneRepository,
    required this.bigDataCollectionService,
  }) : super(ChangePhoneBosVerificationInitial());

  Future<void> startChangePhoneSelfie({
    required String sessionId,
    required String phoneNumber,
    required String dmsUuid,
    required ReasonOfChange reasonOfChange,
    required DisplayData displayData,
  }) async {
    final collectedData = await bigDataCollectionService.process(
      displayData: displayData,
      flow: BigDataFlow.changePrimaryPhone.getEventCode(),
    );

    final result = await changePhoneRepository.postNewPhoneNumberWithSelfieV3(
      sessionId: sessionId,
      phoneNumber: phoneNumber,
      dmsUuid: dmsUuid,
      reasonOfChange: reasonOfChange,
      userTechnicalData: collectedData != null
          ? CollectedDataToUserTechnicalDataMapper.mapCollectedDataToUserTechnicalData(collectedData)
          : null,
    );

    result.fold(
      (l) => _completeWithUnsuccess(UserVerificationFailure(type: UserVerificationFailureType.invalid)),
      (r) => _startPeriodicPolling(sessionId, r ?? 0),
    );
  }

  void _startPeriodicPolling(String sessionId, int pollingDelay) {
    _pollingTimer = Timer.periodic(Duration(seconds: pollingDelay), (timer) async {
      _closePeriodicResender();

      final response = await changePhoneRepository.getChangePhoneNumberStatusV3(sessionId: sessionId);
      response.fold(_completeWithUnsuccess, (r) {
        final callAgainInterval = r.callAgainInterval;
        if (callAgainInterval != null) {
          _startPeriodicPolling(sessionId, callAgainInterval);
          return;
        }

        if (r.status == ChangePhoneNumberStatusV3.updated) {
          emit(ChangePhoneBosVerificationUpdated());
        } else if (r.status == ChangePhoneNumberStatusV3.rejected) {
          emit(ChangePhoneBosVerificationRejected());
        } else if (r.status == ChangePhoneNumberStatusV3.maxAttemptsReached) {
          emit(ChangePhoneBosVerificationMaxAttemptsReached());
        } else if (r.status == ChangePhoneNumberStatusV3.retry) {
          emit(ChangePhoneBosVerificationRetry());
        }
      });
    });
  }

  void _completeWithUnsuccess(UserVerificationFailure failure) {
    emit(ChangePhoneBosVerificationError(failure));
    _closePeriodicResender();
  }

  void _closePeriodicResender() {
    _pollingTimer?.cancel();
  }

  @override
  Future<void> close() {
    _closePeriodicResender();
    return super.close();
  }
}
