import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../domain/model/phone_number.dart';

part 'phone_with_verification_bloc.freezed.dart';
part 'phone_with_verification_event.dart';
part 'phone_with_verification_state.dart';

class PhoneWithVerificationBloc<FailureType, SuccessType, CustomData>
    extends Bloc<PhoneWithVerificationEvent, PhoneWithVerificationState<FailureType, SuccessType, CustomData>> {
  PhoneWithVerificationBloc() : super(PhoneWithVerificationState<FailureType, SuccessType, CustomData>.initialize()) {
    on<Init>(_onInit);
    on<GetLocalPhoneNumbers>(
      (event, emit) => emit(
        state.copyWith(
          showPhoneHintDialog: true,
        ),
      ),
    );
    on<PhoneValue>(_onPhoneValue);
    on<ClearValue>((_, emit) => _onClearValue(emit));
    on<SetExternalLoginResponse>(_onSetExternalLoginResponse);
    on<PostValue>(postValue);
  }

  void _onInit(
    Init event,
    Emitter<PhoneWithVerificationState<FailureType, SuccessType, CustomData>> emit,
  ) {
    if (event.sessionId != null) {
      emit(
        state.copyWith(sessionId: event.sessionId),
      );
    }

    add(PhoneWithVerificationEvent.setValue(value: event.phoneNumber ?? ''));

    if ((event.phoneNumber ?? '').isEmpty) {
      add(const PhoneWithVerificationEvent.getLocalPhoneNumbers());
    } else {
      emit(
        state.copyWith(
          emptyOnFirstTap: true,
        ),
      );
    }
  }

  void _onPhoneValue(
    PhoneValue event,
    Emitter<PhoneWithVerificationState<FailureType, SuccessType, CustomData>> emit,
  ) {
    if (state.state == LoadingState.isLoading) return;

    emit(
      state.copyWith(
        state: LoadingState.isCompleted,
        phoneNumber: PhoneNumber(event.value),
        showPhoneHintDialog: false,
        externalLoginResponse: null,
      ),
    );
  }

  void _onClearValue(
    Emitter<PhoneWithVerificationState<FailureType, SuccessType, CustomData>> emit,
  ) {
    emit(
      state.copyWith(
        state: LoadingState.isCompleted,
        phoneNumber: const PhoneNumber(''),
        showPhoneHintDialog: false,
        externalLoginResponse: null,
        emptyOnFirstTap: false,
      ),
    );
  }

  void _onSetExternalLoginResponse(
    SetExternalLoginResponse event,
    Emitter<PhoneWithVerificationState<FailureType, SuccessType, CustomData>> emit,
  ) {
    emit(
      state.copyWith(
        state: LoadingState.isCompleted,
        phoneNumber: PhoneNumber(event.externalLoginResponse.phoneNumber ?? ''),
        showPhoneHintDialog: false,
        externalLoginResponse: event.externalLoginResponse,
      ),
    );
    add(const PhoneWithVerificationEvent.postValue());
  }

  Future<void> postValue(
    PostValue event,
    Emitter<PhoneWithVerificationState<FailureType, SuccessType, CustomData>> emit,
  ) async {}
}
