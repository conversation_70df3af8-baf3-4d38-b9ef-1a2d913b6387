// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'phone_with_verification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PhoneWithVerificationEvent {}

/// @nodoc

class _$Init implements Init {
  const _$Init({this.sessionId, this.phoneNumber});

  @override
  final String? sessionId;
  @override
  final String? phoneNumber;

  @override
  String toString() {
    return 'PhoneWithVerificationEvent.initialize(sessionId: $sessionId, phoneNumber: $phoneNumber)';
  }
}

abstract class Init implements PhoneWithVerificationEvent {
  const factory Init({final String? sessionId, final String? phoneNumber}) =
      _$Init;

  String? get sessionId;
  String? get phoneNumber;
}

/// @nodoc

class _$PostValue implements PostValue {
  const _$PostValue();

  @override
  String toString() {
    return 'PhoneWithVerificationEvent.postValue()';
  }
}

abstract class PostValue implements PhoneWithVerificationEvent {
  const factory PostValue() = _$PostValue;
}

/// @nodoc

class _$GetLocalPhoneNumbers implements GetLocalPhoneNumbers {
  const _$GetLocalPhoneNumbers();

  @override
  String toString() {
    return 'PhoneWithVerificationEvent.getLocalPhoneNumbers()';
  }
}

abstract class GetLocalPhoneNumbers implements PhoneWithVerificationEvent {
  const factory GetLocalPhoneNumbers() = _$GetLocalPhoneNumbers;
}

/// @nodoc

class _$PhoneValue implements PhoneValue {
  const _$PhoneValue({required this.value});

  @override
  final String value;

  @override
  String toString() {
    return 'PhoneWithVerificationEvent.setValue(value: $value)';
  }
}

abstract class PhoneValue implements PhoneWithVerificationEvent {
  const factory PhoneValue({required final String value}) = _$PhoneValue;

  String get value;
}

/// @nodoc

class _$ClearValue implements ClearValue {
  const _$ClearValue();

  @override
  String toString() {
    return 'PhoneWithVerificationEvent.clearValue()';
  }
}

abstract class ClearValue implements PhoneWithVerificationEvent {
  const factory ClearValue() = _$ClearValue;
}

/// @nodoc

class _$SetExternalLoginResponse implements SetExternalLoginResponse {
  const _$SetExternalLoginResponse({required this.externalLoginResponse});

  @override
  final ExternalLoginResponse externalLoginResponse;

  @override
  String toString() {
    return 'PhoneWithVerificationEvent.setExternalLoginResponse(externalLoginResponse: $externalLoginResponse)';
  }
}

abstract class SetExternalLoginResponse implements PhoneWithVerificationEvent {
  const factory SetExternalLoginResponse(
          {required final ExternalLoginResponse externalLoginResponse}) =
      _$SetExternalLoginResponse;

  ExternalLoginResponse get externalLoginResponse;
}

/// @nodoc
mixin _$PhoneWithVerificationState<FailureType, SuccessType, CustomData> {
  LoadingState get state => throw _privateConstructorUsedError;
  Option<FailureType> get isUnexpectedError =>
      throw _privateConstructorUsedError;
  Option<CustomData> get phonePostResponse =>
      throw _privateConstructorUsedError;
  PhoneNumber get phoneNumber => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  String? get publicKey => throw _privateConstructorUsedError;
  String? get challenge => throw _privateConstructorUsedError;
  bool get showPhoneHintDialog => throw _privateConstructorUsedError;
  bool get emptyOnFirstTap => throw _privateConstructorUsedError;
  ExternalLoginResponse? get externalLoginResponse =>
      throw _privateConstructorUsedError;
  String? get externalLoginVerificationOtpToken =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PhoneWithVerificationStateCopyWith<FailureType, SuccessType, CustomData,
          PhoneWithVerificationState<FailureType, SuccessType, CustomData>>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhoneWithVerificationStateCopyWith<FailureType, SuccessType,
    CustomData, $Res> {
  factory $PhoneWithVerificationStateCopyWith(
      PhoneWithVerificationState<FailureType, SuccessType, CustomData> value,
      $Res Function(
              PhoneWithVerificationState<FailureType, SuccessType, CustomData>)
          then) = _$PhoneWithVerificationStateCopyWithImpl<
      FailureType,
      SuccessType,
      CustomData,
      $Res,
      PhoneWithVerificationState<FailureType, SuccessType, CustomData>>;
  @useResult
  $Res call(
      {LoadingState state,
      Option<FailureType> isUnexpectedError,
      Option<CustomData> phonePostResponse,
      PhoneNumber phoneNumber,
      String? sessionId,
      String? publicKey,
      String? challenge,
      bool showPhoneHintDialog,
      bool emptyOnFirstTap,
      ExternalLoginResponse? externalLoginResponse,
      String? externalLoginVerificationOtpToken});
}

/// @nodoc
class _$PhoneWithVerificationStateCopyWithImpl<
        FailureType,
        SuccessType,
        CustomData,
        $Res,
        $Val extends PhoneWithVerificationState<FailureType, SuccessType,
            CustomData>>
    implements
        $PhoneWithVerificationStateCopyWith<FailureType, SuccessType,
            CustomData, $Res> {
  _$PhoneWithVerificationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? isUnexpectedError = null,
    Object? phonePostResponse = null,
    Object? phoneNumber = null,
    Object? sessionId = freezed,
    Object? publicKey = freezed,
    Object? challenge = freezed,
    Object? showPhoneHintDialog = null,
    Object? emptyOnFirstTap = null,
    Object? externalLoginResponse = freezed,
    Object? externalLoginVerificationOtpToken = freezed,
  }) {
    return _then(_value.copyWith(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isUnexpectedError: null == isUnexpectedError
          ? _value.isUnexpectedError
          : isUnexpectedError // ignore: cast_nullable_to_non_nullable
              as Option<FailureType>,
      phonePostResponse: null == phonePostResponse
          ? _value.phonePostResponse
          : phonePostResponse // ignore: cast_nullable_to_non_nullable
              as Option<CustomData>,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as PhoneNumber,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      challenge: freezed == challenge
          ? _value.challenge
          : challenge // ignore: cast_nullable_to_non_nullable
              as String?,
      showPhoneHintDialog: null == showPhoneHintDialog
          ? _value.showPhoneHintDialog
          : showPhoneHintDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      emptyOnFirstTap: null == emptyOnFirstTap
          ? _value.emptyOnFirstTap
          : emptyOnFirstTap // ignore: cast_nullable_to_non_nullable
              as bool,
      externalLoginResponse: freezed == externalLoginResponse
          ? _value.externalLoginResponse
          : externalLoginResponse // ignore: cast_nullable_to_non_nullable
              as ExternalLoginResponse?,
      externalLoginVerificationOtpToken: freezed ==
              externalLoginVerificationOtpToken
          ? _value.externalLoginVerificationOtpToken
          : externalLoginVerificationOtpToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PhoneWithVerificationStateCopyWith<FailureType, SuccessType,
        CustomData, $Res>
    implements
        $PhoneWithVerificationStateCopyWith<FailureType, SuccessType,
            CustomData, $Res> {
  factory _$$_PhoneWithVerificationStateCopyWith(
      _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData> value,
      $Res Function(
              _$_PhoneWithVerificationState<FailureType, SuccessType,
                  CustomData>)
          then) = __$$_PhoneWithVerificationStateCopyWithImpl<FailureType,
      SuccessType, CustomData, $Res>;
  @override
  @useResult
  $Res call(
      {LoadingState state,
      Option<FailureType> isUnexpectedError,
      Option<CustomData> phonePostResponse,
      PhoneNumber phoneNumber,
      String? sessionId,
      String? publicKey,
      String? challenge,
      bool showPhoneHintDialog,
      bool emptyOnFirstTap,
      ExternalLoginResponse? externalLoginResponse,
      String? externalLoginVerificationOtpToken});
}

/// @nodoc
class __$$_PhoneWithVerificationStateCopyWithImpl<FailureType, SuccessType,
        CustomData, $Res>
    extends _$PhoneWithVerificationStateCopyWithImpl<
        FailureType,
        SuccessType,
        CustomData,
        $Res,
        _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData>>
    implements
        _$$_PhoneWithVerificationStateCopyWith<FailureType, SuccessType,
            CustomData, $Res> {
  __$$_PhoneWithVerificationStateCopyWithImpl(
      _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData>
          _value,
      $Res Function(
              _$_PhoneWithVerificationState<FailureType, SuccessType,
                  CustomData>)
          _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? isUnexpectedError = null,
    Object? phonePostResponse = null,
    Object? phoneNumber = null,
    Object? sessionId = freezed,
    Object? publicKey = freezed,
    Object? challenge = freezed,
    Object? showPhoneHintDialog = null,
    Object? emptyOnFirstTap = null,
    Object? externalLoginResponse = freezed,
    Object? externalLoginVerificationOtpToken = freezed,
  }) {
    return _then(
        _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData>(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      isUnexpectedError: null == isUnexpectedError
          ? _value.isUnexpectedError
          : isUnexpectedError // ignore: cast_nullable_to_non_nullable
              as Option<FailureType>,
      phonePostResponse: null == phonePostResponse
          ? _value.phonePostResponse
          : phonePostResponse // ignore: cast_nullable_to_non_nullable
              as Option<CustomData>,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as PhoneNumber,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      publicKey: freezed == publicKey
          ? _value.publicKey
          : publicKey // ignore: cast_nullable_to_non_nullable
              as String?,
      challenge: freezed == challenge
          ? _value.challenge
          : challenge // ignore: cast_nullable_to_non_nullable
              as String?,
      showPhoneHintDialog: null == showPhoneHintDialog
          ? _value.showPhoneHintDialog
          : showPhoneHintDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      emptyOnFirstTap: null == emptyOnFirstTap
          ? _value.emptyOnFirstTap
          : emptyOnFirstTap // ignore: cast_nullable_to_non_nullable
              as bool,
      externalLoginResponse: freezed == externalLoginResponse
          ? _value.externalLoginResponse
          : externalLoginResponse // ignore: cast_nullable_to_non_nullable
              as ExternalLoginResponse?,
      externalLoginVerificationOtpToken: freezed ==
              externalLoginVerificationOtpToken
          ? _value.externalLoginVerificationOtpToken
          : externalLoginVerificationOtpToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData>
    implements
        _PhoneWithVerificationState<FailureType, SuccessType, CustomData> {
  const _$_PhoneWithVerificationState(
      {required this.state,
      required this.isUnexpectedError,
      required this.phonePostResponse,
      required this.phoneNumber,
      this.sessionId,
      this.publicKey,
      this.challenge,
      required this.showPhoneHintDialog,
      this.emptyOnFirstTap = false,
      this.externalLoginResponse,
      this.externalLoginVerificationOtpToken});

  @override
  final LoadingState state;
  @override
  final Option<FailureType> isUnexpectedError;
  @override
  final Option<CustomData> phonePostResponse;
  @override
  final PhoneNumber phoneNumber;
  @override
  final String? sessionId;
  @override
  final String? publicKey;
  @override
  final String? challenge;
  @override
  final bool showPhoneHintDialog;
  @override
  @JsonKey()
  final bool emptyOnFirstTap;
  @override
  final ExternalLoginResponse? externalLoginResponse;
  @override
  final String? externalLoginVerificationOtpToken;

  @override
  String toString() {
    return 'PhoneWithVerificationState<$FailureType, $SuccessType, $CustomData>(state: $state, isUnexpectedError: $isUnexpectedError, phonePostResponse: $phonePostResponse, phoneNumber: $phoneNumber, sessionId: $sessionId, publicKey: $publicKey, challenge: $challenge, showPhoneHintDialog: $showPhoneHintDialog, emptyOnFirstTap: $emptyOnFirstTap, externalLoginResponse: $externalLoginResponse, externalLoginVerificationOtpToken: $externalLoginVerificationOtpToken)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PhoneWithVerificationState<FailureType, SuccessType,
                CustomData> &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.isUnexpectedError, isUnexpectedError) ||
                other.isUnexpectedError == isUnexpectedError) &&
            (identical(other.phonePostResponse, phonePostResponse) ||
                other.phonePostResponse == phonePostResponse) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.publicKey, publicKey) ||
                other.publicKey == publicKey) &&
            (identical(other.challenge, challenge) ||
                other.challenge == challenge) &&
            (identical(other.showPhoneHintDialog, showPhoneHintDialog) ||
                other.showPhoneHintDialog == showPhoneHintDialog) &&
            (identical(other.emptyOnFirstTap, emptyOnFirstTap) ||
                other.emptyOnFirstTap == emptyOnFirstTap) &&
            (identical(other.externalLoginResponse, externalLoginResponse) ||
                other.externalLoginResponse == externalLoginResponse) &&
            (identical(other.externalLoginVerificationOtpToken,
                    externalLoginVerificationOtpToken) ||
                other.externalLoginVerificationOtpToken ==
                    externalLoginVerificationOtpToken));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      state,
      isUnexpectedError,
      phonePostResponse,
      phoneNumber,
      sessionId,
      publicKey,
      challenge,
      showPhoneHintDialog,
      emptyOnFirstTap,
      externalLoginResponse,
      externalLoginVerificationOtpToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PhoneWithVerificationStateCopyWith<FailureType, SuccessType, CustomData,
          _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData>>
      get copyWith => __$$_PhoneWithVerificationStateCopyWithImpl<
          FailureType,
          SuccessType,
          CustomData,
          _$_PhoneWithVerificationState<FailureType, SuccessType,
              CustomData>>(this, _$identity);
}

abstract class _PhoneWithVerificationState<FailureType, SuccessType, CustomData>
    implements
        PhoneWithVerificationState<FailureType, SuccessType, CustomData> {
  const factory _PhoneWithVerificationState(
          {required final LoadingState state,
          required final Option<FailureType> isUnexpectedError,
          required final Option<CustomData> phonePostResponse,
          required final PhoneNumber phoneNumber,
          final String? sessionId,
          final String? publicKey,
          final String? challenge,
          required final bool showPhoneHintDialog,
          final bool emptyOnFirstTap,
          final ExternalLoginResponse? externalLoginResponse,
          final String? externalLoginVerificationOtpToken}) =
      _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData>;

  @override
  LoadingState get state;
  @override
  Option<FailureType> get isUnexpectedError;
  @override
  Option<CustomData> get phonePostResponse;
  @override
  PhoneNumber get phoneNumber;
  @override
  String? get sessionId;
  @override
  String? get publicKey;
  @override
  String? get challenge;
  @override
  bool get showPhoneHintDialog;
  @override
  bool get emptyOnFirstTap;
  @override
  ExternalLoginResponse? get externalLoginResponse;
  @override
  String? get externalLoginVerificationOtpToken;
  @override
  @JsonKey(ignore: true)
  _$$_PhoneWithVerificationStateCopyWith<FailureType, SuccessType, CustomData,
          _$_PhoneWithVerificationState<FailureType, SuccessType, CustomData>>
      get copyWith => throw _privateConstructorUsedError;
}
