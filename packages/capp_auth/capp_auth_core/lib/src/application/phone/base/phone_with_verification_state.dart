part of 'phone_with_verification_bloc.dart';

@Freezed(
  fromJson: false,
  toJson: false,
  map: FreezedMapOptions.none,
  when: FreezedWhenOptions.none,
)
class PhoneWithVerificationState<FailureType, SuccessType, CustomData>
    with _$PhoneWithVerificationState<FailureType, SuccessType, CustomData> {
  const factory PhoneWithVerificationState({
    required LoadingState state,
    required Option<FailureType> isUnexpectedError,
    required Option<CustomData> phonePostResponse,
    required PhoneNumber phoneNumber,
    String? sessionId,
    String? publicKey,
    String? challenge,
    required bool showPhoneHintDialog,
    @Default(false) bool emptyOnFirstTap,
    ExternalLoginResponse? externalLoginResponse,
    String? externalLoginVerificationOtpToken,
  }) = _PhoneWithVerificationState;

  factory PhoneWithVerificationState.initialize() => PhoneWithVerificationState(
        state: LoadingState.isInitial,
        phonePostResponse: none(),
        isUnexpectedError: none(),
        phoneNumber: const PhoneNumber(''),
        showPhoneHintDialog: false,
      );
}
