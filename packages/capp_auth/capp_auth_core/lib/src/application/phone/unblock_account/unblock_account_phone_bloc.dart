import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../base/phone_with_verification_bloc.dart';

typedef UnblockAccountPhoneState
    = PhoneWithVerificationState<UserVerificationFailure, UnblockAccountSessionResponse, bool>;

class UnblockAccountPhoneBloc
    extends PhoneWithVerificationBloc<UserVerificationFailure, UnblockAccountSessionResponse, bool> {
  final IUnblockAccountRepository unblockAccountRepository;
  final IOtpRepository otpRepository;

  UnblockAccountPhoneBloc({
    required this.unblockAccountRepository,
    required this.otpRepository,
  }) : super();

  @override
  Future<void> postValue(
    PostValue event,
    Emitter<UnblockAccountPhoneState> emit,
  ) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        phonePostResponse: optionOf(false),
      ),
    );

    if (state.externalLoginResponse == null) {
      emit(
        state.copyWith(
          phonePostResponse: optionOf(true),
          state: LoadingState.isCompleted,
        ),
      );
      return;
    }

    final otpTokenResponse = await otpRepository.validateExternalLoginResponse(
      externalLoginResponse: state.externalLoginResponse!,
      otpType: OtpRequestType.unblockAccountCapp,
    );
    if (otpTokenResponse.isLeft()) {
      emit(
        state.copyWith(
          state: LoadingState.isCompleted,
          phonePostResponse: optionOf(true),
          externalLoginResponse: null,
          externalLoginVerificationOtpToken: null,
        ),
      );
      return;
    }

    emit(
      state.copyWith(
        phonePostResponse: optionOf(true),
        externalLoginVerificationOtpToken: otpTokenResponse.fold((l) => null, (r) => r),
        state: LoadingState.isCompleted,
      ),
    );
  }
}
