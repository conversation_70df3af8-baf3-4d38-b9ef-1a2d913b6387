part of 'retrieve_account_authenticate_verification_cubit.dart';

@immutable
abstract class RetrieveAccountAuthenticateVerificationState {}

class RetrieveAccountAuthenticateVerificationInitial extends RetrieveAccountAuthenticateVerificationState {}

class RetrieveAccountAuthenticateVerificationError extends RetrieveAccountAuthenticateVerificationState {
  final UserVerificationFailure failure;

  RetrieveAccountAuthenticateVerificationError(this.failure);
}

class RetrieveAccountAuthenticateVerificationApprove extends RetrieveAccountAuthenticateVerificationState {
  RetrieveAccountAuthenticateVerificationApprove();
}

class RetrieveAccountAuthenticateVerificationNotApprove extends RetrieveAccountAuthenticateVerificationState {
  final AuthenticateBosResult bosResult;
  final AuthenticateAllowedNextSteps? nextSteps;

  RetrieveAccountAuthenticateVerificationNotApprove({
    required this.bosResult,
    required this.nextSteps,
  });
}
