import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:capp_innovatrics_core/capp_innovatrics_core.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../capp_auth_core.dart';

part 'retrieve_account_identify_verification_state.dart';

class RetrieveAccountIdentifyVerificationCubit extends Cubit<RetrieveAccountIdentifyVerificationState> {
  final IRetrieveAccountRepository retrieveAccountRepository;
  final IUserRepository userRepository;
  final IConnectUserRepository connectUserRepository;
  final GlobalTrackingProperties gtp;

  Timer? _pollingTimer;

  RetrieveAccountIdentifyVerificationCubit({
    required this.retrieveAccountRepository,
    required this.userRepository,
    required this.connectUserRepository,
    required this.gtp,
  }) : super(RetrieveAccountIdentifyVerificationInitial());

  Future<void> startBosIdentifyHandling({
    required String sessionId,
    required IdCardResult? idCardResult,
    required String? selfieId,
  }) async {
    final result = await retrieveAccountRepository.retrieveAccountIdentifyPost(
      sessionId: sessionId,
      retrieveAccountIdentifyRequest: RetrieveAccountIdentifyRequest(
        userDocumentIds: [
          if (selfieId != null) selfieId,
          if (idCardResult != null) idCardResult.uploadedImageId!,
        ],
        customerInformation:
            idCardResult != null ? IdCardResulMapper.mapIdCardResultToCustomerInformation(idCardResult) : null,
      ),
    );
    result.fold(
      (l) => _completeWithUnsuccess(UserVerificationFailure(type: l.type)),
      (r) => _startPeriodicPolling(sessionId, r.initialWaitSeconds ?? 0),
    );
  }

  void _startPeriodicPolling(String sessionId, int pollingDelay) {
    _pollingTimer = Timer.periodic(Duration(seconds: pollingDelay), (timer) async {
      _closePeriodicResender();

      final response = await retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId);
      response.fold(_completeWithUnsuccess, (r) async {
        if (r.callAgainInterval != null) {
          _startPeriodicPolling(sessionId, r.callAgainInterval!);
          return;
        }

        final bosResult = r.bosResult;
        if (bosResult != null) {
          if (bosResult == IdentifyBosResult.approved) {
            await connectUserRepository.refreshToken(updateClaims: true);
            gtp.cuid = await userRepository.userCuid();

            emit(const RetrieveAccountIdentifyVerificationApproved());
          } else if (bosResult == IdentifyBosResult.canceled) {
            emit(
              RetrieveAccountIdentifyVerificationCanceled(
                needsSelfie: r.identifyDocuments?.selfie ?? false,
                needsIdCard: r.identifyDocuments?.idCard ?? false,
              ),
            );
          } else if (bosResult == IdentifyBosResult.rejected) {
            emit(const RetrieveAccountIdentifyVerificationRejected());
          } else if (bosResult == IdentifyBosResult.failedToFinish) {
            _completeWithUnsuccess(UserVerificationFailure(type: UserVerificationFailureType.invalid));
          } else {
            _completeWithUnsuccess(UserVerificationFailure(type: UserVerificationFailureType.invalid));
          }
          _closePeriodicResender();
        } else {
          emit(const RetrieveAccountIdentifyVerificationManualDeduplication());
        }
      });
    });
  }

  void _completeWithUnsuccess(UserVerificationFailure failure) {
    emit(RetrieveAccountIdentifyVerificationError(failure));
    _closePeriodicResender();
  }

  void _closePeriodicResender() {
    _pollingTimer?.cancel();
  }

  @override
  Future<void> close() {
    _closePeriodicResender();
    return super.close();
  }
}
