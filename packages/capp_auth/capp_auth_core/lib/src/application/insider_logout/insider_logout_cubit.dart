import 'package:bloc/bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

part 'insider_logout_state.dart';

class InsiderLogoutCubit extends Cubit<InsiderLogoutState> {
  final ICurrentUserRepository currentUserRepository;
  final IConnectUserRepository connectUserRepository;
  final IFeatureFlagInitService featureFlagInitService;
  final IEnvironmentRepository environmentRepository;

  final String insiderBaseUrl;
  final String insiderStageBaseUrl;
  final String insiderPerformanceBaseUrl;

  InsiderLogoutCubit({
    required this.currentUserRepository,
    required this.connectUserRepository,
    required this.featureFlagInitService,
    required this.environmentRepository,
    required this.insiderBaseUrl,
    required this.insiderStageBaseUrl,
    required this.insiderPerformanceBaseUrl,
  }) : super(InsiderLogoutState(state: LoadingState.isInitial));

  // load userId from storage and store it to state
  Future<void> init() async {
    emit(InsiderLogoutState(state: LoadingState.isLoading));

    final environmentType = await environmentRepository.getEnvironmentType();
    final userId = await currentUserRepository.userId();
    final becomeInsiderUserUrl = '${findProperUrl(environmentType)}?userId=$userId';

    emit(
      InsiderLogoutState(
        // isCompletedButNeedMore - so that UI knows it has data but should not pop the dialog yet
        state: LoadingState.isCompletedButNeedMore,
        userId: userId,
        url: becomeInsiderUserUrl,
        environmentType: environmentType,
      ),
    );
  }

  // after becomming an insider Tokens and Feature Flags need to be refrehed
  Future<void> refreshUser() async {
    emit(
      InsiderLogoutState(
        state: LoadingState.isLoading,
        userId: state.userId,
      ),
    );

    await connectUserRepository.refreshToken(updateClaims: true);
    await featureFlagInitService.onUserChanged();

    final isInsider = await currentUserRepository.isInsider();

    if (!isClosed) {
      emit(
        InsiderLogoutState(
          state: isInsider ? LoadingState.isCompleted : LoadingState.isUnexpectedError,
          userId: state.userId,
        ),
      );
    }
  }

  String findProperUrl(EnvironmentType type) {
    switch (type) {
      case EnvironmentType.main:
        return environmentRepository.getAllEnvironmentUrl()?.main.insiderBaseUrl ?? insiderBaseUrl;
      case EnvironmentType.performance:
        return environmentRepository.getAllEnvironmentUrl()?.performance?.insiderBaseUrl ?? insiderPerformanceBaseUrl;
      case EnvironmentType.staging:
        return environmentRepository.getAllEnvironmentUrl()?.stage?.insiderBaseUrl ?? insiderStageBaseUrl;
    }
  }
}
