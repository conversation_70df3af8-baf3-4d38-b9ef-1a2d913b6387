import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  final ICurrentUserRepository userRepository;

  LoginCubit(this.userRepository) : super(LoginInitial());

  Future<bool> isUserLoggedIn() async {
    emit(LoginLoading());

    final user = await userRepository.getUser();
    final loggedIn = user.fold((l) => false, (r) {
      if (r.isAnonymous) {
        return false;
      } else {
        return true;
      }
    });

    emit(LoginDone());
    return loggedIn;
  }
}
