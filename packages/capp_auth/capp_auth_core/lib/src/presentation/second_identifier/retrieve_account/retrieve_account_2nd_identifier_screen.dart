import 'package:capp_innovatrics_core/capp_innovatrics_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';
import 'package:uuid/uuid.dart';

import '../../../../capp_auth_core.dart';

class RetrieveAccount2ndIdentifierScreen
    extends SecondIdentifierVerificationScreenBase<RetrieveAccountSecondIdVerificationResponse> {
  @override
  void trackAuthVerificationOnGoBackClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackRetrieveAccountVerificationOnGoBackClick();
  }

  @override
  void trackSecondIdentifierOnConfirm(BuildContext context, UserSecondIdType secondId) {
    context.get<CappAuthTrackingService>().trackRetrieveAccountVerificationConfirm2ndIdTypeClick(secondId);
  }

  @override
  void trackSecondIdentifierOnNotCustomerClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackRetrieveAccountVerificationNotCustomerClick();
  }

  @override
  void onSuccess({
    required RetrieveAccountSecondIdVerificationResponse r,
    required String sessionId,
    required UserSecondIdType secondIdentifierType,
    required String secondIdValue,
    required BuildContext context,
  }) {
    // Navigate to verification screen which verifies result from BOS authenticate
    context.navigator.push(
      path: NavigatorPath.cappAuth.retrieveAccountAuthenticateVerificationScreen,
      arguments: RetrieveAccountAuthenticateVerificationScreenArguments(
        secondIdentifierType: secondIdentifierType,
        secondIdentifierValue: secondIdValue,
        sessionId: sessionId,
        prefilledInfo: args.prefilledInfo,
        initialWaitTime: r.initialWaitTime,
      ),
    );
  }

  @override
  void onSelfieResult({
    required BuildContext context,
    required String sessionId,
    SelfieResult? result,
  }) {
    if (result?.uuid != null) {
      context.navigator.push(
        path: NavigatorPath.cappAuth.retrieveAccountAuthenticateVerificationScreen,
        arguments: RetrieveAccountAuthenticateVerificationScreenArguments(
          secondIdentifierType: UserSecondIdType.selfie,
          secondIdentifierValue: result?.uuid,
          sessionId: sessionId,
          verifySecondIdentifier: true,
        ),
      );
    }
  }

  @override
  void navigateToIdCardScreen({
    required BuildContext context,
    required String selfieId,
    required String sessionId,
    PrefilledInfo? prefilledInfo,
  }) {
    context.navigator
        .pushFromPackage(
      package: 'CappInnovatrics',
      screen: 'IdCardScreen',
      arguments: InnovatricsIdCardArguments(
        cameraType: CameraType.document,
        flowStage: InnovatricsFlowStage.registration,
        flowInstanceId: const Uuid().v4(),
        bypassSelection: true,
      ),
    )
        .then((value) {
      if (value == null) {
        if (!context.mounted) return;
        context.navigator.pop();
      } else {
        if (!context.mounted) return;
        context.navigator.pushReplacement(
          path: NavigatorPath.cappAuth.retrieveAccountIdentifyVerificationScreen,
          arguments: RetrieveAccountIdentifyVerificationScreenArguments(
            idCardResult: value as IdCardResult,
            selfieId: selfieId,
            sessionId: sessionId,
            prefilledInfo: prefilledInfo,
            bypassSelection: true,
          ),
        );
      }
    });
  }

  @override
  void onNotHciCustomerClick(BuildContext context, String? sessionId) {
    context.externalNavigator.onAuthComplete(context);
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<SecondIdentifierBloc>(
        create: (context) => context.get<RetrieveAccount2ndIdBloc>()..add(const SecondIdentifierEvent.init()),
        child: this,
      );

  RetrieveAccount2ndIdentifierScreen({
    Key? key,
    required SecondIdentifierScreenArguments arguments,
    DateTime? initRegistrationDateBirth,
  }) : super(
          key: key,
          arguments: arguments,
          initRegistrationDateBirth: initRegistrationDateBirth,
        );
}
