import 'package:capp_innovatrics_core/capp_innovatrics_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class Authentication2ndIdentifierScreen
    extends SecondIdentifierVerificationScreenBase<AuthenticationSecondIdVerificationResponse> {
  @override
  void trackAuthVerificationOnGoBackClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackAuthVerificationOnGoBackClick();
  }

  @override
  void trackSecondIdentifierOnConfirm(BuildContext context, UserSecondIdType secondId) {
    context.get<CappAuthTrackingService>().trackAuthVerificationConfirm2ndIdTypeClick(secondId);
  }

  @override
  void trackSecondIdentifierOnNotCustomerClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackAuthVerificationNotCustomerClick();
  }

  @override
  void onSuccess({
    required AuthenticationSecondIdVerificationResponse r,
    required String sessionId,
    required UserSecondIdType secondIdentifierType,
    required String secondIdValue,
    required BuildContext context,
  }) {
    if (r.loginSessionId != null) {
      context.navigator.push(
        path: NavigatorPath.cappAuth.loginPinScreen,
        arguments: PinScreenArguments(
          loginSessionId: r.loginSessionId!,
          passwordLength: r.passwordLength,
          prefilledInfo: args.prefilledInfo,
          challenge: r.challenge,
          publicKey: r.publicKey,
        ),
      );
    } else {
      // Navigate to verification screen which verifies result from BOS authenticate
      context.navigator.push(
        path: NavigatorPath.cappAuth.authenticationAuthenticateVerificationScreen,
        arguments: AuthenticationAuthenticateVerificationScreenArguments(
          secondIdentifierType: secondIdentifierType,
          secondIdentifierValue: secondIdValue,
          sessionId: sessionId,
          passwordLength: r.passwordLength,
          prefilledInfo: args.prefilledInfo,
          initialWaitTime: r.initialWaitTime,
          loginChallenge: args.loginChallenge,
          loginPublicKey: args.loginPublicKey,
        ),
      );
    }
  }

  @override
  void onSelfieResult({
    required BuildContext context,
    required String sessionId,
    SelfieResult? result,
  }) {
    if (result?.uuid != null) {
      context.navigator.push(
        path: NavigatorPath.cappAuth.authenticationAuthenticateVerificationScreen,
        arguments: AuthenticationAuthenticateVerificationScreenArguments(
          secondIdentifierType: UserSecondIdType.selfie,
          secondIdentifierValue: result?.uuid,
          sessionId: sessionId,
          verifySecondIdentifier: true,
          loginChallenge: args.loginChallenge,
          loginPublicKey: args.loginPublicKey,
        ),
      );
    }
  }

  @override
  void onNotHciCustomerClick(BuildContext context, String? sessionId) {
    context.navigator.pushNamed(
      NavigatorPath.cappAuth.registrationSetPasswordScreen,
      arguments: SetPasswordScreenArguments(
        sessionId: sessionId,
        publicKey: args.loginPublicKey,
        challenge: args.loginChallenge,
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<SecondIdentifierBloc>(
        create: (context) => context.get<Authentication2ndIdBloc>()
          ..add(
            SecondIdentifierEvent.init(
              sessionId: args.sessionId,
              allowedIds: args.allowedSecondIds,
              escape: args.escape,
            ),
          ),
        child: this,
      );

  Authentication2ndIdentifierScreen({
    Key? key,
    required SecondIdentifierScreenArguments arguments,
    DateTime? initRegistrationDateBirth,
  }) : super(
          key: key,
          arguments: arguments,
          initRegistrationDateBirth: initRegistrationDateBirth,
        );
}
