import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class UnblockAccountSuccessScreen extends StatelessScreen with RouteWrapper {
  final Widget widgetLogoBuilder;

  UnblockAccountSuccessScreen({
    Key? key,
    UnblockAccountSuccessScreenArguments? arguments,
    required this.widgetLogoBuilder,
  }) : super(key: key, arguments: arguments);

  @override
  Widget wrappedRoute(BuildContext context) => this;

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      key: const Key('__unblockAccountSuccessScreen__'),
      body: InfoScreen(
        title: L10nCappAuth.of(context).unblockSuccessHeadingTitle,
        subTitle: L10nCappAuth.of(context).unblockSuccessHeadingSubtitle,
        image: widgetLogoBuilder,
        primaryButtonKey: getPrimaryKeyButton(),
        primaryButtonText: getPrimaryButtonText(context),
        primaryButtonOnClick: () {
          primaryButtonClick(context);
        },
        tertiaryButtonKey: getTertiaryKeyButton(),
        tertiaryButtonText: getTertiaryButtonText(context),
        tertiaryButtonOnClick: () {
          tertiaryButtonClick(context);
        },
        alignContentCenter: true,
        showButtonsWithoutFooter: true,
      ),
    );
  }

  Key getPrimaryKeyButton() => const Key('__unblockAccountLoginButton__');

  Key getTertiaryKeyButton() => const Key('__unblockAccountForgotPasswordButton__');

  String getPrimaryButtonText(BuildContext context) => L10nCappAuth.of(context).login;

  String getTertiaryButtonText(BuildContext context) => L10nCappAuth.of(context).forgotPinpass;

  void primaryButtonClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackAccountUnblockedScreenOnLoginClick();
    context.navigateToLogoutScreen(LogoutReason.userLogout);
  }

  void tertiaryButtonClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackAccountUnblockedScreenOnForgotPinClick();
    context.get<CappAuthTrackingService>().trackAccountUnblockedScreenOnForgotPasswordClick();
    context.navigator.push(
      path: NavigatorPath.cappAuth.pinPassRecoveryEntryScreen,
      arguments: PinPassRecoveryPhoneScreenArguments(
        prefilledInfo: (arguments as UnblockAccountSuccessScreenArguments?)?.prefilledInfo,
      ),
    );
  }
}
