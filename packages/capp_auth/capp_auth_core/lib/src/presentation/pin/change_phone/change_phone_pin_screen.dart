import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class ChangePhonePinScreen extends PinScreenBase<Unit> {
  final ChangePhonePinScreenArguments args;
  ChangePhonePinScreen({Key? key, required ChangePhonePinScreenArguments arguments})
      : args = arguments,
        super(key: key, arguments: arguments);

  @override
  void trackPinScreenOnGoBackClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberEnterPinOnGoBackClick();
  }

  @override
  void trackTemporaryBlockDialogView(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberEnterPassErrorIncorrectPassAccountTempBlockedView();
  }

  @override
  void trackPermanentBlockDialogView(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberEnterPinOnErrorIncorrectPinAccountPermBlockedView();
  }

  @override
  void trackIncorrectDialogView(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberEnterPinOnErrorIncorrectPinView();
  }

  @override
  void trackChangePhoneNumberEnterPinOnForgotPinClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberEnterPinOnForgotPinClick();
  }

  @override
  void onSuccess(Unit r, BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberEnterPassOnConfirmClick();
    context.navigator.push(
      path: NavigatorPath.cappAuth.changePhoneNewPhoneScreen,
      arguments: ChangePhoneNewPhoneScreenArguments(
        sessionId: (arguments! as PinScreenArguments).loginSessionId,
        includeSuccessScreen: args.includeSuccessScreen,
      ),
    );
  }

  @override
  void onForbiddenAccess(BuildContext context) {
    showUnableToChangePhoneDialog(context);
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<PinBloc<Unit>>(
        create: (ctx) => context.get<ChangePhonePinBloc>()
          ..add(
            PinEvent.init(
              sessionId: (arguments! as PinScreenArguments).loginSessionId,
              passwordLength: (arguments! as PinScreenArguments).passwordLength,
            ),
          ),
        child: this,
      );
}
