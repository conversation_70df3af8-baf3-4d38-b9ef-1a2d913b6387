import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class PinScreenBase<T> extends StatelessScreen with RouteWrapper {
  PinScreenBase({Key? key, ScreenArguments? arguments}) : super(key: key, arguments: arguments);

  void onSuccess(T r, BuildContext context) {}
  void onForbiddenAccess(BuildContext context) {
    showAuthErrorUnexpectedErrorDialog(context);
  }

  void trackPinScreenOnGoBackClick(BuildContext context) {}
  void trackTemporaryBlockDialogView(BuildContext context) {}
  void trackPermanentBlockDialogView(BuildContext context) {}
  void trackIncorrectDialogView(BuildContext context) {}
  void trackChangePhoneNumberEnterPinOnForgotPinClick(BuildContext context) {}
  void trackFaceBiometricDialogPopUp(BuildContext context) {}
  void trackFaceBiometricDialogCancel(BuildContext context) {}
  void trackTouchBiometricDialogPopUp(BuildContext context) {}
  void trackTouchBiometricDialogCancel(BuildContext context) {}

  bool biometricFeatureFlag(BuildContext context) {
    return context.isFlagEnabledRead(FeatureFlag.biometricUnlock);
  }

  String getTitleText(BuildContext context) => '';
  String getHeadingText(BuildContext context) => L10nCappAuth.of(context).loginPinHeading;
  Widget getHeadingIcon(BuildContext context) => const SizedBox();

  @override
  Widget wrappedRoute(BuildContext context) => this;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (ctx) => context.get<AuthErrorHandlerCubit>(),
      child: MultiBlocListener(
        listeners: [
          AuthErrorListener(
            trackTemporaryBlockDialogView: trackTemporaryBlockDialogView,
            trackPermanentBlockDialogView: trackPermanentBlockDialogView,
            trackIncorrectDialogView: trackIncorrectDialogView,
          ),
          BlocListener<PinBloc<T>, PinState>(
            listener: (context, state) {
              if (state.isBiometricDialogShowing) {
                if (state.biometricType == BiometricAuthType.fingerprint ||
                    state.biometricType == BiometricAuthType.touchId) {
                  trackTouchBiometricDialogPopUp(context);
                }
                if (state.biometricType == BiometricAuthType.face || state.biometricType == BiometricAuthType.faceId) {
                  trackFaceBiometricDialogPopUp(context);
                }
              } else if (state.biometricError == BiometricFailure.userCanceled) {
                if (state.biometricType == BiometricAuthType.fingerprint ||
                    state.biometricType == BiometricAuthType.touchId) {
                  trackTouchBiometricDialogCancel(context);
                }
                if (state.biometricType == BiometricAuthType.face || state.biometricType == BiometricAuthType.faceId) {
                  trackFaceBiometricDialogCancel(context);
                }
              }
              if (state.state == LoadingState.isCompleted) {
                state.failureOrSuccess.fold(
                  () {},
                  (a) => {
                    a.fold((l) {
                      //if some error
                      context.get<IFirebasePerformanceMonitoring>().stopTrace(TraceType.login);
                      l.maybeMap(
                        orElse: () => showAuthErrorUnexpectedErrorDialog(context),
                        reachedMaximumAccountAllowed: (_) => showMaximumAccountAllowedDialog(context),
                        unauthorized: (response) => context.read<AuthErrorHandlerCubit>().setErrorResponse(
                              AuthErrorResponseWrapper(
                                authErrorResponse: response.authErrorResponse,
                                prefilledInfo: state.prefilledInfo,
                              ),
                            ),
                        verificationRequired: (response) => context.read<AuthErrorHandlerCubit>().setErrorResponse(
                              AuthErrorResponseWrapper(
                                authErrorResponse: response.authErrorResponse,
                                prefilledInfo: state.prefilledInfo,
                              ),
                            ),
                        userAccessForbiden: (_) => onForbiddenAccess(context),
                        insiderUserUsedForPublicAccount: (_) => showInsiderUserUsedForPublicAccountDialog(context),
                      );
                    }, (dynamic r) {
                      onSuccess(r as T, context);
                    }),
                  },
                );
              }
            },
          ),
        ],
        child: BlocBuilder<PinBloc<T>, PinState>(
          builder: (context, state) => KoyalWillPopScope(
            onWillPop: () async {
              trackPinScreenOnGoBackClick(context);
              return true;
            },
            child: KoyalScaffold(
              key: const Key('__pinScreen__'),
              appBar: KoyalAppBar(title: getTitleText(context)),
              body: Column(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        getHeadingIcon(context),
                        KoyalPadding.normalAll(
                          top: false,
                          left: false,
                          right: false,
                          child: MainHeading(
                            title: getHeadingText(context),
                          ),
                        ),
                        PinInput(
                          currentLength: state.password.length,
                          maxLength: state.requiredPasswordLength,
                        ),
                        AuthErrorNoteBuilder(),
                        if (context.isFlagEnabledWatch(FeatureFlag.passwordRecoveryV2))
                          TertiaryButton(
                            key: const Key('__forgotPasswordButton__'),
                            text: L10nCappAuth.of(context).forgotYourPin,
                            padding: EdgeInsets.zero,
                            onPressed: state.state == LoadingState.isLoading
                                ? null
                                : () {
                                    trackChangePhoneNumberEnterPinOnForgotPinClick(context);
                                    context.navigator.push(
                                      path: NavigatorPath.cappAuth.pinPassRecoveryEntryScreen,
                                      arguments: PinPassRecoveryPhoneScreenArguments(
                                        prefilledInfo: state.prefilledInfo,
                                      ),
                                    );
                                  },
                          ),
                        if (state.state == LoadingState.isLoading) const KoyalProgressIndicator.small(),
                      ],
                    ),
                  ),
                  SecureNumericKeyboard(
                    onNumberPress: (number) {
                      context.read<PinBloc<T>>().add(PinEvent.addDigit(number));
                    },
                    onBackspacePress: () => context.read<PinBloc<T>>().add(const PinEvent.backspace()),
                    onBiometricPress: () {
                      context.read<PinBloc<T>>().add(const PinEvent.biometricPressed());
                    },
                    hasFaceId: state.password.isEmpty &&
                        state.biometricEnabled &&
                        state.supportBiometric &&
                        biometricFeatureFlag(context) &&
                        (state.biometricType == BiometricAuthType.face ||
                            state.biometricType == BiometricAuthType.faceId),
                    hasFingerprint: state.password.isEmpty &&
                        state.biometricEnabled &&
                        state.supportBiometric &&
                        biometricFeatureFlag(context) &&
                        (state.biometricType == BiometricAuthType.fingerprint ||
                            state.biometricType == BiometricAuthType.touchId),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
