import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../domain/model/prefilled_info.dart';

class PinScreenArguments extends ScreenArguments {
  final String loginSessionId;
  final int? passwordLength;
  final PrefilledInfo? prefilledInfo;
  final String shortcutSessionId;
  final ChangePasswordShortcut? changePasswordShortcut;
  final ChangePasswordOptions? changePasswordOptions;
  final String? publicKey;
  final String? challenge;

  PinScreenArguments({
    required this.loginSessionId,
    required this.passwordLength,
    this.prefilledInfo,
    this.shortcutSessionId = '',
    this.changePasswordShortcut,
    this.changePasswordOptions,
    this.challenge,
    this.publicKey,
  });
}
