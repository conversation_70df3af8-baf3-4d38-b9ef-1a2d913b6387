import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_auth_core.dart';

Future showBosAuthenticationLimitReachedDialog(
  BuildContext context,
  String subtitle, {
  GaTrackingCategory? trackingType,
}) async {
  switch (trackingType) {
    case GaTrackingCategory.password:
      context.get<CappAuthTrackingService>().trackAttemptsReachedCooloffChangePwdDialogView();
      break;
    default:
      context.get<CappAuthTrackingService>().trackAttemptsReachedIdentityVerificationDialogView();
      break;
  }

  return showKoyalOverlay<void>(
    context,
    title: L10nCappAuth.of(context).secondIdentiferTooManyAttemptsDialogTitle,
    body: KoyalText.body2(
      subtitle,
      color: ColorTheme.of(context).foreground60Color,
      textAlign: TextAlign.center,
    ),
    primaryButtonBuilder: (context) => PrimaryButton(
      text: L10nCappAuth.of(context).okGotIt,
      onPressed: () => context.navigator.pop(),
    ),
  );
}

enum GaTrackingCategory {
  phoneNumber,
  email,
  password,
}
