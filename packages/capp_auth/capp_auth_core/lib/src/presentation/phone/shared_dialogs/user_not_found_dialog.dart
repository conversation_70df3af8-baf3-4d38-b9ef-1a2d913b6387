import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

import '../../../../capp_auth_core.dart';

Future showUserNotFoundDialog(BuildContext context) async {
  return showKoyalOverlay<void>(
    context,
    body: KoyalText.body1(
      L10nCappAuth.of(context).userNotFoundDialogTitle,
      textAlign: TextAlign.center,
      color: ColorTheme.of(context).defaultTextColor,
    ),
    primaryButtonBuilder: (context) => PrimaryButton(
      text: L10nCappAuth.of(context).iWantToRegister,
      onPressed: () => context.authNavigator.toAuthLandingReplace(context, args: AuthLandingScreenArguments()),
    ),
  );
}
