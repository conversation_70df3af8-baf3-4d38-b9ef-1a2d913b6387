import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../l10n/i18n.dart';

Future showInsiderUserUsedForPublicAccountDialog(BuildContext context) async {
  return showKoyalOverlay<void>(
    context,
    key: const Key('__showInsiderUserUsedForPublicAccountDialog_'),
    body: KoyalText.body1(
      L10nCappAuth.of(context).insiderForPublicUser,
      textAlign: TextAlign.center,
      color: ColorTheme.of(context).defaultTextColor,
    ),
    primaryButtonBuilder: (context) => PrimaryButton(
      key: const Key('__showInsiderUserUsedForPublicAccountDialogButton_'),
      text: L10nCappAuth.of(context).goBack,
      onPressed: () => context.navigator.pop(),
    ),
  );
}
