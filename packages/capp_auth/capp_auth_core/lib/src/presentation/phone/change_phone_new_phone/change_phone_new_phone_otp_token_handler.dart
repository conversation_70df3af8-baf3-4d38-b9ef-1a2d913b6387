import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class ChangePhoneNewPhoneOtpTokenHandler implements IOtpTokenHandler {
  final String phoneNumber;
  final String sessionId;
  final bool includeSuccessScreen;
  bool replaceCurrentScreen;

  final IChangePhoneRepository changePhoneRepository;
  final IFeatureFlagRepository featureFlagRepository;

  ChangePhoneNewPhoneOtpTokenHandler({
    required this.phoneNumber,
    required this.sessionId,
    required this.changePhoneRepository,
    required this.featureFlagRepository,
    required this.replaceCurrentScreen,
    required this.includeSuccessScreen,
  });

  @override
  Future<void> onTokenRecieved(String token, BuildContext currentContext) async {
    // call verification method
    final otpVerificationResponse = await changePhoneRepository.verifyOtp(
      sessionId: sessionId,
      otp: token,
      requiresExtraStepToComplete: featureFlagRepository.isEnabledCached(FeatureFlag.changePhoneSelfieStep),
    );

    otpVerificationResponse.fold((l) {
      // handle verification failure
      if (replaceCurrentScreen) {
        currentContext.navigator.pop(otpVerificationResponse);
      } else {
        handleOtpVerificationFailure(l, currentContext);
      }
    }, (response) {
      // handle verification success
      navigateNext(
        currentContext,
        sessionId: sessionId,
        phoneNumber: phoneNumber,
        replaceCurrentScreen: replaceCurrentScreen,
        response: response,
      );
    });
  }

  void handleOtpVerificationFailure(UserVerificationFailure l, BuildContext currentContext) {
    l.maybeWhen(
      functionMap: {
        UserVerificationFailureType.insiderUserUsedForPublicAccount: (_) =>
            showInsiderUserUsedForPublicAccountDialog(currentContext),
      },
      orElse: () => showOtpNotAcceptedDialog(currentContext),
    );
  }

  void navigateNext(
    BuildContext context, {
    required String phoneNumber,
    required String sessionId,
    required bool replaceCurrentScreen,
    required ChangePhoneNumberOtpVerificationResponse? response,
  }) {
    if (replaceCurrentScreen) {
      if (includeSuccessScreen) {
        context.navigator.pushReplacement(
          path: NavigatorPath.cappAuth.changePhoneSuccessScreen,
          arguments: ChangePhoneSuccessScreenArguments(
            newPhoneNumber: phoneNumber,
          ),
        );
      } else {
        context.navigator.pop(right<UserVerificationFailure, ChangePhoneNumberOtpVerificationResponse?>(response));
        context.navigator.pop(phoneNumber);
        context.navigator.pop(phoneNumber);
      }
    } else {
      return includeSuccessScreen
          ? context.navigator.push(
              path: NavigatorPath.cappAuth.changePhoneSuccessScreen,
              arguments: ChangePhoneSuccessScreenArguments(
                newPhoneNumber: phoneNumber,
              ),
            )
          : context.navigator.pop(phoneNumber);
    }
  }
}
