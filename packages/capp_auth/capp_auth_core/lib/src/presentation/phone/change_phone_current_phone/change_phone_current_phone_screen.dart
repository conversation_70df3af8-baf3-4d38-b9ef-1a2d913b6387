// ignore_for_file: deprecated_member_use

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class ChangePhoneEntryScreen extends ChangePhoneCurrentPhoneScreen {
  ChangePhoneEntryScreen({Key? key, required ChangePhoneEntryScreenArguments arguments})
      : super(key: key, arguments: arguments);
}

class ChangePhoneEntryScreenArguments extends ScreenArguments {
  final String sessionId;
  final PrefilledInfo? prefilledInfo;

  ChangePhoneEntryScreenArguments({
    required this.sessionId,
    this.prefilledInfo,
  });
}

class ChangePhoneCurrentPhoneScreen extends StatelessScreen with RouteWrapper {
  final ChangePhoneEntryScreenArguments args;
  ChangePhoneCurrentPhoneScreen({
    Key? key,
    required ChangePhoneEntryScreenArguments arguments,
  })  : args = arguments,
        super(key: key, arguments: arguments);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (context) => context.get<ChangePhoneLoginPhoneBloc>()
          ..add(
            PhoneWithVerificationEvent.initialize(
              sessionId: (arguments! as ChangePhoneEntryScreenArguments).sessionId,
              phoneNumber: (arguments! as ChangePhoneEntryScreenArguments).prefilledInfo?.phoneNumber,
            ),
          ),
        child: this,
      );

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ChangePhoneLoginPhoneBloc, ChangePhoneLoginPhoneState>(
      listenWhen: (previous, current) => previous.state != current.state,
      listener: (context, state) {
        if (state.state == LoadingState.isCompleted) {
          state.phonePostResponse.fold(() {}, (a) {
            a.fold((l) {
              _errorDialog(context, l.type, tooManyRequestsBlockTime: l.header);
            }, (r) {
              if (r.usernameVerificationSessionId != null) {
                context.navigateToAuthEntryPoint();
              } else {
                // in case it is not prospect in India, continue with entering 2nd id
                context.navigator.push(
                  path: NavigatorPath.cappAuth.changePhone2ndIdScreen,
                  arguments: SecondIdVerificationScreenArguments(
                    allowedSecondIds: r.available2ndIds,
                    sessionId: state.sessionId!,
                  ),
                );
              }
            });
          });
        }
      },
      builder: (context, state) => KoyalWillPopScope(
        onWillPop: () async {
          context.get<CappAuthTrackingService>().trackChangePhoneNumberEnterCurrentNumberOnGoBackClick();
          return true;
        },
        child: KoyalScaffold(
          key: const Key('__changePhoneEntryScreen__'),
          appBar: KoyalAppBar(
            title: L10nCappAuth.of(context).changePhoneCurrentPhoneTitle,
          ),
          body: FixedBottomContentScreen(
            upperContent: [
              KoyalPadding.small(
                left: false,
                right: false,
                top: false,
                child: MainHeading(
                  title: L10nCappAuth.of(context).changePhoneCurrentPhoneHeadingTitle,
                  subtitle: L10nCappAuth.of(context).changePhoneCurrentPhoneHeadingSubtitle,
                  centerAlign: false,
                ),
              ),
              KoyalPadding.normalHorizontal(
                child: CappPhoneTextField(
                  key: const Key('__phoneNumberField__'),
                  country: context.get<PhoneNumberConfig>().country,
                  value: state.phoneNumber.value,
                  onChanged: (value) {
                    context.read<ChangePhoneLoginPhoneBloc>().add(PhoneWithVerificationEvent.setValue(value: value));
                  },
                  phonePrefix: context.get<PhoneNumberConfig>().phonePrefix,
                  hintText: L10nCappAuth.of(context).primaryPhoneNumber,
                  phoneNumberFormat: context.get<PhoneNumberConfig>().useInternationalFormat
                      ? PhoneNumberFormat.international
                      : PhoneNumberFormat.national,
                  toolbarOptions: const ToolbarOptions(
                    paste: true,
                    selectAll: true,
                  ),
                ),
              ),
            ],
            fixedBottomContent: [
              HorizontalButtonsLayout(
                primaryButton: PrimaryButton(
                  key: const Key('__changePhoneEntryContinueButton__'),
                  text: L10nCappAuth.of(context).continueString,
                  isInProgress: state.state == LoadingState.isLoading,
                  onPressed: state.phoneNumber.isValid(context.get<PhoneNumberConfig>().phoneRegex) ||
                          state.externalLoginResponse != null
                      ? () {
                          context
                              .get<CappAuthTrackingService>()
                              .trackChangePhoneNumberEnterCurrentNumberOnContinueClick();
                          context.read<ChangePhoneLoginPhoneBloc>().add(const PhoneWithVerificationEvent.postValue());
                        }
                      : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _errorDialog(
    BuildContext context,
    UserVerificationFailureType failureType, {
    int? tooManyRequestsBlockTime,
  }) {
    if (failureType == UserVerificationFailureType.sessionNotFoundOrExpired) {
      showAuthErrorSessionNotFound(context);
    } else if (failureType == UserVerificationFailureType.sessionStartsRateExceeded) {
      showAuthErrorTemporaryBlockDialogWithTime(
        context,
        blockedUntil: tooManyRequestsBlockTime,
      );
    } else {
      showAuthErrorUnexpectedErrorDialog(context);
    }
  }
}
