import 'package:koyal_auth/koyal_auth.dart';

import '../../../../capp_auth_core.dart';

class IdentifyResultApprovedScreenArgs extends IdentifyResultScreeArgs {
  final String? loginSessionId;
  final int? passwordLength;
  final String? publicKey;
  final String? challenge;
  final String? authenticationSessionId;

  IdentifyResultApprovedScreenArgs({
    required super.identifyFlowType,
    super.identifyBosResult = IdentifyBosResult.approved,
    this.loginSessionId,
    this.passwordLength,
    this.publicKey,
    this.challenge,
    this.authenticationSessionId,
  });
}
