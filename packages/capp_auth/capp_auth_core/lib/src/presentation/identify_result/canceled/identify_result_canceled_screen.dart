import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_auth_core.dart';

class IdentifyResultCanceledScreen extends StatelessScreen {
  final IdentifyResultCanceledScreenArgs args;

  const IdentifyResultCanceledScreen({
    super.key,
    required IdentifyResultCanceledScreenArgs arguments,
  })  : args = arguments,
        super(arguments: arguments);

  @override
  Widget build(BuildContext context) {
    return InfoScreen(
      key: key,
      canPop: false,
      title: getTitle(context),
      subTitle: getSubtitle(context),
      image: getImage(),
      primaryButtonKey: const Key('__TryAgainButton__'),
      primaryButtonText: L10nCappAuth.of(context).identifyResultCanceledTryAgain,
      primaryButtonOnClick: () {
        trackOnTryAgainButtonClicked(context);
        onTryAgainButtonClicked(context);
      },
      tertiaryButtonKey: const Key('__DoItLaterButton__'),
      tertiaryButtonText: L10nCappAuth.of(context).identifyResultCanceledLater,
      tertiaryButtonOnClick: () {
        trackOnDoItLaterButtonClicked(context);
        onDoItLaterButtonClicked(context);
      },
      alignContentCenter: true,
      showButtonsWithoutFooter: true,
    );
  }

  String getTitle(BuildContext context) => L10nCappAuth.of(context).identifyResultCanceledTitle;

  String getSubtitle(BuildContext context) => L10nCappAuth.of(context).identifyResultCanceledSelfieIdCardSubtitle;

  Widget getImage() => const AssetSvgImage(
        'assets/svg/bos_identify_canceled.svg',
        package: 'capp_auth_core',
      );

  void onTryAgainButtonClicked(BuildContext context) {}

  void trackOnTryAgainButtonClicked(BuildContext context) {}

  void onDoItLaterButtonClicked(BuildContext context) => context.navigator.toMainScreen();

  void trackOnDoItLaterButtonClicked(BuildContext context) {}
}
