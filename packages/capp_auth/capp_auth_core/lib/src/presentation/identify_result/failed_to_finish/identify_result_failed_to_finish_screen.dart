import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_auth_core.dart';

class IdentifyResultFailedToFinishScreen extends StatelessScreen {
  final IdentifyResultFailedToFinishScreenArgs args;

  const IdentifyResultFailedToFinishScreen({
    super.key,
    required IdentifyResultFailedToFinishScreenArgs arguments,
  })  : args = arguments,
        super(arguments: arguments);

  @override
  Widget build(BuildContext context) {
    return InfoScreen(
      key: key,
      canPop: false,
      title: getTitle(context),
      subTitle: getSubtitle(context),
      image: getImage(),
      primaryButtonKey: const Key('__IdentifyErrorContinueButton__'),
      primaryButtonText: getPrimaryButtonText(context),
      primaryButtonOnClick: () {
        trackOnPrimaryButtonClicked(context);
        onPrimaryButtonClicked(context);
      },
      tertiaryButtonKey: const Key('__DoItLaterButton__'),
      tertiaryButtonText: getTertiaryButtonText(context),
      tertiaryButtonOnClick: () {
        trackOnTertiaryButtonClicked(context);
        onTertiaryButtonClicked(context);
      },
      alignContentCenter: true,
      showButtonsWithoutFooter: true,
    );
  }

  String? getPrimaryButtonText(BuildContext context) => L10nCappAuth.of(context).identifyResultFailedToFinishContinue;

  String getTitle(BuildContext context) {
    switch (args.errorState) {
      case UserVerificationFailureType.insiderUserUsedForPublicAccount:
      case UserVerificationFailureType.invalid:
      case UserVerificationFailureType.invalidOtp:
      case UserVerificationFailureType.permanentlyBlocked:
      case UserVerificationFailureType.unexpected:
        return L10nCappAuth.of(context).secondIdentifierIdentifyErrorTitle;
      case UserVerificationFailureType.sessionNotFoundOrExpired:
        return L10nCappAuth.of(context).sessionNotFoundDialogTitle;
      case UserVerificationFailureType.sessionStartsRateExceeded:
        return L10nCappAuth.of(context).temporaryBlockDialogTimeTitle;
      default:
        return L10nCappAuth.of(context).secondIdentifierIdentifyErrorTitle;
    }
  }

  String? getSubtitle(BuildContext context) {
    switch (args.errorState) {
      case UserVerificationFailureType.insiderUserUsedForPublicAccount:
      case UserVerificationFailureType.invalid:
      case UserVerificationFailureType.invalidOtp:
      case UserVerificationFailureType.permanentlyBlocked:
      case UserVerificationFailureType.unexpected:
        return L10nCappAuth.of(context).secondIdentifierIdentifyErrorSubtitle;
      case UserVerificationFailureType.sessionNotFoundOrExpired:
        return L10nCappAuth.of(context).sessionNotFoundDialogBody;
      case UserVerificationFailureType.sessionStartsRateExceeded:
        return L10nCappAuth.of(context).temporaryBlockDialogTimeBody(
          args.tooManyRequestsBlockTime != null
              ? DateFormat('dd/MM/yyyy HH:mm:ss')
                  .format(DateTime.now().add(Duration(seconds: args.tooManyRequestsBlockTime!)))
              : DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().add(const Duration(minutes: 5))),
        );
      default:
        return L10nCappAuth.of(context).secondIdentifierIdentifyErrorSubtitle;
    }
  }

  void onPrimaryButtonClicked(BuildContext context) => context.navigator.toMainScreen();

  void trackOnPrimaryButtonClicked(BuildContext context) {}

  String? getTertiaryButtonText(BuildContext context) => null;

  void onTertiaryButtonClicked(BuildContext context) {}

  void trackOnTertiaryButtonClicked(BuildContext context) {}

  Widget getImage() => const AssetSvgImage(
        'assets/svg/identify_error.svg',
        package: 'capp_auth_core',
      );
}
