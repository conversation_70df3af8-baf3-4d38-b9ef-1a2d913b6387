import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class LockScaffold extends StatelessWidget {
  final Widget body;
  final String? title;
  final bool confirmUserActionMode;

  const LockScaffold({
    Key? key,
    required this.body,
    this.title,
    this.confirmUserActionMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return KoyalWillPopScope(
      onWillPop: () async => confirmUserActionMode,
      child: KoyalScaffold(
        key: const Key('__lockPageV2__'),
        appBar: confirmUserActionMode
            ? KoyalAppBar(
                title: title,
              )
            : KoyalAppBar(
                leading: KoyalAppBarLeading.none,
                actions: [
                  TertiaryButton(
                    text: L10nCappAuth.of(context).logout,
                    onPressed: () {
                      context.get<CappAuthTrackingService>().trackUnlockScreenOnSwitchUserClick();
                      context.navigator.push(
                        path: NavigatorPath.cappAuth.logoutScreen,
                        arguments: LogoutScreenArguments(),
                      );
                    },
                  ),
                ],
              ),
        body: body,
      ),
    );
  }
}
