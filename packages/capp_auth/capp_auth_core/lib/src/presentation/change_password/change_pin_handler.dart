// ignore_for_file: use_build_context_synchronously

import 'package:capp_face_guard/capp_face_guard.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:capp_innovatrics_core/capp_innovatrics_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigator/navigator.dart';

import '../../../capp_auth_core.dart';
import 'change_pin_session_start_error_dialog.dart';

class ChangePinHandler {
  static Future<void> navigateToChangePin({
    required BuildContext context,
    required ChangePasswordFlow flow,
    String? sessionId,
    bool cleartextPassword = false,
    bool requireFaceGuardChangePassword = false,
  }) async {
    if (context.isFlagEnabledRead(FeatureFlag.changePasswordOtp) && !cleartextPassword) {
      final changePasswordRepository = context.get<IChangePasswordSettingsRepository>();

      if (sessionId == null) {
        (await changePasswordRepository.postChangePasswordSessionStart()).fold(
          (l) {
            showChangePinStartOtpErrorDialog(context: context, flow: flow);
            return;
          },
          (r) => sessionId = r.sessionId,
        );
      }

      final userRepository = context.get<IUserRepository>();
      final user = await context.get<ICurrentUserRepository>().getCurrentUser();
      final cuid = await userRepository.userCuid();

      final handler = ChangePasswordOtpTokenHandler(
        sessionId: sessionId!,
        faceGuard: cuid != null,
        changePasswordRepository: changePasswordRepository,
        flow: flow,
      );

      await context.navigator
          .push<Either<UserVerificationFailure, Unit>>(
        path: NavigatorPath.koyalOtp.otpScreen,
        arguments: OtpScreenArguments(
          requestType: OtpRequestType.changePasswordCapp,
          secret: sessionId,
          entityToVerify: user?.phoneNumber ?? '',
          otpTokenHandler: handler,
        ),
      )
          .then((response) {
        response?.fold(
          (l) => handler.handleFailure(context),
          (r) => null,
        );
      });
    } else if (cleartextPassword) {
      final changePasswordRepository = context.get<IChangePasswordSettingsRepository>();

      (await changePasswordRepository.postChangePasswordV6SessionStart()).fold((l) {
        showAuthErrorUnexpectedErrorDialog(context);
        return;
      }, (r) {
        context.navigateToChangePassword(
          sessionId: r.sessionId,
          publicKey: r.publicKey,
          challenge: r.challenge,
        );
      });
    } else {
      await context.navigateToChangePassword();
    }
  }

  static Future<void> handleChangePinFaceguard({
    required BuildContext context,
    required ChangePasswordFlow changePasswordFlow,
    required String sessionId,
    bool replaceRouteWithSelfie = false,
  }) async {
    await context.navigator.navigateToFaceGuard(
      flowStage: InnovatricsFlowStage.changePassword,
      faceAuthenticationContext: FaceAuthenticationContext.changePassword,
      context: context,
      replaceRoute: replaceRouteWithSelfie,
      onResult: (response) {
        response.map(
          withResult: (result) {
            if (result.faceAuthResult == FaceAuthResult.approved) {
              context.navigateToChangePassword(
                replaceScreen: true,
                faceGuardToken: result.faceGuardToken,
                sessionId: sessionId,
              );
            } else if (result.faceAuthResult == FaceAuthResult.rejected ||
                result.faceAuthResult == FaceAuthResult.canceled) {
              context.navigator.pushReplacement(
                path: NavigatorPath.cappAuth.changePasswordUnableToVerifyScreen,
                arguments: ChangePasswordUnableToVerifyScreenArguments(
                  changePasswordFlow: changePasswordFlow,
                  sessionId: sessionId,
                ),
              );
            } else {
              context.navigator.pushReplacement(
                path: NavigatorPath.cappAuth.changePasswordErrorScreen,
                arguments: ChangePasswordErrorScreenArguments(
                  flow: changePasswordFlow,
                  sessionId: sessionId,
                ),
              );
            }
          },
          error: (error) {
            if (error.failure == FaceGuardFailure.sessionNotFoundOrExpired) {
              context.navigator.pushReplacement(
                path: NavigatorPath.cappAuth.changePasswordLongInactivityScreen,
                arguments: ChangePasswordErrorScreenArguments(
                  flow: changePasswordFlow,
                  sessionId: sessionId,
                ),
              );
            } else {
              context.navigator.pushReplacement(
                path: NavigatorPath.cappAuth.changePasswordErrorScreen,
                arguments: ChangePasswordErrorScreenArguments(
                  flow: changePasswordFlow,
                  sessionId: sessionId,
                ),
              );
            }
          },
        );
      },
    );
  }
}
