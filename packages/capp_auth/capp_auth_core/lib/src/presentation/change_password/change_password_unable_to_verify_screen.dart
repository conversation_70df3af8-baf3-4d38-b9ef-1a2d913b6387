import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../capp_auth_core.dart';

class ChangePasswordUnableToVerifyScreenArguments extends ScreenArguments {
  final ChangePasswordFlow changePasswordFlow;
  final String sessionId;

  ChangePasswordUnableToVerifyScreenArguments({
    required this.changePasswordFlow,
    required this.sessionId,
  });
}

class ChangePasswordUnableToVerifyScreen extends StatelessScreen {
  final ChangePasswordUnableToVerifyScreenArguments args;

  const ChangePasswordUnableToVerifyScreen({
    super.key,
    required ChangePasswordUnableToVerifyScreenArguments arguments,
  }) : args = arguments;

  @override
  Widget build(BuildContext context) {
    return InfoScreen(
      title: L10nCappAuth.of(context).bosChangePinTitle,
      subTitle: L10nCappAuth.of(context).bosChangePinSubtitle,
      image: const AssetSvgImage(
        'assets/svg/bos_identify_canceled.svg',
        package: 'capp_auth_core',
      ),
      primaryButtonKey: const Key('__TryAgainButton__'),
      primaryButtonText: L10nCappAuth.of(context).identifyResultCanceledTryAgain,
      primaryButtonOnClick: () => ChangePinHandler.handleChangePinFaceguard(
        context: context,
        changePasswordFlow: args.changePasswordFlow,
        sessionId: args.sessionId,
      ),
      tertiaryButtonKey: const Key('__LaterButton__'),
      tertiaryButtonText: L10nCappAuth.of(context).identifyResultCanceledLater,
      tertiaryButtonOnClick: () => switch (args.changePasswordFlow) {
        ChangePasswordFlow.settings => context.navigator.popUntilFromPackage('CappHome', 'SettingsMenuScreen'),
        ChangePasswordFlow.instructionScreen =>
          context.navigator.pushReplacement(path: NavigatorPath.cappAuth.lockScreen),
        ChangePasswordFlow.pendingAction =>
          context.navigator.popUntilFromPackage('CappHome', 'SettingsMenuScreen'), // change
      },
      alignContentCenter: true,
      showButtonsWithoutFooter: true,
      canPop: false,
    );
  }
}
