import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

class SecondIdentifiersRow<T> extends StatelessWidget {
  final T groupValue;
  final List<T> values;
  final List<String> labels;
  final List<Function(BuildContext, T)> onChoice;
  final String? identifier;

  const SecondIdentifiersRow({
    Key? key,
    required this.groupValue,
    required this.values,
    required this.labels,
    required this.onChoice,
    this.identifier,
  })  : assert(values.length == labels.length && labels.length == onChoice.length, 'Length do not match'),
        super(key: key);

  @override
  Widget build(BuildContext context) => KoyalPadding.normalAll(
        child: Column(
          children: mapValuesToSecondIdentifierItems()
              .map(
                (e) => Row(
                  children: [
                    Expanded(
                      child: KoyalRadio<T>(
                        key: Key('__RadioRow${identifier}LeftChoice__'),
                        title: e.leftLabel,
                        onChanged: (_) => e.onLeftChoice(context, e.leftValue),
                        value: e.leftValue,
                        groupValue: groupValue,
                      ),
                    ),
                    Expanded(
                      child: e.rightValue != null && e.onRightChoice != null && e.onRightChoice != null
                          ? KoyalRadio<T>(
                              key: Key('__RadioRow${identifier}RightChoice__'),
                              title: e.rightLabel,
                              onChanged: (_) => e.onRightChoice!(context, e.rightValue as T),
                              value: e.rightValue as T,
                              groupValue: groupValue,
                            )
                          : const SizedBox.shrink(),
                    ),
                  ],
                ),
              )
              .toList(),
        ),
      );

  List<SecondIdentifierItem<T>> mapValuesToSecondIdentifierItems() {
    final items = <SecondIdentifierItem<T>>[];
    for (var i = 0; i < values.length; i = i + 2) {
      final leftIndex = i;
      final rigtIndex = i + 1;

      items.add(
        SecondIdentifierItem(
          leftValue: values[leftIndex],
          leftLabel: labels[leftIndex],
          onLeftChoice: onChoice[leftIndex],
          rightValue: rigtIndex < values.length ? values[rigtIndex] : null,
          rightLabel: rigtIndex < values.length ? labels[rigtIndex] : null,
          onRightChoice: rigtIndex < values.length ? onChoice[rigtIndex] : null,
        ),
      );
    }
    return items;
  }
}

class SecondIdentifierItem<T> {
  final T leftValue;
  final String leftLabel;
  final Function(BuildContext, T) onLeftChoice;
  final T? rightValue;
  final String? rightLabel;
  final Function(BuildContext, T)? onRightChoice;
  final String? identifier;

  const SecondIdentifierItem({
    required this.leftValue,
    required this.leftLabel,
    required this.onLeftChoice,
    this.rightValue,
    this.rightLabel,
    this.onRightChoice,
    this.identifier,
  });
}
