import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_auth_core.dart';

class ManualDeduplicationScreen extends StatelessScreen {
  final ManualDeduplicationScreenArguments args;

  const ManualDeduplicationScreen({
    Key? key,
    required ManualDeduplicationScreenArguments arguments,
  })  : args = arguments,
        super(key: key, arguments: arguments);

  @override
  Widget build(BuildContext context) {
    return KoyalWillPopScope(
      onWillPop: () async => args.canNavigateBack,
      child: Koyal<PERSON>caffold(
        key: const Key('__IdentifyManualDeduplicationScreeen__'),
        appBar: KoyalAppBar(
          leading: args.canNavigateBack ? KoyalAppBarLeading.goBack : KoyalAppBarLeading.none,
        ),
        body: InfoScreen(
          title: getTitle(context, args.identifyFlowType),
          subTitle: getSubTitle(context, args.identifyFlowType),
          image: getIcon(args.identifyFlowType),
          primaryButtonKey: const Key('__IdentifyManualDeduplicationContinueButton__'),
          primaryButtonText: getButtonText(context, args.identifyFlowType),
          primaryButtonOnClick: () {
            context.get<CappAuthTrackingService>().trackGoToHomepageClick();
            if (args.onContinueTap != null) {
              args.onContinueTap!();
              return;
            } else {
              context.externalNavigator.onAuthComplete(context);
              return;
            }
          },
          alignContentCenter: true,
          showButtonsWithoutFooter: true,
        ),
      ),
    );
  }

  String getTitle(BuildContext context, IdentifyFlowType? identifyFlowType) =>
      L10nCappAuth.of(context).secondIdentifierIdentifyManualDeduplicationTitle;

  String getSubTitle(BuildContext context, IdentifyFlowType? identifyFlowType) =>
      L10nCappAuth.of(context).secondIdentifierIdentifyManualDeduplicationSubtitle;

  String getButtonText(BuildContext context, IdentifyFlowType? identifyFlowType) =>
      L10nCappAuth.of(context).secondIdentifierIdentifyManualDeduplicationButton;

  Widget getIcon(IdentifyFlowType? identifyFlowType) => SvgPicture.asset(
        'assets/svg/coffee.svg',
        package: 'capp_auth_core',
        width: 360,
        height: 200,
      );
}
