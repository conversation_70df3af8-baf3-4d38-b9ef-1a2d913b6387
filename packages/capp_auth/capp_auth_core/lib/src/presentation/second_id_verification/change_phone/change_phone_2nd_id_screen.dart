import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class ChangePhone2ndIdScreen extends SecondIdVerificationScreenBase<int?> {
  @override
  void trackOnGoBackClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberSecondIdentifierOnGoBackClick();
  }

  @override
  void onSuccess(int? r, UserSecondIdType? userSecondIdType, BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangePhoneNumberSecondIdentifierOnConfirmClick();
    context.navigator.push(
      path: NavigatorPath.cappAuth.changePhonePinScreen,
      arguments: ChangePhonePinScreenArguments(
        loginSessionId: args.sessionId,
        passwordLength: r,
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<SecondIdVerificationBloc>(
        create: (context) => context.get<ChangePhone2ndIdBloc>()
          ..add(
            SecondIdVerificationEvent.init(
              args.allowedSecondIds,
              args.sessionId,
            ),
          ),
        child: this,
      );

  ChangePhone2ndIdScreen({
    Key? key,
    required SecondIdVerificationScreenArguments arguments,
    DateTime? initRegistrationDateBirth,
  }) : super(
          key: key,
          arguments: arguments,
          allowRegistration: false,
          initRegistrationDateBirth: initRegistrationDateBirth,
        );
}
