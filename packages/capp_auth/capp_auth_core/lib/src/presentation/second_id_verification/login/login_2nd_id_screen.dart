import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

class Login2ndIdScreen extends SecondIdVerificationScreenBase<SecondIdentifierVerificationResponse> {
  @override
  void onSuccess(SecondIdentifierVerificationResponse r, UserSecondIdType? userSecondIdType, BuildContext context) {
    context.navigator.push(
      path: NavigatorPath.cappAuth.loginPinScreen,
      arguments: PinScreenArguments(
        loginSessionId: r.loginSessionId,
        passwordLength: r.passwordLength,
        prefilledInfo: args.prefilledInfo,
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<SecondIdVerificationBloc>(
        create: (context) => context.get<Login2ndIdBloc>()
          ..add(
            SecondIdVerificationEvent.init(
              args.allowedSecondIds,
              args.sessionId,
            ),
          ),
        child: this,
      );

  @override
  void trackOnGoBackClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackLogin2ndIdBackButtonClick();
  }

  @override
  void trackValueEnter(BuildContext context, UserSecondIdType secondId) {
    context.get<CappAuthTrackingService>().trackLogin2ndIdSecondIdEnter(secondId);
  }

  @override
  void trackConfirmClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackLogin2ndIdConfirmClick();
  }

  @override
  void trackNotACustomerClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackLogin2ndIdIAmNotHciCustomerClick();
  }

  @override
  void trackIncorrectCredentialsDialogShow(BuildContext context, UserSecondIdType secondId) {
    context.get<CappAuthTrackingService>().trackLogin2ndIdOverlay2ndIdInvalidCredentialsView(secondId);
  }

  @override
  void trackDialogOkGotIt(BuildContext context, UserSecondIdType secondId) {
    context.get<CappAuthTrackingService>().trackLogin2ndIdOverlay2ndIdOkGotItClick(secondId);
  }

  Login2ndIdScreen({
    Key? key,
    required SecondIdVerificationScreenArguments arguments,
    DateTime? initRegistrationDateBirth,
  }) : super(
          key: key,
          arguments: arguments,
          initRegistrationDateBirth: initRegistrationDateBirth,
        );
}
