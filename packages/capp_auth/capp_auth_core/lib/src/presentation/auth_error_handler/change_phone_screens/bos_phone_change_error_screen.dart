import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_auth_core.dart';

class BosPhoneChangeErrorScreen extends IdentifyResultFailedToFinishScreen {
  const BosPhoneChangeErrorScreen({
    super.key,
    required super.arguments,
  });

  String get bussinesRouteName =>
      '${args.identifyFlowType.getBusinessRoute()}_${args.identifyBosResult.getBusinessRoute()}';

  @override
  String getTitle(BuildContext context) => L10nCappAuth.of(context).bosPhoneChangeGenericTitle;

  @override
  String getSubtitle(BuildContext context) => L10nCappAuth.of(context).bosPhoneChangeGenericSubtitle;

  @override
  Widget getImage() => const AssetSvgImage(
        'assets/svg/identify_error.svg',
        package: 'capp_auth_core',
      );

  @override
  void onPrimaryButtonClicked(BuildContext context) =>
      context.navigator.popUntilFromPackage('CappPersonal', 'PersonalDetailsScreenV2');

  @override
  void trackOnPrimaryButtonClicked(BuildContext context) {}
}
