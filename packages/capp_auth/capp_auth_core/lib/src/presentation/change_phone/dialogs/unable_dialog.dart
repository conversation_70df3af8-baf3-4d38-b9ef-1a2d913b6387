import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../l10n/i18n.dart';
import '../../external_pkg_navigator/external_pkg_navigator_extension.dart';

Future showUnableToChangePhoneDialog(BuildContext context) async {
  return showKoyalOverlay<String>(
    context,
    key: const Key('__unableToChangePhoneDialog__'),
    body: KoyalText.body1(
      L10nCappAuth.of(context).changePhoneUnableDialogText,
      color: ColorTheme.of(context).defaultTextColor,
      textAlign: TextAlign.center,
    ),
    // special case, continue as guest is not supported in SSO, the button must not be visible, but the dialog is shared
    tertiaryButtonBuilder: kIsWeb
        ? null
        : (c) => TertiaryButton(
              key: const Key('__unableToChangePhoneDialogContinueAsGuestButton__'),
              text: L10nCappAuth.of(c).continueAsGuestButtonText,
              onPressed: c.navigator.toMainScreen,
            ),
    primaryButtonBuilder: (context) => PrimaryButton(
      key: const Key('__unableToChangePhoneDialogContactUsButton__'),
      text: L10nCappAuth.of(context).contactHomeCredit,
      onPressed: () {
        context.externalNavigator.onContactUsFromDialog(context);
      },
    ),
  );
}
