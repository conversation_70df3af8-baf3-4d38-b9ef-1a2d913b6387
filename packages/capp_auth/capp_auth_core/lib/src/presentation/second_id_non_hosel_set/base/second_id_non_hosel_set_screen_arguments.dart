import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../domain/model/prefilled_info.dart';

class SecondIdNonHoselSetScreenArguments extends ScreenArguments {
  final String? usernameVerificationSessionId;
  final bool isShowAgreement;
  final bool allowGoBack;
  final PrefilledInfo? prefilledInfo;
  final List<UserSecondIdType>? available2ndIds;
  final String? signUpSessionId;

  SecondIdNonHoselSetScreenArguments({
    required this.isShowAgreement,
    required this.allowGoBack,
    this.prefilledInfo,
    this.usernameVerificationSessionId,
    this.available2ndIds,
    this.signUpSessionId,
  });
}
