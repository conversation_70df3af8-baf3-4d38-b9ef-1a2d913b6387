// ignore_for_file: use_build_context_synchronously

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared/koyal_shared.dart';

import '../../../../l10n/i18n.dart';

mixin EmailHintMixin {
  Future<String?> getEmailInteractive(BuildContext context) async {
    // drive tests cannot handle permissions
    if (context.isInDriveTest == false) {
      final emails = await context.get<UserEmailService>().getUserEmails();
      if (emails.isNotEmpty) {
        return _pickAccounts(context, emails);
      }
    }
    return null;
  }

  Future<String?> _pickAccounts(BuildContext context, List<String> data) {
    return showKoyalOverlay<String>(
      context,
      title: L10nCappAuth.of(context).emailPickerTitle,
      dismissible: false,
      body: ListView(
        key: const Key('__phoneNumberList__'),
        shrinkWrap: true,
        children: [
          ...data.isNotEmpty
              ? data.map(
                  (e) => ListTile(
                    contentPadding: const EdgeInsets.symmetric(vertical: 8),
                    leading: CircleAvatar(
                      backgroundColor: ColorTheme.of(context).foreground5Color,
                      foregroundColor: ColorTheme.of(context).foreground90Color,
                      radius: 16,
                    ),
                    title: KoyalText.body3(
                      e,
                      color: ColorTheme.of(context).defaultTextColor,
                    ),
                    onTap: () {
                      Navigator.of(context).pop(e);
                    },
                  ),
                )
              : [],
        ],
      ),
      primaryButtonBuilder: (context) => PrimaryButton(
        key: const Key('__pickerEmailButton__'),
        onPressed: () {
          context.navigator.pop();
        },
        text: L10nCappAuth.of(context).emailPickerNoSelection,
      ),
    );
  }
}
