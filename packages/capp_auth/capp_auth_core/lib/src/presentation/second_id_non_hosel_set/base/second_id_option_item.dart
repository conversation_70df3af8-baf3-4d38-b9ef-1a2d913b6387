import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:koyal_auth/koyal_auth.dart';

import '../../../../l10n/i18n.dart';

class SecondIdOptionItem extends StatelessWidget {
  final UserSecondIdType type;
  final bool isSelected;
  final Function()? onTap;

  const SecondIdOptionItem({
    Key? key,
    required this.type,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OptionItem.large(
      SecondIdIcon(type: type),
      _secondIdLabel(type, context),
      isSelected: isSelected,
      onTap: onTap,
    );
  }

  String _secondIdLabel(
    UserSecondIdType? type,
    BuildContext context,
  ) {
    switch (type) {
      case UserSecondIdType.none:
        return L10nCappAuth.of(context).secondIdVerificationNoneOptionLabel;
      case UserSecondIdType.dateOfBirth:
        return L10nCappAuth.of(context).secondIdVerificationDobOptionLabel;
      case UserSecondIdType.email:
        return L10nCappAuth.of(context).secondIdVerificationEmailOptionLabel;
      case UserSecondIdType.pan:
        return L10nCappAuth.of(context).secondIdVerificationPancardOptionLabel;
      case UserSecondIdType.loanAccount:
        return L10nCappAuth.of(context).secondIdVerificationLoanaccountnumberOptionLabel;
      case UserSecondIdType.ktp:
        return L10nCappAuth.of(context).secondIdVerificationKtpOptionLabel;
      case UserSecondIdType.idCard:
        return L10nCappAuth.of(context).secondIdVerificationIdnumberOptionLabel;
      default:
        return '';
    }
  }
}

class SecondIdIcon extends StatelessWidget {
  final UserSecondIdType type;

  const SecondIdIcon({Key? key, required this.type}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(_secondIdSvgIcon(type), package: 'capp_ui_core');
  }

  String _secondIdSvgIcon(UserSecondIdType? type) {
    switch (type) {
      case UserSecondIdType.none:
        return 'assets/svg/alert.svg';
      case UserSecondIdType.dateOfBirth:
        return 'assets/svg/calendar.svg';
      case UserSecondIdType.email:
        return 'assets/svg/email.svg';
      case UserSecondIdType.pan:
        return 'assets/svg/contact_pan.svg';
      case UserSecondIdType.loanAccount:
        return 'assets/svg/contact_contractid.svg';
      case UserSecondIdType.ktp:
        return 'assets/svg/contact_ktp_id.svg';
      case UserSecondIdType.idCard:
        return 'assets/svg/contact_ktp_id.svg';
      default:
        return '';
    }
  }
}
