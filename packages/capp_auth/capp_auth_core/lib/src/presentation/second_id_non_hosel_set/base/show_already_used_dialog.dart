import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

import '../../../../capp_auth_core.dart';

Future showAlreadyUsedDialog(BuildContext context, PrefilledInfo? prefilledInfo) async {
  return showKoyalOverlay<void>(
    context,
    key: const Key('__authErrorAlreadyUsedDialog__'),
    title: L10nCappAuth.of(context).existingAccountDialogTitle,
    primaryButtonBuilder: (c) => PrimaryButton(
      key: const Key('__authErrorAlreadyUsedDialog_login_button__'),
      text: L10nCappAuth.of(context).login,
      onPressed: () => c.authNavigator.toAuthLandingReplace(
        context,
        args: AuthLandingScreenArguments(phoneNumber: prefilledInfo?.phoneNumber),
      ),
    ),
    secondaryButtonBuilder: (c) => SecondaryButton(
      text: L10nCappAuth.of(context).forgotYourPassword,
      onPressed: () => c.navigator.push(
        path: NavigatorPath.cappAuth.pinPassRecoveryEntryScreen,
        arguments: PinPassRecoveryPhoneScreenArguments(
          prefilledInfo: prefilledInfo,
        ),
      ),
    ),
    tertiaryButtonBuilder: (c) => TertiaryButton(
      text: L10nCappAuth.of(context).contactHomeCredit,
      onPressed: () => context.externalNavigator.onContactUsFromDialog(context),
    ),
  );
}
