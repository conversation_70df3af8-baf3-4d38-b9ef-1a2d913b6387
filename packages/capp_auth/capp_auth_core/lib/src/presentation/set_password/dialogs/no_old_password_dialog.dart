import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../l10n/i18n.dart';

Future showNoOldPasswordDialog(BuildContext context) async {
  return showKoyalOverlay<void>(
    context,
    key: const Key('__noOldPasswordDialog__'),
    dismissible: false,
    title: L10nCappAuth.of(context).resetPasswordChooseDifferentPasswordTitle,
    body: KoyalText.body2(
      color: ColorTheme.of(context).foreground60Color,
      L10nCappAuth.of(context).resetPasswordChooseDifferentPasswordSubtitle,
      textAlign: TextAlign.center,
    ),
    primaryButtonBuilder: (c) => PrimaryButton(
      key: const Key('__noOldPasswordDialogOkButton__'),
      text: L10nCappAuth.of(context).resetPasswordChooseDifferentPasswordOkGotItButton,
      onPressed: () {
        c.navigator.pop();
      },
    ),
  );
}
