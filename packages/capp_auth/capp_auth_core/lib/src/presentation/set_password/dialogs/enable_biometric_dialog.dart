// ignore_for_file: use_build_context_synchronously

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:local_auth/local_auth.dart';

import '../../../../capp_auth_core.dart';

Future<bool?> showEnableBiometricDialog(BuildContext context) async {
  final localAuthentication = context.get<LocalAuthentication>();
  final canCheckBiometrics = await localAuthentication.canCheckBiometrics;

  if (canCheckBiometrics) {
    context.get<CappAuthTrackingService>().trackBiometricAuthenticationView();
    return showKoyalOverlay<bool>(
      context,
      key: const Key('__enableBiometricDialog__'),
      dismissible: false,
      title: L10nCappAuth.of(context).enableBiometricDialogTitle,
      body: KoyalText.body2(
        L10nCappAuth.of(context).enableBiometricDialogSubtitle,
        color: ColorTheme.of(context).foreground60Color,
        textAlign: TextAlign.center,
      ),
      primaryButtonBuilder: (c) => PrimaryButton(
        key: const Key('__enableBiometricDialog_confirm__'),
        text: L10nCappAuth.of(context).enableBiometricDialogConfirmButton,
        onPressed: () async {
          context.get<CappAuthTrackingService>().trackBiometricProceedClick();
          try {
            final isAuthenticated =
                await localAuthentication.authenticate(localizedReason: L10nCappAuth.of(context).enableBiometricReason);
            context.navigator.pop(isAuthenticated);
            return;
          } on PlatformException catch (_) {}
          c.navigator.pop();
        },
      ),
      tertiaryButtonBuilder: (c) => TertiaryButton(
        text: L10nCappAuth.of(context).enableBiometricDialogDismissButton,
        onPressed: () {
          context.get<CappAuthTrackingService>().trackBiometricLaterClick();
          c.navigator.pop();
        },
      ),
    );
  }

  return false;
}
