import 'package:capp_innovatrics_core/capp_innovatrics_core.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_auth_core.dart';

class RetrieveAccountIdentifyVerificationScreenArguments extends ScreenArguments {
  final IdCardResult? idCardResult;
  final String? selfieId;
  final String sessionId;
  final PrefilledInfo? prefilledInfo;
  final bool bypassSelection;

  RetrieveAccountIdentifyVerificationScreenArguments({
    required this.idCardResult,
    required this.selfieId,
    required this.sessionId,
    required this.prefilledInfo,
    this.bypassSelection = false,
  });
}
