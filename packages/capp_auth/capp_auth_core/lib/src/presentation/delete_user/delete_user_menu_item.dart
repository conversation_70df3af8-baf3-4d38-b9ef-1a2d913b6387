import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../capp_auth_core.dart';

ListMenuItem buildDeleteUserMenuItem(String url) {
  return ListMenuItem(
    title: 'Delete account - GMA tester',
    onTap: (context) async {
      final tokens = await context.get<ITokenManager>().getTokens();
      // ignore: use_build_context_synchronously
      final userResponse = await context.get<ICurrentUserRepository>().getUser();
      final currentUser = userResponse.fold((l) => null, (r) => r);

      final deleteInsiderUrl = '$url?userId=${currentUser?.id}';

      if ((await GmaPlatform.canLaunchUrl(deleteInsiderUrl)) == false) return;
      // ignore: use_build_context_synchronously
      await context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'WebViewScreen',
        arguments: WebViewArguments(
          url: deleteInsiderUrl,
          headers: {'Authorization': 'Bearer ${tokens?.accessToken}'},
        ),
      );

      // ignore: use_build_context_synchronously
      final result = await context.get<IConnectUserRepository>().fetchUserData();
      await result.fold(
        (l) => context.navigateToLogoutScreen(LogoutReason.tokenExpired),
        (r) {
          showKoyalOverlay<void>(
            context,
            title: 'Logging user out was not successful',
            primaryButtonBuilder: (context) => PrimaryButton(
              text: 'OK',
              onPressed: () => context.navigator.pop(),
            ),
          );
        },
      );
    },
    // ignore: no-native-icons
    icon: const Icon(Icons.delete),
  );
}
