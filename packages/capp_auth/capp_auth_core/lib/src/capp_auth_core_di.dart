import 'package:get_it/get_it.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:identityapi/identityapi.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:local_auth/local_auth.dart';
import 'package:logger/logger.dart';

import '../capp_auth_core.dart';
import 'infrastructure/auth_complete/auth_complete_repository.dart';
import 'infrastructure/user_verification_repository.dart';

void registerCoreDependencies(
  GetIt c, {
  required String insiderBaseUrl,
  required String insiderStageBaseUrl,
  required String insiderPerformanceBaseUrl,
}) {
  c
    ..get<ITranslationOverrideRepository>().registerTranslation(() {
      L10nCappAuth.showKeys = !L10nCappAuth.showKeys;
    })
    ..registerLazySingleton<IBiometricRepository>(
      () => BiometricRepository(
        biometricUnlockApi: c<BiometricUnlockApi>(),
        connectUserRepository: c<IConnectUserRepository>(),
        currentUserRepository: c<ICurrentUserRepository>(),
        platformService: c<IPlatformService>(),
        storage: c<IdentityStorage>(),
        logger: c<Logger>(),
        connectStreamController: c<ConnectStreamController>(),
        featureFlagRepository: c<IFeatureFlagRepository>(),
        userApi: c<UserApi>(),
      ),
    )
    ..registerLazySingleton<IBiometricProvider>(
      () => BiometricProvider(
        logger: c<Logger>(),
        identityStorage: c<IdentityStorage>(),
        biometricRepository: c<IBiometricRepository>(),
      ),
    )
    ..registerTrackingLazySingleton<IUserVerificationRepository>(
      () => UserVerificationRepository(
        usernameVerificationApi: c<UsernameVerificationSessionApi>(),
        usernameVerificationV2Api: c<UsernameVerificationV2Api>(),
        featureFlagRepository: c<IFeatureFlagRepository>(),
        platformService: c<IPlatformService>(),
        identityConfig: c<IdentityConfig>(),
        logger: c<Logger>(),
      ),
    )
    ..registerTrackingFactory(
      () => LoginCubit(
        c<ICurrentUserRepository>(),
      ),
    )
    ..registerFactory<IChangePasswordSettingsRepository>(
      () => ChangePasswordSettingsRepository(
        changePasswordApi: c<ChangePasswordApi>(),
        changePasswordOTPApi: c<ChangePasswordOTPApi>(),
        logger: c<Logger>(),
      ),
    )
    ..registerTrackingFactory(
      () => PhoneIdentificationBloc(
        userVerificationRepository: c<IUserVerificationRepository>(),
        otpRepository: c<IOtpRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => Login2ndIdBloc(
        userVerificationRepository: c<IUserVerificationRepository>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => ChangeSecondIdBloc(
        cappSignUpRepository: c<ICappSignUpRepository>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => SecondIdHoselPairingBloc(
        userRepository: c<IUserRepository>(),
        connectUserRepository: c<IConnectUserRepository>(),
        hoselPairingRepository: c<IHoselPairingRepository>(),
        gtp: c<GlobalTrackingProperties>(),
      ),
    )

    /// Authentication
    ..registerTrackingFactory(
      () => AuthenticationBloc(
        authenticationRepository: c<IAuthenticationRepository>(),
        otpRepository: c<IOtpRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => Authentication2ndIdBloc(
        authenticationRepository: c<IAuthenticationRepository>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => AuthenticationAuthenticateVerificationCubit(
        authenticationRepository: c<IAuthenticationRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => AuthenticationIdentifyVerificationCubit(
        authenticationRepository: c<IAuthenticationRepository>(),
      ),
    )

    /// Retrieve account
    ..registerTrackingFactory(
      () => RetrieveAccount2ndIdBloc(
        retrieveAccountRepository: c<IRetrieveAccountRepository>(),
        userRepository: c<ICurrentUserRepository>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => RetrieveAccountAuthenticateVerificationCubit(
        retrieveAccountRepository: c<IRetrieveAccountRepository>(),
        userRepository: c<IUserRepository>(),
        connectUserRepository: c<IConnectUserRepository>(),
        gtp: c<GlobalTrackingProperties>(),
      ),
    )
    ..registerTrackingFactory(
      () => RetrieveAccountIdentifyVerificationCubit(
        retrieveAccountRepository: c<IRetrieveAccountRepository>(),
        userRepository: c<IUserRepository>(),
        connectUserRepository: c<IConnectUserRepository>(),
        gtp: c<GlobalTrackingProperties>(),
      ),
    )

    /// Change phone number process
    ..registerTrackingFactory(
      () => ChangePhoneInitCubit(
        changePhoneRepository: c<IChangePhoneRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => ChangePhoneLoginPhoneBloc(
        changePhoneRepository: c<IChangePhoneRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => ChangePhone2ndIdBloc(
        changePhoneRepository: c<IChangePhoneRepository>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => ChangePhoneNewPhoneBloc(
        changePhoneRepository: c<IChangePhoneRepository>(),
        featureFlagRepository: c<IFeatureFlagRepository>(),
        otpRepository: c<IOtpRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => ChangePhoneBosVerificationCubit(
        changePhoneRepository: c<IChangePhoneRepository>(),
        bigDataCollectionService: c<IBigDataCollectionService>(),
      ),
    )
    ..registerTrackingFactory<IResetPasswordRepository>(
      () => ResetPasswordRepository(
        logger: c<Logger>(),
        api: c<CAPPPasswordResetV2Api>(),
        apiV3: c<CAPPPasswordResetV3Api>(),
        platformService: c<IPlatformService>(),
        apiV4: c<CAPPPasswordResetV4Api>(),
        featureFlagRepository: c<IFeatureFlagRepository>(),
        passwordSignService: c<IPasswordSignService>(),
        passwordAttemptsStorage: c<PasswordAttemptsStorage>(),
      ),
    )
    ..registerTrackingFactory(
      () => PinPassRecovery2ndIdBloc(
        resetPasswordRepository: c<IResetPasswordRepository>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => PinPassRecovery2ndIdShortcutBloc(
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
        authenticationRepository: c<IAuthenticationRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => PasswordRecoverySetPasswordBloc(
        resetPasswordRepository: c<IResetPasswordRepository>(),
        passwordLengthProvider: c<IPasswordLengthProvider>(),
        trackingService: c<CappAuthTrackingService>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => PasswordRecoverySetPasswordShortcutBloc(
        authenticationRepository: c<IAuthenticationRepository>(),
        passwordLengthProvider: c<IPasswordLengthProvider>(),
        trackingService: c<CappAuthTrackingService>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => PinRecoverySuccessCubit(
        connectUserRepository: c<IConnectUserRepository>(),
        featureFlagRepository: c<IFeatureFlagRepository>(),
      ),
    )
    ..registerTrackingFactory(AuthErrorHandlerCubit.new)
    ..registerLazySingleton(
      () => CappAuthTrackingService(
        eventTrackingService: c<EventTrackingService>(),
      ),
    )
    ..registerTrackingFactory<IUnblockAccountRepository>(
      () => UnblockAccountRepository(
        logger: c<Logger>(),
        api: c<CAPPUnblockAccountApi>(),
        platformService: c<IPlatformService>(),
      ),
    )
    ..registerTrackingFactory(
      () => UnblockAccount2ndIdBloc(
        unblockAccountRepository: c<IUnblockAccountRepository>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
        trackingService: c<CappAuthTrackingService>(),
      ),
    )
    ..registerLazySingleton<IPasswordLengthProvider>(
      () => PasswordLengthProvider(
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
        remoteConfigRepository: c<IRemoteConfigRepository>(),
        currentUserRepository: c<ICurrentUserRepository>(),
      ),
    )
    ..registerLazySingleton<LaunchQueryProvider>(
      LaunchQueryProvider.new,
    )
    ..registerTrackingLazySingleton<IChangePasswordRepository>(
      () => ChangePasswordRepository(
        userApi: c<UserApi>(),
        logger: c<Logger>(),
        changePasswordSetPasswordApi: c<ChangePasswordSetPasswordApi>(),
      ),
    )
    ..registerTrackingFactory(
      () => ChangePasswordSetPasswordBloc(
        changePasswordRepository: c<IChangePasswordRepository>(),
        passwordLengthProvider: c<IPasswordLengthProvider>(),
        environmentConfigRepository: c<IEnvironmentConfigRepository>(),
        trackingService: c<CappAuthTrackingService>(),
      ),
    )
    ..registerTrackingFactory(
      () => EnableBiometricCubit(c<IBiometricProvider>()),
    )
    ..registerTrackingLazySingleton<LocalAuthentication>(LocalAuthenticationWrapper.new)
    ..registerTrackingFactory(
      () => UserRefresherCubit(
        featureFlagInitService: c<IFeatureFlagInitService>(),
        connectUserRepository: c<IConnectUserRepository>(),
      ),
    )
    ..registerTrackingFactory(
      () => InsiderLogoutCubit(
        insiderBaseUrl: insiderBaseUrl,
        insiderStageBaseUrl: insiderStageBaseUrl,
        insiderPerformanceBaseUrl: insiderPerformanceBaseUrl,
        environmentRepository: c<IEnvironmentRepository>(),
        featureFlagInitService: c<IFeatureFlagInitService>(),
        connectUserRepository: c<IConnectUserRepository>(),
        currentUserRepository: c<ICurrentUserRepository>(),
      ),
    )
    ..registerLazySingleton(RegistrationLegalLogData.new)
    ..registerTrackingFactory(
      () => PinRecoveryPhoneBloc(
        resetPasswordRepository: c<IResetPasswordRepository>(),
        otpRepository: c<IOtpRepository>(),
      ),
    )
    ..registerTrackingFactory<UnblockAccountPhoneBloc>(
      () => UnblockAccountPhoneBloc(
        unblockAccountRepository: c<IUnblockAccountRepository>(),
        otpRepository: c<IOtpRepository>(),
      ),
    )
    ..registerLazySingleton<IAuthCompleteRepository>(
      () => AuthCompleteRepository(
        l: c<Logger>(),
      ),
    );
}

void registerCoreAuthenticationRepository(GetIt c) {
  c.registerTrackingLazySingleton<IAuthenticationRepository>(
    () => AuthenticationRepository(
      authenticationApi: c<AuthenticationApi>(),
      platformService: c<IPlatformService>(),
      connectUserRepository: c<IConnectUserRepository>(),
      logger: c<Logger>(),
      identityConfig: c<IdentityConfig>(),
      storage: c<IdentityStorage>(),
      connectStreamController: c<ConnectStreamController>(),
      featureFlagRepository: c<IFeatureFlagRepository>(),
      passwordSignService: c<IPasswordSignService>(),
      passwordAttemptsStorage: c<PasswordAttemptsStorage>(),
    ),
  );
}

void registerCoreRetrieveAccountRepository(GetIt c) {
  c.registerTrackingLazySingleton<IRetrieveAccountRepository>(
    () => RetrieveAccountRepository(
      retrieveAccountApi: c<RetrieveAccountApi>(),
      logger: c<Logger>(),
    ),
  );
}
