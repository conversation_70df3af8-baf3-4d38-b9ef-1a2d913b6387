import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('IdentifyResultApprovedScreen', () {
    Future<void> pumpWidget(
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        makeLocalizedTestableWidget(
          IdentifyResultApprovedScreen(
            arguments: IdentifyResultApprovedScreenArgs(
              identifyFlowType: IdentifyFlowType.changePhone,
            ),
          ),
          localizationsDelegates: [
            L10nCappAuth.delegate,
          ],
        ),
      );
      await tester.pump();
    }

    testWidgets('Check if screen has all proper values', (tester) async {
      await pumpWidget(tester);

      final title = find.text(L10nCappAuth().identifyResultApprovedTitle);
      final subtitle = find.text(L10nCappAuth().identifyResultApprovedSubtitle);
      final buttonText = find.text(L10nCappAuth().identifyResultApprovedContinue);
      final findImage = find.byType(AssetSvgImage);
      final imagePath = tester.widget<AssetSvgImage>(findImage).path;

      expect(
        imagePath,
        'assets/svg/bos_identify_approved.svg',
      );
      expect(title, findsOneWidget);
      expect(subtitle, findsOneWidget);
      expect(buttonText, findsOneWidget);
    });
  });
}
