import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/utils/common_logic.dart';
import 'package:capp_ui_core/widgets/buttons/primary_button.dart';
import 'package:capp_ui_core/widgets/dialogs/koyal_overlay.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  Future<void> pumpWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      makeLocalizedTestableWidget(
        localizationsDelegates: [
          L10nCappAuth.delegate,
        ],
        Builder(
          builder: (context) => PrimaryButton(
            text: 'showAuthErrorPermanentLockAttemptDialog',
            onPressed: () {
              showAuthErrorPermanentLockAttemptDialog(context: context);
            },
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('showAuthErrorPermanentLockAttemptDialog', () {
    testWidgets('Dialog has proper values', (tester) async {
      await pumpWidget(tester);

      await tester.tap(find.text('showAuthErrorPermanentLockAttemptDialog'));
      await tester.pumpAndSettle();

      expect(find.byType(KoyalOverlay), findsOneWidget);
      expect(find.text(L10nCappAuth().authErrorDialogPermanentLockAttempt), findsOneWidget);
      expect(find.text(L10nCappAuth().unblockAccount), findsOneWidget);
      expect(find.text(L10nCappAuth().contactHomeCredit), findsOneWidget);
    });
  });
}
