import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/utils/common_logic.dart';
import 'package:capp_ui_core/widgets/buttons/primary_button.dart';
import 'package:capp_ui_core/widgets/dialogs/koyal_overlay.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';

void main() {
  Future<void> pumpWidget(WidgetTester tester, {int? blockedUntil}) async {
    await tester.pumpWidget(
      makeLocalizedTestableWidget(
        localizationsDelegates: [
          L10nCappAuth.delegate,
        ],
        Builder(
          builder: (context) => PrimaryButton(
            text: 'showAuthErrorTemporaryBlockDialogWithTime',
            onPressed: () {
              showAuthErrorTemporaryBlockDialogWithTime(context, blockedUntil: blockedUntil);
            },
          ),
        ),
      ),
    );
    await tester.pump();
  }

  group('showAuthErrorTemporaryBlockDialogWithTime', () {
    setUpAll(() async {
      await initializeDateFormatting('en', '');
    });
    testWidgets('Dialog has proper values with blocUntil == null', (tester) async {
      await pumpWidget(tester);

      await tester.tap(find.text('showAuthErrorTemporaryBlockDialogWithTime'));
      await tester.pumpAndSettle();

      expect(find.byType(KoyalOverlay), findsOneWidget);
      expect(find.text(L10nCappAuth().temporaryBlockDialogTimeTitle), findsOneWidget);
      expect(
        find.text(
          L10nCappAuth().temporaryBlockDialogTimeBody(
            DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().add(const Duration(minutes: 5))),
          ),
        ),
        findsOneWidget,
      );
      expect(find.text(L10nCappAuth().okGotIt), findsOneWidget);
    });

    testWidgets('Dialog has proper values with blocUntil != null', (tester) async {
      const blockedUntil = 200;
      await pumpWidget(tester, blockedUntil: blockedUntil);

      await tester.tap(find.text('showAuthErrorTemporaryBlockDialogWithTime'));
      await tester.pumpAndSettle();

      expect(find.byType(KoyalOverlay), findsOneWidget);
      expect(find.text(L10nCappAuth().temporaryBlockDialogTimeTitle), findsOneWidget);
      expect(
        find.text(
          L10nCappAuth().temporaryBlockDialogTimeBody(
            DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now().add(const Duration(seconds: blockedUntil))),
          ),
        ),
        findsOneWidget,
      );
      expect(find.text(L10nCappAuth().okGotIt), findsOneWidget);
    });
  });
}
