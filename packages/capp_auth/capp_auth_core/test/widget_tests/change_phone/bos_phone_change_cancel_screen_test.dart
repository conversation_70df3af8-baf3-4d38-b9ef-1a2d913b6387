import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_auth/koyal_auth.dart';

void main() {
  group('BosPhoneChangeCancelScreen', () {
    Future<void> pumpWidget(
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        makeLocalizedTestableWidget(
          BosPhoneChangeCancelScreen(
            arguments: BosPhoneChangeCancelScreenArgs(
              phoneNumber: '',
              reasonOfChange: ReasonOfChange.newAndKeepOld,
              sessionId: '',
            ),
          ),
          localizationsDelegates: [
            L10nCappAuth.delegate,
          ],
        ),
      );
      await tester.pump();
    }

    testWidgets('Check if screen has all proper values', (tester) async {
      await pumpWidget(tester);

      final title = find.text(L10nCappAuth().bosPhoneChangeCanceledTitle);
      final subtitle = find.text(L10nCappAuth().bosPhoneChangeCanceledSubtitle);
      final buttonText = find.text(L10nCappAuth().identifyResultCanceledTryAgain);
      final secondaryButtonText = find.text(L10nCappAuth().identifyResultCanceledLater);
      final findImage = find.byType(AssetSvgImage);
      final imagePath = tester.widget<AssetSvgImage>(findImage).path;

      expect(imagePath, 'assets/svg/bos_identify_canceled.svg');
      expect(title, findsOneWidget);
      expect(subtitle, findsOneWidget);
      expect(buttonText, findsOneWidget);
      expect(secondaryButtonText, findsOneWidget);
    });
  });
}
