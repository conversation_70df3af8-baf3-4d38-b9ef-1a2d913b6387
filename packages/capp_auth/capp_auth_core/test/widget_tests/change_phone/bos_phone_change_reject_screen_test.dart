import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BosPhoneChangeRejectScreen', () {
    Future<void> pumpWidget(
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        makeLocalizedTestableWidget(
          BosPhoneChangeRejectScreen(
            arguments: IdentifyResultRejectedScreenArgs(
              identifyFlowType: IdentifyFlowType.changePhone,
            ),
          ),
          localizationsDelegates: [
            L10nCappAuth.delegate,
          ],
        ),
      );
      await tester.pump();
    }

    testWidgets('Check if screen has all proper values', (tester) async {
      await pumpWidget(tester);

      final title = find.text(L10nCappAuth().bosPhoneChangeRejectTitle);
      final subtitle = find.text(L10nCappAuth().bosPhoneChangeRejectSubtitle);
      final buttonText = find.text(L10nCappAuth().identifyResultRejectedOkGotIt);
      final secondaryButtonText = find.text(L10nCappAuth().identifyResultRejectedContactHci);
      final findImage = find.byType(AssetSvgImage);
      final imagePath = tester.widget<AssetSvgImage>(findImage).path;

      expect(
        imagePath,
        'assets/svg/bos_identify_rejected.svg',
      );
      expect(title, findsOneWidget);
      expect(subtitle, findsOneWidget);
      expect(buttonText, findsOneWidget);
      expect(secondaryButtonText, findsOneWidget);
    });
  });
}
