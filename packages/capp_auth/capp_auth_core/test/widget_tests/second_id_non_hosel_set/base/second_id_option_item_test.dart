import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:koyal_auth/koyal_auth.dart';

void main() {
  group('secondIdOptionItem', () {
    setUp(() async {
      await initializeDateFormatting('en', '');
    });
    Future<void> pumpWidget(
      WidgetTester tester,
      UserSecondIdType type,
      void Function() onTap, {
      required bool isSelected,
      String? value,
    }) async {
      await tester.pumpWidget(
        makeLocalizedTestableWidget(
          KoyalScaffold(
            body: SecondIdOptionItem(type: type, isSelected: isSelected, onTap: onTap),
          ),
          localizationsDelegates: [
            L10nCappAuth.delegate,
          ],
        ),
      );
      await tester.pump();
    }

    testWidgets('Second id input type none', (tester) async {
      await pumpWidget(
        tester,
        UserSecondIdType.none,
        () {},
        isSelected: false,
      );
      expect(find.text(L10nCappAuth().secondIdVerificationNoneOptionLabel), findsOneWidget);
    });
    testWidgets('Second id input type UserSecondIdType.dateOfBirth', (tester) async {
      await pumpWidget(
        tester,
        UserSecondIdType.dateOfBirth,
        () {},
        isSelected: false,
      );
      expect(find.text(L10nCappAuth().secondIdVerificationDobOptionLabel), findsOneWidget);
    });

    testWidgets('Second id input type UserSecondIdType.email', (tester) async {
      await pumpWidget(
        tester,
        UserSecondIdType.email,
        () {},
        isSelected: false,
      );
      expect(find.text(L10nCappAuth().secondIdVerificationEmailOptionLabel), findsOneWidget);
    });
    testWidgets('Second id input type UserSecondIdType.pan', (tester) async {
      await pumpWidget(
        tester,
        UserSecondIdType.pan,
        () {},
        isSelected: false,
      );
      expect(find.text(L10nCappAuth().secondIdVerificationPancardOptionLabel), findsOneWidget);
    });
    testWidgets('Second id input type UserSecondIdType.loanAccount', (tester) async {
      await pumpWidget(
        tester,
        UserSecondIdType.loanAccount,
        () {},
        isSelected: false,
      );
      expect(find.text(L10nCappAuth().secondIdVerificationLoanaccountnumberOptionLabel), findsOneWidget);
    });
    testWidgets('Second id input type UserSecondIdType.ktp', (tester) async {
      await pumpWidget(
        tester,
        UserSecondIdType.ktp,
        () {},
        isSelected: false,
      );
      expect(find.text(L10nCappAuth().secondIdVerificationKtpOptionLabel), findsOneWidget);
    });
    testWidgets('Second id input type UserSecondIdType.idCard', (tester) async {
      await pumpWidget(
        tester,
        UserSecondIdType.idCard,
        () {},
        isSelected: false,
      );
      expect(find.text(L10nCappAuth().secondIdVerificationIdnumberOptionLabel), findsOneWidget);
    });
  });
}
