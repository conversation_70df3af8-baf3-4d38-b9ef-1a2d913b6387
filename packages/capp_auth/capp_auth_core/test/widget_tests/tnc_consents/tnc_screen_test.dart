import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TncScreen', () {
    Future<void> pumpWidget(
      WidgetTester tester,
      Widget widgetLogoBuilder,
    ) async {
      await tester.pumpWidget(
        makeLocalizedTestableWidget(
          TncScreen(
            arguments: TncScreenArguments(
              destination: (context) {},
            ),
            widgetLogoBuilder: widgetLogoBuilder,
          ),
          localizationsDelegates: [
            L10nCappAuth.delegate,
          ],
        ),
      );
      await tester.pump();
    }

    testWidgets('Check if screen has all proper values', (tester) async {
      await pumpWidget(
        tester,
        const SizedBox(
          child: Text('test'),
        ),
      );

      final title = find.text(L10nCappAuth().tncTitle);
      final subtitle = find.text(L10nCappAuth().tncBody);
      final buttonText = find.text(L10nCappAuth().confirm);

      expect(title, findsOneWidget);
      expect(find.text('test'), findsOneWidget);
      expect(find.text('test'), findsOneWidget);
      expect(subtitle, findsOneWidget);
      expect(buttonText, findsOneWidget);
    });
  });
}
