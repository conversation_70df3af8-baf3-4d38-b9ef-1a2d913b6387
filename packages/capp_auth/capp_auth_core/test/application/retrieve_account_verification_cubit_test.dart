import 'package:bloc_test/bloc_test.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:mocktail/mocktail.dart';

import '../shared_mocks.dart';

void main() {
  late MockRetrieveAccountRepository retrieveAccountRepository;
  late MockUserRepository identityRepository;
  late MockConnectUserRepository connectUserRepository;
  late GlobalTrackingProperties gtp;
  late RetrieveAccountAuthenticateVerificationCubit sut;

  const sessionId = 'sessionId';

  setUp(() {
    retrieveAccountRepository = MockRetrieveAccountRepository();
    identityRepository = MockUserRepository();
    connectUserRepository = MockConnectUserRepository();
    gtp = GlobalTrackingProperties(utmParameters: {});
    sut = RetrieveAccountAuthenticateVerificationCubit(
      retrieveAccountRepository: retrieveAccountRepository,
      userRepository: identityRepository,
      connectUserRepository: connectUserRepository,
      gtp: gtp,
    );
  });

  tearDown(() => sut.close());

  group('RetrieveAccountAuthenticateVerificationCubit tests', () {
    blocTest<RetrieveAccountAuthenticateVerificationCubit, RetrieveAccountAuthenticateVerificationState>(
      'startBosAutheticateHandling(), retrieveAccountRepository is returning failure',
      build: () {
        when(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            left(
              Failure<UserVerificationFailureType, void>(type: UserVerificationFailureType.invalid),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      wait: const Duration(seconds: 1),
      expect: () => <TypeMatcher>[
        const TypeMatcher<RetrieveAccountAuthenticateVerificationError>().having(
          (s) => s.failure.type,
          'Invalid error',
          UserVerificationFailureType.invalid,
        ),
      ],
      verify: (_) => verify(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).called(1),
    );

    blocTest<RetrieveAccountAuthenticateVerificationCubit, RetrieveAccountAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning approved',
      build: () {
        when(() => connectUserRepository.refreshToken(updateClaims: true)).thenAnswer((_) => Future.value(right(unit)));
        when(() => identityRepository.userCuid()).thenAnswer((_) => Future.value('cuid'));
        when(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              const RetrieveAccountAuthenticateResultResponse(
                bosResult: AuthenticateBosResult.approved,
                allowedNextSteps: null,
                callAgainInterval: null,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      wait: const Duration(seconds: 1),
      expect: () => <TypeMatcher>[const TypeMatcher<RetrieveAccountAuthenticateVerificationApprove>()],
      verify: (_) => verify(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).called(1),
    );

    blocTest<RetrieveAccountAuthenticateVerificationCubit, RetrieveAccountAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning not approved',
      build: () {
        when(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              RetrieveAccountAuthenticateResultResponse(
                bosResult: AuthenticateBosResult.notApproved,
                allowedNextSteps: AuthenticateAllowedNextSteps(
                  escape: false,
                  retry: false,
                  identify: false,
                  identifyEscape: false,
                ),
                callAgainInterval: null,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      wait: const Duration(seconds: 1),
      expect: () => <TypeMatcher>[
        const TypeMatcher<RetrieveAccountAuthenticateVerificationNotApprove>(),
      ],
      verify: (_) => verify(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).called(1),
    );

    blocTest<RetrieveAccountAuthenticateVerificationCubit, RetrieveAccountAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning null. Reaching maxPollingCount',
      build: () {
        when(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              const RetrieveAccountAuthenticateResultResponse(
                bosResult: null,
                allowedNextSteps: null,
                callAgainInterval: null,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      wait: const Duration(seconds: 2),
      expect: () => <TypeMatcher>[
        const TypeMatcher<RetrieveAccountAuthenticateVerificationError>().having(
          (s) => s.failure.type,
          'unexpected failure',
          UserVerificationFailureType.unexpected,
        ),
      ],
    );

    blocTest<RetrieveAccountAuthenticateVerificationCubit, RetrieveAccountAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning null',
      build: () {
        when(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              const RetrieveAccountAuthenticateResultResponse(
                bosResult: null,
                allowedNextSteps: null,
                callAgainInterval: 1,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      expect: () => <dynamic>[],
    );

    blocTest<RetrieveAccountAuthenticateVerificationCubit, RetrieveAccountAuthenticateVerificationState>(
      'startBosAutheticateHandling(), initialWaitTime > 0, retrieveAccountAuthenticateResult never called',
      build: () => sut,
      act: (sut) => sut.startBosAuthenticateHandling(sessionId, initialWaitTime: 1),
      expect: () => <dynamic>[],
      verify: (_) => verifyNever(() => retrieveAccountRepository.retrieveAccountAuthenticateResult(sessionId)),
    );
  });
}
