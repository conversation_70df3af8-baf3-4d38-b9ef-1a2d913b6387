import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:dartz/dartz.dart';
import 'package:koyal_auth/src/domain/auth/failure/user_failure.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../shared_mocks.dart';

void main() {
  late MockCurrentUserRepository currentUserRepository;
  late PasswordLengthProvider sut;
  late MockRemoteConfigRepository remoteConfigRepository;
  late MockEnvironmentConfigRepository environmentConfigRepository;

  const pinLengthRemoteConfigKey = 'pin_length';
  const configurationRoot = ConfigurationRoot(
    cappIdentityConfiguration: CappIdentityConfiguration(
      passwordLength: 5,
      passwordRegex: 'passwordRegex',
      emailRegex: 'emailRegex',
      passwordRegexLength: 'passwordRegexLength',
      passwordRegexDigitsLowerCaseUperCaseLetters: 'passwordRegexDigitsLowerCaseUperCaseLetters',
      passwordRegexSpecialCharacters: 'passwordRegexSpecialCharacters',
      threeRepeatingConsecutiveDigits: 'threeRepeatingConsecutiveDigits',
      threeSucceedingConsecutiveDigits: 'threeSucceedingConsecutiveDigits',
    ),
  );

  setUpAll(() {
    remoteConfigRepository = MockRemoteConfigRepository();
    environmentConfigRepository = MockEnvironmentConfigRepository();
    currentUserRepository = MockCurrentUserRepository();
    sut = PasswordLengthProvider(
      environmentConfigRepository: environmentConfigRepository,
      remoteConfigRepository: remoteConfigRepository,
      currentUserRepository: currentUserRepository,
    );
  });

  group('Testing password_length_provider, Future init()', () {
    // futures[1] 'l' == false, expect {}
    test(
      'Test Future init, if UserFailure is false, then there shouldnt be password length',
      () async {
        //Arrange
        when(() => currentUserRepository.getCurrentUserPasswordLength()).thenAnswer(
          (_) => Future<Either<UserFailure, int?>>.value(
            left(const UserFailure.notFound()),
          ),
        ); // simplify UserFailure so i dont need to write specific case
        when(() => currentUserRepository.passwordLengthStream())
            .thenAnswer((_) => Future<Stream<int?>>.value(StreamController<int?>().stream));

        //Act
        await sut.init();

        //Assert
        expect(sut.passwordLengthInLastLogin, null);
      },
    );

    // futures[1] 'r' == true, expect {passwordLengthInLastLogin == r}
    test(
      'Test Future init, testing for the right password length',
      () async {
        //Arrange
        when(() => currentUserRepository.getCurrentUserPasswordLength())
            .thenAnswer((_) => Future<Either<UserFailure, int?>>.value(right(5)));
        when(() => currentUserRepository.passwordLengthStream())
            .thenAnswer((_) => Future<Stream<int?>>.value(StreamController<int?>().stream));

        //Act
        await sut.init();

        //Assert
        expect(sut.passwordLengthInLastLogin, 5);
      },
    );
  });

  group('testing password_length_provider, passwordLength', () {
    //passwordLengthInLastLogin != null, expect passwordLength
    test('we have got a not null passwordLengthInLastLogin', () {
      //Arrange
      sut.passwordLengthInLastLogin = 5;
      when(() => remoteConfigRepository.getIntValueByKey(pinLengthRemoteConfigKey)).thenAnswer((_) => right(5));

      //Act
      final result = sut.passwordLength;

      //Assert
      expect(result, 5);
    });

    //demoRemoteConfigResponse != null, expect demoRemoteConfigResponse
    test('we have got a not null demoRemoteConfigValue', () {
      //Arrange
      sut.demoRemoteConfigValue = 5;
      when(() => remoteConfigRepository.getIntValueByKey(pinLengthRemoteConfigKey)).thenAnswer((_) => right(5));

      //Act
      final result = sut.passwordLength;

      //Assert
      expect(result, 5);
    });

    //remoteConfigResponse(l) == false || int <= 0, expect fromEnvironmentConfig
    test('remoteConfigResponse on the left is true, we are returning fromEnvironmentConfig', () {
      //Arrange
      when(() => remoteConfigRepository.getIntValueByKey(pinLengthRemoteConfigKey))
          .thenReturn(left(const RemoteConfigFailure.unexpected()));
      when(() => environmentConfigRepository.currentConfig()).thenReturn(configurationRoot);

      //Act
      final result = sut.passwordLength;

      //Assert
      expect(result, configurationRoot.cappIdentityConfiguration.passwordLength);
    });

    //remoteConfigResponse(l) == true || int > 0, expect r
    test('remoteConfigResponse on the left side is false, r is 0', () {
      //Arrange
      when(() => remoteConfigRepository.getIntValueByKey(pinLengthRemoteConfigKey)).thenAnswer((_) => right(5));

      //Act
      final result = sut.passwordLength;

      //Assert
      expect(result, 5);
    });

    //remoteConfigResponse(l) == false || int > 0, expect r
    test('remoteConfigResponse on the left side is true, r is > 0', () {
      //Arrange
      when(() => remoteConfigRepository.getIntValueByKey(pinLengthRemoteConfigKey)).thenReturn(right(0));
      when(() => environmentConfigRepository.currentConfig()).thenReturn(configurationRoot);

      //Act
      final result = sut.passwordLength;

      //Assert
      expect(result, configurationRoot.cappIdentityConfiguration.passwordLength);
    });
  });
}
