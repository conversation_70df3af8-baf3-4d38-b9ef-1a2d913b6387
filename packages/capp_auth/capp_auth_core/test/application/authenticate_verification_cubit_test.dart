import 'package:bloc_test/bloc_test.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:mocktail/mocktail.dart';

import '../shared_mocks.dart';

void main() {
  late MockAuthenticationRepository authenticationRepository;
  late AuthenticationAuthenticateVerificationCubit sut;

  const sessionId = '123456';

  setUp(() {});

  group('Test second identifier verification', () {
    blocTest<AuthenticationAuthenticateVerificationCubit, AuthenticationAuthenticateVerificationState>(
      'startBosAutheticateHandling(), authenticationRepository is returning failure',
      build: () {
        authenticationRepository = MockAuthenticationRepository();
        sut = AuthenticationAuthenticateVerificationCubit(
          authenticationRepository: authenticationRepository,
        );
        when(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            left(
              Failure<UserVerificationFailureType, void>(type: UserVerificationFailureType.invalid),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      wait: const Duration(seconds: 1),
      expect: () => <TypeMatcher>[
        const TypeMatcher<AuthenticationAuthenticateVerificationError>().having(
          (s) => s.failure.type,
          'Invalid error',
          UserVerificationFailureType.invalid,
        ),
      ],
      verify: (sut) {
        verify(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).called(1);
      },
    );

    blocTest<AuthenticationAuthenticateVerificationCubit, AuthenticationAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning approved',
      build: () {
        authenticationRepository = MockAuthenticationRepository();
        sut = AuthenticationAuthenticateVerificationCubit(
          authenticationRepository: authenticationRepository,
        );
        when(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              const AuthenticationAuthenticateResultResponse(
                bosResult: AuthenticateBosResult.approved,
                allowed2ndIds: [],
                loginSessionId: sessionId,
                allowedNextSteps: null,
                passwordLength: 6,
                callAgainInterval: null,
                changePasswordOptions: null,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      wait: const Duration(seconds: 1),
      expect: () => <TypeMatcher>[const TypeMatcher<AuthenticationAuthenticateVerificationApprove>()],
      verify: (sut) {
        verify(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).called(1);
      },
    );

    blocTest<AuthenticationAuthenticateVerificationCubit, AuthenticationAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning not approved',
      build: () {
        authenticationRepository = MockAuthenticationRepository();
        sut = AuthenticationAuthenticateVerificationCubit(
          authenticationRepository: authenticationRepository,
        );
        when(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              AuthenticationAuthenticateResultResponse(
                bosResult: AuthenticateBosResult.notApproved,
                allowed2ndIds: [],
                loginSessionId: sessionId,
                allowedNextSteps: AuthenticateAllowedNextSteps(
                  escape: false,
                  retry: false,
                  identify: false,
                  identifyEscape: false,
                ),
                passwordLength: 6,
                callAgainInterval: null,
                changePasswordOptions: null,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId),
      wait: const Duration(seconds: 1),
      expect: () => <TypeMatcher>[const TypeMatcher<AuthenticationAuthenticateVerificationNotApprove>()],
      verify: (sut) {
        verify(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).called(1);
      },
    );

    blocTest<AuthenticationAuthenticateVerificationCubit, AuthenticationAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning null, callAgainInterval is null',
      build: () {
        authenticationRepository = MockAuthenticationRepository();
        sut = AuthenticationAuthenticateVerificationCubit(
          authenticationRepository: authenticationRepository,
        );
        when(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              const AuthenticationAuthenticateResultResponse(
                bosResult: null,
                allowed2ndIds: null,
                loginSessionId: null,
                allowedNextSteps: null,
                passwordLength: 6,
                callAgainInterval: null,
                changePasswordOptions: null,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) {
        sut.startBosAuthenticateHandling(sessionId);
      },
      expect: () => <TypeMatcher>[
        const TypeMatcher<AuthenticationAuthenticateVerificationError>()
            .having((s) => s.failure.type, 'unexpected failure', UserVerificationFailureType.unexpected),
      ],
    );

    blocTest<AuthenticationAuthenticateVerificationCubit, AuthenticationAuthenticateVerificationState>(
      'startBosAutheticateHandling(), bosResult is returning null. expect []',
      build: () {
        authenticationRepository = MockAuthenticationRepository();
        sut = AuthenticationAuthenticateVerificationCubit(
          authenticationRepository: authenticationRepository,
        );
        when(() => authenticationRepository.authenticationAuthenticateResult(sessionId)).thenAnswer(
          (_) => Future.value(
            right(
              const AuthenticationAuthenticateResultResponse(
                bosResult: null,
                allowed2ndIds: null,
                loginSessionId: null,
                allowedNextSteps: null,
                passwordLength: 6,
                callAgainInterval: 1,
                changePasswordOptions: null,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) {
        sut.startBosAuthenticateHandling(sessionId);
      },
      expect: () => <dynamic>[],
    );

    blocTest<AuthenticationAuthenticateVerificationCubit, AuthenticationAuthenticateVerificationState>(
      'startBosAutheticateHandling(), initialWaitTime > 0, authenticationAuthenticateResult never called',
      build: () {
        authenticationRepository = MockAuthenticationRepository();
        return AuthenticationAuthenticateVerificationCubit(
          authenticationRepository: authenticationRepository,
        );
      },
      act: (sut) => sut.startBosAuthenticateHandling(sessionId, initialWaitTime: 1),
      expect: () => <dynamic>[],
      verify: (_) => verifyNever(() => authenticationRepository.authenticationAuthenticateResult(sessionId)),
    );
  });
}
