import 'package:bloc_test/bloc_test.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_innovatrics_core/capp_innovatrics_core.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:mocktail/mocktail.dart';

import '../shared_mocks.dart';

void main() {
  const sessionId = 'sessionId';

  final idCardresult = IdCardResult(
    idNumber: 'idNumber',
    gender: GenderType.male,
    isAddressSame: true,
    uploadedImageId: 'uploadedImageId',
  );
  final identifyRequest = RetrieveAccountIdentifyRequest(
    userDocumentIds: const [
      'selfieId',
      'uploadedImageId',
    ],
    customerInformation: IdCardResulMapper.mapIdCardResultToCustomerInformation(idCardresult),
  );

  late MockRetrieveAccountRepository retrieveAccountRepository;
  late MockUserRepository userRepository;
  late MockConnectUserRepository connectUserRepository;
  late GlobalTrackingProperties gtp;
  late RetrieveAccountIdentifyVerificationCubit sut;

  setUp(() {
    retrieveAccountRepository = MockRetrieveAccountRepository();
    userRepository = MockUserRepository();
    connectUserRepository = MockConnectUserRepository();
    gtp = GlobalTrackingProperties(utmParameters: {});

    sut = RetrieveAccountIdentifyVerificationCubit(
      retrieveAccountRepository: retrieveAccountRepository,
      userRepository: userRepository,
      connectUserRepository: connectUserRepository,
      gtp: gtp,
    );
  });

  tearDown(() => sut.close());

  group('RetrieveAccountIdentifyVerificationCubit tests', () {
    blocTest<RetrieveAccountIdentifyVerificationCubit, RetrieveAccountIdentifyVerificationState>(
      'startBosIdentifyHandling emits with success',
      build: () {
        when(() => connectUserRepository.refreshToken(updateClaims: true)).thenAnswer((_) => Future.value(right(unit)));
        when(() => userRepository.userCuid()).thenAnswer((_) => Future.value('cuid'));
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        ).thenAnswer(
          (_) => Future.value(
            right(const RetrieveAccountIdentifyResponse(0)),
          ),
        );
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        ).thenAnswer(
          (_) => Future.value(
            right(
              const RetrieveAccountIdentifyResultResponse(
                bosResult: IdentifyBosResult.approved,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosIdentifyHandling(
        sessionId: sessionId,
        idCardResult: idCardresult,
        selfieId: 'selfieId',
      ),
      expect: () => <TypeMatcher>[const TypeMatcher<RetrieveAccountIdentifyVerificationApproved>()],
      verify: (_) {
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        );
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        );
      },
    );

    blocTest<RetrieveAccountIdentifyVerificationCubit, RetrieveAccountIdentifyVerificationState>(
      'startBosIdentifyHandling emits with canceled result',
      build: () {
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        ).thenAnswer(
          (_) => Future.value(
            right(const RetrieveAccountIdentifyResponse(0)),
          ),
        );
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        ).thenAnswer(
          (_) => Future.value(
            right(
              const RetrieveAccountIdentifyResultResponse(
                bosResult: IdentifyBosResult.canceled,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosIdentifyHandling(
        sessionId: sessionId,
        idCardResult: idCardresult,
        selfieId: 'selfieId',
      ),
      expect: () => <TypeMatcher>[const TypeMatcher<RetrieveAccountIdentifyVerificationCanceled>()],
      verify: (_) {
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        );
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        );
      },
    );

    blocTest<RetrieveAccountIdentifyVerificationCubit, RetrieveAccountIdentifyVerificationState>(
      'startBosIdentifyHandling emits with rejected result',
      build: () {
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        ).thenAnswer(
          (_) => Future.value(
            right(const RetrieveAccountIdentifyResponse(0)),
          ),
        );
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        ).thenAnswer(
          (_) => Future.value(
            right(
              const RetrieveAccountIdentifyResultResponse(
                bosResult: IdentifyBosResult.rejected,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosIdentifyHandling(
        sessionId: sessionId,
        idCardResult: idCardresult,
        selfieId: 'selfieId',
      ),
      expect: () => <TypeMatcher>[const TypeMatcher<RetrieveAccountIdentifyVerificationRejected>()],
      verify: (_) {
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        );
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        );
      },
    );

    blocTest<RetrieveAccountIdentifyVerificationCubit, RetrieveAccountIdentifyVerificationState>(
      'startBosIdentifyHandling emits with failed to finish result',
      build: () {
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        ).thenAnswer(
          (_) => Future.value(
            right(const RetrieveAccountIdentifyResponse(0)),
          ),
        );
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        ).thenAnswer(
          (_) => Future.value(
            right(
              const RetrieveAccountIdentifyResultResponse(
                bosResult: IdentifyBosResult.failedToFinish,
              ),
            ),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosIdentifyHandling(
        sessionId: sessionId,
        idCardResult: idCardresult,
        selfieId: 'selfieId',
      ),
      expect: () => <TypeMatcher>[const TypeMatcher<RetrieveAccountIdentifyVerificationError>()],
      verify: (_) {
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        );
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        );
      },
    );

    blocTest<RetrieveAccountIdentifyVerificationCubit, RetrieveAccountIdentifyVerificationState>(
      'startBosIdentifyHandling emits with ManualDeduplication',
      build: () {
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        ).thenAnswer(
          (_) => Future.value(
            right(const RetrieveAccountIdentifyResponse(0)),
          ),
        );
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        ).thenAnswer(
          (_) => Future.value(right(const RetrieveAccountIdentifyResultResponse())),
        );
        return sut;
      },
      act: (sut) => sut.startBosIdentifyHandling(
        sessionId: sessionId,
        idCardResult: idCardresult,
        selfieId: 'selfieId',
      ),
      expect: () => <TypeMatcher>[const TypeMatcher<RetrieveAccountIdentifyVerificationManualDeduplication>()],
      verify: (_) {
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        );
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        );
      },
    );

    blocTest<RetrieveAccountIdentifyVerificationCubit, RetrieveAccountIdentifyVerificationState>(
      'startBosIdentifyHandling emits with Verification error',
      build: () {
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        ).thenAnswer(
          (_) => Future.value(
            right(const RetrieveAccountIdentifyResponse(0)),
          ),
        );
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        ).thenAnswer(
          (_) => Future.value(
            left(Failure<UserVerificationFailureType, void>(type: UserVerificationFailureType.invalid)),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosIdentifyHandling(
        sessionId: sessionId,
        idCardResult: idCardresult,
        selfieId: 'selfieId',
      ),
      expect: () => <TypeMatcher>[const TypeMatcher<RetrieveAccountIdentifyVerificationError>()],
      verify: (_) {
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        );
        verify(
          () => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId),
        );
      },
    );

    blocTest<RetrieveAccountIdentifyVerificationCubit, RetrieveAccountIdentifyVerificationState>(
      'startBosIdentifyHandling(), initialWaitTime > 0, retrieveAccountIdentifyResult never called',
      build: () {
        when(
          () => retrieveAccountRepository.retrieveAccountIdentifyPost(
            sessionId: sessionId,
            retrieveAccountIdentifyRequest: identifyRequest,
          ),
        ).thenAnswer(
          (_) => Future.value(
            right(const RetrieveAccountIdentifyResponse(1)),
          ),
        );
        return sut;
      },
      act: (sut) => sut.startBosIdentifyHandling(
        idCardResult: idCardresult,
        selfieId: 'selfieId',
        sessionId: sessionId,
      ),
      expect: () => <dynamic>[],
      verify: (_) => verifyNever(() => retrieveAccountRepository.retrieveAccountIdentifyResult(sessionId)),
    );
  });
}
