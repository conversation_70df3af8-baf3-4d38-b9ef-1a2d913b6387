name: capp_auth
owner: Acq_C
version: 0.0.1
publish_to: none
description: VN Login/Registration process
environment:
  sdk: ">=3.2.6 <4.0.0"
dependencies:
  get_it: ^7.3.0
  bloc: any
  dartz: ^0.10.0-nullsafety.2
  freezed_annotation: ^2.2.0
  rxdart: ^0.27.3
  flutter_bloc: ^8.1.3
  logger: ^1.3.0
  dio: ^4.0.6
  flutter_libphonenumber:
    path: ../../../forks/flutter_libphonenumber
  flutter_svg: ^2.1.0
  biometric_signature: 5.1.3
  uuid: ^4.0.0
  equatable: ^2.0.3
  flutter:
    sdk: flutter
  koyal_shared:
    path: ../../koyal_shared/koyal_shared
  gma_storage:
    path: ../../gma_storage
  capp_content_core:
    path: ../../capp_content/capp_content_core
  capp_auth_core:
    path: ../capp_auth_core
  capp_bio_signature:
    path: ../../capp_bio_signature
  capp_feature_flags:
    path: ../../capp_feature_flags
  capp_innovatrics_core:
    path: ../../capp_innovatrics/capp_innovatrics_core
  capp_ui_core:
    path: ../../capp_ui/capp_ui_core
  capp_ui:
    path: ../../capp_ui/capp_ui_vn
  koyal_navigation_annotation:
    path: ../../../plugins/koyal_navigation_annotation
  koyal_otp:
    path: ../../koyal_otp
  gma_platform:
    path: ../../gma_platform
  capp_tracking:
    path: ../../capp_tracking
  koyal_auth:
    path: ../../koyal_auth
  koyal_core:
    path: ../../koyal_core
  koyal_lock:
    path: ../../koyal_lock
  koyal_shared_core:
    path: ../../koyal_shared/koyal_shared_core
  navigator:
    path: ../../navigator

  identityapi:
    git:
      url: https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients
      ref: ff6540d40cf259de0a4eb5b3a767192f4106277d
      path: public/identityapi
  selfcareapi:
    git:
      url: https://<EMAIL>/hci-iap/koyal/_git/dart-api-clients
      path: public/selfcareapi
      ref: 37954c3482f43d8e6544a342be7f3a84980026c4

dev_dependencies:
  bloc_test: ^9.1.6
  build_runner: ^2.4.11
  freezed: ^2.3.2
  test: ^1.17.12
  mocktail: any
  koyal_navigation_generator:
    path: ../../../plugins/koyal_navigation_generator
  gma_lints:
    path: ../../gma_lints
  flutter_test:
    sdk: flutter
