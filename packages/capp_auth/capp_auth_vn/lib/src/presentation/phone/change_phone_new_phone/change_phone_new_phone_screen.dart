import 'package:capp_auth_core/capp_auth_core.dart' as capp_auth_core;
import 'package:flutter/material.dart';

class ChangePhoneNewPhoneScreen extends capp_auth_core.ChangePhoneNewPhoneScreen {
  ChangePhoneNewPhoneScreen({
    Key? key,
    required capp_auth_core.ChangePhoneNewPhoneScreenArguments arguments,
    Widget? customOtpFooter,
  }) : super(
          key: key,
          arguments: arguments,
          customOtpFooter: customOtpFooter,
          isTemporarVnDialog: true,
        );
}
