// ignore_for_file: deprecated_member_use

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';

import '../../../application/phone/forgot_username_phone/forgot_username_phone_bloc.dart';
import '../../second_id_verification/forgot_username_second_id/forgot_username_2nd_id_screen_arguments.dart';
import 'phone_not_found_dialog.dart';

class ForgotUsernamePhoneScreen extends StatelessScreen with RouteWrapper, PhoneHintMixin {
  ForgotUsernamePhoneScreen({
    Key? key,
  }) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (context) => context.get<ForgotUsernamePhoneBloc>()..add(const ForgotUsernamePhoneEvent.init()),
        child: this,
      );

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ForgotUsernamePhoneBloc, ForgotUsernamePhoneState>(
          listenWhen: (previous, current) =>
              previous.showPhoneHintDialog != current.showPhoneHintDialog && current.showPhoneHintDialog,
          listener: (context, state) async {
            final bloc = context.read<ForgotUsernamePhoneBloc>();
            final hint = await getHint(context);
            bloc.add(ForgotUsernamePhoneEvent.setValue(value: hint?.phoneHint ?? ''));
          },
        ),
        BlocListener<ForgotUsernamePhoneBloc, ForgotUsernamePhoneState>(
          listenWhen: (previous, current) => previous.otpVerificationResponse != current.otpVerificationResponse,
          listener: (context, state) {
            state.otpVerificationResponse.fold(() => null, (either) {
              either.fold((l) {
                l.maybeWhen(
                  functionMap: {
                    UserVerificationFailureType.sessionNotFoundOrExpired: (_) => showPhoneNotFoundDialog(context),
                    UserVerificationFailureType.insiderUserUsedForPublicAccount: (_) =>
                        showInsiderUserUsedForPublicAccountDialog(context),
                  },
                  orElse: () => showOtpNotAcceptedDialog(context),
                );
              }, (r) {
                context.navigator.push(
                  path: NavigatorPath.cappAuth.forgotUsername2ndIdScreen,
                  arguments: ForgotUsername2ndIdScreenArguments(
                    phoneNumber: state.phoneNumber.value,
                    otpToken: state.otpToken!,
                    allowedSecondIds: r ?? [],
                  ),
                );
              });
            });
          },
        ),
      ],
      child: KoyalWillPopScope(
        onWillPop: () async {
          context.get<CappAuthTrackingService>().trackForgotUsernameEnterPhoneNumberOnGobackClick();
          return true;
        },
        child: BlocBuilder<ForgotUsernamePhoneBloc, ForgotUsernamePhoneState>(
          builder: (context, state) => KoyalScaffold(
            key: const Key('__forgotUsernamePhoneScreen__'),
            appBar: KoyalAppBar(),
            body: FixedBottomContentScreen(
              upperContent: [
                KoyalPadding.normalAll(
                  left: false,
                  right: false,
                  top: false,
                  child: MainHeading(
                    centerAlign: false,
                    title: L10nCappAuth.of(context).forgotUsernamePhoneHeadingTitle,
                    subtitle: L10nCappAuth.of(context).forgotUsernamePhoneHeadingSubtitle,
                  ),
                ),
                KoyalPadding.normalHorizontal(
                  child: CappPhoneTextField(
                    key: const Key('__phoneNumberField__'),
                    country: context.get<PhoneNumberConfig>().country,
                    value: state.phoneNumber.value,
                    onChanged: (value) {
                      context.read<ForgotUsernamePhoneBloc>().add(ForgotUsernamePhoneEvent.setValue(value: value));
                    },
                    phonePrefix: context.get<PhoneNumberConfig>().phonePrefix,
                    hintText: L10nCappAuth.of(context).primaryPhoneNumber,
                    phoneNumberFormat: context.get<PhoneNumberConfig>().useInternationalFormat
                        ? PhoneNumberFormat.international
                        : PhoneNumberFormat.national,
                    toolbarOptions: const ToolbarOptions(
                      paste: true,
                      selectAll: true,
                    ),
                  ),
                ),
              ],
              fixedBottomContent: [
                HorizontalButtonsLayout(
                  primaryButton: PrimaryButton(
                    key: const Key('__forgotUsernamePhoneContinueButton__'),
                    text: L10nCappAuth.of(context).continueString,
                    isInProgress: state.state == LoadingState.isLoading,
                    onPressed: state.phoneNumber.isValid(context.get<PhoneNumberConfig>().phoneRegex)
                        ? () {
                            context.get<CappAuthTrackingService>().trackForgotUsernameEnterPhoneNumberOnContinueClick();
                            context.navigator
                                .push<String>(
                              path: NavigatorPath.koyalOtp.otpScreen,
                              arguments: OtpScreenArguments(
                                requestType: OtpRequestType.forgotUsernameCapp,
                                entityToVerify: state.phoneNumber.value,
                                customTertiaryButton: ChangePhoneButton(
                                  eventCategory: OtpRequestType.forgotUsernameCapp.getBusinessRoute(),
                                ),
                              ),
                            )
                                .then((token) {
                              if (token != null) {
                                if (!context.mounted) return;
                                context.read<ForgotUsernamePhoneBloc>().add(
                                      ForgotUsernamePhoneEvent.otpCompleted(
                                        otpToken: token,
                                      ),
                                    );
                              }
                            });
                          }
                        : null,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
