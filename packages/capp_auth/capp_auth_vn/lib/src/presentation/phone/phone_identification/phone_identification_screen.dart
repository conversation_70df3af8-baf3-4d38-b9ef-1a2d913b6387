import 'package:capp_auth_core/capp_auth_core.dart' as ca_core;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class PhoneIdentificationScreen extends ca_core.PhoneIdentificationScreen {
  PhoneIdentificationScreen({
    Key? key,
    required ca_core.AuthLandingScreenArguments? arguments,
  }) : super(
          key: key,
          arguments: arguments,
          registrationLayout: true,
          widgetLogoBuilder: SvgPicture.asset(
            'assets/svg/auth_logo_without_cupo.svg',
            package: 'capp_auth_core',
            width: double.infinity,
          ),
        );
}
