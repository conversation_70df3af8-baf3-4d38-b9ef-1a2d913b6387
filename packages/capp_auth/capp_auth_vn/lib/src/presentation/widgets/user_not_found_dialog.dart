import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';

Future showUserNotFoundDialog(BuildContext context) async {
  return showKoyalOverlay<void>(
    context,
    body: KoyalText.body1(
      L10nCappAuth.of(context).userNotFoundDialogTitle,
      color: ColorTheme.of(context).defaultTextColor,
      textAlign: TextAlign.center,
    ),
    primaryButtonBuilder: (context) => PrimaryButton(
      text: L10nCappAuth.of(context).iWantToRegister,
      onPressed: () => context.authNavigator.toAuthLandingReplace(context),
    ),
  );
}
