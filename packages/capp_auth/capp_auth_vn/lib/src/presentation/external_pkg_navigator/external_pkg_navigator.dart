// ignore_for_file: depend_on_referenced_packages

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_content_core/capp_content_core.dart';
import 'package:capp_domain/capp_domain.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:navigator/navigator.dart';

class ExternalPkgNavigator implements IExternalPkgNavigator {
  Future Function(BuildContext)? authCompleteCallback;
  Future Function(BuildContext)? handleContinueAsGuestBeforeAuthCompleteCallback;

  @override
  Future onContactUsFromDialog(BuildContext context, {bool replaceCurrentScreen = true}) {
    final navigationMethod =
        replaceCurrentScreen ? context.navigator.pushReplacementFromPackage : context.navigator.pushFromPackage;

    return context.isFlagEnabledRead(FeatureFlag.dynamicContactUsPage)
        ? navigationMethod(package: 'CappContent', screen: 'ContactUsScreen')
        : navigationMethod(package: 'CappContent', screen: 'ObsoleteContactUsScreen');
  }

  @override
  Future onPrivacyPolicy(BuildContext context) => context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'FixedDocumentScreen',
        arguments: FixedDocumentScreenArguments(type: FixedDocumentType.privacyPolicy, closeButton: true),
      );

  @override
  Future onTermsOfUse(BuildContext context) => context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'FixedDocumentScreen',
        arguments: FixedDocumentScreenArguments(type: FixedDocumentType.termsOfUse, closeButton: true),
      );

  @override
  Future onDataConsent(BuildContext context) => context.navigator.pushFromPackage(
        package: 'CappContent',
        screen: 'FixedDocumentScreen',
        arguments: FixedDocumentScreenArguments(type: FixedDocumentType.dataConsent, closeButton: true),
      );

  @override
  Future onSupportiveProcessFinished(BuildContext context) {
    context.read<LockStatusBloc>().add(const LockStatusEvent.setLock(locked: false));
    context.read<LockStatusBloc>().add(const LockStatusEvent.setEnabled(enabled: true));
    return context.navigator.toMainScreen();
  }

  @override
  Future onAuthComplete(
    BuildContext context, {
    String? authSessionId,
    String? password,
    KoyalNavigatorTransition? transition,
  }) {
    context.get<IFirebasePerformanceMonitoring>()
      ..stopTrace(TraceType.login)
      ..startTrace(TraceType.homepageLoading);
    context.read<LockStatusBloc>().add(const LockStatusEvent.setLock(locked: false));
    context.read<LockStatusBloc>().add(const LockStatusEvent.setEnabled(enabled: true));
    context.get<IUserRepository>().updateUserLoginStatus(isUserLoggedIn: true);
    final result = authCompleteCallback?.call(context) ??
        context.navigator.toMainScreen(
          arguments: MainScreenArguments(
            initialTab: TabItem.home,
            checkForMaintenance: true,
            arguments: LegalPermissionCheckArguments(isAfterLogin: true),
          ),
          transition: transition,
        );
    context.get<IAuthCompleteRepository>().setAuthComplete(isComplete: true);
    return result;
  }

  @override
  Future onHandleContinueAsGuestBeforeAuthComplete(BuildContext context) {
    return handleContinueAsGuestBeforeAuthCompleteCallback?.call(context) ?? Future<void>.value();
  }

  @override
  Future onDuplicateAccount(BuildContext context) => context.navigator.pushReplacement(
        path: NavigatorPath.cappAuth.logoutScreen,
        arguments: LogoutScreenArguments(logoutReason: LogoutReason.duplicateAccount),
      );
}
