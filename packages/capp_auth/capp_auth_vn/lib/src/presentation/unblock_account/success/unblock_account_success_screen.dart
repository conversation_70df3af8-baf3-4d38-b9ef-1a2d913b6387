import 'package:capp_auth_core/capp_auth_core.dart' as capp_auth_core;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class UnblockAccountSuccessScreen extends capp_auth_core.UnblockAccountSuccessScreen {
  UnblockAccountSuccessScreen({Key? key, capp_auth_core.UnblockAccountSuccessScreenArguments? arguments})
      : super(
          key: key,
          arguments: arguments,
          widgetLogoBuilder: SvgPicture.asset(
            'assets/svg/unblock_account_success.svg',
            package: 'capp_auth_core',
          ),
        );
}
