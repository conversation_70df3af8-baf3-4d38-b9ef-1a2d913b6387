import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

class CrossroadsScreen extends StatelessWidget {
  const CrossroadsScreen({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final mq = MediaQuery.of(context);
    final viewPadding = mq.viewPadding;

    final screenHeight = mq.size.height;
    final viewPortVerticalPadding = viewPadding.top + viewPadding.bottom;
    return KoyalScaffold(
      key: const Key('__crossroadsVnScaffold__'),
      body: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: screenHeight - viewPortVerticalPadding,
          ),
          child: Column(
            children: [
              const SizedBox(height: 56), // 64 padding above image, 56 for appbar size (appbar is not present)
              const HomeCreditLogoSmall(),
              Expanded(
                child: SvgPicture.asset(
                  'assets/svg/auth_logo_without_cupo.svg',
                  package: 'capp_auth_core',
                  width: double.infinity,
                ),
              ),
              const Heading(),

              KoyalPadding.large(
                child: VerticalButtonsLayout(
                  primaryButton: PrimaryButton(
                    key: const Key('__createAccountButton__'),
                    text: L10nCappAuth.of(context).authCreateAccountButton,
                    onPressed: () => context.navigator.push(path: NavigatorPath.cappAuth.phoneIdentificationScreen),
                  ),
                  secondaryButton: SecondaryButton(
                    key: const Key('__loginButton__'),
                    text: L10nCappAuth.of(context).login,
                    onPressed: () => context.navigator.pushNamedAndRemoveUntil(
                      NavigatorPath.cappAuth.authLandingScreen,
                      NavigatorPath.cappAuth.authLandingScreen,
                    ),
                  ),
                  tertiaryButton: TertiaryButton(
                    key: const Key('__continueAsGuestButton__'),
                    text: L10nCappAuth.of(context).continueAsGuestButtonText,
                    onPressed: () {
                      context.externalNavigator.onHandleContinueAsGuestBeforeAuthComplete(context);
                      context.externalNavigator.onAuthComplete(context);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
