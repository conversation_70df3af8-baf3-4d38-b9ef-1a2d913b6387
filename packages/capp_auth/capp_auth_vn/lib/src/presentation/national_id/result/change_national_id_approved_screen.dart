import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

import 'change_national_id_approved_screen_args.dart';

class ChangeNationalIdApprovedScreen extends IdentifyResultApprovedScreen {
  const ChangeNationalIdApprovedScreen({
    super.key = const Key('__ChangeNationalIdApprovedScreen__'),
    required ChangeNationalIdApprovedScreenArgs arguments,
  }) : super(arguments: arguments);

  @override
  String getTitle(BuildContext context, IdentifyFlowType? identifyFlowType) =>
      L10nCappAuth.of(context).changeNationalIdApprovedTitle;

  @override
  String? getSubtitle(BuildContext context, IdentifyFlowType? identifyFlowType) => null;

  @override
  String? getPrimaryButtonText(BuildContext context, IdentifyFlowType? identifyFlowType) =>
      L10nCappAuth.of(context).changeNationalIdApprovedContinue;

  @override
  void onContinueButtonClicked(BuildContext context) {
    if ((args as ChangeNationalIdApprovedScreenArgs).fromPersonalDetails) {
      context.navigator.popUntilFromPackage('CappPersonal', 'PersonalDetailsScreenV2');
    } else {
      context.navigator.toMainScreen();
    }
  }
}
