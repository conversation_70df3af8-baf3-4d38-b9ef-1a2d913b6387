import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../../../capp_auth.dart';

class ChangeNationalIdFailedScreen extends IdentifyResultFailedToFinishScreen {
  @override
  const ChangeNationalIdFailedScreen({
    super.key = const Key('__ChangeNationalIdFailedScreen__'),
    required ChangeNationalIdFailedScreenArgs arguments,
  }) : super(arguments: arguments);

  String get businessRouteName =>
      '${args.identifyFlowType.getBusinessRoute()}_${args.identifyBosResult.getBusinessRoute()}';

  @override
  String getTitle(BuildContext context) => L10nCappAuth.of(context).changeNationalIdFailedTitle;

  @override
  String? getSubtitle(BuildContext context) => L10nCappAuth.of(context).changeNationalIdFailedSubtitle;

  @override
  String? getPrimaryButtonText(BuildContext context) => L10nCappAuth.of(context).changeNationalIdFailedTryAgain;

  @override
  void onPrimaryButtonClicked(BuildContext context) =>
      context.navigator.popUntilTyped(CappAuth, ChangeNationalIdScreen);

  @override
  String? getTertiaryButtonText(BuildContext context) => L10nCappAuth.of(context).changeNationalIdFailedLater;

  @override
  void onTertiaryButtonClicked(BuildContext context) {
    if ((args as ChangeNationalIdFailedScreenArgs).fromPersonalDetails) {
      context.navigator.popUntilFromPackage('CappPersonal', 'PersonalDetailsScreenV2');
    } else {
      context.navigator.toMainScreen();
    }
  }

  @override
  void trackOnPrimaryButtonClicked(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangeNationalIdFailedScreenRetryButtonClick(businessRouteName);
  }

  @override
  void trackOnTertiaryButtonClicked(BuildContext context) {
    context.get<CappAuthTrackingService>().trackChangeNationalIdFailedScreenLaterButtonClick(businessRouteName);
  }

  @override
  Widget getImage() => const AssetSvgImage(
        'assets/svg/identify_error_without_cupo.svg',
        package: 'capp_auth_core',
      );
}
