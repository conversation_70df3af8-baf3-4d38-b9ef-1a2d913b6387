import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_lock/koyal_lock.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class LogoutScreen extends LogoutIndicationScreen {
  LogoutScreen({Key? key, required LogoutScreenArguments arguments})
      : super(
          key: key,
          userDisabling: arguments.logoutReason == LogoutReason.userDisabling,
          postFrameCallback: (context) {
            context.get<ApiLocker>().logoutInProgress = true;
            context.get<AuthBloc>().add(AuthEvent.logout(logoutReason: arguments.logoutReason));
            context.get<RegistrationLegalLogData>().clear();
            context.get<IBiometricProvider>().disableBiometric();
            context.get<LockStatusBloc>().add(const LockStatusEvent.setLock(locked: false));
            context.get<ILockNavigator>().setLocked(isLocked: false);
          },
          widgetLogoBuilder: const VerificationInProcess(),
        );
}
