// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared/koyal_shared.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:navigator/navigator.dart';

import '../../application/auth_landing/auth_landing_bloc.dart';
import '../username/username_input.dart';
import '../widgets/auth_help.dart';

class AuthLandingScreen extends StatefulScreen with RouteWrapper {
  final AuthLandingScreenArguments? args;
  final bool standaloneMode;

  AuthLandingScreen({
    Key? key,
    AuthLandingScreenArguments? arguments,
    this.standaloneMode = false,
  })  : args = arguments,
        super(key: key, arguments: arguments);

  @override
  Widget wrappedRoute(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => context.get<UserRefresherCubit>(),
        ),
        BlocProvider(
          create: (ctx) => context.get<AuthErrorHandlerCubit>(),
        ),
        BlocProvider(
          create: (context) {
            final bloc = context.get<AuthLandingBloc>();

            if (args?.logoutReason == null) {
              return bloc..add(AuthLandingEvent.init(prefilledUsername: args?.phoneNumber ?? ''));
            } else {
              Future.delayed(Duration.zero, () {
                switch (args!.logoutReason!) {
                  case LogoutReason.signedInOnOtherDevice:
                    showLogoutAnotherDeviceDialog(context).then((_) {
                      bloc.add(const AuthLandingEvent.init());
                    });
                    break;
                  case LogoutReason.inactivityThresholdExceeded:
                    showAfterLogoutDialog(context).then((_) {
                      bloc.add(const AuthLandingEvent.init());
                    });
                    break;
                  case LogoutReason.duplicateAccount:
                    showDuplicateAccountDialog(context).then((_) {
                      bloc.add(const AuthLandingEvent.init());
                    });
                    break;
                  case LogoutReason.unknown:
                  case LogoutReason.userLogout:
                  case LogoutReason.tokenExpired:
                  case LogoutReason.signIn:
                  case LogoutReason.logoutBecauseOfSaRa:
                  case LogoutReason.userDisabling:
                  case LogoutReason.guestLoginWhenLoggedIn:
                  case LogoutReason.changeIdCardRejected:
                  case LogoutReason.playIntegrityError:
                  case LogoutReason.deviceLimitReached:
                    // no action attm, just init the bloc
                    bloc.add(const AuthLandingEvent.init());
                    break;
                  case LogoutReason.phoneUpdated:
                    showPhoneUpdatedDialog(context).then((_) {
                      bloc.add(const AuthLandingEvent.init());
                    });
                    break;
                  case LogoutReason.signOutAccountDeleted:
                    showAccountDeleteDialog(context).then((_) {
                      bloc.add(const AuthLandingEvent.init());
                    });
                    break;
                }
              });
            }

            return bloc;
          },
        ),
        BlocProvider(create: (context) => context.get<LoginCubit>()),
      ],
      child: this,
    );
  }

  @override
  AuthLandingScreenState createState() => AuthLandingScreenState();
}

class AuthLandingScreenState extends State<AuthLandingScreen> {
  late FocusNode passwordInputFocus;

  @override
  void initState() {
    super.initState();
    passwordInputFocus = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    final viewPadding = MediaQuery.of(context).viewPadding;
    final viewPortVerticalPadding = viewPadding.top + viewPadding.bottom;

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenHeight = constraints.maxHeight;
        return MultiBlocListener(
          listeners: [
            AuthErrorListener(),
            BlocListener<AuthLandingBloc, AuthLandingState>(
              listenWhen: (previous, current) => previous.state != current.state,
              listener: (context, state) {
                if (state.state == LoadingState.isCompleted) {
                  state.failureOrSuccess.fold(() {}, (a) {
                    a.fold((l) {
                      context.get<IFirebasePerformanceMonitoring>().stopTrace(TraceType.login);
                      l.maybeMap(
                        reachedMaximumAccountAllowed: (_) => showMaximumAccountAllowedDialog(context),
                        orElse: () => showAuthErrorUnexpectedErrorDialog(context),
                        unauthorized: (response) => context.read<AuthErrorHandlerCubit>().setErrorResponse(
                              AuthErrorResponseWrapper(
                                authErrorResponse: response.authErrorResponse,
                                prefilledInfo: PrefilledInfo(phoneNumber: state.username),
                              ),
                            ),
                        verificationRequired: (response) async {
                          unawaited(
                            context.navigator
                                .push<String>(
                              path: NavigatorPath.koyalOtp.otpScreen,
                              arguments: OtpScreenArguments(
                                requestType: OtpRequestType.loginCapp,
                                entityToVerify: response.authErrorResponse.otpSessionId ??
                                    '', // it should not be null, but just in case
                                recipientType: OtpRecipientType.sessionId,
                                phoneNumber: response.authErrorResponse.phoneNumber,
                                customTertiaryButton: ChangePhoneButton(
                                  eventCategory: OtpRequestType.loginCapp.getBusinessRoute(),
                                ),
                              ),
                            )
                                .then((token) {
                              if (token != null) {
                                context.read<AuthLandingBloc>().add(AuthLandingEvent.submit(otpToken: token));
                              }
                            }),
                          );
                        },
                      );
                    }, (r) {
                      context.externalNavigator.onAuthComplete(
                        context,
                        authSessionId: state.username,
                        password: state.password,
                      );
                    });
                  });
                }
              },
            ),
            BlocListener<AuthLandingBloc, AuthLandingState>(
              listenWhen: (previous, current) => previous.username != current.username,
              listener: (context, state) =>
                  context.read<AuthErrorHandlerCubit>().setErrorResponse(AuthErrorResponseWrapper()),
            ),
          ],
          child: BlocBuilder<AuthLandingBloc, AuthLandingState>(
            builder: (context, state) => KoyalScaffold(
              key: const Key('__authLandingVnScaffold__'),
              body: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: screenHeight - viewPortVerticalPadding,
                  ),
                  child: Column(
                    children: [
                      AppBar(
                        backgroundColor: ColorTheme.of(context).backgroundColor,
                        leading: (widget.args?.navigateHomeOnBackPressed ?? false)
                            ? KoyalIconButton(
                                iconData: KoyalIcons.arrow_back_outline,
                                onPressed: () => context.externalNavigator.onSupportiveProcessFinished(context),
                              )
                            : null,
                      ),
                      const HomeCreditLogoSmall(),
                      Expanded(
                        child: KoyalPadding.small(
                          left: false,
                          right: false,
                          bottom: false,
                          child: ConstrainedBox(
                            constraints: const BoxConstraints(maxWidth: 350),
                            child: UserRefresherBuilder(
                              child: SvgPicture.asset(
                                'assets/svg/auth_logo_without_cupo.svg',
                                package: 'capp_auth_core',
                                width: double.infinity,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const Heading(),
                      UsernameInput(
                        key: const Key('__authUsernameInput__'),
                        isEnabled: state.state != LoadingState.isLoading,
                        initValue: state.username,
                        onChanged: (newUsername, isValid) {
                          context.read<AuthLandingBloc>().add(
                                AuthLandingEvent.usernameChanged(
                                  username: newUsername,
                                  isValid: isValid,
                                ),
                              );
                        },
                        onEditingComplete: (value) {
                          FocusScope.of(context).requestFocus(passwordInputFocus);
                        },
                        scrollPadding: const EdgeInsets.only(bottom: 160),
                      ),
                      KoyalPadding.xSmallVertical(
                        child: CappSecureInputText(
                          key: const Key('__authPasswordInput__'),
                          focusNode: passwordInputFocus,
                          initialValue: state.password,
                          labelText: L10nCappAuth.of(context).password,
                          enabled: state.state != LoadingState.isLoading,
                          onChanged: (newPassword) =>
                              context.read<AuthLandingBloc>().add(AuthLandingEvent.passwordChanged(newPassword)),
                          scrollPadding: const EdgeInsets.only(bottom: 90),
                          onFieldSubmitted: (_) {
                            if (state.usernameValid && state.password.isNotEmpty) {
                              context.get<CappAuthTrackingService>().trackAccountLoginScreenOnLoginClick();
                              context.get<IFirebasePerformanceMonitoring>().startTrace(TraceType.login);
                              context.read<AuthLandingBloc>().add(const AuthLandingEvent.submit());
                            }
                          },
                        ),
                      ),
                      KoyalPadding.normalAll(
                        child: state.timetoBlockAccount == null
                            ? _LoginButton(state: state)
                            : CountdownFormatted(
                                key: const Key('__countdownLock__'),
                                duration: state.timetoBlockAccount!,
                                builder: (ctx, remaining, isFinished) {
                                  //ignore: buttons-layout
                                  return PrimaryButton(
                                    text: L10nCappAuth.of(context).unblockAccount,
                                    onPressed: () {
                                      context.navigator.push(
                                        path: NavigatorPath.cappAuth.unblockAccountEntryScreen,
                                        arguments: UnblockAccountPhoneScreenArguments(
                                          prefilledInfo: PrefilledInfo(phoneNumber: state.username),
                                        ),
                                      );
                                    },
                                  );
                                },
                                onFinish: () => context.read<AuthLandingBloc>().add(const AuthLandingEvent.refresh()),
                              ),
                      ),
                      Wrap(
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: [
                          if (!widget.standaloneMode) ...[
                            BlocBuilder<LoginCubit, LoginState>(
                              builder: (context, loginState) {
                                return TertiaryButton(
                                  key: const Key('__continueAsGuestButton__'),
                                  text: L10nCappAuth.of(context).continueAsGuestButtonText,
                                  isInProgress: loginState is LoginLoading,
                                  onPressed: loginState is LoginLoading ||
                                          state.state == LoadingState.isLoading ||
                                          state.username.isNotEmpty
                                      ? null
                                      : () async {
                                          context
                                              .get<CappAuthTrackingService>()
                                              .trackAccountLoginScreenOnContinueAsGuestClick();
                                          if (await context.get<LoginCubit>().isUserLoggedIn()) {
                                            await context.navigateToLogoutScreen(LogoutReason.guestLoginWhenLoggedIn);
                                          } else {
                                            await context.externalNavigator
                                                .onHandleContinueAsGuestBeforeAuthComplete(context);

                                            if (context.navigator.canPop() ?? false) {
                                              context.navigator.pop();
                                            } else {
                                              await context.externalNavigator.onAuthComplete(context);
                                            }
                                          }
                                        },
                                );
                              },
                            ),
                            Container(
                              height: 16,
                              width: 1,
                              color: ColorTheme.of(context).foreground30Color,
                            ),
                          ],
                          TertiaryButton(
                            text: L10nCappAuth.of(context).authCreateAccountButton,
                            key: const Key('__createAccountButton__'),
                            onPressed: state.state == LoadingState.isLoading
                                ? null
                                : () {
                                    context
                                        .get<CappAuthTrackingService>()
                                        .trackAccountLoginScreenOnCreateAccountClick();
                                    context.navigator.push(path: NavigatorPath.cappAuth.phoneIdentificationScreen);
                                  },
                          ),
                        ],
                      ),
                      InkWell(
                        key: const Key('__forgotUsernameOrPassword__'),
                        onTap: state.state == LoadingState.isLoading
                            ? null
                            : () {
                                context
                                    .get<CappAuthTrackingService>()
                                    .trackAccountLoginScreenOnForgotUserPasswordClick();
                                showAuthHelp(
                                  context: context,
                                  prefilledInfo: PrefilledInfo(phoneNumber: state.username),
                                );
                              },
                        child: KoyalPadding.normalAll(
                          child: KoyalText.body2(
                            L10nCappAuth.of(context).forgotUsernameOrPassword,
                            color: ColorTheme.of(context).informationTextColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _LoginButton extends StatelessWidget {
  final AuthLandingState state;
  const _LoginButton({
    Key? key,
    required this.state,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    //ignore: buttons-layout
    return PrimaryButton(
      key: const Key('__loginButton__'),
      text: L10nCappAuth.of(context).login,
      isInProgress: state.state == LoadingState.isLoading,
      onPressed: state.usernameValid && state.password.isNotEmpty
          ? () {
              context.get<CappAuthTrackingService>().trackAccountLoginScreenOnLoginClick();
              context.get<IFirebasePerformanceMonitoring>().startTrace(TraceType.login);
              context.read<AuthLandingBloc>().add(const AuthLandingEvent.submit());
            }
          : null,
    );
  }
}

class Heading extends StatelessWidget {
  const Heading({super.key});

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.largeHorizontal(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          KoyalPadding.xSmall(
            top: false,
            left: false,
            right: false,
            child: KoyalText.header4(
              L10nCappAuth.of(context).authWelcome,
              textAlign: TextAlign.center,
            ),
          ),
          KoyalPadding.normalAll(
            top: false,
            left: false,
            right: false,
            child: KoyalText.body2(
              L10nCappAuth.of(context).enterPhoneToLogin,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
