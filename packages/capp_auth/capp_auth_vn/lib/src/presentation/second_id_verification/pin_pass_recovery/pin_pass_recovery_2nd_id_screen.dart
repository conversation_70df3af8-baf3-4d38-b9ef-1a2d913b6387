import 'package:capp_auth_core/capp_auth_core.dart' as capp_auth_core;
import 'package:flutter/material.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class PinPassRecovery2ndIdScreen extends capp_auth_core.PinPassRecovery2ndIdScreen {
  PinPassRecovery2ndIdScreen({
    Key? key,
    required capp_auth_core.SecondIdVerificationScreenArguments arguments,
  }) : super(
          key: key,
          arguments: arguments,
          initRegistrationDateBirth: DateTime.now().reduceOrAddYears(yearsNumber: 21, isAdd: false), //21 years
        );
}
