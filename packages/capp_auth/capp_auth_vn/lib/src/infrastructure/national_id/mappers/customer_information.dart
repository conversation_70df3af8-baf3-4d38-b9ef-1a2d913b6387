import 'package:koyal_auth/koyal_auth.dart' show IdentifyCustomerInformation;
import 'package:selfcareapi/model/models.dart' as api_models;

import 'code_value.dart';
import 'customer_address.dart';

extension CustomerInformationMapper on api_models.CustomerInformation {
  static api_models.CustomerInformation fromDomain(
    IdentifyCustomerInformation? item,
  ) =>
      api_models.CustomerInformation(
        documentName: item?.documentName,
        documentType: item?.documentType,
        idNumber: item?.idNumber,
        fullName: item?.fullName,
        suffix: item?.suffix,
        gender: item?.gender,
        mothersMaidenName: item?.mothersMaidenName,
        email: item?.email,
        dateOfBirth: item?.birthDate,
        firstAddress: CustomerAddressMapper.fromDomain(item?.firstAddress),
        secondAddress: CustomerAddressMapper.fromDomain(item?.secondAddress),
        placeOfBirth: CodeValueMapper.fromDomain(item?.placeOfBirth),
        nfcReferenceKey: item?.nfcReferenceKey,
      );
}
