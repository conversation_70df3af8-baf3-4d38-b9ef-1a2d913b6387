import 'package:koyal_auth/koyal_auth.dart' show IdentifyCustomerAddress;
import 'package:selfcareapi/model/models.dart' as api_models;

import 'code_value.dart';

extension CustomerAddressMapper on api_models.CustomerAddress {
  static api_models.CustomerAddress fromDomain(
    IdentifyCustomerAddress? item,
  ) =>
      api_models.CustomerAddress(
        rt: item?.rt,
        rw: item?.rw,
        landmark: item?.landmark,
        houseNumber: item?.houseNumber,
        streetName: item?.streetName,
        ward: CodeValueMapper.fromDomain(item?.ward),
        district: CodeValueMapper.fromDomain(item?.district),
        region: CodeValueMapper.fromDomain(item?.region),
        areaCode: CodeValueMapper.fromDomain(item?.areaCode),
      );
}
