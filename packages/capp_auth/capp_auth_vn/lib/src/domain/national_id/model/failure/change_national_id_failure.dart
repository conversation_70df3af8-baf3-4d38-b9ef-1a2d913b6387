import 'package:freezed_annotation/freezed_annotation.dart';

import '../rate_limit_error_response.dart';

part 'change_national_id_failure.freezed.dart';

@Freezed(fromJson: false, toJson: false, copyWith: false, equal: false, toStringOverride: false)
class ChangeNationalIdFailure with _$ChangeNationalIdFailure {
  const factory ChangeNationalIdFailure.forbidden() = _Forbidden;
  const factory ChangeNationalIdFailure.sessionNotFound() = _SessionNotFound;
  const factory ChangeNationalIdFailure.tooManyRequests(RateLimitErrorResponse? response) = _TooManyrequests;
  const factory ChangeNationalIdFailure.unexpected() = _Unexpected;
}
