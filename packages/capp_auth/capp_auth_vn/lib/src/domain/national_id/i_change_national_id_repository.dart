import 'package:dartz/dartz.dart';

import '../../../capp_auth.dart';

abstract class IChangeNationalIdRepository {
  Future<Either<ChangeNationalIdFailure, StartChangeNationalIdResponse>> changeNationalIdStart();
  Future<Either<ChangeNationalIdFailure, ChangeIdCardResponse>> uploadNewIdCard({
    required String sessionId,
    required ChangeIdCardRequest request,
  });
  Future<Either<ChangeNationalIdFailure, IdCardResultResponse>> getResult(String sessionId);
}
