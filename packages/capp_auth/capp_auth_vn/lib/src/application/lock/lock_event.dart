part of 'lock_bloc.dart';

@Freezed(
  copyWith: false,
  equal: false,
  fromJson: false,
  toJson: false,
  map: FreezedMapOptions.none,
  when: FreezedWhenOptions.none,
)
class LockEvent with _$LockEvent {
  const factory LockEvent.init({
    required String localizedBiometricReason,
    required bool disableAutoFaceBiometricScan,
    required bool isLockStatusEnabled,
  }) = _Init;
  const factory LockEvent.refresh() = _Refresh;
  const factory LockEvent.passwordChanged(String newPassword) = _PasswordChanged;
  const factory LockEvent.submit() = _Submit;
  const factory LockEvent.clear() = _Clear;
  const factory LockEvent.biometricPressed() = _BiometricPressed;
}
