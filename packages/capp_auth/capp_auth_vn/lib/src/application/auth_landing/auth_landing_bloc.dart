import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:dartz/dartz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

part 'auth_landing_bloc.freezed.dart';
part 'auth_landing_event.dart';
part 'auth_landing_state.dart';

class AuthLandingBloc extends Bloc<AuthLandingEvent, AuthLandingState> {
  final ICurrentUserRepository currentUserRepository;
  final IUserRepository userRepository;
  final IConnectUserRepository connectUserRepository;
  final StartupMeasurementService startupMeasurementService;

  AuthLandingBloc({
    required this.currentUserRepository,
    required this.userRepository,
    required this.startupMeasurementService,
    required this.connectUserRepository,
  }) : super(AuthLandingState.initialize()) {
    on<_Init>(_onInit);
    on<_Refresh>((_, emit) => _onRefresh(emit));
    on<_UsernameChanged>(_onUsernameChanged);
    on<_PasswordChanged>(_onPasswordChanged);
    on<_Submit>(_onSubmit);
  }

  void _onInit(_Init event, Emitter<AuthLandingState> emit) {
    startupMeasurementService.stopMeasure(StartupMeasurementOrigin.lockScreen);
    emit(
      state.copyWith(
        username: event.prefilledUsername,
        usernameValid: event.prefilledUsername.isNotEmpty,
      ),
    );
  }

  void _onRefresh(Emitter<AuthLandingState> emit) {
    emit(
      state.copyWith(
        state: LoadingState.isInitial,
        failureOrSuccess: none(),
        timetoBlockAccount: null,
      ),
    );
  }

  void _onUsernameChanged(_UsernameChanged event, Emitter<AuthLandingState> emit) {
    emit(
      state.copyWith(
        username: event.username,
        usernameValid: event.isValid,
        failureOrSuccess: none(),
        timetoBlockAccount: null,
      ),
    );
  }

  void _onPasswordChanged(_PasswordChanged event, Emitter<AuthLandingState> emit) {
    if (state.timetoBlockAccount != null) {
      emit(
        state.copyWith(
          password: event.password,
        ),
      );
    } else {
      emit(
        state.copyWith(password: event.password, failureOrSuccess: none()),
      );
    }
  }

  Future<void> _onSubmit(_Submit event, Emitter<AuthLandingState> emit) async {
    if (state.username.isNotEmpty && state.password.isNotEmpty && state.state != LoadingState.isLoading) {
      emit(
        state.copyWith(
          state: LoadingState.isLoading,
          failureOrSuccess: none(),
          timetoBlockAccount: null,
        ),
      );

      final currentUserId = (await userRepository.currentUser()).fold((l) => null, (r) => r.id!);
      final result = await connectUserRepository.connectWithCredentials(
        username: state.username,
        password: state.password,
        otpCode: event.otpToken,
        anonymousUserId: currentUserId,
      );
      emit(
        result.fold((failure) {
          return failure.maybeWhen(
            verificationRequired: (authErrorResponse) => state.copyWith(
              failureOrSuccess: optionOf(result),
              state: LoadingState.isCompleted,
            ),
            orElse: () {
              return state.copyWith(
                failureOrSuccess: optionOf(result),
                password: '',
                state: LoadingState.isCompleted,
                timetoBlockAccount: timeToBlock(result),
              );
            },
          );
        }, (r) {
          return state.copyWith(failureOrSuccess: optionOf(result), state: LoadingState.isCompleted);
        }),
      );
    }
  }

  Duration? timeToBlock(Either<AuthFailure, AuthTokens> result) => result.fold(
        (l) => l.maybeMap(
          orElse: () => null,
          unauthorized: (value) => value.authErrorResponse?.lockedUntil?.difference(DateTime.now()),
        ),
        (r) => null,
      );
}
