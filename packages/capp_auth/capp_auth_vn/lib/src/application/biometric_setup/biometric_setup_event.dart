part of 'biometric_setup_bloc.dart';

sealed class BiometricSetupEvent {
  const BiometricSetupEvent();

  factory BiometricSetupEvent.initialize() => const BiometricSetupInitialize();
  factory BiometricSetupEvent.toggleUnlock({required bool enable}) => BiometricSetupToggleUnlock(enable: enable);
  factory BiometricSetupEvent.toggleTransaction({required bool enable}) =>
      BiometricSetupToggleTransaction(enable: enable);
  factory BiometricSetupEvent.dialogAction(DialogAction action) => BiometricSetupDialogAction(action: action);
}

enum DialogAction {
  confirm,
  dismiss,
}

final class BiometricSetupInitialize extends BiometricSetupEvent {
  const BiometricSetupInitialize();
}

final class BiometricSetupToggleUnlock extends BiometricSetupEvent {
  final bool enable;
  const BiometricSetupToggleUnlock({required this.enable});
}

final class BiometricSetupToggleTransaction extends BiometricSetupEvent {
  final bool enable;
  const BiometricSetupToggleTransaction({required this.enable});
}

final class BiometricSetupDialogAction extends BiometricSetupEvent {
  final DialogAction action;
  const BiometricSetupDialogAction({required this.action});
}
