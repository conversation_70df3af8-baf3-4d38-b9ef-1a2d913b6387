// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'change_password_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ChangePasswordState {
  bool get passwordChanged => throw _privateConstructorUsedError;
  LoadingState get state => throw _privateConstructorUsedError;
  int? get attemptsLeft => throw _privateConstructorUsedError;
  SetPasswordFailure? get error => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ChangePasswordStateCopyWith<ChangePasswordState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChangePasswordStateCopyWith<$Res> {
  factory $ChangePasswordStateCopyWith(
          ChangePasswordState value, $Res Function(ChangePasswordState) then) =
      _$ChangePasswordStateCopyWithImpl<$Res, ChangePasswordState>;
  @useResult
  $Res call(
      {bool passwordChanged,
      LoadingState state,
      int? attemptsLeft,
      SetPasswordFailure? error});

  $SetPasswordFailureCopyWith<$Res>? get error;
}

/// @nodoc
class _$ChangePasswordStateCopyWithImpl<$Res, $Val extends ChangePasswordState>
    implements $ChangePasswordStateCopyWith<$Res> {
  _$ChangePasswordStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? passwordChanged = null,
    Object? state = null,
    Object? attemptsLeft = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      passwordChanged: null == passwordChanged
          ? _value.passwordChanged
          : passwordChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      attemptsLeft: freezed == attemptsLeft
          ? _value.attemptsLeft
          : attemptsLeft // ignore: cast_nullable_to_non_nullable
              as int?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as SetPasswordFailure?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $SetPasswordFailureCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $SetPasswordFailureCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ChangePasswordStateCopyWith<$Res>
    implements $ChangePasswordStateCopyWith<$Res> {
  factory _$$_ChangePasswordStateCopyWith(_$_ChangePasswordState value,
          $Res Function(_$_ChangePasswordState) then) =
      __$$_ChangePasswordStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool passwordChanged,
      LoadingState state,
      int? attemptsLeft,
      SetPasswordFailure? error});

  @override
  $SetPasswordFailureCopyWith<$Res>? get error;
}

/// @nodoc
class __$$_ChangePasswordStateCopyWithImpl<$Res>
    extends _$ChangePasswordStateCopyWithImpl<$Res, _$_ChangePasswordState>
    implements _$$_ChangePasswordStateCopyWith<$Res> {
  __$$_ChangePasswordStateCopyWithImpl(_$_ChangePasswordState _value,
      $Res Function(_$_ChangePasswordState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? passwordChanged = null,
    Object? state = null,
    Object? attemptsLeft = freezed,
    Object? error = freezed,
  }) {
    return _then(_$_ChangePasswordState(
      passwordChanged: null == passwordChanged
          ? _value.passwordChanged
          : passwordChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      attemptsLeft: freezed == attemptsLeft
          ? _value.attemptsLeft
          : attemptsLeft // ignore: cast_nullable_to_non_nullable
              as int?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as SetPasswordFailure?,
    ));
  }
}

/// @nodoc

class _$_ChangePasswordState implements _ChangePasswordState {
  const _$_ChangePasswordState(
      {this.passwordChanged = false,
      this.state = LoadingState.isInitial,
      this.attemptsLeft,
      this.error});

  @override
  @JsonKey()
  final bool passwordChanged;
  @override
  @JsonKey()
  final LoadingState state;
  @override
  final int? attemptsLeft;
  @override
  final SetPasswordFailure? error;

  @override
  String toString() {
    return 'ChangePasswordState(passwordChanged: $passwordChanged, state: $state, attemptsLeft: $attemptsLeft, error: $error)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ChangePasswordState &&
            (identical(other.passwordChanged, passwordChanged) ||
                other.passwordChanged == passwordChanged) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.attemptsLeft, attemptsLeft) ||
                other.attemptsLeft == attemptsLeft) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, passwordChanged, state, attemptsLeft, error);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ChangePasswordStateCopyWith<_$_ChangePasswordState> get copyWith =>
      __$$_ChangePasswordStateCopyWithImpl<_$_ChangePasswordState>(
          this, _$identity);
}

abstract class _ChangePasswordState implements ChangePasswordState {
  const factory _ChangePasswordState(
      {final bool passwordChanged,
      final LoadingState state,
      final int? attemptsLeft,
      final SetPasswordFailure? error}) = _$_ChangePasswordState;

  @override
  bool get passwordChanged;
  @override
  LoadingState get state;
  @override
  int? get attemptsLeft;
  @override
  SetPasswordFailure? get error;
  @override
  @JsonKey(ignore: true)
  _$$_ChangePasswordStateCopyWith<_$_ChangePasswordState> get copyWith =>
      throw _privateConstructorUsedError;
}
