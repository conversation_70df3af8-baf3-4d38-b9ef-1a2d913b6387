import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_feature_flags/capp_feature_flags.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

class RegistrationSetPasswordBloc extends SetPasswordBloc<PasswordSignUpCompleteResponse> {
  final IConnectUserRepository connectUserRepository;
  final IAuthenticationRepository authenticationRepository;
  final IFeatureFlagRepository featureFlagRepository;

  RegistrationSetPasswordBloc({
    required this.connectUserRepository,
    required this.authenticationRepository,
    required this.featureFlagRepository,
    required IPasswordLengthProvider passwordLengthProvider,
    required IEnvironmentConfigRepository environmentConfigRepository,
    required CappAuthTrackingService trackingService,
  }) : super(
          passwordLengthProvider: passwordLengthProvider,
          environmentConfigRepository: environmentConfigRepository,
          trackingService: trackingService,
        );

  @override
  Future<void> submit(Emitter<SetPasswordState<PasswordSignUpCompleteResponse>> emit) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        failureOrSuccess: none(),
      ),
    );

    final result = featureFlagRepository.isEnabledCached(FeatureFlag.onboardingV4Bos)
        ? await authenticationRepository.authenticationSetPassword(
            password: state.password,
            sessionId: state.sessionId!,
            challenge: state.challenge,
            publicKey: state.publicKey,
          )
        : await connectUserRepository.signUpV5Complete(
            password: state.password,
            sessionId: state.sessionId!,
            challenge: state.challenge,
            publicKey: state.publicKey,
          );

    var allowPasswordClean = true;

    emit(
      result.fold((l) {
        final mappedFailure = l.maybeMap(
          loginFailed: (value) => value.authFailure.maybeMap(
            reachedMaximumAccountAllowed: (_) => const SetPasswordFailure.reachedMaximumAccountAllowed(),
            orElse: () => const SetPasswordFailure.unexpected(),
          ),
          setPasswordFailed: (value) {
            trackingService.trackAccountCreationSetupPinOnUnqualifiedPinImpression();
            return value.setPasswordFailure;
          },
          orElse: () {
            allowPasswordClean = false;
            return const SetPasswordFailure.unexpected();
          },
        );

        return state.copyWith(
          password: allowPasswordClean ? '' : state.password,
          firstPassword: allowPasswordClean ? '' : state.firstPassword,
          state: LoadingState.isCompleted,
          failureOrSuccess: optionOf(left(mappedFailure)),
        );
      }, (r) {
        trackingService.trackAccountCreationSetupPinOnReenterPinEvent();
        return state.copyWith(
          password: '',
          failureOrSuccess: optionOf(right(r)),
          state: LoadingState.isCompleted,
        );
      }),
    );
  }
}
