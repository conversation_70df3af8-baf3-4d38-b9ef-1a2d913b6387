// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'password_recovery_username_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$PasswordRecoveryUsernameEvent {}

/// @nodoc

class _$_Initialize implements _Initialize {
  const _$_Initialize({this.prefilledInfo});

  @override
  final PrefilledInfo? prefilledInfo;

  @override
  String toString() {
    return 'PasswordRecoveryUsernameEvent.initialize(prefilledInfo: $prefilledInfo)';
  }
}

abstract class _Initialize implements PasswordRecoveryUsernameEvent {
  const factory _Initialize({final PrefilledInfo? prefilledInfo}) =
      _$_Initialize;

  PrefilledInfo? get prefilledInfo;
}

/// @nodoc

class _$_SetUsername implements _SetUsername {
  const _$_SetUsername({this.maskedUsername = '', this.unmaskedUsername = ''});

  @override
  @JsonKey()
  final String maskedUsername;
  @override
  @JsonKey()
  final String unmaskedUsername;

  @override
  String toString() {
    return 'PasswordRecoveryUsernameEvent.setUsername(maskedUsername: $maskedUsername, unmaskedUsername: $unmaskedUsername)';
  }
}

abstract class _SetUsername implements PasswordRecoveryUsernameEvent {
  const factory _SetUsername(
      {final String maskedUsername,
      final String unmaskedUsername}) = _$_SetUsername;

  String get maskedUsername;
  String get unmaskedUsername;
}

/// @nodoc

class _$_OtpCompleted implements _OtpCompleted {
  const _$_OtpCompleted({required this.otpToken});

  @override
  final String otpToken;

  @override
  String toString() {
    return 'PasswordRecoveryUsernameEvent.otpCompleted(otpToken: $otpToken)';
  }
}

abstract class _OtpCompleted implements PasswordRecoveryUsernameEvent {
  const factory _OtpCompleted({required final String otpToken}) =
      _$_OtpCompleted;

  String get otpToken;
}

/// @nodoc

class _$_ButtonClicked implements _ButtonClicked {
  const _$_ButtonClicked();

  @override
  String toString() {
    return 'PasswordRecoveryUsernameEvent.buttonClicked()';
  }
}

abstract class _ButtonClicked implements PasswordRecoveryUsernameEvent {
  const factory _ButtonClicked() = _$_ButtonClicked;
}

/// @nodoc
mixin _$PasswordRecoveryUsernameState {
  LoadingState get state => throw _privateConstructorUsedError;
  Option<
      Either<Failure<UserVerificationFailureType, void>,
          ResetPasswordSessionResponseV2>> get otpVerificationResponse =>
      throw _privateConstructorUsedError;
  Option<Either<Failure<OtpPrepareFailureType, void>, OtpPrepareResponse>>
      get otpPrepareResponse => throw _privateConstructorUsedError;
  String get maskedUsername => throw _privateConstructorUsedError;
  String get unmaskedUsername => throw _privateConstructorUsedError;
  String? get otpToken => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PasswordRecoveryUsernameStateCopyWith<PasswordRecoveryUsernameState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PasswordRecoveryUsernameStateCopyWith<$Res> {
  factory $PasswordRecoveryUsernameStateCopyWith(
          PasswordRecoveryUsernameState value,
          $Res Function(PasswordRecoveryUsernameState) then) =
      _$PasswordRecoveryUsernameStateCopyWithImpl<$Res,
          PasswordRecoveryUsernameState>;
  @useResult
  $Res call(
      {LoadingState state,
      Option<
              Either<Failure<UserVerificationFailureType, void>,
                  ResetPasswordSessionResponseV2>>
          otpVerificationResponse,
      Option<Either<Failure<OtpPrepareFailureType, void>, OtpPrepareResponse>>
          otpPrepareResponse,
      String maskedUsername,
      String unmaskedUsername,
      String? otpToken});
}

/// @nodoc
class _$PasswordRecoveryUsernameStateCopyWithImpl<$Res,
        $Val extends PasswordRecoveryUsernameState>
    implements $PasswordRecoveryUsernameStateCopyWith<$Res> {
  _$PasswordRecoveryUsernameStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? otpVerificationResponse = null,
    Object? otpPrepareResponse = null,
    Object? maskedUsername = null,
    Object? unmaskedUsername = null,
    Object? otpToken = freezed,
  }) {
    return _then(_value.copyWith(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      otpVerificationResponse: null == otpVerificationResponse
          ? _value.otpVerificationResponse
          : otpVerificationResponse // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<Failure<UserVerificationFailureType, void>,
                      ResetPasswordSessionResponseV2>>,
      otpPrepareResponse: null == otpPrepareResponse
          ? _value.otpPrepareResponse
          : otpPrepareResponse // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<Failure<OtpPrepareFailureType, void>,
                      OtpPrepareResponse>>,
      maskedUsername: null == maskedUsername
          ? _value.maskedUsername
          : maskedUsername // ignore: cast_nullable_to_non_nullable
              as String,
      unmaskedUsername: null == unmaskedUsername
          ? _value.unmaskedUsername
          : unmaskedUsername // ignore: cast_nullable_to_non_nullable
              as String,
      otpToken: freezed == otpToken
          ? _value.otpToken
          : otpToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PasswordRecoveryUsernameStateCopyWith<$Res>
    implements $PasswordRecoveryUsernameStateCopyWith<$Res> {
  factory _$$_PasswordRecoveryUsernameStateCopyWith(
          _$_PasswordRecoveryUsernameState value,
          $Res Function(_$_PasswordRecoveryUsernameState) then) =
      __$$_PasswordRecoveryUsernameStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoadingState state,
      Option<
              Either<Failure<UserVerificationFailureType, void>,
                  ResetPasswordSessionResponseV2>>
          otpVerificationResponse,
      Option<Either<Failure<OtpPrepareFailureType, void>, OtpPrepareResponse>>
          otpPrepareResponse,
      String maskedUsername,
      String unmaskedUsername,
      String? otpToken});
}

/// @nodoc
class __$$_PasswordRecoveryUsernameStateCopyWithImpl<$Res>
    extends _$PasswordRecoveryUsernameStateCopyWithImpl<$Res,
        _$_PasswordRecoveryUsernameState>
    implements _$$_PasswordRecoveryUsernameStateCopyWith<$Res> {
  __$$_PasswordRecoveryUsernameStateCopyWithImpl(
      _$_PasswordRecoveryUsernameState _value,
      $Res Function(_$_PasswordRecoveryUsernameState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? otpVerificationResponse = null,
    Object? otpPrepareResponse = null,
    Object? maskedUsername = null,
    Object? unmaskedUsername = null,
    Object? otpToken = freezed,
  }) {
    return _then(_$_PasswordRecoveryUsernameState(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as LoadingState,
      otpVerificationResponse: null == otpVerificationResponse
          ? _value.otpVerificationResponse
          : otpVerificationResponse // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<Failure<UserVerificationFailureType, void>,
                      ResetPasswordSessionResponseV2>>,
      otpPrepareResponse: null == otpPrepareResponse
          ? _value.otpPrepareResponse
          : otpPrepareResponse // ignore: cast_nullable_to_non_nullable
              as Option<
                  Either<Failure<OtpPrepareFailureType, void>,
                      OtpPrepareResponse>>,
      maskedUsername: null == maskedUsername
          ? _value.maskedUsername
          : maskedUsername // ignore: cast_nullable_to_non_nullable
              as String,
      unmaskedUsername: null == unmaskedUsername
          ? _value.unmaskedUsername
          : unmaskedUsername // ignore: cast_nullable_to_non_nullable
              as String,
      otpToken: freezed == otpToken
          ? _value.otpToken
          : otpToken // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_PasswordRecoveryUsernameState
    implements _PasswordRecoveryUsernameState {
  const _$_PasswordRecoveryUsernameState(
      {required this.state,
      required this.otpVerificationResponse,
      required this.otpPrepareResponse,
      required this.maskedUsername,
      required this.unmaskedUsername,
      this.otpToken});

  @override
  final LoadingState state;
  @override
  final Option<
      Either<Failure<UserVerificationFailureType, void>,
          ResetPasswordSessionResponseV2>> otpVerificationResponse;
  @override
  final Option<Either<Failure<OtpPrepareFailureType, void>, OtpPrepareResponse>>
      otpPrepareResponse;
  @override
  final String maskedUsername;
  @override
  final String unmaskedUsername;
  @override
  final String? otpToken;

  @override
  String toString() {
    return 'PasswordRecoveryUsernameState(state: $state, otpVerificationResponse: $otpVerificationResponse, otpPrepareResponse: $otpPrepareResponse, maskedUsername: $maskedUsername, unmaskedUsername: $unmaskedUsername, otpToken: $otpToken)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PasswordRecoveryUsernameState &&
            (identical(other.state, state) || other.state == state) &&
            (identical(
                    other.otpVerificationResponse, otpVerificationResponse) ||
                other.otpVerificationResponse == otpVerificationResponse) &&
            (identical(other.otpPrepareResponse, otpPrepareResponse) ||
                other.otpPrepareResponse == otpPrepareResponse) &&
            (identical(other.maskedUsername, maskedUsername) ||
                other.maskedUsername == maskedUsername) &&
            (identical(other.unmaskedUsername, unmaskedUsername) ||
                other.unmaskedUsername == unmaskedUsername) &&
            (identical(other.otpToken, otpToken) ||
                other.otpToken == otpToken));
  }

  @override
  int get hashCode => Object.hash(runtimeType, state, otpVerificationResponse,
      otpPrepareResponse, maskedUsername, unmaskedUsername, otpToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PasswordRecoveryUsernameStateCopyWith<_$_PasswordRecoveryUsernameState>
      get copyWith => __$$_PasswordRecoveryUsernameStateCopyWithImpl<
          _$_PasswordRecoveryUsernameState>(this, _$identity);
}

abstract class _PasswordRecoveryUsernameState
    implements PasswordRecoveryUsernameState {
  const factory _PasswordRecoveryUsernameState(
      {required final LoadingState state,
      required final Option<
              Either<Failure<UserVerificationFailureType, void>,
                  ResetPasswordSessionResponseV2>>
          otpVerificationResponse,
      required final Option<
              Either<Failure<OtpPrepareFailureType, void>, OtpPrepareResponse>>
          otpPrepareResponse,
      required final String maskedUsername,
      required final String unmaskedUsername,
      final String? otpToken}) = _$_PasswordRecoveryUsernameState;

  @override
  LoadingState get state;
  @override
  Option<
      Either<Failure<UserVerificationFailureType, void>,
          ResetPasswordSessionResponseV2>> get otpVerificationResponse;
  @override
  Option<Either<Failure<OtpPrepareFailureType, void>, OtpPrepareResponse>>
      get otpPrepareResponse;
  @override
  String get maskedUsername;
  @override
  String get unmaskedUsername;
  @override
  String? get otpToken;
  @override
  @JsonKey(ignore: true)
  _$$_PasswordRecoveryUsernameStateCopyWith<_$_PasswordRecoveryUsernameState>
      get copyWith => throw _privateConstructorUsedError;
}
