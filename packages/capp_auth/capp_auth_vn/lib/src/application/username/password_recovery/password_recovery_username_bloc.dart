import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:dartz/dartz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_otp/koyal_otp.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../domain/prepare_otp/i_prepare_otp_repository.dart';
import '../../../domain/prepare_otp/otp_prepare_failure_type.dart';
import '../../../domain/prepare_otp/otp_prepare_response.dart';

part 'password_recovery_username_bloc.freezed.dart';
part 'password_recovery_username_event.dart';
part 'password_recovery_username_state.dart';

class PasswordRecoveryUsernameBloc extends Bloc<PasswordRecoveryUsernameEvent, PasswordRecoveryUsernameState> {
  final IResetPasswordRepository resetPasswordRepository;
  final IPrepareOtpRepository prepareOtpRepository;

  PasswordRecoveryUsernameBloc({
    required this.resetPasswordRepository,
    required this.prepareOtpRepository,
  }) : super(PasswordRecoveryUsernameState.initialize()) {
    on<_Initialize>(
      (event, emit) => emit(
        state.copyWith(
          maskedUsername: event.prefilledInfo?.maskedPhoneNumber ?? '',
          unmaskedUsername: event.prefilledInfo?.phoneNumber ?? '',
        ),
      ),
    );
    on<_SetUsername>(
      (event, emit) => emit(
        state.copyWith(
          maskedUsername: event.maskedUsername,
          unmaskedUsername: event.unmaskedUsername,
        ),
      ),
    );
    on<_ButtonClicked>(_onButtonClicked);
    on<_OtpCompleted>(_onOtpCompleted);
  }

  Future<void> _onButtonClicked(_ButtonClicked event, Emitter<PasswordRecoveryUsernameState> emit) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        otpVerificationResponse: none(),
        otpPrepareResponse: none(),
      ),
    );

    final result = await prepareOtpRepository.initOtpSession(
      username: state.unmaskedUsername,
      otpType: OtpRequestType.changePasswordCapp,
    );

    emit(
      state.copyWith(
        state: LoadingState.isCompleted,
        otpVerificationResponse: none(),
        otpPrepareResponse: optionOf(result),
      ),
    );
  }

  Future<void> _onOtpCompleted(_OtpCompleted event, Emitter<PasswordRecoveryUsernameState> emit) async {
    emit(
      state.copyWith(
        state: LoadingState.isLoading,
        otpVerificationResponse: none(),
        otpToken: event.otpToken,
      ),
    );

    final result = await resetPasswordRepository.initSession(
      username: state.unmaskedUsername,
      otpToken: event.otpToken,
    );

    emit(
      state.copyWith(
        state: LoadingState.isCompleted,
        otpVerificationResponse: optionOf(result),
        otpToken: event.otpToken,
      ),
    );
  }
}
