import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_tracking/capp_tracking.dart';
import 'package:flutter/material.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_shared/koyal_shared.dart';

class <PERSON>callerLoginHelper implements IExternalLoginHelper {
  final CappTrackingService cappTrackingService;

  TruecallerLoginHelper({required this.cappTrackingService});

  @override
  String? getErrorMessage({int? errorType, required BuildContext context}) {
    if (errorType == null) return null;
    if (GmaPlatform.isIOS) {
      // base on https://docs.truecaller.com/truecaller-sdk/ios/integrating-with-your-ios-app/handling-error-scenarios
      switch (errorType) {
        case 1:
        case 2:
        case 4:
        case 5:
        case 10:
        case 14:
        case 15:
        case 16:
          return L10nCappAuth.of(context).truecallerMessageTechnicalError;
        case 9:
          return L10nCappAuth.of(context).truecallerMessageNetworkError;
        default:
          return null;
      }
    } else {
      // base on https://docs.truecaller.com/truecaller-sdk/android/integrating-with-your-app/handling-error-scenarios
      switch (errorType) {
        case 1:
          return L10nCappAuth.of(context).truecallerMessageNetworkError;
        case 3:
        case 4:
        case 5:
        case 10:
          return L10nCappAuth.of(context).truecallerMessageTechnicalError;

        case 15:
          return L10nCappAuth.of(context).truecallerMessageTimeoutError;

        default:
          return null;
      }
    }
  }

  @override
  void sendGAEvents({int? errorType}) {
    if (errorType == null) return;

    switch (errorType) {
      case 1:
      case 3:
      case 4:
      case 5:
      case 10:
      case 13:
      case 15:
        cappTrackingService.trackTruecallerEvent(
          TrackingCategories.loginGapp,
          KoyalAnalyticsConstants.error,
          '${TrackingLabels.truecallerError}$errorType',
          null,
        );
        break;

      case 14:
        cappTrackingService.trackTruecallerEvent(
          TrackingCategories.trueccallerBottomSlider,
          KoyalAnalyticsConstants.click,
          TrackingLabels.useAnotherNumber,
          KoyalEvent.truecallerBtmSliderAnotherNum,
        );
        break;
      default:
        break;
    }
  }
}
