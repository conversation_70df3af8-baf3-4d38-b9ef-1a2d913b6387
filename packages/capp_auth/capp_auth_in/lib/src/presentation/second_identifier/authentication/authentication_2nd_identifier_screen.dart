import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:navigator/navigator.dart';

class Authentication2ndIdentifierScreen
    extends SecondIdentifierVerificationScreenBase<AuthenticationSecondIdVerificationResponse> {
  @override
  void trackAuthVerificationOnGoBackClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackAuthVerificationOnGoBackClick();
  }

  @override
  void trackSecondIdentifierOnConfirm(BuildContext context, UserSecondIdType secondId) {
    context.get<CappAuthTrackingService>().trackAuthVerificationConfirm2ndIdTypeClick(secondId);
  }

  @override
  void trackSecondIdentifierOnNotCustomerClick(BuildContext context) {
    context.get<CappAuthTrackingService>().trackAuthVerificationNotCustomerClick();
  }

  @override
  void onSuccess({
    required AuthenticationSecondIdVerificationResponse r,
    required String sessionId,
    required UserSecondIdType secondIdentifierType,
    required String secondIdValue,
    required BuildContext context,
  }) {
    if (r.loginSessionId != null) {
      context.navigator.push(
        path: NavigatorPath.cappAuth.loginPinScreen,
        arguments: PinScreenArguments(
          loginSessionId: r.loginSessionId!,
          passwordLength: r.passwordLength,
          prefilledInfo: args.prefilledInfo,
        ),
      );
    } else {
      if (!context.mounted) return;
      context.read<SecondIdentifierBloc>().add(const SecondIdentifierEvent.authenticateStart());
      context.get<AuthenticationAuthenticateVerificationCubit>()
        ..startBosAuthenticateHandling(sessionId, initialWaitTime: r.initialWaitTime)
        ..stream.listen((state) {
          if (state is AuthenticationAuthenticateVerificationApprove) {
            if (!context.mounted) return;
            context.read<SecondIdentifierBloc>().add(const SecondIdentifierEvent.authenticateStop());
            if (state.loginSessionId != null) {
              if (!context.mounted) return;
              // User with CUID -> login screen
              context.navigator.pushReplacement(
                path: NavigatorPath.cappAuth.loginPinScreen,
                arguments: PinScreenArguments(
                  loginSessionId: state.loginSessionId!,
                  passwordLength: r.passwordLength,
                  prefilledInfo: args.prefilledInfo,
                ),
              );
            } else {
              // User without CUID -> create prospect
              if (!context.mounted) return;
              context.navigator.pushReplacement(
                path: NavigatorPath.cappAuth.registrationSetPasswordScreen,
                arguments: SetPasswordScreenArguments(
                  sessionId: args.sessionId,
                ),
              );
            }
          } else {
            if (!context.mounted) return;
            context.read<SecondIdentifierBloc>().add(const SecondIdentifierEvent.authenticateErrorResult());
          }
        });
    }
  }

  @override
  void onNotHciCustomerClick(BuildContext context, String? sessionId) {
    context.navigator.pushNamed(
      NavigatorPath.cappAuth.registrationSetPasswordScreen,
      arguments: SetPasswordScreenArguments(
        sessionId: sessionId,
      ),
    );
  }

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider<SecondIdentifierBloc>(
        create: (context) => context.get<Authentication2ndIdBloc>()
          ..add(
            SecondIdentifierEvent.init(
              sessionId: args.sessionId,
              allowedIds: args.allowedSecondIds,
              escape: args.escape,
            ),
          ),
        child: this,
      );

  Authentication2ndIdentifierScreen({
    Key? key,
    required SecondIdentifierScreenArguments arguments,
  }) : super(key: key, arguments: arguments);
}
