import 'package:capp_auth_core/capp_auth_core.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gma_platform/gma_platform.dart';

import 'steps_instructions_screen_arguments.dart';

class StepsInstructionsScreen extends StatelessWidget {
  final StepsInstructionsScreenArguments arguments;
  const StepsInstructionsScreen({
    super.key,
    required this.arguments,
  });

  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      appBar: KoyalAppBar(),
      body: FixedBottomContentScreen(
        upperContent: [
          MainHeading(
            title: L10nCappAuth.of(context).stepsInstructionsScreenTitle,
            subtitle: L10nCappAuth.of(context).stepsInstructionsScreenSubtitle,
            centerAlign: false,
          ),
          SvgPicture.asset(
            'assets/svg/steps_instructions_screen/ilu_mascot_communication.svg',
            package: 'capp_auth_core',
          ),
          KoyalStepsInformationGuide(
            items: [
              KoyalStepsInformationGuideData(
                title: L10nCappAuth.of(context).stepsInstructionsScreenStepOneTitle,
                desc: L10nCappAuth.of(context).stepsInstructionsScreenStepOneSubtitle(arguments.email),
                localIcon: SvgPicture.asset(
                  'assets/svg/steps_instructions_screen/steps_instructions_screen_email.svg',
                  package: 'capp_auth_core',
                ),
              ),
              KoyalStepsInformationGuideData(
                title: L10nCappAuth.of(context).stepsInstructionsScreenStepTwoTitle,
                desc: L10nCappAuth.of(context).stepsInstructionsScreenStepTwoSubtitle,
                localIcon: SvgPicture.asset(
                  'assets/svg/steps_instructions_screen/steps_instructions_screen_phone.svg',
                  package: 'capp_auth_core',
                ),
              ),
              KoyalStepsInformationGuideData(
                title: L10nCappAuth.of(context).stepsInstructionsScreenStepThreeTitle,
                desc: L10nCappAuth.of(context).stepsInstructionsScreenStepThreeSubtitle,
                localIcon: SvgPicture.asset(
                  'assets/svg/steps_instructions_screen/steps_instructions_screen_repeat.svg',
                  package: 'capp_auth_core',
                ),
              ),
            ],
          ),
        ],
        fixedBottomContent: [
          VerticalButtonsLayout(
            primaryButton: PrimaryButton(
              text: L10nCappAuth.of(context).stepsInstructionsScreenContactUsButton,
              onPressed: () => GmaPlatform.launchUrl('mailto:${arguments.email}'),
            ),
          ),
        ],
      ),
    );
  }
}
