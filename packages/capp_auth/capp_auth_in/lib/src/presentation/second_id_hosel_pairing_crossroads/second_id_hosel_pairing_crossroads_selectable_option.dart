import 'package:capp_auth_core/l10n/i18n.dart';
import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'second_id_hosel_pairing_crossroads_selectable_option_type.dart';

class SecondIdHoselPairingCrossroadsSelectableOption extends StatelessWidget {
  const SecondIdHoselPairingCrossroadsSelectableOption({
    super.key,
    required this.selectableOptionType,
    required this.updateSelectedOption,
    required this.selectedOption,
  });
  final SecondIdHoselPairingCrossroadsSelectableOptionType selectableOptionType;
  final SecondIdHoselPairingCrossroadsSelectableOptionType? selectedOption;
  final VoidCallback updateSelectedOption;

  @override
  Widget build(BuildContext context) {
    return KoyalPadding.smallVertical(
      child: SingleSelectableContainer<SecondIdHoselPairingCrossroadsSelectableOptionType>(
        value: selectableOptionType,
        groupValue: selectedOption,
        title: _getTitle(
          l10nCappAuth: L10nCappAuth.of(context),
          selectableOptionType: selectableOptionType,
        ),
        bodyText: _getBodyText(
          l10nCappAuth: L10nCappAuth.of(context),
          selectableOptionType: selectableOptionType,
        ),
        avatar: _getAvatar(selectableOptionType: selectableOptionType),
        onChanged: (_) => updateSelectedOption(),
      ),
    );
  }
}

String? _getTitle({
  required L10nCappAuth l10nCappAuth,
  required SecondIdHoselPairingCrossroadsSelectableOptionType selectableOptionType,
}) {
  if (selectableOptionType == SecondIdHoselPairingCrossroadsSelectableOptionType.login) {
    return l10nCappAuth.decisionCrossroadsOptionLogoutTitle;
  } else {
    return l10nCappAuth.decisionCrossroadsOptionNoAccessTitle;
  }
}

String _getBodyText({
  required L10nCappAuth l10nCappAuth,
  required SecondIdHoselPairingCrossroadsSelectableOptionType selectableOptionType,
}) {
  if (selectableOptionType == SecondIdHoselPairingCrossroadsSelectableOptionType.login) {
    return l10nCappAuth.decisionCrossroadsOptionLogoutSubtitle;
  } else {
    return l10nCappAuth.decisionCrossroadsOptionNoAccessSubtitle;
  }
}

Widget _getAvatar({
  required SecondIdHoselPairingCrossroadsSelectableOptionType selectableOptionType,
}) {
  final assetName =
      selectableOptionType == SecondIdHoselPairingCrossroadsSelectableOptionType.login ? 'login' : 'no_access';
  return SvgPicture.asset(
    'assets/svg/decision_screen/$assetName.svg',
    package: 'capp_auth_core',
  );
}
