import 'dart:convert';
import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:koyal_otp/koyal_otp.dart';

import '../domain/i_truecaller_login_repository.dart';

class TruecallerLoginRepository implements ITruecallerLoginRepository {
  @override
  Future<Either<int, TruecallerUserTokenResponse>> getUserToken({
    required String clientId,
    required String code,
    required String codeVerifier,
  }) async {
    try {
      final uri = Uri.parse('https://oauth-account-noneu.truecaller.com/v1/token');
      final requestData = 'grant_type=authorization_code'
          '&client_id=${Uri.encodeQueryComponent(clientId)}'
          '&code=${Uri.encodeQueryComponent(code)}'
          '&code_verifier=${Uri.encodeQueryComponent(codeVerifier)}';

      final request = await HttpClient().postUrl(uri);
      request.headers.set(HttpHeaders.contentTypeHeader, 'application/x-www-form-urlencoded');
      request.write(requestData);

      final response = await request.close();

      if (response.statusCode == HttpStatus.ok) {
        final stringResponse = await response.transform(utf8.decoder).join();
        final decodedResponse = json.decode(stringResponse) as Map<String, dynamic>;

        return right(TruecallerUserTokenResponse.fromJson(decodedResponse));
      } else {
        return left(response.statusCode);
      }
    } catch (_) {
      return left(HttpStatus.internalServerError);
    }
  }

  @override
  Future<Either<int, TruecallerProfileResponse>> getUserProfile(String token) async {
    try {
      final uri = Uri.parse('https://oauth-account-noneu.truecaller.com/v1/userinfo');

      final request = await HttpClient().getUrl(uri);
      request.headers.set(HttpHeaders.contentTypeHeader, 'application/json');
      request.headers.set(HttpHeaders.authorizationHeader, 'Bearer $token');

      final response = await request.close();

      if (response.statusCode == HttpStatus.ok) {
        final stringResponse = await response.transform(utf8.decoder).join();
        final decodedResponse = json.decode(stringResponse) as Map<String, dynamic>;

        return right(TruecallerProfileResponse.fromJson(decodedResponse));
      } else {
        return left(response.statusCode);
      }
    } catch (_) {
      return left(HttpStatus.internalServerError);
    }
  }
}
