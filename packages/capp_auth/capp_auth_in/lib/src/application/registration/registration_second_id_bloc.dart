import 'dart:async';

import 'package:capp_auth_core/capp_auth_core.dart' as core;
import 'package:capp_content_core/capp_content_core.dart';
import 'package:dartz/dartz.dart';
import 'package:koyal_auth/koyal_auth.dart';

class RegistrationSecondIdBloc extends core.RegistrationSecondIdBloc {
  final core.RegistrationLegalLogData legalLogData;
  final IDocumentsRepository documentsRepository;

  RegistrationSecondIdBloc({
    required ICappSignUpRepository cappSignUpRepository,
    required core.IEnvironmentConfigRepository environmentConfigRepository,
    required this.documentsRepository,
    required this.legalLogData,
  }) : super(
          cappSignUpRepository: cappSignUpRepository,
          environmentConfigRepository: environmentConfigRepository,
        );

  @override
  Future<Either<SignUpFailure, Unit>> post(String? otpVerificationToken) async {
    final privacyPolicy = (await documentsRepository.getPrivacyPolicyDocument()).fold((l) => null, (r) => r);
    final termsOfUse = (await documentsRepository.getTermsOfUseDocument()).fold((l) => null, (r) => r);

    legalLogData
      ..privacyPolicyVersion = privacyPolicy?.version?.toString() ?? ''
      ..termsOfUseVersion = termsOfUse?.version?.toString() ?? '';

    return super.post(otpVerificationToken);
  }

  @override
  Future<Either<SignUpFailure, StartUserSignUpSessionResponse>> getAllowedSecondIds(String sessionId) {
    return cappSignUpRepository.signUpV5InitSession(
      usernameVerificationSessionId: sessionId,
    );
  }
}
