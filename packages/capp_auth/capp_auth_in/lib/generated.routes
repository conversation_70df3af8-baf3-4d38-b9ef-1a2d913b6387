[{"name": "authenticationInitialScreen", "package": "CappAuth", "path": "capp_auth/authentication-initial"}, {"name": "authentication2ndIdentifierScreen", "package": "CappAuth", "path": "capp_auth/authentication/2nd_id"}, {"name": "tncScreen", "package": "CappAuth", "path": "capp_auth/authentication/tnc_screen"}, {"name": "retrieveAccount2ndIdentifierScreen", "package": "CappAuth", "path": "capp_auth/retrieve-account/2nd_id"}, {"name": "crossroadsScreen", "package": "CappAuth", "path": "capp_auth/crossroads"}, {"name": "authLandingScreen", "package": "CappAuth", "path": "capp_auth/auth-landing"}, {"name": "login2ndIdScreen", "package": "CappAuth", "path": "capp_auth/login/2nd_id"}, {"name": "loginPinScreen", "package": "CappAuth", "path": "capp_auth/login_pin"}, {"name": "registrationSecondIdNonHoselScreen", "package": "CappAuth", "path": "capp_auth/registration/second_id_non_hosel"}, {"name": "changeSecondIdNonHoselScreen", "package": "CappAuth", "path": "capp_auth/change/second_id_non_hosel"}, {"name": "secondIdHoselPairingScreen", "package": "CappAuth", "path": "capp_auth/registration/second_id_hosel_pairing"}, {"name": "registrationSetPasswordScreen", "package": "CappAuth", "path": "capp_auth/registration/set-password"}, {"name": "lockScreen", "package": "CappAuth", "path": "capp_auth/lock_screen"}, {"name": "confirmUserDisablingScreen", "package": "CappAuth", "path": "capp_auth/confirm_user_disabling"}, {"name": "logoutScreen", "package": "CappAuth", "path": "capp_auth/logout"}, {"name": "logoutIndicationScreen", "package": "CappAuth", "path": "capp_auth/logout-indication"}, {"name": "changePhoneEntryScreen", "package": "CappAuth", "path": "capp_auth/change-phone/login/phone"}, {"name": "changePhone2ndIdScreen", "package": "CappAuth", "path": "capp_auth/change-phone/login/second-id"}, {"name": "changePhonePinScreen", "package": "CappAuth", "path": "capp_auth/change-phone/login/pin"}, {"name": "changePhoneNewPhoneScreen", "package": "CappAuth", "path": "capp_auth/change-phone/new-phone"}, {"name": "changePhoneFailScreen", "package": "CappAuth", "path": "capp_auth/change-phone/fail"}, {"name": "changePhoneSuccessScreen", "package": "CappAuth", "path": "capp_auth/change-phone/success"}, {"name": "pinPassRecoveryEntryScreen", "package": "CappAuth", "path": "capp_auth/pin-pass-recovery/entry"}, {"name": "pinPassRecovery2ndIdScreen", "package": "CappAuth", "path": "capp_auth/pin-pass-recovery/2nd-id"}, {"name": "passwordRecoverySetPasswordScreen", "package": "CappAuth", "path": "capp_auth/pin-pass-recovery/set-password"}, {"name": "pinPassRecoverySuccessScreen", "package": "CappAuth", "path": "capp_auth/pin-pass-recovery/success"}, {"name": "unblockAccountEntryScreen", "package": "CappAuth", "path": "capp_auth/unblock-account/entry"}, {"name": "unblockAccount2ndIdScreen", "package": "CappAuth", "path": "capp_auth/unblock-account/2nd-id"}, {"name": "unblockAccountSuccessScreen", "package": "CappAuth", "path": "capp_auth/unblock-account/success"}, {"name": "changePasswordSetPasswordScreen", "package": "CappAuth", "path": "capp_auth/general/reset_pin_password"}, {"name": "secondIdHoselPairingCrossroadsScreen", "package": "CappAuth", "path": "capp_auth/second_id_hosel_crossroads_screen"}, {"name": "stepsInstructionsScreen", "package": "CappAuth", "path": "capp_auth/steps_instructions_screen"}]