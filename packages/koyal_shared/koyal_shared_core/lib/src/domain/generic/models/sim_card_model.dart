import 'package:freezed_annotation/freezed_annotation.dart';

part 'sim_card_model.freezed.dart';

@freezed
class SimCardModel with _$SimCardModel {
  factory SimCardModel({
    String? carrierName,
    String? displayName,
    int? slotIndex,
    String? number,
    String? countryIso,
    String? countryPhonePrefix,
  }) = _SimCardModel;

  const SimCardModel._();

  String? get numberWithoutCountryPrefix => number!.replaceAll('+$countryPhonePrefix', '');
}
