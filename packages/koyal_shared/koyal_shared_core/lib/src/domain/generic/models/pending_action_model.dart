import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:intl/intl.dart';

import '../../../../koyal_shared_core.dart';

part 'pending_action_model.freezed.dart';

const pendingActionMetadataKeyApplicationCode = 'ApplicationCode';
const pendingActionMetadataKeyProductType = 'ProductType';
const pendingActionMetadataKeyMerchantName = 'merchantName';
const pendingActionMetadataKeyExpiryDate = 'ExpiryDate';
const pendingActionMetadataKeyRefNum = 'refNum';
const pendingActionMetadataKeyAmount = 'amount';
const pendingActionMetadataKeySessionId = 'surveySessionId';
const pendingActionMetadataKeyCreationDate = 'creationDate';
const pendingActionPaymentOptionType = 'paymentOptionType';
const pendingActionWKsurveyContractId = 'ContractID';
const pendingActionMetaDataAccountExpirationDate = 'accountExpirationDate';
const pendingActionMetaDataAccountNumber = 'accountNumber';
const pendingActionMetaDataProductProfileCode = 'ProductProfileCode';

@freezed
class PendingActionModel with _$PendingActionModel {
  const factory PendingActionModel({
    required String id,
    required PendingActionType type,
    DataLoanPending? dataLoanPending,
    int? count,
    String? icon,
    String? iconPackage,
    Map<String, String?>? metadata,
  }) = _PendingActionModel;

  const PendingActionModel._();

  String? get metadataProductType => metadata?[pendingActionMetadataKeyProductType];
  String? get metadataApplicationCode => metadata?[pendingActionMetadataKeyApplicationCode];
  String? get metadataMerchantName => metadata?[pendingActionMetadataKeyMerchantName];
  String? get metadataPaymentOptionType => metadata?[pendingActionPaymentOptionType];
  String? get metadataProductProfileCode => metadata?[pendingActionMetaDataProductProfileCode];
  DateTime? get metadataExpiryDate => metadata?[pendingActionMetadataKeyExpiryDate] == null
      ? null
      : DateTime.tryParse(metadata![pendingActionMetadataKeyExpiryDate]!);
  String? get metadataRefNum => metadata?[pendingActionMetadataKeyRefNum];
  Decimal? get metadataAmount {
    final key = metadata?.keys.firstWhereOrNull((e) => pendingActionMetadataKeyAmount.toLowerCase() == e.toLowerCase());
    if (key != null && metadata?[key] != null) {
      return Decimal.tryParse(metadata![key]!);
    }
    return null;
  }

  String? get metadataWKsurveyContractId => metadata?[pendingActionWKsurveyContractId];
  String? get metadataAccountNumber => metadata?[pendingActionMetaDataAccountNumber];
  factory PendingActionModel.empty() => const PendingActionModel(
        id: '',
        type: PendingActionType.uploadDocument,
      );
  String? get metadataAccountExpirationDate {
    try {
      final dateString = metadata?[pendingActionMetaDataAccountExpirationDate];

      if (dateString == null) return null;

      final parsedDate = DateFormat('MM/dd/yyyy hh:mm:ss').parse(dateString);

      return DateFormat('MMMM d').format(parsedDate);
    } catch (e) {
      return null;
    }
  }
}

@freezed
class PendingActionListModel with _$PendingActionListModel {
  factory PendingActionListModel({
    @Default(<PendingActionModel>[]) List<PendingActionModel> items,
  }) = _PendingActionListModel;

  const PendingActionListModel._();

  int? get count => items.length;
}

extension PendingActionModelIcon on PendingActionModel {
  String defaultIcon() {
    switch (type) {
      case PendingActionType.takeSelfie:
        return 'assets/svg/pending_actions/pa_take_selfie.svg';
      case PendingActionType.uploadDocument:
        return 'assets/svg/pending_actions/pa_upload_document.svg';
      case PendingActionType.loanApplication:
        return 'assets/svg/pending_actions/pa_loan_application.svg';
      case PendingActionType.signContract:
        return 'assets/svg/pending_actions/pa_sign_contract.svg';
      case PendingActionType.signAppendix:
        return 'assets/svg/pending_actions/pa_sign_contract.svg';
      case PendingActionType.transactionSignature:
        return 'assets/svg/pending_actions/pa_sign_contract.svg';
      case PendingActionType.setupDirectDebit:
        return 'assets/svg/pending_actions/pa_setup_direct_debit.svg';
      case PendingActionType.limitedOffer:
        return 'assets/svg/pending_actions/pa_limited_offer.svg';
      case PendingActionType.mappFinancingNotAvailableAnymore:
        return 'assets/svg/pending_actions/pa_no_contract.svg';
      case PendingActionType.mappPriceChangeRequired:
        return 'assets/svg/pending_actions/pa_alert.svg';
      case PendingActionType.userMustChangePassword:
        return 'assets/svg/pending_actions/pa_internet_banking_verification.svg';
      case PendingActionType.offerScoring:
        return 'assets/svg/pending_actions/pa_offer_scoring.svg';
      case PendingActionType.loanPending:
        {
          switch (dataLoanPending) {
            case DataLoanPending.accountVerification:
            case DataLoanPending.manualDeduplication:
              return 'assets/svg/pending_actions/pa_offer_scoring.svg';
            default:
              return 'assets/svg/pending_actions/pa_loan_pending.svg';
          }
        }
      case PendingActionType.cuidPairing:
      case PendingActionType.identificationUpgradeToCustomer:
        return 'assets/svg/contact_contractid.svg';
      case PendingActionType.identificationPending:
        return 'assets/svg/pending_actions/pa_kupo_2_hands.svg';
      case PendingActionType.completeOnboarding:
        return 'assets/svg/pending_actions/pa_kupo_2_hands.svg';
      case PendingActionType.productVAS:
        return 'assets/svg/pending_actions/pa_alarm_outline.svg';
      case PendingActionType.webTransactions:
        return 'assets/svg/pending_actions/pa_hc_pay.svg';
      case PendingActionType.survey:
        return 'assets/svg/pending_actions/pa_survey.svg';
      case PendingActionType.productVoC:
        return 'assets/svg/pending_actions/pa_alarm_outline.svg';
      case PendingActionType.onbGuideline:
        return 'assets/svg/pending_actions/pa_welcome_guidelines.svg';
      case PendingActionType.wKsurvey:
        return 'assets/svg/pending_actions/pa_alarm_outline.svg';
      case PendingActionType.qwartaRenewal:
        return 'assets/svg/pending_actions/pa_qwarta.svg';
    }
  }

  String newDefaultIcon() {
    switch (type) {
      case PendingActionType.takeSelfie:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.uploadDocument:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.loanApplication:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.signContract:
        return 'assets/svg/pending_actions/pa_sign.svg';
      case PendingActionType.signAppendix:
        return 'assets/svg/pending_actions/pa_sign.svg';
      case PendingActionType.transactionSignature:
        return 'assets/svg/pending_actions/pa_sign.svg';
      case PendingActionType.setupDirectDebit:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.limitedOffer:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.mappFinancingNotAvailableAnymore:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.mappPriceChangeRequired:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.userMustChangePassword:
        return 'assets/svg/pending_actions/pa_reset.svg';
      case PendingActionType.offerScoring:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.loanPending:
        {
          switch (dataLoanPending) {
            case DataLoanPending.accountVerification:
            case DataLoanPending.manualDeduplication:
              return 'assets/svg/pending_actions/pa_list.svg';
            default:
              return 'assets/svg/pending_actions/pa_list.svg';
          }
        }
      case PendingActionType.cuidPairing:
      case PendingActionType.identificationUpgradeToCustomer:
        return 'assets/svg/pending_actions/pa_reset.svg';
      case PendingActionType.identificationPending:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.completeOnboarding:
        return 'assets/svg/pending_actions/pa_list.svg';
      case PendingActionType.productVAS:
        return 'assets/svg/pending_actions/pa_alarm_outline.svg';
      case PendingActionType.webTransactions:
        return 'assets/svg/pending_actions/pa_hc_pay.svg';
      case PendingActionType.survey:
        return 'assets/svg/pending_actions/pa_survey.svg';
      case PendingActionType.productVoC:
        return 'assets/svg/pending_actions/pa_alarm_outline.svg';
      case PendingActionType.onbGuideline:
        return 'assets/svg/pending_actions/pa_welcome_guidelines.svg';
      case PendingActionType.wKsurvey:
        return 'assets/svg/pending_actions/pa_alarm_outline.svg';
      case PendingActionType.qwartaRenewal:
        return 'assets/svg/pending_actions/pa_qwarta.svg';
    }
  }

  String get defaultIconPackage {
    if (type == PendingActionType.cuidPairing || type == PendingActionType.identificationUpgradeToCustomer) {
      return 'capp_ui_core';
    }

    return 'capp_ui';
  }
}

extension PendingActionModelComponents on PendingActionModel {
  String title(L10nKoyalShared l10n) {
    switch (type) {
      case PendingActionType.takeSelfie:
        return l10n.pendingActionTypeTakeSelfieTitle;
      case PendingActionType.uploadDocument:
        return l10n.pendingActionTypeUploadDocumentsTitle;
      case PendingActionType.loanApplication:
        return l10n.pendingActionTypeLoanApplicationTitle;
      case PendingActionType.signContract:
        return l10n.pendingActionTypeSignContractTitle;
      case PendingActionType.signAppendix:
        return l10n.pendingActionTypeSignAppendixTitle;
      case PendingActionType.transactionSignature:
        return l10n.pendingActionTypeSignTransactionTitle;
      case PendingActionType.setupDirectDebit:
        return l10n.pendingActionTypeSetupDirectDebitTitle;
      case PendingActionType.limitedOffer:
        return l10n.pendingActionTypeLimitdOfferTitle;
      case PendingActionType.mappFinancingNotAvailableAnymore:
        return l10n.pendingActionTypeHcinFinancingNotAvailableTitle;
      case PendingActionType.mappPriceChangeRequired:
        return l10n.pendingActionTypePriceChangeRequiredTitle;
      case PendingActionType.userMustChangePassword:
        return l10n.pendingActionTypeChangePasswordTitle;
      case PendingActionType.offerScoring:
        return l10n.pendingActionTypeOfferScoringTitle;
      case PendingActionType.loanPending:
        {
          switch (dataLoanPending) {
            case DataLoanPending.accountVerification:
              return l10n.pendingActionTypeAccountVerificationTitle;
            case DataLoanPending.manualDeduplication:
              return l10n.pendingActionTypeManualDeduplicationTitle;
            default:
              return l10n.pendingActionTypeLoanPendingTitle;
          }
        }
      case PendingActionType.cuidPairing:
      case PendingActionType.identificationUpgradeToCustomer:
        return l10n.pendingActionTypeCuidPairingTitle;
      case PendingActionType.identificationPending:
        return l10n.pendingActionTypeIdentificationTitle;
      case PendingActionType.completeOnboarding:
        return l10n.pendingActionTypeCompleteOnboardingTitle;
      case PendingActionType.productVAS:
        {
          final paStage = metadata?[pendingActionMetadataKeyPAStage];
          if (paStage == VasCspPendingActionTypeStage.cspPendingApplication.value) {
            return l10n.pendingActionTypeVasContinueSelecting;
          } else if (paStage == VasCspPendingActionTypeStage.cspPendingPayment.value) {
            return l10n.pendingActionTypeVasContinuePayment;
          } else if (paStage == VasCspPendingActionTypeStage.cspPendingActivation.value) {
            return l10n.pendingActionTypeVasOneLastStep;
          } else if (paStage == VasCspPendingActionTypeStage.cspPendingReactivate.value) {
            return l10n.pendingActionTypeVasActivationFailed;
          } else {
            return '';
          }
        }
      case PendingActionType.webTransactions:
        return l10n.pendingActionTypeWebTransactionsTitle(metadataMerchantName ?? '');
      case PendingActionType.survey:
        return l10n.pendingActionTypeSurveyTitle;
      case PendingActionType.productVoC:
        return l10n.pendingActionTypeProductVocTitle;
      case PendingActionType.wKsurvey:
        return l10n.pendingActionTypeWkSurveyTitle(metadataWKsurveyContractId ?? '');
      case PendingActionType.onbGuideline:
        return '';
      case PendingActionType.qwartaRenewal:
        return l10n.pendingActionTypeQwartaRenewalTitle(metadataAccountExpirationDate.toString());
    }
  }

  String subtitle(L10nKoyalShared l10n) {
    switch (type) {
      case PendingActionType.takeSelfie:
        return l10n.pendingActionTypeTakeSelfieSubtitle;
      case PendingActionType.uploadDocument:
        return l10n.pendingActionTypeUploadDocumentsSubtitle;
      case PendingActionType.loanApplication:
        return l10n.pendingActionTypeLoanApplicationSubtitle;
      case PendingActionType.signContract:
        return l10n.pendingActionTypeSignContractSubtitle;
      case PendingActionType.signAppendix:
        return l10n.pendingActionTypeSignAppendixSubtitle;
      case PendingActionType.transactionSignature:
        return l10n.pendingActionTypeSignTransactionSubtitle;
      case PendingActionType.setupDirectDebit:
        return l10n.pendingActionTypeSetupDirectDebitSubtitle;
      case PendingActionType.limitedOffer:
        return l10n.pendingActionTypeLimitdOfferSubtitle;
      case PendingActionType.mappFinancingNotAvailableAnymore:
        return l10n.pendingActionTypeHcinFinancingNotAvailableSubtitle;
      case PendingActionType.mappPriceChangeRequired:
        return l10n.pendingActionTypePriceChangeRequiredSubtitle;
      case PendingActionType.userMustChangePassword:
        return l10n.pendingActionTypeChangePasswordSubtitle;
      case PendingActionType.offerScoring:
        return l10n.pendingActionTypeOfferScoringSubtitle;
      case PendingActionType.loanPending:
        {
          switch (dataLoanPending) {
            case DataLoanPending.accountVerification:
              return l10n.pendingActionTypeAccountVerificationSubtitle;
            case DataLoanPending.manualDeduplication:
              return l10n.pendingActionTypeManualDeduplicationSubtitle;
            case DataLoanPending.posGL:
              return l10n.pendingActionTypePosGlSubtitle;
            case DataLoanPending.twSelection:
              return l10n.pendingActionTypeSelectionTwSubtitle;
            default:
              return l10n.pendingActionTypeLoanPendingSubtitle;
          }
        }
      case PendingActionType.cuidPairing:
      case PendingActionType.identificationUpgradeToCustomer:
        return l10n.pendingActionTypeCuidPairingSubtitle;
      case PendingActionType.identificationPending:
        return l10n.pendingActionTypeIdentificationSubtitle;
      case PendingActionType.completeOnboarding:
        if (metadata?['loginSessionId'] != null) {
          return l10n.pendingActionTypeCompleteOnboardingLoginSubtitle;
        }
        return l10n.pendingActionTypeCompleteOnboardingRegistrationSubtitle;
      case PendingActionType.productVAS:
        {
          final paStage = metadata?[pendingActionMetadataKeyPAStage];
          if (paStage == VasCspPendingActionTypeStage.cspPendingApplication.value) {
            return l10n.pendingActionTypeVasContinueSelecting;
          } else if (paStage == VasCspPendingActionTypeStage.cspPendingPayment.value) {
            return l10n.pendingActionTypeVasContinuePayment;
          } else if (paStage == VasCspPendingActionTypeStage.cspPendingActivation.value) {
            return l10n.pendingActionTypeVasOneLastStep;
          } else if (paStage == VasCspPendingActionTypeStage.cspPendingReactivate.value) {
            return l10n.pendingActionTypeVasActivationFailed;
          } else {
            return '';
          }
        }
      case PendingActionType.webTransactions:
        return l10n.pendingActionTypeWebTransactionsTitle(metadataMerchantName ?? '');
      case PendingActionType.survey:
        return l10n.pendingActionTypeSurveySubtitle;
      case PendingActionType.productVoC:
        return l10n.pendingActionTypeVocSubtitle;
      case PendingActionType.wKsurvey:
        return l10n.pendingActionTypeWkSurveySubtitle(metadataWKsurveyContractId ?? '');
      case PendingActionType.onbGuideline:
        final contentType = metadata?['ContentTemplate'] ?? '';
        final completedMission = metadata?['NoCompletedMissions'] ?? 0;
        final totalMission = metadata?['TotalMissions'] ?? 0;
        if (contentType == 'CompletedEvent') {
          return l10n.pendingActionTypeWelcomeGuidelineCompletedTitle(completedMission, totalMission);
        }
        return l10n.pendingActionTypeWelcomeGuidelineIncompleteTitle(completedMission, totalMission);
      case PendingActionType.qwartaRenewal:
        return l10n.pendingActionTypeQwartaRenewalTitle(metadataAccountExpirationDate.toString());
    }
  }

  String? infoText(L10nKoyalShared l10n) {
    switch (type) {
      case PendingActionType.signContract:
        return metadataApplicationCode != null ? l10n.pendingActionTypeSignContractInfo(metadataApplicationCode) : null;
      case PendingActionType.webTransactions:
        return l10n.pendingActionTypeWebTransactionsSubtitle(
          metadataAmount.formatCurrency() ?? '',
          metadataRefNum ?? '',
        );
      case PendingActionType.qwartaRenewal:
        return l10n.pendingActionTypeQwartaRenewalSubtitle(metadataAccountNumber);
      default:
        return null;
    }
  }
}
