// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ValueFailure<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ValueFailureCopyWith<T, $Res> {
  factory $ValueFailureCopyWith(
          ValueFailure<T> value, $Res Function(ValueFailure<T>) then) =
      _$ValueFailureCopyWithImpl<T, $Res, ValueFailure<T>>;
}

/// @nodoc
class _$ValueFailureCopyWithImpl<T, $Res, $Val extends ValueFailure<T>>
    implements $ValueFailureCopyWith<T, $Res> {
  _$ValueFailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ExceedingLengthImplCopyWith<T, $Res> {
  factory _$$ExceedingLengthImplCopyWith(_$ExceedingLengthImpl<T> value,
          $Res Function(_$ExceedingLengthImpl<T>) then) =
      __$$ExceedingLengthImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue, int max});
}

/// @nodoc
class __$$ExceedingLengthImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$ExceedingLengthImpl<T>>
    implements _$$ExceedingLengthImplCopyWith<T, $Res> {
  __$$ExceedingLengthImplCopyWithImpl(_$ExceedingLengthImpl<T> _value,
      $Res Function(_$ExceedingLengthImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
    Object? max = null,
  }) {
    return _then(_$ExceedingLengthImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
      max: null == max
          ? _value.max
          : max // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ExceedingLengthImpl<T>
    with DiagnosticableTreeMixin
    implements ExceedingLength<T> {
  const _$ExceedingLengthImpl({required this.failedValue, required this.max});

  @override
  final T failedValue;
  @override
  final int max;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.exceedingLength(failedValue: $failedValue, max: $max)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.exceedingLength'))
      ..add(DiagnosticsProperty('failedValue', failedValue))
      ..add(DiagnosticsProperty('max', max));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExceedingLengthImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue) &&
            (identical(other.max, max) || other.max == max));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue), max);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExceedingLengthImplCopyWith<T, _$ExceedingLengthImpl<T>> get copyWith =>
      __$$ExceedingLengthImplCopyWithImpl<T, _$ExceedingLengthImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return exceedingLength(failedValue, max);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return exceedingLength?.call(failedValue, max);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (exceedingLength != null) {
      return exceedingLength(failedValue, max);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return exceedingLength(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return exceedingLength?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (exceedingLength != null) {
      return exceedingLength(this);
    }
    return orElse();
  }
}

abstract class ExceedingLength<T> implements ValueFailure<T> {
  const factory ExceedingLength(
      {required final T failedValue,
      required final int max}) = _$ExceedingLengthImpl<T>;

  T get failedValue;
  int get max;
  @JsonKey(ignore: true)
  _$$ExceedingLengthImplCopyWith<T, _$ExceedingLengthImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ToShortLengthImplCopyWith<T, $Res> {
  factory _$$ToShortLengthImplCopyWith(_$ToShortLengthImpl<T> value,
          $Res Function(_$ToShortLengthImpl<T>) then) =
      __$$ToShortLengthImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue, int min});
}

/// @nodoc
class __$$ToShortLengthImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$ToShortLengthImpl<T>>
    implements _$$ToShortLengthImplCopyWith<T, $Res> {
  __$$ToShortLengthImplCopyWithImpl(_$ToShortLengthImpl<T> _value,
      $Res Function(_$ToShortLengthImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
    Object? min = null,
  }) {
    return _then(_$ToShortLengthImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
      min: null == min
          ? _value.min
          : min // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ToShortLengthImpl<T>
    with DiagnosticableTreeMixin
    implements ToShortLength<T> {
  const _$ToShortLengthImpl({required this.failedValue, required this.min});

  @override
  final T failedValue;
  @override
  final int min;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.toShortLength(failedValue: $failedValue, min: $min)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.toShortLength'))
      ..add(DiagnosticsProperty('failedValue', failedValue))
      ..add(DiagnosticsProperty('min', min));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToShortLengthImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue) &&
            (identical(other.min, min) || other.min == min));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue), min);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ToShortLengthImplCopyWith<T, _$ToShortLengthImpl<T>> get copyWith =>
      __$$ToShortLengthImplCopyWithImpl<T, _$ToShortLengthImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return toShortLength(failedValue, min);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return toShortLength?.call(failedValue, min);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (toShortLength != null) {
      return toShortLength(failedValue, min);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return toShortLength(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return toShortLength?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (toShortLength != null) {
      return toShortLength(this);
    }
    return orElse();
  }
}

abstract class ToShortLength<T> implements ValueFailure<T> {
  const factory ToShortLength(
      {required final T failedValue,
      required final int min}) = _$ToShortLengthImpl<T>;

  T get failedValue;
  int get min;
  @JsonKey(ignore: true)
  _$$ToShortLengthImplCopyWith<T, _$ToShortLengthImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EmptyImplCopyWith<T, $Res> {
  factory _$$EmptyImplCopyWith(
          _$EmptyImpl<T> value, $Res Function(_$EmptyImpl<T>) then) =
      __$$EmptyImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$EmptyImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$EmptyImpl<T>>
    implements _$$EmptyImplCopyWith<T, $Res> {
  __$$EmptyImplCopyWithImpl(
      _$EmptyImpl<T> _value, $Res Function(_$EmptyImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$EmptyImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$EmptyImpl<T> with DiagnosticableTreeMixin implements Empty<T> {
  const _$EmptyImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.empty(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.empty'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmptyImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EmptyImplCopyWith<T, _$EmptyImpl<T>> get copyWith =>
      __$$EmptyImplCopyWithImpl<T, _$EmptyImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return empty(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return empty?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class Empty<T> implements ValueFailure<T> {
  const factory Empty({required final T failedValue}) = _$EmptyImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$EmptyImplCopyWith<T, _$EmptyImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MultilineImplCopyWith<T, $Res> {
  factory _$$MultilineImplCopyWith(
          _$MultilineImpl<T> value, $Res Function(_$MultilineImpl<T>) then) =
      __$$MultilineImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$MultilineImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$MultilineImpl<T>>
    implements _$$MultilineImplCopyWith<T, $Res> {
  __$$MultilineImplCopyWithImpl(
      _$MultilineImpl<T> _value, $Res Function(_$MultilineImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$MultilineImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$MultilineImpl<T> with DiagnosticableTreeMixin implements Multiline<T> {
  const _$MultilineImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.multiline(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.multiline'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MultilineImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MultilineImplCopyWith<T, _$MultilineImpl<T>> get copyWith =>
      __$$MultilineImplCopyWithImpl<T, _$MultilineImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return multiline(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return multiline?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (multiline != null) {
      return multiline(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return multiline(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return multiline?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (multiline != null) {
      return multiline(this);
    }
    return orElse();
  }
}

abstract class Multiline<T> implements ValueFailure<T> {
  const factory Multiline({required final T failedValue}) = _$MultilineImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$MultilineImplCopyWith<T, _$MultilineImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NumberTooLargeImplCopyWith<T, $Res> {
  factory _$$NumberTooLargeImplCopyWith(_$NumberTooLargeImpl<T> value,
          $Res Function(_$NumberTooLargeImpl<T>) then) =
      __$$NumberTooLargeImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue, num max});
}

/// @nodoc
class __$$NumberTooLargeImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$NumberTooLargeImpl<T>>
    implements _$$NumberTooLargeImplCopyWith<T, $Res> {
  __$$NumberTooLargeImplCopyWithImpl(_$NumberTooLargeImpl<T> _value,
      $Res Function(_$NumberTooLargeImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
    Object? max = null,
  }) {
    return _then(_$NumberTooLargeImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
      max: null == max
          ? _value.max
          : max // ignore: cast_nullable_to_non_nullable
              as num,
    ));
  }
}

/// @nodoc

class _$NumberTooLargeImpl<T>
    with DiagnosticableTreeMixin
    implements NumberTooLarge<T> {
  const _$NumberTooLargeImpl({required this.failedValue, required this.max});

  @override
  final T failedValue;
  @override
  final num max;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.numberTooLarge(failedValue: $failedValue, max: $max)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.numberTooLarge'))
      ..add(DiagnosticsProperty('failedValue', failedValue))
      ..add(DiagnosticsProperty('max', max));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NumberTooLargeImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue) &&
            (identical(other.max, max) || other.max == max));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue), max);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NumberTooLargeImplCopyWith<T, _$NumberTooLargeImpl<T>> get copyWith =>
      __$$NumberTooLargeImplCopyWithImpl<T, _$NumberTooLargeImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return numberTooLarge(failedValue, max);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return numberTooLarge?.call(failedValue, max);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (numberTooLarge != null) {
      return numberTooLarge(failedValue, max);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return numberTooLarge(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return numberTooLarge?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (numberTooLarge != null) {
      return numberTooLarge(this);
    }
    return orElse();
  }
}

abstract class NumberTooLarge<T> implements ValueFailure<T> {
  const factory NumberTooLarge(
      {required final T failedValue,
      required final num max}) = _$NumberTooLargeImpl<T>;

  T get failedValue;
  num get max;
  @JsonKey(ignore: true)
  _$$NumberTooLargeImplCopyWith<T, _$NumberTooLargeImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ListTooLongImplCopyWith<T, $Res> {
  factory _$$ListTooLongImplCopyWith(_$ListTooLongImpl<T> value,
          $Res Function(_$ListTooLongImpl<T>) then) =
      __$$ListTooLongImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue, int max});
}

/// @nodoc
class __$$ListTooLongImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$ListTooLongImpl<T>>
    implements _$$ListTooLongImplCopyWith<T, $Res> {
  __$$ListTooLongImplCopyWithImpl(
      _$ListTooLongImpl<T> _value, $Res Function(_$ListTooLongImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
    Object? max = null,
  }) {
    return _then(_$ListTooLongImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
      max: null == max
          ? _value.max
          : max // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ListTooLongImpl<T>
    with DiagnosticableTreeMixin
    implements ListTooLong<T> {
  const _$ListTooLongImpl({required this.failedValue, required this.max});

  @override
  final T failedValue;
  @override
  final int max;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.listTooLong(failedValue: $failedValue, max: $max)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.listTooLong'))
      ..add(DiagnosticsProperty('failedValue', failedValue))
      ..add(DiagnosticsProperty('max', max));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListTooLongImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue) &&
            (identical(other.max, max) || other.max == max));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue), max);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ListTooLongImplCopyWith<T, _$ListTooLongImpl<T>> get copyWith =>
      __$$ListTooLongImplCopyWithImpl<T, _$ListTooLongImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return listTooLong(failedValue, max);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return listTooLong?.call(failedValue, max);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (listTooLong != null) {
      return listTooLong(failedValue, max);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return listTooLong(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return listTooLong?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (listTooLong != null) {
      return listTooLong(this);
    }
    return orElse();
  }
}

abstract class ListTooLong<T> implements ValueFailure<T> {
  const factory ListTooLong(
      {required final T failedValue,
      required final int max}) = _$ListTooLongImpl<T>;

  T get failedValue;
  int get max;
  @JsonKey(ignore: true)
  _$$ListTooLongImplCopyWith<T, _$ListTooLongImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidEmailImplCopyWith<T, $Res> {
  factory _$$InvalidEmailImplCopyWith(_$InvalidEmailImpl<T> value,
          $Res Function(_$InvalidEmailImpl<T>) then) =
      __$$InvalidEmailImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$InvalidEmailImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$InvalidEmailImpl<T>>
    implements _$$InvalidEmailImplCopyWith<T, $Res> {
  __$$InvalidEmailImplCopyWithImpl(
      _$InvalidEmailImpl<T> _value, $Res Function(_$InvalidEmailImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$InvalidEmailImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$InvalidEmailImpl<T>
    with DiagnosticableTreeMixin
    implements InvalidEmail<T> {
  const _$InvalidEmailImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.invalidEmail(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.invalidEmail'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidEmailImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InvalidEmailImplCopyWith<T, _$InvalidEmailImpl<T>> get copyWith =>
      __$$InvalidEmailImplCopyWithImpl<T, _$InvalidEmailImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return invalidEmail(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return invalidEmail?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (invalidEmail != null) {
      return invalidEmail(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return invalidEmail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return invalidEmail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (invalidEmail != null) {
      return invalidEmail(this);
    }
    return orElse();
  }
}

abstract class InvalidEmail<T> implements ValueFailure<T> {
  const factory InvalidEmail({required final T failedValue}) =
      _$InvalidEmailImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$InvalidEmailImplCopyWith<T, _$InvalidEmailImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidPanImplCopyWith<T, $Res> {
  factory _$$InvalidPanImplCopyWith(
          _$InvalidPanImpl<T> value, $Res Function(_$InvalidPanImpl<T>) then) =
      __$$InvalidPanImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$InvalidPanImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$InvalidPanImpl<T>>
    implements _$$InvalidPanImplCopyWith<T, $Res> {
  __$$InvalidPanImplCopyWithImpl(
      _$InvalidPanImpl<T> _value, $Res Function(_$InvalidPanImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$InvalidPanImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$InvalidPanImpl<T>
    with DiagnosticableTreeMixin
    implements InvalidPan<T> {
  const _$InvalidPanImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.invalidPan(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.invalidPan'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidPanImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InvalidPanImplCopyWith<T, _$InvalidPanImpl<T>> get copyWith =>
      __$$InvalidPanImplCopyWithImpl<T, _$InvalidPanImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return invalidPan(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return invalidPan?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (invalidPan != null) {
      return invalidPan(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return invalidPan(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return invalidPan?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (invalidPan != null) {
      return invalidPan(this);
    }
    return orElse();
  }
}

abstract class InvalidPan<T> implements ValueFailure<T> {
  const factory InvalidPan({required final T failedValue}) =
      _$InvalidPanImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$InvalidPanImplCopyWith<T, _$InvalidPanImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidUjjwalCardImplCopyWith<T, $Res> {
  factory _$$InvalidUjjwalCardImplCopyWith(_$InvalidUjjwalCardImpl<T> value,
          $Res Function(_$InvalidUjjwalCardImpl<T>) then) =
      __$$InvalidUjjwalCardImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$InvalidUjjwalCardImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$InvalidUjjwalCardImpl<T>>
    implements _$$InvalidUjjwalCardImplCopyWith<T, $Res> {
  __$$InvalidUjjwalCardImplCopyWithImpl(_$InvalidUjjwalCardImpl<T> _value,
      $Res Function(_$InvalidUjjwalCardImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$InvalidUjjwalCardImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$InvalidUjjwalCardImpl<T>
    with DiagnosticableTreeMixin
    implements InvalidUjjwalCard<T> {
  const _$InvalidUjjwalCardImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.invalidUjjwalCard(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.invalidUjjwalCard'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidUjjwalCardImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InvalidUjjwalCardImplCopyWith<T, _$InvalidUjjwalCardImpl<T>>
      get copyWith =>
          __$$InvalidUjjwalCardImplCopyWithImpl<T, _$InvalidUjjwalCardImpl<T>>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return invalidUjjwalCard(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return invalidUjjwalCard?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (invalidUjjwalCard != null) {
      return invalidUjjwalCard(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return invalidUjjwalCard(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return invalidUjjwalCard?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (invalidUjjwalCard != null) {
      return invalidUjjwalCard(this);
    }
    return orElse();
  }
}

abstract class InvalidUjjwalCard<T> implements ValueFailure<T> {
  const factory InvalidUjjwalCard({required final T failedValue}) =
      _$InvalidUjjwalCardImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$InvalidUjjwalCardImplCopyWith<T, _$InvalidUjjwalCardImpl<T>>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidContractIdImplCopyWith<T, $Res> {
  factory _$$InvalidContractIdImplCopyWith(_$InvalidContractIdImpl<T> value,
          $Res Function(_$InvalidContractIdImpl<T>) then) =
      __$$InvalidContractIdImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$InvalidContractIdImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$InvalidContractIdImpl<T>>
    implements _$$InvalidContractIdImplCopyWith<T, $Res> {
  __$$InvalidContractIdImplCopyWithImpl(_$InvalidContractIdImpl<T> _value,
      $Res Function(_$InvalidContractIdImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$InvalidContractIdImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$InvalidContractIdImpl<T>
    with DiagnosticableTreeMixin
    implements InvalidContractId<T> {
  const _$InvalidContractIdImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.invalidContractId(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.invalidContractId'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidContractIdImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InvalidContractIdImplCopyWith<T, _$InvalidContractIdImpl<T>>
      get copyWith =>
          __$$InvalidContractIdImplCopyWithImpl<T, _$InvalidContractIdImpl<T>>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return invalidContractId(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return invalidContractId?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (invalidContractId != null) {
      return invalidContractId(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return invalidContractId(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return invalidContractId?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (invalidContractId != null) {
      return invalidContractId(this);
    }
    return orElse();
  }
}

abstract class InvalidContractId<T> implements ValueFailure<T> {
  const factory InvalidContractId({required final T failedValue}) =
      _$InvalidContractIdImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$InvalidContractIdImplCopyWith<T, _$InvalidContractIdImpl<T>>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ShortPasswordImplCopyWith<T, $Res> {
  factory _$$ShortPasswordImplCopyWith(_$ShortPasswordImpl<T> value,
          $Res Function(_$ShortPasswordImpl<T>) then) =
      __$$ShortPasswordImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$ShortPasswordImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$ShortPasswordImpl<T>>
    implements _$$ShortPasswordImplCopyWith<T, $Res> {
  __$$ShortPasswordImplCopyWithImpl(_$ShortPasswordImpl<T> _value,
      $Res Function(_$ShortPasswordImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$ShortPasswordImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$ShortPasswordImpl<T>
    with DiagnosticableTreeMixin
    implements ShortPassword<T> {
  const _$ShortPasswordImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.shortPassword(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.shortPassword'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShortPasswordImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ShortPasswordImplCopyWith<T, _$ShortPasswordImpl<T>> get copyWith =>
      __$$ShortPasswordImplCopyWithImpl<T, _$ShortPasswordImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return shortPassword(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return shortPassword?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (shortPassword != null) {
      return shortPassword(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return shortPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return shortPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (shortPassword != null) {
      return shortPassword(this);
    }
    return orElse();
  }
}

abstract class ShortPassword<T> implements ValueFailure<T> {
  const factory ShortPassword({required final T failedValue}) =
      _$ShortPasswordImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$ShortPasswordImplCopyWith<T, _$ShortPasswordImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidPhotoUrlImplCopyWith<T, $Res> {
  factory _$$InvalidPhotoUrlImplCopyWith(_$InvalidPhotoUrlImpl<T> value,
          $Res Function(_$InvalidPhotoUrlImpl<T>) then) =
      __$$InvalidPhotoUrlImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$InvalidPhotoUrlImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$InvalidPhotoUrlImpl<T>>
    implements _$$InvalidPhotoUrlImplCopyWith<T, $Res> {
  __$$InvalidPhotoUrlImplCopyWithImpl(_$InvalidPhotoUrlImpl<T> _value,
      $Res Function(_$InvalidPhotoUrlImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$InvalidPhotoUrlImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$InvalidPhotoUrlImpl<T>
    with DiagnosticableTreeMixin
    implements InvalidPhotoUrl<T> {
  const _$InvalidPhotoUrlImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.invalidPhotoUrl(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.invalidPhotoUrl'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidPhotoUrlImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InvalidPhotoUrlImplCopyWith<T, _$InvalidPhotoUrlImpl<T>> get copyWith =>
      __$$InvalidPhotoUrlImplCopyWithImpl<T, _$InvalidPhotoUrlImpl<T>>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return invalidPhotoUrl(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return invalidPhotoUrl?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (invalidPhotoUrl != null) {
      return invalidPhotoUrl(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return invalidPhotoUrl(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return invalidPhotoUrl?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (invalidPhotoUrl != null) {
      return invalidPhotoUrl(this);
    }
    return orElse();
  }
}

abstract class InvalidPhotoUrl<T> implements ValueFailure<T> {
  const factory InvalidPhotoUrl({required final T failedValue}) =
      _$InvalidPhotoUrlImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$InvalidPhotoUrlImplCopyWith<T, _$InvalidPhotoUrlImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidPhoneNumberImplCopyWith<T, $Res> {
  factory _$$InvalidPhoneNumberImplCopyWith(_$InvalidPhoneNumberImpl<T> value,
          $Res Function(_$InvalidPhoneNumberImpl<T>) then) =
      __$$InvalidPhoneNumberImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$InvalidPhoneNumberImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$InvalidPhoneNumberImpl<T>>
    implements _$$InvalidPhoneNumberImplCopyWith<T, $Res> {
  __$$InvalidPhoneNumberImplCopyWithImpl(_$InvalidPhoneNumberImpl<T> _value,
      $Res Function(_$InvalidPhoneNumberImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$InvalidPhoneNumberImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$InvalidPhoneNumberImpl<T>
    with DiagnosticableTreeMixin
    implements InvalidPhoneNumber<T> {
  const _$InvalidPhoneNumberImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.invalidPhoneNumber(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.invalidPhoneNumber'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidPhoneNumberImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InvalidPhoneNumberImplCopyWith<T, _$InvalidPhoneNumberImpl<T>>
      get copyWith => __$$InvalidPhoneNumberImplCopyWithImpl<T,
          _$InvalidPhoneNumberImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return invalidPhoneNumber(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return invalidPhoneNumber?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (invalidPhoneNumber != null) {
      return invalidPhoneNumber(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return invalidPhoneNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return invalidPhoneNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (invalidPhoneNumber != null) {
      return invalidPhoneNumber(this);
    }
    return orElse();
  }
}

abstract class InvalidPhoneNumber<T> implements ValueFailure<T> {
  const factory InvalidPhoneNumber({required final T failedValue}) =
      _$InvalidPhoneNumberImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$InvalidPhoneNumberImplCopyWith<T, _$InvalidPhoneNumberImpl<T>>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWith<T, $Res> {
  factory _$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWith(
          _$NoIncomeOccupationWhenIncomeDeclaredImpl<T> value,
          $Res Function(_$NoIncomeOccupationWhenIncomeDeclaredImpl<T>) then) =
      __$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res,
        _$NoIncomeOccupationWhenIncomeDeclaredImpl<T>>
    implements _$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWith<T, $Res> {
  __$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWithImpl(
      _$NoIncomeOccupationWhenIncomeDeclaredImpl<T> _value,
      $Res Function(_$NoIncomeOccupationWhenIncomeDeclaredImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$NoIncomeOccupationWhenIncomeDeclaredImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$NoIncomeOccupationWhenIncomeDeclaredImpl<T>
    with DiagnosticableTreeMixin
    implements NoIncomeOccupationWhenIncomeDeclared<T> {
  const _$NoIncomeOccupationWhenIncomeDeclaredImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.noIncomeOccupationWhenIncomeDeclared(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty(
          'type', 'ValueFailure<$T>.noIncomeOccupationWhenIncomeDeclared'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NoIncomeOccupationWhenIncomeDeclaredImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWith<T,
          _$NoIncomeOccupationWhenIncomeDeclaredImpl<T>>
      get copyWith => __$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWithImpl<
          T, _$NoIncomeOccupationWhenIncomeDeclaredImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return noIncomeOccupationWhenIncomeDeclared(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return noIncomeOccupationWhenIncomeDeclared?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (noIncomeOccupationWhenIncomeDeclared != null) {
      return noIncomeOccupationWhenIncomeDeclared(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return noIncomeOccupationWhenIncomeDeclared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return noIncomeOccupationWhenIncomeDeclared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (noIncomeOccupationWhenIncomeDeclared != null) {
      return noIncomeOccupationWhenIncomeDeclared(this);
    }
    return orElse();
  }
}

abstract class NoIncomeOccupationWhenIncomeDeclared<T>
    implements ValueFailure<T> {
  const factory NoIncomeOccupationWhenIncomeDeclared(
          {required final T failedValue}) =
      _$NoIncomeOccupationWhenIncomeDeclaredImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$NoIncomeOccupationWhenIncomeDeclaredImplCopyWith<T,
          _$NoIncomeOccupationWhenIncomeDeclaredImpl<T>>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$IncomeExceedsLimitImplCopyWith<T, $Res> {
  factory _$$IncomeExceedsLimitImplCopyWith(_$IncomeExceedsLimitImpl<T> value,
          $Res Function(_$IncomeExceedsLimitImpl<T>) then) =
      __$$IncomeExceedsLimitImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T failedValue});
}

/// @nodoc
class __$$IncomeExceedsLimitImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$IncomeExceedsLimitImpl<T>>
    implements _$$IncomeExceedsLimitImplCopyWith<T, $Res> {
  __$$IncomeExceedsLimitImplCopyWithImpl(_$IncomeExceedsLimitImpl<T> _value,
      $Res Function(_$IncomeExceedsLimitImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failedValue = freezed,
  }) {
    return _then(_$IncomeExceedsLimitImpl<T>(
      failedValue: freezed == failedValue
          ? _value.failedValue
          : failedValue // ignore: cast_nullable_to_non_nullable
              as T,
    ));
  }
}

/// @nodoc

class _$IncomeExceedsLimitImpl<T>
    with DiagnosticableTreeMixin
    implements IncomeExceedsLimit<T> {
  const _$IncomeExceedsLimitImpl({required this.failedValue});

  @override
  final T failedValue;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.incomeExceedsLimit(failedValue: $failedValue)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ValueFailure<$T>.incomeExceedsLimit'))
      ..add(DiagnosticsProperty('failedValue', failedValue));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IncomeExceedsLimitImpl<T> &&
            const DeepCollectionEquality()
                .equals(other.failedValue, failedValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(failedValue));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IncomeExceedsLimitImplCopyWith<T, _$IncomeExceedsLimitImpl<T>>
      get copyWith => __$$IncomeExceedsLimitImplCopyWithImpl<T,
          _$IncomeExceedsLimitImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return incomeExceedsLimit(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return incomeExceedsLimit?.call(failedValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (incomeExceedsLimit != null) {
      return incomeExceedsLimit(failedValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return incomeExceedsLimit(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return incomeExceedsLimit?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (incomeExceedsLimit != null) {
      return incomeExceedsLimit(this);
    }
    return orElse();
  }
}

abstract class IncomeExceedsLimit<T> implements ValueFailure<T> {
  const factory IncomeExceedsLimit({required final T failedValue}) =
      _$IncomeExceedsLimitImpl<T>;

  T get failedValue;
  @JsonKey(ignore: true)
  _$$IncomeExceedsLimitImplCopyWith<T, _$IncomeExceedsLimitImpl<T>>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnexpectedImplCopyWith<T, $Res> {
  factory _$$UnexpectedImplCopyWith(
          _$UnexpectedImpl<T> value, $Res Function(_$UnexpectedImpl<T>) then) =
      __$$UnexpectedImplCopyWithImpl<T, $Res>;
}

/// @nodoc
class __$$UnexpectedImplCopyWithImpl<T, $Res>
    extends _$ValueFailureCopyWithImpl<T, $Res, _$UnexpectedImpl<T>>
    implements _$$UnexpectedImplCopyWith<T, $Res> {
  __$$UnexpectedImplCopyWithImpl(
      _$UnexpectedImpl<T> _value, $Res Function(_$UnexpectedImpl<T>) _then)
      : super(_value, _then);
}

/// @nodoc

class _$UnexpectedImpl<T>
    with DiagnosticableTreeMixin
    implements Unexpected<T> {
  const _$UnexpectedImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ValueFailure<$T>.unexpected()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('type', 'ValueFailure<$T>.unexpected'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnexpectedImpl<T>);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T failedValue, int max) exceedingLength,
    required TResult Function(T failedValue, int min) toShortLength,
    required TResult Function(T failedValue) empty,
    required TResult Function(T failedValue) multiline,
    required TResult Function(T failedValue, num max) numberTooLarge,
    required TResult Function(T failedValue, int max) listTooLong,
    required TResult Function(T failedValue) invalidEmail,
    required TResult Function(T failedValue) invalidPan,
    required TResult Function(T failedValue) invalidUjjwalCard,
    required TResult Function(T failedValue) invalidContractId,
    required TResult Function(T failedValue) shortPassword,
    required TResult Function(T failedValue) invalidPhotoUrl,
    required TResult Function(T failedValue) invalidPhoneNumber,
    required TResult Function(T failedValue)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(T failedValue) incomeExceedsLimit,
    required TResult Function() unexpected,
  }) {
    return unexpected();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T failedValue, int max)? exceedingLength,
    TResult? Function(T failedValue, int min)? toShortLength,
    TResult? Function(T failedValue)? empty,
    TResult? Function(T failedValue)? multiline,
    TResult? Function(T failedValue, num max)? numberTooLarge,
    TResult? Function(T failedValue, int max)? listTooLong,
    TResult? Function(T failedValue)? invalidEmail,
    TResult? Function(T failedValue)? invalidPan,
    TResult? Function(T failedValue)? invalidUjjwalCard,
    TResult? Function(T failedValue)? invalidContractId,
    TResult? Function(T failedValue)? shortPassword,
    TResult? Function(T failedValue)? invalidPhotoUrl,
    TResult? Function(T failedValue)? invalidPhoneNumber,
    TResult? Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(T failedValue)? incomeExceedsLimit,
    TResult? Function()? unexpected,
  }) {
    return unexpected?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T failedValue, int max)? exceedingLength,
    TResult Function(T failedValue, int min)? toShortLength,
    TResult Function(T failedValue)? empty,
    TResult Function(T failedValue)? multiline,
    TResult Function(T failedValue, num max)? numberTooLarge,
    TResult Function(T failedValue, int max)? listTooLong,
    TResult Function(T failedValue)? invalidEmail,
    TResult Function(T failedValue)? invalidPan,
    TResult Function(T failedValue)? invalidUjjwalCard,
    TResult Function(T failedValue)? invalidContractId,
    TResult Function(T failedValue)? shortPassword,
    TResult Function(T failedValue)? invalidPhotoUrl,
    TResult Function(T failedValue)? invalidPhoneNumber,
    TResult Function(T failedValue)? noIncomeOccupationWhenIncomeDeclared,
    TResult Function(T failedValue)? incomeExceedsLimit,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ExceedingLength<T> value) exceedingLength,
    required TResult Function(ToShortLength<T> value) toShortLength,
    required TResult Function(Empty<T> value) empty,
    required TResult Function(Multiline<T> value) multiline,
    required TResult Function(NumberTooLarge<T> value) numberTooLarge,
    required TResult Function(ListTooLong<T> value) listTooLong,
    required TResult Function(InvalidEmail<T> value) invalidEmail,
    required TResult Function(InvalidPan<T> value) invalidPan,
    required TResult Function(InvalidUjjwalCard<T> value) invalidUjjwalCard,
    required TResult Function(InvalidContractId<T> value) invalidContractId,
    required TResult Function(ShortPassword<T> value) shortPassword,
    required TResult Function(InvalidPhotoUrl<T> value) invalidPhotoUrl,
    required TResult Function(InvalidPhoneNumber<T> value) invalidPhoneNumber,
    required TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)
        noIncomeOccupationWhenIncomeDeclared,
    required TResult Function(IncomeExceedsLimit<T> value) incomeExceedsLimit,
    required TResult Function(Unexpected<T> value) unexpected,
  }) {
    return unexpected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ExceedingLength<T> value)? exceedingLength,
    TResult? Function(ToShortLength<T> value)? toShortLength,
    TResult? Function(Empty<T> value)? empty,
    TResult? Function(Multiline<T> value)? multiline,
    TResult? Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult? Function(ListTooLong<T> value)? listTooLong,
    TResult? Function(InvalidEmail<T> value)? invalidEmail,
    TResult? Function(InvalidPan<T> value)? invalidPan,
    TResult? Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult? Function(InvalidContractId<T> value)? invalidContractId,
    TResult? Function(ShortPassword<T> value)? shortPassword,
    TResult? Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult? Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult? Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult? Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult? Function(Unexpected<T> value)? unexpected,
  }) {
    return unexpected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ExceedingLength<T> value)? exceedingLength,
    TResult Function(ToShortLength<T> value)? toShortLength,
    TResult Function(Empty<T> value)? empty,
    TResult Function(Multiline<T> value)? multiline,
    TResult Function(NumberTooLarge<T> value)? numberTooLarge,
    TResult Function(ListTooLong<T> value)? listTooLong,
    TResult Function(InvalidEmail<T> value)? invalidEmail,
    TResult Function(InvalidPan<T> value)? invalidPan,
    TResult Function(InvalidUjjwalCard<T> value)? invalidUjjwalCard,
    TResult Function(InvalidContractId<T> value)? invalidContractId,
    TResult Function(ShortPassword<T> value)? shortPassword,
    TResult Function(InvalidPhotoUrl<T> value)? invalidPhotoUrl,
    TResult Function(InvalidPhoneNumber<T> value)? invalidPhoneNumber,
    TResult Function(NoIncomeOccupationWhenIncomeDeclared<T> value)?
        noIncomeOccupationWhenIncomeDeclared,
    TResult Function(IncomeExceedsLimit<T> value)? incomeExceedsLimit,
    TResult Function(Unexpected<T> value)? unexpected,
    required TResult orElse(),
  }) {
    if (unexpected != null) {
      return unexpected(this);
    }
    return orElse();
  }
}

abstract class Unexpected<T> implements ValueFailure<T> {
  const factory Unexpected() = _$UnexpectedImpl<T>;
}
