import 'dart:math' as math;

import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'filter_id.dart';

part 'filter_options.freezed.dart';

enum FilterType { single, multi, range }

enum RequestType { pageItems, filterOptions, all }

@freezed
class FilterOptions with _$FilterOptions {
  const factory FilterOptions({
    required int totalCount,
    required List<Filter> filters,
  }) = _FilterOptions;

  const FilterOptions._();

  factory FilterOptions.empty() => const FilterOptions(totalCount: 0, filters: []);

  FilterOptions merge(FilterOptions other) {
    final finalFilters = <Filter>[];

    final filterMapOther = <FilterId, bool>{};
    final entries = other.filters.map((e) => MapEntry<FilterId, bool>(e.id, false));
    filterMapOther.addEntries(entries);

    for (final filter in filters) {
      final filterInOther = other.filters.firstWhereOrNull((e) => e.id == filter.id);
      if (filterInOther != null) {
        finalFilters.add(filter.merge(filterInOther));
        filterMapOther[filterInOther.id] = true;
      } else {
        finalFilters.add(filter);
      }
    }

    filterMapOther.removeWhere((key, value) => value);
    final unprocessedFilterIds = filterMapOther.keys.toList();
    final otherFilters = other.filters..retainWhere((x) => unprocessedFilterIds.remove(x.id));

    finalFilters.addAll(otherFilters);

    return FilterOptions(
      filters: finalFilters,
      totalCount: totalCount + other.totalCount,
    );
  }

  Filter? get categoryFilter {
    final categoryFilter = filters.firstWhereOrNull(
      (e) => e.id == const FilterId.categories(),
    );

    if (categoryFilter == null) return null;

    return categoryFilter;
  }
}

@freezed
class Filter with _$Filter {
  const factory Filter({
    required FilterId id,
    bool? quickFilter,
    String? imageUrl,
    String? iconUrl,
    String? label,
    FilterType? filterType,
    double? min,
    double? max,
    double? initialMin,
    double? initialMax,
    int? divisions,
    required List<FilterItem> items,
  }) = _Filter;

  const Filter._();

  Filter merge(Filter? other) {
    // ignore: avoid_returning_this
    if (other == null) return this;

    final finalMin = getSmaller(min, other.min);
    final finalMax = getGreater(max, other.max);
    final finalInitialMin = getSmaller(initialMin, other.initialMin);
    final finalInitialMax = getGreater(initialMax, other.initialMax);
    final finalDivisions = getSmallerInt(divisions, other.divisions);

    final finalItems = items + other.items;
    final ids = finalItems.map((e) => e.id).toSet();
    finalItems.retainWhere((x) => ids.remove(x.id));

    return copyWith(
      min: finalMin,
      max: finalMax,
      initialMin: finalInitialMin,
      initialMax: finalInitialMax,
      divisions: finalDivisions,
      items: finalItems,
    );
  }

  int? getSmallerInt(int? first, int? second) {
    if (first != null && second != null) {
      return math.min(first, second);
    }

    return first ?? second;
  }

  double? getSmaller(double? first, double? second) {
    if (first != null && second != null) {
      return math.min(first, second);
    }

    return first ?? second;
  }

  double? getGreater(double? first, double? second) {
    if (first != null && second != null) {
      return math.max(first, second);
    }

    return first ?? second;
  }

  bool get isActive =>
      (filterType == FilterType.range &&
          ((min != initialMin && initialMin != null) || (max != initialMax && initialMax != null))) ||
      items.firstWhereOrNull((e) => e.selected ?? false) != null;
}

@freezed
class FilterItem with _$FilterItem {
  const factory FilterItem({
    required String id,
    String? label,
    int? count,
    String? imageUrl,
    String? iconUrl,
    double? min,
    double? max,
    bool? selected,
  }) = _FilterItem;
}

@freezed
class ApplyFilters with _$ApplyFilters {
  const factory ApplyFilters({
    RequestType? requestType,
    List<FilterSelection>? items,
  }) = _ApplyFilters;
}

@freezed
class FilterSelection with _$FilterSelection {
  const factory FilterSelection({
    required FilterId id,
    double? min,
    double? max,
    List<String>? selectedIds,
  }) = _FilterSelection;
}
