class EnvironmentUrls {
  EnvironmentUrl main;
  EnvironmentUrl? stage;
  EnvironmentUrl? performance;
  bool isFromRc;

  EnvironmentUrls({
    required this.main,
    this.stage,
    this.performance,
    this.isFromRc = false,
  });

  @override
  String toString() {
    return '''
  EnvironmentUrls {
  isFromRc: $isFromRc,
  main: $main,
  stage: ${stage?.toString() ?? 'null'},
  performance: ${performance?.toString() ?? 'null'},

}''';
  }
}

class EnvironmentUrl {
  String baseUrl;
  String identityUrl;
  String flagsmithUrl;

  String? commonBaseUrl;
  String? deeplinkUrl;
  String? dynamicLinkUrl;
  String? cdnImageBaseUrl;
  String? imageBaseUrl;
  String? insiderBaseUrl;
  String? oneLinkUrl;

// firebase remote config settings
//   {
//   "main": {
//     "baseUrl": "https://api.ph.hcgma.com/",
//     "identityUrl": "https://identity.ph.hcgma.com/",
//     "flagsmithUrl": "https://api.flagsmith.com/api/v1/",
//     "imageBaseUrl": "https://files.ph.hcgma.com/image/",
//     "insiderBaseUrl": "https://insider.ph.hcgma.com/",
//     "cdnImageBaseUrl": "https://cdn.ph.hcgma.com/image/",
//     "commonBaseUrl": "https://api.ph.hcgma.com/",
//     "deeplinkUrl": "https://app.gma.homecredit.ph/",
//     "dynamicLinkUrl": "https://hcphcapp.page.link/",
//     "oneLinkUrl": "https://hcv.onelink.me/"
//   },
//   "staging": {
//     "baseUrl": "https://api.ph.hcgma.com/stage/",
//     "identityUrl": "https://identity.ph.hcgma.com/",
//     "flagsmithUrl": "https://api.flagsmith.com/api/v1/",
//     "imageBaseUrl": "https://files.ph.hcgma.com/image/",
//     "insiderBaseUrl": "https://insider.ph.hcgma.com/",
//     "cdnImageBaseUrl": "https://cdn.ph.hcgma.com/image/",
//     "commonBaseUrl": "https://api.ph.hcgma.com/",
//     "deeplinkUrl": "https://app.gma.homecredit.ph/",
//     "dynamicLinkUrl": "https://hcphcapp.page.link/",
//     "oneLinkUrl": "https://hcv.onelink.me/"
//   },
//   "performance": {
//     "baseUrl": "https://api.ph.hcgma.com/",
//     "identityUrl": "https://identity.ph.hcgma.com/",
//     "flagsmithUrl": "https://api.flagsmith.com/api/v1/",
//     "imageBaseUrl": "https://files.ph.hcgma.com/image/",
//     "insiderBaseUrl": "https://insider.ph.hcgma.com/",
//     "cdnImageBaseUrl": "https://cdn.ph.hcgma.com/image/",
//     "commonBaseUrl": "https://api.ph.hcgma.com/",
//     "deeplinkUrl": "https://app.gma.homecredit.ph/",
//     "dynamicLinkUrl": "https://hcphcapp.page.link/",
//     "oneLinkUrl": "https://hcv.onelink.me/"
//   }
// }
  EnvironmentUrl({
    required this.baseUrl,
    required this.identityUrl,
    required this.flagsmithUrl,
    this.commonBaseUrl,
    this.deeplinkUrl,
    this.dynamicLinkUrl,
    this.imageBaseUrl,
    this.insiderBaseUrl,
    this.cdnImageBaseUrl,
    this.oneLinkUrl,
  });

  Map<String, dynamic> toMap() {
    return {
      'baseUrl': baseUrl,
      'identityUrl': identityUrl,
      'flagsmithUrl': flagsmithUrl,
      'imageBaseUrl': imageBaseUrl,
      'insiderBaseUrl': insiderBaseUrl,
      'cdnImageBaseUrl': cdnImageBaseUrl,
      'commonBaseUrl': commonBaseUrl,
      'deeplinkUrl': deeplinkUrl,
      'dynamicLinkUrl': dynamicLinkUrl,
      'oneLinkUrl': oneLinkUrl,
    };
  }

  factory EnvironmentUrl.fromMap(Map<String, dynamic> map) {
    return EnvironmentUrl(
      baseUrl: map['baseUrl'] as String,
      identityUrl: map['identityUrl'] as String,
      flagsmithUrl: map['flagsmithUrl'] as String,
      imageBaseUrl: map['imageBaseUrl'] as String?,
      insiderBaseUrl: map['insiderBaseUrl'] as String?,
      cdnImageBaseUrl: map['cdnImageBaseUrl'] as String?,
      commonBaseUrl: map['commonBaseUrl'] as String?,
      deeplinkUrl: map['deeplinkUrl'] as String?,
      dynamicLinkUrl: map['dynamicLinkUrl'] as String?,
      oneLinkUrl: map['oneLinkUrl'] as String?,
    );
  }

  @override
  String toString() {
    return '''
  EnvironmentUrl {
  baseUrl: $baseUrl,
  identityUrl: $identityUrl,
  flagsmithUrl: $flagsmithUrl,
  commonBaseUrl: $commonBaseUrl,
  deeplinkUrl: $deeplinkUrl,
  dynamicLinkUrl: $dynamicLinkUrl,
  cdnImageBaseUrl: $cdnImageBaseUrl,
  imageBaseUrl: $imageBaseUrl,
  insiderBaseUrl: $insiderBaseUrl,
  oneLinkUrl: $oneLinkUrl
}''';
  }
}
