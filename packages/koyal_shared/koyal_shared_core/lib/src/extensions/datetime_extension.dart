import 'package:intl/intl.dart';

import '../infrastructure/configuration/format_config.dart';

extension DateTimeX on DateTime {
  static String? _shortFormat;
  static String? _mediumFormat;
  static String? _longFormat;
  static String? _intervalFormat;

  static void init(FormatConfig format) {
    _shortFormat = format.shortFormat;
    _mediumFormat = format.mediumFormat;
    _longFormat = format.longFormat;
    _intervalFormat = format.intervalFormat;
  }

  String shortDate([String? locale]) => DateFormat(_shortFormat).format(this);
  String mediumDate([String? locale]) => DateFormat(_mediumFormat).format(this);
  String longDate() => DateFormat(_longFormat).format(this);
  String dayName([String? locale]) => DateFormat.EEEE(locale).format(this);
  String monthName([String? locale]) => DateFormat.LLLL(locale).format(this);
  DateTime midnight() => DateTime(year, month, day, 23, 59, 59);

  String intervalTo(DateTime to, [String? locale]) {
    return '${DateFormat(_intervalFormat).format(this)} - ${DateFormat(_intervalFormat).format(to)}';
  }

  String shortTime({bool is24 = true, String? locale}) {
    return is24 ? DateFormat.Hm(locale).format(this) : DateFormat.jm(locale).format(this);
  }

  String longTime({bool is24 = true, String? locale}) {
    return is24 ? DateFormat.Hms(locale).format(this) : DateFormat.jms(locale).format(this);
  }

  bool isToday() {
    final now = DateTime.now();
    return now.day == day && now.month == month && now.year == year;
  }

  bool isYesterday() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return yesterday.day == day && yesterday.month == month && yesterday.year == year;
  }

  bool isLeapYear() {
    if (year % 4 != 0) {
      return false;
    } else if (year % 100 != 0) {
      return true;
    } else if (year % 400 != 0) {
      return false;
    } else {
      return true;
    }
  }

  DateTime reduceOrAddYears({
    required int yearsNumber,
    required bool isAdd,
  }) {
    final currentDate = DateTime.now();
    var newDate = DateTime(
      isAdd ? (currentDate.year + yearsNumber) : (currentDate.year - yearsNumber),
      currentDate.month,
      currentDate.day,
    );

    if (currentDate.month == 2 && currentDate.day == 29 && !DateTime(newDate.year).isLeapYear()) {
      newDate = DateTime(newDate.year, 2, 28);
    }
    return newDate;
  }
}
