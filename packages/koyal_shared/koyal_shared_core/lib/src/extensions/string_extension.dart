//ignore_for_file: avoid_catches_without_on_clauses

extension StringExt on String {
  bool get isVideo =>
      toLowerCase().contains('.avi') ||
      toLowerCase().contains('.mpv') ||
      toLowerCase().contains('.mp4') ||
      toLowerCase().contains('.mpg') ||
      toLowerCase().contains('.mov') ||
      toLowerCase().contains('.wmv');
  bool get isBitmapImage =>
      toLowerCase().contains('.jpg') ||
      toLowerCase().contains('.jpeg') ||
      toLowerCase().contains('.bmp') ||
      toLowerCase().contains('.png') ||
      toLowerCase().contains('.tiff') ||
      toLowerCase().contains('.gif');
  bool get isVector =>
      toLowerCase().contains('.svg') ||
      toLowerCase().contains('.ai') ||
      toLowerCase().contains('.drw') ||
      toLowerCase().contains('.eps') ||
      toLowerCase().contains('.ps');

  bool get isLocalImage => toLowerCase().contains(isLocalConst);
  static String isLocalConst = 'local://';

  String capitalizeFirstLetter() {
    return '${this[0].toUpperCase()}${substring(1)}';
  }

  String removeMarkdown({int previewLength = 128}) {
    var input = this;
    if (previewLength > 0 && input.length > previewLength) {
      input = input.substring(0, previewLength);
    }
    try {
      // Remove horizontal rules
      return input
          .replaceAll(RegExp(r'^(-\s*?|\*\s*?|_\s*?){3}$', multiLine: true), '')
          .replaceAllMapped(
            RegExp(r'^([\s\t]*)([\*\-\+]|\d+\.)\s+', multiLine: true),
            (match) => match.group(1) ?? '',
          )
          .replaceAll(RegExp(r'^#{1,6}\s', multiLine: true), '') // Header
          .replaceAll(RegExp('~~'), '') // Strikethrough
          .replaceAll(RegExp('<[^>]*>'), '') // Remove HTML tags
          .replaceAll(
            RegExp(r'^[=\-]{1,3}\s*$', multiLine: true),
            '',
          ) // Remove setext-style headers
          // Remove emphasis
          .replaceAllMapped(
            RegExp(r'([\*_]{1,3})(\S.*?\S{0,1})\1'),
            (match) => match.group(2) ?? '',
          )
          .replaceAll(
            RegExp(r'\!\[(.*?)\][\[\(].*?[\]\)]'),
            '',
          ) // Remove images
          .replaceAllMapped(
            RegExp(r'\[(.*?)\][\[\(].*?[\]\)]'),
            (match) => match.group(1) ?? '',
          ) // Remove reference-style links
          .replaceAll(
            RegExp(r'^\s{1,2}\[(.*?)\]: (\S+)( ".*?")?\s*$', multiLine: true),
            '',
          )
          .trim();
    } catch (e) {
      return this;
    }
  }
}

extension StringNullExt on String? {
  bool equalsIgnoreCase(String? value) =>
      (this == null && value == null) || (this != null && value != null && this!.toLowerCase() == value.toLowerCase());
}
