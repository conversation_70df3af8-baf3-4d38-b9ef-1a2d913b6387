//later we should move this to remote config constant file
// ignore_for_file: avoid_dynamic_calls

import 'dart:convert';

import '../../../koyal_shared_core.dart';

enum AppConfigUrlStrongType { main, staging, performance }

AppConfigUrlModel parseAppConfigUrlByType(String value, {AppConfigUrlStrongType type = AppConfigUrlStrongType.main}) {
  final Map<String, dynamic> map = jsonDecode(value);
  switch (type) {
    case AppConfigUrlStrongType.main:
      return AppConfigUrlModel(
        baseUrl: map['main']['baseUrl'],
        identityUrl: map['main']['identityUrl'],
        flagsmithUrl: map['main']['flagsmithUrl'],
        imageBaseUrl: map['main']['imageBaseUrl'],
        insiderBaseUrl: map['main']['insiderBaseUrl'],
        commonBaseUrl: map['main']['commonBaseUrl'],
        deeplinkUrl: map['main']['deeplinkUrl'],
        dynamicLinkUrl: map['main']['dynamicLinkUrl'],
        cdnImageBaseUrl: map['main']['cdnImageBaseUrl'],
        oneLinkUrl: map['main']['oneLinkUrl'],
      );
    case AppConfigUrlStrongType.staging:
      return AppConfigUrlModel(
        baseUrl: map['staging']['baseUrl'],
        identityUrl: map['staging']['identityUrl'],
        flagsmithUrl: map['staging']['flagsmithUrl'],
        imageBaseUrl: map['staging']['imageBaseUrl'],
        insiderBaseUrl: map['staging']['insiderBaseUrl'],
        commonBaseUrl: map['staging']['commonBaseUrl'],
        deeplinkUrl: map['staging']['deeplinkUrl'],
        dynamicLinkUrl: map['staging']['dynamicLinkUrl'],
        cdnImageBaseUrl: map['staging']['cdnImageBaseUrl'],
        oneLinkUrl: map['staging']['oneLinkUrl'],
      );
    case AppConfigUrlStrongType.performance:
      return AppConfigUrlModel(
        baseUrl: map['performance']['baseUrl'],
        identityUrl: map['performance']['identityUrl'],
        flagsmithUrl: map['performance']['flagsmithUrl'],
        imageBaseUrl: map['performance']['imageBaseUrl'],
        insiderBaseUrl: map['performance']['insiderBaseUrl'],
        commonBaseUrl: map['performance']['commonBaseUrl'],
        deeplinkUrl: map['performance']['deeplinkUrl'],
        dynamicLinkUrl: map['performance']['dynamicLinkUrl'],
        cdnImageBaseUrl: map['performance']['cdnImageBaseUrl'],
        oneLinkUrl: map['performance']['oneLinkUrl'],
      );
  }
}

extension AppConfigUrlModelExtension on AppConfigUrlModel {
  EnvironmentUrl toUrl() {
    return EnvironmentUrl(
      baseUrl: baseUrl,
      identityUrl: identityUrl,
      flagsmithUrl: flagsmithUrl,
      imageBaseUrl: imageBaseUrl,
      insiderBaseUrl: insiderBaseUrl,
      commonBaseUrl: commonBaseUrl,
      cdnImageBaseUrl: cdnImageBaseUrl,
      deeplinkUrl: deeplinkUrl,
      dynamicLinkUrl: dynamicLinkUrl,
      oneLinkUrl: oneLinkUrl,
    );
  }
}

class AppConfigUrlModel {
  String baseUrl;
  String identityUrl;
  String flagsmithUrl;
  String imageBaseUrl;
  String insiderBaseUrl;
  String cdnImageBaseUrl;
  String commonBaseUrl;
  String deeplinkUrl;
  String dynamicLinkUrl;
  String? oneLinkUrl;

  AppConfigUrlModel({
    required this.baseUrl,
    required this.identityUrl,
    required this.flagsmithUrl,
    required this.imageBaseUrl,
    required this.insiderBaseUrl,
    required this.cdnImageBaseUrl,
    required this.commonBaseUrl,
    required this.deeplinkUrl,
    required this.dynamicLinkUrl,
    required this.oneLinkUrl,
  });
}
