import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../application/feature_flag/feature_flag_bloc.dart';

extension FeatureFlagContextExtension on BuildContext {
  bool isFlagEnabledRead(String featureName) {
    return read<FeatureFlagBloc>().isFlagEnabledCached(featureName);
  }

  String? getFlagValueRead(String featureName) {
    return read<FeatureFlagBloc>().getFlagValueCached(featureName);
  }

// internally uses select, but conditions are same as for .watch

  String? getFlagValueWatch(String featureName) {
    return select<FeatureFlagBloc, String?>(
      (b) => b.getFlagValueCached(featureName),
    );
  }

  // internally uses select, but conditions are same as for .watch
  bool isFlagEnabledWatch(String featureName) {
    return select<FeatureFlagBloc, bool>(
      (b) => b.isFlagEnabledCached(featureName),
    );
  }
}
