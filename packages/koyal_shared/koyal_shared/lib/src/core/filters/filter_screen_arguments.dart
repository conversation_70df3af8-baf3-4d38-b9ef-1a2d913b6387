import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'i_filter_source.dart';

class FilterScreenArguments extends ScreenArguments {
  final IFilterSource? filterSource;
  final Function(List<Filter>)? onFilterApply;
  final FilterOptions? filterOptions;
  final List<FilterSelection>? activeFilters;
  final Function? onRetry;
  final bool showItemCount;

  FilterScreenArguments({
    this.filterSource,
    this.onFilterApply,
    this.filterOptions,
    this.activeFilters,
    this.onRetry,
    this.showItemCount = false,
  });
}
