import 'package:get_it/get_it.dart';
import 'package:gma_storage/gma_storage.dart';
import 'package:logger/logger.dart';

import 'domain/location/i_location_repository.dart';
import 'infrastructure/location/location_repository.dart';
import 'infrastructure/proxy/proxy_url_storage.dart';
import 'infrastructure/service/location/i_default_position_provider.dart';
import 'infrastructure/service/location/i_location_service.dart';
import 'infrastructure/service/location/test_fake_location_service.dart';
import 'koyal_shared_fake.dart';
import 'koyal_shared_settings.dart';

class KoyalSharedTest extends KoyalSharedFake {
  KoyalSharedTest({required KoyalSharedSettings settings}) : super(settings: settings);

  @override
  void registerPersistentStorages(GetIt c) {
    c
      ..registerLazySingleton<ILocationRepository>(
        () => LocationRepository(
          storage: ReactiveInMemoryStorageProvider(logger: c<Logger>()),
        ),
      )
      ..registerLazySingleton(
        () => ProxyUrlStorage(
          storage: ReactiveInMemoryStorageProvider(logger: c<Logger>()),
        ),
      );
  }

  @override
  void registerFakeLocationService(GetIt c) {
    c.registerLazySingleton<ILocationService>(
      () => TestFakeLocationService(
        defaultPositionProvider: c<IDefaultPositionProvider>(),
      ),
    );
  }
}
