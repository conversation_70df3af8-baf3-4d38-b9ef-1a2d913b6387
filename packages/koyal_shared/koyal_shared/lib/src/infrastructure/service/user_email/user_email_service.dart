// ignore_for_file: avoid_print

import 'package:account_manager/account_manager.dart';
import 'package:flutter/services.dart';
import 'package:gma_platform/gma_platform.dart';


class UserEmailService {
  static final RegExp _emailRegExp = RegExp(
    r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+",
  );

  Future<List<String>> getUserEmails() async {
    if (GmaPlatform.isAndroid) {
      try {
        final accounts = await AccountManager.getAllAccounts();

        return accounts.where(_emailRegExp.hasMatch).toSet().toList();
      } on PlatformException catch (e) {
        print('UserEmailService accounts: $e');
        return [];
      }
    }
    return List.of([]);
  }
}
