import 'dart:async';

import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart' hide ServiceStatus;

import '../../../domain/location/i_location_repository.dart';
import 'i_default_position_provider.dart';
import 'i_location_service.dart';

class LocationService implements ILocationService {
  final Logger logger;
  final IPermissionsService permissionsService;
  final IDefaultPositionProvider defaultPositionProvider;
  final ILocationRepository locationRepository;

  LocationService({
    required this.logger,
    required this.permissionsService,
    required this.defaultPositionProvider,
    required this.locationRepository,
  });

  @override
  Future<Position?> getPosition({
    LocationAccuracy accuracy = LocationAccuracy.medium,
    bool showPermissionDialog = true,
    bool allowDefaultLocation = true,
  }) async {
    if (!await hasPermission(showPermissionDialog: showPermissionDialog)) return null;
    final useRealLocation = await locationRepository.loadUseRealLocation();

    if (useRealLocation) {
      return _getCurrentOrLastKnownPosition();
    }

    final fakePosition = await locationRepository.getLastFakeLocation();
    if (fakePosition != null) {
      return Position(
        latitude: fakePosition.lat,
        longitude: fakePosition.lng,
        accuracy: 0,
        altitude: 0,
        heading: 0,
        speed: 0,
        speedAccuracy: 0,
        timestamp: DateTime.now(),
        altitudeAccuracy: 0,
        headingAccuracy: 0,
      );
    }

    return getDefaultPosition();
  }

  @override
  Position getDefaultPosition() {
    return defaultPositionProvider.position;
  }

  @override
  Future<bool> hasPermission({bool showPermissionDialog = true}) async =>
      await _checkAppPermission(showPermissionDialog: showPermissionDialog) && await hasDevicePermission();

  @override
  Future<bool>? hasAppPermission({bool showPermissionDialog = true}) async {
    try {
      if (showPermissionDialog) {
        return await permissionsService.request(Permission.locationWhenInUse) == PermissionStatus.granted;
      } else {
        return await permissionsService.getStatus(Permission.locationWhenInUse) == PermissionStatus.granted;
      }
    } on PlatformException catch (exception) {
      logger.w('App Permission for location not granted: ${exception.code}');
      return false;
    }
  }

  @override
  Future<bool> hasDevicePermission() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } on PlatformException catch (exception) {
      logger.w('Device Permission for location not granted: ${exception.code}');
      return false;
    }
  }

  @override
  Future<Position?>? getDebouncedPosition({
    double debounceDiameter = 1000,
    bool showPermissionDialog = true,
  }) async {
    if (!await hasPermission(showPermissionDialog: showPermissionDialog)) return null;
    Position? position;
    try {
      final debouncePositionMap = await locationRepository.loadDebouncedPosition();
      position = await getPosition(accuracy: LocationAccuracy.low);
      if (debouncePositionMap != null) {
        final debouncePosition = Position(
          latitude: debouncePositionMap.lat,
          longitude: debouncePositionMap.lng,
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          timestamp: DateTime.now(),
          headingAccuracy: 0,
          altitudeAccuracy: 0,
        );
        final isInDiameter = await _isInDistance(debouncePosition, position, debounceDiameter);
        if (isInDiameter || position == null) {
          position = debouncePosition;
        }
      } else if (position != null) {
        await locationRepository.saveDebouncedPosition(position);
      }
    } on PermissionDeniedException catch (exception) {
      logger.w('[$runtimeType]: ${exception.message}');
    }
    return position;
  }

  Future<Position?> _getCurrentOrLastKnownPosition({
    LocationAccuracy accuracy = LocationAccuracy.medium,
    bool allowDefaultLocation = true,
  }) async {
    Position? position;
    try {
      position = await Geolocator.getCurrentPosition(desiredAccuracy: accuracy).timeout(const Duration(seconds: 15));
    } on TimeoutException {
      logger.e('Permission not returned in time, probably an android emulator');
      position = await getLastKnownPosition();
      if (allowDefaultLocation) {
        //If position still be null, using DefaultPosition if allowed
        position ??= getDefaultPosition();
      }
    } on PermissionDeniedException catch (exception) {
      logger.w('[$runtimeType]: ${exception.message}');
    } on PlatformException catch (exception) {
      logger.w('Permission for location not granted: ${exception.code}');
    }
    return position;
  }

  Future<bool> _isInDistance(
    Position p1,
    Position? p2,
    double distanceMeters,
  ) async {
    if (p2 == null) {
      return false;
    }
    if (distanceMeters <= 0) {
      return true;
    }
    return (Geolocator.distanceBetween(
          p1.latitude,
          p1.longitude,
          p2.latitude,
          p2.longitude,
        )) <=
        distanceMeters;
  }

  @override
  Future<Position?> getLastKnownPosition() async {
    Position? position;
    try {
      position = await Geolocator.getLastKnownPosition();
    } on PermissionDeniedException catch (exception) {
      logger.w('[$runtimeType]: ${exception.message}');
    }
    return position;
  }

  @override
  Stream<bool> get deviceLocationEnabledStream =>
      Geolocator.getServiceStatusStream().map((event) => event == ServiceStatus.enabled);

  Future<bool> _checkAppPermission({bool showPermissionDialog = true}) async {
    return await hasAppPermission(showPermissionDialog: showPermissionDialog) ?? false;
  }

  @override
  Future<Position?> getCurrentPosition({LocationAccuracy accuracy = LocationAccuracy.medium}) async {
    Position? position;
    try {
      position = await Geolocator.getCurrentPosition(desiredAccuracy: accuracy).timeout(const Duration(seconds: 15));
    } on TimeoutException {
      logger.e('Permission not returned in time, probably an android emulator');
    } on PermissionDeniedException catch (exception) {
      logger.w('[$runtimeType]: ${exception.message}');
    } on PlatformException catch (exception) {
      logger.w('Permission for location not granted: ${exception.code}');
    }
    return position;
  }
}
