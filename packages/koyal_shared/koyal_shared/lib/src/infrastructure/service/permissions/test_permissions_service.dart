import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:permission_handler/permission_handler.dart';

/// Workaround the issue in UI tests, when we are not able
/// to confirm phone dialog
class TestPermissionsService implements IPermissionsService {
  @override
  Future<PermissionStatus> getStatus(Permission permission) async {
    return PermissionStatus.granted;
  }

  @override
  Future<PermissionStatus> request(Permission permission) async {
    return PermissionStatus.granted;
  }

  @override
  List<Permission> getConsentPermissions() {
    return [];
  }

  @override
  List<Permission> getCommunicationsPermissions() {
    return [];
  }

  @override
  Future<void> clearLocationPermissions() {
    return Future.value();
  }

  @override
  List<Permission> getFinboxPermissions() {
    return [];
  }
}
