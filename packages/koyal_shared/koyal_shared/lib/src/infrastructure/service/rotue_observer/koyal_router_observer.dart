import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:koyal_core/koyal_core.dart';

typedef KoyalScreenEvent = Function(RouteSettings settings);

class KoyalRouterObserver extends RouteObserver<PageRoute<dynamic>> {
  final KoyalScreenEvent screenEvent;

  KoyalRouterObserver({required this.screenEvent});

  void _sendScreenView(PageRoute<dynamic> route) => screenEvent(route.settings);

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    if (previousRoute?.settings.name == 'ongoing_maintenance' && route.settings.name != 'aboutus/menu') {
      final args =
          previousRoute?.settings.arguments != null ? previousRoute!.settings.arguments! as RouteArguments : null;
      Future.microtask(() {
        navigator?.pushReplacement(
          MaterialPageRoute(
            settings: RouteSettings(name: previousRoute?.settings.name),
            builder: (context) => OngoingMaintenanceScreen(
              arguments: args?.arguments != null
                  ? args!.arguments! as MaintenanceScreenArguments
                  : MaintenanceScreenArguments(
                      endDate: DateTime.now(),
                    ),
            ),
          ),
        );
      });
    } else {
      super.didPush(route, previousRoute);
      if (route is PageRoute) {
        _sendScreenView(route);
      }
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute is PageRoute) {
      _sendScreenView(newRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute is PageRoute && route is PageRoute) {
      _sendScreenView(previousRoute);
    }
  }
}
