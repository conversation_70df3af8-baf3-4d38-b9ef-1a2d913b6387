import 'package:koyal_core/koyal_core.dart';
import 'package:logger/logger.dart';

import '../tracking/events/event_tracking_service.dart';
import '../tracking/events/global_tracking_properties.dart';
import 'koyal_router_observer.dart';

class ScreenNameRouterObserver extends KoyalRouterObserver {
  final EventTrackingService eventTrackingService;
  final Logger logger;
  final Map<String, String> userDefaultProperty;
  final GlobalTrackingProperties? gtp;

  ScreenNameRouterObserver({
    required this.eventTrackingService,
    required this.logger,
    required this.userDefaultProperty,
    this.gtp,
  }) : super(
          screenEvent: (settings) {
            var businessRouteName = settings.name;

            if (settings.arguments is RouteArguments?) {
              final router = settings.arguments as RouteArguments?;
              // take the value from ScreenArguments, if empty take value from route, if empty name (url) of the route
              businessRouteName =
                  router?.arguments?.businessRouteName ?? router?.route.bussinesRouteName ?? settings.name;
            }

            if (businessRouteName?.isNotEmpty == true) {
              logger.v('Screen name called $businessRouteName');
              if (businessRouteName != 'get_loan_wizard' &&
                  businessRouteName != 'lock_screen' &&
                  (gtp?.currScreen == null || gtp?.prevScreen != gtp?.currScreen)) {
                gtp?.prevScreen = gtp.currScreen;
                gtp?.currScreen = businessRouteName;
              }
              //do not track ulo, id card base page name
              if (businessRouteName != 'get_loan_wizard' ||
                  businessRouteName != 'id_card_screen' ||
                  businessRouteName != 'id_card_ph_screen') {
                eventTrackingService.trackScreenEvent(
                  screenName: businessRouteName!,
                );
              }
            }
          },
        );
}
