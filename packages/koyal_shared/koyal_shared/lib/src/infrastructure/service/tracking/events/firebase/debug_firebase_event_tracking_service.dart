import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:gma_platform/gma_platform.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../../../koyal_shared.dart';

class DebugFirebaseEventTrackingService extends FirebaseEventTrackingService {
  File? _logDirectory;

  DebugFirebaseEventTrackingService({
    required FirebaseAnalytics firebaseAnalytics,
    required Map<KoyalEvent, bool> eventsConfig,
    required GlobalTrackingProperties gtp,
    required ShouldTrackProperties shouldTrackProperties,
  }) : super(
          firebaseAnalytics: firebaseAnalytics,
          eventsConfig: eventsConfig,
          gtp: gtp,
          shouldTrackProperties: shouldTrackProperties,
        );

  @override
  Future<void> trackEvent({
    required String name,
    KoyalEvent? event,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
    List<String?>? customDimensions,
  }) async {
    if (shouldTrack(eventsConfig, event) == false) return;

    final newUserProps = _getIndexBasedUserProperty(userProperty);
    return _logEvent(
      tag: 'EVENT',
      name: name,
      userProperty: newUserProps,
      properties: properties,
    );
  }

  @override
  Future<void> trackScreenEvent({
    required String screenName,
    Map<String, String>? userProperty,
  }) async {
    return _logEvent(
      tag: 'SCREEN',
      name: screenName,
      userProperty: userProperty,
    );
  }

  Map<String, String> _getIndexBasedUserProperty(Map<String, String>? userProperty) {
    final trackPropertiesMap = shouldTrackProperties.shouldTrackInMap();
    final indexMap = <String, String>{};
    if (globalTrackingProperties.userId != null) {
      indexMap[KoyalTrackingProperties.propertyUserId] = globalTrackingProperties.userId!;
    }

    if (globalTrackingProperties.transactionNumber != null) {
      indexMap[KoyalTrackingProperties.propertyCdTransactionNumber] = globalTrackingProperties.transactionNumber!;
    }

    if (globalTrackingProperties.isInsider != null) {
      indexMap[KoyalTrackingProperties.propertyCdUserIsInsider] = globalTrackingProperties.isInsider.toString();
    }

    if (globalTrackingProperties.isInsider != null) {
      indexMap[KoyalTrackingProperties.propertyCdIsProd] = (!globalTrackingProperties.isInsider!).toString();
    }

    if (globalTrackingProperties.journeyType != null) {
      indexMap[KoyalTrackingProperties.propertyJourneyType] =
          globalTrackingProperties.journeyType.translateToCorrectJourneyType();
    }

    if (globalTrackingProperties.contractId != null) {
      indexMap[KoyalTrackingProperties.propertyCdContractNumber] = globalTrackingProperties.contractId!;
    }
    if (globalTrackingProperties.userType != null) {
      indexMap[KoyalTrackingProperties.propertyUserType] = globalTrackingProperties.userType!;
    }
    if (globalTrackingProperties.appVersion != null) {
      indexMap[KoyalTrackingProperties.propertyCdAppVersion] = globalTrackingProperties.appVersion!;
    }

    if (globalTrackingProperties.loanType != null && globalTrackingProperties.loanType!.isNotEmpty) {
      indexMap[KoyalTrackingProperties.propertyCdLoanType] = globalTrackingProperties.loanType!;
    }

    if (globalTrackingProperties.productType != null && globalTrackingProperties.productType!.isNotEmpty) {
      indexMap[KoyalTrackingProperties.propertyCdProductType] = globalTrackingProperties.productType!;
    }

    if (globalTrackingProperties.cuid != null) {
      indexMap[KoyalTrackingProperties.propertyCdCuid] = globalTrackingProperties.cuid!;
    }
    if (globalTrackingProperties.sessionID != null) {
      indexMap[KoyalTrackingProperties.propertyCdSessionID] = globalTrackingProperties.sessionID!;
    }

    if (globalTrackingProperties.eLoadCategory != null) {
      indexMap[KoyalTrackingProperties.propertyCdEloadCategory] = globalTrackingProperties.eLoadCategory!;
    }

    if (globalTrackingProperties.eBillCategory != null) {
      indexMap[KoyalTrackingProperties.propertyCdEbillCategory] = globalTrackingProperties.eBillCategory!;
    }

    if (globalTrackingProperties.transactionPaymentMethod != null) {
      indexMap[KoyalTrackingProperties.propertyCdTranPaymentMethod] =
          globalTrackingProperties.transactionPaymentMethod!;
    }

    if (globalTrackingProperties.prevScreen != null) {
      indexMap[KoyalTrackingProperties.propertyCdPrevScreen] = globalTrackingProperties.prevScreen!;
    }

    if (globalTrackingProperties.repaymentAbTest != null) {
      indexMap[KoyalTrackingProperties.propertyCdAbTest] = globalTrackingProperties.repaymentAbTest!;
    }
    if (globalTrackingProperties.loanJourneyAbTest != null) {
      indexMap[KoyalTrackingProperties.propertyCdAbTest] = globalTrackingProperties.loanJourneyAbTest!;
    }
    if (globalTrackingProperties.referralCode != null) {
      indexMap[KoyalTrackingProperties.propertyCdReferralCode] = globalTrackingProperties.referralCode!;
    }
    if (globalTrackingProperties.flowStage != null) {
      indexMap[KoyalTrackingProperties.propertyCdFlowStage] = globalTrackingProperties.flowStage!;
    }
    for (final param in UTMTrackingProperties.utmParameters) {
      if (globalTrackingProperties.utmParameters[param] != null) {
        indexMap[param] = globalTrackingProperties.utmParameters[param]!;
      }
    }
    userProperty?.forEach((key, value) {
      if (!indexMap.containsKey(key) || value.isNotEmpty) {
        indexMap[key] = value;
      }
    });
    indexMap.removeWhere(
      (key, _) => trackPropertiesMap[key] == false,
    );
    return indexMap;
  }

  @override
  Future<void> trackCampaignFromUrl({required String url}) async {
    return _writeEventToLogFile(
      '${DateTime.now().millisecondsSinceEpoch}::Campaign tracking, event-url->$url',
    );
  }

  Future _logEvent({
    required String tag,
    required String name,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
  }) async {
    if (kIsWeb) return;
    await _writeEventToLogFile(
      '${DateTime.now().millisecondsSinceEpoch}::$tag->$name, event_info->${properties?.toString() ?? ''}, userProperty->${userProperty?.toString() ?? ''}',
    );
  }

  Future<void> _writeEventToLogFile(String eventLog) async {
    if (GmaPlatform.isWeb) return Future.value();
    final currentDate = DateTime.now();
    final fileName = _getFileNameForDate(currentDate);
    if (_logDirectory == null) {
      final documentDirectory = await getApplicationDocumentsDirectory();
      _logDirectory = File('${documentDirectory.path}/logs/firebase');
    }
    final file = File('${_logDirectory!.path}/$fileName.txt');
    if (!file.existsSync()) {
      file.createSync(recursive: true);
    }
    file.writeAsStringSync('$eventLog\n', mode: FileMode.append);
  }

  String _getFileNameForDate(DateTime date) {
    return DateFormat('dd-MM-yyyy').format(date);
  }
}
