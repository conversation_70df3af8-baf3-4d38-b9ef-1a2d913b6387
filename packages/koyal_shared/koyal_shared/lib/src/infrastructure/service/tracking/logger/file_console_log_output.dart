import 'package:logger/logger.dart';

import 'logger_console_log_output.dart';

class FileConsoleLogOutput extends LogOutput {
  late ConsoleOutput consoleOutput;
  late LoggerConsoleLogOutput loggerConsoleLogOutput;

  FileConsoleLogOutput() {
    consoleOutput = ConsoleOutput();
    loggerConsoleLogOutput = LoggerConsoleLogOutput();
  }

  @override
  void output(OutputEvent event) {
    consoleOutput.output(event);
    loggerConsoleLogOutput.output(event);
  }
}
