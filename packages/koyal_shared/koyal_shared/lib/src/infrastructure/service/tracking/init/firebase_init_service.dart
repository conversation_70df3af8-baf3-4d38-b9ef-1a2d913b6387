// ignore_for_file: avoid_print

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';

class FirebaseInitService {
  FirebaseInitService({
    this.firebaseOptions,
  });
  FirebaseOptions? firebaseOptions;

  Future<FirebaseApp?> init() async {
    try {
      return await Firebase.initializeApp(options: firebaseOptions);
    } on PlatformException catch (_) {
      // In case of integration tests on linux we want to ignore this exception
      print("This platform doesn't support firebase.");
    }
    return null;
  }
}
