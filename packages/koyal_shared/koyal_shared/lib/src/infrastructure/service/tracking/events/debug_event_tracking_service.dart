import 'package:logger/logger.dart';

import 'event_tracking_service.dart';
import 'events.dart';
import 'global_tracking_properties.dart';
import 'koyal_analytics_constants.dart';

class DebugEventTrackingService extends EventTrackingService {
  final Logger logger;

  DebugEventTrackingService({
    required this.logger,
    required GlobalTrackingProperties gtp,
  }) : super(globalTrackingProperties: gtp);

  @override
  Future<void> trackEvent({
    required String name,
    KoyalEvent? event,
    Map<String, String>? userProperty,
    Map<String, String>? properties,
    List<String?>? customDimensions,
  }) async {
    logger
      ..v('Captured event $name in debug service')
      ..v('user properties:');
    prepareUserProperty(userProperty).forEach((dynamic k, v) => logger.v('$k: $v'));
    if (properties != null) {
      logger.v('properties:');
      properties.forEach((k, v) => logger.v('$k: $v'));
    }
    return Future.value();
  }

  @override
  Future<void> trackScreenEvent({
    required String screenName,
    Map<String, String>? userProperty,
  }) {
    logger
      ..v('Captured screen $screenName in debug service')
      ..v('user properties:');
    prepareUserProperty(userProperty).forEach((dynamic k, v) => logger.v('$k: $v'));
    return Future.value();
  }

  Map<String, String> prepareUserProperty(Map<String, String>? userProperty) {
    final nonNullUserProperty = userProperty ?? {};
    if (globalTrackingProperties.userId != null) {
      nonNullUserProperty[KoyalTrackingProperties.propertyUserId] = globalTrackingProperties.userId!;
    }

    return nonNullUserProperty;
  }

  @override
  Future<void> trackCampaignFromUrl({required String url}) {
    logger.v('Captured campaign url: $url');
    return Future.value();
  }

  @override
  Future<void> trackInstallReferrer({required String referrerUrl}) {
    logger.v('Captured referrer url: $referrerUrl');
    return Future.value();
  }

  @override
  Future<void> setUser({
    required String userId,
    String? cuid,
    required bool isInsider,
    String? phoneNumber,
    required bool isExistingUser,
    required String signonStatus,
  }) =>
      Future.value();

  @override
  void setCurrentScreen({required String screenName}) {}

  @override
  Future<void> cleanUp() async {}
}
