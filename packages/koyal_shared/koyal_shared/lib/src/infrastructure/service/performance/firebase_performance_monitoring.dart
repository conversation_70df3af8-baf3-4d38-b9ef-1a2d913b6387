import 'dart:async';

import 'package:collection/collection.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:koyal_auth/koyal_auth.dart';

import '../../../../koyal_shared.dart';

class FirebasePerformanceMonitoring extends IFirebasePerformanceMonitoring {
  final PerformanceConfig performanceConfig;
  final IdentityStorage identityStorage;
  final List<TraceType> enabledTraces;

  FirebasePerformanceMonitoring({
    required this.performanceConfig,
    required this.identityStorage,
    required this.enabledTraces,
  });

  List<CustomTrace> runningTraces = [];

  @override
  Future<void> startTrace(TraceType traceType) async {
    if (shouldTrack(enabledTraces, traceType)) {
      final user = await _currentUser();
      if (user != null && user.isInsider) {
        return;
      }
      final trace = runningTraces.firstWhereOrNull((element) => element.traceType == traceType) ??
          CustomTrace(traceType, maxTraceDuration: _maxTraceDuration(traceType));
      initTrace(trace);
      await trace.trace.start();
      if (user != null) {
        trace.trace.putAttribute(
          IFirebasePerformanceMonitoring.userIdProperty,
          user.id ?? '',
        );
      }
    }
  }

  Future<CurrentUser?> _currentUser() async {
    return identityStorage.getIdentity();
  }

  @override
  Future<void> stopTrace(TraceType traceType, {bool withRemoveFromList = true}) async {
    if (shouldTrack(enabledTraces, traceType)) {
      final traceIndex = runningTraces.indexWhere((element) => element.traceType == traceType);
      if (traceIndex > -1) {
        final trace = runningTraces[traceIndex];
        trace.maxTraceDurationTimer?.cancel();
        await trace.trace.stop();
        if (withRemoveFromList &&
            traceIndex < runningTraces.length &&
            runningTraces[traceIndex].traceType == traceType) {
          runningTraces.removeAt(traceIndex);
        }
      }
    }
  }

  void initTrace(CustomTrace trace) {
    stopTrace(trace.traceType);
    trace.maxTraceDurationTimer = Timer(trace.maxTraceDuration, () {
      stopTrace(trace.traceType);
    });
    runningTraces.add(trace);
  }

  @override
  void setIsEnabled({required bool isEnabled}) {
    FirebasePerformance.instance.setPerformanceCollectionEnabled(isEnabled);
  }

  @override
  Future<void> stopAllTraces() async {
    await Future.wait(
      runningTraces.map((trace) async {
        await stopTrace(trace.traceType, withRemoveFromList: false);
      }),
    );
    runningTraces = [];
  }

  Duration _maxTraceDuration(TraceType traceType) {
    if ([
      TraceType.documentPreparation,
      TraceType.bod1Submission,
      TraceType.idDocumentDetection,
      TraceType.otpSignature,
    ].contains(traceType)) {
      return const Duration(seconds: 5 * 60);
    }

    return const Duration(seconds: 30);
  }
}
