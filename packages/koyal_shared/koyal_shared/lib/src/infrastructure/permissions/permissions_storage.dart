import 'package:gma_storage/gma_storage.dart';

import 'request_permissions_model.dart';

const requestPermissions = 'requestPermissions';

class PermissionsStorage {
  final GmaStorageProvider storage;

  PermissionsStorage({required this.storage});

  Future<RequestPermissionsModel?> getRequestPermissions() async => storage.get(
        requestPermissions,
        fromMap: RequestPermissionsModel().fromMap,
      );

  Future<void> insertRequestPermissions(RequestPermissionsModel value) async =>
      storage.insert(requestPermissions, value);

  Future<void> deleteRequestPermissions() async => storage.delete(requestPermissions);
}
