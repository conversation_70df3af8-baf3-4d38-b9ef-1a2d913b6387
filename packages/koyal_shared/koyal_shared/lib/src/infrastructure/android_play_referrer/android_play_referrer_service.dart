import 'package:android_play_install_referrer/android_play_install_referrer.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';
import 'package:logger/logger.dart';

import '../service/tracking/events/event_tracking_service.dart';

class AndroidPlayReferrerService {
  final EventTrackingService trackingService;
  final Logger logger;
  final IAppStateRepository appStateRepository;

  AndroidPlayReferrerService({
    required this.trackingService,
    required this.logger,
    required this.appStateRepository,
  });

  Future<void> init() async {
    final isFirstStart = (await appStateRepository.isFirstStart()).fold((l) => false, (r) => r);
    if (kDebugMode || !GmaPlatform.isAndroid || !isFirstStart) return;
    try {
      final referrerDetails = await AndroidPlayInstallReferrer.installReferrer;
      final referrerUrl = referrerDetails.installReferrer;
      if (referrerUrl != null) {
        await trackingService.trackInstallReferrer(referrerUrl: referrerUrl);
      }
      await appStateRepository.setAndroidInstallReferrer(<String, dynamic>{
        'referrerUrl': referrerUrl ?? '',
        'referrerClickTimestampSeconds': referrerDetails.referrerClickTimestampSeconds,
        'referrerClickTimestampServerSeconds': referrerDetails.referrerClickTimestampServerSeconds,
        'installBeginTimestampSeconds': referrerDetails.installBeginTimestampSeconds,
        'installBeginTimestampServerSeconds': referrerDetails.installBeginTimestampServerSeconds,
      });
    }
    // ignore: avoid_catches_without_on_clauses
    catch (e) {
      logger.e('Failed to get referrer details: $e');
    }
  }
}
