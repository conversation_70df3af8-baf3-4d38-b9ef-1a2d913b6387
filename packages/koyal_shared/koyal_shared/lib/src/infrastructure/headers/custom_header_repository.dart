import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'custom_header_storage.dart';

class CustomHeaderRepository extends ICustomHeaderRepository {
  final CustomHeaderStorage storage;

  CustomHeaderRepository({required this.storage});

  @override
  Future<void> saveXTestScenarioHeader({String value = ''}) async {
    await storage.saveXTestScenarioHeader(value: value);
  }

  @override
  Future<String?> getXTestScenarioHeader() async {
    return storage.getXTestScenarioHeader();
  }

  @override
  Future<void> saveXTestScenarioUrlForLoan({
    bool onlyLoanJourney = false,
    bool onlyUlo = false,
  }) async {
    await storage.saveXTestScenarioOnlyUlo(value: onlyUlo);
    await storage.saveXTestScenarioOnlyLo(value: onlyLoanJourney);
    if (onlyLoanJourney) {
      await storage.saveXTestScenarioLoanUrl(
        values: ['/multiworkflow/LoanWorkFlow/'],
      );
    } else if (onlyUlo) {
      await storage.saveXTestScenarioLoanUrl(
        values: [
          '/v2/multiworkflow/loan-unified/',
          '/banner/dashboard',
          '/v1/signature/banners/bundle',
        ],
      );
    } else {
      await storage.saveXTestScenarioLoanUrl(values: []);
    }
  }

  @override
  Future<List<String>> getXTestScenarioUrlForLoan() {
    return storage.getXTestScenarioLoanUrl();
  }

  @override
  Future<bool?> getXTestScenarioOnlyUlo() {
    return storage.getXTestScenarioOnlyUlo();
  }

  @override
  Future<bool?> getXTestScenarioOnlyLo() {
    return storage.getXTestScenarioOnlyLo();
  }

  @override
  Future<void> saveTransactionHistoryUrl({
    required bool enableXTestInTransactionHistory,
  }) async {
    await storage.toggleXTestInTransactionHistory(enable: enableXTestInTransactionHistory);
    if (enableXTestInTransactionHistory) {
      await storage.saveTransactionHistoryUrl(url: '/transaction-history');
    } else {
      await storage.saveTransactionHistoryUrl(url: '');
    }
  }

  @override
  Future<String?> getTransactionHistoryUrl() async {
    return storage.getTransactionHistoryUrl();
  }

  @override
  Future<bool?> isXTestEnabledInTransactionHistory() async {
    return storage.isXTestEnabledInTransactionHistory();
  }

  @override
  Future<void> toggleXTestInTransactionHistory({
    required bool enable,
  }) async {
    await storage.toggleXTestInTransactionHistory(enable: enable);
  }

  @override
  Future<bool?> getXTestScenarioRetentionTransaction() async {
    return storage.getXTestScenarioRetentionTransaction();
  }

  @override
  Future<void> setXTestScenarioRetentionTransaction({required bool value}) async {
    await storage.setXTestScenarioRetentionTransaction(value: value);
    if (value) {
      await storage.setXTestScenarioRetentionTransactionUrl(
        values: [
          '/ebills',
          '/v2/ebills',
          '/ph-eloads',
          '/ph-wt',
          '/ph-qr',
          '/ph-payment',
          '/vn-qr',
        ],
      );
    } else {
      await storage.deleteXTestScenarioRetentionTransactionUrl();
    }
  }

  @override
  Future<List<String>> getXTestScenarioUrlForRetentionTransaction() async {
    return storage.getXTestScenarioRetentionTransactionUrl();
  }

  @override
  Future<bool?> getXTestScenarioCreditCardSetLimit() {
    return storage.getXTestScenarioCreditCardSetLimit();
  }

  @override
  Future<List<String>> getXTestScenarioCreditCardSetLimitUrl() {
    return storage.getXTestScenarioCreditCardSetLimitUrl();
  }

  @override
  Future<void> setXTestScenarioCreditCardSetLimit({required bool value}) async {
    await storage.setXTestScenarioCreditCardSetLimit(value: value);
    if (value) {
      await storage.setXTestScenarioCreditCardSetLimitUrl(
        values: [
          '/spending-limits',
        ],
      );
    } else {
      await storage.deleteXTestScenarioCreditCardSetLimitUrl();
    }
  }
}
