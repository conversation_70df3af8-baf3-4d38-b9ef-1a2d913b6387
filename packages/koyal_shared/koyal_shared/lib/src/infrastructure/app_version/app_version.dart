import 'dart:async';
import 'dart:math';

import 'package:gma_platform/gma_platform.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../koyal_shared.dart';

class AppVersion extends IAppVersion {
  final IPlatformService platformService;
  final IFeatureFlagRepository featureFlagRepository;
  final IRemoteConfigRepository remoteConfigRepository;
  final String appVersionFlag;
  final bool isHuawei;
  AppVersion({
    required this.platformService,
    required this.featureFlagRepository,
    required this.remoteConfigRepository,
    required this.appVersionFlag,
    required String googlePlayId,
    required String appStoreId,
    required this.isHuawei,
    String? appGalleryId,
  }) : super(googlePlayId: googlePlayId, appStoreId: appStoreId, appGalleryId: appGalleryId) {
    featureFlagRepository.featureFlagsChanged.stream.listen((event) {
      check();
    });
    remoteConfigRepository.minAppVersion.listen((minVersion) async {
      final isUpdateNeeded = await isRemoteConfigForceUpdateNeeded(remoteConfigMinVersion: minVersion);
      if (isUpdateNeeded) {
        update(value: true);
      }
    });
  }

  final StreamController<bool> _controller = StreamController.broadcast();

  @override
  Stream<bool> get stream => _controller.stream;

  @override
  Future<String?> getMinSupportedVersion() async {
    final minRequiredVersion =
        (await featureFlagRepository.getFeatureFlagValue(appVersionFlag)).fold((l) => null, (r) => r);
    return minRequiredVersion;
  }

  @override
  Future<bool> isForceUpdateNeeded() async {
    final packageInfo = await platformService.getPackageInfo();
    final minRequiredVersion = await getMinSupportedVersion();
    final currentVersion = packageInfo.version;
    final shoulUpgrade = _isTargetVersionGreater(currentVersion, minRequiredVersion);
    return shoulUpgrade;
  }

  @override
  Future<String?> getRemoteConfigMinSupportedVersion() async {
    final minRequiredVersion =
        remoteConfigRepository.getStringValueByKey(isHuawei ? minAppVersionHuaweiConfigKey : minAppVersionConfigKey);
    return minRequiredVersion.fold((l) => null, (r) => r);
  }

  @override
  Future<bool> isRemoteConfigForceUpdateNeeded({String? remoteConfigMinVersion}) async {
    final packageInfo = await platformService.getPackageInfo();
    final minRequiredVersion = remoteConfigMinVersion ?? await getRemoteConfigMinSupportedVersion();
    final currentVersion = packageInfo.version;
    final shoulUpgrade = _isTargetVersionGreater(currentVersion, minRequiredVersion);
    return shoulUpgrade;
  }

  @override
  void close() => _controller.close();

  @override
  void update({required bool value}) => _controller.add(value);

  @override
  void check() {
    isForceUpdateNeeded().then((v) => update(value: v));
  }

  bool _isTargetVersionGreater(String source, String? target) {
    if (target == null) {
      return false;
    }
    final sourceNumbers = source.split('.');
    final targetNumbers = target.split('.');

    final sourceLength = sourceNumbers.length;
    final targetLength = targetNumbers.length;

    final len = max(sourceLength, targetLength);
    for (var i = 0; i < len; i++) {
      if (sourceLength < i + 1) {
        sourceNumbers.add('0');
      }

      if (targetLength < i + 1) {
        targetNumbers.add('0');
      }

      final targetAsInt = int.tryParse(targetNumbers[i], radix: 10) ?? 0;
      final sourceAsInt = int.tryParse(sourceNumbers[i], radix: 10) ?? 0;

      final cmp = targetAsInt - sourceAsInt;

      if (cmp != 0) {
        return cmp > 0;
      }
    }
    return false;
  }
}
