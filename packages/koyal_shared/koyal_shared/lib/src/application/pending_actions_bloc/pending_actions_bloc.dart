import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

part 'pending_actions_bloc.freezed.dart';
part 'pending_actions_event.dart';
part 'pending_actions_state.dart';

class PendingActionsBloc extends Bloc<PendingActionsEvent, PendingActionsState> {
  final INewPendingActionsRepository repo;
  final IIdentityRepository identityRepository;
  final IFeatureFlagRepository featureFlagRepository;
  final ICurrentUserRepository currentUserRepository;
  StreamSubscription<CurrentUser?>? _userSubscription;
  StreamSubscription<void>? _featureFlagsChangesSubscription;

  var _isShowPendingList = false;

  PendingActionsBloc({
    required this.repo,
    required this.identityRepository,
    required this.featureFlagRepository,
    required this.currentUserRepository,
  }) : super(PendingActionsState.initial()) {
    _featureFlagsChangesSubscription = featureFlagRepository.featureFlagsChanged.stream.listen((event) async {
      if (await currentUserRepository.isInsider()) {
        add(const PendingActionsEvent.init());
      }
    });
    on<_Init>((event, emit) => _onInit(emit));
    on<_ExternalLoad>(_onExternalLoad);
    on<_Load>(_onLoad);
    on<_Open>(_onOpen);
    on<_ShowPendingActionsList>(_onShowPendingActionsList);
  }

  Future<void> _onInit(Emitter<PendingActionsState> emit) async {
    await identityRepository.streamIdentity()?.then(
      (value) async {
        await _userSubscription?.cancel();
        _userSubscription = value.where((event) => event != null).listen((event) {
          add(const PendingActionsEvent.load());
        });
      },
    );
    emit(PendingActionsState.initial());
  }

  Future<void> _onExternalLoad(_ExternalLoad event, Emitter<PendingActionsState> emit) async {
    final items = event.items;
    if (items.isNotEmpty) {
      emit(
        state.copyWith(
          items: event.items,
          status: LoadingState.isCompleted,
        ),
      );
    }
  }

  Future<void> _onLoad(_Load event, Emitter<PendingActionsState> emit) async {
    emit(
      state.copyWith(status: LoadingState.isLoading),
    );
    final response = await repo.load(traceId: event.traceId);
    emit(
      response.fold((l) {
        return state.copyWith(items: [], status: LoadingState.isCompleted);
      }, (r) {
        return state.copyWith(
          items: r.items,
          status: LoadingState.isCompleted,
          showPendingActionList: _isShowPendingList,
        );
      }),
    );
  }

  Future<void> _onOpen(_Open event, Emitter<PendingActionsState> emit) async {
    if (event.item != null && !state.isOpening) {
      emit(
        state.copyWith(isOpening: true),
      );
      repo.open(event.item!);
      await Future<void>.delayed(const Duration(seconds: 1));
      emit(
        state.copyWith(isOpening: false),
      );
    }
  }

  Future<void> _onShowPendingActionsList(_ShowPendingActionsList event, Emitter<PendingActionsState> emit) async {
    emit(
      state.copyWith(
        showPendingActionList: event.isShowPendingList,
      ),
    );
    _isShowPendingList = event.isShowPendingList;
  }

  @override
  Future<void> close() {
    _userSubscription?.cancel();
    _featureFlagsChangesSubscription?.cancel();
    return super.close();
  }
}
