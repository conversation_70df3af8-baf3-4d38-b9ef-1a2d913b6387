part of 'pending_actions_bloc.dart';

@freezed
class PendingActionsState with _$PendingActionsState {
  factory PendingActionsState({
    required LoadingState status,
    required bool isOpening,
    @Default(<PendingActionModel>[]) List<PendingActionModel> items,
    @Default(false) bool showPendingActionList,
  }) = _PendingActionsState;

  PendingActionsState._();

  factory PendingActionsState.initial() => PendingActionsState(
        status: LoadingState.isInitial,
        isOpening: false,
        items: <PendingActionModel>[],
      );
  bool? get isLoading => status == LoadingState.isLoading;
}
