import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flagsmith/flagsmith.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_auth/koyal_auth.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import 'set_traits_state.dart';

class SetTraitsCubit extends Cubit<SetTraitsState> {
  final IFeatureFlagRepository featureFlagRepository;
  final IConnectUserRepository connectUserRepository;

  SetTraitsCubit({
    required this.featureFlagRepository,
    required this.connectUserRepository,
  }) : super(const SetTraitsState());

  Future<void> init(List<Map<String, String>> traits) async {
    final result = await featureFlagRepository.updateTraits(
      traits
          .map(
            (trait) => Trait(key: trait.keys.first, value: trait.values.first),
          )
          .toList(),
    );

    final success = result.fold((l) => false, (r) => true);
    if (success) {
      await connectUserRepository.refreshToken(
        updateClaims: true,
      );
      emit(
        state.copyWith(isSuccess: true),
      );
    } else {
      emit(
        state.copyWith(isFail: true),
      );
    }
  }
}
