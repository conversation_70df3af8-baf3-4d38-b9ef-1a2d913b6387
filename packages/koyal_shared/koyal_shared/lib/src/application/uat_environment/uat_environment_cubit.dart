import 'package:bloc/bloc.dart';

import '../../domain/uat_environment/i_uat_repository.dart';

/// For dev tools
/// It can enable/disable UAT data over uat repository
class UatEnvironmentCubit extends Cubit<bool> {
  final IUatRepository uatRepository;

  UatEnvironmentCubit({
    required this.uatRepository,
  }) : super(false);

  Future<void> init() async {
    emit(await uatRepository.isUatEnabled);
  }

  Future<void> setUatEnvironment({required bool enabled}) async {
    await uatRepository.enableUat(enable: enabled);

    emit(await uatRepository.isUatEnabled);
  }
}
