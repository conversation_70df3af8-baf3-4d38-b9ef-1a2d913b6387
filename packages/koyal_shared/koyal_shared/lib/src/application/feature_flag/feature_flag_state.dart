part of 'feature_flag_bloc.dart';

@freezed
class FeatureFlagState with _$FeatureFlagState {
  const factory FeatureFlagState({
    required LoadingState status,
    required DateTime loadedDateTime,
  }) = _FeatureFlagState;

  const FeatureFlagState._();

  factory FeatureFlagState.initial() => FeatureFlagState(
        status: LoadingState.isInitial,
        loadedDateTime: DateTime.now(),
      );
}
