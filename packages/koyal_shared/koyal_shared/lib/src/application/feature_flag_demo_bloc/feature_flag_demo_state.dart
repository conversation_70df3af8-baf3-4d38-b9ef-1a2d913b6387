part of 'feature_flag_demo_bloc.dart';

@freezed
class FeatureFlagDemoState with _$FeatureFlagDemoState {
  factory FeatureFlagDemoState({
    required LoadingState loading,
    bool? saveSuccess,
    @Default(<Flag>[]) List<Flag> cached,
    @Default(<Flag>[]) List<Flag> onStart,
    @Default(<Flag>[]) List<Flag> current,
    @Default(<Flag>[]) List<Flag> seed,
    @Default(<String>[]) List<String> fetchedFlags,
    @Default(<String, String?>{}) Map<String, String?> seedsWithOverride,
    @Default(<String, String?>{}) Map<String, String?> temporarySeedsWithOverride,
    @Default(false) bool identityOverrides,
    @Default(false) bool isInsider,
    @Default(<Tuple2<Flag, Flag?>>[]) List<Tuple2<Flag, Flag?>> seed2Default,
    @Default(<Tuple2<Flag, Flag?>>[]) List<Tuple2<Flag, Flag?>> default2Seed,
    @Default(<Tuple3<Flag?, Flag?, Flag>>[]) List<Tuple3<Flag?, Flag?, Flag>> filtred,
    String? query,
  }) = _FeatureFlagDemoState;
  FeatureFlagDemoState._();
  factory FeatureFlagDemoState.initialize() => FeatureFlagDemoState(loading: LoadingState.isInitial);
}
