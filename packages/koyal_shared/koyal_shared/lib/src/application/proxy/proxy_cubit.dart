import 'package:bloc/bloc.dart';

import '../../domain/proxy/i_proxy_url_provider.dart';
import 'proxy_cubit_state.dart';

class ProxyCubit extends Cubit<ProxyCubitState> {
  final IProxyUrlProvider proxyUrlProvider;

  ProxyCubit({
    required this.proxyUrlProvider,
  }) : super(ProxyCubitState.initial());

  void init() {
    emit(
      ProxyCubitState(
        proxyUrl: proxyUrlProvider.proxyUrl,
        validateCertificate: proxyUrlProvider.validateCertificate,
      ),
    );
  }

  Future<void> setProxy(String? proxyUrl) async {
    if (proxyUrl == null) {
      await proxyUrlProvider.changeProxy(proxyUrl);
      emit(
        ProxyCubitState(
          proxyUrl: proxyUrl,
        ),
      );
    } else {
      await proxyUrlProvider.changeProxy(proxyUrl);
      emit(
        ProxyCubitState(
          proxyUrl: proxyUrl,
          validateCertificate: proxyUrlProvider.validateCertificate,
        ),
      );
    }
  }

  Future<void> setCertValidation({required bool certValidation}) async {
    await proxyUrlProvider.changeProxy(proxyUrlProvider.proxyUrl, validateCertificate: certValidation);
    emit(
      ProxyCubitState(
        proxyUrl: proxyUrlProvider.proxyUrl,
        validateCertificate: proxyUrlProvider.validateCertificate,
      ),
    );
  }
}
