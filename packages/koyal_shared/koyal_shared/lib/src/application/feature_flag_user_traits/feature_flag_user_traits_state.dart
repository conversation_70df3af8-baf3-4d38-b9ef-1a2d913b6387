import 'package:flagsmith/flagsmith.dart';

class FeatureFlagUserTraitsState {
  final String traitKey;
  final String traitValue;
  final bool canContinue;
  final List<Trait> userTraits;
  final bool loadingTraits;
  final bool savingTrait;

  FeatureFlagUserTraitsState({
    this.traitKey = 'team',
    this.traitValue = '',
    this.canContinue = false,
    this.userTraits = const [],
    this.loadingTraits = false,
    this.savingTrait = false,
  });

  FeatureFlagUserTraitsState copyWith({
    String? traitKey,
    String? traitValue,
    bool? canContinue,
    List<Trait>? userTraits,
    bool? loadingTraits,
    bool? savingTrait,
  }) {
    return FeatureFlagUserTraitsState(
      traitKey: traitKey ?? this.traitKey,
      traitValue: traitValue ?? this.traitValue,
      canContinue: canContinue ?? this.canContinue,
      userTraits: userTraits ?? this.userTraits,
      loadingTraits: loadingTraits ?? this.loadingTraits,
      savingTrait: savingTrait ?? this.savingTrait,
    );
  }
}
