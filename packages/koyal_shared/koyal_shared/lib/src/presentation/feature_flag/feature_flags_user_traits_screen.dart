import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';

import '../../application/feature_flag_user_traits/feature_flag_user_traits_cubit.dart';
import '../../application/feature_flag_user_traits/feature_flag_user_traits_state.dart';

class FeatureFlagsUserTraitsScreen extends StatelessWidget with RouteWrapper {
  const FeatureFlagsUserTraitsScreen({Key? key}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (context) => context.get<FeatureFlagUserTraitsCubit>()..loadTraits(),
        child: this,
      );
  @override
  Widget build(BuildContext context) {
    return KoyalScaffold(
      appBar: KoyalAppBar(title: 'Feature flags - user traits'),
      body: BlocBuilder<FeatureFlagUserTraitsCubit, FeatureFlagUserTraitsState>(
        builder: (context, state) {
          return StickyFooterShadow.basic(
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                KoyalPadding.normalHorizontal(
                  child: KoyalText.caption1('Current user traits'),
                ),
                if (state.loadingTraits)
                  const KoyalProgressDialog()
                else
                  ...state.userTraits
                      .map(
                        (e) => _traitText(context, e.key, e.value),
                      )
                      .toList(),
                KoyalPadding.normalAll(
                  child: KoyalPadding.large(
                    left: false,
                    right: false,
                    bottom: false,
                    child: KoyalText.caption1('Set a new traits'),
                  ),
                ),
                InputText(
                  label: 'Trait key',
                  initValue: state.traitKey,
                  onChanged: (value, _) => context.read<FeatureFlagUserTraitsCubit>().setTraitKey(value),
                ),
                InputText(
                  label: 'Trait value',
                  onChanged: (value, _) => context.read<FeatureFlagUserTraitsCubit>().setTraitValue(value),
                ),
              ],
            ),
            footer: KoyalPadding.normalAll(
              child: KoyalShimmer(
                showProcessing: state.savingTrait,
                child: PrimaryButton(
                  text: 'Set trait',
                  onPressed: state.canContinue ? () => context.read<FeatureFlagUserTraitsCubit>().saveTrait() : null,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _traitText(BuildContext context, String key, String value) => KoyalPadding.largeHorizontal(
        child: RichText(
          text: TextSpan(
            text: '• ',
            style: Theme.of(context).textTheme.bodyMedium,
            children: <TextSpan>[
              TextSpan(
                text: '$key: $value',
              ),
            ],
          ),
        ),
      );
}
