//ignore_for_file: buttons-layout, koyal-text

import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../application/theme_mode/theme_mode_cubit.dart';

class ThemeModeScreen extends StatefulWidget {
  const ThemeModeScreen({Key? key}) : super(key: key);

  @override
  State<ThemeModeScreen> createState() => _ThemeModeScreenState();
}

class _ThemeModeScreenState extends State<ThemeModeScreen> {
  @override
  Widget build(BuildContext context) {
    final themeModeCubit = context.read<ThemeModeCubit>();

    return KoyalScaffold(
      appBar: KoyalAppBar(
        title: 'Change Theme Mode',
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          KoyalPadding.normalAll(
            child: Center(
              child: Text(
                'Current ThemeMode: ${themeModeCubit.state}',
                style: const TextStyle(fontSize: 18),
              ),
            ),
          ),
          KoyalPadding.small(
            child: PrimaryButton(
              text: 'Switch to light mode',
              onPressed: () {
                setState(() {
                  themeModeCubit.changeMode(ThemeMode.light);
                });
              },
            ),
          ),
          KoyalPadding.normalAll(
            child: PrimaryButton(
              text: 'Switch to dark mode',
              onPressed: () {
                setState(() {
                  themeModeCubit.changeMode(ThemeMode.dark);
                });
              },
            ),
          ),
          KoyalPadding.small(
            child: PrimaryButton(
              text: 'Switch to system mode',
              onPressed: () {
                setState(() {
                  themeModeCubit.changeMode(ThemeMode.system);
                });
              },
            ),
          ),
        ],
      ),
    );
  }
}
