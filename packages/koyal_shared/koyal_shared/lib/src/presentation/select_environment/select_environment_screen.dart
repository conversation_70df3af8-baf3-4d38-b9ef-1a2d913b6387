import 'package:capp_ui_core/capp_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:koyal_core/koyal_core.dart';
import 'package:koyal_shared_core/koyal_shared_core.dart';

import '../../../koyal_shared.dart';
import '../../application/select_environment/select_environment_state.dart';

class SelectEnvironmentScreen extends StatefulScreen with RouteWrapper {
  SelectEnvironmentScreen({Key? key}) : super(key: key);

  @override
  Widget wrappedRoute(BuildContext context) => BlocProvider(
        create: (c) => c.get<SelectEnvironmentCubit>()..loadData(),
        child: this,
      );

  @override
  SelectEnvironmentScreenState createState() => SelectEnvironmentScreenState();
}

class SelectEnvironmentScreenState extends State<SelectEnvironmentScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) => KoyalScaffold(
        appBar: KoyalAppBar(
          title: 'Select target cluster',
        ),
        body: BlocListener<SelectEnvironmentCubit, SelectEnvironmentState>(
          listener: (context, state) {
            KoyalSnackBar.show(
              context: context,
              hasBottomButton: true,
              message: L10nCappUi.of(context).updated,
              duration: const Duration(seconds: 2),
            );
          },
          listenWhen: (previous, current) => !previous.success && current.success,
          child: BlocBuilder<SelectEnvironmentCubit, SelectEnvironmentState>(
            builder: (context, state) => KoyalShimmer(
              showProcessing: state.process,
              child: Center(
                child: KoyalPadding.normalAll(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      NewInputDropdown<EnvironmentType>(
                        options: state.availableEnvironments
                            .map(
                              (e) => NewInputDropdownItem<EnvironmentType>(
                                value: e,
                                text: e.name.toUpperCase(),
                              ),
                            )
                            .toList(),
                        value: state.environmentType,
                        onSelected: (selectedValue) {
                          if (selectedValue != null) {
                            context.read<SelectEnvironmentCubit>().setEnvironmentType(environmentType: selectedValue);
                          }
                        },
                        label: 'Target cluster',
                        isError: false,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      );

  List<EnvironmentType> get options {
    final values = <EnvironmentType>[];

    return values;
  }
}
