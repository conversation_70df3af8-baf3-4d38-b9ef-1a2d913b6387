import 'dart:ui';

import 'package:flutter/foundation.dart';

abstract class FlavorSettings {
  String? get bannerName;
  Color? get bannerColor;

  final bool isBannerVisible;
  final bool showFailureDialogs;
  final bool isTest;

  bool get isInDebugMode {
    return !kReleaseMode;
  }

  FlavorSettings({
    this.isBannerVisible = true,
    this.showFailureDialogs = false,
    this.isTest = false,
  });
}
