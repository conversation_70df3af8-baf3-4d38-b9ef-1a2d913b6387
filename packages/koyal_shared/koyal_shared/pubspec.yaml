name: koyal_shared
owner: GMA
version: 0.0.1
publish_to: none
description: A new Flutter package project.
environment:
  sdk: ">=3.2.0 <4.0.0"
dependencies:
  clipboard: ^0.1.3
  account_manager:
    path: ../../../forks/account_manager
  alice:
    path: ../../../forks/alice
  android_play_install_referrer: ^0.4.0
  collection: ^1.15.0-nullsafety.4
  connectivity_plus: ^6.0.1
  dartz: ^0.10.0-nullsafety.2
  firebase_analytics: ^11.2.0
  firebase_core: 3.3.0
  firebase_crashlytics: ^4.0.3
  firebase_dynamic_links: ^6.0.0
  firebase_remote_config: ^5.0.3
  firebase_core_platform_interface: 5.2.0
  flagsmith:
    path: ../../../forks/flagsmith
  flutter:
    sdk: flutter
  flutter_image_compress: ^2.3.0
  safe_device: ^1.2.0
  flutter_markdown: ^0.6.14
  freezed_annotation: ^2.2.0
  geolocator: ^10.0.0
  geolocator_android: 4.5.4
  get_it: ^7.3.0
  gma_platform:
    path: ../../gma_platform
  gma_storage:
    path: ../../gma_storage
  image: ^3.0.2
  image_size_getter: ^1.0.0
  koyal_core:
    path: ../../koyal_core
  koyal_shared_core:
    path: ../koyal_shared_core
  capp_ui_core:
    path: ../../capp_ui/capp_ui_core
  logger: ^1.3.0
  logger_flutter:
    path: ../../../forks/logger_flutter
  secure_application: ^4.0.1
  merge_images: ^2.0.0-nullsafety
  multiple_localization: ^0.5.0
  open_filex: 4.3.2
  permission_handler: ^11.3.0
  retrofit: ^3.3.1
  flutter_svg: ^2.1.0
  rxdart: ^0.27.3
  capp_plugins_core:
    path: ../../capp_plugins/capp_plugins_core
  sms_autofill: ^2.4.1
  uuid: ^4.0.0
  timezone: ^0.9.2
  firebase_performance: ^0.10.0+3
  watcher: ^1.1.0
  dart_ipify: ^1.1.1
  a2a_intent_receiver:
    path: ../../../plugins/a2a_intent_receiver

dev_dependencies:
  bloc_test: ^9.1.6
  build_runner: ^2.4.11

  flutter_test:
    sdk: flutter
  freezed: ^2.3.2
  gma_lints:
    path: ../../gma_lints
  json_serializable: ^6.6.1

flutter:
  assets:
    - assets/svg/
